#!/bin/bash

# 部署脚本 - 将项目部署到远程服务器

# 配置信息
SERVER_IP="************"
SERVER_USER="root"
SERVER_PASSWORD="root@123"
SERVER_PORT="22"    # SSH端口，默认22
REMOTE_DIR="/opt/ai_sql_pcap"  # 远程服务器上的目标目录
LOCAL_DIR="."       # 本地项目目录

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查sshpass是否安装
if ! command -v sshpass &> /dev/null; then
    echo "sshpass未安装，正在安装..."
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        if command -v brew &> /dev/null; then
            brew install sshpass
        else
            echo "请先安装Homebrew或手动安装sshpass"
            exit 1
        fi
    else
        # Linux
        sudo apt-get update && sudo apt-get install -y sshpass || \
        sudo yum install -y sshpass || \
        echo "请手动安装sshpass"
    fi
fi

# 确认部署
echo "准备将项目部署到 ${SERVER_USER}@${SERVER_IP}:${REMOTE_DIR}"
read -p "是否继续? (y/n) " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]
then
    echo "部署已取消"
    exit 1
fi

# 创建远程目录（如果不存在）
echo "创建远程目录..."
sshpass -p "${SERVER_PASSWORD}" ssh -o StrictHostKeyChecking=no -p ${SERVER_PORT} ${SERVER_USER}@${SERVER_IP} "mkdir -p ${REMOTE_DIR}"

# 复制项目文件到远程服务器
echo -e "${YELLOW}复制项目文件到远程服务器...${NC}"
sshpass -p "${SERVER_PASSWORD}" rsync -avz \
    --exclude 'node_modules' \
    --exclude 'venv' \
    --exclude '__pycache__' \
    --exclude '.git' \
    --exclude 'captures' \
    --exclude 'logs' \
    --exclude '*.pyc' \
    -e "ssh -o StrictHostKeyChecking=no -p ${SERVER_PORT}" \
    ${LOCAL_DIR}/ ${SERVER_USER}@${SERVER_IP}:${REMOTE_DIR}/

# 在远程服务器上构建和启动Docker容器
echo -e "${YELLOW}构建Docker镜像...${NC}"
sshpass -p "${SERVER_PASSWORD}" ssh -o StrictHostKeyChecking=no -p ${SERVER_PORT} ${SERVER_USER}@${SERVER_IP} "cd ${REMOTE_DIR} && docker-compose build"

echo -e "${YELLOW}停止现有容器...${NC}"
sshpass -p "${SERVER_PASSWORD}" ssh -o StrictHostKeyChecking=no -p ${SERVER_PORT} ${SERVER_USER}@${SERVER_IP} "cd ${REMOTE_DIR} && docker-compose down"

echo -e "${YELLOW}启动容器...${NC}"
sshpass -p "${SERVER_PASSWORD}" ssh -o StrictHostKeyChecking=no -p ${SERVER_PORT} ${SERVER_USER}@${SERVER_IP} "cd ${REMOTE_DIR} && docker-compose up -d"

# 等待容器启动
echo -e "${YELLOW}等待容器启动...${NC}"
sleep 15

# 检查容器状态
echo -e "${YELLOW}检查容器状态...${NC}"
sshpass -p "${SERVER_PASSWORD}" ssh -o StrictHostKeyChecking=no -p ${SERVER_PORT} ${SERVER_USER}@${SERVER_IP} "cd ${REMOTE_DIR} && docker-compose ps"

# 检查应用健康状态
echo -e "${YELLOW}检查应用健康状态...${NC}"
sshpass -p "${SERVER_PASSWORD}" ssh -o StrictHostKeyChecking=no -p ${SERVER_PORT} ${SERVER_USER}@${SERVER_IP} "
echo '后端API状态:'
curl -s -I http://localhost:8000/docs | head -2 || echo '后端API不可访问'
echo '前端状态:'
curl -s -I http://localhost:8080 | head -2 || echo '前端不可访问'
"

echo -e "${GREEN}部署完成！${NC}"
echo -e "${GREEN}前端地址: http://${SERVER_IP}:8080${NC}"
echo -e "${GREEN}后端API: http://${SERVER_IP}:8000${NC}"
echo -e "${GREEN}API文档: http://${SERVER_IP}:8000/docs${NC}"

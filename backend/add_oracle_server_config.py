#!/usr/bin/env python3
"""
添加Oracle服务器配置
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.mysql_service import MySQLService
from utils.config import Config
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def add_oracle_server_config():
    """添加Oracle服务器配置"""
    try:
        # 创建MySQL服务实例
        mysql_service = MySQLService(
            host=Config.MYSQL_HOST,
            port=Config.MYSQL_PORT,
            user=Config.MYSQL_USER,
            password=Config.MYSQL_PASSWORD,
            database=Config.MYSQL_DATABASE
        )
        
        # 检查是否已存在Oracle服务器配置
        result = await mysql_service.execute_query(
            "SELECT * FROM server_configs WHERE host = %s",
            ('*************',)
        )
        
        if result['data']:
            logger.info("Oracle服务器配置已存在")
            config_id = result['data'][0]['id']
        else:
            # 添加Oracle服务器配置
            oracle_config = {
                'name': 'Oracle服务器',
                'host': '*************',
                'port': 22,
                'username': 'root',
                'password': 'QZ@1005#1005',
                'description': 'Oracle数据库服务器 - 用于Oracle抓包',
                'is_active': True,
                'is_default': False
            }
            
            await mysql_service.execute_query("""
                INSERT INTO server_configs
                (name, host, port, username, password, description, is_active, is_default)
                VALUES (%(name)s, %(host)s, %(port)s, %(username)s, %(password)s, %(description)s, %(is_active)s, %(is_default)s)
            """, oracle_config)
            
            # 获取插入的ID
            id_result = await mysql_service.execute_query("SELECT LAST_INSERT_ID() as id")
            config_id = id_result['data'][0]['id']
            
            logger.info(f"Oracle服务器配置已添加，ID: {config_id}")
        
        # 显示所有服务器配置
        all_configs = await mysql_service.execute_query(
            "SELECT id, name, host, port, is_default FROM server_configs WHERE is_active = TRUE"
        )
        
        logger.info("当前服务器配置:")
        for config in all_configs['data']:
            default_mark = " (默认)" if config['is_default'] else ""
            logger.info(f"  ID: {config['id']}, 名称: {config['name']}, 地址: {config['host']}:{config['port']}{default_mark}")
        
        return config_id
        
    except Exception as e:
        logger.error(f"添加Oracle服务器配置失败: {str(e)}")
        raise

if __name__ == "__main__":
    asyncio.run(add_oracle_server_config())

"""
Arq工作器配置
"""

import asyncio
import logging
from arq import create_pool, run_worker
from arq.worker import Worker
from arq.connections import RedisSettings

from config.redis_config import redis_config, redis_manager
from tasks.async_capture_tasks import (
    mysql_capture_task,
    postgres_capture_task,
    mongo_capture_task,
    oracle_capture_task,
    gaussdb_capture_task,
    docker_build_task,
    docker_image_build_task,
    ai_mysql_capture_task,
    ai_postgres_capture_task,
    ai_mongo_capture_task,
    ai_oracle_capture_task,
    ai_gaussdb_capture_task,
    ai_test_case_generation_task,
    batch_test_case_execution_task,
    single_test_case_execution_task,
    gateway_single_execution_task,
    gateway_batch_execution_task
)

logger = logging.getLogger(__name__)

# 任务函数映射
TASK_FUNCTIONS = [
    mysql_capture_task,
    postgres_capture_task,
    mongo_capture_task,
    oracle_capture_task,
    gaussdb_capture_task,
    docker_build_task,
    docker_image_build_task,
    ai_mysql_capture_task,
    ai_postgres_capture_task,
    ai_mongo_capture_task,
    ai_oracle_capture_task,
    ai_gaussdb_capture_task,
    ai_test_case_generation_task,
    batch_test_case_execution_task,
    single_test_case_execution_task,
    gateway_single_execution_task,
    gateway_batch_execution_task,
]

class WorkerSettings:
    """工作器设置"""

    # Redis设置
    redis_settings = redis_config.redis_settings

    # 任务函数
    functions = TASK_FUNCTIONS

    # 工作器设置 - 允许最多3个任务并发运行
    max_jobs = 3  # 最大并发任务数改为3，提高并发处理能力
    job_timeout = 3600  # 任务超时时间（1小时）
    keep_result = 86400  # 结果保留时间（24小时）

    # 队列设置
    queue_name = 'capture_tasks'

    # 日志设置
    log_results = True

    # 健康检查
    health_check_interval = 30

async def startup(ctx):
    """工作器启动时的初始化"""
    try:
        # 初始化Redis连接
        await redis_manager.initialize()

    # 不再在启动时清理所有 in-progress 任务，避免误将正常运行任务标记为可重排队导致重复执行
    # 如需清理，请实现基于 worker 心跳与超时的安全判定后再启用

        # 设置worker唯一标识
        import os
        worker_id = f"worker_{os.getpid()}"
        await redis_manager.redis.set(f"worker:active:{worker_id}", "1", ex=60)

        logger.info(f"工作器启动成功，Worker ID: {worker_id}，Redis连接已建立")
    except Exception as e:
        logger.error(f"工作器启动失败: {e}")
        raise

async def cleanup_orphaned_tasks():
    """清理孤儿任务（保留函数但默认不在启动时调用）"""
    try:
        logger.info("检查并清理孤儿任务...")

        # 获取所有in-progress任务
        in_progress_keys = []
        async for key in redis_manager.redis.scan_iter(match="arq:in-progress:*"):
            in_progress_keys.append(key)

        if not in_progress_keys:
            logger.info("没有发现in-progress任务")
            return

        logger.warning(f"发现 {len(in_progress_keys)} 个in-progress任务，可能是孤儿任务")

        # 清理这些任务的in-progress状态
        cleaned_count = 0
        for key in in_progress_keys:
            try:
                # 处理bytes类型的键
                if isinstance(key, bytes):
                    key_str = key.decode('utf-8')
                else:
                    key_str = key

                # 提取任务ID
                task_id = key_str.replace("arq:in-progress:", "")
                logger.info(f"清理孤儿任务: {task_id}")

                # 删除in-progress标记，让任务可以重新执行
                await redis_manager.redis.delete(key)
                cleaned_count += 1

            except Exception as e:
                logger.error(f"清理任务 {key} 失败: {e}")

        if cleaned_count > 0:
            logger.info(f"✅ 清理了 {cleaned_count} 个孤儿任务")

    except Exception as e:
        logger.error(f"清理孤儿任务失败: {e}")

async def shutdown(ctx):
    """工作器关闭时的清理"""
    try:
        # 清理worker标识
        import os
        worker_id = f"worker_{os.getpid()}"
        await redis_manager.redis.delete(f"worker:active:{worker_id}")

        # 清理任务相关的tcpdump进程
        try:
            from services.tcpdump_process_manager import tcpdump_manager
            await tcpdump_manager.cleanup_all_processes("Worker关闭清理")
        except Exception as e:
            logger.warning(f"清理tcpdump进程失败: {e}")

        # 关闭Redis连接
        await redis_manager.close()
        logger.info(f"工作器已关闭，Worker ID: {worker_id}，Redis连接已断开")
    except Exception as e:
        logger.error(f"工作器关闭时出错: {e}")

# 更新工作器设置
WorkerSettings.on_startup = startup
WorkerSettings.on_shutdown = shutdown

async def create_worker() -> Worker:
    """创建工作器实例"""
    try:
        redis_pool = await create_pool(redis_config.redis_settings)
        worker = Worker(
            functions=TASK_FUNCTIONS,
            redis_pool=redis_pool,
            max_jobs=WorkerSettings.max_jobs,
            job_timeout=WorkerSettings.job_timeout,
            keep_result=WorkerSettings.keep_result,
            queue_name=WorkerSettings.queue_name,
            on_startup=startup,
            on_shutdown=shutdown
        )
        return worker
    except Exception as e:
        logger.error(f"创建工作器失败: {e}")
        raise

def main():
    """运行工作器"""
    try:
        logger.info("启动Arq工作器...")
        run_worker(WorkerSettings)
    except KeyboardInterrupt:
        logger.info("工作器被用户中断")
    except Exception as e:
        logger.error(f"工作器运行出错: {e}")
        raise

if __name__ == '__main__':
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    main()

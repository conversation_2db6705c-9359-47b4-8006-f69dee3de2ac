"""
异步抓包任务
"""

import asyncio
import logging
import json
import time
import os
import traceback
from datetime import datetime
# 确保ARQ任务进程能正确加载环境变量，解决数据库连接问题
from dotenv import load_dotenv
load_dotenv(override=True)
from typing import Dict, Any, Optional, List
from arq import ArqRedis
from arq.jobs import Job

from config.redis_config import redis_manager
from services.mysql_service import MySQLService
from services.postgres_service import PostgresService
from services.gaussdb_service_v2 import GaussDBServiceV2
from services.mongo_service import MongoService
from services.remote_packet_capture_service import RemotePacketCaptureService
from services.postgres_local_packet_capture_service import PostgresLocalPacketCaptureService
from services.gaussdb_packet_capture_service import gaussdb_packet_service
from services.mongo_local_packet_capture_service import MongoLocalPacketCaptureService
from utils.timezone_utils import get_current_time, format_time


logger = logging.getLogger(__name__)

async def _sleep_cancellable(seconds: float, task_id: str) -> None:
    """可取消的睡眠：每0.5秒检查一次取消标记，提前返回以尽快停止任务。"""
    interval = 0.5
    remaining = float(seconds)
    from config.redis_config import redis_manager as _rm
    while remaining > 0:
        try:
            # 检查取消标记
            val = await _rm.redis.get(f"task_cancelled:{task_id}")
            if val:
                return
        except Exception:
            # 忽略读取异常，继续睡眠
            pass
        step = min(interval, remaining)
        await asyncio.sleep(step)
        remaining -= step

async def _is_cancelled(task_id: str) -> bool:
    """检查任务是否被取消。"""
    try:
        from config.redis_config import redis_manager as _rm
        return bool(await _rm.redis.get(f"task_cancelled:{task_id}"))
    except Exception:
        return False

async def execute_oracle_sql_smart(oracle_service, sql_query: str, use_existing_connection: bool = False) -> Dict[str, Any]:
    """智能执行Oracle SQL - 自动检测SQL类型并选择合适的执行方法"""
    try:
        # 清理SQL - 移除分号和特殊字符
        sql_query = sql_query.strip()
        if sql_query.endswith(';'):
            sql_query = sql_query[:-1]

        # 移除可能的隐藏字符
        sql_query = ''.join(char for char in sql_query if ord(char) >= 32 or char in ['\n', '\t'])

        logger.info(f"Executing cleaned Oracle SQL: {sql_query} (use_existing_connection: {use_existing_connection})")

        # 检查是否为多语句
        if '\n' in sql_query:
            # 分割语句检查
            statements = [stmt.strip() for stmt in sql_query.split('\n') if stmt.strip()]
            if len(statements) > 1:
                logger.info(f"Detected multiple statements ({len(statements)}), using execute_multiple_statements")
                return await oracle_service.execute_multiple_statements(sql_query, use_existing_connection=use_existing_connection)

        # 单语句处理
        sql_upper = sql_query.upper().strip()

        if sql_upper.startswith('SELECT'):
            logger.info("Executing SELECT statement")
            return await oracle_service.execute_query(sql_query, use_existing_connection=use_existing_connection)
        elif sql_upper.startswith(('CREATE', 'INSERT', 'UPDATE', 'DELETE', 'DROP', 'ALTER')):
            logger.info(f"Executing {sql_upper.split()[0]} statement")
            return await oracle_service.execute_non_query(sql_query, use_existing_connection=use_existing_connection)
        else:
            # 未知类型，尝试作为查询执行
            logger.info("Unknown SQL type, trying as query first")
            try:
                return await oracle_service.execute_query(sql_query, use_existing_connection=use_existing_connection)
            except:
                logger.info("Query failed, trying as non-query")
                return await oracle_service.execute_non_query(sql_query, use_existing_connection=use_existing_connection)

    except Exception as e:
        logger.error(f"Failed to execute Oracle SQL: {e}")
        return {
            'success': False,
            'error': str(e),
            'data': [],
            'columns': [],
            'row_count': 0
        }

def json_serializer(obj):
    """JSON序列化处理器，处理datetime等特殊类型"""
    if isinstance(obj, datetime):
        from utils.timezone_utils import format_time, to_china_timezone
        # 确保时间是+8时区
        china_time = to_china_timezone(obj)
        return format_time(china_time)
    raise TypeError(f"Object of type {obj.__class__.__name__} is not JSON serializable")

class TaskStatus:
    """任务状态常量"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class TaskProgress:
    """任务进度管理 - 使用MySQL存储"""

    def __init__(self, task_id: str, redis: ArqRedis = None):
        self.task_id = task_id
        self.redis = redis  # 保留Redis引用，但主要使用MySQL
        self.start_time = time.time()
        self._mysql_service = None

    async def _get_task_service(self):
        """获取ARQ任务管理服务"""
        if not self._mysql_service:
            from services.arq_task_management_service import arq_task_management_service
            self._mysql_service = arq_task_management_service
            await self._mysql_service._ensure_initialized()
        return self._mysql_service

    async def update_progress(self, progress: int, message: str = "", status: str = TaskStatus.RUNNING):
        """更新任务进度"""
        try:
            task_service = await self._get_task_service()
            await task_service.update_task_progress(self.task_id, progress, message, status)
            logger.info(f"任务 {self.task_id} 进度更新: {progress}% - {message}")
        except Exception as e:
            logger.error(f"更新任务进度失败: {e}")
            # 如果任务服务失败，尝试使用Redis作为备份
            if self.redis:
                try:
                    progress_data = {
                        "task_id": self.task_id,
                        "progress": progress,
                        "message": message,
                        "status": status,
                        "start_time": self.start_time,
                        "current_time": time.time(),
                        "updated_at": format_time(get_current_time())
                    }
                    await self.redis.set(
                        f"task_progress:{self.task_id}",
                        json.dumps(progress_data),
                        ex=3600  # 1小时过期
                    )
                except Exception as redis_error:
                    logger.error(f"Redis备份也失败: {redis_error}")

    async def set_completed(self, result: Dict[str, Any]):
        """设置任务完成"""
        try:
            # 清理任务相关的tcpdump进程
            try:
                from services.tcpdump_process_manager import tcpdump_manager
                await tcpdump_manager.cleanup_task_processes(self.task_id, "任务完成")
            except Exception as e:
                logger.warning(f"清理任务 {self.task_id} 的tcpdump进程失败: {e}")

            task_service = await self._get_task_service()
            duration = time.time() - self.start_time
            await task_service.set_task_completed(self.task_id, result, duration)
            logger.info(f"任务 {self.task_id} 已完成")
        except Exception as e:
            logger.error(f"设置任务完成失败: {e}")
            # 如果任务服务失败，尝试使用Redis作为备份
            if self.redis:
                try:
                    await self.update_progress(100, "任务完成", TaskStatus.COMPLETED)
                    result_data = {
                        "task_id": self.task_id,
                        "status": TaskStatus.COMPLETED,
                        "result": result,
                        "completed_at": format_time(get_current_time()),
                        "duration": time.time() - self.start_time
                    }
                    await self.redis.set(
                        f"task_result:{self.task_id}",
                        json.dumps(result_data, default=json_serializer),
                        ex=86400  # 24小时过期
                    )
                except Exception as redis_error:
                    logger.error(f"Redis备份也失败: {redis_error}")

    async def set_failed(self, error: str):
        """设置任务失败"""
        try:
            # 清理任务相关的tcpdump进程
            try:
                from services.tcpdump_process_manager import tcpdump_manager
                await tcpdump_manager.cleanup_task_processes(self.task_id, "任务失败")
            except Exception as e:
                logger.warning(f"清理任务 {self.task_id} 的tcpdump进程失败: {e}")

            task_service = await self._get_task_service()
            duration = time.time() - self.start_time
            await task_service.set_task_failed(self.task_id, error, duration)
            logger.info(f"任务 {self.task_id} 已失败: {error}")
        except Exception as e:
            logger.error(f"设置任务失败失败: {e}")
            # 如果任务服务失败，尝试使用Redis作为备份
            if self.redis:
                try:
                    await self.update_progress(0, f"任务失败: {error}", TaskStatus.FAILED)
                    error_data = {
                        "task_id": self.task_id,
                        "status": TaskStatus.FAILED,
                        "error": error,
                        "failed_at": format_time(get_current_time()),
                        "duration": time.time() - self.start_time
                    }
                    await self.redis.set(
                        f"task_result:{self.task_id}",
                        json.dumps(error_data),
                        ex=86400  # 24小时过期
                    )
                except Exception as redis_error:
                    logger.error(f"Redis备份也失败: {redis_error}")

async def docker_build_task(ctx: Dict[str, Any], server_config_id: int, build_requests: list) -> Dict[str, Any]:
    """Docker环境构建异步任务"""
    task_id = ctx.get('job_id', 'unknown')
    redis = redis_manager.redis
    progress = TaskProgress(task_id, redis)

    try:
        await progress.update_progress(0, "开始Docker构建任务", TaskStatus.RUNNING)

        # 导入必要的服务
        from services.server_config_service import server_config_service
        from services.docker_environment_service import DockerEnvironmentService
        from services.database_config_service import database_config_service

        # 获取服务器配置
        await progress.update_progress(5, "获取服务器配置")
        server_config = await server_config_service.get_config(server_config_id)
        if not server_config:
            raise Exception(f"Server config {server_config_id} not found")

        docker_service = DockerEnvironmentService()
        results = []
        total_requests = len(build_requests)

        # 逐个构建Docker环境
        for i, request in enumerate(build_requests):
            current_progress = 10 + (i * 80 // total_requests)
            await progress.update_progress(
                current_progress,
                f"构建Docker环境 {i+1}/{total_requests}: {request['database_type']}"
            )

            try:
                # 构建单个Docker环境
                result = await docker_service.build_database_environment(
                    server_config,
                    request['ai_prompt'],
                    request['database_type']
                )

                # 如果构建成功，自动保存数据库配置
                if result['success']:
                    try:
                        # 保存数据库配置
                        from services.database_config_service import DatabaseConfigModel

                        database_config = DatabaseConfigModel(
                            name=f"{request['database_type']}-{result['container_name']}",
                            host=server_config.host,
                            port=result['port'],
                            user=result['username'],
                            password=result['password'],
                            database_name=result['database_name'],
                            database_type=request['database_type'],
                            description=f"Auto-generated {request['database_type']} database from Docker container {result['container_name']}",
                            is_default=False,
                            is_active=True
                        )

                        saved_config_id = await database_config_service.add_config(database_config)
                        saved_config = await database_config_service.get_config(saved_config_id)
                        result['database_config_id'] = saved_config.id
                        result['database_config_name'] = saved_config.name

                        logger.info(f"Database config saved: {saved_config.name}")
                    except Exception as e:
                        logger.warning(f"Failed to save database config: {e}")
                        result['database_config_warning'] = str(e)

                results.append({
                    "request": request,
                    "result": result,
                    "success": result['success']
                })

            except Exception as e:
                logger.error(f"Failed to build Docker environment for {request['database_type']}: {e}")
                results.append({
                    "request": request,
                    "result": {
                        "success": False,
                        "message": str(e)
                    },
                    "success": False
                })

        await progress.update_progress(95, "汇总构建结果")

        # 统计结果
        successful_builds = sum(1 for r in results if r['success'])
        failed_builds = total_requests - successful_builds

        final_result = {
            "total_requests": total_requests,
            "successful_builds": successful_builds,
            "failed_builds": failed_builds,
            "results": results,
            "summary": f"成功构建 {successful_builds}/{total_requests} 个Docker环境"
        }

        await progress.set_completed(final_result)
        return final_result

    except Exception as e:
        logger.error(f"Docker build task failed: {e}")
        await progress.set_failed(str(e))
        return {
            "total_requests": len(build_requests) if build_requests else 0,
            "successful_builds": 0,
            "failed_builds": len(build_requests) if build_requests else 0,
            "results": [],
            "error": str(e)
        }

async def docker_image_build_task(
    ctx: Dict[str, Any],
    server_config_id: int,
    image_repository: str,
    image_tag: str,
    database_type: str,
    ai_prompt: str = ""
) -> Dict[str, Any]:
    """Docker镜像构建异步任务"""
    task_id = ctx.get('job_id', 'unknown')
    redis = redis_manager.redis
    progress = TaskProgress(task_id, redis)

    try:
        await progress.update_progress(0, f"开始使用镜像 {image_repository}:{image_tag} 构建Docker环境")

        # 获取服务器配置
        await progress.update_progress(10, "获取服务器配置")
        from services.server_config_service import ServerConfigService
        server_config_service = ServerConfigService()
        await server_config_service.initialize()

        server_config = await server_config_service.get_config(server_config_id)
        if not server_config:
            raise Exception(f"服务器配置不存在: {server_config_id}")

        # 初始化Docker环境服务
        await progress.update_progress(20, "初始化Docker环境服务")
        from services.docker_environment_service import DockerEnvironmentService
        docker_service = DockerEnvironmentService()

        # 构建Docker环境
        await progress.update_progress(30, f"使用镜像 {image_repository}:{image_tag} 构建Docker环境")
        result = await docker_service.build_database_environment_with_image(
            server_config,
            image_repository,
            image_tag,
            database_type,
            ai_prompt
        )

        if not result['success']:
            raise Exception(result['message'])

        await progress.update_progress(70, "Docker环境构建成功，保存数据库配置")

        # 自动保存数据库配置
        database_config_id = None
        database_config_name = None

        try:
            from services.database_config_service import DatabaseConfigService, DatabaseConfigModel
            database_config_service = DatabaseConfigService()
            await database_config_service.initialize()

            # 构造数据库配置
            db_config = DatabaseConfigModel(
                name=f"{database_type}_{result['container_name']}",
                host=server_config.host,
                port=result['port'],
                user=result['username'],
                password=result['password'],
                database_name=result['database_name'],
                database_type=database_type,
                description=f"Auto-created from Docker container {result['container_name']} using image {image_repository}:{image_tag}"
            )

            # 保存到数据库
            saved_config_id = await database_config_service.add_config(db_config)
            saved_config = await database_config_service.get_config(saved_config_id)
            database_config_id = saved_config.id
            database_config_name = saved_config.name

            logger.info(f"Auto-saved database config: {saved_config.name}")

        except Exception as save_error:
            logger.warning(f"Failed to auto-save database config: {str(save_error)}")

        await progress.update_progress(100, "Docker镜像构建任务完成")

        final_result = {
            "success": True,
            "container_name": result['container_name'],
            "container_id": result['container_id'],
            "port": result['port'],
            "username": result['username'],
            "password": result['password'],
            "database_name": result['database_name'],
            "docker_command": result['docker_command'],
            "image_name": f"{image_repository}:{image_tag}",
            "database_config_id": database_config_id,
            "database_config_name": database_config_name,
            "message": f"Docker环境构建成功！镜像: {image_repository}:{image_tag}"
        }

        await progress.set_completed(final_result)
        return final_result

    except Exception as e:
        error_msg = f"Docker image build task failed: {str(e)}"
        logger.error(error_msg)
        logger.error(f"Task parameters: server_config_id={server_config_id}, image_repository={image_repository}, image_tag={image_tag}, database_type={database_type}")
        
        # 记录详细的错误信息
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
        
        await progress.set_failed(str(e))
        return {
            "success": False,
            "error": str(e),
            "task_id": task_id,
            "server_config_id": server_config_id,
            "image_repository": image_repository,
            "image_tag": image_tag,
            "database_type": database_type,
            "message": f"Docker镜像构建失败: {str(e)}"
        }

async def mysql_capture_task(ctx: Dict[str, Any], config_id: int, sql_query: str, capture_duration: int = 30) -> Dict[str, Any]:
    """MySQL异步抓包任务"""
    task_id = ctx.get('job_id', 'unknown')
    redis = redis_manager.redis
    progress = TaskProgress(task_id, redis)

    # 创建新的任务执行日志记录器
    from utils.task_execution_logger import create_task_logger, ExecutorSelector, ExecutorType
    task_logger = create_task_logger(
        task_id=task_id,
        task_type="mysql_capture",
        task_name=f"MySQL抓包任务-配置{config_id}"
    )

    try:
        # 初始化任务记录
        await task_logger.initialize(
            task_description=f"对配置ID {config_id} 执行MySQL抓包任务",
            database_config_id=config_id,
            input_sql_query=sql_query,
            capture_enabled=True,
            capture_duration=capture_duration
        )

        await progress.update_progress(0, "开始MySQL抓包任务")

        # 获取数据库配置
        await progress.update_progress(10, "获取数据库配置")
        await task_logger.update_progress(10, "获取数据库配置")

        from services.database_config_service import DatabaseConfigService
        db_config_service = DatabaseConfigService()
        await db_config_service.initialize()

        config = await db_config_service.get_config(config_id)
        if not config:
            raise Exception(f"数据库配置不存在: {config_id}")

        # 初始化MySQL服务
        mysql_service = MySQLService(
            host=config.host,
            port=config.port,
            user=config.user,
            password=config.password,
            database=config.database_name
        )

        # 选择执行器并开始执行
        executor_type, selection_reason = ExecutorSelector.select_executor("mysql")
        await task_logger.start_execution(
            executor_type=executor_type,
            executor_selection_reason=selection_reason
        )

        # 初始化抓包服务
        await progress.update_progress(20, "初始化抓包服务")
        await task_logger.update_progress(20, "初始化抓包服务")

        from services.mysql_local_packet_capture_service import MySQLLocalPacketCaptureService
        capture_service = MySQLLocalPacketCaptureService()
        # 将task_id注入到底层tcpdump以便取消时能按任务号终止进程
        try:
            capture_service.local_tcpdump_service.set_task_id(task_id)
        except Exception as e:
            logger.warning(f"Failed to set task_id for MySQL capture: {e}")

        # 获取服务器配置ID（如果有的话）
        server_config_id = None
        try:
            from services.server_config_service import ServerConfigService
            server_config_service = ServerConfigService()
            await server_config_service.initialize()
            default_config = await server_config_service.get_default_config()
            if default_config:
                server_config_id = default_config.id
        except Exception as e:
            logger.warning(f"Failed to get server config for MySQL capture: {e}")

        # 开始智能抓包
        await progress.update_progress(30, "开始智能数据包捕获")
        await task_logger.update_progress(30, "开始智能数据包捕获")

        try:
            # 使用本地抓包功能
            capture_file = await capture_service.start_capture(
                config.host,
                config.port,
                server_config_id
            )
            logger.info(f"Started MySQL smart packet capture: {capture_file}")
        except Exception as e:
            logger.error(f"Smart capture failed, falling back to regular capture: {str(e)}")
            # 回退到常规抓包
            capture_file = await capture_service.start_capture(
                config.host,
                config.port
            )
            logger.info(f"Started MySQL regular packet capture: {capture_file}")

        await progress.update_progress(40, "建立数据库持久连接")
        await task_logger.update_progress(40, "建立数据库持久连接")

        # 建立持久连接
        try:
            connection_success = await mysql_service.create_persistent_connection()
            if not connection_success:
                raise Exception("Failed to create persistent MySQL connection")
            logger.info("MySQL persistent connection established")
        except Exception as e:
            logger.error(f"Failed to create persistent connection: {e}")
            # 清理抓包并抛出异常
            try:
                await capture_service.stop_capture()
            except Exception as e:

                logger.error(f"操作失败: {type(e).__name__}: {str(e)}")

                logger.error(f"异常堆栈跟踪: {traceback.format_exc()}")
            raise Exception(f"Database connection failed: {e}")

        await progress.update_progress(50, "执行SQL查询")
        await task_logger.update_progress(50, f"执行SQL查询: {sql_query[:50]}...")

        # 使用持久连接执行SQL查询
        try:
            result = await mysql_service.execute_query_with_persistent_connection(sql_query)
            logger.info("SQL executed successfully with persistent connection")
        except Exception as e:
            logger.warning(f"SQL执行失败，但继续抓包: {e}")
            result = {"error": str(e)}

        await progress.update_progress(60, "关闭数据库连接")
        await task_logger.update_progress(60, "关闭数据库连接")

        # 关闭持久连接
        try:
            await mysql_service.close_persistent_connection()
            logger.info("MySQL persistent connection closed")
        except Exception as e:
            logger.warning(f"Error closing persistent connection: {e}")

        # 等待抓包完成
        await progress.update_progress(70, f"等待抓包完成 ({capture_duration}秒)")
        await _sleep_cancellable(capture_duration, task_id)
        if await _is_cancelled(task_id):
            try:
                await capture_service.stop_capture()
            except Exception:
                pass
            await task_logger.fail_execution(
                error_code="TASK_CANCELLED",
                error_message="任务被取消",
                error_details={"task_id": task_id}
            )
            await progress.set_failed("任务被取消")
            return {"cancelled": True, "task_id": task_id}
        
        # 停止抓包
        await progress.update_progress(90, "停止抓包并下载文件")
        final_file = None
        try:
            final_file = await capture_service.stop_capture()
            if final_file:
                logger.info(f"抓包文件已保存: {final_file}")
            else:
                logger.warning("抓包文件为空，但任务继续")
        except Exception as e:
            logger.warning(f"停止抓包时出错，但任务继续: {e}")

        # 检查抓包质量，如果是空包则重试
        if final_file:
            try:
                from utils.path_manager import path_manager
                full_path = path_manager.get_capture_file_path(final_file)
                
                if os.path.exists(full_path):
                    file_size = os.path.getsize(full_path)
                    logger.info(f"MySQL capture file size: {file_size} bytes (filename: {final_file}, path: {full_path})")
                else:
                    logger.warning(f"MySQL capture file not found at: {full_path}")
                    final_file = None
            except Exception as e:
                logger.error(f"检查抓包文件时出错: {str(e)}")
                final_file = None
        
        if final_file and os.path.exists(path_manager.get_capture_file_path(final_file)):
            file_size = os.path.getsize(path_manager.get_capture_file_path(final_file))

            if file_size <= 24:
                logger.warning("Detected empty MySQL capture file, attempting smart retry")
                await progress.update_progress(92, "检测到空包，正在智能重试")

                try:
                    # 使用智能抓包重试
                    from services.mysql_local_packet_capture_service import MySQLLocalPacketCaptureService
                    smart_capture_service = MySQLLocalPacketCaptureService()

                    retry_file = await smart_capture_service.start_capture(
                        config.host,
                        config.port
                    )

                    if retry_file:
                        logger.info(f"MySQL smart retry capture started: {retry_file}")
                        await progress.update_progress(94, "重新执行查询以产生流量")

                        # 等待抓包启动
                        await asyncio.sleep(3)

                        # 重新执行查询以产生流量
                        try:
                            await mysql_service.execute_query(sql_query)
                        except Exception as e:
                            logger.warning(f"Retry SQL execution failed: {e}")

                        # 等待流量产生
                        await asyncio.sleep(2)

                        # 停止重试抓包
                        await progress.update_progress(96, "停止重试抓包")
                        final_retry_file = None
                        try:
                            final_retry_file = await smart_capture_service.stop_capture()
                        except Exception as e:
                            logger.warning(f"停止重试抓包时出错: {e}")

                        if final_retry_file:
                            try:
                                # 使用path_manager获取完整路径进行验证
                                retry_full_path = path_manager.get_capture_file_path(final_retry_file)
                                
                                if os.path.exists(retry_full_path):
                                    retry_file_size = os.path.getsize(retry_full_path)
                                    logger.info(f"MySQL retry capture file size: {retry_file_size} bytes (filename: {final_retry_file}, path: {retry_full_path})")
    
                                    if retry_file_size > 24:
                                        final_file = final_retry_file
                                        logger.info(f"MySQL smart retry successful: {final_file}")
                                        await progress.update_progress(98, "智能重试成功")
                                    else:
                                        logger.warning("MySQL smart retry also produced empty capture")
                                else:
                                    logger.warning(f"MySQL retry capture file not found at: {retry_full_path}")
                            except Exception as e:
                                logger.error(f"检查重试抓包文件时出错: {str(e)}")
                        else:
                            logger.warning("MySQL smart retry did not return a valid filename")

                except Exception as e:
                    logger.error(f"MySQL smart retry failed: {str(e)}")
                    await progress.update_progress(95, f"智能重试失败: {str(e)}")

        # 准备结果
        task_result = {
            "type": "mysql_capture",
            "config_id": config_id,
            "sql_query": sql_query,
            "capture_file": final_file,
            "sql_result": result,
            "capture_duration": capture_duration,
            "database_info": {
                "host": config.host,
                "port": config.port,
                "database": config.database_name
            }
        }

        # 记录任务完成
        await task_logger.complete_execution(
            execution_result=task_result,
            capture_files=[final_file] if final_file else [],
            output_files=[]
        )

        await progress.set_completed(task_result)
        return task_result

    except Exception as e:
        logger.error(f"MySQL抓包任务失败: {e}")

        # 记录任务失败
        await task_logger.fail_execution(
            error_code="MYSQL_CAPTURE_ERROR",
            error_message=str(e),
            error_details={
                "config_id": config_id,
                "sql_query": sql_query,
                "capture_duration": capture_duration
            }
        )

        await progress.set_failed(str(e))
        raise

async def gateway_single_execution_task(ctx: Dict[str, Any], test_case_id: str, gateway_server_id: int, wait_time: int = 2) -> Dict[str, Any]:
    """网关单个执行 ARQ 任务：调用网关服务并把进度/结果写入任务系统"""
    task_id = ctx.get('job_id', 'unknown')
    redis = redis_manager.redis
    progress = TaskProgress(task_id, redis)

    try:
        await progress.update_progress(0, "开始网关单个执行", TaskStatus.RUNNING)

        # 早期取消检查
        if await _is_cancelled(task_id):
            await progress.set_failed("任务被取消")
            return {"cancelled": True, "task_id": task_id}

        # 执行网关测试
        await progress.update_progress(10, "上传PCAP并触发Kafka消费")
        from services.gateway_execution_service import gateway_execution_service
        result = await gateway_execution_service.execute_gateway_test(
            test_case_id=test_case_id,
            gateway_server_id=gateway_server_id,
            wait_time=wait_time
        )

        # 结果判定与完成
        msg = "网关执行完成" if result.get("success") else f"网关执行失败: {result.get('error', '未知错误')}"
        await progress.update_progress(100, msg, TaskStatus.COMPLETED if result.get("success") else TaskStatus.FAILED)
        await progress.set_completed({
            "task_type": "网关单个执行",
            "test_case_id": test_case_id,
            "gateway_server_id": gateway_server_id,
            "wait_time": wait_time,
            **result
        })
        return result

    except Exception as e:
        logging.error(f"gateway_single_execution_task failed: {e}")
        await progress.set_failed(str(e))
        return {"success": False, "error": str(e)}

async def gateway_batch_execution_task(ctx: Dict[str, Any], test_case_ids: List[str], gateway_server_id: int, wait_time: int = 2) -> Dict[str, Any]:
    """网关批量执行 ARQ 任务：串行执行多个用例并汇总结果"""
    task_id = ctx.get('job_id', 'unknown')
    redis = redis_manager.redis
    progress = TaskProgress(task_id, redis)

    try:
        total = len(test_case_ids or [])
        await progress.update_progress(0, f"开始批量执行，共 {total} 个用例", TaskStatus.RUNNING)

        from services.gateway_execution_service import gateway_execution_service
        results: List[Dict[str, Any]] = []
        success_count = 0

        for idx, tc_id in enumerate(test_case_ids, start=1):
            # 取消检查
            if await _is_cancelled(task_id):
                await progress.set_failed("任务被取消")
                return {"cancelled": True, "task_id": task_id}

            await progress.update_progress(int((idx - 1) / max(total,1) * 100), f"执行第 {idx}/{total} 个用例...")
            
            # 增加重试机制处理网络不稳定问题
            max_retries = 3
            retry_count = 0
            success = False
            
            while retry_count < max_retries and not success:
                try:
                    # 确保Redis连接健康
                    if retry_count > 0:
                        logger.info(f"用例 {tc_id} 重试第 {retry_count} 次...")
                        await asyncio.sleep(2 ** retry_count)  # 指数退避
                        await progress.update_progress(int((idx - 1) / max(total,1) * 100), f"重试执行第 {idx}/{total} 个用例 (第{retry_count + 1}次)...")
                        
                        # 检查并重新初始化Redis连接
                        try:
                            from config.redis_config import redis_manager
                            await redis_manager.ensure_connection()
                        except Exception as redis_e:
                            logger.warning(f"Redis连接检查失败: {redis_e}")
                    
                    r = await gateway_execution_service.execute_gateway_test(
                        test_case_id=tc_id,
                        gateway_server_id=gateway_server_id,
                        wait_time=wait_time
                    )
                    
                    results.append({
                        "test_case_id": tc_id,
                        "success": bool(r.get("success")),
                        "error": r.get("error"),
                        "summary": r.get("summary"),
                        "sql_comparison": r.get("sql_comparison")
                    })
                    if r.get("success"):
                        success_count += 1
                    success = True
                    
                except Exception as case_e:
                    retry_count += 1
                    error_msg = str(case_e)
                    logger.warning(f"批量执行用例 {tc_id} 失败 (尝试 {retry_count}/{max_retries}): {error_msg}")
                    
                    # 如果是连接相关错误，记录更多信息
                    if any(keyword in error_msg.lower() for keyword in ['connection', 'reset', 'timeout', 'redis']):
                        logger.error(f"检测到连接相关错误: {error_msg}")
                    
                    if retry_count >= max_retries:
                        logger.error(f"批量执行用例 {tc_id} 最终失败: {error_msg}")
                        results.append({
                            "test_case_id": tc_id,
                            "success": False,
                            "error": f"执行失败 (重试{max_retries}次后): {error_msg}"
                        })

            await progress.update_progress(int(idx / max(total,1) * 100), f"已完成 {idx}/{total} 个用例")

        summary = {
            "total_cases": total,
            "success_cases": success_count,
            "failed_cases": total - success_count,
            "success_rate": (success_count / total * 100.0) if total else 0.0,
            "gateway_server_id": gateway_server_id
        }
        final_result = {
            "task_type": "网关批量执行",
            "results": results,
            "summary": summary,
            "message": f"批量执行完成，成功 {success_count} 个，失败 {total - success_count} 个，成功率 {summary['success_rate']:.1f}%"
        }

        await progress.update_progress(100, "批量执行完成", TaskStatus.COMPLETED)
        await progress.set_completed(final_result)
        return final_result

    except Exception as e:
        logging.error(f"gateway_batch_execution_task failed: {e}")
        await progress.set_failed(str(e))
        return {"success": False, "error": str(e)}

async def postgres_capture_task(ctx: Dict[str, Any], config_id: int, sql_query: str, capture_duration: int = 30) -> Dict[str, Any]:
    """PostgreSQL异步抓包任务"""
    task_id = ctx.get('job_id', 'unknown')
    redis = redis_manager.redis
    progress = TaskProgress(task_id, redis)

    # 创建新的任务执行日志记录器
    from utils.task_execution_logger import create_task_logger, ExecutorSelector, ExecutorType
    task_logger = create_task_logger(
        task_id=task_id,
        task_type="postgres_capture",
        task_name=f"PostgreSQL抓包任务-配置{config_id}"
    )

    try:
        # 初始化任务记录
        await task_logger.initialize(
            task_description=f"对配置ID {config_id} 执行PostgreSQL抓包任务",
            database_config_id=config_id,
            input_sql_query=sql_query,
            capture_enabled=True,
            capture_duration=capture_duration
        )

        # 选择执行器并开始执行
        executor_type, selection_reason = ExecutorSelector.select_executor("postgresql")
        await task_logger.start_execution(
            executor_type=executor_type,
            executor_selection_reason=selection_reason
        )

        await progress.update_progress(0, "开始PostgreSQL抓包任务")

        # 获取数据库配置
        await progress.update_progress(10, "获取数据库配置")
        await task_logger.update_progress(10, "获取数据库配置")

        from services.database_config_service import DatabaseConfigService
        db_config_service = DatabaseConfigService()
        await db_config_service.initialize()

        config = await db_config_service.get_config(config_id)
        if not config:
            raise Exception(f"数据库配置不存在: {config_id}")

        # 初始化PostgreSQL服务
        postgres_service = PostgresService(
            host=config.host,
            port=config.port,
            user=config.user,
            password=config.password,
            database=config.database_name
        )
        await postgres_service.initialize()

        # 初始化抓包服务
        await progress.update_progress(20, "初始化抓包服务")
        capture_service = PostgresLocalPacketCaptureService()
        # 注入task_id，便于取消时精准终止tcpdump
        try:
            capture_service.local_tcpdump_service.set_task_id(task_id)
        except Exception as e:
            logger.warning(f"Failed to set task_id for Postgres capture: {e}")

        # 开始抓包
        await progress.update_progress(30, "开始数据包捕获")
        capture_file = await capture_service.start_capture(
            config.host,
            config.port
        )

        # 早期取消检查
        if await _is_cancelled(task_id):
            try:
                await capture_service.stop_capture()
            except Exception:
                pass
            await progress.set_failed("任务被取消")
            return {"cancelled": True, "task_id": task_id}

        await progress.update_progress(40, "建立PostgreSQL持久连接")

        # 建立持久连接
        try:
            connection_success = await postgres_service.create_persistent_connection()
            if not connection_success:
                raise Exception("Failed to create persistent PostgreSQL connection")
            logger.info("PostgreSQL persistent connection established")
        except Exception as e:
            logger.error(f"Failed to create persistent PostgreSQL connection: {e}")
            # 清理抓包并抛出异常
            try:
                await capture_service.stop_capture()
            except Exception as e:

                logger.error(f"操作失败: {type(e).__name__}: {str(e)}")

                logger.error(f"异常堆栈跟踪: {traceback.format_exc()}")
            raise Exception(f"PostgreSQL connection failed: {e}")

        await progress.update_progress(50, "执行SQL查询")

        # 使用持久连接执行SQL查询
        try:
            result = await postgres_service.execute_sql_query_with_persistent_connection(sql_query)
            logger.info("PostgreSQL executed successfully with persistent connection")
        except Exception as e:
            logger.warning(f"PostgreSQL查询失败，但继续抓包: {e}")
            result = {"error": str(e)}

        await progress.update_progress(60, "关闭PostgreSQL连接")

        # 关闭持久连接
        try:
            await postgres_service.close_persistent_connection()
            logger.info("PostgreSQL persistent connection closed")
        except Exception as e:
            logger.warning(f"Error closing PostgreSQL persistent connection: {e}")

        # 等待抓包完成（可取消睡眠）
        await progress.update_progress(70, f"等待抓包完成 ({capture_duration}秒)")
        await _sleep_cancellable(capture_duration, task_id)
        if await _is_cancelled(task_id):
            try:
                await capture_service.stop_capture()
            except Exception:
                pass
            await progress.set_failed("任务被取消")
            return {"cancelled": True, "task_id": task_id}
        
        # 停止抓包
        await progress.update_progress(90, "停止抓包并下载文件")
        final_file = await capture_service.stop_capture()

        # 检查抓包质量，如果是空包则重试
        if final_file and os.path.exists(final_file):
            file_size = os.path.getsize(final_file)
            logger.info(f"PostgreSQL capture file size: {file_size} bytes")

            if file_size <= 24:
                logger.warning("Detected empty PostgreSQL capture file, attempting smart retry")
                await progress.update_progress(92, "检测到空包，正在智能重试")

                try:
                    # 使用智能抓包重试
                    retry_file = await capture_service.start_smart_capture(
                        postgres_host=config.host,
                        postgres_port=config.port
                    )

                    if retry_file:
                        logger.info(f"PostgreSQL smart retry capture started: {retry_file}")
                        await progress.update_progress(94, "重新执行查询以产生流量")

                        # 等待抓包启动
                        await asyncio.sleep(3)

                        # 重新执行查询以产生流量
                        try:
                            await postgres_service.execute_sql_query(sql_query)
                        except Exception as e:
                            logger.warning(f"Retry SQL execution failed: {e}")

                        # 等待流量产生
                        await asyncio.sleep(2)

                        # 停止重试抓包
                        await progress.update_progress(96, "停止重试抓包")
                        final_retry_file = await capture_service.stop_capture()

                        if final_retry_file and os.path.exists(final_retry_file):
                            retry_file_size = os.path.getsize(final_retry_file)
                            logger.info(f"PostgreSQL retry capture file size: {retry_file_size} bytes")

                            if retry_file_size > 24:
                                final_file = final_retry_file
                                logger.info(f"PostgreSQL smart retry successful: {final_file}")
                                await progress.update_progress(98, "智能重试成功")
                            else:
                                logger.warning("PostgreSQL smart retry also produced empty capture")
                        else:
                            logger.warning("PostgreSQL smart retry did not produce a valid file")

                except Exception as e:
                    logger.error(f"PostgreSQL smart retry failed: {str(e)}")
                    await progress.update_progress(95, f"智能重试失败: {str(e)}")

        # 准备结果
        task_result = {
            "type": "postgres_capture",
            "config_id": config_id,
            "sql_query": sql_query,
            "capture_file": final_file,
            "sql_result": result,
            "capture_duration": capture_duration,
            "database_info": {
                "host": config.host,
                "port": config.port,
                "database": config.database_name
            }
        }

        # 记录任务完成
        await task_logger.complete_execution(
            execution_result=task_result,
            capture_files=[final_file] if final_file else [],
            output_files=[]
        )

        await progress.set_completed(task_result)
        return task_result

    except Exception as e:
        logger.error(f"PostgreSQL抓包任务失败: {e}")

        # 记录任务失败
        await task_logger.fail_execution(
            error_code="POSTGRES_CAPTURE_ERROR",
            error_message=str(e),
            error_details={
                "config_id": config_id,
                "sql_query": sql_query,
                "capture_duration": capture_duration
            }
        )

        await progress.set_failed(str(e))
        raise

async def mongo_capture_task(ctx: Dict[str, Any], config_id: int, mongo_query: str, capture_duration: int = 30, executor_type: str = "python") -> Dict[str, Any]:
    """MongoDB异步抓包任务"""
    task_id = ctx.get('job_id', 'unknown')
    redis = redis_manager.redis
    progress = TaskProgress(task_id, redis)

    # 创建新的任务执行日志记录器
    from utils.task_execution_logger import create_task_logger, ExecutorSelector, ExecutorType
    task_logger = create_task_logger(
        task_id=task_id,
        task_type="mongo_capture",
        task_name=f"MongoDB抓包任务-配置{config_id}"
    )

    try:
        # 初始化任务记录
        await task_logger.initialize(
            task_description=f"对配置ID {config_id} 执行MongoDB抓包任务",
            database_config_id=config_id,
            input_mongo_query=mongo_query,
            capture_enabled=True,
            capture_duration=capture_duration
        )

        # 选择执行器并开始执行
        selected_executor_type = ExecutorType.C if executor_type.lower() == "c" else ExecutorType.PYTHON
        executor_type_obj, selection_reason = ExecutorSelector.select_executor("mongodb", prefer_c=(executor_type.lower() == "c"))

        await task_logger.start_execution(
            executor_type=executor_type_obj,
            executor_selection_reason=selection_reason
        )

        await progress.update_progress(0, "开始MongoDB抓包任务")

        # 获取数据库配置
        await progress.update_progress(10, "获取数据库配置")
        await task_logger.update_progress(10, "获取数据库配置")

        from services.database_config_service import DatabaseConfigService
        db_config_service = DatabaseConfigService()
        await db_config_service.initialize()

        config = await db_config_service.get_config(config_id)
        if not config:
            raise Exception(f"数据库配置不存在: {config_id}")

        # 初始化MongoDB服务
        await progress.update_progress(15, "初始化MongoDB服务")
        mongo_service = MongoService(
            host=config.host,
            port=config.port,
            user=config.user,
            password=config.password,
            database=config.database_name
        )
        await mongo_service.initialize()

        # 根据选择的执行器类型初始化
        await progress.update_progress(18, f"初始化MongoDB执行器 ({executor_type})")
        use_c_executor = False
        mongo_c_service = None

        if executor_type == "c":
            from services.mongo_c_executor_service import mongo_c_executor_service
            if mongo_c_executor_service.is_available():
                mongo_c_service = mongo_c_executor_service
                use_c_executor = True
                logger.info("Using MongoDB C executor as requested")
            else:
                logger.warning("MongoDB C executor not available, falling back to Python")
        else:
            logger.info("Using MongoDB Python executor as requested")

        # 初始化抓包服务
        await progress.update_progress(20, "初始化抓包服务")
        capture_service = MongoLocalPacketCaptureService()
        try:
            capture_service.local_tcpdump_service.set_task_id(task_id)
        except Exception as e:
            logger.warning(f"Failed to set task_id for Mongo capture: {e}")

        # 开始抓包
        await progress.update_progress(30, "开始数据包捕获")
        capture_file = await capture_service.start_capture(
            config.host,
            config.port
        )

        await progress.update_progress(40, "建立MongoDB持久连接")

        # 建立持久连接
        try:
            if use_c_executor and mongo_c_service:
                connection_success = await mongo_c_service.create_persistent_connection(
                    config.host, config.port, config.user, config.password, config.database_name
                )
                if not connection_success:
                    logger.warning("C executor connection failed, falling back to Python")
                    use_c_executor = False
                    mongo_c_service = None
                    connection_success = await mongo_service.create_persistent_connection()
                else:
                    logger.info("MongoDB C executor persistent connection established")
            else:
                connection_success = await mongo_service.create_persistent_connection()

            if not connection_success:
                raise Exception("Failed to create persistent MongoDB connection")

        except Exception as e:
            logger.error(f"Failed to create persistent MongoDB connection: {e}")
            # 清理抓包并抛出异常
            try:
                await capture_service.stop_capture()
            except Exception as e:

                logger.error(f"操作失败: {type(e).__name__}: {str(e)}")

                logger.error(f"异常堆栈跟踪: {traceback.format_exc()}")
            raise Exception(f"MongoDB connection failed: {e}")

        await progress.update_progress(50, "执行MongoDB查询")

        # 使用持久连接执行MongoDB查询
        try:
            if use_c_executor and mongo_c_service:
                result = await mongo_c_service.execute_mongo_query_persistent(mongo_query)
                logger.info("MongoDB executed successfully with C executor persistent connection")
            else:
                result = await mongo_service.execute_mongo_query_with_persistent_connection(mongo_query)
                logger.info("MongoDB executed successfully with Python persistent connection")
        except Exception as e:
            logger.warning(f"MongoDB查询失败，但继续抓包: {e}")
            result = {"error": str(e)}

        await progress.update_progress(60, "关闭MongoDB连接")

        # 关闭持久连接
        try:
            if use_c_executor and mongo_c_service:
                await mongo_c_service.close_persistent_connection()
                logger.info("MongoDB C executor persistent connection closed")
            else:
                await mongo_service.close_persistent_connection()
                logger.info("MongoDB Python persistent connection closed")
        except Exception as e:
            logger.warning(f"Error closing MongoDB persistent connection: {e}")

        # 等待抓包完成
        await progress.update_progress(70, f"等待抓包完成 ({capture_duration}秒)")
        await _sleep_cancellable(capture_duration, task_id)
        if await _is_cancelled(task_id):
            try:
                await capture_service.stop_capture()
            except Exception:
                pass
            await task_logger.fail_execution(
                error_code="TASK_CANCELLED",
                error_message="任务被取消",
                error_details={"task_id": task_id}
            )
            await progress.set_failed("任务被取消")
            return {"cancelled": True, "task_id": task_id}
        
        # 停止抓包
        await progress.update_progress(90, "停止抓包并下载文件")
        final_file = await capture_service.stop_capture()

        # 检查抓包质量，如果是空包则重试
        if final_file and os.path.exists(final_file):
            file_size = os.path.getsize(final_file)
            logger.info(f"MongoDB capture file size: {file_size} bytes")

            if file_size <= 24:
                logger.warning("Detected empty MongoDB capture file, attempting smart retry")
                await progress.update_progress(92, "检测到空包，正在智能重试")

                try:
                    # 使用智能抓包重试
                    retry_file = await capture_service.start_smart_capture(
                        mongo_host=config['host'],
                        mongo_port=config['port']
                    )

                    if retry_file:
                        logger.info(f"MongoDB smart retry capture started: {retry_file}")
                        await progress.update_progress(94, "重新执行查询以产生流量")

                        # 等待抓包启动
                        await asyncio.sleep(3)

                        # 重新执行查询以产生流量
                        try:
                            await mongo_service.execute_mongo_query(mongo_query)
                        except Exception as e:
                            logger.warning(f"Retry MongoDB query execution failed: {e}")

                        # 等待流量产生
                        await asyncio.sleep(2)

                        # 停止重试抓包
                        await progress.update_progress(96, "停止重试抓包")
                        final_retry_file = await capture_service.stop_capture()

                        if final_retry_file and os.path.exists(final_retry_file):
                            retry_file_size = os.path.getsize(final_retry_file)
                            logger.info(f"MongoDB retry capture file size: {retry_file_size} bytes")

                            if retry_file_size > 24:
                                final_file = final_retry_file
                                logger.info(f"MongoDB smart retry successful: {final_file}")
                                await progress.update_progress(98, "智能重试成功")
                            else:
                                logger.warning("MongoDB smart retry also produced empty capture")
                        else:
                            logger.warning("MongoDB smart retry did not produce a valid file")

                except Exception as e:
                    logger.error(f"MongoDB smart retry failed: {str(e)}")
                    await progress.update_progress(95, f"智能重试失败: {str(e)}")

        # 准备结果
        task_result = {
            "type": "mongo_capture",
            "config_id": config_id,
            "mongo_query": mongo_query,
            "capture_file": final_file,
            "query_result": result,
            "capture_duration": capture_duration,
            "database_info": {
                "host": config.host,
                "port": config.port,
                "database": config.database_name
            }
        }
        
        await progress.set_completed(task_result)
        return task_result
        
    except Exception as e:
        logger.error(f"MongoDB抓包任务失败: {e}")
        await progress.set_failed(str(e))
        raise

async def ai_mysql_capture_task(ctx: Dict[str, Any], config_id: int, natural_query: str, capture_duration: int = 30) -> Dict[str, Any]:
    """AI+MySQL抓包异步任务（包含大模型请求）"""
    task_id = ctx.get('job_id', 'unknown')
    redis = redis_manager.redis
    progress = TaskProgress(task_id, redis)

    try:
        logger.info(f"开始AI+MySQL抓包任务: {task_id}, 自然语言查询: {natural_query}")

        # 1. 使用MCP服务解析自然语言为SQL（5-25%）
        await progress.update_progress(5, "正在使用MCP服务解析自然语言查询...")

        from services.mysql_mcp_agent_service import mysql_mcp_agent_service

        # 获取数据库配置以提供上下文
        from services.database_config_service import database_config_service
        config = await database_config_service.get_config(config_id)
        if not config:
            raise Exception(f"数据库配置不存在: {config_id}")

        # 配置MCP代理服务的MySQL连接
        await mysql_mcp_agent_service.configure_mysql_connection(config_id)

        # 使用MCP服务生成SQL，提供数据库上下文和配置ID
        sql_generation_result = await mysql_mcp_agent_service.generate_sql_from_natural_language(
            natural_query,
            config.database_name,  # 提供数据库上下文
            config_id  # 提供配置ID以获取正确的数据库连接
        )

        if not sql_generation_result.get("success", False):
            raise Exception(f"MCP SQL生成失败: {sql_generation_result.get('error', 'Unknown error')}")

        sql_query = sql_generation_result.get("sql_query", "")
        if not sql_query:
            raise Exception("MCP服务未生成有效的SQL语句")

        await progress.update_progress(25, f"MCP解析完成，生成SQL: {sql_query[:100]}...")

        # 2. 配置MCP MySQL服务（25-30%）
        await progress.update_progress(25, "配置MCP MySQL服务")

        # 配置MCP MySQL服务连接
        from services.mcp_mysql_service import mcp_mysql_service
        await mcp_mysql_service.configure_mysql_connection(config_id)

        # 3. 配置MCP MySQL服务（30-35%）
        await progress.update_progress(30, "配置MySQL连接")
        from services.mcp_mysql_service import mcp_mysql_service
        mysql_config_result = await mcp_mysql_service.configure_mysql_connection(config_id)
        logger.info(f"MySQL配置结果: {mysql_config_result}")

        # 4. 初始化抓包服务（35-40%）
        await progress.update_progress(35, "初始化抓包服务")
        from services.mysql_local_packet_capture_service import MySQLLocalPacketCaptureService
        from services.server_config_service import server_config_service

        # 获取默认服务器配置用于抓包
        default_server_config = await server_config_service.get_default_config()
        server_config_id = default_server_config.id if default_server_config else None

        capture_service = MySQLLocalPacketCaptureService()
        # 注入task_id，便于取消时精准终止tcpdump
        try:
            capture_service.local_tcpdump_service.set_task_id(task_id)
        except Exception as e:
            logger.warning(f"Failed to set task_id for AI MySQL capture: {e}")

        # 5. 开始智能抓包（40-45%）
        await progress.update_progress(40, "开始智能数据包捕获")
        try:
            # 使用智能抓包功能，传入MySQL目标主机和端口
            capture_file = await capture_service.start_capture(
                config.host,
                config.port,
                server_config_id
            )
            logger.info(f"Started MySQL smart packet capture: {capture_file}")
        except Exception as e:
            logger.error(f"Smart capture failed, falling back to regular capture: {str(e)}")
            # 回退到常规抓包
            capture_file = await capture_service.start_capture(
                config.host,
                config.port
            )
            logger.info(f"Started MySQL regular packet capture: {capture_file}")

        # 5.5. 等待抓包进程完全启动（45-50%）
        await progress.update_progress(45, "等待抓包进程完全启动...")
        await asyncio.sleep(3)  # 给抓包进程3秒时间完全启动

        # 6. 建立持久数据库连接（50-55%）
        await progress.update_progress(50, "建立持久数据库连接...")

        # 初始化MySQL服务用于持久连接
        from services.mysql_service import MySQLService
        mysql_service = MySQLService(
            host=config.host,
            port=config.port,
            user=config.user,
            password=config.password,
            database=config.database_name
        )
        await mysql_service.initialize()

        try:
            # 建立持久连接
            connection_success = await mysql_service.create_persistent_connection()
            if not connection_success:
                raise Exception("Failed to create persistent MySQL connection")
            logger.info("AI MySQL persistent connection established")
        except Exception as e:
            logger.error(f"Failed to create persistent connection: {e}")
            # 清理抓包并抛出异常
            try:
                await capture_service.stop_capture()
            except Exception as e:

                logger.error(f"操作失败: {type(e).__name__}: {str(e)}")

                logger.error(f"异常堆栈跟踪: {traceback.format_exc()}")
            raise Exception(f"Database connection failed: {e}")

        # 7. 使用持久连接执行SQL查询（55-65%）
        await progress.update_progress(55, f"使用持久连接执行SQL查询: {sql_query[:50]}...")

        try:
            # 使用持久连接执行SQL操作
            result = await mysql_service.execute_query_with_persistent_connection(sql_query)
            logger.info("AI SQL executed successfully with persistent connection")
        except Exception as e:
            logger.warning(f"AI SQL执行失败，但继续抓包: {e}")
            result = {"error": str(e), "service": "persistent_connection"}

        # 8. 关闭持久连接（65-70%）
        await progress.update_progress(65, "关闭数据库持久连接")

        try:
            await mysql_service.close_persistent_connection()
            logger.info("AI MySQL persistent connection closed")
        except Exception as e:
            logger.warning(f"Error closing AI persistent connection: {e}")

        # 8. 等待抓包完成（70-90%）
        await progress.update_progress(70, f"等待抓包完成 ({capture_duration + 5}秒)")
        await _sleep_cancellable(capture_duration + 5, task_id)
        if await _is_cancelled(task_id):
            try:
                await capture_service.stop_capture()
            except Exception:
                pass
            await progress.set_failed("任务被取消")
            return {"cancelled": True, "task_id": task_id}

        # 8. 停止抓包（90-95%）
        await progress.update_progress(90, "停止抓包并下载文件")
        final_file = None
        try:
            final_file = await capture_service.stop_capture()
            if final_file:
                logger.info(f"抓包文件已保存: {final_file}")
            else:
                logger.warning("抓包文件为空，但任务继续")
        except Exception as e:
            logger.warning(f"停止抓包时出错，但任务继续: {e}")

        # 9. 检查抓包文件大小并重试（95%）
        import os
        capture_quality = "unknown"
        if final_file and os.path.exists(final_file):
            file_size = os.path.getsize(final_file)
            logger.info(f"Capture file size: {file_size} bytes")

            if file_size <= 24:  # 检测到空包，尝试智能重试
                await progress.update_progress(95, "检测到空包，尝试智能重试")
                logger.warning(f"Detected empty capture file ({file_size} bytes), attempting smart retry")

                try:
                    # 使用智能抓包重试，传入正确的目标主机和端口
                    retry_capture_file = await capture_service.start_smart_capture(
                        mysql_host=config.host,
                        mysql_port=config.port,
                        server_config_id=server_config_id
                    )
                    logger.info(f"Smart retry capture started: {retry_capture_file}")

                    # 确保MCP MySQL连接配置正确
                    try:
                        await mcp_mysql_service.configure_mysql_connection(config_id)
                    except Exception as e:
                        logger.warning(f"Failed to reconfigure MCP MySQL for retry: {e}")

                    # 使用MCP服务重新执行查询以生成流量
                    await progress.update_progress(96, "使用MCP服务重新执行查询以生成流量")
                    try:
                        retry_result = await mcp_mysql_service.execute_sql_operations(sql_query)
                        if retry_result and not retry_result.get("error"):
                            result = retry_result  # 使用重试的结果
                            logger.info(f"MCP重试执行成功: {retry_result.get('successful', 0)}/{retry_result.get('total_statements', 0)} 成功")
                    except Exception as e:
                        logger.warning(f"MCP retry query failed: {e}")

                    # 等待一段时间让抓包捕获流量
                    await asyncio.sleep(5)

                    # 停止重试抓包
                    retry_final_file = await capture_service.stop_capture()

                    # 检查重试文件大小
                    if retry_final_file and os.path.exists(retry_final_file):
                        retry_file_size = os.path.getsize(retry_final_file)
                        logger.info(f"Retry capture file size: {retry_file_size} bytes")

                        if retry_file_size > 24:
                            final_file = retry_final_file  # 使用重试成功的文件
                            file_size = retry_file_size
                            logger.info(f"Smart retry successful: {final_file}")
                        else:
                            logger.warning(f"Smart retry also resulted in empty file: {retry_file_size} bytes")

                except Exception as retry_e:
                    logger.error(f"Smart retry failed: {retry_e}")
                    # 继续使用原始文件

            # 验证抓包质量
            if file_size > 100:
                capture_quality = "good"
            else:
                capture_quality = "poor"
        else:
            capture_quality = "failed"

        # 10. 分析抓包结果（98-100%）
        await progress.update_progress(98, "分析抓包结果")

        task_result = {
            "success": True,
            "natural_query": natural_query,
            "sql_query": sql_query,
            "query_result": result,
            "capture_file": final_file,
            "capture_quality": capture_quality,
            "config_id": config_id,
            "capture_duration": capture_duration,
            "task_type": "ai_mysql_capture"
        }

        await progress.set_completed(task_result)
        logger.info(f"AI+MySQL抓包任务完成: {task_id}, 抓包质量: {capture_quality}")

        return task_result

    except Exception as e:
        logger.error(f"AI+MySQL抓包任务失败: {e}")
        await progress.set_failed(str(e))
        raise

async def ai_postgres_capture_task(ctx: Dict[str, Any], config_id: int, natural_query: str, capture_duration: int = 30) -> Dict[str, Any]:
    """AI+PostgreSQL抓包异步任务（包含大模型请求）"""
    task_id = ctx.get('job_id', 'unknown')
    redis = redis_manager.redis
    progress = TaskProgress(task_id, redis)

    try:
        logger.info(f"开始AI+PostgreSQL抓包任务: {task_id}, 自然语言查询: {natural_query}")

        # 1. AI解析自然语言为SQL（5-25%）
        await progress.update_progress(5, "正在使用AI解析自然语言查询...")

        from services.postgres_ai_service import postgres_ai_service
        sql_query = await postgres_ai_service.parse_natural_language_to_sql(natural_query)

        await progress.update_progress(25, f"AI解析完成，生成SQL: {sql_query[:100]}...")

        # 2. 获取数据库配置（25-30%）
        await progress.update_progress(25, "获取数据库配置")
        from services.database_config_service import database_config_service
        config = await database_config_service.get_config(config_id)
        if not config:
            raise Exception(f"数据库配置不存在: {config_id}")

        # 3. 初始化PostgreSQL服务（30-35%）
        await progress.update_progress(30, "初始化PostgreSQL服务")
        from services.postgres_service import PostgresService

        # 根据数据库配置创建PostgreSQL服务实例
        postgres_service = PostgresService(
            host=config.host,
            port=config.port,
            user=config.user,
            password=config.password,
            database=config.database_name
        )
        await postgres_service.initialize()

        # 4. 获取默认服务器配置并初始化抓包服务（35-40%）
        await progress.update_progress(35, "初始化抓包服务")
        from services.postgres_local_packet_capture_service import PostgresLocalPacketCaptureService
        from services.server_config_service import server_config_service

        # 获取默认服务器配置用于抓包
        default_server_config = await server_config_service.get_default_config()
        server_config_id = default_server_config.id if default_server_config else None

        capture_service = PostgresLocalPacketCaptureService()
        try:
            capture_service.local_tcpdump_service.set_task_id(task_id)
        except Exception as e:
            logger.warning(f"Failed to set task_id for AI Postgres capture: {e}")

        # 5. 开始智能抓包（40-50%）
        await progress.update_progress(40, "开始智能数据包捕获")
        try:
            # 使用智能抓包功能，传入PostgreSQL目标主机和端口
            capture_file = await capture_service.start_smart_capture(
                target_host=config.host,
                target_port=config.port,
                server_config_id=server_config_id
            )
            logger.info(f"Started PostgreSQL smart packet capture: {capture_file}")
        except Exception as e:
            logger.error(f"Smart capture failed, falling back to regular capture: {str(e)}")
            # 回退到常规抓包
            capture_file = await capture_service.start_capture(
                target_host=config.host,
                target_port=config.port
            )
            logger.info(f"Started PostgreSQL regular packet capture: {capture_file}")

        # 6. 建立持久连接（50-55%）
        await progress.update_progress(50, "建立PostgreSQL持久连接")

        try:
            connection_success = await postgres_service.create_persistent_connection()
            if not connection_success:
                raise Exception("Failed to create persistent PostgreSQL connection")
            logger.info("AI PostgreSQL persistent connection established")
        except Exception as e:
            logger.error(f"Failed to create persistent PostgreSQL connection: {e}")
            # 清理抓包并抛出异常
            try:
                await capture_service.stop_capture()
            except Exception as e:

                logger.error(f"操作失败: {type(e).__name__}: {str(e)}")

                logger.error(f"异常堆栈跟踪: {traceback.format_exc()}")
            raise Exception(f"PostgreSQL connection failed: {e}")

        # 7. 执行SQL查询（55-65%）
        await progress.update_progress(55, f"执行SQL查询: {sql_query[:50]}...")

        try:
            result = await postgres_service.execute_sql_query_with_persistent_connection(sql_query)
            logger.info("AI PostgreSQL executed successfully with persistent connection")
        except Exception as e:
            logger.warning(f"PostgreSQL查询失败，但继续抓包: {e}")
            result = {"error": str(e)}

        # 8. 关闭持久连接（65-70%）
        await progress.update_progress(65, "关闭PostgreSQL持久连接")

        try:
            await postgres_service.close_persistent_connection()
            logger.info("AI PostgreSQL persistent connection closed")
        except Exception as e:
            logger.warning(f"Error closing AI PostgreSQL persistent connection: {e}")

        # 9. 等待抓包完成（70-90%）
        await progress.update_progress(70, f"等待抓包完成 ({capture_duration}秒)")
        await _sleep_cancellable(capture_duration, task_id)
        if await _is_cancelled(task_id):
            try:
                await capture_service.stop_capture()
            except Exception:
                pass
            await progress.set_failed("任务被取消")
            return {"cancelled": True, "task_id": task_id}

        # 8. 停止抓包（90-95%）
        await progress.update_progress(90, "停止抓包并下载文件")
        final_file = await capture_service.stop_capture()

        # 9. 检查抓包文件大小并重试（95%）
        import os
        if os.path.exists(final_file):
            file_size = os.path.getsize(final_file)
            logger.info(f"Capture file size: {file_size} bytes")

            if file_size <= 24:  # 检测到空包，尝试智能重试
                await progress.update_progress(95, "检测到空包，尝试智能重试")
                logger.warning(f"Detected empty capture file ({file_size} bytes), attempting smart retry")

                try:
                    # 使用智能抓包重试，传入正确的目标主机和端口
                    retry_capture_file = await capture_service.start_smart_capture(
                        target_host=config.host,
                        target_port=config.port,
                        server_config_id=server_config_id
                    )
                    logger.info(f"Smart retry capture started: {retry_capture_file}")

                    # 重新执行查询以生成流量
                    await progress.update_progress(96, "重新执行查询以生成流量")
                    try:
                        retry_result = await postgres_service.execute_sql_query(sql_query)
                        if retry_result and not retry_result.get("error"):
                            result = retry_result  # 使用重试的结果
                    except Exception as e:
                        logger.warning(f"Retry query failed: {e}")

                    # 等待一段时间让抓包捕获流量
                    await asyncio.sleep(5)

                    # 停止重试抓包
                    retry_final_file = await capture_service.stop_capture()

                    # 检查重试文件大小
                    if os.path.exists(retry_final_file):
                        retry_file_size = os.path.getsize(retry_final_file)
                        logger.info(f"Retry capture file size: {retry_file_size} bytes")

                        if retry_file_size > 24:
                            final_file = retry_final_file  # 使用重试成功的文件
                            logger.info(f"Smart retry successful: {final_file}")
                        else:
                            logger.warning(f"Smart retry also resulted in empty file: {retry_file_size} bytes")

                except Exception as retry_e:
                    logger.error(f"Smart retry failed: {retry_e}")
                    # 继续使用原始文件

        # 10. 分析抓包结果（98-100%）
        await progress.update_progress(98, "分析抓包结果")

        task_result = {
            "success": True,
            "natural_query": natural_query,
            "sql_query": sql_query,
            "query_result": result,
            "capture_file": final_file,
            "config_id": config_id,
            "capture_duration": capture_duration,
            "task_type": "ai_postgres_capture"
        }

        await progress.set_completed(task_result)
        logger.info(f"AI+PostgreSQL抓包任务完成: {task_id}")

        return task_result

    except Exception as e:
        logger.error(f"AI+PostgreSQL抓包任务失败: {e}")
        await progress.set_failed(str(e))
        raise

async def ai_mongo_capture_task(ctx: Dict[str, Any], config_id: int, natural_query: str, capture_duration: int = 30, executor_type: str = "python") -> Dict[str, Any]:
    """AI+MongoDB抓包异步任务（包含大模型请求）"""
    task_id = ctx.get('job_id', 'unknown')
    redis = redis_manager.redis
    progress = TaskProgress(task_id, redis)

    try:
        logger.info(f"开始AI+MongoDB抓包任务: {task_id}, 自然语言查询: {natural_query}")

        # 1. AI解析自然语言为MongoDB查询（5-25%）
        await progress.update_progress(5, "正在使用AI解析自然语言查询...")

        from services.mongo_ai_service import mongo_ai_service
        mongo_query = await mongo_ai_service.parse_natural_language_to_mongo(natural_query)

        await progress.update_progress(25, f"AI解析完成，生成MongoDB查询: {mongo_query[:100]}...")

        # 2. 获取数据库配置（25-30%）
        await progress.update_progress(25, "获取数据库配置")
        from services.database_config_service import database_config_service
        config = await database_config_service.get_config(config_id)
        if not config:
            raise Exception(f"数据库配置不存在: {config_id}")

        # 3. 初始化MongoDB服务（30-35%）
        await progress.update_progress(30, "初始化MongoDB服务")
        from services.mongo_service import MongoService

        # 根据数据库配置创建MongoDB服务实例
        mongo_service = MongoService(
            host=config.host,
            port=config.port,
            user=config.user,
            password=config.password,
            database=config.database_name
        )
        await mongo_service.initialize()

        # 3.5. 根据选择的执行器类型初始化（32-35%）
        await progress.update_progress(32, f"初始化MongoDB执行器 ({executor_type})")
        use_c_executor = False
        mongo_c_service = None

        if executor_type == "c":
            from services.mongo_c_executor_service import mongo_c_executor_service
            if mongo_c_executor_service.is_available():
                mongo_c_service = mongo_c_executor_service
                use_c_executor = True
                logger.info("AI task using MongoDB C executor as requested")
            else:
                logger.warning("AI task: MongoDB C executor not available, falling back to Python")
        else:
            logger.info("AI task using MongoDB Python executor as requested")

        # 4. 初始化抓包服务（35-40%）
        await progress.update_progress(35, "初始化抓包服务")
        from services.mongo_local_packet_capture_service import MongoLocalPacketCaptureService
        capture_service = MongoLocalPacketCaptureService()
        # 将当前任务ID传递给本地tcpdump服务，便于取消时按task_id清理进程
        try:
            capture_service.local_tcpdump_service.set_task_id(task_id)
        except Exception as e:
            logger.warning(f"Failed to set task_id for AI Mongo capture: {e}")

        # 5. 开始抓包（40-45%）
        await progress.update_progress(40, "开始数据包捕获")
        capture_file = await capture_service.start_capture(
            config.host,
            config.port
        )

        # 6. 建立持久连接（45-50%）
        await progress.update_progress(45, "建立MongoDB持久连接")

        try:
            if use_c_executor and mongo_c_service:
                connection_success = await mongo_c_service.create_persistent_connection(
                    config.host, config.port, config.user, config.password, config.database_name
                )
                if not connection_success:
                    logger.warning("AI task: C executor connection failed, falling back to Python")
                    use_c_executor = False
                    mongo_c_service = None
                    connection_success = await mongo_service.create_persistent_connection()
                else:
                    logger.info("AI MongoDB C executor persistent connection established")
            else:
                connection_success = await mongo_service.create_persistent_connection()

            if not connection_success:
                raise Exception("Failed to create persistent MongoDB connection")

        except Exception as e:
            logger.error(f"Failed to create persistent MongoDB connection: {e}")
            # 清理抓包并抛出异常
            try:
                await capture_service.stop_capture()
            except Exception as e:

                logger.error(f"操作失败: {type(e).__name__}: {str(e)}")

                logger.error(f"异常堆栈跟踪: {traceback.format_exc()}")
            raise Exception(f"MongoDB connection failed: {e}")

        # 7. 执行MongoDB查询（50-60%）
        await progress.update_progress(50, f"执行MongoDB查询: {mongo_query[:50]}...")

        try:
            if use_c_executor and mongo_c_service:
                result = await mongo_c_service.execute_mongo_query_persistent(mongo_query)
                logger.info("AI MongoDB executed successfully with C executor persistent connection")
            else:
                result = await mongo_service.execute_mongo_query_with_persistent_connection(mongo_query)
                logger.info("AI MongoDB executed successfully with Python persistent connection")
        except Exception as e:
            logger.warning(f"MongoDB查询失败，但继续抓包: {e}")
            result = {"error": str(e)}

        # 8. 关闭持久连接（60-65%）
        await progress.update_progress(60, "关闭MongoDB持久连接")

        try:
            if use_c_executor and mongo_c_service:
                await mongo_c_service.close_persistent_connection()
                logger.info("AI MongoDB C executor persistent connection closed")
            else:
                await mongo_service.close_persistent_connection()
                logger.info("AI MongoDB Python persistent connection closed")
        except Exception as e:
            logger.warning(f"Error closing AI MongoDB persistent connection: {e}")

        # 9. 等待抓包完成（65-90%）
        await progress.update_progress(65, f"等待抓包完成 ({capture_duration}秒)")
        await _sleep_cancellable(capture_duration, task_id)
        if await _is_cancelled(task_id):
            try:
                await capture_service.stop_capture()
            except Exception:
                pass
            await progress.set_failed("任务被取消")
            return {"cancelled": True, "task_id": task_id}

        # 8. 停止抓包（90-95%）
        await progress.update_progress(90, "停止抓包并下载文件")
        final_file = await capture_service.stop_capture()

        # 9. 分析抓包结果（95-100%）
        await progress.update_progress(95, "分析抓包结果")

        task_result = {
            "success": True,
            "natural_query": natural_query,
            "mongo_query": mongo_query,
            "query_result": result,
            "capture_file": final_file,
            "config_id": config_id,
            "capture_duration": capture_duration,
            "task_type": "ai_mongo_capture"
        }

        await progress.set_completed(task_result)
        logger.info(f"AI+MongoDB抓包任务完成: {task_id}")

        return task_result

    except Exception as e:
        logger.error(f"AI+MongoDB抓包任务失败: {e}")
        await progress.set_failed(str(e))
        raise

async def oracle_capture_task(ctx: Dict[str, Any], config_id: int, sql_query: str, capture_duration: int = 30) -> Dict[str, Any]:
    """Oracle异步抓包任务"""
    task_id = ctx.get('job_id', 'unknown')
    redis = redis_manager.redis
    progress = TaskProgress(task_id, redis)

    try:
        logger.info(f"开始Oracle抓包任务: {task_id}, SQL查询: {sql_query}")

        # 1. 获取数据库配置（5-10%）
        await progress.update_progress(5, "获取数据库配置")
        from services.database_config_service import database_config_service
        config = await database_config_service.get_config(config_id)
        if not config:
            raise Exception(f"数据库配置不存在: {config_id}")

        await progress.update_progress(10, f"数据库配置获取成功: {config.host}:{config.port}")

        # 2. 获取服务器配置（10-15%）
        await progress.update_progress(10, "获取服务器配置")
        from services.server_config_service import server_config_service
        server_configs = await server_config_service.get_all_configs()
        server_config_id = server_configs[0].id if server_configs else None

        await progress.update_progress(15, "服务器配置获取成功")

        # 3. 初始化Oracle抓包服务（15-25%）
        await progress.update_progress(15, "初始化Oracle抓包服务")
        from services.oracle_packet_capture_service import OraclePacketCaptureService
        capture_service = OraclePacketCaptureService()
        # 绑定task_id用于取消时终止tcpdump
        try:
            capture_service.local_tcpdump_service.set_task_id(task_id)
        except Exception as e:
            logger.warning(f"Failed to set task_id for Oracle capture: {e}")

        await progress.update_progress(25, "Oracle抓包服务初始化完成")

        # 4. 开始智能抓包（25-35%）
        await progress.update_progress(25, "开始智能数据包捕获")
        try:
            # 使用智能抓包功能，传入Oracle目标主机和端口，优先使用数据库配置关联的服务器
            capture_file = await capture_service.start_smart_capture(
                target_host=config.host,
                target_port=config.port,
                server_config_id=server_config_id
            )
            logger.info(f"Started Oracle smart packet capture: {capture_file}")
        except Exception as e:
            logger.error(f"Smart capture failed, falling back to regular capture: {str(e)}")
            # 回退到常规抓包
            capture_file = await capture_service.start_capture(
                target_host=config.host,
                target_port=config.port
            )
            logger.info(f"Started Oracle regular packet capture: {capture_file}")

        await progress.update_progress(35, f"数据包捕获已启动: {capture_file}")

        # 5. 执行Oracle查询（35-70%）
        await progress.update_progress(35, "建立Oracle持久连接")
        from services.oracle_service import OracleService
        oracle_service = OracleService(
            host=config.host,
            port=config.port,
            user=config.user,
            password=config.password,
            service_name=config.database_name
        )

        # 等待抓包启动（可取消）
        await _sleep_cancellable(2, task_id)
        if await _is_cancelled(task_id):
            try:
                await capture_service.stop_capture()
            except Exception:
                pass
            await progress.set_failed("任务被取消")
            return {"cancelled": True, "task_id": task_id}

        # 建立持久连接
        await progress.update_progress(40, "建立Oracle数据库连接")
        await oracle_service.connect()
        logger.info("Oracle persistent connection established")

        # 执行SQL查询（保持连接）
        await progress.update_progress(45, "执行Oracle查询（持久连接）")
        result = await execute_oracle_sql_smart(oracle_service, sql_query, use_existing_connection=True)

        # 关闭持久连接
        await progress.update_progress(65, "关闭Oracle数据库连接")
        await oracle_service.disconnect()
        logger.info("Oracle persistent connection closed")

        await progress.update_progress(70, f"Oracle查询执行完成，返回 {result.get('row_count', 0)} 行")

        # 6. 等待数据包捕获（70-80%）
        await progress.update_progress(70, f"等待数据包捕获完成（{capture_duration}秒）")
        await _sleep_cancellable(capture_duration, task_id)
        if await _is_cancelled(task_id):
            try:
                await capture_service.stop_capture()
            except Exception:
                pass
            await progress.set_failed("任务被取消")
            return {"cancelled": True, "task_id": task_id}

        await progress.update_progress(80, "数据包捕获等待完成")

        # 7. 停止抓包（80-90%）
        await progress.update_progress(80, "停止数据包捕获")
        try:
            final_file = await capture_service.stop_capture()
            logger.info(f"Oracle packet capture completed: {final_file}")
        except Exception as e:
            logger.warning(f"Failed to stop capture cleanly: {str(e)}")
            final_file = capture_file

        await progress.update_progress(90, f"数据包捕获已停止: {final_file}")

        # 8. 准备结果（90-100%）
        await progress.update_progress(90, "准备任务结果")

        # 准备结果
        task_result = {
            "type": "oracle_capture",
            "config_id": config_id,
            "sql_query": sql_query,
            "capture_file": final_file,
            "sql_result": result,
            "capture_duration": capture_duration,
            "database_info": {
                "host": config.host,
                "port": config.port,
                "service_name": config.database_name
            }
        }

        await progress.set_completed(task_result)
        return task_result

    except Exception as e:
        logger.error(f"Oracle抓包任务失败: {e}")
        await progress.set_failed(str(e))
        raise

async def ai_oracle_capture_task(ctx: Dict[str, Any], config_id: int, natural_query: str, capture_duration: int = 30) -> Dict[str, Any]:
    """AI+Oracle抓包异步任务（包含大模型请求）"""
    task_id = ctx.get('job_id', 'unknown')
    redis = redis_manager.redis
    progress = TaskProgress(task_id, redis)

    try:
        logger.info(f"开始AI+Oracle抓包任务: {task_id}, 自然语言查询: {natural_query}")

        # 1. AI解析自然语言为SQL（5-25%）
        await progress.update_progress(5, "正在使用AI解析自然语言查询...")

        from services.oracle_ai_service import oracle_ai_service
        sql_query = await oracle_ai_service.parse_natural_language_to_sql(natural_query)

        await progress.update_progress(25, f"AI解析完成，生成SQL: {sql_query[:100]}...")

        # 更新任务信息，添加生成的SQL
        try:
            from services.arq_task_management_service import arq_task_management_service
            await arq_task_management_service.update_task_sql(task_id, sql_query)
            logger.info(f"Updated task {task_id} with generated SQL")
        except Exception as e:
            logger.warning(f"Failed to update task SQL: {e}")

        # 2. 获取数据库配置（25-30%）
        await progress.update_progress(25, "获取数据库配置")
        from services.database_config_service import database_config_service
        config = await database_config_service.get_config(config_id)
        if not config:
            raise Exception(f"数据库配置不存在: {config_id}")

        # 3. 初始化Oracle AI服务（30-35%）
        await progress.update_progress(30, "初始化Oracle AI服务")

        # 准备Oracle配置
        oracle_config = {
            'host': config.host,
            'port': config.port,
            'user': config.user,
            'password': config.password,
            'database_name': config.database_name
        }

        # 获取Oracle服务实例（支持连接池复用）
        oracle_service = oracle_ai_service._get_oracle_service(oracle_config)

        # 4. 获取默认服务器配置并初始化抓包服务（35-40%）
        await progress.update_progress(35, "初始化抓包服务")
        from services.oracle_packet_capture_service import OraclePacketCaptureService
        from services.server_config_service import server_config_service

        # 获取默认服务器配置用于抓包
        default_server_config = await server_config_service.get_default_config()
        server_config_id = default_server_config.id if default_server_config else None

        capture_service = OraclePacketCaptureService()
        # 绑定task_id用于取消时终止tcpdump
        try:
            capture_service.local_tcpdump_service.set_task_id(task_id)
        except Exception as e:
            logger.warning(f"Failed to set task_id for AI Oracle capture: {e}")

        # 5. 开始智能抓包（40-50%）
        await progress.update_progress(40, "开始智能数据包捕获")
        try:
            # 使用智能抓包功能，传入Oracle目标主机和端口，优先使用数据库配置关联的服务器
            capture_file = await capture_service.start_smart_capture(
                target_host=config.host,
                target_port=config.port,
                server_config_id=server_config_id
            )
            logger.info(f"Started Oracle smart packet capture: {capture_file}")
        except Exception as e:
            logger.error(f"Smart capture failed, falling back to regular capture: {str(e)}")
            # 回退到常规抓包
            capture_file = await capture_service.start_capture(
                target_host=config.host,
                target_port=config.port
            )
            logger.info(f"Started Oracle regular packet capture: {capture_file}")

        # 6. 等待抓包启动（50-55%）
        await progress.update_progress(50, "等待抓包进程完全启动...")
        await _sleep_cancellable(5, task_id)  # 给Oracle抓包进程5秒时间完全启动（比其他数据库更长）
        if await _is_cancelled(task_id):
            try:
                await capture_service.stop_capture()
            except Exception:
                pass
            await progress.set_failed("任务被取消")
            return {"cancelled": True, "task_id": task_id}

        # 7. 执行SQL查询（使用连接池）（55-65%）
        await progress.update_progress(55, "执行Oracle SQL查询")

        try:
            logger.info(f"Executing Oracle SQL with connection pool: {sql_query}")

            # 执行SQL查询（使用连接池）- 这将产生所有必要的网络流量
            await progress.update_progress(60, f"执行SQL查询（连接池）: {sql_query[:50]}...")
            result = await oracle_ai_service.generate_oracle_query(natural_query, oracle_config)
            logger.info(f"Oracle SQL executed successfully with connection pool, returned {result.get('row_count', 0)} rows")

        except Exception as e:
            logger.warning(f"Oracle查询失败，但继续抓包: {e}")
            result = {"error": str(e)}

        # 8. 关闭连接池（65%）
        await progress.update_progress(65, "关闭Oracle连接池")
        try:
            # 关闭连接池以释放资源
            oracle_ai_service.close_all_pools()
            logger.info("Oracle connection pool closed successfully")
        except Exception as e:
            logger.warning(f"Failed to close Oracle connection pool: {e}")
            # 确保连接被关闭
            try:
                await oracle_service.disconnect()
            except Exception as e:
                logger.error(f"操作失败: {type(e).__name__}: {str(e)}")
                logger.error(f"异常堆栈跟踪: {traceback.format_exc()}")

        # 8. 等待抓包完成（65-90%）
        await progress.update_progress(65, f"等待抓包完成 ({capture_duration}秒)")
        await _sleep_cancellable(capture_duration, task_id)
        if await _is_cancelled(task_id):
            try:
                await capture_service.stop_capture()
            except Exception:
                pass
            await progress.set_failed("任务被取消")
            return {"cancelled": True, "task_id": task_id}

        # 9. 停止抓包（90-95%）
        await progress.update_progress(90, "停止抓包并下载文件")
        final_file = None
        try:
            final_file = await capture_service.stop_capture()
            logger.info(f"Oracle packet capture completed: {final_file}")
        except Exception as e:
            logger.warning(f"Failed to stop capture cleanly: {str(e)}")
            final_file = capture_file

        # 10. 检查抓包文件大小并重试（95%）
        import os
        if final_file and os.path.exists(final_file):
            file_size = os.path.getsize(final_file)
            logger.info(f"Capture file size: {file_size} bytes")

            if file_size <= 24:  # 检测到空包，尝试智能重试
                await progress.update_progress(95, "检测到空包，尝试智能重试")
                logger.warning(f"Detected empty capture file ({file_size} bytes), attempting smart retry")

                try:
                    # 使用智能抓包重试，传入正确的目标主机和端口
                    retry_capture_file = await capture_service.start_smart_capture(
                        target_host=config.host,
                        target_port=config.port,
                        server_config_id=server_config_id
                    )
                    logger.info(f"Smart retry capture started: {retry_capture_file}")

                    # 重新执行查询以生成流量
                    await progress.update_progress(96, "重新执行查询以生成流量")
                    try:
                        retry_result = await execute_oracle_sql_smart(oracle_service, sql_query)
                        if retry_result and not retry_result.get("error"):
                            result = retry_result  # 使用重试的结果
                    except Exception as e:
                        logger.warning(f"Retry query failed: {e}")

                    # 等待一段时间让抓包捕获流量（可取消）
                    await _sleep_cancellable(5, task_id)
                    if await _is_cancelled(task_id):
                        try:
                            await capture_service.stop_capture()
                        except Exception:
                            pass
                        await progress.set_failed("任务被取消")
                        return {"cancelled": True, "task_id": task_id}

                    # 停止重试抓包
                    retry_final_file = await capture_service.stop_capture()

                    # 检查重试文件大小
                    if retry_final_file and os.path.exists(retry_final_file):
                        retry_file_size = os.path.getsize(retry_final_file)
                        logger.info(f"Retry capture file size: {retry_file_size} bytes")

                        if retry_file_size > 24:
                            final_file = retry_final_file  # 使用重试成功的文件
                            logger.info(f"Smart retry successful: {final_file}")
                        else:
                            logger.warning(f"Smart retry also resulted in empty file: {retry_file_size} bytes")

                except Exception as retry_e:
                    logger.error(f"Smart retry failed: {retry_e}")
                    # 继续使用原始文件

        # 11. 分析抓包结果（98-100%）
        await progress.update_progress(98, "分析抓包结果")

        task_result = {
            "success": True,
            "natural_query": natural_query,
            "sql_query": sql_query,
            "query_result": result,
            "capture_file": final_file,
            "config_id": config_id,
            "capture_duration": capture_duration,
            "task_type": "ai_oracle_capture"
        }

        await progress.set_completed(task_result)
        logger.info(f"AI+Oracle抓包任务完成: {task_id}")

        return task_result

    except Exception as e:
        logger.error(f"AI+Oracle抓包任务失败: {e}")
        await progress.set_failed(str(e))
        raise

async def gaussdb_capture_task(ctx: Dict[str, Any], config_id: int, sql_query: str, capture_duration: int = 30) -> Dict[str, Any]:
    """GaussDB异步抓包任务"""
    task_id = ctx.get('job_id', 'unknown')
    redis = redis_manager.redis
    progress = TaskProgress(task_id, redis)

    try:
        await progress.update_progress(0, "开始GaussDB抓包任务")

        # 获取数据库配置
        await progress.update_progress(10, "获取数据库配置")
        from services.database_config_service import DatabaseConfigService
        db_config_service = DatabaseConfigService()
        await db_config_service.initialize()

        config = await db_config_service.get_config(config_id)
        if not config:
            raise Exception(f"数据库配置不存在: {config_id}")

        # 初始化GaussDB AI服务
        from services.gaussdb_ai_service import gaussdb_ai_service

        gaussdb_config = {
            'host': config.host,
            'port': config.port,
            'user': config.user,
            'password': config.password,
            'database_name': config.database_name
        }

        # 开始抓包
        await progress.update_progress(30, "开始数据包捕获")
        capture_file = await gaussdb_packet_service.start_capture(
            config.host,
            config.port,
            server_config_id=1  # 使用默认服务器配置
        )

        # 等待抓包服务启动
        await progress.update_progress(40, "等待抓包服务启动")
        await asyncio.sleep(3)

        # 执行SQL查询
        await progress.update_progress(50, f"执行SQL查询: {sql_query[:50]}...")

        try:
            # 使用GaussDB AI服务执行查询（支持连接池）
            result = await gaussdb_ai_service.execute_gaussdb_query(sql_query, gaussdb_config)
            logger.info("GaussDB查询执行成功")
        except Exception as e:
            logger.warning(f"GaussDB查询失败，但继续抓包: {e}")
            result = {"error": str(e)}

        # 关闭GaussDB连接池
        await progress.update_progress(67, "关闭GaussDB连接池")
        try:
            # 关闭连接池以释放资源
            gaussdb_ai_service.close_all_pools()
            logger.info("GaussDB connection pool closed successfully")
        except Exception as e:
            logger.warning(f"Failed to close GaussDB connection pool: {e}")

        # 等待抓包完成
        await progress.update_progress(70, f"等待抓包完成 ({capture_duration}秒)")
        await asyncio.sleep(capture_duration)

        # 停止抓包
        await progress.update_progress(90, "停止抓包并分析")
        capture_result = await gaussdb_packet_service.stop_capture()
        final_file = capture_result.get('capture_file') if isinstance(capture_result, dict) else capture_result
        local_file = capture_result.get('local_file') if isinstance(capture_result, dict) else None

        # 检查抓包质量
        if final_file and os.path.exists(final_file):
            file_size = os.path.getsize(final_file)
            logger.info(f"GaussDB capture file size: {file_size} bytes")

            if file_size <= 24:
                logger.warning("检测到空包，尝试智能重试")
                await progress.update_progress(92, "检测到空包，正在智能重试")

                try:
                    # 使用智能抓包重试
                    retry_file = await gaussdb_packet_service.start_smart_capture(
                        target_host=config.host,
                        target_port=config.port,
                        server_config_id=1  # 使用默认服务器配置
                    )

                    # 再次执行查询（使用AI服务执行查询）
                    await gaussdb_ai_service.execute_gaussdb_query(sql_query, gaussdb_config)
                    await asyncio.sleep(5)  # 短时间抓包

                    retry_result = await gaussdb_packet_service.stop_capture()
                    retry_final_file = retry_result.get('capture_file') if isinstance(retry_result, dict) else retry_result

                    if retry_final_file and os.path.exists(retry_final_file):
                        retry_size = os.path.getsize(retry_final_file)
                        if retry_size > 24:
                            final_file = retry_final_file
                            logger.info(f"智能重试成功，新文件大小: {retry_size} bytes")
                        else:
                            logger.warning("智能重试仍然产生空包")

                except Exception as retry_error:
                    logger.error(f"智能重试失败: {retry_error}")

        # 准备结果
        task_result = {
            "type": "gaussdb_capture",
            "config_id": config_id,
            "sql_query": sql_query,
            "capture_file": final_file,
            "local_file": local_file,
            "sql_result": result,
            "capture_duration": capture_duration,
            "database_info": {
                "host": config.host,
                "port": config.port,
                "database": config.database_name
            }
        }

        await progress.set_completed(task_result)
        return task_result

    except Exception as e:
        logger.error(f"GaussDB抓包任务失败: {e}")
        await progress.set_failed(str(e))
        raise

async def ai_gaussdb_capture_task(ctx: Dict[str, Any], config_id: int, natural_query: str, capture_duration: int = 30) -> Dict[str, Any]:
    """AI+GaussDB异步抓包任务（包含大模型请求）"""
    task_id = ctx.get('job_id', 'unknown')
    redis = redis_manager.redis
    progress = TaskProgress(task_id, redis)

    try:
        await progress.update_progress(0, "开始AI+GaussDB抓包任务")

        # 1. 获取数据库配置（0-10%）
        await progress.update_progress(5, "获取数据库配置")
        from services.database_config_service import DatabaseConfigService
        db_config_service = DatabaseConfigService()
        await db_config_service.initialize()

        config = await db_config_service.get_config(config_id)
        if not config:
            raise Exception(f"数据库配置不存在: {config_id}")

        # 2. 初始化AI服务（10-20%）
        await progress.update_progress(10, "初始化AI服务")
        from services.ai_service import AIService
        ai_service = AIService()

        # 3. 使用AI将自然语言转换为SQL（20-40%）
        await progress.update_progress(20, f"AI转换自然语言查询: {natural_query[:30]}...")

        system_prompt = "你是一个专业的GaussDB数据库专家，擅长将自然语言转换为SQL语句。GaussDB是基于PostgreSQL的企业级数据库，支持标准SQL语法。"
        user_prompt = f"""
        将以下自然语言查询转换为GaussDB SQL语句：

        自然语言查询: {natural_query}

        要求:
        1. 生成标准的SQL语句
        2. 考虑GaussDB的语法特性
        3. 只返回SQL语句，不要其他解释
        4. 支持多语句查询，用分号分隔
        5. **重要：确保语句语法正确，特别是括号匹配**
        6. **重要：如果需要创建表，必须先生成DROP TABLE IF EXISTS table_name;语句，然后再CREATE TABLE**
        7. CREATE TABLE语句必须以右括号和分号结尾: );

        **创建表的标准格式：**
        DROP TABLE IF EXISTS table_name;
        CREATE TABLE table_name (
            field1 datatype,
            field2 datatype,
            ...
        );

        SQL语句:
        """

        sql_query = await ai_service.generate_response(system_prompt, user_prompt)
        sql_query = sql_query.strip()

        # 清理AI响应
        if sql_query.startswith('```sql'):
            sql_query = sql_query[6:]
        if sql_query.endswith('```'):
            sql_query = sql_query[:-3]
        sql_query = sql_query.strip()

        # 后处理：确保CREATE TABLE语句前包含DROP TABLE语句
        import re
        if re.search(r'\bCREATE\s+TABLE\s+(\w+)', sql_query, re.IGNORECASE):
            # 提取表名
            match = re.search(r'\bCREATE\s+TABLE\s+(\w+)', sql_query, re.IGNORECASE)
            if match:
                table_name = match.group(1)
                drop_statement = f"DROP TABLE IF EXISTS {table_name};"

                # 检查是否已经包含DROP TABLE语句
                if not re.search(rf'\bDROP\s+TABLE\s+.*{table_name}', sql_query, re.IGNORECASE):
                    # 在CREATE TABLE前添加DROP TABLE语句
                    sql_query = drop_statement + "\n" + sql_query
                    logger.info(f"Added DROP TABLE statement for table: {table_name}")

        await progress.update_progress(40, f"AI生成SQL: {sql_query[:50]}...")

        # 4. 初始化GaussDB AI服务（40-50%）
        await progress.update_progress(45, "初始化GaussDB AI服务")
        from services.gaussdb_ai_service import gaussdb_ai_service

        gaussdb_config = {
            'host': config.host,
            'port': config.port,
            'user': config.user,
            'password': config.password,
            'database_name': config.database_name
        }

        # 5. 开始抓包（50-55%）
        await progress.update_progress(50, "开始数据包捕获")
        capture_file = await gaussdb_packet_service.start_capture(
            target_host=config.host,
            target_port=config.port,
            server_config_id=1  # 使用默认服务器配置
        )

        # 6. 等待抓包服务启动（55-60%）
        await progress.update_progress(55, "等待抓包服务启动")
        await asyncio.sleep(3)

        # 7. 执行SQL查询（60-70%）
        await progress.update_progress(60, f"执行AI生成的SQL: {sql_query[:50]}...")

        try:
            # 支持多语句执行
            statements = [stmt.strip() for stmt in sql_query.split(';') if stmt.strip()]
            results = []

            for i, statement in enumerate(statements):
                await progress.update_progress(60 + (i * 5), f"执行语句 {i+1}/{len(statements)}")
                # 使用GaussDB AI服务执行查询（支持连接池）
                result = await gaussdb_ai_service.execute_gaussdb_query(statement, gaussdb_config)
                results.append(result)

                # 在语句之间添加短暂延迟
                if i < len(statements) - 1:
                    await asyncio.sleep(0.5)

            if len(results) == 1:
                final_result = results[0]
            else:
                final_result = {
                    'statements_count': len(statements),
                    'results': results,
                    'total_rows': sum(r.get('rows_affected', 0) for r in results),
                    'all_successful': all(r.get('success', False) for r in results)
                }

            logger.info("AI+GaussDB查询执行成功")
        except Exception as e:
            logger.warning(f"AI+GaussDB查询失败，但继续抓包: {e}")
            final_result = {"error": str(e)}

        # 7.5. 关闭GaussDB连接池（67%）
        await progress.update_progress(67, "关闭GaussDB连接池")
        try:
            # 关闭连接池以释放资源
            gaussdb_ai_service.close_all_pools()
            logger.info("GaussDB connection pool closed successfully")
        except Exception as e:
            logger.warning(f"Failed to close GaussDB connection pool: {e}")

        # 8. 等待抓包完成（70-90%）
        await progress.update_progress(70, f"等待抓包完成 ({capture_duration}秒)")
        await asyncio.sleep(capture_duration)

        # 9. 停止抓包并分析（90-100%）
        await progress.update_progress(90, "停止抓包并分析结果")
        capture_result = await gaussdb_packet_service.stop_capture()
        final_file = capture_result.get('capture_file') if isinstance(capture_result, dict) else capture_result
        local_file = capture_result.get('local_file') if isinstance(capture_result, dict) else None

        # 检查抓包质量
        if final_file and os.path.exists(final_file):
            file_size = os.path.getsize(final_file)
            logger.info(f"AI+GaussDB capture file size: {file_size} bytes")

            if file_size <= 24:
                logger.warning("检测到空包，尝试智能重试")
                await progress.update_progress(95, "检测到空包，正在智能重试")

                try:
                    # 使用智能抓包重试
                    retry_file = await gaussdb_packet_service.start_smart_capture(
                        target_host=config.host,
                        target_port=config.port
                    )

                    # 再次执行查询（使用AI服务执行查询）
                    await gaussdb_ai_service.execute_gaussdb_query(sql_query, gaussdb_config)
                    await asyncio.sleep(5)  # 短时间抓包

                    retry_result = await gaussdb_packet_service.stop_capture()
                    retry_final_file = retry_result.get('capture_file') if isinstance(retry_result, dict) else retry_result

                    if retry_final_file and os.path.exists(retry_final_file):
                        retry_size = os.path.getsize(retry_final_file)
                        if retry_size > 24:
                            final_file = retry_final_file
                            logger.info(f"智能重试成功，新文件大小: {retry_size} bytes")
                        else:
                            logger.warning("智能重试仍然产生空包")

                except Exception as retry_error:
                    logger.error(f"智能重试失败: {retry_error}")

        # 准备结果
        task_result = {
            "type": "ai_gaussdb_capture",
            "config_id": config_id,
            "natural_query": natural_query,
            "generated_sql": sql_query,
            "capture_file": final_file,
            "local_file": local_file,
            "sql_result": final_result,
            "capture_duration": capture_duration,
            "database_info": {
                "host": config.host,
                "port": config.port,
                "database": config.database_name
            },
            "task_type": "ai_gaussdb_capture"
        }

        await progress.set_completed(task_result)
        logger.info(f"AI+GaussDB抓包任务完成: {task_id}")

        return task_result

    except Exception as e:
        logger.error(f"AI+GaussDB抓包任务失败: {e}")
        await progress.set_failed(str(e))
        raise

async def ai_test_case_generation_task(ctx: Dict[str, Any], requirement: str, database_type: str = "mysql", database_version: str = "", operation_type: str = "查询", batch_size: int = 1) -> Dict[str, Any]:
    """AI测试用例生成异步任务"""
    task_id = ctx.get('job_id', 'unknown')
    redis = redis_manager.redis
    progress = TaskProgress(task_id, redis)

    try:
        logger.info(f"开始AI测试用例生成任务: {task_id}, 需求: {requirement}, 批量大小: {batch_size}")

        # 1. 初始化AI服务（5-10%）
        await progress.update_progress(5, "初始化AI测试用例生成服务...")

        from services.test_case_ai_service import test_case_ai_service

        # 2. 生成测试用例（10-70%）
        await progress.update_progress(10, "正在生成测试用例...")

        generated_test_cases = []

        # 使用智能批量生成，让AI根据需求自动决定生成数量
        await progress.update_progress(15, "正在分析需求并生成测试用例...")

        try:
            test_cases = await test_case_ai_service.batch_generation_agent.generate_batch_test_cases(
                requirement=requirement,
                database_type=database_type,
                database_version=database_version,
                operation_type=operation_type
            )

            if test_cases and len(test_cases) > 0:
                # 根据batch_size参数决定是否限制数量
                if batch_size > 0 and len(test_cases) > batch_size:
                    # 有最大数量限制，截取前batch_size个
                    generated_test_cases = test_cases[:batch_size]
                    await progress.update_progress(50, f"AI生成 {len(test_cases)} 个用例，按限制取前 {len(generated_test_cases)} 个")
                    logger.info(f"AI生成了 {len(test_cases)} 个测试用例，按最大限制 {batch_size} 取前 {len(generated_test_cases)} 个")
                else:
                    # 无限制或生成数量未超过限制，使用AI生成的所有用例
                    generated_test_cases = test_cases
                    await progress.update_progress(50, f"AI智能生成 {len(generated_test_cases)} 个测试用例")
                    logger.info(f"AI根据需求'{requirement}'智能生成了 {len(generated_test_cases)} 个测试用例")
            else:
                # 批量生成返回空结果
                error_msg = "批量生成测试用例失败：AI未返回有效的测试用例，请检查需求描述或稍后重试"
                await progress.set_failed(error_msg)
                raise Exception(error_msg)

        except Exception as e:
            # 批量生成过程中出现异常
            error_msg = f"批量生成测试用例失败：{str(e)}"
            logger.error(error_msg)
            await progress.set_failed(error_msg)
            raise Exception(error_msg)

        if not generated_test_cases:
            raise Exception("测试用例生成失败")

        await progress.update_progress(70, f"测试用例生成完成，共 {len(generated_test_cases)} 个")

        # 3. 自动入库（70-90%）
        await progress.update_progress(70, "开始自动保存测试用例到数据库...")

        from services.test_case_management_service import test_case_management_service
        saved_test_cases = []
        failed_count = 0

        for i, test_case in enumerate(generated_test_cases):
            try:
                progress_percent = 70 + int((i + 1) / len(generated_test_cases) * 20)
                await progress.update_progress(progress_percent, f"保存第 {i+1}/{len(generated_test_cases)} 个测试用例...")

                # 检查测试用例是否为空
                if not test_case or not isinstance(test_case, dict):
                    logger.warning(f"跳过无效的测试用例: {test_case}")
                    failed_count += 1
                    continue

                # 转换为数据库模型格式
                from models.test_case_management import TestCaseManagementCreate, TestStep, PriorityEnum, StatusEnum, ReviewStatusEnum, AutomationLevelEnum, DatabaseTypeEnum

                # 转换测试步骤
                test_steps = []
                for step_data in test_case.get("test_steps", []):
                    if isinstance(step_data, dict):
                        # 处理test_data字段的类型转换
                        test_data_raw = step_data.get("test_data", "")
                        if isinstance(test_data_raw, dict):
                            # 如果是字典，转换为JSON字符串
                            import json
                            test_data_str = json.dumps(test_data_raw, ensure_ascii=False, indent=2)
                        elif isinstance(test_data_raw, (list, tuple)):
                            # 如果是列表或元组，转换为JSON字符串
                            import json
                            test_data_str = json.dumps(test_data_raw, ensure_ascii=False, indent=2)
                        else:
                            # 如果是其他类型，转换为字符串
                            test_data_str = str(test_data_raw) if test_data_raw is not None else ""

                        test_step = TestStep(
                            step_number=step_data.get("step_number", len(test_steps) + 1),
                            action=step_data.get("action", "执行操作"),
                            expected_result=step_data.get("expected_result", "操作成功"),
                            test_data=test_data_str
                        )
                        test_steps.append(test_step)

                # 如果没有测试步骤，创建默认步骤
                if not test_steps:
                    test_steps = [
                        TestStep(
                            step_number=1,
                            action="执行测试操作",
                            expected_result="操作成功完成",
                            test_data=""
                        )
                    ]

                # 转换枚举值
                priority_map = {"low": PriorityEnum.LOW, "medium": PriorityEnum.MEDIUM, "high": PriorityEnum.HIGH, "critical": PriorityEnum.CRITICAL}
                automation_map = {"manual": AutomationLevelEnum.MANUAL, "auto": AutomationLevelEnum.AUTO, "semi": AutomationLevelEnum.SEMI_AUTO, "semi_auto": AutomationLevelEnum.SEMI_AUTO}
                database_map = {
                    "mysql": DatabaseTypeEnum.MYSQL,
                    "postgresql": DatabaseTypeEnum.POSTGRESQL,
                    "mongodb": DatabaseTypeEnum.MONGODB,
                    "oracle": DatabaseTypeEnum.ORACLE,
                    "gaussdb": DatabaseTypeEnum.GAUSSDB
                }

                priority = priority_map.get(test_case.get("priority", "medium"), PriorityEnum.MEDIUM)
                automation_level = automation_map.get(test_case.get("automation_level", "manual"), AutomationLevelEnum.MANUAL)
                db_type = database_map.get(database_type.lower(), DatabaseTypeEnum.MYSQL)

                # 创建测试用例模型
                test_case_create = TestCaseManagementCreate(
                    title=test_case.get("title", "AI生成的测试用例"),
                    module=test_case.get("module", "AI生成模块"),
                    priority=priority,
                    status=StatusEnum.DRAFT,
                    preconditions=test_case.get("preconditions", ""),
                    test_steps=test_steps,
                    expected_result=test_case.get("expected_result", ""),
                    test_data=test_case.get("test_data", {}),
                    tags=test_case.get("tags", []),
                    author="AI生成",
                    review_status=ReviewStatusEnum.PENDING,
                    estimated_time=test_case.get("estimated_time", 30),
                    automation_level=automation_level,
                    test_environment=test_case.get("test_environment", "测试环境"),
                    database_type=db_type,
                    database_version=database_version,
                    operation_type=operation_type,
                    notes=f"由AI自动生成 - 需求: {requirement[:100]}..."
                )

                # 保存到数据库
                saved_test_case = await test_case_management_service.create_test_case(test_case_create)
                saved_test_cases.append({
                    "id": saved_test_case.id,
                    "title": saved_test_case.title,
                    "original_data": test_case
                })

                logger.info(f"测试用例已保存: ID={saved_test_case.id}, 标题={saved_test_case.title}")

            except Exception as e:
                logger.error(f"保存测试用例失败: {e}")
                failed_count += 1

        await progress.update_progress(90, f"测试用例保存完成，成功 {len(saved_test_cases)} 个，失败 {failed_count} 个")

        # 4. 完成任务（90-100%）
        await progress.update_progress(95, "任务即将完成...")

        task_result = {
            "task_id": task_id,
            "requirement": requirement,
            "database_type": database_type,
            "operation_type": operation_type,
            "batch_size": batch_size,
            "generated_count": len(generated_test_cases),
            "saved_count": len(saved_test_cases),
            "failed_count": failed_count,
            "saved_test_cases": saved_test_cases,
            "generated_test_cases": generated_test_cases,
            "success": len(saved_test_cases) > 0,
            "message": f"成功生成并保存 {len(saved_test_cases)} 个测试用例" + (f"，{failed_count} 个保存失败" if failed_count > 0 else "")
        }

        await progress.update_progress(100, task_result["message"])
        await progress.set_completed(task_result)

        logger.info(f"AI测试用例生成任务完成: {task_id}, 成功保存 {len(saved_test_cases)} 个测试用例")
        return task_result

    except Exception as e:
        logger.error(f"AI测试用例生成任务失败: {e}")
        await progress.set_failed(str(e))
        raise


async def _cleanup_database_connections(database_type: str, execution_service=None):
    """清理数据库连接池"""
    try:
        logger.info(f"开始清理 {database_type} 数据库连接池...")

        # 首先清理执行服务中的连接
        if execution_service:
            try:
                await execution_service.cleanup_database_connections()
                logger.info("执行服务连接池已清理")
            except Exception as e:
                logger.error(f"清理执行服务连接池失败: {e}")

        # 然后清理全局连接池
        if database_type.lower() == "gaussdb":
            # 清理GaussDB连接池
            from services.gaussdb_ai_service import gaussdb_ai_service
            gaussdb_ai_service.close_all_pools()
            logger.info("GaussDB全局连接池已清理")

        elif database_type.lower() == "mysql":
            # 清理MySQL连接池（如果有的话）
            # MySQL服务通常不使用连接池，但为了一致性保留此逻辑
            logger.info("MySQL连接清理完成（无连接池需要清理）")

        elif database_type.lower() == "oracle":
            # 清理Oracle连接池（如果有的话）
            logger.info("Oracle连接清理完成（无连接池需要清理）")

        elif database_type.lower() == "postgresql":
            # 清理PostgreSQL连接池（如果有的话）
            logger.info("PostgreSQL连接清理完成（无连接池需要清理）")

        elif database_type.lower() == "mongodb":
            # 清理MongoDB连接池（如果有的话）
            logger.info("MongoDB连接清理完成（无连接池需要清理）")

        else:
            logger.warning(f"未知数据库类型 {database_type}，跳过连接池清理")

        logger.info(f"{database_type} 数据库连接池清理完成")

    except Exception as e:
        logger.error(f"清理 {database_type} 数据库连接池失败: {str(e)}")
        # 不抛出异常，避免影响主任务流程


async def batch_test_case_execution_task(
    ctx: Dict[str, Any],
    batch_name: str,
    database_type: str,
    database_version: str,
    config_id: int,
    test_case_items: list,
    capture_enabled: bool = True,
    timeout_per_case: int = 600,
    stop_on_failure: bool = False,
    use_c_executor: bool = False
) -> Dict[str, Any]:
    """批量测试用例执行异步任务 - 使用新的日志系统"""
    task_id = ctx.get('job_id', 'unknown')
    redis = redis_manager.redis
    progress = TaskProgress(task_id, redis)

    # 详细记录任务开始信息
    logger.info(f"=== 批量测试用例执行任务开始 ===")
    logger.info(f"任务ID: {task_id}")
    logger.info(f"批量名称: {batch_name}")
    logger.info(f"数据库类型: {database_type}")
    logger.info(f"数据库版本: {database_version}")
    logger.info(f"配置ID: {config_id}")
    logger.info(f"测试用例数量: {len(test_case_items)}")
    logger.info(f"抓包启用: {capture_enabled}")
    logger.info(f"单用例超时: {timeout_per_case}秒")
    logger.info(f"失败时停止: {stop_on_failure}")
    logger.info(f"使用C执行器: {use_c_executor}")

    # 调试：检查worker进程中的路径
    import os
    from utils.path_manager import path_manager
    logger.info(f"🔍 Worker进程路径调试:")
    logger.info(f"🔍 当前工作目录: {os.getcwd()}")
    logger.info(f"🔍 path_manager.get_captures_dir(): {path_manager.get_captures_dir()}")
    logger.info(f"🔍 path_manager.backend_root: {path_manager.backend_root}")
    logger.info(f"🔍 __file__: {__file__}")
    logger.info(f"🔍 os.path.abspath(__file__): {os.path.abspath(__file__)}")

    # 记录测试用例详情
    for i, item in enumerate(test_case_items):
        logger.info(f"测试用例 {i+1}: ID={item.get('test_case_id', 'unknown')}, 标题={item.get('test_case_title', 'unknown')}")
        # 记录测试用例JSON的前100个字符，避免日志过长
        test_case_json = item.get('test_case_json', '')
        if test_case_json:
            logger.debug(f"测试用例 {i+1} JSON内容预览: {test_case_json[:100]}...")
        else:
            logger.warning(f"测试用例 {i+1} 缺少test_case_json字段")

    # 创建主任务执行日志记录器
    from utils.task_execution_logger import create_task_logger, ExecutorSelector, ExecutorType
    main_task_logger = create_task_logger(
        task_id=task_id,
        task_type="batch_test_case_execution",
        task_name=f"批量测试用例执行-{batch_name}"
    )

    try:
        # 初始化主任务记录
        await main_task_logger.initialize(
            task_description=f"批量执行{database_type}测试用例，共{len(test_case_items)}个",
            database_config_id=config_id,
            capture_enabled=capture_enabled,
            batch_name=batch_name,
            batch_total_count=len(test_case_items)
        )
        
        # 选择执行器并开始执行
        executor_type, selection_reason = ExecutorSelector.select_executor(database_type.lower())
        await main_task_logger.start_execution(
            executor_type=executor_type,
            executor_selection_reason=selection_reason
        )
        
        logger.info(f"开始批量测试用例执行任务: {task_id}, 批量名称: {batch_name}, 用例数量: {len(test_case_items)}")

        # 1. 初始化执行服务（5%）
        await progress.update_progress(5, "初始化测试用例执行服务...")
        await main_task_logger.update_progress(5, "初始化测试用例执行服务")

        from services.test_case_execution_agent_service import test_case_execution_agent_service

        # 2. 开始执行测试用例（5-90%）
        await progress.update_progress(10, "开始执行测试用例...")
        await main_task_logger.update_progress(10, "开始执行测试用例")

        total_cases = len(test_case_items)
        completed_cases = 0
        success_cases = 0
        failed_cases = 0
        execution_results = []

        for i, test_case_item in enumerate(test_case_items):
            test_case_id = test_case_item.get('test_case_id', 'Unknown')
            test_case_title = test_case_item.get('test_case_title', 'Unknown')

            try:
                # 计算当前进度（10-90%）
                current_progress = 10 + int((i / total_cases) * 80)
                await progress.update_progress(
                    current_progress,
                    f"正在执行测试用例 {i+1}/{total_cases}: {test_case_title}"
                )
                await main_task_logger.update_progress(
                    current_progress,
                    f"正在执行测试用例 {i+1}/{total_cases}: {test_case_title}"
                )

                logger.info(f"开始执行测试用例 {i+1}/{total_cases}: {test_case_id}")
                logger.info(f"测试用例标题: {test_case_title}")
                logger.info(f"数据库配置ID: {config_id}")
                logger.info(f"抓包启用: {capture_enabled}")
                logger.info(f"使用C执行器: {use_c_executor}")

                # 记录测试用例JSON内容的关键信息
                test_case_json = test_case_item.get('test_case_json', '')

                # 检查test_case_json是否为空或只包含空对象
                if not test_case_json or test_case_json.strip() in ['{}', '']:
                    logger.warning(f"测试用例JSON为空，尝试从数据库获取完整内容: {test_case_id}")
                    try:
                        # 从数据库获取完整的测试用例内容
                        from services.test_case_management_service import TestCaseManagementService
                        test_case_service = TestCaseManagementService()
                        await test_case_service.init_pool()
                        full_test_case = await test_case_service.get_test_case(test_case_id)
                        if full_test_case:
                            # 构建完整的测试用例JSON
                            import json
                            test_case_json = json.dumps({
                                "id": full_test_case.id,
                                "title": full_test_case.title,
                                "test_steps": full_test_case.test_steps,
                                "database_type": full_test_case.database_type,
                                "expected_result": full_test_case.expected_result
                            }, ensure_ascii=False)
                            # 更新test_case_item中的test_case_json
                            test_case_item['test_case_json'] = test_case_json
                            logger.info(f"成功从数据库获取测试用例内容，包含 {len(full_test_case.test_steps)} 个测试步骤")
                        else:
                            logger.error(f"无法从数据库获取测试用例: {test_case_id}")
                    except Exception as e:
                        logger.error(f"从数据库获取测试用例失败: {e}")

                try:
                    import json
                    test_case_data = json.loads(test_case_json)
                    logger.info(f"测试用例包含 {len(test_case_data.get('test_steps', []))} 个测试步骤")
                    for step_idx, step in enumerate(test_case_data.get('test_steps', [])):
                        logger.debug(f"步骤 {step_idx + 1}: {step.get('action', 'unknown')}")
                except Exception as e:
                    logger.warning(f"解析测试用例JSON失败: {e}")
                    logger.debug(f"原始JSON内容: {test_case_json[:200]}...")

                # 记录开始时间
                start_time = get_current_time()
                logger.info(f"测试用例执行开始时间: {format_time(start_time)}")

                # 执行测试用例
                # 注意：test_case_json 已经是JSON字符串，不需要再次编码
                logger.info("调用测试用例执行服务...")
                result = await test_case_execution_agent_service.execute_test_case(
                    test_case_json=test_case_json,
                    config_id=config_id,
                    capture_enabled=capture_enabled,
                    test_case_id=test_case_id,
                    use_c_executor=use_c_executor
                )
                logger.info(f"测试用例执行服务返回结果: success={result.get('success')}, error={result.get('error')}")

                # 记录结束时间
                end_time = get_current_time()
                duration = int((end_time - start_time).total_seconds())

                # 记录执行结果
                if result.get("success"):
                    success_cases += 1
                else:
                    failed_cases += 1

                # 收集执行结果
                execution_result = {
                    "test_case_id": test_case_id,
                    "test_case_title": test_case_title,
                    "success": result.get("success", False),
                    "error": result.get("error"),
                    "duration": duration,
                    "start_time": format_time(start_time),
                    "end_time": format_time(end_time),
                    "capture_files": []
                }

                # 收集抓包文件
                # 支持 capture_files (复数)
                if result.get("capture_files"):
                    for capture_file in result["capture_files"]:
                        if capture_file and os.path.sep in capture_file:
                            capture_filename = os.path.basename(capture_file)
                        else:
                            capture_filename = capture_file
                        execution_result["capture_files"].append(capture_filename)

                # 支持 capture_file (单数) - 用于Oracle等单个文件返回
                if result.get("capture_file"):
                    capture_file = result["capture_file"]
                    if capture_file and os.path.sep in capture_file:
                        capture_filename = os.path.basename(capture_file)
                    else:
                        capture_filename = capture_file
                    if capture_filename not in execution_result["capture_files"]:
                        execution_result["capture_files"].append(capture_filename)

                # 支持 summary 中的 capture_files
                if result.get("summary", {}).get("capture_files"):
                    for capture_file in result["summary"]["capture_files"]:
                        if capture_file and os.path.sep in capture_file:
                            capture_filename = os.path.basename(capture_file)
                        else:
                            capture_filename = capture_file
                        if capture_filename not in execution_result["capture_files"]:
                            execution_result["capture_files"].append(capture_filename)

                execution_results.append(execution_result)

                # 更新统计
                completed_cases += 1
                logger.info(f"测试用例执行完成: {test_case_item.get('test_case_id')}, 成功: {result.get('success')}")

                # 如果设置了遇到失败停止，则停止执行
                if not result.get("success") and stop_on_failure:
                    logger.info(f"遇到失败停止执行，已完成 {completed_cases}/{total_cases} 个用例")
                    break

            except Exception as e:
                logger.error(f"执行测试用例失败: {test_case_item.get('test_case_id', 'Unknown')}, 错误: {e}")

                # 记录失败结果
                current_time = get_current_time()
                execution_result = {
                    "test_case_id": test_case_id,
                    "test_case_title": test_case_title,
                    "success": False,
                    "error": str(e),
                    "duration": 0,
                    "start_time": format_time(current_time),
                    "end_time": format_time(current_time),
                    "capture_files": []
                }
                execution_results.append(execution_result)

                # 更新统计
                completed_cases += 1
                failed_cases += 1

                # 如果设置了遇到失败停止，则停止执行
                if stop_on_failure:
                    logger.info(f"遇到失败停止执行，已完成 {completed_cases}/{total_cases} 个用例")
                    break

        # 3. 保存抓包文件到数据库（90-92%）
        await progress.update_progress(90, "保存抓包文件到数据库...")
        await main_task_logger.update_progress(90, "保存抓包文件到数据库")

        # 收集所有抓包文件
        all_capture_files = []
        for execution_result in execution_results:
            all_capture_files.extend(execution_result.get("capture_files", []))

        # 保存抓包文件
        if capture_enabled and all_capture_files:
            try:
                from services.capture_file_service import CaptureFileService
                capture_file_service = CaptureFileService()
                await capture_file_service.initialize()

                saved_count = 0
                # 使用正确的抓包文件目录路径
                capture_dir = "captures"

                for capture_filename in all_capture_files:
                    try:
                        # 构建正确的抓包文件路径
                        # 由于worker在backend目录下运行，直接使用相对路径即可
                        capture_file_path = os.path.join(capture_dir, capture_filename)

                        logger.info(f"检查抓包文件路径: {capture_file_path}")

                        if os.path.exists(capture_file_path):
                            with open(capture_file_path, 'rb') as f:
                                file_content = f.read()

                            # 获取数据库配置信息用于构建CaptureFileCreate对象
                            from services.database_config_service import database_config_service
                            await database_config_service.initialize()
                            config = await database_config_service.get_config(config_id)

                            from models.capture_file import CaptureFileCreate
                            capture_data = CaptureFileCreate(
                                filename=capture_filename,
                                file_path=f"captures/{capture_filename}",
                                file_size=len(file_content),
                                database_type=database_type,
                                target_host=config.host if config else "unknown",
                                target_port=config.port if config else 0,
                                description=f"批量执行任务生成的{database_type}抓包文件"
                            )

                            await capture_file_service.save_capture_file(capture_data, task_id=task_id)
                            saved_count += 1
                            logger.info(f"成功保存抓包文件到数据库: {capture_filename}")
                        else:
                            logger.warning(f"抓包文件不存在，跳过保存: {capture_filename} (路径: {capture_file_path})")
                    except Exception as e:
                        logger.error(f"保存抓包文件失败 {capture_filename}: {e}")

                logger.info(f"批量执行任务保存抓包文件完成: {saved_count}/{len(all_capture_files)} 个文件")

            except Exception as e:
                logger.error(f"保存抓包文件到数据库失败: {e}")

        # 4. 批量PCAP验证（92-94%）
        batch_validation_result = None
        if all_capture_files and capture_enabled:
            try:
                await progress.update_progress(92, "正在进行批量PCAP验证...")
                await main_task_logger.update_progress(92, "正在进行批量PCAP验证")

                from services.capture_validation_manager import CaptureValidationManager
                validation_manager = CaptureValidationManager()

                # 创建批量验证任务
                batch_validation_task_id = f"batch_validation_{task_id}_{int(time.time())}"

                # 预期的SQL操作类型
                expected_operations = ["SELECT", "INSERT", "UPDATE", "DELETE"]

                validation_task = validation_manager.start_capture_task(
                    task_id=batch_validation_task_id,
                    database_type=database_type,
                    expected_operations=expected_operations
                )

                # 添加所有抓包文件到验证任务
                for capture_file in all_capture_files:
                    validation_manager.add_pcap_file_to_task(batch_validation_task_id, capture_file)

                # 执行批量验证
                batch_validation_result = validation_manager.complete_capture_task(batch_validation_task_id)

                if batch_validation_result:
                    validation_summary = batch_validation_result.get('summary', {})
                    success_rate = validation_summary.get('success_rate', 0)
                    total_sql_found = validation_summary.get('total_sql_found', 0)
                    files_with_sql = validation_summary.get('files_with_sql', 0)

                    await main_task_logger.update_progress(
                        93,
                        f"批量PCAP验证完成: 成功率 {success_rate:.1%}, 找到SQL语句 {total_sql_found} 条"
                    )

                    logger.info(f"批量PCAP验证结果: 成功率 {success_rate:.1%}, "
                              f"总文件 {len(all_capture_files)}, 包含SQL的文件 {files_with_sql}")

                    # 如果验证成功率较低，记录警告
                    if success_rate < 0.5:
                        logger.warning(f"批量PCAP验证成功率较低: {success_rate:.1%}")
                else:
                    await main_task_logger.update_progress(93, "批量PCAP验证失败")
                    logger.warning("批量PCAP验证失败")

            except Exception as e:
                await main_task_logger.update_progress(93, f"批量PCAP验证异常: {str(e)}")
                logger.warning(f"批量PCAP验证失败: {str(e)}")

        # 5. 清理连接池（94-95%）
        await progress.update_progress(94, "清理数据库连接池...")
        await main_task_logger.update_progress(94, "清理数据库连接池")
        await _cleanup_database_connections(database_type, test_case_execution_agent_service)

        # 6. 完成任务（95-100%）
        await progress.update_progress(95, "批量执行即将完成...")
        await main_task_logger.update_progress(95, "批量执行即将完成")

        # 计算成功率
        success_rate = (success_cases / completed_cases) * 100 if completed_cases > 0 else 0

        # 记录最终结果
        final_message = f"批量执行完成，成功 {success_cases} 个，失败 {failed_cases} 个，成功率 {success_rate:.1f}%"

        task_result = {
            "task_id": task_id,
            "batch_name": batch_name,
            "database_type": database_type,
            "database_version": database_version,
            "config_id": config_id,
            "total_cases": total_cases,
            "completed_cases": completed_cases,
            "success_cases": success_cases,
            "failed_cases": failed_cases,
            "success_rate": success_rate,
            "execution_results": execution_results,
            "capture_enabled": capture_enabled,
            "batch_validation_result": batch_validation_result,
            "timeout_per_case": timeout_per_case,
            "stop_on_failure": stop_on_failure,
            "use_c_executor": use_c_executor,
            "description": f"批量执行 {database_type} 测试用例，共 {total_cases} 个用例",
            "success": success_cases > 0,
            "message": final_message
        }

        # 记录任务完成
        await main_task_logger.complete_execution(
            execution_result=task_result,
            capture_files=all_capture_files,
            output_files=[]
        )

        await progress.update_progress(100, task_result["message"])
        await progress.set_completed(task_result)

        logger.info(f"批量测试用例执行任务完成: {task_id}, 成功 {success_cases}/{completed_cases} 个用例")
        return task_result

    except Exception as e:
        # 记录任务失败
        await main_task_logger.fail_execution(
            error_code="BATCH_EXECUTION_ERROR",
            error_message=str(e),
            error_details={
                "batch_name": batch_name,
                "database_type": database_type,
                "total_cases": len(test_case_items)
            }
        )
        
        logger.error(f"批量测试用例执行任务失败: {e}")

        # 即使任务失败，也要清理连接池
        try:
            await _cleanup_database_connections(database_type, test_case_execution_agent_service)
        except Exception as cleanup_error:
            logger.error(f"清理连接池失败: {cleanup_error}")

        await progress.set_failed(str(e))
        raise



async def single_test_case_execution_task(
    ctx: Dict[str, Any],
    test_case_json: str,
    config_id: int,
    capture_enabled: bool = True,
    test_case_id: Optional[str] = None,
    use_c_executor: bool = False
) -> Dict[str, Any]:
    """单个测试用例执行任务"""
    task_id = ctx.get('job_id', 'unknown')
    redis = redis_manager.redis
    progress = TaskProgress(task_id, redis)

    # 创建新的任务执行日志记录器
    from utils.task_execution_logger import create_task_logger, ExecutorSelector, ExecutorType
    exec_logger = create_task_logger(
        task_id=task_id,
        task_type="single_test_case_execution",
        task_name=f"单个测试用例执行-{test_case_id or 'Unknown'}"
    )

    try:
        await exec_logger.test_execution_info_async(f"开始单个测试用例执行任务: {task_id}")
        logger.info(f"开始单个测试用例执行任务: {task_id}")

        # 1. 初始化执行服务（5%）
        await progress.update_progress(5, "初始化测试用例执行服务...")
        await exec_logger.system_info_async("初始化测试用例执行服务")

        from services.test_case_execution_agent_service import test_case_execution_agent_service
        
        # 2. 执行测试用例（5-90%）
        await progress.update_progress(10, "开始执行测试用例...")
        await exec_logger.test_execution_info_async(f"开始执行测试用例，抓包启用: {capture_enabled}")

        # 记录开始时间
        start_time = get_current_time()

        # 执行测试用例
        result = await test_case_execution_agent_service.execute_test_case(
            test_case_json,
            config_id,
            capture_enabled,
            test_case_id,
            use_c_executor
        )

        # 记录结束时间
        end_time = get_current_time()
        duration = int((end_time - start_time).total_seconds())

        # 记录执行结果
        if result.get("success"):
            await exec_logger.test_execution_info_async(f"测试用例执行成功，耗时: {duration}秒")
        else:
            error_msg = result.get("error", "未知错误")
            await exec_logger.test_execution_error_async(f"测试用例执行失败: {error_msg}", error_details=error_msg)
        
        # 3. 更新进度到90%
        await progress.update_progress(90, "测试用例执行完成，正在整理结果...")
        await exec_logger.test_execution_info_async("测试用例执行完成，正在整理结果")

        # 4. 收集抓包文件
        capture_files = []
        if result.get("success") and result.get("capture_files"):
            capture_files = result.get("capture_files", [])
            if capture_files:
                await exec_logger.capture_info_async(f"收集到抓包文件: {len(capture_files)} 个")
                for capture_file in capture_files:
                    await exec_logger.capture_info_async(f"抓包文件: {capture_file}", capture_file=capture_file)
            else:
                await exec_logger.capture_info_async("未生成抓包文件")
        else:
            await exec_logger.capture_info_async("测试用例执行失败或未启用抓包，无抓包文件")

        # 5. PCAP验证（如果有抓包文件）
        validation_result = None
        if capture_files and capture_enabled:
            try:
                await progress.update_progress(92, "正在验证抓包质量...")
                await exec_logger.capture_info_async("开始PCAP抓包质量验证")

                from services.capture_validation_manager import CaptureValidationManager
                validation_manager = CaptureValidationManager()

                # 创建验证任务
                validation_task_id = f"validation_{task_id}_{int(time.time())}"

                # 从配置中获取数据库类型
                from services.database_config_service import database_config_service
                config = await database_config_service.get_config(config_id)
                database_type = config.database_type if config else "mysql"

                # 预期的SQL操作类型（基于测试用例内容推断）
                expected_operations = ["SELECT", "INSERT", "UPDATE", "DELETE"]

                validation_task = validation_manager.start_capture_task(
                    task_id=validation_task_id,
                    database_type=database_type,
                    expected_operations=expected_operations
                )

                # 添加抓包文件到验证任务
                for capture_file in capture_files:
                    validation_manager.add_pcap_file_to_task(validation_task_id, capture_file)

                # 执行验证
                validation_result = validation_manager.complete_capture_task(validation_task_id)

                if validation_result:
                    success_rate = validation_result.get('summary', {}).get('success_rate', 0)
                    total_sql_found = validation_result.get('summary', {}).get('total_sql_found', 0)

                    await exec_logger.capture_info_async(
                        f"PCAP验证完成: 成功率 {success_rate:.1%}, 找到SQL语句 {total_sql_found} 条"
                    )

                    # 如果验证成功率较低，记录警告
                    if success_rate < 0.5:
                        await exec_logger.capture_info_async(
                            f"警告: PCAP验证成功率较低 ({success_rate:.1%})，建议检查抓包配置"
                        )
                else:
                    await exec_logger.capture_info_async("PCAP验证失败")

            except Exception as e:
                await exec_logger.capture_info_async(f"PCAP验证异常: {str(e)}")
                logger.warning(f"PCAP验证失败: {str(e)}")
        
        # 6. 完成（100%）
        final_result = {
            "success": result.get("success", False),
            "execution_result": result,
            "capture_files": capture_files,
            "validation_result": validation_result,
            "duration": duration,
            "test_case_id": test_case_id,
            "config_id": config_id,
            "capture_enabled": capture_enabled
        }
        
        if result.get("success"):
            await exec_logger.test_execution_info_async(f"单个测试用例执行任务完成，耗时: {duration}秒")
            await progress.set_completed(final_result)
            logger.info(f"单个测试用例执行任务完成: {task_id}, 耗时: {duration}秒")
        else:
            error_msg = result.get("error", "执行失败")
            await exec_logger.test_execution_error_async(f"单个测试用例执行任务失败: {error_msg}", error_details=error_msg)
            await progress.set_failed(error_msg)
            logger.error(f"单个测试用例执行任务失败: {task_id}, 错误: {error_msg}")

        return final_result

    except Exception as e:
        await exec_logger.test_execution_error_async(f"单个测试用例执行任务异常: {str(e)}",
                                                   error_details=str(e), include_stack_trace=True)
        logger.error(f"单个测试用例执行任务异常: {task_id}, 错误: {str(e)}")
        await progress.set_failed(str(e))
        return {
            "success": False,
            "error": str(e),
            "duration": int(time.time() - progress.start_time) if hasattr(progress, 'start_time') else 0
        }

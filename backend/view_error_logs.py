#!/usr/bin/env python3
"""
错误日志查看工具
用于查看和分析项目中的错误日志
"""

import os
import sys
from pathlib import Path
from datetime import datetime
import argparse

def list_log_files(log_type="error"):
    """列出指定类型的日志文件"""
    from utils.path_manager import path_manager
    log_dir = Path(path_manager.get_logs_dir()) / log_type
    
    if not log_dir.exists():
        print(f"❌ {log_type}日志目录不存在: {log_dir}")
        return []
    
    log_files = list(log_dir.glob("*.log*"))
    log_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
    
    return log_files

def view_log_file(file_path, lines=50, follow=False):
    """查看日志文件"""
    if not file_path.exists():
        print(f"❌ 日志文件不存在: {file_path}")
        return
    
    print(f"📋 查看日志文件: {file_path.name}")
    print("=" * 80)
    
    try:
        if follow:
            # 实时跟踪日志文件
            import subprocess
            subprocess.run(['tail', '-f', str(file_path)])
        else:
            # 显示最后N行
            with open(file_path, 'r', encoding='utf-8') as f:
                all_lines = f.readlines()
                if lines > 0:
                    display_lines = all_lines[-lines:] if len(all_lines) > lines else all_lines
                else:
                    display_lines = all_lines
                
                for line in display_lines:
                    print(line.rstrip())
                    
    except Exception as e:
        print(f"❌ 读取日志文件失败: {e}")

def search_in_logs(search_term, log_type="error", context_lines=3):
    """在日志中搜索特定内容"""
    log_files = list_log_files(log_type)
    
    if not log_files:
        print(f"❌ 没有找到{log_type}日志文件")
        return
    
    print(f"🔍 在{log_type}日志中搜索: '{search_term}'")
    print("=" * 80)
    
    found_any = False
    
    for log_file in log_files:
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            matches = []
            for i, line in enumerate(lines):
                if search_term.lower() in line.lower():
                    matches.append(i)
            
            if matches:
                found_any = True
                print(f"\n📁 文件: {log_file.name}")
                print("-" * 60)
                
                for match_line in matches:
                    # 显示上下文
                    start = max(0, match_line - context_lines)
                    end = min(len(lines), match_line + context_lines + 1)
                    
                    print(f"\n行 {match_line + 1}:")
                    for i in range(start, end):
                        prefix = ">>> " if i == match_line else "    "
                        print(f"{prefix}{i + 1:4d}: {lines[i].rstrip()}")
                    
        except Exception as e:
            print(f"❌ 搜索文件 {log_file.name} 失败: {e}")
    
    if not found_any:
        print(f"❌ 在{log_type}日志中未找到 '{search_term}'")

def show_log_summary():
    """显示日志文件摘要"""
    log_types = ["error", "app", "debug", "startup"]
    
    print("📊 日志文件摘要")
    print("=" * 80)
    
    for log_type in log_types:
        log_files = list_log_files(log_type)
        print(f"\n📂 {log_type.upper()} 日志:")
        
        if not log_files:
            print("   ❌ 无日志文件")
            continue
        
        total_size = 0
        for log_file in log_files:
            size = log_file.stat().st_size
            total_size += size
            mtime = datetime.fromtimestamp(log_file.stat().st_mtime)
            
            print(f"   📄 {log_file.name}")
            print(f"      📅 修改时间: {mtime.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"      📏 文件大小: {size:,} bytes")
        
        print(f"   📊 总大小: {total_size:,} bytes ({len(log_files)} 个文件)")

def show_recent_errors(hours=24, max_lines=100):
    """显示最近的错误"""
    error_files = list_log_files("error")
    
    if not error_files:
        print("❌ 没有找到错误日志文件")
        return
    
    print(f"🚨 最近 {hours} 小时的错误 (最多显示 {max_lines} 行)")
    print("=" * 80)
    
    from datetime import datetime, timedelta
    cutoff_time = datetime.now() - timedelta(hours=hours)
    
    recent_errors = []
    
    for error_file in error_files:
        try:
            # 检查文件修改时间
            file_mtime = datetime.fromtimestamp(error_file.stat().st_mtime)
            if file_mtime < cutoff_time:
                continue
            
            with open(error_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for line in lines:
                # 简单的时间解析（假设日志格式为标准格式）
                if line.strip():
                    recent_errors.append((error_file.name, line.strip()))
                    
        except Exception as e:
            print(f"❌ 处理文件 {error_file.name} 失败: {e}")
    
    if not recent_errors:
        print("✅ 最近没有错误记录")
        return
    
    # 显示最近的错误（限制行数）
    for i, (filename, error_line) in enumerate(recent_errors[-max_lines:]):
        print(f"[{filename}] {error_line}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="错误日志查看工具")
    parser.add_argument("--type", "-t", default="error", 
                       choices=["error", "app", "debug", "startup"],
                       help="日志类型 (默认: error)")
    parser.add_argument("--lines", "-n", type=int, default=50,
                       help="显示行数 (默认: 50, 0表示全部)")
    parser.add_argument("--follow", "-f", action="store_true",
                       help="实时跟踪日志文件")
    parser.add_argument("--search", "-s", type=str,
                       help="搜索特定内容")
    parser.add_argument("--summary", action="store_true",
                       help="显示日志摘要")
    parser.add_argument("--recent", "-r", type=int, metavar="HOURS",
                       help="显示最近N小时的错误")
    parser.add_argument("--file", type=int, metavar="INDEX",
                       help="查看指定索引的日志文件")
    
    args = parser.parse_args()
    
    if args.summary:
        show_log_summary()
        return
    
    if args.recent is not None:
        show_recent_errors(args.recent)
        return
    
    if args.search:
        search_in_logs(args.search, args.type)
        return
    
    # 列出日志文件
    log_files = list_log_files(args.type)
    
    if not log_files:
        print(f"❌ 没有找到{args.type}日志文件")
        return
    
    if args.file is not None:
        # 查看指定文件
        if 0 <= args.file < len(log_files):
            view_log_file(log_files[args.file], args.lines, args.follow)
        else:
            print(f"❌ 无效的文件索引: {args.file} (有效范围: 0-{len(log_files)-1})")
        return
    
    # 显示文件列表
    print(f"📋 {args.type.upper()} 日志文件列表:")
    print("=" * 80)
    
    for i, log_file in enumerate(log_files):
        mtime = datetime.fromtimestamp(log_file.stat().st_mtime)
        size = log_file.stat().st_size
        
        print(f"{i:2d}. {log_file.name}")
        print(f"    📅 时间: {mtime.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"    📏 大小: {size:,} bytes")
        print()
    
    print("💡 使用 --file INDEX 查看特定文件")
    print("💡 使用 --search TERM 搜索内容")
    print("💡 使用 --recent HOURS 查看最近错误")
    print("💡 使用 --summary 查看摘要")

if __name__ == "__main__":
    main()

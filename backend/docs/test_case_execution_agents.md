# 测试用例执行Agents系统

## 概述

本系统基于LangChain agents架构，实现了测试用例的自动化解析、SQL提取、执行顺序管理和抓包控制。系统能够智能地分析测试用例JSON结构，提取SQL语句，确定抓包需求，并按正确的顺序执行测试步骤。

## 架构设计

### 核心组件

1. **TestCaseParserTool** - 测试用例解析器
   - 解析测试用例JSON结构
   - 提取test_steps中的SQL语句和test_data
   - 确定执行阶段和抓包策略

2. **SQLClassifierTool** - SQL分类器
   - 分类SQL语句类型（DDL/DML/DQL/DCL/TCL）
   - 识别需要抓包的SQL语句
   - 提供SQL类型描述

3. **ExecutionOrderTool** - 执行顺序管理器
   - 管理测试步骤的执行顺序
   - 分析步骤间的依赖关系
   - 确保正确的执行流程

4. **TestCaseExecutionAgentService** - 主协调器
   - 整合所有子agents
   - 协调抓包和SQL执行
   - 管理完整的测试用例执行流程

### 执行阶段

系统将测试步骤分为四个执行阶段：

1. **SETUP** - 环境准备阶段
   - 创建表、插入测试数据
   - 通常不需要抓包

2. **EXECUTION** - 查询执行阶段
   - 执行主要的业务查询
   - 需要抓包分析

3. **VALIDATION** - 验证阶段
   - 验证查询结果
   - 可能需要抓包

4. **CLEANUP** - 清理阶段
   - 删除测试数据、清理环境
   - 通常不需要抓包

### 抓包策略

- **NO_CAPTURE** - 不需要抓包（DDL操作、环境准备）
- **CAPTURE_REQUIRED** - 需要抓包（DQL查询）
- **OPTIONAL_CAPTURE** - 可选抓包（DML操作）

## API接口

### 1. 解析测试步骤
```http
POST /api/test-case-execution/parse-steps
Content-Type: application/json

{
    "test_case_json": "测试用例的JSON字符串"
}
```

### 2. 分类SQL语句
```http
POST /api/test-case-execution/classify-sql
Content-Type: application/json

["SQL语句1", "SQL语句2", ...]
```

### 3. 分析测试用例
```http
POST /api/test-case-execution/analyze
Content-Type: application/json

{
    "test_case_json": "测试用例的JSON字符串"
}
```

### 4. 执行测试用例
```http
POST /api/test-case-execution/execute
Content-Type: application/json

{
    "test_case_json": "测试用例的JSON字符串",
    "config_id": 1,
    "capture_enabled": true,
    "async_execution": false
}
```

### 5. 查询执行状态（异步执行）
```http
GET /api/test-case-execution/status/{execution_id}
```

### 6. 获取执行结果（异步执行）
```http
GET /api/test-case-execution/result/{execution_id}
```

## 使用示例

### 基本使用流程

1. **解析测试用例**
```python
# 解析测试用例，提取步骤信息
result = requests.post("/api/test-case-execution/parse-steps", json={
    "test_case_json": test_case_json
})
```

2. **分析执行计划**
```python
# 使用LangChain agents分析测试用例
result = requests.post("/api/test-case-execution/analyze", json={
    "test_case_json": test_case_json
})
```

3. **执行测试用例**
```python
# 执行测试用例并进行抓包
result = requests.post("/api/test-case-execution/execute", json={
    "test_case_json": test_case_json,
    "config_id": 1,
    "capture_enabled": True
})
```

### 测试用例JSON格式

系统支持以下测试用例JSON结构：

```json
{
    "id": "测试用例ID",
    "title": "测试用例标题",
    "test_steps": [
        {
            "step_number": 1,
            "action": "步骤描述",
            "expected_result": "预期结果",
            "test_data": "{\"sql\": \"SELECT * FROM table\"}"
        }
    ]
}
```

### test_data支持的格式

- `create_table`: 建表语句
- `insert_data`: 插入数据语句（数组或单个语句）
- `sql`: 查询语句
- `verify_sql`: 验证查询语句
- `cleanup`: 清理语句

## 执行流程

1. **解析阶段**
   - 解析测试用例JSON
   - 提取所有SQL语句
   - 确定执行阶段和抓包策略

2. **分析阶段**
   - 使用LangChain agents分析测试用例
   - 生成详细的执行计划
   - 分析步骤依赖关系

3. **执行阶段**
   - 按阶段顺序执行：SETUP → EXECUTION → VALIDATION → CLEANUP
   - 在需要时启动/停止抓包
   - 收集执行结果和抓包文件

4. **结果阶段**
   - 生成执行摘要
   - 返回抓包文件路径
   - 提供成功率统计

## 集成现有服务

系统与现有的MySQL抓包服务完全集成：

- **MySQLLocalPacketCaptureService** - 本地抓包服务
- **MySQLService** - MySQL数据库服务
- **DatabaseConfigService** - 数据库配置服务

## 优势特性

1. **智能解析** - 自动从测试用例中提取SQL语句和执行信息
2. **依赖管理** - 自动分析步骤间的依赖关系
3. **抓包控制** - 智能决定何时需要进行数据包捕获
4. **异步执行** - 支持长时间运行的测试用例
5. **错误处理** - 完善的错误处理和状态管理
6. **扩展性** - 基于LangChain agents，易于扩展新功能

## 测试和验证

运行测试脚本验证agents功能：

```bash
cd backend
python test_agents.py
```

运行API演示：

```bash
cd backend
python example_test_case_execution.py
```

## 配置要求

1. **数据库配置** - 需要有效的MySQL数据库配置
2. **DeepSeek API** - 需要配置DeepSeek API密钥
3. **网络权限** - 需要tcpdump权限进行抓包
4. **Python依赖** - 安装LangChain和相关依赖

## 注意事项

1. 确保数据库配置正确且可访问
2. 抓包功能需要适当的系统权限
3. 异步执行状态会在内存中保存，重启服务会丢失
4. 大型测试用例可能需要较长的执行时间
5. 抓包文件会保存在captures目录中

## 未来扩展

- 支持更多数据库类型（PostgreSQL、MongoDB等）
- 增加测试结果的AI分析功能
- 支持测试用例的批量执行
- 增加更详细的执行报告生成
- 支持测试用例的版本管理

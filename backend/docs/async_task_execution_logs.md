# 异步任务执行记录系统

## 概述

异步任务执行记录系统是一个全面的任务执行监控和日志记录解决方案，专门为AI SQL抓包项目设计。该系统能够详细记录每个异步任务的执行情况，包括执行器选择、性能指标、错误信息等，为后期排查问题和性能优化提供重要数据支持。

## 主要功能

### 1. 详细的任务执行记录
- **任务基本信息**: 任务ID、类型、名称、描述
- **执行状态跟踪**: PENDING、RUNNING、COMPLETED、FAILED、CANCELLED、TIMEOUT
- **进度监控**: 实时进度百分比和当前执行步骤
- **执行器管理**: Python、C、Java执行器的选择和回退记录

### 2. 性能监控
- **执行时间**: 开始时间、结束时间、执行时长
- **系统资源**: CPU使用率、内存使用量
- **执行器性能对比**: 不同执行器的性能统计

### 3. 错误处理和诊断
- **详细错误信息**: 错误代码、错误消息、错误详情
- **堆栈跟踪**: 完整的错误堆栈信息
- **重试机制**: 重试次数和最大重试次数记录

### 4. 批量任务管理
- **批量执行跟踪**: 批量ID、批量名称、总数、当前索引
- **依赖关系**: 父任务、子任务、依赖任务的关系记录

## 数据库表结构

### async_task_execution_logs 表

```sql
CREATE TABLE async_task_execution_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    task_id VARCHAR(36) NOT NULL,                    -- 任务ID（UUID）
    task_type ENUM(...) NOT NULL,                    -- 任务类型
    task_name VARCHAR(255),                          -- 任务名称
    task_description TEXT,                           -- 任务描述
    
    -- 任务状态信息
    status ENUM(...) NOT NULL DEFAULT 'PENDING',    -- 任务状态
    progress INT DEFAULT 0,                          -- 执行进度(0-100)
    current_step VARCHAR(255),                       -- 当前执行步骤
    
    -- 执行器信息
    executor_type ENUM('PYTHON', 'C', 'JAVA') NOT NULL DEFAULT 'PYTHON',
    executor_version VARCHAR(50),                    -- 执行器版本
    executor_path VARCHAR(500),                      -- 执行器路径
    executor_selection_reason TEXT,                  -- 执行器选择原因
    fallback_from_executor ENUM('PYTHON', 'C', 'JAVA'), -- 回退来源执行器
    
    -- 配置信息
    database_config_id INT,                          -- 数据库配置ID
    server_config_id INT,                           -- 服务器配置ID
    capture_enabled BOOLEAN DEFAULT TRUE,           -- 是否启用抓包
    capture_duration INT,                           -- 抓包持续时间(秒)
    
    -- 输入参数
    input_sql_query TEXT,                           -- 输入SQL查询
    input_mongo_query TEXT,                         -- 输入MongoDB查询
    input_natural_query TEXT,                       -- 输入自然语言查询
    input_parameters JSON,                          -- 其他输入参数
    
    -- 执行结果
    execution_result JSON,                          -- 执行结果详情
    output_files JSON,                              -- 输出文件列表
    capture_files JSON,                             -- 抓包文件列表
    generated_sql TEXT,                             -- AI生成的SQL语句
    
    -- 性能指标
    start_time TIMESTAMP NULL,                      -- 开始时间
    end_time TIMESTAMP NULL,                        -- 结束时间
    duration_seconds DECIMAL(10,3),                 -- 执行时长(秒)
    cpu_usage_percent DECIMAL(5,2),                 -- CPU使用率
    memory_usage_mb DECIMAL(10,2),                  -- 内存使用量(MB)
    
    -- 错误信息
    error_code VARCHAR(50),                         -- 错误代码
    error_message TEXT,                             -- 错误消息
    error_details JSON,                             -- 详细错误信息
    error_stack_trace TEXT,                         -- 错误堆栈跟踪
    retry_count INT DEFAULT 0,                      -- 重试次数
    max_retries INT DEFAULT 0,                      -- 最大重试次数
    
    -- 环境信息
    worker_id VARCHAR(100),                         -- 工作器ID
    worker_host VARCHAR(255),                       -- 工作器主机
    python_version VARCHAR(50),                     -- Python版本
    system_info JSON,                               -- 系统信息
    environment_variables JSON,                     -- 环境变量
    
    -- 依赖任务
    parent_task_id VARCHAR(36),                     -- 父任务ID
    child_task_ids JSON,                            -- 子任务ID列表
    dependency_task_ids JSON,                       -- 依赖任务ID列表
    
    -- 批量执行相关
    batch_id VARCHAR(36),                           -- 批量执行ID
    batch_name VARCHAR(255),                        -- 批量执行名称
    batch_total_count INT,                          -- 批量总数
    batch_current_index INT,                        -- 批量当前索引
    
    -- 测试用例相关
    test_case_id VARCHAR(36),                       -- 测试用例ID
    test_case_name VARCHAR(255),                    -- 测试用例名称
    test_steps_executed JSON,                       -- 已执行的测试步骤
    test_validation_results JSON,                   -- 测试验证结果
    
    -- 抓包相关
    network_interface VARCHAR(100),                 -- 网络接口
    packet_count INT,                               -- 抓包数量
    packet_size_bytes BIGINT,                       -- 抓包总大小(字节)
    capture_filter VARCHAR(500),                    -- 抓包过滤器
    
    -- 审计信息
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, -- 更新时间
    created_by VARCHAR(100),                        -- 创建者
    
    -- 索引
    UNIQUE KEY uk_task_id (task_id),
    INDEX idx_task_type (task_type),
    INDEX idx_status (status),
    INDEX idx_executor_type (executor_type),
    -- ... 更多索引
);
```

## 使用方法

### 1. 初始化数据库表

```bash
cd backend
python scripts/init_task_execution_logs_table.py
```

### 2. 在异步任务中集成日志记录

#### 基本使用示例

```python
from utils.task_execution_logger import create_task_logger, ExecutorType

async def my_async_task(task_id: str, config_id: int, sql_query: str):
    # 创建任务日志记录器
    task_logger = create_task_logger(
        task_id=task_id,
        task_type="mysql_capture",
        task_name="MySQL抓包任务"
    )
    
    try:
        # 初始化任务记录
        await task_logger.initialize(
            database_config_id=config_id,
            input_sql_query=sql_query,
            capture_enabled=True
        )
        
        # 开始执行
        await task_logger.start_execution(
            executor_type=ExecutorType.PYTHON,
            executor_selection_reason="系统推荐使用Python执行器"
        )
        
        # 更新进度
        await task_logger.update_progress(50, "正在执行SQL查询")
        
        # 执行具体任务逻辑
        result = await execute_task_logic()
        
        # 记录完成
        await task_logger.complete_execution(
            execution_result=result,
            capture_files=["/tmp/capture.pcap"]
        )
        
    except Exception as e:
        # 记录失败
        await task_logger.fail_execution(
            error_code="TASK_ERROR",
            error_message=str(e)
        )
        raise
```

#### 执行器回退示例

```python
async def task_with_executor_fallback(task_id: str):
    task_logger = create_task_logger(task_id, "mongo_capture")
    await task_logger.initialize()
    
    try:
        # 尝试使用C执行器
        await task_logger.start_execution(ExecutorType.C, "性能优先选择C执行器")
        
        result = await execute_with_c_executor()
        
    except Exception as c_error:
        # 记录执行器回退
        await task_logger.record_executor_fallback(
            ExecutorType.C, ExecutorType.PYTHON, 
            f"C执行器失败: {str(c_error)}"
        )
        
        # 使用Python执行器重试
        result = await execute_with_python_executor()
    
    await task_logger.complete_execution(result)
```

#### 批量任务示例

```python
from utils.task_execution_logger import create_batch_task_logger

async def batch_task_execution(test_case_ids: list):
    # 创建批量任务日志记录器
    batch_logger = create_batch_task_logger(
        batch_id=str(uuid.uuid4()),
        batch_name="用户模块测试批次",
        total_count=len(test_case_ids)
    )
    
    for test_case_id in test_case_ids:
        # 为每个子任务创建日志记录器
        task_logger = await batch_logger.create_task_logger(
            task_id=str(uuid.uuid4()),
            task_type="single_test_case_execution",
            test_case_id=test_case_id
        )
        
        # 执行单个测试用例
        await execute_single_test_case(task_logger, test_case_id)
    
    # 获取批量执行统计
    stats = await batch_logger.get_batch_statistics()
    return stats
```

### 3. API接口使用

#### 获取任务执行日志

```bash
# 获取指定任务的执行日志
GET /api/task-execution-logs/task/{task_id}

# 根据任务类型获取日志
GET /api/task-execution-logs/tasks/by-type/mysql_capture?limit=50

# 获取失败的任务
GET /api/task-execution-logs/tasks/failed?hours=24

# 获取执行器统计
GET /api/task-execution-logs/statistics/executor?days=7

# 搜索任务日志
GET /api/task-execution-logs/search?task_type=mysql_capture&status=FAILED&start_date=2024-01-01
```

### 4. 性能监控和分析

#### 执行器性能对比

```python
from services.async_task_execution_log_service import async_task_execution_log_service

# 获取执行器统计
stats = await async_task_execution_log_service.get_executor_statistics(days=7)
print(f"C执行器平均执行时间: {stats['c_executor_avg_duration']}秒")
print(f"Python执行器平均执行时间: {stats['python_executor_avg_duration']}秒")
```

#### 失败任务分析

```python
# 获取最近24小时的失败任务
failed_tasks = await async_task_execution_log_service.get_failed_tasks(hours=24)

for task in failed_tasks:
    print(f"任务ID: {task['task_id']}")
    print(f"失败原因: {task['error_message']}")
    print(f"执行器类型: {task['executor_type']}")
```

## 最佳实践

### 1. 任务命名规范
- 使用描述性的任务名称
- 包含关键参数信息
- 便于后期查询和分析

### 2. 错误处理
- 提供详细的错误信息
- 记录完整的堆栈跟踪
- 使用标准化的错误代码

### 3. 性能监控
- 定期分析执行器性能
- 监控任务执行时长趋势
- 及时发现性能瓶颈

### 4. 数据清理
- 定期清理过期的日志数据
- 保留重要的统计信息
- 建立数据归档策略

## 故障排查

### 常见问题

1. **任务记录创建失败**
   - 检查数据库连接
   - 验证表结构是否正确
   - 确认权限设置

2. **执行器选择异常**
   - 检查C执行器可用性
   - 验证环境检测器配置
   - 查看执行器路径设置

3. **性能数据异常**
   - 确认系统监控权限
   - 检查psutil库安装
   - 验证时间同步

### 日志查看

```bash
# 查看任务执行日志
tail -f logs/task_execution.log

# 查看数据库操作日志
tail -f logs/mysql_service.log
```

## 扩展功能

### 1. 自定义执行器
- 支持Java执行器集成
- 可扩展的执行器选择策略
- 动态执行器注册机制

### 2. 高级分析
- 任务执行趋势分析
- 性能基准测试
- 异常模式识别

### 3. 告警机制
- 失败率阈值告警
- 性能下降告警
- 资源使用告警

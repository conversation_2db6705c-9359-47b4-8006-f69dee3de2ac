# PCAP抓包自检功能使用指南

## 概述

PCAP抓包自检功能是一个用于验证抓包是否成功捕获到预期SQL语句的工具集。它支持六种主流数据库协议的SQL语句解析和验证：

- **MySQL** - 支持COM_QUERY协议和通用SQL模式匹配
- **PostgreSQL** - 支持Simple Query和Extended Query协议
- **MongoDB** - 支持Wire Protocol和BSON查询解析
- **Oracle** - 支持TNS协议和PL/SQL语句
- **SQL Server** - 支持TDS协议和T-SQL语句
- **GaussDB** - 兼容PostgreSQL协议，支持标准SQL语句

## 核心组件

### 1. PCAP SQL分析器 (`pcap_sql_analyzer.py`)
- 解析PCAP文件中的数据库协议数据包
- 提取SQL语句并分类（SELECT、INSERT、UPDATE、DELETE等）
- 支持多种数据库协议的专门解析器
- 提供置信度评估

### 2. PCAP验证服务 (`pcap_validation_service.py`)
- 根据预定义规则验证抓包结果
- 支持自定义验证规则
- 生成详细的验证报告
- 提供建议和改进意见

### 3. 抓包验证管理器 (`capture_validation_manager.py`)
- 统一管理抓包任务的验证流程
- 支持任务跟踪和状态管理
- 自动发现和添加PCAP文件
- 批量验证功能

### 4. REST API接口 (`pcap_validation_api.py`)
- 提供HTTP API接口
- 支持单文件和批量验证
- 任务管理API
- 自定义规则管理

### 5. 命令行工具 (`pcap_validator_cli.py`)
- 独立的命令行验证工具
- 支持分析、验证、批量处理
- 适合脚本化和自动化使用

## 快速开始

### 1. 安装依赖

```bash
pip install scapy
```

### 2. 基本使用

#### 分析PCAP文件中的SQL语句

```python
from services.pcap_sql_analyzer import PCAPSQLAnalyzer

analyzer = PCAPSQLAnalyzer()
result = analyzer.analyze_pcap_file("path/to/capture.pcap")

print(f"找到 {len(result.sql_statements)} 条SQL语句")
for stmt in result.sql_statements:
    print(f"{stmt.sql_type.value}: {stmt.sql}")
```

#### 验证PCAP文件

```python
from services.pcap_validation_service import PCAPValidationService

validator = PCAPValidationService()
result = validator.validate_pcap_file("path/to/capture.pcap", rule_name="mysql_basic")

print(f"验证状态: {result.status.value}")
print(f"成功: {result.success}")
print(f"找到SQL数量: {result.total_sql_found}")
```

### 3. 集成到抓包流程

```python
from services.capture_validation_manager import CaptureValidationManager

# 创建验证管理器
manager = CaptureValidationManager()

# 开始抓包任务
task = manager.start_capture_task(
    task_id="mysql_test_001",
    database_type="mysql",
    expected_operations=["SELECT", "INSERT", "UPDATE"]
)

# 执行抓包...
# pcap_file = await capture_service.start_capture()
# await capture_service.stop_capture()

# 添加PCAP文件到任务
manager.add_pcap_file_to_task("mysql_test_001", pcap_file)

# 完成任务并验证
validation_result = manager.complete_capture_task("mysql_test_001")
print(f"验证成功率: {validation_result['summary']['success_rate']:.1%}")
```

## 命令行工具使用

### 分析PCAP文件

```bash
python backend/tools/pcap_validator_cli.py analyze capture1.pcap capture2.pcap -o report.json
```

### 验证PCAP文件

```bash
python backend/tools/pcap_validator_cli.py validate capture.pcap \
    --database-type mysql \
    --expected-operations SELECT INSERT UPDATE \
    --output validation_report.json
```

### 批量验证目录

```bash
python backend/tools/pcap_validator_cli.py batch backend/captures \
    --pattern "mysql_*.pcap" \
    --database-type mysql \
    --expected-operations SELECT INSERT UPDATE DELETE
```

## API接口使用

### 启动后端服务

```bash
cd backend
python start_with_worker.py
```

### 验证单个文件

```bash
curl -X POST "http://localhost:8000/api/pcap-validation/validate-single" \
    -H "Content-Type: application/json" \
    -d '{
        "pcap_file": "mysql_capture_20250815.pcap",
        "database_type": "mysql",
        "expected_operations": ["SELECT", "INSERT"]
    }'
```

### 管理抓包任务

```bash
# 开始任务
curl -X POST "http://localhost:8000/api/pcap-validation/tasks/start" \
    -H "Content-Type: application/json" \
    -d '{
        "task_id": "test_001",
        "database_type": "mysql",
        "expected_operations": ["SELECT", "INSERT", "UPDATE"]
    }'

# 添加PCAP文件
curl -X POST "http://localhost:8000/api/pcap-validation/tasks/add-pcap" \
    -H "Content-Type: application/json" \
    -d '{
        "task_id": "test_001",
        "pcap_file": "mysql_capture.pcap"
    }'

# 完成任务
curl -X POST "http://localhost:8000/api/pcap-validation/tasks/complete" \
    -H "Content-Type: application/json" \
    -d '{
        "task_id": "test_001",
        "auto_validate": true
    }'
```

## 自定义验证规则

### 创建自定义规则

```python
from services.pcap_validation_service import ValidationRule, ExpectedSQL
from services.pcap_sql_analyzer import DatabaseType, SQLType

custom_rule = ValidationRule(
    name="custom_mysql_rule",
    description="自定义MySQL验证规则",
    expected_sqls=[
        ExpectedSQL("SELECT", SQLType.SELECT, DatabaseType.MYSQL, required=True),
        ExpectedSQL("INSERT", SQLType.INSERT, DatabaseType.MYSQL, required=True),
        ExpectedSQL("UPDATE", SQLType.UPDATE, DatabaseType.MYSQL, required=False),
    ],
    min_total_sql_count=2,
    min_confidence=0.7
)

validator.add_custom_rule(custom_rule)
```

### 通过API添加自定义规则

```bash
curl -X POST "http://localhost:8000/api/pcap-validation/rules/custom" \
    -H "Content-Type: application/json" \
    -d '{
        "name": "custom_rule",
        "description": "自定义验证规则",
        "database_type": "mysql",
        "expected_sql_patterns": ["SELECT", "INSERT"],
        "expected_sql_types": ["SELECT", "INSERT"],
        "min_total_sql_count": 1,
        "min_confidence": 0.6
    }'
```

## 验证结果解读

### 验证状态

- **SUCCESS**: 验证成功，找到所有预期的SQL语句
- **PARTIAL**: 部分成功，找到部分预期的SQL语句
- **FAILED**: 验证失败，缺少必需的SQL语句
- **NO_SQL_FOUND**: 未找到任何SQL语句
- **FILE_NOT_FOUND**: PCAP文件不存在
- **ANALYSIS_ERROR**: 分析过程出错

### 置信度说明

- **高 (≥0.8)**: 通过协议解析确认的SQL语句
- **中 (0.5-0.8)**: 通过模式匹配找到的SQL语句
- **低 (<0.5)**: 可能的SQL语句，需要人工确认

### 常见问题和建议

1. **未找到SQL语句**
   - 检查抓包过滤条件是否正确
   - 确认数据库连接和查询是否在抓包期间执行
   - 验证网络接口选择是否正确

2. **置信度较低**
   - 可能是加密连接或压缩数据
   - 尝试调整抓包参数
   - 检查数据库配置

3. **缺少预期操作**
   - 确认测试用例是否完整执行
   - 检查SQL语句是否被正确分类
   - 调整验证规则的要求

## 最佳实践

1. **抓包前准备**
   - 确定预期的SQL操作类型
   - 选择合适的网络接口
   - 设置正确的过滤条件

2. **验证规则设计**
   - 根据测试场景定制验证规则
   - 设置合理的置信度阈值
   - 考虑可选和必需的操作

3. **结果分析**
   - 关注验证状态和建议
   - 检查置信度分布
   - 分析缺失的操作类型

4. **自动化集成**
   - 在CI/CD流程中集成验证
   - 设置自动化报告生成
   - 建立验证失败的告警机制

## 故障排除

### 常见错误

1. **scapy库未安装**
   ```bash
   pip install scapy
   ```

2. **文件权限问题**
   ```bash
   chmod 644 *.pcap
   ```

3. **内存不足**
   - 处理大文件时分批验证
   - 增加系统内存或使用更强的机器

### 调试模式

启用详细日志输出：

```python
import logging
logging.getLogger().setLevel(logging.DEBUG)
```

或在命令行中使用 `-v` 参数：

```bash
python backend/tools/pcap_validator_cli.py validate capture.pcap -v
```

## 扩展开发

### 添加新的数据库协议支持

1. 继承 `BaseProtocolAnalyzer` 类
2. 实现 `analyze` 方法
3. 在 `PCAPSQLAnalyzer` 中注册新的分析器
4. 添加端口映射和测试用例

### 自定义验证逻辑

1. 扩展 `ValidationRule` 类
2. 实现自定义的匹配逻辑
3. 添加新的验证状态类型
4. 更新报告生成逻辑

## 性能优化

1. **大文件处理**
   - 使用流式处理
   - 分块读取数据包
   - 并行处理多个文件

2. **内存优化**
   - 及时释放不需要的数据
   - 使用生成器而非列表
   - 限制同时处理的文件数量

3. **缓存机制**
   - 缓存分析结果
   - 重用验证规则
   - 避免重复解析相同文件

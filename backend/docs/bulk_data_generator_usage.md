# 大批量数据生成服务使用说明

## 概述

本文档介绍如何使用大批量数据生成服务来解决AI生成SQL时经常生成少量示例数据而不是请求的大量数据的问题。

## 问题描述

当用户请求"帮我在large_data插入1000条数据"时，AI往往只生成10条数据的INSERT语句，而不是真正的1000条数据。大批量数据生成服务专门解决这个问题。

## 服务组件

### 1. BulkDataGeneratorService (`services/bulk_data_generator_service.py`)

核心服务类，提供以下功能：
- 检测是否为大批量操作请求
- 从自然语言中提取数量、表名和操作类型
- 根据表结构生成真实的大批量SQL语句
- 支持增删改查四种操作类型

### 2. BulkDataAPI (`api/bulk_data_api.py`)

REST API接口，提供以下端点：
- `/api/bulk-data/detect` - 检测是否为大批量操作
- `/api/bulk-data/generate-sql` - 生成大批量SQL
- `/api/bulk-data/execute-bulk-sql` - 执行大批量SQL
- `/api/bulk-data/smart-bulk-operation` - 一键智能处理
- `/api/bulk-data/templates` - 获取操作模板

### 3. MySQL MCP Agent集成

在 `mysql_mcp_agent_service.py` 中集成了大批量数据处理，当检测到大批量操作时会自动使用专门的处理逻辑。

## 使用方法

### 1. 通过API直接使用

#### 检测大批量操作

```bash
curl -X POST "http://localhost:8000/api/bulk-data/detect" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "帮我在large_data插入1000条数据",
    "config_id": 55
  }'
```

#### 生成大批量SQL

```bash
curl -X POST "http://localhost:8000/api/bulk-data/generate-sql" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "帮我在large_data插入1000条数据", 
    "config_id": 55,
    "execute_sql": false
  }'
```

#### 智能处理（推荐）

```bash
curl -X POST "http://localhost:8000/api/bulk-data/smart-bulk-operation" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "帮我在large_data插入1000条数据",
    "config_id": 55,
    "execute_sql": true,
    "capture_packets": true
  }'
```

### 2. 通过现有MySQL MCP API使用

现有的MySQL MCP API已经集成了大批量数据处理，会自动检测并处理：

```bash
curl -X POST "http://localhost:8000/api/mysql-mcp/smart-query" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "帮我在large_data插入1000条数据",
    "config_id": 55,
    "capture_packets": true
  }'
```

## 支持的操作类型

### 1. 批量插入 (INSERT)

**示例查询：**
- "帮我在large_data插入1000条数据"
- "向users表添加5000条用户记录"
- "批量插入500条商品数据到products表"

**生成效果：**
```sql
-- 批量插入 1000 条数据到表 large_data

INSERT INTO large_data (name, value, description, created_at) VALUES
('用户0001', 456, '描述信息_0001', '2023-12-15 10:30:25'),
('用户0002', 789, '描述信息_0002', '2023-11-08 14:22:18'),
-- ... (真正的1000条数据)
('用户1000', 234, '描述信息_1000', '2023-10-01 09:15:42');
```

### 2. 批量删除 (DELETE)

**示例查询：**
- "删除users表中的前1000条记录"
- "批量删除orders表中500条过期订单"
- "清理products表中的100条测试数据"

**生成效果：**
```sql
-- 批量删除 1000 条记录
DELETE FROM users ORDER BY id LIMIT 1000;
```

### 3. 批量更新 (UPDATE)

**示例查询：**
- "批量更新users表中1000条用户状态"
- "修改orders表中500条订单的状态为已完成"

**生成效果：**
```sql
-- 批量更新 1000 条记录
UPDATE users SET status = '更新值_20250821_142530' ORDER BY id LIMIT 1000;
```

### 4. 批量查询 (SELECT)

**示例查询：**
- "查询users表中的前1000条记录"
- "获取orders表中最新的500条订单"

**生成效果：**
```sql
-- 查询 1000 条记录
SELECT * FROM users ORDER BY id LIMIT 1000;
```

## 数据生成策略

### 智能字段识别

服务会根据字段名称智能生成相应的数据：

- `name`, `username` → "用户0001", "用户0002"...
- `email` → "<EMAIL>", "<EMAIL>"...
- `phone`, `mobile` → 随机手机号格式
- `age` → 18-65随机年龄
- `price`, `amount` → 随机价格/金额
- `city` → 随机城市名
- `company` → 随机公司名
- `created_at` → 随机日期时间

### 分批处理

对于大量数据（>1000条），会自动分批处理：
- 每批最多1000条
- 生成多个INSERT语句
- 避免单个语句过长

## 配置参数

### config_id
数据库配置ID，用于：
- 连接目标数据库
- 获取表结构信息
- 执行生成的SQL语句

### execute_sql
是否执行生成的SQL：
- `true` - 生成并执行SQL
- `false` - 仅生成SQL，不执行

### capture_packets
是否进行数据包捕获：
- `true` - 执行时同时抓包
- `false` - 仅执行，不抓包

## 检测规则

服务会检测以下关键词来判断是否为大批量操作：

### 数量关键词
- `数字 + 条` (如：1000条)
- `数字 + 行` (如：500行)
- `数字 + 个` (如：200个)
- `数字 + 张` (如：100张)
- `数字 + 份` (如：50份)

### 批量关键词
- "批量"、"大量"、"很多"、"大批"
- "插入"、"删除"、"更新"、"查询"
- "生成"、"创建"、"添加"

### 检测阈值
- 数量 ≥ 50 时会被识别为批量操作
- 或者包含明确的批量关键词

## 错误处理

### 常见错误及解决方案

1. **表不存在**
   ```json
   {
     "success": false,
     "error": "MySQL service not configured"
   }
   ```
   解决：检查config_id是否正确，数据库连接是否正常

2. **字段解析失败**
   ```json
   {
     "success": false,
     "error": "Failed to get table structure"
   }
   ```
   解决：确保表存在且有访问权限

3. **SQL执行失败**
   ```json
   {
     "sql_executed": false,
     "execution_error": "Duplicate entry"
   }
   ```
   解决：检查数据约束，可能需要调整生成的数据

## 性能考虑

### 执行时间估算

服务会提供执行时间估算：
- ≤ 100条：< 1秒
- ≤ 1000条：1-3秒
- ≤ 10000条：3-10秒
- \> 10000条：按比例估算

### 优化建议

1. **批量插入优化**
   - 使用事务包装大批量操作
   - 考虑禁用自动提交
   - 适当调整批次大小

2. **索引考虑**
   - 大批量插入前可考虑临时删除索引
   - 插入完成后重建索引

3. **监控资源**
   - 监控内存使用
   - 关注磁盘IO
   - 检查数据库连接数

## 扩展开发

### 添加新的数据模板

在 `BulkDataGeneratorService` 的 `data_templates` 中添加：

```python
self.data_templates = {
    'custom_field': ['值1', '值2', '值3'],
    # 更多模板...
}
```

### 支持新的操作类型

在 `extract_quantity_and_table` 方法中添加检测规则：

```python
elif any(keyword in natural_query for keyword in ['truncate', '清空']):
    operation_type = "TRUNCATE"
```

### 自定义数据生成逻辑

在 `generate_sample_data` 方法中添加字段处理逻辑：

```python
elif field_name.lower() in ["custom_field"]:
    row_data[field_name] = generate_custom_data(i)
```

## 使用示例

### 任务ID: 9def73fa-27e3-49ab-8945-5c9a114682e3

对于您提到的任务，您可以这样使用：

```bash
curl -X POST "http://localhost:8000/api/bulk-data/smart-bulk-operation" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "帮我在large_data插入1000条数据",
    "config_id": 55,
    "execute_sql": true,
    "capture_packets": true
  }'
```

这将会：
1. 自动检测为大批量插入操作
2. 获取large_data表结构
3. 生成真正的1000条INSERT数据
4. 执行SQL并进行数据包捕获
5. 返回完整的执行结果

## 总结

大批量数据生成服务解决了AI生成SQL时的数量不匹配问题，提供了：
- 智能检测大批量操作需求
- 根据表结构生成真实数据
- 支持增删改查四种操作
- 自动分批处理大量数据
- 集成数据包捕获功能
- 完善的错误处理和性能优化

这个服务特别适合处理测试数据生成、性能测试、数据迁移等场景中的大批量数据操作需求。

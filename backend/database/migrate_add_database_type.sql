-- 数据库迁移脚本：添加database_type字段
-- 执行时间：2025-07-17

-- 1. 添加database_type字段（如果不存在）
-- 使用存储过程来检查字段是否存在
DELIMITER $$
CREATE PROCEDURE AddDatabaseTypeColumn()
BEGIN
    DECLARE column_exists INT DEFAULT 0;

    SELECT COUNT(*) INTO column_exists
    FROM information_schema.columns
    WHERE table_schema = 'ai_sql_pcap'
    AND table_name = 'database_configs'
    AND column_name = 'database_type';

    IF column_exists = 0 THEN
        ALTER TABLE database_configs
        ADD COLUMN database_type VARCHAR(20) NOT NULL DEFAULT 'mysql' COMMENT '数据库类型(mysql/postgresql/mongodb/oracle)'
        AFTER database_name;
    END IF;
END$$
DELIMITER ;

CALL AddDatabaseTypeColumn();
DROP PROCEDURE AddDatabaseTypeColumn;

-- 2. 为现有记录设置正确的数据库类型
-- 根据端口号推断数据库类型
UPDATE database_configs
SET database_type = CASE
    WHEN port = 3306 OR port = 3307 OR port = 3308 THEN 'mysql'
    WHEN port = 5432 OR port = 5433 OR port = 5434 OR port = 5435 THEN 'postgresql'
    WHEN port = 27017 OR port = 27018 OR port = 27019 THEN 'mongodb'
    WHEN port = 1521 OR port = 1522 OR port = 1523 THEN 'oracle'
    ELSE 'mysql'
END
WHERE database_type = 'mysql';

-- 3. 添加索引（如果不存在）
DELIMITER $$
CREATE PROCEDURE AddDatabaseTypeIndex()
BEGIN
    DECLARE index_exists INT DEFAULT 0;

    SELECT COUNT(*) INTO index_exists
    FROM information_schema.statistics
    WHERE table_schema = 'ai_sql_pcap'
    AND table_name = 'database_configs'
    AND index_name = 'idx_database_type';

    IF index_exists = 0 THEN
        CREATE INDEX idx_database_type ON database_configs(database_type);
    END IF;
END$$
DELIMITER ;

CALL AddDatabaseTypeIndex();
DROP PROCEDURE AddDatabaseTypeIndex;

-- 4. 显示迁移结果
SELECT 
    id, 
    name, 
    host, 
    port, 
    database_type,
    is_default,
    created_at
FROM database_configs 
ORDER BY database_type, id;

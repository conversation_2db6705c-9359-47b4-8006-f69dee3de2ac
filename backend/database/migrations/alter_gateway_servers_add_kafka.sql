-- 迁移脚本：为 gateway_servers 表添加 Kafka 相关列（向后兼容）
USE ai_sql_pcap;

-- kafka_enabled
ALTER TABLE gateway_servers 
    ADD COLUMN IF NOT EXISTS kafka_enabled BOOLEAN DEFAULT FALSE COMMENT '是否启用Kafka';

-- kafka_host
ALTER TABLE gateway_servers 
    ADD COLUMN IF NOT EXISTS kafka_host VARCHAR(255) NULL COMMENT 'Kafka服务器IP';

-- kafka_port
ALTER TABLE gateway_servers 
    ADD COLUMN IF NOT EXISTS kafka_port INT DEFAULT 9092 COMMENT 'Kafka端口';

-- kafka_topic
ALTER TABLE gateway_servers 
    ADD COLUMN IF NOT EXISTS kafka_topic VARCHAR(255) NULL COMMENT 'Kafka主题';

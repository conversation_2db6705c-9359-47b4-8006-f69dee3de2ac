-- 网关服务器管理表迁移
-- 在 ai_sql_pcap 数据库中添加网关服务器配置表

USE ai_sql_pcap;

-- 创建网关服务器配置表
CREATE TABLE IF NOT EXISTS gateway_servers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE COMMENT '网关服务器名称',
    host VARCHAR(255) NOT NULL COMMENT '服务器IP地址',
    port INT NOT NULL DEFAULT 22 COMMENT 'SSH端口',
    username VARCHAR(100) NOT NULL COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码',
    upload_path VARCHAR(500) NOT NULL DEFAULT '/tmp' COMMENT '文件上传路径',
    description TEXT COMMENT '描述信息',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    is_default BOOLEAN DEFAULT FALSE COMMENT '是否为默认网关',
    connection_timeout INT DEFAULT 30 COMMENT '连接超时(秒)',
    max_connections INT DEFAULT 10 COMMENT '最大连接数',
    gateway_type VARCHAR(20) DEFAULT 'ssh' COMMENT '网关类型(ssh/sftp/ftp)',
    proxy_enabled BOOLEAN DEFAULT FALSE COMMENT '是否启用代理',
    proxy_host VARCHAR(255) NULL COMMENT '代理主机',
    proxy_port INT NULL COMMENT '代理端口',
    -- Kafka配置
    kafka_enabled BOOLEAN DEFAULT FALSE COMMENT '是否启用Kafka',
    kafka_host VARCHAR(255) NULL COMMENT 'Kafka服务器IP',
    kafka_port INT DEFAULT 9092 COMMENT 'Kafka端口',
    kafka_topic VARCHAR(255) NULL COMMENT 'Kafka主题',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_gateway_host (host),
    INDEX idx_gateway_name (name),
    INDEX idx_gateway_active (is_active),
    INDEX idx_gateway_default (is_default),
    INDEX idx_gateway_type (gateway_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='网关服务器配置表';

-- 创建网关连接测试日志表
CREATE TABLE IF NOT EXISTS gateway_connection_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    gateway_id INT NOT NULL COMMENT '网关服务器ID',
    test_success BOOLEAN NOT NULL COMMENT '测试是否成功',
    test_message TEXT COMMENT '测试消息',
    response_time DECIMAL(10,2) NOT NULL COMMENT '响应时间(毫秒)',
    upload_test BOOLEAN NULL COMMENT '上传测试结果',
    disk_space VARCHAR(100) NULL COMMENT '磁盘空间信息',
    server_info JSON NULL COMMENT '服务器信息',
    test_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '测试时间',
    FOREIGN KEY (gateway_id) REFERENCES gateway_servers(id) ON DELETE CASCADE,
    INDEX idx_gateway_connection_gateway_id (gateway_id),
    INDEX idx_gateway_connection_success (test_success),
    INDEX idx_gateway_connection_timestamp (test_timestamp)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='网关连接测试日志表';

-- 创建网关文件传输记录表
CREATE TABLE IF NOT EXISTS gateway_file_transfers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    gateway_id INT NOT NULL COMMENT '网关服务器ID',
    local_path VARCHAR(500) NOT NULL COMMENT '本地文件路径',
    remote_path VARCHAR(500) NOT NULL COMMENT '远程文件路径',
    transfer_type ENUM('upload', 'download') NOT NULL COMMENT '传输类型',
    file_size BIGINT NOT NULL COMMENT '文件大小(字节)',
    transfer_time DECIMAL(10,2) NOT NULL COMMENT '传输耗时(秒)',
    transfer_success BOOLEAN NOT NULL COMMENT '传输是否成功',
    error_message TEXT NULL COMMENT '错误信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (gateway_id) REFERENCES gateway_servers(id) ON DELETE CASCADE,
    INDEX idx_gateway_transfer_gateway_id (gateway_id),
    INDEX idx_gateway_transfer_type (transfer_type),
    INDEX idx_gateway_transfer_success (transfer_success),
    INDEX idx_gateway_transfer_created (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='网关文件传输记录表';

-- 插入示例数据（可选）
INSERT IGNORE INTO gateway_servers (name, host, username, password, upload_path, description, is_default) VALUES
('测试网关1', '*************', 'testuser', 'changeme123', '/tmp/uploads', '测试用网关服务器', TRUE),
('测试网关2', '*************', 'admin', 'changeme123', '/data/uploads', '备用网关服务器', FALSE);

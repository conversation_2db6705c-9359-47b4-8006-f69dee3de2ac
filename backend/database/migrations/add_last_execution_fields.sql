-- 添加最近执行记录相关字段
-- 执行时间：2025-08-07

ALTER TABLE test_cases_management 
ADD COLUMN last_execution_id VARCHAR(36) NULL COMMENT '最后执行记录ID' AFTER last_execution_result,
ADD COLUMN last_execution_config_id INT NULL COMMENT '最后执行使用的数据库配置ID' AFTER last_execution_id,
ADD COLUMN last_execution_capture_files JSON NULL COMMENT '最后执行的抓包文件列表' AFTER last_execution_config_id,
ADD COLUMN last_execution_duration INT DEFAULT 0 COMMENT '最后执行耗时(秒)' AFTER last_execution_capture_files;

-- 添加外键约束（如果需要的话）
-- ALTER TABLE test_cases_management 
-- ADD CONSTRAINT fk_last_execution_config 
-- FOREIGN KEY (last_execution_config_id) REFERENCES database_configs(id) ON DELETE SET NULL;

-- 添加GaussDB数据库类型支持
-- 执行时间: 2025-08-01

USE ai_sql_pcap;

-- 更新database_type字段的注释，包含GaussDB
ALTER TABLE database_configs 
MODIFY COLUMN database_type VARCHAR(50) NOT NULL DEFAULT 'mysql' COMMENT '数据库类型(mysql/postgresql/mongodb/oracle/gaussdb)';

-- 验证修改
DESCRIBE database_configs;

-- 显示当前支持的数据库类型
SELECT DISTINCT database_type, COUNT(*) as count 
FROM database_configs 
WHERE is_active = TRUE 
GROUP BY database_type 
ORDER BY database_type;

-- 添加抓包相关字段到database_configs表
-- 如果字段不存在则添加

-- 检查并添加last_capture_interface字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'database_configs' 
     AND column_name = 'last_capture_interface' 
     AND table_schema = DATABASE()) = 0,
    'ALTER TABLE database_configs ADD COLUMN last_capture_interface VARCHAR(100) COMMENT "最后一次成功抓包的网络接口"',
    'SELECT "Column last_capture_interface already exists" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加last_capture_filter字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'database_configs' 
     AND column_name = 'last_capture_filter' 
     AND table_schema = DATABASE()) = 0,
    'ALTER TABLE database_configs ADD COLUMN last_capture_filter TEXT COMMENT "最后一次成功抓包的过滤表达式"',
    'SELECT "Column last_capture_filter already exists" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加last_capture_success_time字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'database_configs' 
     AND column_name = 'last_capture_success_time' 
     AND table_schema = DATABASE()) = 0,
    'ALTER TABLE database_configs ADD COLUMN last_capture_success_time TIMESTAMP NULL COMMENT "最后一次成功抓包时间"',
    'SELECT "Column last_capture_success_time already exists" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加capture_retry_count字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'database_configs' 
     AND column_name = 'capture_retry_count' 
     AND table_schema = DATABASE()) = 0,
    'ALTER TABLE database_configs ADD COLUMN capture_retry_count INT DEFAULT 0 COMMENT "抓包重试次数"',
    'SELECT "Column capture_retry_count already exists" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加索引
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE table_name = 'database_configs' 
     AND index_name = 'idx_last_capture_interface' 
     AND table_schema = DATABASE()) = 0,
    'ALTER TABLE database_configs ADD INDEX idx_last_capture_interface (last_capture_interface)',
    'SELECT "Index idx_last_capture_interface already exists" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- AI SQL PCAP 系统数据库表结构
-- 数据库: ai_sql_pcap (**************:3306)

-- 1. 数据库配置表
CREATE TABLE IF NOT EXISTS database_configs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE COMMENT '配置名称',
    host VARCHAR(255) NOT NULL COMMENT '主机地址',
    port INT NOT NULL DEFAULT 3306 COMMENT '端口号',
    user VARCHAR(100) NOT NULL COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码',
    database_name VARCHAR(100) NOT NULL COMMENT '数据库名',
    database_type VARCHAR(20) NOT NULL DEFAULT 'mysql' COMMENT '数据库类型(mysql/postgresql/mongodb/oracle/gaussdb)',
    database_version VARCHAR(50) COMMENT '数据库版本',
    server_config_id INT NULL COMMENT '关联的服务器配置ID',
    description TEXT COMMENT '描述',
    is_default BOOLEAN DEFAULT FALSE COMMENT '是否为默认配置',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    last_capture_interface VARCHAR(100) COMMENT '最后一次成功抓包的网络接口',
    last_capture_filter TEXT COMMENT '最后一次成功抓包的过滤表达式',
    last_capture_success_time TIMESTAMP NULL COMMENT '最后一次成功抓包时间',
    capture_retry_count INT DEFAULT 0 COMMENT '抓包重试次数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (server_config_id) REFERENCES server_configs(id) ON DELETE SET NULL,
    INDEX idx_name (name),
    INDEX idx_host_port (host, port),
    INDEX idx_server_config_id (server_config_id),
    INDEX idx_is_default (is_default),
    INDEX idx_is_active (is_active),
    INDEX idx_last_capture_interface (last_capture_interface)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据库配置表';

-- 2. 数据库表信息缓存表
CREATE TABLE IF NOT EXISTS database_tables (
    id INT AUTO_INCREMENT PRIMARY KEY,
    config_id INT NOT NULL COMMENT '数据库配置ID',
    database_name VARCHAR(100) NOT NULL COMMENT '数据库名',
    table_name VARCHAR(100) NOT NULL COMMENT '表名',
    table_type VARCHAR(50) DEFAULT 'BASE TABLE' COMMENT '表类型',
    table_comment TEXT COMMENT '表注释',
    table_rows BIGINT DEFAULT 0 COMMENT '表行数',
    data_length BIGINT DEFAULT 0 COMMENT '数据大小',
    index_length BIGINT DEFAULT 0 COMMENT '索引大小',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (config_id) REFERENCES database_configs(id) ON DELETE CASCADE,
    UNIQUE KEY uk_config_db_table (config_id, database_name, table_name),
    INDEX idx_config_id (config_id),
    INDEX idx_database_name (database_name),
    INDEX idx_table_name (table_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据库表信息缓存表';

-- 3. 数据库连接测试记录表
CREATE TABLE IF NOT EXISTS connection_tests (
    id INT AUTO_INCREMENT PRIMARY KEY,
    config_id INT NOT NULL COMMENT '数据库配置ID',
    test_result BOOLEAN NOT NULL COMMENT '测试结果',
    response_time INT DEFAULT 0 COMMENT '响应时间(毫秒)',
    error_message TEXT COMMENT '错误信息',
    databases_count INT DEFAULT 0 COMMENT '数据库数量',
    tables_count INT DEFAULT 0 COMMENT '表数量',
    tested_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '测试时间',
    FOREIGN KEY (config_id) REFERENCES database_configs(id) ON DELETE CASCADE,
    INDEX idx_config_id (config_id),
    INDEX idx_test_result (test_result),
    INDEX idx_tested_at (tested_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据库连接测试记录表';

-- 4. 测试用例表
CREATE TABLE IF NOT EXISTS test_cases (
    id VARCHAR(36) PRIMARY KEY COMMENT '测试用例UUID',
    name VARCHAR(255) NOT NULL COMMENT '测试用例名称',
    protocol ENUM('mysql', 'postgres', 'mongo') NOT NULL COMMENT '协议类型',
    category ENUM('normal', 'error', 'boundary', 'performance') NOT NULL COMMENT '用例分类',
    description TEXT COMMENT '测试用例描述',
    test_case JSON NOT NULL COMMENT '测试用例详细配置',
    tags JSON COMMENT '标签数组',
    source_document VARCHAR(500) COMMENT '来源文档',
    execution_count INT DEFAULT 0 COMMENT '执行次数',
    success_count INT DEFAULT 0 COMMENT '成功次数',
    success_rate DECIMAL(5,2) DEFAULT 0.0 COMMENT '成功率',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_protocol (protocol),
    INDEX idx_category (category),
    INDEX idx_success_rate (success_rate),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='测试用例表';

-- 5. 测试用例管理表
CREATE TABLE IF NOT EXISTS test_cases_management (
    id VARCHAR(36) PRIMARY KEY COMMENT '测试用例UUID',
    title VARCHAR(255) NOT NULL COMMENT '测试用例标题',
    module VARCHAR(100) NOT NULL COMMENT '所属模块',
    priority ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium' COMMENT '优先级',
    status ENUM('draft', 'active', 'completed', 'failed', 'deprecated', 'archived') DEFAULT 'draft' COMMENT '状态',
    preconditions TEXT COMMENT '前置条件',
    test_steps JSON NOT NULL COMMENT '测试步骤数组，每个步骤包含step_number, action, expected_result',
    expected_result TEXT COMMENT '整体预期结果',
    test_data JSON COMMENT '测试数据',
    tags JSON COMMENT '标签数组',
    author VARCHAR(100) COMMENT '创建者',
    reviewer VARCHAR(100) COMMENT '评审者',
    review_status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending' COMMENT '评审状态',
    review_comments TEXT COMMENT '评审意见',
    execution_count INT DEFAULT 0 COMMENT '执行次数',
    success_count INT DEFAULT 0 COMMENT '成功次数',
    failure_count INT DEFAULT 0 COMMENT '失败次数',
    last_execution_time TIMESTAMP NULL COMMENT '最后执行时间',
    last_execution_result ENUM('pass', 'fail', 'blocked', 'skip') NULL COMMENT '最后执行结果',
    last_execution_id VARCHAR(36) NULL COMMENT '最后执行记录ID',
    last_execution_config_id INT NULL COMMENT '最后执行使用的数据库配置ID',
    last_execution_capture_files JSON NULL COMMENT '最后执行的抓包文件列表',
    last_execution_duration INT DEFAULT 0 COMMENT '最后执行耗时(秒)',
    estimated_time INT DEFAULT 0 COMMENT '预估执行时间(分钟)',
    actual_time INT DEFAULT 0 COMMENT '实际执行时间(分钟)',
    automation_level ENUM('manual', 'semi_auto', 'auto') DEFAULT 'manual' COMMENT '自动化程度',
    test_environment VARCHAR(100) COMMENT '测试环境',
    database_type ENUM('mysql', 'postgresql', 'mongodb', 'oracle', 'gaussdb') DEFAULT 'mysql' COMMENT '数据库类型',
    database_version VARCHAR(50) COMMENT '数据库版本',
    operation_type VARCHAR(50) COMMENT '操作类型',
    target_database_config_id INT COMMENT '目标数据库配置ID',
    sql_statements JSON COMMENT 'SQL语句列表，用于自动化执行',
    expected_packet_patterns JSON COMMENT '预期的数据包模式',
    packet_validation_rules JSON COMMENT '数据包验证规则',
    capture_duration INT DEFAULT 30 COMMENT '抓包持续时间(秒)',
    auto_execute BOOLEAN DEFAULT FALSE COMMENT '是否支持自动执行',
    related_requirements TEXT COMMENT '关联需求',
    related_defects TEXT COMMENT '关联缺陷',
    notes TEXT COMMENT '备注',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_module (module),
    INDEX idx_priority (priority),
    INDEX idx_status (status),
    INDEX idx_author (author),
    INDEX idx_review_status (review_status),
    INDEX idx_last_execution_result (last_execution_result),
    INDEX idx_automation_level (automation_level),
    INDEX idx_database_type (database_type),
    INDEX idx_target_database_config_id (target_database_config_id),
    INDEX idx_auto_execute (auto_execute),
    INDEX idx_created_at (created_at),
    INDEX idx_updated_at (updated_at),
    FULLTEXT idx_title_content (title, preconditions, expected_result, notes),
    FOREIGN KEY (target_database_config_id) REFERENCES database_configs(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='测试用例管理表';

-- 6. 测试用例执行记录表
CREATE TABLE IF NOT EXISTS test_case_executions (
    id VARCHAR(36) PRIMARY KEY COMMENT '执行记录UUID',
    test_case_id VARCHAR(36) NOT NULL COMMENT '测试用例ID',
    execution_result ENUM('pass', 'fail', 'blocked', 'skip') NOT NULL COMMENT '执行结果',
    execution_time INT DEFAULT 0 COMMENT '执行时间(分钟)',
    executor VARCHAR(100) COMMENT '执行者',
    execution_environment VARCHAR(100) COMMENT '执行环境',
    database_config_id INT COMMENT '使用的数据库配置ID',
    capture_file_path VARCHAR(500) COMMENT '抓包文件路径',
    capture_file_size BIGINT DEFAULT 0 COMMENT '抓包文件大小',
    packets_captured INT DEFAULT 0 COMMENT '捕获的数据包数量',
    sql_queries_executed JSON COMMENT '执行的SQL查询列表',
    expected_packets JSON COMMENT '预期的数据包特征',
    actual_packets JSON COMMENT '实际捕获的数据包分析结果',
    packet_analysis_result JSON COMMENT '数据包分析结果',
    defects_found JSON COMMENT '发现的缺陷列表',
    execution_notes TEXT COMMENT '执行备注',
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '开始执行时间',
    completed_at TIMESTAMP NULL COMMENT '完成执行时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (test_case_id) REFERENCES test_cases_management(id) ON DELETE CASCADE,
    FOREIGN KEY (database_config_id) REFERENCES database_configs(id) ON DELETE SET NULL,
    INDEX idx_test_case_id (test_case_id),
    INDEX idx_execution_result (execution_result),
    INDEX idx_executor (executor),
    INDEX idx_started_at (started_at),
    INDEX idx_completed_at (completed_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='测试用例执行记录表';

-- 7. 测试执行记录表
CREATE TABLE IF NOT EXISTS test_executions (
    id VARCHAR(36) PRIMARY KEY COMMENT '执行记录UUID',
    test_case_id VARCHAR(36) NOT NULL COMMENT '测试用例ID',
    server_config_id INT COMMENT '服务器配置ID',
    database_config_id INT COMMENT '数据库配置ID',
    status ENUM('pending', 'running', 'completed', 'failed', 'cancelled') NOT NULL DEFAULT 'pending' COMMENT '执行状态',
    start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
    end_time TIMESTAMP NULL COMMENT '结束时间',
    duration_ms INT DEFAULT 0 COMMENT '执行时长(毫秒)',
    result JSON COMMENT '执行结果',
    packet_file VARCHAR(500) COMMENT '抓包文件路径',
    error_message TEXT COMMENT '错误信息',
    execution_log TEXT COMMENT '执行日志',
    validation_result JSON COMMENT '验证结果',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (test_case_id) REFERENCES test_cases(id) ON DELETE CASCADE,
    FOREIGN KEY (server_config_id) REFERENCES server_configs(id) ON DELETE SET NULL,
    FOREIGN KEY (database_config_id) REFERENCES database_configs(id) ON DELETE SET NULL,
    INDEX idx_test_case_id (test_case_id),
    INDEX idx_status (status),
    INDEX idx_start_time (start_time),
    INDEX idx_duration (duration_ms)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='测试执行记录表';

-- 6. 批量执行记录表
CREATE TABLE IF NOT EXISTS batch_executions (
    id VARCHAR(36) PRIMARY KEY COMMENT '批量执行UUID',
    name VARCHAR(255) NOT NULL COMMENT '批量执行名称',
    test_case_ids JSON NOT NULL COMMENT '测试用例ID数组',
    server_config_id INT COMMENT '服务器配置ID',
    database_config_id INT COMMENT '数据库配置ID',
    execution_options JSON COMMENT '执行选项',
    status ENUM('pending', 'running', 'completed', 'failed', 'cancelled') NOT NULL DEFAULT 'pending' COMMENT '执行状态',
    total_cases INT DEFAULT 0 COMMENT '总用例数',
    completed_cases INT DEFAULT 0 COMMENT '已完成用例数',
    success_cases INT DEFAULT 0 COMMENT '成功用例数',
    failed_cases INT DEFAULT 0 COMMENT '失败用例数',
    start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
    end_time TIMESTAMP NULL COMMENT '结束时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (server_config_id) REFERENCES server_configs(id) ON DELETE SET NULL,
    FOREIGN KEY (database_config_id) REFERENCES database_configs(id) ON DELETE SET NULL,
    INDEX idx_status (status),
    INDEX idx_start_time (start_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='批量执行记录表';

-- 4. 查询执行记录表
CREATE TABLE IF NOT EXISTS query_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    config_id INT COMMENT '数据库配置ID',
    query_text TEXT NOT NULL COMMENT '查询语句',
    query_type VARCHAR(50) COMMENT '查询类型(SQL/PROTOCOL)',
    natural_language TEXT COMMENT '自然语言输入',
    execution_time INT DEFAULT 0 COMMENT '执行时间(毫秒)',
    result_rows INT DEFAULT 0 COMMENT '结果行数',
    packet_file VARCHAR(255) COMMENT '抓包文件路径',
    success BOOLEAN DEFAULT TRUE COMMENT '执行是否成功',
    error_message TEXT COMMENT '错误信息',
    user_ip VARCHAR(45) COMMENT '用户IP',
    executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '执行时间',
    FOREIGN KEY (config_id) REFERENCES database_configs(id) ON DELETE SET NULL,
    INDEX idx_config_id (config_id),
    INDEX idx_query_type (query_type),
    INDEX idx_success (success),
    INDEX idx_executed_at (executed_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='查询执行记录表';

-- 5. 抓包文件记录表
CREATE TABLE IF NOT EXISTS packet_files (
    id INT AUTO_INCREMENT PRIMARY KEY,
    query_log_id INT COMMENT '查询记录ID',
    filename VARCHAR(255) NOT NULL COMMENT '文件名',
    filepath VARCHAR(500) NOT NULL COMMENT '文件路径',
    file_size BIGINT DEFAULT 0 COMMENT '文件大小',
    total_packets INT DEFAULT 0 COMMENT '总数据包数',
    mysql_packets INT DEFAULT 0 COMMENT 'MySQL数据包数',
    connections_count INT DEFAULT 0 COMMENT '连接数',
    queries_count INT DEFAULT 0 COMMENT '查询数',
    analysis_data JSON COMMENT '分析结果JSON',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (query_log_id) REFERENCES query_logs(id) ON DELETE SET NULL,
    INDEX idx_query_log_id (query_log_id),
    INDEX idx_filename (filename),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='抓包文件记录表';

-- 7. 服务器Docker镜像表
CREATE TABLE IF NOT EXISTS server_docker_images (
    id INT AUTO_INCREMENT PRIMARY KEY,
    server_config_id INT NOT NULL COMMENT '服务器配置ID',
    image_id VARCHAR(255) NOT NULL COMMENT 'Docker镜像ID',
    repository VARCHAR(255) NOT NULL COMMENT '镜像仓库名',
    tag VARCHAR(100) NOT NULL DEFAULT 'latest' COMMENT '镜像标签',
    size BIGINT DEFAULT 0 COMMENT '镜像大小(字节)',
    created_time TIMESTAMP NULL COMMENT '镜像创建时间',
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    is_available BOOLEAN DEFAULT TRUE COMMENT '镜像是否可用',
    image_info JSON COMMENT '镜像详细信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
    FOREIGN KEY (server_config_id) REFERENCES server_configs(id) ON DELETE CASCADE,
    UNIQUE KEY uk_server_image (server_config_id, image_id),
    INDEX idx_server_config_id (server_config_id),
    INDEX idx_repository (repository),
    INDEX idx_tag (tag),
    INDEX idx_is_available (is_available),
    INDEX idx_created_time (created_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='服务器Docker镜像表';

-- 8. 执行日志表
CREATE TABLE IF NOT EXISTS execution_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    task_id VARCHAR(255) COMMENT '任务ID',
    test_case_id VARCHAR(255) COMMENT '测试用例ID',
    execution_id VARCHAR(255) COMMENT '执行ID',
    database_config_id INT COMMENT '数据库配置ID',
    level ENUM('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL') NOT NULL COMMENT '日志级别',
    category ENUM('CAPTURE', 'SQL_EXECUTION', 'DATABASE_CONNECTION', 'FILE_OPERATION', 'SYSTEM', 'TEST_EXECUTION', 'ERROR_HANDLING') NOT NULL COMMENT '日志分类',
    message TEXT NOT NULL COMMENT '日志消息',
    module_name VARCHAR(255) COMMENT '模块名称',
    function_name VARCHAR(255) COMMENT '函数名称',
    line_number INT COMMENT '行号',
    sql_query TEXT COMMENT '相关SQL查询',
    capture_file VARCHAR(500) COMMENT '相关抓包文件',
    error_details TEXT COMMENT '错误详情',
    stack_trace TEXT COMMENT '堆栈跟踪',
    database_type VARCHAR(50) COMMENT '数据库类型',
    target_host VARCHAR(255) COMMENT '目标主机',
    target_port INT COMMENT '目标端口',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (database_config_id) REFERENCES database_configs(id) ON DELETE SET NULL,
    INDEX idx_execution_logs_task_id (task_id),
    INDEX idx_execution_logs_test_case_id (test_case_id),
    INDEX idx_execution_logs_execution_id (execution_id),
    INDEX idx_execution_logs_task_time (task_id, created_time),
    INDEX idx_execution_logs_level_category (level, category),
    INDEX idx_execution_logs_execution_time (execution_id, created_time),
    INDEX idx_execution_logs_created_time (created_time),
    INDEX idx_execution_logs_level (level),
    INDEX idx_execution_logs_category (category)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='执行日志表';

-- 9. 异步任务执行记录表
CREATE TABLE IF NOT EXISTS async_task_execution_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    task_id VARCHAR(36) NOT NULL COMMENT '任务ID（UUID）',
    task_type ENUM(
        'mysql_capture', 'postgres_capture', 'mongo_capture', 'oracle_capture', 'gaussdb_capture',
        'ai_mysql_capture', 'ai_postgres_capture', 'ai_mongo_capture', 'ai_oracle_capture', 'ai_gaussdb_capture',
        'docker_build', 'docker_image_build', 'ai_test_case_generation',
        'batch_test_case_execution', 'single_test_case_execution'
    ) NOT NULL COMMENT '任务类型',
    task_name VARCHAR(255) COMMENT '任务名称',
    task_description TEXT COMMENT '任务描述',

    -- 任务状态信息
    status ENUM('PENDING', 'RUNNING', 'COMPLETED', 'FAILED', 'CANCELLED', 'TIMEOUT') NOT NULL DEFAULT 'PENDING' COMMENT '任务状态',
    progress INT DEFAULT 0 COMMENT '执行进度(0-100)',
    current_step VARCHAR(255) COMMENT '当前执行步骤',

    -- 执行器信息
    executor_type ENUM('PYTHON', 'C', 'JAVA') NOT NULL DEFAULT 'PYTHON' COMMENT '执行器类型',
    executor_version VARCHAR(50) COMMENT '执行器版本',
    executor_path VARCHAR(500) COMMENT '执行器路径',
    executor_selection_reason TEXT COMMENT '执行器选择原因',
    fallback_from_executor ENUM('PYTHON', 'C', 'JAVA') COMMENT '从哪个执行器回退而来',

    -- 配置信息
    database_config_id INT COMMENT '数据库配置ID',
    server_config_id INT COMMENT '服务器配置ID',
    capture_enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用抓包',
    capture_duration INT COMMENT '抓包持续时间(秒)',

    -- 输入参数
    input_sql_query TEXT COMMENT '输入SQL查询',
    input_mongo_query TEXT COMMENT '输入MongoDB查询',
    input_natural_query TEXT COMMENT '输入自然语言查询',
    input_parameters JSON COMMENT '其他输入参数',

    -- 执行结果
    execution_result JSON COMMENT '执行结果详情',
    output_files JSON COMMENT '输出文件列表',
    capture_files JSON COMMENT '抓包文件列表',
    generated_sql TEXT COMMENT 'AI生成的SQL语句',

    -- 性能指标
    start_time TIMESTAMP NULL COMMENT '开始时间',
    end_time TIMESTAMP NULL COMMENT '结束时间',
    duration_seconds DECIMAL(10,3) COMMENT '执行时长(秒)',
    cpu_usage_percent DECIMAL(5,2) COMMENT 'CPU使用率',
    memory_usage_mb DECIMAL(10,2) COMMENT '内存使用量(MB)',

    -- 错误信息
    error_code VARCHAR(50) COMMENT '错误代码',
    error_message TEXT COMMENT '错误消息',
    error_details JSON COMMENT '详细错误信息',
    error_stack_trace TEXT COMMENT '错误堆栈跟踪',
    retry_count INT DEFAULT 0 COMMENT '重试次数',
    max_retries INT DEFAULT 0 COMMENT '最大重试次数',

    -- 环境信息
    worker_id VARCHAR(100) COMMENT '工作器ID',
    worker_host VARCHAR(255) COMMENT '工作器主机',
    python_version VARCHAR(50) COMMENT 'Python版本',
    system_info JSON COMMENT '系统信息',
    environment_variables JSON COMMENT '环境变量',

    -- 依赖任务
    parent_task_id VARCHAR(36) COMMENT '父任务ID',
    child_task_ids JSON COMMENT '子任务ID列表',
    dependency_task_ids JSON COMMENT '依赖任务ID列表',

    -- 批量执行相关
    batch_id VARCHAR(36) COMMENT '批量执行ID',
    batch_name VARCHAR(255) COMMENT '批量执行名称',
    batch_total_count INT COMMENT '批量总数',
    batch_current_index INT COMMENT '批量当前索引',

    -- 测试用例相关
    test_case_id VARCHAR(36) COMMENT '测试用例ID',
    test_case_name VARCHAR(255) COMMENT '测试用例名称',
    test_steps_executed JSON COMMENT '已执行的测试步骤',
    test_validation_results JSON COMMENT '测试验证结果',

    -- 抓包相关
    network_interface VARCHAR(100) COMMENT '网络接口',
    packet_count INT COMMENT '抓包数量',
    packet_size_bytes BIGINT COMMENT '抓包总大小(字节)',
    capture_filter VARCHAR(500) COMMENT '抓包过滤器',

    -- 审计信息
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(100) COMMENT '创建者',

    -- 外键约束
    FOREIGN KEY (database_config_id) REFERENCES database_configs(id) ON DELETE SET NULL,
    FOREIGN KEY (parent_task_id) REFERENCES async_task_execution_logs(task_id) ON DELETE SET NULL,

    -- 索引
    UNIQUE KEY uk_task_id (task_id),
    INDEX idx_task_type (task_type),
    INDEX idx_status (status),
    INDEX idx_executor_type (executor_type),
    INDEX idx_database_config_id (database_config_id),
    INDEX idx_server_config_id (server_config_id),
    INDEX idx_batch_id (batch_id),
    INDEX idx_test_case_id (test_case_id),
    INDEX idx_parent_task_id (parent_task_id),
    INDEX idx_start_time (start_time),
    INDEX idx_end_time (end_time),
    INDEX idx_duration (duration_seconds),
    INDEX idx_created_at (created_at),
    INDEX idx_status_type (status, task_type),
    INDEX idx_executor_status (executor_type, status),
    INDEX idx_batch_status (batch_id, status),
    INDEX idx_error_code (error_code),
    INDEX idx_retry_count (retry_count)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='异步任务执行记录表';

-- 插入默认配置数据
INSERT INTO database_configs (name, host, port, user, password, database_name, description, is_default)
VALUES
('local_test', '**************', 3308, 'root', 'QZ@1005#1005', 'mysql', '本地测试数据库', TRUE),
('remote_test', '**************', 3307, 'root', 'QZ@1005#1005', 'test', '远程测试数据库', FALSE)
ON DUPLICATE KEY UPDATE
    host = VALUES(host),
    port = VALUES(port),
    user = VALUES(user),
    password = VALUES(password),
    database_name = VALUES(database_name),
    description = VALUES(description),
    updated_at = CURRENT_TIMESTAMP;

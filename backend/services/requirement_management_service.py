"""
协议需求管理服务
"""
import logging
import json
import uuid
from typing import List, Optional, Dict, Any
from datetime import datetime
import asyncio
from models.requirement_management import (
    ProtocolRequirementCreate,
    ProtocolRequirementUpdate,
    ProtocolRequirementResponse,
    ProtocolRequirementQuery,
    ProtocolRequirementListResponse,
    RequirementStatistics,
    RequirementBatchOperation,
    RequirementBatchOperationResponse,
    RequirementTraceability,
    RequirementTestCriteria,
    RequirementStatusEnum,
    RequirementPriorityEnum
)

logger = logging.getLogger(__name__)


class RequirementManagementService:
    """协议需求管理服务"""
    
    def __init__(self):
        """初始化服务"""
        # 模拟数据存储，实际项目中应该使用数据库
        self._requirements_storage = {}
        self._next_id = 1
        logger.info("协议需求管理服务初始化完成")
    
    async def get_requirements(self, query: ProtocolRequirementQuery) -> ProtocolRequirementListResponse:
        """获取需求列表"""
        try:
            # 获取所有需求
            all_requirements = list(self._requirements_storage.values())
            
            # 应用筛选条件
            filtered_requirements = self._apply_filters(all_requirements, query)
            
            # 应用排序
            sorted_requirements = self._apply_sorting(filtered_requirements, query.sort_by, query.sort_order)
            
            # 计算分页
            total = len(sorted_requirements)
            start_index = (query.page - 1) * query.page_size
            end_index = start_index + query.page_size
            page_requirements = sorted_requirements[start_index:end_index]
            
            # 计算总页数
            total_pages = (total + query.page_size - 1) // query.page_size
            
            return ProtocolRequirementListResponse(
                items=page_requirements,
                total=total,
                page=query.page,
                page_size=query.page_size,
                total_pages=total_pages
            )
            
        except Exception as e:
            logger.error(f"获取需求列表失败: {e}")
            raise
    
    def _apply_filters(self, requirements: List[ProtocolRequirementResponse], query: ProtocolRequirementQuery) -> List[ProtocolRequirementResponse]:
        """应用筛选条件"""
        filtered = requirements
        
        if query.keyword:
            keyword_lower = query.keyword.lower()
            filtered = [req for req in filtered 
                       if keyword_lower in req.title.lower() or keyword_lower in req.description.lower()]
        
        if query.requirement_type:
            filtered = [req for req in filtered if req.requirement_type == query.requirement_type]
        
        if query.protocol_type:
            filtered = [req for req in filtered if req.protocol_type == query.protocol_type]
        
        if query.protocol_version:
            filtered = [req for req in filtered if req.protocol_version == query.protocol_version]
        
        if query.priority:
            filtered = [req for req in filtered if req.priority == query.priority]
        
        if query.complexity:
            filtered = [req for req in filtered if req.complexity == query.complexity]
        
        if query.testability:
            filtered = [req for req in filtered if req.testability == query.testability]
        
        if query.status:
            filtered = [req for req in filtered if req.status == query.status]
        
        if query.category:
            filtered = [req for req in filtered if req.category == query.category]
        
        if query.module:
            filtered = [req for req in filtered if req.module == query.module]
        
        if query.author:
            filtered = [req for req in filtered if req.author == query.author]
        
        if query.reviewer:
            filtered = [req for req in filtered if req.reviewer == query.reviewer]
        
        if query.tags:
            filtered = [req for req in filtered 
                       if any(tag in req.tags for tag in query.tags)]
        
        if query.has_dependencies is not None:
            if query.has_dependencies:
                filtered = [req for req in filtered if req.dependencies]
            else:
                filtered = [req for req in filtered if not req.dependencies]
        
        if query.created_start:
            filtered = [req for req in filtered if req.created_at >= query.created_start]
        
        if query.created_end:
            filtered = [req for req in filtered if req.created_at <= query.created_end]
        
        return filtered
    
    def _apply_sorting(self, requirements: List[ProtocolRequirementResponse], sort_by: str, sort_order: str) -> List[ProtocolRequirementResponse]:
        """应用排序"""
        reverse = sort_order.lower() == "desc"
        
        if sort_by == "title":
            return sorted(requirements, key=lambda x: x.title, reverse=reverse)
        elif sort_by == "priority":
            priority_order = {"low": 1, "medium": 2, "high": 3, "critical": 4}
            return sorted(requirements, key=lambda x: priority_order.get(x.priority.value, 0), reverse=reverse)
        elif sort_by == "status":
            return sorted(requirements, key=lambda x: x.status.value, reverse=reverse)
        elif sort_by == "created_at":
            return sorted(requirements, key=lambda x: x.created_at, reverse=reverse)
        elif sort_by == "updated_at":
            return sorted(requirements, key=lambda x: x.updated_at, reverse=reverse)
        else:
            return sorted(requirements, key=lambda x: x.created_at, reverse=reverse)
    
    async def get_requirement_by_id(self, requirement_id: str) -> Optional[ProtocolRequirementResponse]:
        """根据ID获取需求"""
        try:
            return self._requirements_storage.get(requirement_id)
        except Exception as e:
            logger.error(f"获取需求详情失败: {e}")
            raise
    
    async def create_requirement(self, requirement_data: ProtocolRequirementCreate) -> ProtocolRequirementResponse:
        """创建新需求"""
        try:
            requirement_id = str(uuid.uuid4())
            current_time = datetime.now()
            
            # 创建需求响应对象
            requirement = ProtocolRequirementResponse(
                id=requirement_id,
                title=requirement_data.title,
                description=requirement_data.description,
                requirement_type=requirement_data.requirement_type,
                protocol_type=requirement_data.protocol_type,
                protocol_version=requirement_data.protocol_version,
                priority=requirement_data.priority,
                complexity=requirement_data.complexity,
                testability=requirement_data.testability,
                status=RequirementStatusEnum.DRAFT,
                functional_description=requirement_data.functional_description,
                technical_specifications=requirement_data.technical_specifications,
                business_rules=requirement_data.business_rules,
                constraints=requirement_data.constraints,
                assumptions=requirement_data.assumptions,
                test_criteria=requirement_data.test_criteria,
                expected_test_cases=requirement_data.expected_test_cases,
                actual_test_cases=0,
                test_coverage=0.0,
                traceability=requirement_data.traceability,
                dependencies=requirement_data.dependencies,
                tags=requirement_data.tags,
                category=requirement_data.category,
                module=requirement_data.module,
                author=requirement_data.author,
                reviewer=requirement_data.reviewer,
                stakeholders=requirement_data.stakeholders,
                references=requirement_data.references,
                related_documents=requirement_data.related_documents,
                notes=requirement_data.notes,
                created_at=current_time,
                updated_at=current_time,
                version=1
            )
            
            # 保存到存储
            self._requirements_storage[requirement_id] = requirement
            
            logger.info(f"创建需求成功: {requirement_id} - {requirement_data.title}")
            return requirement
            
        except Exception as e:
            logger.error(f"创建需求失败: {e}")
            raise
    
    async def update_requirement(self, requirement_id: str, requirement_data: ProtocolRequirementUpdate) -> Optional[ProtocolRequirementResponse]:
        """更新需求"""
        try:
            existing_requirement = self._requirements_storage.get(requirement_id)
            if not existing_requirement:
                return None
            
            # 更新字段
            update_data = requirement_data.dict(exclude_unset=True)
            for field, value in update_data.items():
                if hasattr(existing_requirement, field):
                    setattr(existing_requirement, field, value)
            
            # 更新时间戳和版本
            existing_requirement.updated_at = datetime.now()
            existing_requirement.version += 1
            
            # 保存更新
            self._requirements_storage[requirement_id] = existing_requirement
            
            logger.info(f"更新需求成功: {requirement_id}")
            return existing_requirement
            
        except Exception as e:
            logger.error(f"更新需求失败: {e}")
            raise
    
    async def delete_requirement(self, requirement_id: str) -> bool:
        """删除需求"""
        try:
            if requirement_id in self._requirements_storage:
                del self._requirements_storage[requirement_id]
                logger.info(f"删除需求成功: {requirement_id}")
                return True
            return False
            
        except Exception as e:
            logger.error(f"删除需求失败: {e}")
            raise
    
    async def get_requirements_statistics(self) -> RequirementStatistics:
        """获取需求统计信息"""
        try:
            all_requirements = list(self._requirements_storage.values())
            total_count = len(all_requirements)
            
            # 状态分布
            status_distribution = {}
            for req in all_requirements:
                status = req.status.value
                status_distribution[status] = status_distribution.get(status, 0) + 1
            
            # 优先级分布
            priority_distribution = {}
            for req in all_requirements:
                priority = req.priority.value
                priority_distribution[priority] = priority_distribution.get(priority, 0) + 1
            
            # 类型分布
            type_distribution = {}
            for req in all_requirements:
                req_type = req.requirement_type.value
                type_distribution[req_type] = type_distribution.get(req_type, 0) + 1
            
            # 协议分布
            protocol_distribution = {}
            for req in all_requirements:
                protocol = req.protocol_type.value
                protocol_distribution[protocol] = protocol_distribution.get(protocol, 0) + 1
            
            # 复杂度分布
            complexity_distribution = {}
            for req in all_requirements:
                complexity = req.complexity.value
                complexity_distribution[complexity] = complexity_distribution.get(complexity, 0) + 1
            
            # 可测试性分布
            testability_distribution = {}
            for req in all_requirements:
                testability = req.testability.value
                testability_distribution[testability] = testability_distribution.get(testability, 0) + 1
            
            # 覆盖率汇总
            total_coverage = sum(req.test_coverage for req in all_requirements)
            average_coverage = total_coverage / total_count if total_count > 0 else 0.0
            
            coverage_summary = {
                "average_coverage": average_coverage,
                "high_coverage_count": len([req for req in all_requirements if req.test_coverage >= 0.8]),
                "medium_coverage_count": len([req for req in all_requirements if 0.5 <= req.test_coverage < 0.8]),
                "low_coverage_count": len([req for req in all_requirements if req.test_coverage < 0.5])
            }
            
            # 最近需求（最近5个）
            recent_requirements = sorted(all_requirements, key=lambda x: x.created_at, reverse=True)[:5]
            
            return RequirementStatistics(
                total_count=total_count,
                status_distribution=status_distribution,
                priority_distribution=priority_distribution,
                type_distribution=type_distribution,
                protocol_distribution=protocol_distribution,
                complexity_distribution=complexity_distribution,
                testability_distribution=testability_distribution,
                coverage_summary=coverage_summary,
                recent_requirements=recent_requirements
            )
            
        except Exception as e:
            logger.error(f"获取需求统计失败: {e}")
            raise
    
    async def batch_operation_requirements(self, operation: RequirementBatchOperation) -> RequirementBatchOperationResponse:
        """批量操作需求"""
        try:
            operation_id = str(uuid.uuid4())
            total_count = len(operation.requirement_ids)
            success_count = 0
            failed_count = 0
            failed_items = []
            
            for requirement_id in operation.requirement_ids:
                try:
                    if operation.operation_type == "update_status":
                        await self._update_requirement_status(requirement_id, operation.operation_data.get("status"))
                    elif operation.operation_type == "update_priority":
                        await self._update_requirement_priority(requirement_id, operation.operation_data.get("priority"))
                    elif operation.operation_type == "add_tags":
                        await self._add_requirement_tags(requirement_id, operation.operation_data.get("tags", []))
                    elif operation.operation_type == "remove_tags":
                        await self._remove_requirement_tags(requirement_id, operation.operation_data.get("tags", []))
                    elif operation.operation_type == "delete":
                        await self.delete_requirement(requirement_id)
                    
                    success_count += 1
                    
                except Exception as e:
                    failed_count += 1
                    failed_items.append({
                        "requirement_id": requirement_id,
                        "error": str(e)
                    })
            
            return RequirementBatchOperationResponse(
                operation_id=operation_id,
                total_count=total_count,
                success_count=success_count,
                failed_count=failed_count,
                failed_items=failed_items,
                message=f"批量操作完成，成功 {success_count} 个，失败 {failed_count} 个"
            )
            
        except Exception as e:
            logger.error(f"批量操作需求失败: {e}")
            raise
    
    async def _update_requirement_status(self, requirement_id: str, status: str):
        """更新需求状态"""
        requirement = self._requirements_storage.get(requirement_id)
        if requirement:
            requirement.status = RequirementStatusEnum(status)
            requirement.updated_at = datetime.now()
    
    async def _update_requirement_priority(self, requirement_id: str, priority: str):
        """更新需求优先级"""
        requirement = self._requirements_storage.get(requirement_id)
        if requirement:
            requirement.priority = RequirementPriorityEnum(priority)
            requirement.updated_at = datetime.now()
    
    async def _add_requirement_tags(self, requirement_id: str, tags: List[str]):
        """添加需求标签"""
        requirement = self._requirements_storage.get(requirement_id)
        if requirement:
            for tag in tags:
                if tag not in requirement.tags:
                    requirement.tags.append(tag)
            requirement.updated_at = datetime.now()
    
    async def _remove_requirement_tags(self, requirement_id: str, tags: List[str]):
        """移除需求标签"""
        requirement = self._requirements_storage.get(requirement_id)
        if requirement:
            for tag in tags:
                if tag in requirement.tags:
                    requirement.tags.remove(tag)
            requirement.updated_at = datetime.now()
    
    async def get_requirement_dependencies(self, requirement_id: str) -> Dict[str, Any]:
        """获取需求依赖关系"""
        try:
            requirement = self._requirements_storage.get(requirement_id)
            if not requirement:
                return {"dependencies": [], "dependents": []}
            
            # 获取直接依赖
            dependencies = requirement.dependencies
            
            # 获取被依赖的需求（反向依赖）
            dependents = []
            for req_id, req in self._requirements_storage.items():
                for dep in req.dependencies:
                    if dep.dependency_id == requirement_id:
                        dependents.append({
                            "requirement_id": req_id,
                            "title": req.title,
                            "dependency_type": dep.dependency_type
                        })
            
            return {
                "requirement_id": requirement_id,
                "title": requirement.title,
                "dependencies": dependencies,
                "dependents": dependents
            }
            
        except Exception as e:
            logger.error(f"获取需求依赖关系失败: {e}")
            raise
    
    async def validate_dependencies(self, requirement_ids: List[str]) -> Dict[str, Any]:
        """验证需求依赖关系的一致性"""
        try:
            issues = []
            
            for requirement_id in requirement_ids:
                requirement = self._requirements_storage.get(requirement_id)
                if not requirement:
                    issues.append({
                        "type": "missing_requirement",
                        "requirement_id": requirement_id,
                        "message": f"需求 {requirement_id} 不存在"
                    })
                    continue
                
                # 检查依赖关系是否存在循环
                visited = set()
                path = []
                if self._has_circular_dependency(requirement_id, visited, path):
                    issues.append({
                        "type": "circular_dependency",
                        "requirement_id": requirement_id,
                        "path": path,
                        "message": f"检测到循环依赖: {' -> '.join(path)}"
                    })
                
                # 检查依赖的需求是否存在
                for dep in requirement.dependencies:
                    if dep.dependency_id not in self._requirements_storage:
                        issues.append({
                            "type": "missing_dependency",
                            "requirement_id": requirement_id,
                            "dependency_id": dep.dependency_id,
                            "message": f"依赖的需求 {dep.dependency_id} 不存在"
                        })
            
            return {
                "valid": len(issues) == 0,
                "issues": issues,
                "message": "依赖关系验证完成" if len(issues) == 0 else f"发现 {len(issues)} 个问题"
            }
            
        except Exception as e:
            logger.error(f"验证需求依赖关系失败: {e}")
            raise
    
    def _has_circular_dependency(self, requirement_id: str, visited: set, path: List[str]) -> bool:
        """检查是否存在循环依赖"""
        if requirement_id in visited:
            return True
        
        visited.add(requirement_id)
        path.append(requirement_id)
        
        requirement = self._requirements_storage.get(requirement_id)
        if requirement:
            for dep in requirement.dependencies:
                if self._has_circular_dependency(dep.dependency_id, visited, path):
                    return True
        
        visited.remove(requirement_id)
        path.pop()
        return False
    
    async def get_requirement_test_cases(self, requirement_id: str) -> Dict[str, Any]:
        """获取需求关联的测试用例"""
        try:
            # 这里应该查询测试用例数据库，获取关联到此需求的测试用例
            # 目前返回模拟数据
            requirement = self._requirements_storage.get(requirement_id)
            if not requirement:
                return {"test_cases": [], "total": 0}
            
            # 模拟测试用例数据
            test_cases = [
                {
                    "id": f"tc_{requirement_id}_001",
                    "title": f"测试用例 - {requirement.title} - 基本功能",
                    "status": "active",
                    "priority": "high",
                    "created_at": datetime.now().isoformat()
                }
            ]
            
            return {
                "requirement_id": requirement_id,
                "requirement_title": requirement.title,
                "test_cases": test_cases,
                "total": len(test_cases)
            }
            
        except Exception as e:
            logger.error(f"获取需求测试用例失败: {e}")
            raise
    
    # 获取选项数据的方法
    async def get_categories(self) -> List[str]:
        """获取需求分类选项"""
        categories = set()
        for req in self._requirements_storage.values():
            if req.category:
                categories.add(req.category)
        return sorted(list(categories))
    
    async def get_modules(self) -> List[str]:
        """获取需求模块选项"""
        modules = set()
        for req in self._requirements_storage.values():
            if req.module:
                modules.add(req.module)
        return sorted(list(modules))
    
    async def get_authors(self) -> List[str]:
        """获取需求创建者选项"""
        authors = set()
        for req in self._requirements_storage.values():
            if req.author:
                authors.add(req.author)
        return sorted(list(authors))
    
    async def get_reviewers(self) -> List[str]:
        """获取需求评审者选项"""
        reviewers = set()
        for req in self._requirements_storage.values():
            if req.reviewer:
                reviewers.add(req.reviewer)
        return sorted(list(reviewers))
    
    async def get_tags(self) -> List[str]:
        """获取需求标签选项"""
        tags = set()
        for req in self._requirements_storage.values():
            tags.update(req.tags)
        return sorted(list(tags))
    
    async def export_requirements(self, format: str, requirement_ids: Optional[List[str]] = None) -> Dict[str, Any]:
        """导出需求"""
        try:
            # 获取要导出的需求
            if requirement_ids:
                requirements = [self._requirements_storage[req_id] for req_id in requirement_ids 
                               if req_id in self._requirements_storage]
            else:
                requirements = list(self._requirements_storage.values())
            
            export_data = []
            for req in requirements:
                export_data.append(req.dict())
            
            # 这里应该根据格式生成实际的文件
            # 目前只返回数据
            return {
                "format": format,
                "count": len(export_data),
                "data": export_data,
                "message": f"成功导出 {len(export_data)} 个需求"
            }
            
        except Exception as e:
            logger.error(f"导出需求失败: {e}")
            raise
    
    async def import_requirements(self, file_path: str, format: str, options: Dict[str, Any]) -> Dict[str, Any]:
        """导入需求"""
        try:
            # 这里应该根据文件格式解析文件内容
            # 目前返回模拟结果
            return {
                "file_path": file_path,
                "format": format,
                "imported_count": 0,
                "skipped_count": 0,
                "errors": [],
                "message": "导入功能开发中"
            }
            
        except Exception as e:
            logger.error(f"导入需求失败: {e}")
            raise


# 创建全局服务实例
requirement_management_service = RequirementManagementService()

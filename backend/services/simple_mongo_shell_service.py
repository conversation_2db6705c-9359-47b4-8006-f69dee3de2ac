import asyncio
import json
import subprocess
import tempfile
import os
import logging
from typing import Dict, Any, List, Optional
from pathlib import Path

logger = logging.getLogger(__name__)

class SimpleMongoShellService:
    """简化版MongoDB Shell服务 - 直接执行mongosh命令"""
    
    def __init__(self, connection_string: str = None):
        """初始化MongoDB Shell服务"""
        self.connection_string = connection_string or "********************************************************************************************"
        self.mongosh_path = self._find_mongosh()
        
    def _find_mongosh(self) -> str:
        """查找mongosh可执行文件路径"""
        possible_paths = [
            "/usr/local/bin/mongosh",
            "/usr/bin/mongosh", 
            "mongosh"
        ]
        
        for path in possible_paths:
            try:
                result = subprocess.run([path, "--version"], 
                                      capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    logger.info(f"Found mongosh at: {path}")
                    return path
            except (subprocess.TimeoutExpired, FileNotFoundError):
                continue
                
        raise Exception("mongosh not found. Please install MongoDB Shell.")
    
    async def execute_mongo_query(self, query: str) -> Dict[str, Any]:
        """执行MongoDB查询"""
        try:
            # 直接使用mongosh执行命令
            output = await self._execute_direct(query)
            
            # 解析结果
            return self._parse_output(output, query)
            
        except Exception as e:
            logger.error(f"Failed to execute MongoDB query: {str(e)}")
            raise Exception(f"MongoDB query execution failed: {str(e)}")
    
    async def _execute_direct(self, query: str) -> str:
        """直接执行mongosh命令"""
        try:
            # 创建临时脚本文件
            with tempfile.NamedTemporaryFile(mode='w', suffix='.js', delete=False) as temp_file:
                # 直接写入查询，不包装
                temp_file.write(query)
                temp_file_path = temp_file.name
            
            # 执行mongosh命令
            cmd = [
                self.mongosh_path,
                self.connection_string,
                "--quiet",
                "--file", temp_file_path
            ]
            
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            # 清理临时文件
            try:
                os.unlink(temp_file_path)
            except:
                pass
            
            if process.returncode != 0:
                error_msg = stderr.decode() if stderr else "Unknown error"
                # 如果错误信息包含结果，尝试解析
                stdout_text = stdout.decode().strip()
                if stdout_text:
                    return stdout_text
                raise Exception(f"mongosh execution failed: {error_msg}")
            
            return stdout.decode().strip()
            
        except Exception as e:
            logger.error(f"Failed to execute with mongosh: {str(e)}")
            raise
    
    def _parse_output(self, output: str, original_query: str) -> Dict[str, Any]:
        """解析mongosh输出"""
        try:
            if not output.strip():
                return {
                    'type': 'command',
                    'operation': self._extract_operation(original_query),
                    'acknowledged': True
                }
            
            lines = output.strip().split('\n')
            
            # 处理不同类型的输出
            if 'insertedId' in output:
                # 插入操作
                return self._parse_insert_result(output, original_query)
            elif 'acknowledged' in output:
                # 其他修改操作
                return self._parse_modification_result(output, original_query)
            elif original_query.lower().startswith('show databases'):
                # show databases
                return self._parse_show_databases(lines)
            elif original_query.lower().startswith('show collections'):
                # show collections
                return self._parse_show_collections(lines)
            elif 'find' in original_query.lower():
                # 查询结果
                return self._parse_find_result(output, original_query)
            else:
                # 其他命令
                return {
                    'type': 'command',
                    'operation': self._extract_operation(original_query),
                    'acknowledged': True,
                    'raw_output': output
                }
                
        except Exception as e:
            logger.error(f"Failed to parse output: {str(e)}, output: {output}")
            return {
                'type': 'command',
                'operation': self._extract_operation(original_query),
                'acknowledged': True,
                'raw_output': output
            }
    
    def _parse_insert_result(self, output: str, query: str) -> Dict[str, Any]:
        """解析插入操作结果"""
        import re
        
        # 查找insertedId
        id_match = re.search(r'insertedId:\s*ObjectId\(["\']([^"\']+)["\']\)', output)
        inserted_id = id_match.group(1) if id_match else None
        
        # 查找insertedIds（批量插入）
        ids_matches = re.findall(r'ObjectId\(["\']([^"\']+)["\']\)', output)
        
        return {
            'type': 'modification',
            'operation': 'insertOne' if 'insertOne' in query else 'insertMany',
            'acknowledged': True,
            'insertedId': inserted_id,
            'insertedIds': ids_matches if len(ids_matches) > 1 else None
        }
    
    def _parse_modification_result(self, output: str, query: str) -> Dict[str, Any]:
        """解析修改操作结果"""
        import re
        
        # 提取数字字段
        acknowledged = 'acknowledged: true' in output.lower()
        matched_count = self._extract_number(output, r'matchedCount:\s*(\d+)')
        modified_count = self._extract_number(output, r'modifiedCount:\s*(\d+)')
        deleted_count = self._extract_number(output, r'deletedCount:\s*(\d+)')
        
        return {
            'type': 'modification',
            'operation': self._extract_operation(query),
            'acknowledged': acknowledged,
            'matchedCount': matched_count,
            'modifiedCount': modified_count,
            'deletedCount': deleted_count
        }
    
    def _parse_show_databases(self, lines: List[str]) -> Dict[str, Any]:
        """解析show databases结果"""
        databases = []
        for line in lines:
            if line.strip() and not line.startswith('admin') and not line.startswith('config'):
                parts = line.split()
                if len(parts) >= 2:
                    databases.append({
                        'name': parts[0],
                        'sizeOnDisk': parts[1]
                    })
        
        return {
            'type': 'command',
            'operation': 'listDatabases',
            'databases': databases
        }
    
    def _parse_show_collections(self, lines: List[str]) -> Dict[str, Any]:
        """解析show collections结果"""
        collections = [line.strip() for line in lines if line.strip()]
        
        return {
            'type': 'command',
            'operation': 'listCollections',
            'collections': collections
        }
    
    def _parse_find_result(self, output: str, query: str) -> Dict[str, Any]:
        """解析查询结果"""
        import re
        
        # 尝试提取JSON对象
        json_objects = []
        
        # 查找所有可能的MongoDB文档格式
        doc_pattern = r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}'
        matches = re.findall(doc_pattern, output)
        
        for match in matches:
            try:
                # 简单处理ObjectId格式
                cleaned = re.sub(r'ObjectId\(["\']([^"\']+)["\']\)', r'{"$oid":"\1"}', match)
                # 处理ISODate格式
                cleaned = re.sub(r'ISODate\(["\']([^"\']+)["\']\)', r'{"$date":"\1"}', cleaned)
                
                # 如果解析成功就是有效文档
                json_objects.append(cleaned)
            except:
                continue
        
        return {
            'type': 'query',
            'operation': 'find',
            'documents': json_objects,
            'count': len(json_objects)
        }
    
    def _extract_number(self, output: str, pattern: str) -> int:
        """从输出中提取数字"""
        import re
        match = re.search(pattern, output)
        return int(match.group(1)) if match else 0
    
    def _extract_operation(self, query: str) -> str:
        """从查询中提取操作类型"""
        query = query.lower().strip()
        
        if 'insertone' in query:
            return 'insertOne'
        elif 'insertmany' in query:
            return 'insertMany'
        elif 'updateone' in query:
            return 'updateOne'
        elif 'updatemany' in query:
            return 'updateMany'
        elif 'deleteone' in query:
            return 'deleteOne'
        elif 'deletemany' in query:
            return 'deleteMany'
        elif 'find(' in query:
            return 'find'
        elif 'count' in query:
            return 'count'
        elif 'show databases' in query:
            return 'listDatabases'
        elif 'show collections' in query:
            return 'listCollections'
        elif 'for(' in query:
            return 'for'
        elif 'var ' in query or 'let ' in query or 'const ' in query:
            return 'variable'
        else:
            return 'command'
    
    async def test_connection(self) -> bool:
        """测试连接"""
        try:
            result = await self.execute_mongo_query("db.runCommand({ping: 1})")
            return 'acknowledged' in str(result) or 'ok' in str(result)
        except:
            return False
    
    async def close(self):
        """关闭连接"""
        pass

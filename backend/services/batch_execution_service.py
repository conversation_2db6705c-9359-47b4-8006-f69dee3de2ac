#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
批量执行数据库服务
处理批量执行记录的数据库操作，包括创建、更新、查询等功能
"""

import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from urllib.parse import quote_plus
from utils.config import Config

logger = logging.getLogger(__name__)

class BatchExecutionService:
    """批量执行数据库服务"""
    
    def __init__(self):
        self.engine = None
        self.SessionLocal = None
        self._init_database()
    
    def _init_database(self):
        """初始化数据库连接"""
        try:
            # 使用配置中的数据库连接信息
            db_config = Config.get_mysql_config()
            # URL-encode the password to handle special characters
            encoded_password = quote_plus(db_config['password'])
            database_url = (
                f"mysql+pymysql://{db_config['user']}:{encoded_password}@"
                f"{db_config['host']}:{db_config['port']}/{db_config['database']}"
            )
            self.engine = create_engine(database_url, echo=False)
            self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
            logger.info("批量执行服务数据库连接初始化成功")
        except Exception as e:
            logger.error(f"批量执行服务数据库连接初始化失败: {e}")
    
    def create_batch_execution(self, batch_data: Dict[str, Any]) -> bool:
        """创建批量执行记录"""
        try:
            if not self.engine:
                logger.warning("数据库连接未初始化，跳过数据库存储")
                return False
                
            with self.engine.connect() as conn:
                # 准备插入数据
                insert_data = {
                    'id': batch_data.get('batch_id'),
                    'name': batch_data.get('name', ''),
                    'test_case_ids': json.dumps([item.get('test_case_id') for item in batch_data.get('test_case_items', [])]),
                    'server_config_id': batch_data.get('server_config_id'),
                    'database_config_id': batch_data.get('config_id'),
                    'execution_options': json.dumps({
                        'database_type': batch_data.get('database_type'),
                        'database_version': batch_data.get('database_version'),
                        'capture_enabled': batch_data.get('capture_enabled', True),
                        'timeout_per_case': batch_data.get('timeout_per_case', 300),
                        'stop_on_failure': batch_data.get('stop_on_failure', False)
                    }),
                    'status': str(batch_data.get('status', 'pending')).replace('BatchExecutionStatusEnum.', '').lower(),
                    'total_cases': batch_data.get('total_cases', 0),
                    'completed_cases': batch_data.get('completed_cases', 0),
                    'success_cases': batch_data.get('success_cases', 0),
                    'failed_cases': batch_data.get('failed_cases', 0),
                    'start_time': batch_data.get('start_time'),
                    'end_time': batch_data.get('end_time'),
                    'created_at': batch_data.get('created_at') or datetime.now().isoformat()
                }
                
                # 执行插入
                sql = text("""
                    INSERT INTO batch_executions 
                    (id, name, test_case_ids, server_config_id, database_config_id, execution_options, 
                     status, total_cases, completed_cases, success_cases, failed_cases, 
                     start_time, end_time, created_at)
                    VALUES 
                    (:id, :name, :test_case_ids, :server_config_id, :database_config_id, :execution_options,
                     :status, :total_cases, :completed_cases, :success_cases, :failed_cases,
                     :start_time, :end_time, :created_at)
                    ON DUPLICATE KEY UPDATE
                        name = VALUES(name),
                        status = VALUES(status),
                        total_cases = VALUES(total_cases),
                        completed_cases = VALUES(completed_cases),
                        success_cases = VALUES(success_cases),
                        failed_cases = VALUES(failed_cases),
                        start_time = VALUES(start_time),
                        end_time = VALUES(end_time)
                """)
                
                conn.execute(sql, insert_data)
                conn.commit()
                logger.info(f"批量执行记录已保存到数据库: {batch_data.get('batch_id')}")
                return True
                
        except Exception as e:
            logger.error(f"保存批量执行记录到数据库失败: {e}")
            return False
    
    def update_batch_execution(self, batch_id: str, update_data: Dict[str, Any]) -> bool:
        """更新批量执行记录"""
        try:
            if not self.engine:
                logger.warning("数据库连接未初始化，跳过数据库更新")
                return False
                
            with self.engine.connect() as conn:
                # 构建更新字段
                set_clauses = []
                params = {'batch_id': batch_id}
                
                if 'status' in update_data:
                    set_clauses.append("status = :status")
                    params['status'] = str(update_data['status']).replace('BatchExecutionStatusEnum.', '').lower()
                
                if 'completed_cases' in update_data:
                    set_clauses.append("completed_cases = :completed_cases")
                    params['completed_cases'] = update_data['completed_cases']
                
                if 'success_cases' in update_data:
                    set_clauses.append("success_cases = :success_cases")
                    params['success_cases'] = update_data['success_cases']
                
                if 'failed_cases' in update_data:
                    set_clauses.append("failed_cases = :failed_cases")
                    params['failed_cases'] = update_data['failed_cases']
                
                if 'start_time' in update_data:
                    set_clauses.append("start_time = :start_time")
                    params['start_time'] = update_data['start_time']
                
                if 'end_time' in update_data:
                    set_clauses.append("end_time = :end_time")
                    params['end_time'] = update_data['end_time']
                
                if not set_clauses:
                    return True
                
                sql = text(f"""
                    UPDATE batch_executions 
                    SET {', '.join(set_clauses)}
                    WHERE id = :batch_id
                """)
                
                result = conn.execute(sql, params)
                conn.commit()
                
                if result.rowcount > 0:
                    logger.info(f"批量执行记录已更新: {batch_id}")
                    return True
                else:
                    logger.warning(f"未找到要更新的批量执行记录: {batch_id}")
                    return False
                    
        except Exception as e:
            logger.error(f"更新批量执行记录失败: {e}")
            return False
    
    def get_batch_executions(self, limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
        """获取批量执行记录列表"""
        try:
            if not self.engine:
                logger.warning("数据库连接未初始化，返回空列表")
                return []
                
            with self.engine.connect() as conn:
                sql = text("""
                    SELECT id, name, test_case_ids, server_config_id, database_config_id, 
                           execution_options, status, total_cases, completed_cases, 
                           success_cases, failed_cases, start_time, end_time, created_at
                    FROM batch_executions 
                    ORDER BY created_at DESC 
                    LIMIT :limit OFFSET :offset
                """)
                
                result = conn.execute(sql, {'limit': limit, 'offset': offset})
                rows = result.fetchall()
                
                executions = []
                for row in rows:
                    execution_options = {}
                    try:
                        if row[5]:  # execution_options
                            execution_options = json.loads(row[5])
                    except Exception as e:
                        logger.warning(f"解析execution_options时发生异常: {e}")
                        execution_options = {}
                    
                    executions.append({
                        'execution_id': row[0],
                        'name': row[1],
                        'database_type': execution_options.get('database_type', 'unknown'),
                        'status': f"BatchExecutionStatusEnum.{row[6].upper()}" if row[6] else 'unknown',
                        'total_cases': row[7] or 0,
                        'completed_cases': row[8] or 0,
                        'success_cases': row[9] or 0,
                        'failed_cases': row[10] or 0,
                        'start_time': row[11].isoformat() if row[11] else None,
                        'end_time': row[12].isoformat() if row[12] else None,
                        'duration': self._calculate_duration(row[11], row[12]),
                        'description': f"包含 {row[7] or 0} 个测试用例的{execution_options.get('database_type', 'unknown')}数据库执行任务"
                    })
                
                logger.info(f"从数据库获取到 {len(executions)} 个批量执行记录")
                return executions
                
        except Exception as e:
            logger.error(f"从数据库获取批量执行记录失败: {e}")
            return []
    
    def get_batch_execution(self, batch_id: str) -> Optional[Dict[str, Any]]:
        """获取单个批量执行记录"""
        try:
            if not self.engine:
                logger.warning("数据库连接未初始化")
                return None
                
            with self.engine.connect() as conn:
                sql = text("""
                    SELECT id, name, test_case_ids, server_config_id, database_config_id, 
                           execution_options, status, total_cases, completed_cases, 
                           success_cases, failed_cases, start_time, end_time, created_at
                    FROM batch_executions 
                    WHERE id = :batch_id
                """)
                
                result = conn.execute(sql, {'batch_id': batch_id})
                row = result.fetchone()
                
                if not row:
                    return None
                
                execution_options = {}
                try:
                    if row[5]:  # execution_options
                        execution_options = json.loads(row[5])
                except Exception as e:
                    logger.warning(f"解析execution_options时发生异常: {e}")
                    execution_options = {}
                
                return {
                    'execution_id': row[0],
                    'name': row[1],
                    'database_type': execution_options.get('database_type', 'unknown'),
                    'status': f"BatchExecutionStatusEnum.{row[6].upper()}" if row[6] else 'unknown',
                    'total_cases': row[7] or 0,
                    'completed_cases': row[8] or 0,
                    'success_cases': row[9] or 0,
                    'failed_cases': row[10] or 0,
                    'start_time': row[11].isoformat() if row[11] else None,
                    'end_time': row[12].isoformat() if row[12] else None,
                    'duration': self._calculate_duration(row[11], row[12]),
                    'description': f"包含 {row[7] or 0} 个测试用例的{execution_options.get('database_type', 'unknown')}数据库执行任务"
                }
                
        except Exception as e:
            logger.error(f"从数据库获取批量执行记录失败: {e}")
            return None
    
    def _calculate_duration(self, start_time, end_time):
        """计算执行时长"""
        try:
            if start_time and end_time:
                if isinstance(start_time, str):
                    start_time = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
                if isinstance(end_time, str):
                    end_time = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
                
                duration = (end_time - start_time).total_seconds()
                return f"{int(duration)}秒"
        except Exception as e:
            logger.warning(f"计算执行时长时发生异常: {e}")
        return None

# 创建全局实例
batch_execution_service = BatchExecutionService()

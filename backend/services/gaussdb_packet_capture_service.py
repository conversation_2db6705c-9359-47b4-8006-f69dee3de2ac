"""
GaussDB数据包捕获服务 - 本地抓包
"""

import asyncio
import os
import logging
from typing import Dict, Any, Optional
from datetime import datetime

from services.local_tcpdump_service import LocalTcpdumpService
from utils.local_network_utils import LocalNetworkUtils
from services.capture_file_service import capture_file_service
from models.capture_file import CaptureFileCreate
from utils.execution_logger import ExecutionLogger
from models.execution_log import LogCategory
from utils.path_manager import path_manager

logger = logging.getLogger(__name__)

class GaussDBPacketCaptureService:
    """GaussDB数据包捕获服务 - 本地抓包"""

    def __init__(self):
        # 改为任务级别的状态管理，避免全局状态冲突
        self.active_captures: Dict[str, Dict[str, Any]] = {}  # task_id -> capture_info
        self.gaussdb_port = 5432  # GaussDB默认端口
        # 使用统一路径管理器
        self.capture_dir = path_manager.get_captures_dir()

        # 执行日志相关
        self.task_id: Optional[str] = None
        self.execution_id: Optional[str] = None
        # 记录本次 start_capture 使用的 task_id，确保 stop_capture 能找到同一个 key
        self._current_capture_task_id: Optional[str] = None


        # 本地tcpdump服务
        self.local_tcpdump_service = LocalTcpdumpService(self.capture_dir)

    def set_execution_context(self, task_id: Optional[str] = None, execution_id: Optional[str] = None):
        """设置执行日志上下文"""
        self.task_id = task_id
        self.execution_id = execution_id

    async def start_local_capture(self, gaussdb_host: str = "**************", gaussdb_port: int = 5432) -> str:
        """
        启动本地GaussDB数据包捕获
        直接监控指定主机和端口的流量

        Args:
            gaussdb_host: GaussDB主机地址
            gaussdb_port: GaussDB端口

        Returns:
            str: 抓包文件路径
        """
        logger.info(f"Starting GaussDB packet capture - Target server: {gaussdb_host}:{gaussdb_port}")

        try:
            # 保存当前抓包配置，用于后续保存到数据库
            self._current_target_host = gaussdb_host
            self._current_target_port = gaussdb_port

            # 获取最佳本地网络接口
            interface = LocalNetworkUtils.get_best_local_interface()
            logger.info(f"Selected local interface: {interface}")

            # 构建过滤表达式 - 监控指定主机和端口的流量
            filter_expr = f"host {gaussdb_host} and tcp port {gaussdb_port}"
            logger.info(f"Monitoring traffic: {filter_expr}")

            # 启动本地tcpdump
            capture_file = await self.local_tcpdump_service.start_capture(
                database_type="gaussdb",
                target_port=gaussdb_port,
                interface=interface,
                filter_expression=filter_expr
            )

            logger.info(f"GaussDB packet capture started successfully: {capture_file}")
            logger.info(f"Monitoring traffic to/from {gaussdb_host}:{gaussdb_port}")
            return capture_file

        except Exception as e:
            logger.error(f"Failed to start GaussDB packet capture: {str(e)}")
            raise Exception(f"GaussDB抓包启动失败: {str(e)}")





    async def start_capture(self, target_host: str = None, target_port: int = None, server_config_id: int = None) -> str:
        """启动GaussDB数据包捕获"""
        gaussdb_host = target_host or "**************"  # 默认使用远程GaussDB服务器
        gaussdb_port = target_port or self.gaussdb_port

        logger.info(f"GaussDB抓包配置 - Host: {gaussdb_host}, Port: {gaussdb_port}")

        # 获取当前任务ID，如果没有则生成一个临时ID
        current_task_id = self.task_id or f"temp_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"

        # 检查当前任务是否已经在抓包
        if current_task_id in self.active_captures:
            logger.warning(f"任务 {current_task_id} 已经在抓包中，先停止之前的抓包")
            await self.stop_capture()

        # 启动抓包
        try:
            # 防御式检查：如果底层local tcpdump还在运行，先强制停止，避免“already running”
            try:
                if getattr(self.local_tcpdump_service, 'is_capturing', False):
                    logger.warning("Detected existing local tcpdump capture, forcing stop before starting new one")
                    await self.local_tcpdump_service.force_stop()
            except Exception as pre_e:
                logger.warning(f"Pre-start force stop warning: {pre_e}")

            logger.info(f"Starting packet capture to monitor GaussDB server: {gaussdb_host}:{gaussdb_port}")
            # 设置 task_id 便于 tcpdump 进程登记
            if current_task_id:
                try:
                    self.local_tcpdump_service.set_task_id(current_task_id)
                except Exception as e:
                    logger.warning(f"设置task_id时发生异常: {e}")
            # 记录本次使用的 task_id，stop 时用同一个 key
            self._current_capture_task_id = current_task_id

            capture_file = await self.start_local_capture(gaussdb_host, gaussdb_port)

            # 记录任务级别的抓包状态
            self.active_captures[current_task_id] = {
                'capture_file': capture_file,
                'target_host': gaussdb_host,
                'target_port': gaussdb_port,
                'start_time': datetime.now(),
                'is_capturing': True
            }

            return capture_file
        except Exception as e:
            logger.error(f"GaussDB capture failed: {str(e)}")
            raise Exception(f"GaussDB抓包启动失败: {str(e)}")

    async def stop_capture(self) -> str:
        """停止GaussDB数据包捕获"""
        try:
            # 获取当前任务ID（优先使用 start_capture 记录的同一个 task_id）
            current_task_id = self._current_capture_task_id or self.task_id or f"temp_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"

            # 检查当前任务是否在抓包
            if current_task_id not in self.active_captures:
                logger.warning(f"任务 {current_task_id} 没有活动的抓包")
                return ""

            # 停止本地tcpdump服务
            if self.local_tcpdump_service.is_capturing:
                capture_file = await self.local_tcpdump_service.stop_capture()
                logger.info(f"Local GaussDB capture stopped: {capture_file}")

                # 检查文件大小，如果太小则删除
                if capture_file:
                    from utils.path_manager import path_manager
                    # LocalTcpdumpService.stop_capture 返回完整路径，这里两种情况都兼容
                    if os.path.isabs(capture_file):
                        full_path = capture_file
                        filename = os.path.basename(capture_file)
                    else:
                        filename = capture_file
                        full_path = path_manager.get_capture_file_path(filename)

                    logger.info(f"🔍 检查抓包文件: filename={filename}, full_path={full_path}")

                    if not os.path.exists(full_path):
                        logger.warning(f"抓包文件不存在: {full_path}")
                        capture_file = None
                    else:
                        file_size = os.path.getsize(full_path)

                        # 创建执行日志记录器
                        exec_logger = ExecutionLogger(
                            task_id=self.task_id,
                            execution_id=self.execution_id,
                            database_type="gaussdb"
                        )

                        await exec_logger.capture_info_async(f"GaussDB抓包文件大小检查: {filename} = {file_size} bytes",
                                                            capture_file=filename)
                        logger.info(f"🔍 GaussDB抓包文件大小检查: {full_path} = {file_size} bytes")

                        if file_size <= 24:  # pcap文件头大小，表示没有实际数据包
                            await exec_logger.capture_info_async(f"GaussDB抓包文件为空，删除文件: {filename} ({file_size} bytes)",
                                                                capture_file=filename)
                            logger.warning(f"GaussDB capture file is empty (only {file_size} bytes), deleting it")
                            try:
                                os.remove(full_path)
                                await exec_logger.capture_info_async(f"成功删除空的GaussDB抓包文件: {filename}",
                                                                    capture_file=filename)
                                logger.info(f"Deleted empty capture file: {full_path}")
                            except Exception as e:
                                await exec_logger.capture_error_async(f"删除空抓包文件失败: {filename}",
                                                                    capture_file=filename, error_details=str(e))
                                logger.error(f"Failed to delete empty capture file: {e}")
                            capture_file = None
                        else:
                            # 保存文件信息到数据库
                            try:
                                await exec_logger.capture_info_async(f"开始保存GaussDB抓包文件到数据库: {filename}",
                                                                    capture_file=filename)
                                logger.info(f"🔍 开始保存GaussDB抓包文件到数据库: {filename}")
                                await self._save_capture_file_to_db(filename)
                                await exec_logger.capture_info_async(f"GaussDB抓包文件已成功保存到数据库: {filename}",
                                                                    capture_file=filename)
                                logger.info(f"✅ GaussDB capture file saved to database: {filename}")
                            except Exception as e:
                                await exec_logger.capture_error_async(f"保存GaussDB抓包文件到数据库失败: {filename}",
                                                                    capture_file=filename,
                                                                    error_details=str(e), include_stack_trace=True)
                                logger.error(f"❌ Failed to save capture file to database: {e}")
                                logger.error(f"❌ 错误详情: {type(e).__name__}: {str(e)}")
                                import traceback
                                logger.error(f"❌ 堆栈跟踪: {traceback.format_exc()}")
                                # 如果保存到数据库失败，不影响返回文件路径

                # 清理任务级别的抓包状态
                if current_task_id in self.active_captures:
                    del self.active_captures[current_task_id]

                # 返回相对路径（不包含captures/前缀），这样下载API就能正确处理
                if capture_file:
                    filename = os.path.basename(capture_file)
                    logger.info(f"Returning filename for download: {filename}")
                    return filename
                else:
                    return ""
            else:
                logger.warning("No local capture process found")
                # 清理任务状态
                if current_task_id in self.active_captures:
                    del self.active_captures[current_task_id]
                return ""

        except Exception as e:
            logger.error(f"Failed to stop GaussDB capture: {str(e)}")
            # 清理任务状态
            current_task_id = self.task_id or f"temp_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"
            if current_task_id in self.active_captures:
                del self.active_captures[current_task_id]
            raise

    def get_capture_status(self) -> Dict[str, Any]:
        """获取抓包状态"""
        current_task_id = self.task_id or "unknown"
        task_info = self.active_captures.get(current_task_id, {})

        return {
            'is_capturing': bool(task_info.get('is_capturing', False)),
            'current_file': task_info.get('capture_file'),
            'local_capture': True,
            'active_captures_count': len(self.active_captures),
            'current_task_id': current_task_id
        }

    def is_capturing_active(self) -> bool:
        """检查当前任务是否正在抓包"""
        current_task_id = self.task_id or "unknown"
        return current_task_id in self.active_captures and self.active_captures[current_task_id].get('is_capturing', False)

    async def _save_capture_file_to_db(self, capture_filename: str):
        """保存抓包文件信息到数据库"""

        # 创建执行日志记录器
        exec_logger = ExecutionLogger(
            task_id=self.task_id,
            execution_id=self.execution_id,
            database_type="gaussdb"
        )

        try:
            # 如果传入的是文件名，构造完整路径
            if os.path.isabs(capture_filename):
                capture_file_path = capture_filename
                filename = os.path.basename(capture_filename)
            else:
                filename = capture_filename
                capture_file_path = path_manager.get_capture_file_path(filename)

            await exec_logger.capture_info_async(f"开始保存GaussDB抓包文件到数据库: {filename}",
                                                capture_file=filename)
            logger.info(f"🔍 _save_capture_file_to_db 被调用")
            logger.info(f"🔍 传入参数: {capture_filename}")
            logger.info(f"🔍 解析后文件名: {filename}")
            logger.info(f"🔍 完整文件路径: {capture_file_path}")
            logger.info(f"🔍 captures目录: {self.capture_dir}")

            if not os.path.exists(capture_file_path):
                error_msg = f"Capture file does not exist: {capture_file_path}"
                await exec_logger.capture_error_async(f"GaussDB抓包文件不存在: {filename}",
                                                     capture_file=filename, error_details=error_msg)
                logger.warning(f"❌ {error_msg}")
                return

            file_size = os.path.getsize(capture_file_path)
            await exec_logger.capture_info_async(f"GaussDB抓包文件信息: {filename}, 大小: {file_size} bytes",
                                                capture_file=filename)
            logger.info(f"🔍 文件信息 - 文件名: {filename}, 大小: {file_size} bytes")

            # 从当前抓包配置中获取目标主机和端口信息
            target_host = getattr(self, '_current_target_host', '**************')
            target_port = getattr(self, '_current_target_port', 5432)
            await exec_logger.capture_info_async(f"GaussDB抓包目标配置: {target_host}:{target_port}",
                                                capture_file=filename)
            logger.info(f"🔍 目标配置 - 主机: {target_host}, 端口: {target_port}")

            capture_data = CaptureFileCreate(
                filename=filename,
                file_path=path_manager.get_relative_capture_path(filename),  # 使用统一路径管理器
                file_size=file_size,
                database_type="gaussdb",
                target_host=target_host,
                target_port=target_port,
                description=f"GaussDB packet capture from {target_host}:{target_port}"
            )
            await exec_logger.capture_info_async(f"创建GaussDB抓包文件数据对象成功: {filename}",
                                                capture_file=filename)
            logger.info(f"🔍 创建CaptureFileCreate对象成功")

            await exec_logger.capture_info_async(f"开始调用抓包文件服务保存: {filename}",
                                                capture_file=filename)
            logger.info(f"🔍 开始调用 capture_file_service.save_capture_file")
            file_id = await capture_file_service.save_capture_file(capture_data, self.task_id, self.execution_id)

            await exec_logger.capture_info_async(f"GaussDB抓包文件成功保存到数据库: {filename}, ID: {file_id}",
                                                capture_file=filename)
            logger.info(f"✅ Saved GaussDB capture file to database with ID: {file_id}")

        except Exception as e:
            await exec_logger.capture_error_async(f"保存GaussDB抓包文件到数据库失败: {filename}",
                                                 capture_file=filename,
                                                 error_details=str(e), include_stack_trace=True)
            logger.error(f"❌ Failed to save capture file to database: {e}")
            logger.error(f"❌ 错误类型: {type(e).__name__}")
            import traceback
            logger.error(f"❌ 完整堆栈跟踪: {traceback.format_exc()}")
            # 不抛出异常，避免影响抓包流程

# 创建全局实例
gaussdb_packet_service = GaussDBPacketCaptureService()


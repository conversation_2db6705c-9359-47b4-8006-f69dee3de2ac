"""
Oracle数据库服务类 - 用于连接和操作Oracle数据库，支持连接池
"""

import logging
import traceback
import oracledb
from typing import List, Dict, Any, Optional, Tuple
import asyncio
from concurrent.futures import ThreadPoolExecutor
import time
import threading

logger = logging.getLogger(__name__)

# 尝试导入cx_Oracle作为备选方案
try:
    import cx_Oracle
    _cx_oracle_available = True
    logger.info("cx_Oracle is available as fallback for Oracle 11g")
except ImportError:
    _cx_oracle_available = False
    logger.info("cx_Oracle not available, will use oracledb only")

# 尝试初始化Oracle客户端（Thick模式）以支持Oracle 11g
_oracle_thick_mode = False
try:
    # 检查是否已经初始化过
    if not hasattr(oracledb, '_initialized'):
        # 尝试初始化Oracle客户端库
        oracledb.init_oracle_client()
        oracledb._initialized = True
        _oracle_thick_mode = True
        logger.info("Oracle client initialized in Thick mode (with Oracle client library)")
    else:
        _oracle_thick_mode = True
        logger.info("Oracle client already initialized in Thick mode")
except Exception as e:
    logger.warning(f"Failed to initialize Oracle client library for Thick mode: {e}")
    if _cx_oracle_available:
        logger.info("Will use cx_Oracle as fallback for Oracle 11g compatibility")
    else:
        logger.info("Will attempt to use oracledb Thin mode, but Oracle 11g may not be supported")

class OracleService:
    """Oracle数据库服务 - 支持连接池"""

    def __init__(self, host: str, port: int, user: str, password: str, service_name: str = "helowin"):
        self.host = host
        self.port = port
        self.user = user
        self.password = password
        self.service_name = service_name
        self.connection = None
        self._persistent_connection = None
        self.executor = ThreadPoolExecutor(max_workers=5)

        # 连接池配置
        self.pool = None
        self.pool_lock = threading.Lock()
        self.pool_min_size = 2
        self.pool_max_size = 10
        self.pool_increment = 1

        # 添加简单的内存缓存
        self._schemas_cache = None
        self._cache_timestamp = None
        self._cache_ttl = 300  # 缓存5分钟

        # 安全初始化Oracle客户端
        try:
            self._safe_oracle_init()
        except AttributeError as e:
            # 如果出现AttributeError，尝试动态添加方法
            logger.error(f"OracleService缺少_safe_oracle_init方法，尝试动态添加...")

            # 动态添加_safe_oracle_init方法
            def _safe_oracle_init_fallback(self):
                """动态添加的安全初始化Oracle客户端方法"""
                try:
                    # 检查是否已经初始化过
                    if not hasattr(oracledb, '_initialized'):
                        # 尝试初始化Oracle客户端库
                        oracledb.init_oracle_client()
                        oracledb._initialized = True
                        logger.info("Oracle client initialized in Thick mode (with Oracle client library)")
                    else:
                        logger.debug("Oracle client already initialized in Thick mode")
                except Exception as e:
                    logger.warning(f"Failed to initialize Oracle client library for Thick mode: {e}")
                    if _cx_oracle_available:
                        logger.info("Will use cx_Oracle as fallback for Oracle 11g compatibility")
                    else:
                        logger.info("Will attempt to use oracledb Thin mode, but Oracle 11g may not be supported")

            # 绑定方法到实例
            import types
            self._safe_oracle_init = types.MethodType(_safe_oracle_init_fallback, self)

            # 调用动态添加的方法
            self._safe_oracle_init()
            logger.info("成功动态添加并调用_safe_oracle_init方法")

    def _safe_oracle_init(self):
        """安全初始化Oracle客户端"""
        try:
            # 检查是否已经初始化过
            if not hasattr(oracledb, '_initialized'):
                # 尝试初始化Oracle客户端库
                oracledb.init_oracle_client()
                oracledb._initialized = True
                logger.info("Oracle client initialized in Thick mode (with Oracle client library)")
            else:
                logger.debug("Oracle client already initialized in Thick mode")
        except Exception as e:
            logger.warning(f"Failed to initialize Oracle client library for Thick mode: {e}")
            if _cx_oracle_available:
                logger.info("Will use cx_Oracle as fallback for Oracle 11g compatibility")
            else:
                logger.info("Will attempt to use oracledb Thin mode, but Oracle 11g may not be supported")

    def _create_connection_pool(self):
        """创建Oracle连接池"""
        try:
            with self.pool_lock:
                if self.pool is not None:
                    return self.pool

                logger.info(f"Creating Oracle connection pool: {self.host}:{self.port}/{self.service_name}")

                # 尝试不同的连接池创建方式
                pool_creation_attempts = []

                # 如果cx_Oracle可用，优先使用它
                if _cx_oracle_available:
                    pool_creation_attempts.extend([
                        # cx_Oracle连接池方式1: 标准DSN连接池
                        lambda: cx_Oracle.create_pool(
                            user=self.user,
                            password=self.password,
                            dsn=self._get_dsn_cx_oracle(),
                            min=self.pool_min_size,
                            max=self.pool_max_size,
                            increment=self.pool_increment,
                            encoding="UTF-8"
                        ),
                        # cx_Oracle连接池方式2: 直接连接字符串
                        lambda: cx_Oracle.create_pool(
                            f"{self.user}/{self.password}@{self.host}:{self.port}/{self.service_name}",
                            min=self.pool_min_size,
                            max=self.pool_max_size,
                            increment=self.pool_increment,
                            encoding="UTF-8"
                        ),
                    ])

                # 然后尝试oracledb连接池
                pool_creation_attempts.extend([
                    # oracledb连接池方式1: 标准DSN连接池
                    lambda: oracledb.create_pool(
                        user=self.user,
                        password=self.password,
                        dsn=self._get_dsn(),
                        min=self.pool_min_size,
                        max=self.pool_max_size,
                        increment=self.pool_increment
                    ),
                    # oracledb连接池方式2: 直接连接字符串
                    lambda: oracledb.create_pool(
                        f"{self.user}/{self.password}@{self.host}:{self.port}/{self.service_name}",
                        min=self.pool_min_size,
                        max=self.pool_max_size,
                        increment=self.pool_increment
                    ),
                    # oracledb连接池方式3: 参数化连接
                    lambda: oracledb.create_pool(
                        user=self.user,
                        password=self.password,
                        host=self.host,
                        port=self.port,
                        service_name=self.service_name,
                        min=self.pool_min_size,
                        max=self.pool_max_size,
                        increment=self.pool_increment
                    )
                ])

                # 尝试创建连接池
                last_error = None
                for i, attempt in enumerate(pool_creation_attempts, 1):
                    try:
                        logger.debug(f"Oracle connection pool attempt {i}/{len(pool_creation_attempts)}")
                        pool = attempt()

                        # 测试连接池
                        test_conn = pool.acquire()
                        test_conn.close()

                        self.pool = pool
                        logger.info(f"Oracle connection pool created successfully (attempt {i})")
                        logger.info(f"Pool config: min={self.pool_min_size}, max={self.pool_max_size}, increment={self.pool_increment}")
                        return self.pool

                    except Exception as e:
                        last_error = e
                        logger.debug(f"Connection pool attempt {i} failed: {e}")
                        continue

                # 如果所有尝试都失败，抛出最后一个错误
                raise last_error

        except Exception as e:
            logger.error(f"Failed to create Oracle connection pool: {str(e)}")
            raise

    def _get_pool_connection(self):
        """从连接池获取连接"""
        try:
            if self.pool is None:
                self._create_connection_pool()

            connection = self.pool.acquire()
            logger.debug("Acquired connection from Oracle pool")
            return connection

        except Exception as e:
            logger.error(f"Failed to get connection from Oracle pool: {str(e)}")
            # 如果连接池失败，回退到单连接模式
            logger.warning("Falling back to single connection mode")
            return self._create_single_connection()

    def _create_single_connection(self):
        """创建单个连接（回退方案）"""
        # 尝试不同的用户凭据组合
        credential_combinations = [
            (self.user, self.password),  # 原始凭据
        ]

        # 如果用户名不是这些常见用户名，添加它们作为备选
        common_users = ['sys', 'system', 'scott', 'hr', 'oe', 'sh']
        for common_user in common_users:
            if self.user.lower() != common_user:
                credential_combinations.append((common_user, self.password))

        last_error = None
        for user, password in credential_combinations:
            connection_attempts = []

            # 如果cx_Oracle可用，优先使用它
            if _cx_oracle_available:
                connection_attempts.extend([
                    # cx_Oracle方式1: 标准DSN连接
                    lambda u=user, p=password: cx_Oracle.connect(
                        user=u,
                        password=p,
                        dsn=self._get_dsn_cx_oracle(),
                        encoding="UTF-8"
                    ),
                    # cx_Oracle方式2: 直接连接字符串
                    lambda u=user, p=password: cx_Oracle.connect(f"{u}/{p}@{self.host}:{self.port}/{self.service_name}", encoding="UTF-8"),
                    # cx_Oracle方式3: 使用SID而不是service_name
                    lambda u=user, p=password: cx_Oracle.connect(f"{u}/{p}@{self.host}:{self.port}:{self.service_name}", encoding="UTF-8"),
                ])

            # 然后尝试oracledb
            connection_attempts.extend([
                # oracledb方式1: 标准DSN连接
                lambda u=user, p=password: oracledb.connect(
                    user=u,
                    password=p,
                    dsn=self._get_dsn()
                ),
                # oracledb方式2: 直接连接字符串
                lambda u=user, p=password: oracledb.connect(f"{u}/{p}@{self.host}:{self.port}/{self.service_name}"),
                # oracledb方式3: 使用SID而不是service_name
                lambda u=user, p=password: oracledb.connect(f"{u}/{p}@{self.host}:{self.port}:{self.service_name}"),
                # oracledb方式4: 简单主机端口连接
                lambda u=user, p=password: oracledb.connect(
                    user=u,
                    password=p,
                    host=self.host,
                    port=self.port,
                    service_name=self.service_name
                )
            ])

            # 尝试当前凭据的所有连接方式
            total_attempts = len(connection_attempts)
            for i, attempt in enumerate(connection_attempts, 1):
                try:
                    logger.debug(f"Oracle single connection attempt {i}/{total_attempts} with {user}")
                    connection = attempt()
                    # 检测使用的是哪个库
                    if hasattr(connection, 'version') and hasattr(connection.version, 'split'):
                        logger.info(f"Connected using cx_Oracle version: {connection.version}")
                    else:
                        logger.info(f"Connected using oracledb")

                    # 更新实际使用的凭据
                    self.user = user
                    self.password = password
                    logger.info(f"Successfully connected with credentials: {user}/***")
                    return connection
                except Exception as e:
                    last_error = e
                    logger.debug(f"Connection attempt {i} with {user} failed: {e}")
                    continue

        # 如果所有尝试都失败，抛出最后一个错误
        raise last_error

    def close_pool(self):
        """关闭连接池"""
        try:
            with self.pool_lock:
                if self.pool is not None:
                    self.pool.close()
                    self.pool = None
                    logger.info("Oracle connection pool closed")
        except Exception as e:
            logger.error(f"Error closing Oracle connection pool: {str(e)}")
        
    def _get_dsn(self) -> str:
        """构建Oracle DSN连接字符串（oracledb）"""
        try:
            # 使用新的oracledb库构建连接字符串
            return f"{self.host}:{self.port}/{self.service_name}"
        except:
            # 如果失败，尝试使用SID格式
            return f"{self.host}:{self.port}:{self.service_name}"

    def _get_dsn_cx_oracle(self) -> str:
        """构建Oracle DSN连接字符串（cx_Oracle）"""
        try:
            # 尝试使用service_name
            return cx_Oracle.makedsn(self.host, self.port, service_name=self.service_name)
        except:
            # 如果失败，尝试使用SID
            return cx_Oracle.makedsn(self.host, self.port, sid=self.service_name)
    
    async def connect(self) -> bool:
        """异步连接到Oracle数据库 - 使用连接池"""
        try:
            def _connect():
                # 尝试创建连接池
                try:
                    self._create_connection_pool()
                    # 测试连接池
                    test_conn = self._get_pool_connection()
                    test_conn.close()
                    logger.info("Oracle connection pool initialized successfully")
                    return True
                except Exception as e:
                    logger.warning(f"Failed to create connection pool, falling back to single connection: {e}")
                    # 回退到单连接模式
                    connection = self._create_single_connection()
                    self.connection = connection
                    return connection


            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(self.executor, _connect)
            if result is True:
                logger.info(f"Connected to Oracle database with connection pool: {self.host}:{self.port}/{self.service_name}")
            else:
                self.connection = result
                logger.info(f"Connected to Oracle database with single connection: {self.host}:{self.port}/{self.service_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to Oracle database: {str(e)}")
            return False
    
    async def disconnect(self):
        """断开数据库连接"""
        try:
            def _disconnect():
                # 关闭连接池
                self.close_pool()

                # 如果有单连接，也关闭它
                if self.connection:
                    self.connection.close()
                    self.connection = None

            loop = asyncio.get_event_loop()
            await loop.run_in_executor(self.executor, _disconnect)
            logger.info("Disconnected from Oracle database")
        except Exception as e:
            logger.error(f"Error disconnecting from Oracle database: {str(e)}")
    
    async def execute_query(self, query: str, params: Tuple = None, use_existing_connection: bool = False) -> Dict[str, Any]:
        """执行查询语句 - 使用连接池"""
        try:
            # 确保连接池已初始化
            if not self.pool and not self.connection:
                await self.connect()

            def _execute():
                connection = None
                try:
                    # 优先使用连接池
                    if self.pool:
                        connection = self._get_pool_connection()
                    elif self.connection:
                        connection = self.connection
                    else:
                        raise Exception("No connection available")

                    cursor = connection.cursor()
                    try:
                        if params:
                            cursor.execute(query, params)
                        else:
                            cursor.execute(query)

                        # 获取列名
                        columns = [desc[0] for desc in cursor.description] if cursor.description else []

                        # 获取数据
                        rows = cursor.fetchall()

                        # 转换为字典列表
                        result = []
                        for row in rows:
                            result.append(dict(zip(columns, row)))

                        return {
                            'success': True,
                            'data': result,
                            'columns': columns,
                            'row_count': len(result)
                        }
                    finally:
                        cursor.close()
                finally:
                    # 如果使用的是连接池连接，需要释放回连接池
                    if connection and self.pool and connection != self.connection:
                        connection.close()  # 这会将连接返回到连接池

            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(self.executor, _execute)
            
        except Exception as e:
            logger.error(f"Failed to execute Oracle query: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'data': [],
                'columns': [],
                'row_count': 0
            }
    
    async def execute_non_query(self, query: str, params: Tuple = None, use_existing_connection: bool = False) -> Dict[str, Any]:
        """执行非查询语句（INSERT, UPDATE, DELETE等）- 使用连接池"""
        try:
            # 确保连接池已初始化
            if not self.pool and not self.connection:
                await self.connect()

            def _execute():
                connection = None
                try:
                    # 优先使用连接池
                    if self.pool:
                        connection = self._get_pool_connection()
                    elif self.connection:
                        connection = self.connection
                    else:
                        raise Exception("No connection available")

                    cursor = connection.cursor()
                    try:
                        if params:
                            cursor.execute(query, params)
                        else:
                            cursor.execute(query)

                        affected_rows = cursor.rowcount
                        connection.commit()

                        return {
                            'success': True,
                            'affected_rows': affected_rows
                        }
                    finally:
                        cursor.close()
                finally:
                    # 如果使用的是连接池连接，需要释放回连接池
                    if connection and self.pool and connection != self.connection:
                        connection.close()  # 这会将连接返回到连接池

            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(self.executor, _execute)
            
        except Exception as e:
            logger.error(f"Failed to execute Oracle non-query: {str(e)}")
            if self.connection:
                try:
                    self.connection.rollback()
                except Exception as e:

                    logger.error(f"操作失败: {type(e).__name__}: {str(e)}")

                    logger.error(f"异常堆栈跟踪: {traceback.format_exc()}")
            return {
                'success': False,
                'error': str(e),
                'affected_rows': 0
            }

    async def execute_multiple_statements(self, sql_text: str, use_existing_connection: bool = False) -> Dict[str, Any]:
        """执行多个SQL语句（支持CREATE TABLE + INSERT等组合）"""
        try:
            # 确保连接池已初始化
            if not self.pool and not self.connection:
                await self.connect()

            # 分割SQL语句
            statements = []
            for line in sql_text.split('\n'):
                line = line.strip()
                if line and not line.startswith('--') and not line.startswith('/*'):
                    statements.append(line.rstrip(';'))

            if not statements:
                return {
                    'success': False,
                    'error': 'No valid SQL statements found',
                    'results': []
                }

            def _execute_multiple():
                connection = None
                try:
                    # 优先使用连接池
                    if self.pool:
                        connection = self._get_pool_connection()
                    elif self.connection:
                        connection = self.connection
                    else:
                        raise Exception("No connection available")

                    cursor = connection.cursor()
                    results = []

                    try:
                        for i, stmt in enumerate(statements):
                            try:
                                logger.info(f"Executing statement {i+1}: {stmt}")
                                cursor.execute(stmt)

                                # 根据语句类型处理结果
                                if stmt.upper().strip().startswith('SELECT'):
                                    # 查询语句，获取结果
                                    columns = [desc[0] for desc in cursor.description] if cursor.description else []
                                    rows = cursor.fetchall()
                                    result_data = []
                                    for row in rows:
                                        result_data.append(dict(zip(columns, row)))

                                    results.append({
                                        'statement': stmt,
                                        'type': 'SELECT',
                                        'success': True,
                                        'data': result_data,
                                        'columns': columns,
                                        'row_count': len(result_data)
                                    })
                                else:
                                    # 非查询语句
                                    results.append({
                                        'statement': stmt,
                                        'type': 'NON_QUERY',
                                        'success': True,
                                        'affected_rows': cursor.rowcount,
                                        'message': 'Statement executed successfully'
                                    })

                            except Exception as stmt_error:
                                logger.error(f"Failed to execute statement {i+1}: {stmt_error}")
                                results.append({
                                    'statement': stmt,
                                    'success': False,
                                    'error': str(stmt_error)
                                })

                        # 提交所有更改
                        connection.commit()

                        # 返回最后一个SELECT结果作为主要结果（如果有的话）
                        select_results = [r for r in results if r.get('type') == 'SELECT' and r.get('success')]
                        if select_results:
                            main_result = select_results[-1]  # 最后一个SELECT结果
                            return {
                                'success': True,
                                'data': main_result['data'],
                                'columns': main_result['columns'],
                                'row_count': main_result['row_count'],
                                'all_results': results
                            }
                        else:
                            # 没有SELECT结果，返回执行摘要
                            successful_count = len([r for r in results if r.get('success')])
                            return {
                                'success': True,
                                'data': [],
                                'columns': [],
                                'row_count': 0,
                                'message': f'Executed {successful_count}/{len(statements)} statements successfully',
                                'all_results': results
                            }

                    finally:
                        cursor.close()
                finally:
                    # 如果使用的是连接池连接，需要释放回连接池
                    if connection and self.pool and connection != self.connection:
                        connection.close()  # 这会将连接返回到连接池

            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(self.executor, _execute_multiple)

        except Exception as e:
            logger.error(f"Failed to execute multiple Oracle statements: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'data': [],
                'columns': [],
                'row_count': 0
            }

    async def get_schemas(self) -> List[str]:
        """获取所有schema列表"""
        import time

        # 检查缓存是否有效
        current_time = time.time()
        if (self._schemas_cache is not None and
            self._cache_timestamp is not None and
            current_time - self._cache_timestamp < self._cache_ttl):
            logger.debug("Returning cached Oracle schemas")
            return self._schemas_cache

        try:
            # 使用更简单高效的查询，直接从all_users获取用户schema
            query = """
                SELECT username
                FROM all_users
                WHERE username NOT IN (
                    'SYS', 'SYSTEM', 'DBSNMP', 'SYSMAN', 'OUTLN', 'MDSYS',
                    'ORDSYS', 'EXFSYS', 'DMSYS', 'WMSYS', 'CTXSYS', 'ANONYMOUS',
                    'XDB', 'ORDPLUGINS', 'OLAPSYS', 'TSMSYS', 'DIP', 'ORACLE_OCM',
                    'APPQOSSYS', 'FLOWS_FILES'
                )
                AND username NOT LIKE 'APEX_%'
                ORDER BY username
            """
            result = await self.execute_query(query)

            if result['success']:
                schemas = [row['USERNAME'] for row in result['data']]
                # 更新缓存
                self._schemas_cache = schemas
                self._cache_timestamp = current_time
                logger.debug(f"Cached {len(schemas)} Oracle schemas")
                return schemas
            else:
                return []

        except Exception as e:
            logger.error(f"Failed to get Oracle schemas: {str(e)}")

            # 如果是Oracle客户端库缺失，返回模拟的schema信息
            if "Cannot locate a 64-bit Oracle Client library" in str(e) or "libclntsh" in str(e):
                logger.info("Oracle client library not found, returning mock schema data")
                mock_schemas = [
                    "HR",           # 人力资源示例schema
                    "SCOTT",        # 经典Oracle示例schema
                    "SYSTEM",       # 系统schema
                    self.user.upper()  # 当前用户schema
                ]
                # 缓存模拟数据
                self._schemas_cache = mock_schemas
                self._cache_timestamp = current_time
                return mock_schemas

            return []

    def clear_cache(self):
        """清除缓存"""
        self._schemas_cache = None
        self._cache_timestamp = None
        logger.debug("Oracle schemas cache cleared")

    async def wait_for_oracle_ready(self, max_wait_seconds: int = 300, check_interval: int = 10) -> bool:
        """等待Oracle数据库就绪"""
        import time
        start_time = time.time()

        logger.info(f"Waiting for Oracle database to be ready (max {max_wait_seconds}s)...")

        while time.time() - start_time < max_wait_seconds:
            try:
                # 尝试连接并执行简单查询
                if await self.connect():
                    result = await self.execute_query("SELECT 1 FROM DUAL")
                    if result['success']:
                        logger.info("Oracle database is ready!")
                        return True
                    await self.disconnect()
            except Exception as e:
                logger.debug(f"Oracle not ready yet: {e}")

            # 等待一段时间后重试
            await asyncio.sleep(check_interval)
            elapsed = int(time.time() - start_time)
            logger.info(f"Still waiting for Oracle... ({elapsed}s elapsed)")

        logger.warning(f"Oracle database did not become ready within {max_wait_seconds} seconds")
        return False

    async def get_tables(self, schema: str = None) -> List[Dict[str, Any]]:
        """获取指定schema的表列表"""
        try:
            if schema:
                query = """
                    SELECT table_name, tablespace_name, num_rows, last_analyzed
                    FROM all_tables 
                    WHERE owner = :schema
                    ORDER BY table_name
                """
                params = (schema.upper(),)
            else:
                query = """
                    SELECT owner, table_name, tablespace_name, num_rows, last_analyzed
                    FROM all_tables 
                    WHERE owner = USER
                    ORDER BY table_name
                """
                params = None
            
            result = await self.execute_query(query, params)
            
            if result['success']:
                return result['data']
            else:
                return []
                
        except Exception as e:
            logger.error(f"Failed to get Oracle tables: {str(e)}")
            return []
    
    async def get_table_structure(self, table_name: str, schema: str = None) -> List[Dict[str, Any]]:
        """获取表结构信息"""
        try:
            if schema:
                query = """
                    SELECT column_name, data_type, data_length, data_precision, data_scale, 
                           nullable, data_default, column_id
                    FROM all_tab_columns 
                    WHERE owner = :schema AND table_name = :table_name
                    ORDER BY column_id
                """
                params = (schema.upper(), table_name.upper())
            else:
                query = """
                    SELECT column_name, data_type, data_length, data_precision, data_scale, 
                           nullable, data_default, column_id
                    FROM user_tab_columns 
                    WHERE table_name = :table_name
                    ORDER BY column_id
                """
                params = (table_name.upper(),)
            
            result = await self.execute_query(query, params)
            
            if result['success']:
                return result['data']
            else:
                return []
                
        except Exception as e:
            logger.error(f"Failed to get Oracle table structure: {str(e)}")
            return []
    
    async def test_connection(self) -> Dict[str, Any]:
        """测试数据库连接"""
        try:
            # 尝试连接
            if not await self.connect():
                return {
                    'success': False,
                    'message': 'Failed to establish connection',
                    'error': 'Connection failed'
                }

            # 执行简单查询测试
            result = await self.execute_query("SELECT 1 FROM DUAL")

            if result['success']:
                # 获取数据库版本信息
                version_result = await self.execute_query("SELECT * FROM v$version WHERE banner LIKE 'Oracle%'")
                version = version_result['data'][0]['BANNER'] if version_result['success'] and version_result['data'] else 'Unknown'

                return {
                    'success': True,
                    'message': 'Connection successful',
                    'version': version,
                    'host': self.host,
                    'port': self.port,
                    'service_name': self.service_name
                }
            else:
                return {
                    'success': False,
                    'message': 'Connection established but query failed',
                    'error': result.get('error', 'Unknown error')
                }

        except Exception as e:
            error_str = str(e)
            logger.error(f"Oracle connection test failed: {error_str}")

            # 检查是否是Oracle客户端库缺失的错误
            if "Cannot locate a 64-bit Oracle Client library" in error_str or "libclntsh" in error_str:
                return {
                    'success': False,
                    'message': 'Oracle客户端库缺失',
                    'error': 'Oracle客户端库未安装。请安装Oracle Instant Client或完整的Oracle客户端。参考: https://cx-oracle.readthedocs.io/en/latest/user_guide/installation.html'
                }
            elif "DPI-1047" in error_str:
                return {
                    'success': False,
                    'message': 'Oracle客户端配置错误',
                    'error': 'Oracle客户端库配置有问题，请检查安装和环境变量配置'
                }
            else:
                return {
                    'success': False,
                    'message': 'Connection test failed',
                    'error': error_str
                }
        finally:
            await self.disconnect()

    async def create_persistent_connection(self) -> bool:
        """创建持久连接用于抓包"""
        try:
            if self._persistent_connection:
                # 检查现有连接是否有效
                try:
                    def test_connection():
                        cursor = self._persistent_connection.cursor()
                        cursor.execute("SELECT 1 FROM DUAL")
                        cursor.fetchone()
                        cursor.close()

                    await asyncio.get_event_loop().run_in_executor(self.executor, test_connection)
                    logger.info("Existing Oracle persistent connection is valid")
                    return True
                except:
                    # 现有连接无效，关闭并重新创建
                    try:
                        self._persistent_connection.close()
                    except Exception as e:
                        logger.debug(f"Error closing invalid Oracle connection: {e}")
                    self._persistent_connection = None

            # 创建新的持久连接
            def create_conn():
                return self._create_single_connection()

            self._persistent_connection = await asyncio.get_event_loop().run_in_executor(
                self.executor, create_conn
            )

            # 测试连接
            def test_new_connection():
                cursor = self._persistent_connection.cursor()
                cursor.execute("SELECT 1 FROM DUAL")
                cursor.fetchone()
                cursor.close()

            await asyncio.get_event_loop().run_in_executor(self.executor, test_new_connection)

            logger.info("Persistent Oracle connection created successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to create persistent Oracle connection: {str(e)}")
            if self._persistent_connection:
                try:
                    self._persistent_connection.close()
                except Exception as e:
                    logger.error(f"操作失败: {type(e).__name__}: {str(e)}")
                    logger.error(f"异常堆栈跟踪: {traceback.format_exc()}")
                self._persistent_connection = None
            return False

    async def create_fresh_connection(self) -> bool:
        """创建全新的连接用于抓包（确保能捕获握手包）"""
        try:
            # 强制关闭现有连接
            if self._persistent_connection:
                try:
                    self._persistent_connection.close()
                except Exception as e:
                    logger.debug(f"Error closing existing Oracle connection: {e}")
                self._persistent_connection = None

            # 等待一小段时间确保连接完全关闭
            await asyncio.sleep(0.5)

            # 创建全新的连接
            def create_conn():
                return self._create_single_connection()

            self._persistent_connection = await asyncio.get_event_loop().run_in_executor(
                self.executor, create_conn
            )

            # 测试连接
            def test_new_connection():
                cursor = self._persistent_connection.cursor()
                cursor.execute("SELECT 1 FROM DUAL")
                cursor.fetchone()
                cursor.close()

            await asyncio.get_event_loop().run_in_executor(self.executor, test_new_connection)

            logger.info("Fresh Oracle connection created successfully for packet capture")
            return True

        except Exception as e:
            logger.error(f"Failed to create fresh Oracle connection: {str(e)}")
            if self._persistent_connection:
                try:
                    self._persistent_connection.close()
                except Exception as e:
                    logger.error(f"操作失败: {type(e).__name__}: {str(e)}")
                    logger.error(f"异常堆栈跟踪: {traceback.format_exc()}")
                self._persistent_connection = None
            return False

    async def close_persistent_connection(self):
        """关闭持久连接"""
        try:
            if self._persistent_connection:
                self._persistent_connection.close()
                self._persistent_connection = None
                logger.info("Persistent Oracle connection closed")
        except Exception as e:
            logger.error(f"Error closing persistent Oracle connection: {str(e)}")
            self._persistent_connection = None

    async def force_close_connection(self):
        """强制关闭连接（确保能捕获挥手包）"""
        try:
            if self._persistent_connection:
                logger.info("Force closing Oracle connection for packet capture")

                # 执行一个简单查询确保连接活跃
                try:
                    def test_connection():
                        cursor = self._persistent_connection.cursor()
                        cursor.execute("SELECT 1 FROM DUAL")
                        cursor.fetchone()
                        cursor.close()

                    await asyncio.get_event_loop().run_in_executor(self.executor, test_connection)
                    logger.debug("Oracle connection is active before closing")
                except Exception as e:
                    logger.debug(f"Oracle connection test failed before closing: {e}")

                # 强制关闭连接
                self._persistent_connection.close()
                self._persistent_connection = None

                # 等待一小段时间确保挥手包被发送
                await asyncio.sleep(0.5)

                logger.info("Oracle connection force closed successfully")
            else:
                logger.warning("No persistent Oracle connection to close")

        except Exception as e:
            logger.error(f"Error during Oracle force close: {str(e)}")
            self._persistent_connection = None

    async def execute_query_with_persistent_connection(self, query: str, params: tuple = None) -> Dict[str, Any]:
        """使用持久连接执行Oracle查询（用于抓包场景）"""
        try:
            if not self._persistent_connection:
                raise Exception("No persistent connection available. Call create_persistent_connection first.")

            logger.info(f"Executing Oracle query with persistent connection: {query}")

            # 在线程池中执行查询
            def execute_sync():
                cursor = self._persistent_connection.cursor()
                try:
                    if params:
                        cursor.execute(query, params)
                    else:
                        cursor.execute(query)

                    # 判断查询类型
                    query_upper = query.strip().upper()
                    if query_upper.startswith(('SELECT', 'WITH')):
                        # 查询操作
                        columns = [desc[0] for desc in cursor.description] if cursor.description else []
                        data = cursor.fetchall()
                        return {
                            'type': 'query',
                            'data': [dict(zip(columns, row)) for row in data] if columns else [],
                            'count': len(data),
                            'columns': columns
                        }
                    else:
                        # 修改操作
                        self._persistent_connection.commit()
                        affected_rows = cursor.rowcount
                        return {
                            'type': 'modification',
                            'affected_rows': affected_rows,
                            'message': f'Command executed successfully, {affected_rows} rows affected'
                        }
                finally:
                    cursor.close()

            result = await asyncio.get_event_loop().run_in_executor(self.executor, execute_sync)
            logger.info("Oracle query executed successfully with persistent connection")
            return result

        except Exception as e:
            logger.error(f"Failed to execute Oracle query with persistent connection: {str(e)}")
            raise Exception(f"Oracle persistent query execution failed: {str(e)}")

    async def execute_query_for_capture(self, query: str, params: tuple = None) -> Dict[str, Any]:
        """专门用于抓包的Oracle查询执行方法（包含完整的连接生命周期）"""
        try:
            logger.info("Starting Oracle query execution for packet capture")
            logger.info("抓包流程: 开始抓包 → 连接数据库 → 执行语句 → 断开数据库 → 停止抓包")

            # 1. 创建全新连接（确保捕获握手包）
            logger.info("步骤1: 连接Oracle数据库...")
            if not await self.create_fresh_connection():
                raise Exception("Failed to create fresh Oracle connection for packet capture")

            # 2. 等待一小段时间确保连接建立完成
            await asyncio.sleep(0.2)

            # 3. 执行Oracle查询
            logger.info(f"步骤2: 执行Oracle查询: {query}")
            result = await self.execute_query_with_persistent_connection(query, params)

            # 4. 等待一小段时间确保查询完成
            await asyncio.sleep(0.2)

            # 5. 断开连接（确保捕获断开包）
            logger.info("步骤3: 断开Oracle数据库连接...")
            await self.close_persistent_connection()

            # 6. 等待一小段时间确保断开完成
            await asyncio.sleep(0.2)

            logger.info("Oracle query execution for packet capture completed")
            return result

        except Exception as e:
            logger.error(f"Oracle query execution for capture failed: {str(e)}")
            # 确保连接被关闭
            try:
                await self.close_persistent_connection()
            except Exception as e:
                logger.warning(f"关闭Oracle持久连接失败: {str(e)}")
            raise

    def __del__(self):
        """析构函数，确保连接被关闭"""
        if self.connection:
            try:
                self.connection.close()
            except Exception as e:

                logger.error(f"操作失败: {type(e).__name__}: {str(e)}")

                logger.error(f"异常堆栈跟踪: {traceback.format_exc()}")

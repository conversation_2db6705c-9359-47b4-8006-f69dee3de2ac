import logging
from typing import Dict, Any, List
from openai import OpenAI
from utils.config import Config

logger = logging.getLogger(__name__)

class MongoAIService:
    """MongoDB AI服务 - 专门处理MongoDB相关的自然语言转换"""
    
    def __init__(self):
        """初始化MongoDB AI服务"""
        self.client = OpenAI(
            api_key=Config.DEEPSEEK_API_KEY,
            base_url=Config.DEEPSEEK_BASE_URL
        )
        logger.info("MongoDB AI Service initialized")
    
    def _call_deepseek(self, prompt: str) -> str:
        """调用DeepSeek API"""
        try:
            response = self.client.chat.completions.create(
                model="deepseek-chat",
                messages=[
                    {"role": "system", "content": "你是一个专业的MongoDB专家，能够将自然语言转换为准确的MongoDB查询语句和操作。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=4096
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            logger.error(f"DeepSeek API call failed: {str(e)}")
            raise
    
    async def parse_natural_language_to_mongo(self, natural_query: str) -> str:
        """将自然语言转换为MongoDB查询"""
        try:
            logger.info(f"Parsing natural language query to MongoDB: {natural_query}")

            # 构建MongoDB专用提示词
            prompt = f"""
请将以下自然语言查询转换为MongoDB操作。

自然语言查询: {natural_query}

要求:
1. 如果是查询操作，返回MongoDB查询语句，格式如: db.collection.find({{query}})
2. 如果是插入操作，返回MongoDB插入语句，格式如: db.collection.insertOne({{document}})
3. 如果是更新操作，返回MongoDB更新语句，格式如: db.collection.updateOne({{filter}}, {{update}})
4. 如果是删除操作，返回MongoDB删除语句，格式如: db.collection.deleteOne({{filter}})
5. 如果是聚合操作，返回MongoDB聚合管道，格式如: db.collection.aggregate([{{pipeline}}])
6. 如果是索引操作，返回MongoDB索引语句，格式如: db.collection.createIndex({{keys}})
7. 如果是管理操作，返回MongoDB管理命令，格式如: db.runCommand({{command}})
8. 如果是创建集合/表操作，返回插入示例文档的语句来创建集合，格式如: db.collection.insertOne({{sample_document}})
9. 如果是删除集合操作，返回删除集合的语句，格式如: db.collection.drop()

**重要限制 - 避免复杂JavaScript代码**：
- 绝对不要生成包含for、while、if等JavaScript控制结构的代码
- 不要使用Math.random()、new Date()、setTimeout等JavaScript函数
- 不要使用变量声明（let、var、const）和字符串拼接
- 如果需要插入多条数据，使用insertMany([{{}}, {{}}, {{}}])格式，每个文档使用固定值
- 保持MongoDB操作简单、直接、可执行

**创建集合的标准格式：**
对于"创建产品表，包含产品ID、产品名称、价格字段"这样的请求，应该返回：
db.products.insertOne({{"productId": "P001", "productName": "示例产品", "price": 99.99}})

MongoDB操作示例:
- 查询: db.users.find({{"age": {{"$gte": 18}}}})
- 插入: db.users.insertOne({{"name": "张三", "age": 25}})
- 更新: db.users.updateOne({{"name": "张三"}}, {{"$set": {{"age": 26}}}})
- 删除: db.users.deleteOne({{"name": "张三"}})
- 删除集合: db.users.drop()
- 聚合: db.users.aggregate([{{"$match": {{"age": {{"$gte": 18}}}}}}, {{"$group": {{"_id": null, "count": {{"$sum": 1}}}}}}])
- 索引: db.users.createIndex({{"name": 1}})
- 统计: db.runCommand({{"collStats": "users"}})

注意事项:
1. 使用正确的MongoDB语法和操作符
2. 字段名和值要符合MongoDB规范
3. 对于复杂查询，使用适当的操作符如 $gte, $lte, $in, $regex 等
4. 对于聚合操作，使用管道操作符如 $match, $group, $sort, $project 等
5. 返回的语句应该是可以直接在MongoDB shell中执行的
6. 对于创建操作，不要先删除集合，直接创建即可
7. **严格禁止**：不要生成任何包含JavaScript编程结构的代码
8. **数据固定化**：所有文档字段使用具体的固定值，不使用随机函数或计算表达式

请直接返回MongoDB操作语句，不要添加额外的解释。
            """

            # 调用DeepSeek API
            result = self._call_deepseek(prompt)

            # 提取MongoDB语句
            mongo_query = self._extract_mongo_from_response(result)

            logger.info(f"Generated MongoDB query: {mongo_query}")
            return mongo_query

        except Exception as e:
            logger.error(f"Failed to parse natural language to MongoDB: {str(e)}")
            raise Exception(f"MongoDB AI parsing failed: {str(e)}")
    
    def _extract_mongo_from_response(self, response: str) -> str:
        """从AI响应中提取MongoDB语句"""
        try:
            # 移除多余的空白字符
            response = response.strip()
            
            # 按行分割响应
            lines = response.split('\n')
            
            # MongoDB操作关键字
            mongo_keywords = ['db.', 'use ', 'show ', 'rs.', 'sh.']
            
            for line in lines:
                line = line.strip()
                # 移除可能的代码块标记
                if line.startswith('```'):
                    continue
                if line.startswith('`') and line.endswith('`'):
                    line = line[1:-1]
                
                # 检查是否包含MongoDB操作
                if any(line.startswith(keyword) for keyword in mongo_keywords):
                    return line
            
            # 如果没有找到明确的MongoDB语句，返回整个响应
            return response

        except Exception as e:
            logger.error(f"Failed to extract MongoDB query: {str(e)}")
            return response
    
    async def analyze_mongo_query_intent(self, natural_query: str) -> Dict[str, Any]:
        """分析MongoDB查询意图"""
        try:
            prompt = f"""
分析以下自然语言查询的MongoDB操作意图：

查询: {natural_query}

请分析并返回以下信息：
1. 操作类型 (find/insert/update/delete/aggregate/index/admin等)
2. 涉及的集合名称
3. 查询条件或操作内容
4. 是否需要抓包分析

请用简洁的文字描述。
            """

            result = self._call_deepseek(prompt)

            return {
                'query': natural_query,
                'analysis': result,
                'requires_capture': True  # 默认需要抓包
            }

        except Exception as e:
            logger.error(f"Failed to analyze MongoDB query intent: {str(e)}")
            return {
                'query': natural_query,
                'analysis': f"Analysis failed: {str(e)}",
                'requires_capture': True
            }
    
    async def generate_mongo_explanation(self, mongo_query: str, result: Dict[str, Any]) -> str:
        """生成MongoDB查询结果的解释"""
        try:
            prompt = f"""
请解释以下MongoDB查询的执行结果：

MongoDB查询: {mongo_query}
执行结果: {result}

请用自然语言解释：
1. 查询做了什么操作
2. 返回了什么结果
3. 结果的含义

请用简洁明了的中文回答。
            """

            explanation = self._call_deepseek(prompt)
            return explanation

        except Exception as e:
            logger.error(f"Failed to generate MongoDB explanation: {str(e)}")
            return f"无法生成解释: {str(e)}"
    
    async def suggest_mongo_optimizations(self, mongo_query: str) -> List[str]:
        """建议MongoDB优化"""
        try:
            prompt = f"""
请分析以下MongoDB查询并提供优化建议：

MongoDB查询: {mongo_query}

请提供：
1. 性能优化建议
2. 索引建议
3. 查询重写建议
4. 最佳实践建议

请用中文回答，每个建议用一行表示。
            """

            suggestions = self._call_deepseek(prompt)

            # 将建议按行分割
            return [line.strip() for line in suggestions.split('\n') if line.strip()]

        except Exception as e:
            logger.error(f"Failed to generate MongoDB suggestions: {str(e)}")
            return [f"无法生成建议: {str(e)}"]

# 创建全局实例
mongo_ai_service = MongoAIService()

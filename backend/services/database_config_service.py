import asyncio
from typing import Dict, List, Optional, Any, Tuple
import logging
import time
from dataclasses import dataclass, asdict
from services.mysql_service import MySQLService
from services.mongo_service import MongoService
from services.postgres_service import PostgresService
from utils.config import Config

logger = logging.getLogger(__name__)

@dataclass
class DatabaseConfigModel:
    """数据库配置模型"""
    id: Optional[int] = None
    name: str = ""
    host: str = ""
    port: int = 3306
    user: str = ""
    password: str = ""
    database_name: str = ""
    database_type: str = "mysql"
    database_version: Optional[str] = None
    server_config_id: Optional[int] = None
    description: str = ""
    is_default: bool = False
    is_active: bool = True
    last_capture_interface: Optional[str] = None
    last_capture_filter: Optional[str] = None
    last_capture_success_time: Optional[str] = None
    capture_retry_count: int = 0
    created_at: Optional[str] = None
    updated_at: Optional[str] = None

@dataclass
class DatabaseTableInfo:
    """数据库表信息模型"""
    database_name: str
    table_name: str
    table_type: str = "BASE TABLE"
    table_comment: str = ""
    table_rows: int = 0
    data_length: int = 0
    index_length: int = 0

class DatabaseConfigService:
    """基于数据库的配置管理服务"""
    
    def __init__(self):
        # 管理数据库配置 (ai_sql_pcap) - 使用Config类的配置
        from utils.config import Config
        self.management_config = {
            'host': Config.MYSQL_HOST,
            'port': Config.MYSQL_PORT,
            'user': Config.MYSQL_USER,
            'password': Config.MYSQL_PASSWORD,
            'database': Config.MYSQL_DATABASE
        }
        self.management_service: Optional[MySQLService] = None
    
    async def _get_management_service(self) -> MySQLService:
        """获取管理数据库服务"""
        if self.management_service is None:
            self.management_service = MySQLService(**self.management_config)
        return self.management_service
    
    async def initialize(self):
        """初始化数据库表结构"""
        try:
            service = await self._get_management_service()
            
            # 读取并执行schema.sql
            import os
            schema_file = os.path.join(os.path.dirname(__file__), '..', 'database', 'schema.sql')
            
            if os.path.exists(schema_file):
                with open(schema_file, 'r', encoding='utf-8') as f:
                    schema_sql = f.read()
                
                # 分割SQL语句并执行
                statements = [stmt.strip() for stmt in schema_sql.split(';') if stmt.strip()]
                for statement in statements:
                    if statement.upper().startswith(('CREATE', 'INSERT')):
                        try:
                            await service.execute_query(statement)
                        except Exception as e:
                            if "already exists" not in str(e) and "Duplicate entry" not in str(e):
                                logger.warning(f"Schema execution warning: {e}")
                
                logger.info("Database schema initialized successfully")
            else:
                logger.warning("Schema file not found, skipping initialization")
                
        except Exception as e:
            logger.error(f"Failed to initialize database schema: {e}")
            raise
    
    async def add_config(self, config: DatabaseConfigModel) -> int:
        """添加数据库配置"""
        try:
            service = await self._get_management_service()
            
            # 检查名称是否已存在
            name_check = await service.execute_query(
                "SELECT id FROM database_configs WHERE name = %s AND is_active = TRUE",
                (config.name,)
            )
            
            if name_check['data']:
                raise Exception(f"Database configuration name '{config.name}' already exists")
            
            # 如果设置为默认，先清除同类型数据库的其他默认配置
            if config.is_default:
                await service.execute_query(
                    "UPDATE database_configs SET is_default = FALSE WHERE is_default = TRUE AND database_type = %s",
                    (config.database_type,)
                )
            
            # 插入新配置
            sql = """
                INSERT INTO database_configs
                (name, host, port, user, password, database_name, database_type, database_version, server_config_id, description, is_default, is_active)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """

            result = await service.execute_query(sql, (
                config.name, config.host, config.port, config.user,
                config.password, config.database_name, config.database_type, config.database_version, config.server_config_id,
                config.description, config.is_default, config.is_active
            ))
            
            # 获取插入的ID
            config_id = result.get('insert_id', 0)
            logger.info(f"Added database config: {config.name} (ID: {config_id})")
            
            return config_id

        except Exception as e:
            logger.error(f"Failed to add database config: {e}")
            raise

    async def get_all_configs(self) -> List[DatabaseConfigModel]:
        """获取所有数据库配置"""
        try:
            service = await self._get_management_service()

            result = await service.execute_query("""
                SELECT * FROM database_configs
                WHERE is_active = TRUE
                ORDER BY is_default DESC, name ASC
            """)

            configs = []
            for row in result['data']:
                config = DatabaseConfigModel(
                    id=row['id'],
                    name=row['name'],
                    host=row['host'],
                    port=row['port'],
                    user=row['user'],
                    password=row['password'],
                    database_name=row['database_name'],
                    database_type=row.get('database_type', 'mysql'),
                    database_version=row.get('database_version'),
                    server_config_id=row.get('server_config_id'),
                    description=row.get('description', ''),
                    is_default=bool(row['is_default']),
                    is_active=bool(row['is_active']),
                    last_capture_interface=row.get('last_capture_interface'),
                    last_capture_filter=row.get('last_capture_filter'),
                    last_capture_success_time=str(row['last_capture_success_time']) if row.get('last_capture_success_time') else None,
                    capture_retry_count=row.get('capture_retry_count', 0),
                    created_at=str(row['created_at']),
                    updated_at=str(row['updated_at'])
                )
                configs.append(config)

            return configs

        except Exception as e:
            logger.error(f"Failed to get all database configs: {str(e)}")
            return []

    async def update_config(self, config: DatabaseConfigModel) -> bool:
        """更新数据库配置"""
        try:
            service = await self._get_management_service()

            # 如果设置为默认，先清除同类型数据库的其他默认配置
            if config.is_default:
                await service.execute_query(
                    "UPDATE database_configs SET is_default = FALSE WHERE is_default = TRUE AND database_type = %s AND id != %s",
                    (config.database_type, config.id)
                )

            # 更新配置
            sql = """
                UPDATE database_configs SET
                    name = %s, host = %s, port = %s, user = %s, password = %s,
                    database_name = %s, database_type = %s, database_version = %s, server_config_id = %s, description = %s, is_default = %s, is_active = %s,
                    last_capture_interface = %s, last_capture_filter = %s,
                    last_capture_success_time = %s, capture_retry_count = %s,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = %s
            """

            await service.execute_query(sql, (
                config.name, config.host, config.port, config.user,
                config.password, config.database_name, config.database_type, config.database_version, config.server_config_id, config.description,
                config.is_default, config.is_active,
                config.last_capture_interface, config.last_capture_filter,
                config.last_capture_success_time, config.capture_retry_count,
                config.id
            ))

            logger.info(f"Updated database config: {config.name} (ID: {config.id})")
            return True

        except Exception as e:
            logger.error(f"Failed to update database config: {str(e)}")
            return False
    
    async def get_config(self, config_id: int) -> Optional[DatabaseConfigModel]:
        """获取数据库配置"""
        try:
            service = await self._get_management_service()
            
            result = await service.execute_query(
                "SELECT * FROM database_configs WHERE id = %s", (config_id,)
            )
            
            if result['data']:
                row = result['data'][0]
                return DatabaseConfigModel(
                    id=row['id'],
                    name=row['name'],
                    host=row['host'],
                    port=row['port'],
                    user=row['user'],
                    password=row['password'],
                    database_name=row['database_name'],
                    database_type=row.get('database_type', 'mysql'),
                    database_version=row.get('database_version'),
                    description=row.get('description', ''),
                    is_default=bool(row['is_default']),
                    is_active=bool(row['is_active']),
                    last_capture_interface=row.get('last_capture_interface'),
                    last_capture_filter=row.get('last_capture_filter'),
                    last_capture_success_time=str(row['last_capture_success_time']) if row.get('last_capture_success_time') else None,
                    capture_retry_count=row.get('capture_retry_count', 0),
                    created_at=str(row['created_at']),
                    updated_at=str(row['updated_at'])
                )
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to get database config: {e}")
            raise
    
    async def get_config_by_name(self, name: str) -> Optional[DatabaseConfigModel]:
        """根据名称获取数据库配置"""
        try:
            service = await self._get_management_service()
            
            result = await service.execute_query(
                "SELECT * FROM database_configs WHERE name = %s AND is_active = TRUE", (name,)
            )
            
            if result['data']:
                row = result['data'][0]
                return DatabaseConfigModel(
                    id=row['id'],
                    name=row['name'],
                    host=row['host'],
                    port=row['port'],
                    user=row['user'],
                    password=row['password'],
                    database_name=row['database_name'],
                    database_type=row.get('database_type', 'mysql'),
                    database_version=row.get('database_version'),
                    description=row.get('description', ''),
                    is_default=bool(row['is_default']),
                    is_active=bool(row['is_active']),
                    last_capture_interface=row.get('last_capture_interface'),
                    last_capture_filter=row.get('last_capture_filter'),
                    last_capture_success_time=str(row['last_capture_success_time']) if row.get('last_capture_success_time') else None,
                    capture_retry_count=row.get('capture_retry_count', 0),
                    created_at=str(row['created_at']),
                    updated_at=str(row['updated_at'])
                )
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to get database config by name: {e}")
            raise
    
    async def list_configs(self, active_only: bool = True) -> List[DatabaseConfigModel]:
        """列出所有数据库配置"""
        try:
            service = await self._get_management_service()

            sql = "SELECT * FROM database_configs"
            if active_only:
                sql += " WHERE is_active = TRUE"
            sql += " ORDER BY is_default DESC, created_at ASC"

            result = await service.execute_query(sql)

            configs = []
            for row in result['data']:
                configs.append(DatabaseConfigModel(
                    id=row['id'],
                    name=row['name'],
                    host=row['host'],
                    port=row['port'],
                    user=row['user'],
                    password=row['password'],
                    database_name=row['database_name'],
                    database_type=row.get('database_type', 'mysql'),
                    database_version=row.get('database_version'),
                    description=row['description'],
                    is_default=bool(row['is_default']),
                    is_active=bool(row['is_active']),
                    created_at=str(row['created_at']),
                    updated_at=str(row['updated_at'])
                ))

            return configs

        except Exception as e:
            logger.error(f"Failed to list database configs: {e}")
            raise

    async def list_configs_paginated(self, page: int = 1, page_size: int = 20, db_type: Optional[str] = None, db_version: Optional[str] = None, active_only: bool = True) -> Tuple[List[DatabaseConfigModel], int]:
        """分页列出数据库配置"""
        try:
            service = await self._get_management_service()

            # 构建WHERE条件
            where_conditions = []
            params = []

            if active_only:
                where_conditions.append("is_active = %s")
                params.append(True)

            if db_type:
                where_conditions.append("database_type = %s")
                params.append(db_type)

            if db_version:
                where_conditions.append("database_version = %s")
                params.append(db_version)

            where_clause = ""
            if where_conditions:
                where_clause = " WHERE " + " AND ".join(where_conditions)

            # 调试日志：显示实际连接的数据库信息
            debug_sql = "SELECT @@hostname as hostname, @@port as port, DATABASE() as current_db"
            debug_result = await service.execute_query(debug_sql)
            logger.info(f"Database connection info: {debug_result['data'][0]}")

            # 获取总数
            count_sql = f"SELECT COUNT(*) as total FROM database_configs{where_clause}"
            logger.info(f"Count SQL: {count_sql}, params: {params}")
            count_result = await service.execute_query(count_sql, tuple(params))
            total = count_result['data'][0]['total']
            logger.info(f"Total count: {total}")

            # 获取分页数据
            offset = (page - 1) * page_size
            data_sql = f"""
                SELECT * FROM database_configs{where_clause}
                ORDER BY is_default DESC, created_at ASC
                LIMIT %s OFFSET %s
            """
            params.extend([page_size, offset])
            logger.info(f"Data SQL: {data_sql}, params: {params}")

            result = await service.execute_query(data_sql, tuple(params))
            logger.info(f"Query result: {len(result['data'])} rows")

            configs = []
            for row in result['data']:
                logger.info(f"Row data: {row}")
                configs.append(DatabaseConfigModel(
                    id=row['id'],
                    name=row['name'],
                    host=row['host'],
                    port=row['port'],
                    user=row['user'],
                    password=row['password'],
                    database_name=row['database_name'],
                    database_type=row.get('database_type', 'mysql'),
                    database_version=row.get('database_version'),
                    description=row['description'],
                    is_default=bool(row['is_default']),
                    is_active=bool(row['is_active']),
                    created_at=str(row['created_at']),
                    updated_at=str(row['updated_at'])
                ))

            return configs, total

        except Exception as e:
            logger.error(f"Failed to list database configs with pagination: {e}")
            raise
    

    
    async def delete_config(self, config_id: int) -> bool:
        """删除数据库配置"""
        try:
            service = await self._get_management_service()
            
            # 软删除：设置为非活跃状态
            result = await service.execute_query(
                "UPDATE database_configs SET is_active = FALSE WHERE id = %s",
                (config_id,)
            )
            
            success = result.get('affected_rows', 0) > 0
            if success:
                logger.info(f"Deleted database config ID: {config_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to delete database config: {e}")
            raise
    
    async def test_connection(self, config: DatabaseConfigModel) -> Dict[str, Any]:
        """测试数据库连接并获取表信息"""
        start_time = time.time()

        try:
            # 根据数据库类型选择相应的服务（不区分大小写）
            db_type = config.database_type.lower()
            if db_type == 'mysql':
                return await self._test_mysql_connection(config, start_time)
            elif db_type == 'mongodb':
                return await self._test_mongodb_connection(config, start_time)
            elif db_type == 'postgresql':
                return await self._test_postgresql_connection(config, start_time)
            elif db_type == 'oracle':
                return await self._test_oracle_connection(config, start_time)
            elif db_type == 'gaussdb':
                return await self._test_gaussdb_connection(config, start_time)
            else:
                return {
                    'success': False,
                    'error': f'Unsupported database type: {config.database_type}',
                    'response_time': int((time.time() - start_time) * 1000)
                }

        except Exception as e:
            logger.error(f"Failed to test connection: {e}")
            return {
                'success': False,
                'error': str(e),
                'response_time': int((time.time() - start_time) * 1000)
            }

    async def _test_gaussdb_connection(self, config: DatabaseConfigModel, start_time: float) -> Dict[str, Any]:
        """测试GaussDB连接"""
        try:
            # 创建GaussDB连接配置
            gaussdb_config = {
                'host': config.host,
                'port': config.port,
                'user': config.user,
                'password': config.password,
                'database': config.database_name
            }

            # 使用GaussDB服务V2（支持连接池和专用驱动）
            from services.gaussdb_service_v2 import GaussDBServiceV2
            gaussdb_service = GaussDBServiceV2(**gaussdb_config)
            await gaussdb_service.initialize()

            # 测试连接（添加超时）
            try:
                is_connected = await asyncio.wait_for(
                    gaussdb_service.check_connection(),
                    timeout=15.0
                )
            except asyncio.TimeoutError:
                logger.error("GaussDB connection test timeout (15 seconds)")
                await gaussdb_service.close()
                return {
                    'success': False,
                    'error': 'GaussDB connection test timeout',
                    'response_time': int((time.time() - start_time) * 1000)
                }

            response_time = int((time.time() - start_time) * 1000)

            if not is_connected:
                await gaussdb_service.close()
                return {
                    'success': False,
                    'error': 'GaussDB connection failed',
                    'response_time': response_time
                }

            # 获取数据库信息
            try:
                databases = await gaussdb_service.get_databases()
                tables = await gaussdb_service.get_tables()

                # 保存连接测试记录
                await self._save_connection_test_record(
                    config_id=config.id,
                    success=True,
                    response_time=response_time,
                    databases_count=len(databases),
                    tables_count=len(tables)
                )

                await gaussdb_service.close()

                return {
                    'success': True,
                    'message': 'GaussDB connection successful',
                    'response_time': response_time,
                    'databases': databases,
                    'tables': tables,
                    'database_count': len(databases),
                    'table_count': len(tables)
                }

            except Exception as info_error:
                logger.warning(f"GaussDB connected but failed to get info: {str(info_error)}")

                # 保存连接测试记录（连接成功但获取信息失败）
                await self._save_connection_test_record(
                    config_id=config.id,
                    success=True,
                    response_time=response_time,
                    error_message=f"Connected but failed to get database info: {str(info_error)}"
                )

                await gaussdb_service.close()

                return {
                    'success': True,
                    'message': 'GaussDB connection successful (limited info)',
                    'response_time': response_time,
                    'warning': f"Connected but failed to get database info: {str(info_error)}"
                }

        except Exception as e:
            response_time = int((time.time() - start_time) * 1000)
            error_message = str(e)

            # 保存连接测试记录
            await self._save_connection_test_record(
                config_id=config.id,
                success=False,
                response_time=response_time,
                error_message=error_message
            )

            logger.error(f"GaussDB connection test failed: {error_message}")
            return {
                'success': False,
                'error': error_message,
                'response_time': response_time
            }

    async def _test_mysql_connection(self, config: DatabaseConfigModel, start_time: float) -> Dict[str, Any]:
        """测试MySQL连接"""
        try:
            # 创建MySQL服务配置
            target_config = {
                'host': config.host,
                'port': config.port,
                'user': config.user,
                'password': config.password,
                'database': config.database_name
            }

            target_service = MySQLService(**target_config)
            
            # 测试连接
            is_connected = await target_service.check_connection()
            response_time = int((time.time() - start_time) * 1000)
            
            if not is_connected:
                await target_service.close()
                return {
                    'success': False,
                    'error': 'Connection failed',
                    'response_time': response_time
                }
            
            # 获取MySQL数据库列表
            databases = await target_service.get_databases()

            # 获取表信息
            tables_info = []
            total_tables = 0

            for db_name in databases:
                try:
                    # MySQL查询表信息
                    table_result = await target_service.execute_query("""
                        SELECT
                            TABLE_NAME,
                            TABLE_TYPE,
                            TABLE_COMMENT,
                            TABLE_ROWS,
                            DATA_LENGTH,
                            INDEX_LENGTH
                        FROM information_schema.TABLES
                        WHERE TABLE_SCHEMA = %s
                        ORDER BY TABLE_NAME
                    """, (db_name,))

                    for table_row in table_result['data']:
                        tables_info.append(DatabaseTableInfo(
                            database_name=db_name,
                            table_name=table_row['TABLE_NAME'],
                            table_type=table_row['TABLE_TYPE'] or 'BASE TABLE',
                            table_comment=table_row['TABLE_COMMENT'] or '',
                            table_rows=table_row['TABLE_ROWS'] or 0,
                            data_length=table_row['DATA_LENGTH'] or 0,
                            index_length=table_row['INDEX_LENGTH'] or 0
                        ))
                        total_tables += 1
                        
                except Exception as e:
                    logger.warning(f"Failed to get tables for database {db_name}: {e}")
            
            await target_service.close()
            
            # 记录测试结果
            if config.id:
                await self._record_connection_test(
                    config.id, True, response_time, None, len(databases), total_tables
                )
            
            return {
                'success': True,
                'response_time': response_time,
                'databases': databases,
                'databases_count': len(databases),
                'tables': [asdict(table) for table in tables_info],
                'tables_count': total_tables
            }
            
        except Exception as e:
            response_time = int((time.time() - start_time) * 1000)
            error_msg = str(e)
            
            # 记录测试失败
            if config.id:
                await self._record_connection_test(
                    config.id, False, response_time, error_msg, 0, 0
                )
            
            return {
                'success': False,
                'error': error_msg,
                'response_time': response_time
            }
    
    async def _record_connection_test(self, config_id: int, success: bool, 
                                    response_time: int, error_msg: Optional[str],
                                    databases_count: int, tables_count: int):
        """记录连接测试结果"""
        try:
            service = await self._get_management_service()
            
            await service.execute_query("""
                INSERT INTO connection_tests 
                (config_id, test_result, response_time, error_message, databases_count, tables_count)
                VALUES (%s, %s, %s, %s, %s, %s)
            """, (config_id, success, response_time, error_msg, databases_count, tables_count))
            
        except Exception as e:
            logger.error(f"Failed to record connection test: {e}")
    
    async def get_default_config(self) -> Optional[DatabaseConfigModel]:
        """获取默认数据库配置"""
        try:
            service = await self._get_management_service()
            
            result = await service.execute_query(
                "SELECT * FROM database_configs WHERE is_default = TRUE AND is_active = TRUE LIMIT 1"
            )
            
            if result['data']:
                row = result['data'][0]
                return DatabaseConfigModel(
                    id=row['id'],
                    name=row['name'],
                    host=row['host'],
                    port=row['port'],
                    user=row['user'],
                    password=row['password'],
                    database_name=row['database_name'],
                    database_type=row.get('database_type', 'mysql'),
                    database_version=row.get('database_version'),
                    description=row['description'],
                    is_default=bool(row['is_default']),
                    is_active=bool(row['is_active']),
                    created_at=str(row['created_at']),
                    updated_at=str(row['updated_at'])
                )
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to get default config: {e}")
            raise

    async def get_default_config_by_type(self, database_type: str) -> Optional[DatabaseConfigModel]:
        """根据数据库类型获取默认配置"""
        try:
            service = await self._get_management_service()

            result = await service.execute_query(
                "SELECT * FROM database_configs WHERE database_type = %s AND is_default = TRUE AND is_active = TRUE LIMIT 1",
                (database_type,)
            )

            if result['data']:
                row = result['data'][0]
                return DatabaseConfigModel(
                    id=row['id'],
                    name=row['name'],
                    host=row['host'],
                    port=row['port'],
                    user=row['user'],
                    password=row['password'],
                    database_name=row['database_name'],
                    database_type=row.get('database_type', 'mysql'),
                    database_version=row.get('database_version'),
                    description=row['description'],
                    is_default=bool(row['is_default']),
                    is_active=bool(row['is_active']),
                    created_at=str(row['created_at']),
                    updated_at=str(row['updated_at'])
                )

            # 如果没有找到默认配置，尝试获取该类型的第一个配置
            result = await service.execute_query(
                "SELECT * FROM database_configs WHERE database_type = %s AND is_active = TRUE LIMIT 1",
                (database_type,)
            )

            if result['data']:
                row = result['data'][0]
                return DatabaseConfigModel(
                    id=row['id'],
                    name=row['name'],
                    host=row['host'],
                    port=row['port'],
                    user=row['user'],
                    password=row['password'],
                    database_name=row['database_name'],
                    database_type=row.get('database_type', 'mysql'),
                    database_version=row.get('database_version'),
                    description=row['description'],
                    is_default=bool(row['is_default']),
                    is_active=bool(row['is_active']),
                    created_at=str(row['created_at']),
                    updated_at=str(row['updated_at'])
                )

            return None

        except Exception as e:
            logger.error(f"Failed to get default config by type {database_type}: {e}")
            return None

    async def set_default_config(self, config_id: int) -> bool:
        """设置默认数据库配置"""
        try:
            service = await self._get_management_service()

            # 获取要设置为默认的配置信息
            config_info = await service.execute_query(
                "SELECT database_type FROM database_configs WHERE id = %s AND is_active = TRUE",
                (config_id,)
            )

            if not config_info['data']:
                raise Exception(f"Database configuration with ID {config_id} not found")

            database_type = config_info['data'][0]['database_type']

            # 清除同类型数据库的默认配置
            await service.execute_query(
                "UPDATE database_configs SET is_default = FALSE WHERE database_type = %s",
                (database_type,)
            )

            # 设置新的默认配置
            result = await service.execute_query(
                "UPDATE database_configs SET is_default = TRUE WHERE id = %s AND is_active = TRUE",
                (config_id,)
            )
            
            success = result.get('affected_rows', 0) > 0
            if success:
                logger.info(f"Set default database config ID: {config_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to set default config: {e}")
            raise
    
    async def _test_mongodb_connection(self, config: DatabaseConfigModel, start_time: float) -> Dict[str, Any]:
        """测试MongoDB连接"""
        try:
            # 创建MongoDB连接配置
            mongo_config = {
                'host': config.host,
                'port': config.port,
                'user': config.user,
                'password': config.password,
                'database': config.database_name
            }

            mongo_service = MongoService(**mongo_config)
            await mongo_service.initialize()

            # 测试连接
            is_connected = await mongo_service.check_connection()
            response_time = int((time.time() - start_time) * 1000)

            if not is_connected:
                await mongo_service.close()
                return {
                    'success': False,
                    'error': 'MongoDB connection failed',
                    'response_time': response_time
                }

            # 获取数据库列表
            databases = await mongo_service.get_databases()

            # 获取集合信息
            collections_info = []
            total_collections = 0

            for db_name in databases:
                try:
                    collections = await mongo_service.get_collections(db_name)
                    total_collections += len(collections)

                    for collection in collections:
                        collections_info.append({
                            'database_name': db_name,
                            'collection_name': collection,
                            'document_count': 0,  # MongoDB集合文档数需要单独查询
                            'size': 0
                        })
                except Exception as e:
                    logger.warning(f"Failed to get collections for database {db_name}: {e}")

            await mongo_service.close()

            return {
                'success': True,
                'response_time': response_time,
                'databases': databases,
                'databases_count': len(databases),
                'collections': collections_info,
                'collections_count': total_collections
            }

        except Exception as e:
            logger.error(f"MongoDB connection test failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'response_time': int((time.time() - start_time) * 1000)
            }

    async def _test_postgresql_connection(self, config: DatabaseConfigModel, start_time: float) -> Dict[str, Any]:
        """测试PostgreSQL连接"""
        try:
            # 创建PostgreSQL连接配置
            postgres_config = {
                'host': config.host,
                'port': config.port,
                'user': config.user,
                'password': config.password,
                'database': config.database_name
            }

            postgres_service = PostgresService(**postgres_config)
            await postgres_service.initialize()

            # 测试连接
            is_connected = await postgres_service.check_connection()
            response_time = int((time.time() - start_time) * 1000)

            if not is_connected:
                await postgres_service.close()
                return {
                    'success': False,
                    'error': 'PostgreSQL connection failed',
                    'response_time': response_time
                }

            # 获取数据库列表
            databases_result = await postgres_service.execute_sql_query("SELECT datname FROM pg_database WHERE datistemplate = false")
            databases = [row['datname'] for row in databases_result['data']]

            # 获取表信息
            tables_info = []
            total_tables = 0

            try:
                # 获取当前数据库的表信息
                tables_result = await postgres_service.execute_sql_query("""
                    SELECT
                        schemaname as schema_name,
                        tablename as table_name,
                        'table' as table_type
                    FROM pg_tables
                    WHERE schemaname NOT IN ('information_schema', 'pg_catalog')
                    ORDER BY schemaname, tablename
                """)

                for table in tables_result['data']:
                    tables_info.append({
                        'database_name': config.database_name,
                        'schema_name': table['schema_name'],
                        'table_name': table['table_name'],
                        'table_type': table['table_type'],
                        'table_comment': '',  # PostgreSQL表注释需要单独查询
                        'table_rows': 0,  # PostgreSQL表行数需要单独查询
                        'data_length': 0,
                        'index_length': 0
                    })

                total_tables = len(tables_info)

            except Exception as e:
                logger.warning(f"Failed to get PostgreSQL table info: {e}")

            await postgres_service.close()

            return {
                'success': True,
                'response_time': response_time,
                'databases': databases,
                'databases_count': len(databases),
                'tables': tables_info,
                'tables_count': total_tables
            }

        except Exception as e:
            logger.error(f"PostgreSQL connection test failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'response_time': int((time.time() - start_time) * 1000)
            }

    async def _test_oracle_connection(self, config: DatabaseConfigModel, start_time: float) -> Dict[str, Any]:
        """测试Oracle连接"""
        try:
            from services.oracle_service import OracleService

            # 创建Oracle服务
            oracle_service = OracleService(
                host=config.host,
                port=config.port,
                user=config.user,
                password=config.password,
                service_name=config.database_name
            )

            # 测试连接
            test_result = await oracle_service.test_connection()
            response_time = int((time.time() - start_time) * 1000)

            if not test_result['success']:
                return {
                    'success': False,
                    'error': test_result.get('error', 'Oracle connection failed'),
                    'response_time': response_time
                }

            # 获取schemas
            await oracle_service.connect()
            schemas = await oracle_service.get_schemas()

            # 获取表信息
            tables_info = []
            total_tables = 0

            try:
                # 获取当前用户的表
                tables = await oracle_service.get_tables()
                # 转换Oracle表信息格式以匹配API期望的格式
                for table in tables:
                    formatted_table = {
                        'database_name': table.get('OWNER', config.user.upper()),  # Oracle中用OWNER作为schema/database
                        'table_name': table.get('TABLE_NAME', ''),
                        'table_type': 'TABLE',  # Oracle中默认为TABLE
                        'table_comment': '',  # Oracle表注释需要单独查询
                        'table_rows': table.get('NUM_ROWS', 0) or 0,
                        'data_length': 0,  # Oracle中需要单独计算
                        'index_length': 0   # Oracle中需要单独计算
                    }
                    tables_info.append(formatted_table)
                total_tables = len(tables)

                # 如果有其他schemas，也获取一些表信息（限制数量避免过多）
                for schema in schemas[:3]:  # 只获取前3个schema的表
                    try:
                        schema_tables = await oracle_service.get_tables(schema)
                        for table in schema_tables[:10]:  # 每个schema最多10个表
                            formatted_table = {
                                'database_name': schema,
                                'table_name': table.get('TABLE_NAME', ''),
                                'table_type': 'TABLE',
                                'table_comment': '',
                                'table_rows': table.get('NUM_ROWS', 0) or 0,
                                'data_length': 0,
                                'index_length': 0
                            }
                            tables_info.append(formatted_table)
                        total_tables += len(schema_tables)
                    except Exception as e:
                        logger.debug(f"Failed to get tables for schema {schema}: {e}")

            except Exception as e:
                logger.warning(f"Failed to get Oracle table info: {e}")

            await oracle_service.disconnect()

            return {
                'success': True,
                'response_time': response_time,
                'databases': schemas,  # Oracle中schemas相当于databases
                'databases_count': len(schemas),
                'tables': tables_info,
                'tables_count': total_tables,
                'version': test_result.get('version', 'Unknown')
            }

        except Exception as e:
            error_str = str(e)
            logger.error(f"Oracle connection test failed: {error_str}")

            # 检查是否是Oracle客户端库缺失的错误
            if "Cannot locate a 64-bit Oracle Client library" in error_str or "libclntsh" in error_str:
                error_message = 'Oracle客户端库缺失。请安装Oracle Instant Client或完整的Oracle客户端。'
            elif "DPI-1047" in error_str:
                error_message = 'Oracle客户端库配置有问题，请检查安装和环境变量配置'
            elif "'database_name'" in error_str:
                error_message = 'Oracle配置错误：数据库名称(service_name)配置有问题'
            else:
                error_message = error_str

            return {
                'success': False,
                'error': error_message,
                'response_time': int((time.time() - start_time) * 1000)
            }

    async def wait_for_oracle_ready(self, config: DatabaseConfigModel, max_wait_seconds: int = 300) -> Dict[str, Any]:
        """等待Oracle数据库就绪"""
        try:
            from services.oracle_service import OracleService

            oracle_service = OracleService(
                host=config.host,
                port=config.port,
                user=config.user,
                password=config.password,
                service_name=config.database_name
            )

            is_ready = await oracle_service.wait_for_oracle_ready(max_wait_seconds)

            if is_ready:
                return {
                    'success': True,
                    'message': 'Oracle database is ready',
                    'ready': True
                }
            else:
                return {
                    'success': False,
                    'message': f'Oracle database did not become ready within {max_wait_seconds} seconds',
                    'ready': False
                }

        except Exception as e:
            logger.error(f"Failed to check Oracle readiness: {e}")
            return {
                'success': False,
                'message': f'Error checking Oracle readiness: {str(e)}',
                'ready': False,
                'error': str(e)
            }

    async def _save_connection_test_record(
        self,
        config_id: int,
        success: bool,
        response_time: int,
        databases_count: int = 0,
        tables_count: int = 0,
        error_message: str = None
    ):
        """保存连接测试记录"""
        try:
            service = await self._get_management_service()

            # 这里可以保存到数据库或日志
            # 目前只记录日志
            if success:
                logger.info(f"Connection test successful for config {config_id}: "
                          f"response_time={response_time}ms, "
                          f"databases={databases_count}, "
                          f"tables={tables_count}")
            else:
                logger.warning(f"Connection test failed for config {config_id}: "
                             f"response_time={response_time}ms, "
                             f"error={error_message}")

        except Exception as e:
            logger.error(f"Failed to save connection test record: {str(e)}")

    async def close(self):
        """关闭服务"""
        if self.management_service:
            await self.management_service.close()
            self.management_service = None

# 创建全局服务实例
database_config_service = DatabaseConfigService()

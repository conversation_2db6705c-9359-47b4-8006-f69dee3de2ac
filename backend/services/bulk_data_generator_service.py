"""
大批量数据生成服务
专门处理大量数据的SQL生成，包括增删改查操作
针对AI生成SQL时经常生成少量示例数据而不是请求的大量数据的问题
"""

import logging
import asyncio
import re
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
import random
import string
from services.mysql_service import MySQLService
from services.database_config_service import DatabaseConfigService
from utils.config import Config

logger = logging.getLogger(__name__)

class BulkDataGeneratorService:
    """大批量数据生成服务"""
    
    def __init__(self):
        self.mysql_service = None
        self.db_config_service = DatabaseConfigService()
        self.current_config_id = None
        # 预定义的示例数据模板
        self.data_templates = {
            'names': ['张三', '李四', '王五', '赵六', '孙七', '周八', '吴九', '郑十', 
                     '马小明', '陈大华', '刘小红', '杨小芳', '林小强', '黄小美', '徐小军',
                     '朱小丽', '胡小刚', '郭小敏', '何小亮', '梁小静', '宋小伟', '唐小娟'],
            'products': ['手机', '电脑', '平板', '耳机', '键盘', '鼠标', '显示器', '音响',
                        '相机', '打印机', '路由器', '充电器', '数据线', 'U盘', '硬盘', '内存条'],
            'cities': ['北京', '上海', '广州', '深圳', '杭州', '南京', '苏州', '成都',
                      '重庆', '武汉', '西安', '天津', '青岛', '大连', '宁波', '厦门'],
            'companies': ['阿里巴巴', '腾讯', '百度', '字节跳动', '美团', '京东', '网易', '华为',
                         '小米', '滴滴', '快手', '拼多多', '携程', '新浪', '搜狐', '360'],
            'departments': ['技术部', '销售部', '市场部', '人事部', '财务部', '运营部', '客服部', '采购部']
        }
        logger.info("Bulk Data Generator Service initialized")

    async def configure_mysql_connection(self, config_id: int) -> Dict[str, Any]:
        """配置MySQL数据库连接"""
        try:
            config = await self.db_config_service.get_config(config_id)
            if not config:
                raise Exception(f"Database config not found: {config_id}")
            
            self.mysql_service = MySQLService(
                host=config.host,
                port=config.port,
                user=config.user,
                password=config.password,
                database=config.database_name
            )
            
            # 测试连接
            connection_test = await self.mysql_service.check_connection()
            if not connection_test:
                raise Exception("MySQL connection test failed")
            
            self.current_config_id = config_id
            logger.info(f"MySQL connection configured for bulk data generator: {config.host}:{config.port}")
            
            return {
                "success": True,
                "config_id": config_id,
                "connection_info": {
                    "host": config.host,
                    "port": config.port,
                    "database": config.database_name
                }
            }
            
        except Exception as e:
            logger.error(f"Failed to configure MySQL connection: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }

    def extract_quantity_and_table(self, natural_query: str) -> Tuple[int, str, str]:
        """从自然语言中提取数量、表名和操作类型"""
        # 提取数量
        quantity_patterns = [
            r'(\d+)条',
            r'(\d+)行',
            r'(\d+)个',
            r'(\d+)张',
            r'(\d+)份'
        ]
        
        quantity = 10  # 默认数量
        for pattern in quantity_patterns:
            match = re.search(pattern, natural_query)
            if match:
                quantity = int(match.group(1))
                break
        
        # 提取表名 - 完全重写的表名提取逻辑
        table_name = "data_table"  # 默认表名
        
        # 按优先级排列的表名提取规则
        table_patterns = [
            # 优先级1："在[table_name]表中" 或 "在[table_name]表里" 或 "在[table_name]中"
            (r'在\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*表?[中里]', 1),
            # 优先级2："向[table_name]表" 
            (r'向\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*表', 2),
            # 优先级3："[table_name]表" （排除后面跟着中文的情况）
            (r'([a-zA-Z_][a-zA-Z0-9_]*)\s*表(?![中里插入删除更新])', 3),
            # 优先级4："表[table_name]" 
            (r'表\s*([a-zA-Z_][a-zA-Z0-9_]*)', 4),
            # 优先级5："[table_name]插入" - 但必须是英文标识符
            (r'([a-zA-Z_][a-zA-Z0-9_]*)(?=\s*插入)', 5)
        ]
        
        best_match = None
        best_priority = 999
        
        for pattern, priority in table_patterns:
            match = re.search(pattern, natural_query)
            if match and priority < best_priority:
                potential_table = match.group(1).strip()
                
                # 验证表名的有效性
                if self._is_valid_table_name(potential_table):
                    best_match = potential_table
                    best_priority = priority
                    logger.info(f"Found potential table name: {potential_table} (priority {priority}) using pattern: {pattern}")
        
        if best_match:
            table_name = best_match
            logger.info(f"Final extracted table name: {table_name}")
        else:
            logger.warning(f"No valid table name found in query: {natural_query}, using default: {table_name}")
        
        # 判断操作类型
        operation_type = "INSERT"
        if any(keyword in natural_query for keyword in ['删除', '删掉', 'delete']):
            operation_type = "DELETE"
        elif any(keyword in natural_query for keyword in ['更新', '修改', 'update']):
            operation_type = "UPDATE"
        elif any(keyword in natural_query for keyword in ['查询', '查看', '选择', 'select']):
            operation_type = "SELECT"
        elif any(keyword in natural_query for keyword in ['插入', '增加', '添加', 'insert']):
            operation_type = "INSERT"
        
        logger.info(f"Extraction result: quantity={quantity}, table_name={table_name}, operation_type={operation_type}")
        return quantity, table_name, operation_type
    
    def _is_valid_table_name(self, table_name: str) -> bool:
        """验证表名是否有效"""
        if not table_name or len(table_name) == 0:
            return False
        
        # 排除常见的非表名词汇
        invalid_words = [
            '数据', '记录', '条', '行', '个', '帮我', '请', '要', '让我', 
            '插入', '删除', '更新', '查询', '添加', '增加', '修改',
            '表', '中', '里', '向', '在'
        ]
        
        if table_name in invalid_words:
            return False
        
        # 检查是否为纯中文（排除纯中文的匹配）
        if all(ord(c) >= 0x4e00 and ord(c) <= 0x9fff for c in table_name):
            return False
        
        # 检查是否为有效的SQL标识符格式
        if not re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*$', table_name):
            return False
        
        # 排除过长的匹配（可能是误匹配的短语）
        if len(table_name) > 50:
            return False
        
        return True

    async def get_table_structure(self, table_name: str) -> Dict[str, Any]:
        """获取表结构信息"""
        try:
            if not self.mysql_service:
                raise Exception("MySQL service not configured")
            
            # 获取表结构
            structure = await self.mysql_service.get_table_structure(table_name)
            return structure
            
        except Exception as e:
            logger.warning(f"Failed to get table structure for {table_name}: {str(e)}")
            # 返回默认结构
            return {
                "table_name": table_name,
                "columns": [
                    {"Field": "id", "Type": "int", "Key": "PRI"},
                    {"Field": "name", "Type": "varchar(100)", "Key": ""},
                    {"Field": "value", "Type": "int", "Key": ""},
                    {"Field": "description", "Type": "text", "Key": ""},
                    {"Field": "created_at", "Type": "timestamp", "Key": ""}
                ]
            }

    def generate_sample_data(self, columns: List[Dict], count: int) -> List[Dict]:
        """根据字段结构生成示例数据"""
        sample_data = []
        
        for i in range(count):
            row_data = {}
            
            for col in columns:
                field_name = col.get("Field", "")
                field_type = col.get("Type", "").lower()
                is_primary = col.get("Key", "") == "PRI"
                
                # 跳过自增主键
                if is_primary and "auto_increment" in str(col).lower():
                    continue
                
                # 根据字段名和类型生成数据
                if field_name.lower() == "id" and not is_primary:
                    row_data[field_name] = i + 1
                elif field_name.lower() in ["name", "username", "user_name"]:
                    row_data[field_name] = f"用户{i+1:04d}"
                elif field_name.lower() in ["email", "mail"]:
                    row_data[field_name] = f"user{i+1:04d}@example.com"
                elif field_name.lower() in ["phone", "mobile", "tel"]:
                    row_data[field_name] = f"1{random.randint(30,89)}{random.randint(10000000,99999999)}"
                elif field_name.lower() in ["age", "年龄"]:
                    row_data[field_name] = random.randint(18, 65)
                elif field_name.lower() in ["price", "amount", "money", "salary"]:
                    row_data[field_name] = round(random.uniform(100, 10000), 2)
                elif field_name.lower() in ["score", "grade"]:
                    row_data[field_name] = round(random.uniform(60, 100), 1)
                elif field_name.lower() in ["status", "state"]:
                    row_data[field_name] = random.choice(['active', 'inactive', 'pending'])
                elif field_name.lower() in ["city", "address"]:
                    row_data[field_name] = random.choice(self.data_templates['cities'])
                elif field_name.lower() in ["company", "organization"]:
                    row_data[field_name] = random.choice(self.data_templates['companies'])
                elif field_name.lower() in ["department", "dept"]:
                    row_data[field_name] = random.choice(self.data_templates['departments'])
                elif "varchar" in field_type or "text" in field_type:
                    if "description" in field_name.lower():
                        row_data[field_name] = f"描述信息_{i+1:04d}"
                    else:
                        row_data[field_name] = f"数据_{i+1:04d}"
                elif "int" in field_type:
                    row_data[field_name] = random.randint(1, 1000)
                elif "decimal" in field_type or "float" in field_type:
                    row_data[field_name] = round(random.uniform(1, 1000), 2)
                elif "date" in field_type or "time" in field_type:
                    if "created" in field_name.lower():
                        base_date = datetime.now() - timedelta(days=random.randint(1, 365))
                        row_data[field_name] = base_date.strftime('%Y-%m-%d %H:%M:%S')
                    else:
                        row_data[field_name] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                else:
                    row_data[field_name] = f"值_{i+1:04d}"
            
            sample_data.append(row_data)
        
        return sample_data

    def generate_bulk_insert_sql(self, table_name: str, columns: List[Dict], count: int) -> str:
        """生成批量插入SQL"""
        try:
            # 生成示例数据
            sample_data = self.generate_sample_data(columns, count)
            
            if not sample_data:
                return f"-- 无法为表 {table_name} 生成数据"
            
            # 获取非自增字段
            fields = []
            for col in columns:
                field_name = col.get("Field", "")
                is_auto_increment = "auto_increment" in str(col).lower()
                if field_name and not is_auto_increment:
                    fields.append(field_name)
            
            if not fields:
                return f"-- 表 {table_name} 没有可插入的字段"
            
            # 构建INSERT语句
            sql_parts = []
            sql_parts.append(f"INSERT INTO {table_name} ({', '.join(fields)}) VALUES")
            
            # 分批处理，避免单个语句过长
            batch_size = min(1000, count)  # 每批最多1000条
            batches = [sample_data[i:i + batch_size] for i in range(0, len(sample_data), batch_size)]
            
            all_sqls = []
            
            for batch_idx, batch in enumerate(batches):
                values_parts = []
                for row in batch:
                    values = []
                    for field in fields:
                        value = row.get(field, "NULL")
                        if value is None or value == "NULL":
                            values.append("NULL")
                        elif isinstance(value, str):
                            # 转义单引号
                            escaped_value = value.replace("'", "''")
                            values.append(f"'{escaped_value}'")
                        else:
                            values.append(str(value))
                    
                    values_parts.append(f"({', '.join(values)})")
                
                # 构建完整的INSERT语句
                batch_sql = f"INSERT INTO {table_name} ({', '.join(fields)}) VALUES\n" + ",\n".join(values_parts) + ";"
                all_sqls.append(batch_sql)
            
            # 如果有多批，添加说明注释
            if len(all_sqls) > 1:
                result = f"-- 批量插入 {count} 条数据到表 {table_name} (分 {len(all_sqls)} 批执行)\n\n"
                result += "\n\n".join(all_sqls)
            else:
                result = f"-- 批量插入 {count} 条数据到表 {table_name}\n\n"
                result += all_sqls[0]
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to generate bulk insert SQL: {str(e)}")
            return f"-- 生成批量插入SQL失败: {str(e)}"

    def generate_bulk_delete_sql(self, table_name: str, count: int) -> str:
        """生成批量删除SQL"""
        try:
            # 根据数量生成不同的删除策略
            if count >= 1000:
                # 大量删除，使用LIMIT
                return f"-- 批量删除 {count} 条记录\nDELETE FROM {table_name} ORDER BY id LIMIT {count};"
            else:
                # 少量删除，可以指定具体条件
                conditions = []
                for i in range(1, min(count + 1, 101)):  # 最多生成100个具体ID
                    conditions.append(str(i))
                
                if len(conditions) < count:
                    return f"-- 批量删除 {count} 条记录\nDELETE FROM {table_name} ORDER BY id LIMIT {count};"
                else:
                    return f"-- 批量删除 {count} 条记录\nDELETE FROM {table_name} WHERE id IN ({', '.join(conditions)});"
                    
        except Exception as e:
            logger.error(f"Failed to generate bulk delete SQL: {str(e)}")
            return f"-- 生成批量删除SQL失败: {str(e)}"

    def generate_bulk_update_sql(self, table_name: str, columns: List[Dict], count: int) -> str:
        """生成批量更新SQL"""
        try:
            # 找到可更新的字段（排除主键和自增字段）
            updatable_fields = []
            for col in columns:
                field_name = col.get("Field", "")
                is_primary = col.get("Key", "") == "PRI"
                is_auto_increment = "auto_increment" in str(col).lower()
                
                if field_name and not is_primary and not is_auto_increment:
                    updatable_fields.append(field_name)
            
            if not updatable_fields:
                return f"-- 表 {table_name} 没有可更新的字段"
            
            # 选择要更新的字段
            update_field = updatable_fields[0]
            field_type = ""
            for col in columns:
                if col.get("Field") == update_field:
                    field_type = col.get("Type", "").lower()
                    break
            
            # 根据字段类型生成更新值
            if "varchar" in field_type or "text" in field_type:
                update_value = f"'更新值_{datetime.now().strftime('%Y%m%d_%H%M%S')}'"
            elif "int" in field_type:
                update_value = str(random.randint(1000, 9999))
            elif "decimal" in field_type or "float" in field_type:
                update_value = str(round(random.uniform(100, 999), 2))
            else:
                update_value = f"'批量更新_{datetime.now().strftime('%H%M%S')}'"
            
            # 生成更新SQL
            if count >= 1000:
                sql = f"-- 批量更新 {count} 条记录\n"
                sql += f"UPDATE {table_name} SET {update_field} = {update_value} ORDER BY id LIMIT {count};"
            else:
                sql = f"-- 批量更新 {count} 条记录\n"
                sql += f"UPDATE {table_name} SET {update_field} = {update_value} WHERE id <= {count};"
            
            return sql
            
        except Exception as e:
            logger.error(f"Failed to generate bulk update SQL: {str(e)}")
            return f"-- 生成批量更新SQL失败: {str(e)}"

    def generate_bulk_select_sql(self, table_name: str, count: int) -> str:
        """生成批量查询SQL"""
        try:
            if count >= 1000:
                return f"-- 查询 {count} 条记录\nSELECT * FROM {table_name} ORDER BY id LIMIT {count};"
            else:
                return f"-- 查询 {count} 条记录\nSELECT * FROM {table_name} WHERE id <= {count} ORDER BY id;"
                
        except Exception as e:
            logger.error(f"Failed to generate bulk select SQL: {str(e)}")
            return f"-- 生成批量查询SQL失败: {str(e)}"

    async def process_bulk_operation_request(
        self, 
        natural_query: str,
        config_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """处理大批量操作请求"""
        try:
            logger.info(f"Processing bulk operation request: {natural_query}")
            
            # 配置数据库连接
            if config_id and config_id != self.current_config_id:
                connection_result = await self.configure_mysql_connection(config_id)
                if not connection_result.get("success"):
                    return connection_result
            
            # 提取操作信息
            quantity, table_name, operation_type = self.extract_quantity_and_table(natural_query)
            
            logger.info(f"Extracted: quantity={quantity}, table={table_name}, operation={operation_type}")
            
            # 获取表结构
            table_structure = await self.get_table_structure(table_name)
            columns = table_structure.get("columns", [])
            
            # 根据操作类型生成对应的SQL
            generated_sql = ""
            
            if operation_type == "INSERT":
                generated_sql = self.generate_bulk_insert_sql(table_name, columns, quantity)
            elif operation_type == "DELETE":
                generated_sql = self.generate_bulk_delete_sql(table_name, quantity)
            elif operation_type == "UPDATE":
                generated_sql = self.generate_bulk_update_sql(table_name, columns, quantity)
            elif operation_type == "SELECT":
                generated_sql = self.generate_bulk_select_sql(table_name, quantity)
            else:
                generated_sql = f"-- 不支持的操作类型: {operation_type}"
            
            return {
                "success": True,
                "natural_query": natural_query,
                "detected_quantity": quantity,
                "detected_table": table_name,
                "detected_operation": operation_type,
                "generated_sql": generated_sql,
                "table_structure": table_structure,
                "config_id": config_id,
                "is_bulk_operation": True,
                "estimated_execution_time": self._estimate_execution_time(quantity, operation_type)
            }
            
        except Exception as e:
            logger.error(f"Failed to process bulk operation request: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "natural_query": natural_query,
                "is_bulk_operation": True
            }

    def _estimate_execution_time(self, quantity: int, operation_type: str) -> str:
        """估算执行时间"""
        if operation_type == "INSERT":
            if quantity <= 100:
                return "< 1秒"
            elif quantity <= 1000:
                return "1-3秒"
            elif quantity <= 10000:
                return "3-10秒"
            else:
                return f"预计 {quantity // 1000}0-{quantity // 1000 * 2}0秒"
        else:
            if quantity <= 1000:
                return "< 1秒"
            elif quantity <= 10000:
                return "1-5秒"
            else:
                return f"预计 {quantity // 10000}0-{quantity // 5000}0秒"

    def detect_bulk_operation(self, natural_query: str) -> bool:
        """检测是否为大批量操作请求"""
        # 检测数量关键词
        quantity_patterns = [
            r'\d+条',
            r'\d+行', 
            r'\d+个',
            r'\d+张',
            r'\d+份'
        ]
        
        found_quantity = False
        extracted_number = 0
        
        for pattern in quantity_patterns:
            match = re.search(pattern, natural_query)
            if match:
                found_quantity = True
                # 提取数字
                number_match = re.search(r'(\d+)', match.group(0))
                if number_match:
                    extracted_number = int(number_match.group(1))
                break
        
        # 检测批量操作关键词
        bulk_keywords = [
            '批量', '大量', '很多', '大批',
            '插入', '删除', '更新', '查询',
            '生成', '创建', '添加'
        ]
        
        found_bulk_keyword = any(keyword in natural_query for keyword in bulk_keywords)
        
        # 如果找到数量且数量大于等于50，或者有明确的批量关键词，则认为是批量操作
        is_bulk = (found_quantity and extracted_number >= 50) or found_bulk_keyword
        
        logger.info(f"Bulk detection: found_quantity={found_quantity}, number={extracted_number}, "
                   f"found_bulk_keyword={found_bulk_keyword}, is_bulk={is_bulk}")
        
        return is_bulk

# 创建全局实例
bulk_data_generator_service = BulkDataGeneratorService()

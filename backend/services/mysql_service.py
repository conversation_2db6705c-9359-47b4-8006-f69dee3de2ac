import pymysql
import asyncio
from typing import List, Dict, Any, Optional
import logging
import traceback
from contextlib import asynccontextmanager
from utils.exception_handler import log_exceptions, exception_context

logger = logging.getLogger(__name__)

class MySQLService:
    """MySQL数据库服务"""

    def __init__(self, host: str, port: int, user: str, password: str, database: str = None):
        self.host = host
        self.port = port
        self.user = user
        self.password = password
        self.database = database
        self.connection_pool = []
        self.max_connections = 10
        # 添加持久连接支持
        self._persistent_connection = None
        
    @log_exceptions(reraise=True)
    async def initialize(self):
        """初始化服务"""
        with exception_context("MySQL服务初始化"):
            # 测试连接
            await self.check_connection()
            logger.info("MySQL service initialized successfully")
    
    def _create_connection(self) -> pymysql.Connection:
        """创建数据库连接"""
        return pymysql.connect(
            host=self.host,
            port=self.port,
            user=self.user,
            password=self.password,
            database=self.database,
            charset='utf8mb4',
            autocommit=True,
            cursorclass=pymysql.cursors.DictCursor,
            # 添加超时配置，防止连接挂起
            connect_timeout=30,  # 连接超时30秒
            read_timeout=60,     # 读取超时60秒
            write_timeout=60,    # 写入超时60秒
            # 启用TCP keepalive，保持连接活跃，并设置时区为+8
            init_command="SET SESSION wait_timeout=300, time_zone='+08:00'"  # 设置会话超时为5分钟，时区为+8
        )
    
    @asynccontextmanager
    async def get_connection(self):
        """获取数据库连接（上下文管理器）"""
        connection = None
        try:
            # 在实际应用中，这里应该使用连接池
            connection = await asyncio.get_event_loop().run_in_executor(
                None, self._create_connection
            )
            yield connection
        except Exception as e:
            logger.error(f"Database connection error: {str(e)}")
            raise
        finally:
            if connection:
                connection.close()
    
    async def check_connection(self) -> bool:
        """检查数据库连接"""
        try:
            async with self.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute("SELECT 1")
                    cursor.fetchone()
            return True
        except Exception as e:
            logger.error(f"Connection check failed: {str(e)}")
            return False

    async def create_persistent_connection(self) -> bool:
        """创建持久连接用于抓包"""
        try:
            if self._persistent_connection:
                # 检查现有连接是否有效
                try:
                    with self._persistent_connection.cursor() as cursor:
                        cursor.execute("SELECT 1")
                        cursor.fetchone()
                    logger.info("Existing persistent connection is valid")
                    return True
                except Exception as e:
                    # 现有连接无效，关闭并重新创建
                    logger.warning(f"MySQL持久连接测试失败: {str(e)}")
                    try:
                        self._persistent_connection.close()
                    except Exception as e:

                        logger.error(f"操作失败: {type(e).__name__}: {str(e)}")

                        logger.error(f"异常堆栈跟踪: {traceback.format_exc()}")
                    self._persistent_connection = None

            # 创建新的持久连接
            self._persistent_connection = await asyncio.get_event_loop().run_in_executor(
                None, self._create_connection
            )

            # 测试连接
            with self._persistent_connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                cursor.fetchone()

            logger.info("Persistent MySQL connection created successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to create persistent connection: {str(e)}")
            self._persistent_connection = None
            return False

    async def create_fresh_connection(self) -> bool:
        """创建全新的连接用于抓包（确保能捕获握手包）"""
        try:
            # 强制关闭现有连接
            if self._persistent_connection:
                try:
                    self._persistent_connection.close()
                except Exception as e:
                    logger.debug(f"Error closing existing connection: {e}")
                self._persistent_connection = None

            # 等待一小段时间确保连接完全关闭
            await asyncio.sleep(0.5)

            # 创建全新的连接
            self._persistent_connection = await asyncio.get_event_loop().run_in_executor(
                None, self._create_connection
            )

            # 测试连接
            with self._persistent_connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                cursor.fetchone()

            logger.info("Fresh MySQL connection created successfully for packet capture")
            return True

        except Exception as e:
            logger.error(f"Failed to create fresh connection: {str(e)}")
            self._persistent_connection = None
            return False

    @log_exceptions(reraise=False)  # 关闭连接失败不应该抛出异常
    async def close_persistent_connection(self):
        """关闭持久连接"""
        with exception_context("关闭MySQL持久连接", reraise=False):
            if self._persistent_connection:
                self._persistent_connection.close()
                self._persistent_connection = None
                logger.info("Persistent MySQL connection closed")

    async def force_close_connection(self):
        """强制关闭连接（确保能捕获挥手包）"""
        try:
            if self._persistent_connection:
                logger.info("Force closing MySQL connection for packet capture")

                # 执行一个简单查询确保连接活跃
                try:
                    with self._persistent_connection.cursor() as cursor:
                        cursor.execute("SELECT 1")
                        cursor.fetchone()
                    logger.debug("Connection is active before closing")
                except Exception as e:
                    logger.debug(f"Connection test failed before closing: {e}")

                # 强制关闭连接
                self._persistent_connection.close()
                self._persistent_connection = None

                # 等待一小段时间确保挥手包被发送
                await asyncio.sleep(0.5)

                logger.info("MySQL connection force closed successfully")
            else:
                logger.warning("No persistent connection to close")

        except Exception as e:
            logger.error(f"Error during force close: {str(e)}")
            self._persistent_connection = None
    
    @log_exceptions(reraise=True)
    async def execute_query(self, sql: str, params: tuple = None) -> Dict[str, Any]:
        """执行SQL查询"""
        with exception_context(f"执行SQL查询: {sql[:100]}..."):
            logger.debug(f"Executing SQL: {sql}")

            if params:
                # 带参数的单条SQL语句
                return await self._execute_single_query(sql, params)
            else:
                # 处理多条SQL语句
                sql_statements = [stmt.strip() for stmt in sql.split(';') if stmt.strip()]

                if len(sql_statements) == 1:
                    # 单条SQL语句
                    return await self._execute_single_query(sql_statements[0])
                else:
                    # 多条SQL语句
                    return await self._execute_multiple_queries(sql_statements)

    async def execute_query_with_persistent_connection(self, sql: str, params: tuple = None) -> Dict[str, Any]:
        """使用持久连接执行SQL查询（用于抓包场景）"""
        try:
            if not self._persistent_connection:
                raise Exception("No persistent connection available. Call create_persistent_connection first.")

            logger.debug(f"Executing SQL with persistent connection: {sql}")

            if params:
                # 带参数的单条SQL语句
                return await self._execute_single_query_persistent(sql, params)
            else:
                # 处理多条SQL语句
                sql_statements = [stmt.strip() for stmt in sql.split(';') if stmt.strip()]

                if len(sql_statements) == 1:
                    # 单条SQL语句
                    return await self._execute_single_query_persistent(sql_statements[0])
                else:
                    # 多条SQL语句
                    return await self._execute_multiple_queries_persistent(sql_statements)

        except Exception as e:
            logger.error(f"Persistent SQL execution error: {str(e)}")
            raise Exception(f"Persistent SQL execution failed: {str(e)}")

    async def execute_query_for_capture(self, sql: str, params: tuple = None) -> Dict[str, Any]:
        """专门用于抓包的SQL执行方法（包含完整的连接生命周期）"""
        try:
            logger.info("Starting SQL execution for packet capture")

            # 1. 创建全新连接（确保捕获握手包）
            if not await self.create_fresh_connection():
                raise Exception("Failed to create fresh connection for packet capture")

            # 2. 等待一小段时间确保连接建立完成
            await asyncio.sleep(0.8) # 增加延迟，确保抓包工具准备就绪

            # 3. 执行SQL查询
            logger.info(f"Executing SQL for capture: {sql}")
            result = await self.execute_query_with_persistent_connection(sql, params)

            # 4. 等待一小段时间确保查询完成
            await asyncio.sleep(0.8) # 增加延迟，确保数据包被完全捕获

            logger.info("SQL execution for packet capture completed")
            return result

        except Exception as e:
            logger.error(f"SQL execution for capture failed: {str(e)}")
            raise

    async def _execute_single_query(self, sql: str, params: tuple = None) -> Dict[str, Any]:
        """执行单条SQL查询"""
        async with self.get_connection() as conn:
            with conn.cursor() as cursor:
                # 执行查询
                if params:
                    affected_rows = cursor.execute(sql, params)
                else:
                    affected_rows = cursor.execute(sql)

                # 判断查询类型
                sql_upper = sql.strip().upper()

                if sql_upper.startswith('SELECT') or sql_upper.startswith('SHOW') or sql_upper.startswith('DESCRIBE'):
                    # 查询操作
                    results = cursor.fetchall()
                    return {
                        'type': 'query',
                        'data': results,
                        'row_count': len(results),
                        'columns': [desc[0] for desc in cursor.description] if cursor.description else []
                    }
                else:
                    # 修改操作（INSERT, UPDATE, DELETE, FLUSH等）
                    result = {
                        'type': 'modification',
                        'affected_rows': affected_rows,
                        'message': f"Query executed successfully. {affected_rows} rows affected."
                    }

                    # 如果是INSERT操作，返回插入的ID
                    if sql_upper.startswith('INSERT'):
                        result['insert_id'] = cursor.lastrowid

                    return result

    async def _execute_single_query_persistent(self, sql: str, params: tuple = None) -> Dict[str, Any]:
        """使用持久连接执行单条SQL查询"""
        try:
            if not self._persistent_connection:
                raise Exception("No persistent connection available")

            # 在线程池中执行，避免阻塞
            def execute_sync():
                with self._persistent_connection.cursor() as cursor:
                    # 执行查询
                    if params:
                        affected_rows = cursor.execute(sql, params)
                    else:
                        affected_rows = cursor.execute(sql)

                    # 判断查询类型
                    sql_upper = sql.strip().upper()

                    if sql_upper.startswith('SELECT') or sql_upper.startswith('SHOW') or sql_upper.startswith('DESCRIBE'):
                        # 查询操作
                        results = cursor.fetchall()
                        return {
                            'type': 'query',
                            'data': results,
                            'row_count': len(results),
                            'columns': [desc[0] for desc in cursor.description] if cursor.description else []
                        }
                    else:
                        # 修改操作（INSERT, UPDATE, DELETE, FLUSH等）
                        result = {
                            'type': 'modification',
                            'affected_rows': affected_rows,
                            'message': f"Query executed successfully. {affected_rows} rows affected."
                        }

                        # 如果是INSERT操作，返回插入的ID
                        if sql_upper.startswith('INSERT'):
                            result['insert_id'] = cursor.lastrowid

                        return result

            return await asyncio.get_event_loop().run_in_executor(None, execute_sync)

        except Exception as e:
            logger.error(f"Persistent single query execution error: {str(e)}")
            raise

    async def _execute_multiple_queries_persistent(self, sql_statements: List[str]) -> Dict[str, Any]:
        """使用持久连接执行多条SQL语句"""
        try:
            if not self._persistent_connection:
                raise Exception("No persistent connection available")

            results = []
            total_affected_rows = 0

            for sql in sql_statements:
                if sql.strip():
                    result = await self._execute_single_query_persistent(sql.strip())
                    results.append({
                        'sql': sql.strip(),
                        'result': result
                    })

                    if result.get('type') == 'modification':
                        total_affected_rows += result.get('affected_rows', 0)

            return {
                'type': 'multiple_queries',
                'results': results,
                'total_queries': len(results),
                'total_affected_rows': total_affected_rows,
                'message': f"Executed {len(results)} queries successfully"
            }

        except Exception as e:
            logger.error(f"Persistent multiple queries execution error: {str(e)}")
            raise

    async def get_databases(self) -> List[str]:
        """获取数据库列表"""
        try:
            result = await self.execute_query("SHOW DATABASES")
            return [db['Database'] for db in result['data']]
        except Exception as e:
            logger.error(f"Failed to get databases: {str(e)}")
            raise
    
    async def get_tables(self, database: str = None) -> List[Dict[str, Any]]:
        """获取表列表"""
        try:
            db = database or self.database
            if not db:
                raise Exception("No database specified")
            
            result = await self.execute_query(f"SHOW TABLES FROM `{db}`")
            tables = []
            
            for table_info in result['data']:
                table_name = list(table_info.values())[0]  # 获取表名
                
                # 获取表的详细信息
                table_detail = await self.execute_query(
                    f"SELECT TABLE_COMMENT, TABLE_ROWS, DATA_LENGTH "
                    f"FROM information_schema.TABLES "
                    f"WHERE TABLE_SCHEMA = '{db}' AND TABLE_NAME = '{table_name}'"
                )
                
                detail = table_detail['data'][0] if table_detail['data'] else {}
                
                tables.append({
                    'name': table_name,
                    'comment': detail.get('TABLE_COMMENT', ''),
                    'rows': detail.get('TABLE_ROWS', 0),
                    'size': detail.get('DATA_LENGTH', 0)
                })
            
            return tables
            
        except Exception as e:
            logger.error(f"Failed to get tables: {str(e)}")
            raise
    
    async def get_table_structure(self, table_name: str, database: str = None) -> Dict[str, Any]:
        """获取表结构"""
        try:
            db = database or self.database
            if not db:
                raise Exception("No database specified")
            
            # 获取列信息
            columns_result = await self.execute_query(f"DESCRIBE `{db}`.`{table_name}`")
            
            # 获取索引信息
            indexes_result = await self.execute_query(f"SHOW INDEX FROM `{db}`.`{table_name}`")
            
            return {
                'table_name': table_name,
                'database': db,
                'columns': columns_result['data'],
                'indexes': indexes_result['data']
            }
            
        except Exception as e:
            logger.error(f"Failed to get table structure: {str(e)}")
            raise
    
    async def get_database_schema(self, database: str = None) -> Dict[str, Any]:
        """获取数据库架构信息"""
        try:
            db = database or self.database
            if not db:
                raise Exception("No database specified")
            
            # 获取所有表
            tables = await self.get_tables(db)
            
            # 获取每个表的结构
            schema = {
                'database': db,
                'tables': {}
            }
            
            for table in tables:
                table_structure = await self.get_table_structure(table['name'], db)
                schema['tables'][table['name']] = {
                    'info': table,
                    'structure': table_structure
                }
            
            return schema
            
        except Exception as e:
            logger.error(f"Failed to get database schema: {str(e)}")
            raise
    
    async def _execute_multiple_queries(self, sql_statements: List[str]) -> Dict[str, Any]:
        """执行多条SQL查询"""
        results = []
        total_affected_rows = 0

        async with self.get_connection() as conn:
            with conn.cursor() as cursor:
                for i, sql in enumerate(sql_statements):
                    try:
                        logger.info(f"Executing statement {i+1}/{len(sql_statements)}: {sql}")
                        affected_rows = cursor.execute(sql)

                        sql_upper = sql.strip().upper()
                        if sql_upper.startswith('SELECT') or sql_upper.startswith('SHOW') or sql_upper.startswith('DESCRIBE'):
                            # 查询操作
                            data = cursor.fetchall()
                            results.append({
                                'sql': sql,
                                'type': 'query',
                                'data': data,
                                'row_count': len(data)
                            })
                        else:
                            # 修改操作
                            total_affected_rows += affected_rows
                            results.append({
                                'sql': sql,
                                'type': 'modification',
                                'affected_rows': affected_rows,
                                'message': f"Statement executed successfully. {affected_rows} rows affected."
                            })
                    except Exception as e:
                        results.append({
                            'sql': sql,
                            'type': 'error',
                            'error': str(e)
                        })

        return {
            'type': 'multiple',
            'statements': len(sql_statements),
            'results': results,
            'total_affected_rows': total_affected_rows,
            'message': f"Executed {len(sql_statements)} statements successfully."
        }

    async def close(self):
        """关闭服务"""
        # 在实际应用中，这里应该关闭连接池
        logger.info("MySQL service closed")

# 注意：不创建默认实例，避免启动时连接外部数据库
# mysql_service 实例应该在需要时动态创建

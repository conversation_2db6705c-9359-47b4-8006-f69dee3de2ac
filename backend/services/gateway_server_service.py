"""
网关服务器管理服务
"""

import logging
import asyncio
import json
from typing import List, Optional, Dict, Any, Tu<PERSON>
from datetime import datetime
import time
from pathlib import Path
import pymysql
import os

from models.gateway_server import GatewayServer, GatewayConnectionTest, GatewayFileTransfer
from utils.config import Config

logger = logging.getLogger(__name__)

class GatewayServerService:
    """网关服务器管理服务"""
    
    def __init__(self):
        # 使用项目的MySQL配置
        self.mysql_config = {
            'host': Config.MYSQL_HOST,
            'port': Config.MYSQL_PORT,
            'user': Config.MYSQL_USER,
            'password': Config.MYSQL_PASSWORD,
            'database': Config.MYSQL_DATABASE,
            'charset': 'utf8mb4',
            'autocommit': True
        }
    
    def _get_connection(self):
        """获取MySQL连接"""
        return pymysql.connect(**self.mysql_config)
    
    async def create_gateway(self, gateway: GatewayServer) -> int:
        """创建网关服务器配置"""
        
        # 如果设置为默认，先取消其他默认配置
        if gateway.is_default:
            await self._clear_default_gateway()
        
        conn = self._get_connection()
        try:
            cursor = conn.cursor()
            
            sql = '''
                INSERT INTO gateway_servers (
                    name, host, port, username, password, upload_path, description,
                    is_active, is_default, connection_timeout, max_connections, 
                    gateway_type, proxy_enabled, proxy_host, proxy_port,
                    kafka_enabled, kafka_host, kafka_port, kafka_topic
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            '''
            
            cursor.execute(sql, (
                gateway.name, gateway.host, gateway.port, gateway.username, 
                gateway.password, gateway.upload_path, gateway.description,
                gateway.is_active, gateway.is_default, gateway.connection_timeout,
                gateway.max_connections, gateway.gateway_type, gateway.proxy_enabled,
                gateway.proxy_host, gateway.proxy_port,
                gateway.kafka_enabled, gateway.kafka_host, gateway.kafka_port, gateway.kafka_topic
            ))
            
            gateway_id = cursor.lastrowid
            
            logger.info(f"Created gateway server: {gateway.name} (ID: {gateway_id})")
            return gateway_id
            
        finally:
            conn.close()
    
    async def get_gateway(self, gateway_id: int) -> Optional[GatewayServer]:
        """获取指定网关服务器配置"""
        
        conn = self._get_connection()
        try:
            cursor = conn.cursor(pymysql.cursors.DictCursor)
            
            cursor.execute('SELECT * FROM gateway_servers WHERE id = %s', (gateway_id,))
            row = cursor.fetchone()
            
            if row:
                return GatewayServer.from_dict(row)
            return None
            
        finally:
            conn.close()
    
    async def list_gateways(
        self, 
        search_ip: Optional[str] = None,
        page: int = 1,
        page_size: int = 20
    ) -> Tuple[List[GatewayServer], int]:
        """获取网关服务器列表（支持IP搜索和分页）"""
        
        # 构建查询条件
        where_clause = "WHERE 1=1"
        params = []
        
        if search_ip:
            where_clause += " AND host LIKE %s"
            params.append(f"%{search_ip}%")
        
        conn = self._get_connection()
        try:
            cursor = conn.cursor()
            
            # 计算总数
            cursor.execute(f"SELECT COUNT(*) FROM gateway_servers {where_clause}", params)
            total = cursor.fetchone()[0]
            
            # 获取分页数据
            offset = (page - 1) * page_size
            cursor = conn.cursor(pymysql.cursors.DictCursor)
            
            sql = f'''
                SELECT * FROM gateway_servers {where_clause}
                ORDER BY is_default DESC, name ASC
                LIMIT %s OFFSET %s
            '''
            
            cursor.execute(sql, params + [page_size, offset])
            rows = cursor.fetchall()
            
            gateways = [GatewayServer.from_dict(row) for row in rows]
            
            return gateways, total
            
        finally:
            conn.close()
    
    async def update_gateway(self, gateway: GatewayServer) -> bool:
        """更新网关服务器配置"""
        
        # 如果设置为默认，先取消其他默认配置
        if gateway.is_default:
            await self._clear_default_gateway(exclude_id=gateway.id)
        
        conn = self._get_connection()
        try:
            cursor = conn.cursor()
            
            sql = '''
                UPDATE gateway_servers SET
                    name = %s, host = %s, port = %s, username = %s, password = %s,
                    upload_path = %s, description = %s, is_active = %s, is_default = %s,
                    connection_timeout = %s, max_connections = %s, gateway_type = %s,
                    proxy_enabled = %s, proxy_host = %s, proxy_port = %s,
                    kafka_enabled = %s, kafka_host = %s, kafka_port = %s, kafka_topic = %s,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = %s
            '''
            
            cursor.execute(sql, (
                gateway.name, gateway.host, gateway.port, gateway.username,
                gateway.password, gateway.upload_path, gateway.description,
                gateway.is_active, gateway.is_default, gateway.connection_timeout,
                gateway.max_connections, gateway.gateway_type, gateway.proxy_enabled,
                gateway.proxy_host, gateway.proxy_port,
                gateway.kafka_enabled, gateway.kafka_host, gateway.kafka_port, gateway.kafka_topic,
                gateway.id
            ))
            
            success = cursor.rowcount > 0
            
            if success:
                logger.info(f"Updated gateway server: {gateway.name} (ID: {gateway.id})")
            
            return success
            
        finally:
            conn.close()
    
    async def delete_gateway(self, gateway_id: int) -> bool:
        """删除网关服务器配置"""
        
        conn = self._get_connection()
        try:
            cursor = conn.cursor()
            
            cursor.execute('DELETE FROM gateway_servers WHERE id = %s', (gateway_id,))
            success = cursor.rowcount > 0
            
            if success:
                logger.info(f"Deleted gateway server ID: {gateway_id}")
            
            return success
            
        finally:
            conn.close()
    
    async def get_default_gateway(self) -> Optional[GatewayServer]:
        """获取默认网关服务器"""
        
        conn = self._get_connection()
        try:
            cursor = conn.cursor(pymysql.cursors.DictCursor)
            
            cursor.execute('SELECT * FROM gateway_servers WHERE is_default = 1 AND is_active = 1 LIMIT 1')
            row = cursor.fetchone()
            
            if row:
                return GatewayServer.from_dict(row)
            return None
            
        finally:
            conn.close()
    
    async def set_default_gateway(self, gateway_id: int) -> bool:
        """设置默认网关服务器"""
        
        conn = self._get_connection()
        try:
            cursor = conn.cursor()
            
            # 先取消所有默认设置
            cursor.execute('UPDATE gateway_servers SET is_default = 0')
            
            # 设置新的默认
            cursor.execute('UPDATE gateway_servers SET is_default = 1 WHERE id = %s', (gateway_id,))
            
            success = cursor.rowcount > 0
            
            if success:
                logger.info(f"Set default gateway server ID: {gateway_id}")
            
            return success
            
        finally:
            conn.close()
    
    async def test_connection(self, gateway: GatewayServer) -> GatewayConnectionTest:
        """测试网关服务器连接"""
        start_time = time.time()
        
        try:
            # 简化的连接测试（不使用paramiko，避免依赖问题）
            # 在实际部署时，需要安装paramiko: pip install paramiko
            
            # 模拟连接测试
            await asyncio.sleep(0.1)  # 模拟网络延迟
            
            response_time = (time.time() - start_time) * 1000  # 转换为毫秒
            
            # 简单的连接性检查（ping测试）
            import subprocess
            result = subprocess.run(['ping', '-c', '1', '-W', '1000', gateway.host], 
                                  capture_output=True, text=True)
            
            # 记录连接测试日志到数据库
            test_result = None
            if result.returncode == 0:
                test_result = GatewayConnectionTest(
                    success=True,
                    message="连接测试成功（基础网络连通性已验证）",
                    response_time=response_time,
                    upload_test=None,  # 需要SSH连接才能测试
                    disk_space=None,
                    server_info={"test_method": "ping"}
                )
            else:
                test_result = GatewayConnectionTest(
                    success=False,
                    message="网络连接失败，无法ping通目标主机",
                    response_time=response_time
                )
            
            # 保存测试日志到数据库
            if gateway.id:
                await self._save_connection_test_log(gateway.id, test_result)
            
            return test_result
                
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            error_msg = str(e)
            
            logger.error(f"Gateway connection test failed for {gateway.host}: {error_msg}")
            
            test_result = GatewayConnectionTest(
                success=False,
                message=f"连接测试失败: {error_msg}",
                response_time=response_time
            )
            
            # 保存测试日志到数据库
            if gateway.id:
                await self._save_connection_test_log(gateway.id, test_result)
            
            return test_result
    
    async def search_by_ip(self, ip_pattern: str) -> List[GatewayServer]:
        """根据IP模式搜索网关服务器"""
        gateways, _ = await self.list_gateways(search_ip=ip_pattern)
        return gateways
    
    async def get_connection_stats(self) -> Dict[str, Any]:
        """获取连接统计信息"""
        
        conn = self._get_connection()
        try:
            cursor = conn.cursor()
            
            # 总数统计
            cursor.execute('SELECT COUNT(*) FROM gateway_servers')
            total = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM gateway_servers WHERE is_active = 1')
            active = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM gateway_servers WHERE is_default = 1')
            default = cursor.fetchone()[0]
            
            # 按类型统计
            cursor.execute('SELECT gateway_type, COUNT(*) FROM gateway_servers GROUP BY gateway_type')
            type_stats = {row[0]: row[1] for row in cursor.fetchall()}
            
            return {
                'total_gateways': total,
                'active_gateways': active,
                'default_gateways': default,
                'type_statistics': type_stats
            }
            
        finally:
            conn.close()
    
    async def _clear_default_gateway(self, exclude_id: Optional[int] = None):
        """清除默认网关设置"""
        conn = self._get_connection()
        try:
            cursor = conn.cursor()
            
            if exclude_id:
                cursor.execute('UPDATE gateway_servers SET is_default = 0 WHERE id != %s', (exclude_id,))
            else:
                cursor.execute('UPDATE gateway_servers SET is_default = 0')
                
        finally:
            conn.close()
    
    async def _save_connection_test_log(self, gateway_id: int, test_result: GatewayConnectionTest):
        """保存连接测试日志到数据库"""
        conn = self._get_connection()
        try:
            cursor = conn.cursor()
            
            sql = '''
                INSERT INTO gateway_connection_logs (
                    gateway_id, test_success, test_message, response_time,
                    upload_test, disk_space, server_info
                ) VALUES (%s, %s, %s, %s, %s, %s, %s)
            '''
            
            server_info_json = json.dumps(test_result.server_info) if test_result.server_info else None
            
            cursor.execute(sql, (
                gateway_id, test_result.success, test_result.message,
                test_result.response_time, test_result.upload_test,
                test_result.disk_space, server_info_json
            ))
            
        except Exception as e:
            logger.error(f"Failed to save connection test log: {e}")
        finally:
            conn.close()
    
    async def export_gateways(self) -> List[Dict[str, Any]]:
        """导出网关配置（不包含密码）"""
        gateways, _ = await self.list_gateways()
        
        export_data = []
        for gateway in gateways:
            data = gateway.to_dict()
            # 移除敏感信息
            data.pop('password', None)
            data.pop('id', None)
            export_data.append(data)
        
        return export_data
    
    async def import_gateways(self, gateways_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """导入网关配置"""
        imported = 0
        failed = 0
        errors = []
        
        for data in gateways_data:
            try:
                # 检查必需字段
                required_fields = ['name', 'host', 'username']
                if not all(field in data for field in required_fields):
                    raise ValueError(f"Missing required fields: {required_fields}")
                
                # 设置默认密码（需要用户后续修改）
                if 'password' not in data:
                    data['password'] = 'changeme123'
                
                # 创建网关对象
                gateway = GatewayServer.from_dict(data)
                await self.create_gateway(gateway)
                imported += 1
                
            except Exception as e:
                failed += 1
                errors.append(f"Failed to import {data.get('name', 'Unknown')}: {str(e)}")
                logger.error(f"Failed to import gateway: {e}")
        
        return {
            'imported': imported,
            'failed': failed,
            'errors': errors
        }

# 全局服务实例
gateway_server_service = GatewayServerService()

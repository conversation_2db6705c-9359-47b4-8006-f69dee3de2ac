"""
PostgreSQL异步抓包服务 - 提供PostgreSQL数据库的异步抓包功能
"""
import asyncio
import logging
from typing import Dict, Any, Optional
from datetime import datetime

from services.postgres_local_packet_capture_service import PostgresLocalPacketCaptureService
from services.postgres_service import PostgresService
from services.database_config_service import database_config_service

logger = logging.getLogger(__name__)

class PostgresAsyncCaptureService:
    """PostgreSQL异步抓包服务"""
    
    def __init__(self):
        """初始化PostgreSQL异步抓包服务"""
        self.packet_service = PostgresLocalPacketCaptureService()
        self.postgres_service = None
        self.current_capture_file = None
        self.is_capturing = False
        logger.info("PostgreSQL Async Capture Service initialized")
    
    async def configure_postgres_connection(self, config_id: int):
        """配置PostgreSQL连接"""
        try:
            config = await database_config_service.get_config(config_id)
            if not config:
                raise Exception(f"Database config not found: {config_id}")
            
            # 创建PostgreSQL服务实例
            self.postgres_service = PostgresService(
                host=config.host,
                port=config.port,
                user=config.user,
                password=config.password,
                database=config.database_name
            )
            
            # 初始化连接
            await self.postgres_service.initialize()
            
            # 测试连接
            is_connected = await self.postgres_service.check_connection()
            if not is_connected:
                raise Exception("PostgreSQL database connection failed")
            
            logger.info(f"PostgreSQL connection configured: {config.host}:{config.port}")
            
        except Exception as e:
            logger.error(f"Failed to configure PostgreSQL connection: {str(e)}")
            raise
    
    async def execute_sql_with_async_capture(
        self,
        sql_query: str,
        config_id: int,
        capture_duration: int = 10,
        use_c_executor: bool = False
    ) -> Dict[str, Any]:
        """执行PostgreSQL SQL查询并进行异步抓包

        抓包顺序严格为：
        1) 开始抓包 (step=start)
        2) 建立连接 (step=connect)
        3) 执行语句 (step=execute)
        4) 断开连接 (step=disconnect)
        5) 停止抓包 (step=stop)
        """
        try:
            logger.info(f"Starting PostgreSQL async capture for SQL: {sql_query}")

            # 获取数据库配置（仅读取，不建立连接）
            config = await database_config_service.get_config(config_id)
            if not config:
                raise Exception(f"Database config not found: {config_id}")

            # 1) 开始抓包（开始时就落盘一个带step的文件名，便于核对时序）
            capture_file = await self.packet_service.start_capture(
                target_host=config.host,
                target_port=config.port,
                step_identifier="start"
            )
            self.current_capture_file = capture_file
            self.is_capturing = True
            logger.info(f"PostgreSQL packet capture started: {capture_file}")

            # 等待抓包器稳定
            await asyncio.sleep(1.5)

            # 2) 建立连接（必须在抓包启动之后）
            await self.configure_postgres_connection(config_id)
            await asyncio.sleep(0.3)

            # 3) 执行语句
            if use_c_executor:
                # 当前项目C执行器为GaussDB适配，不强行复用，先抛出不支持
                raise Exception("use_c_executor for PostgreSQL is not supported in this workflow")
            else:
                # 使用抓包友好的执行：新建连接 -> 执行 -> 等待 -> 断开
                # 创建全新连接，确保握手被抓到
                created = await self.postgres_service.create_fresh_connection()
                if not created:
                    raise Exception("Failed to create fresh PostgreSQL connection for capture")

                # 用持久连接执行查询，确保走同一条连接
                execution_result = await self.postgres_service.execute_sql_query_with_persistent_connection(sql_query)

                # 给网络留一点时间写完响应
                await asyncio.sleep(0.5)

            # 4) 断开连接（确保挥手被抓到）
            await self.postgres_service.force_close_connection()

            # 再等待少许，收尾挥手包
            await asyncio.sleep(0.6)

            # 额外保留窗口
            if capture_duration and capture_duration > 0:
                await asyncio.sleep(max(0, capture_duration - 3))

            # 5) 停止抓包
            final_packet_file = await self.packet_service.stop_capture()
            self.is_capturing = False
            
            logger.info(f"PostgreSQL async capture completed: {final_packet_file}")
            
            return {
                "success": True,
                "sql_query": sql_query,
                "execution_result": execution_result,
                "packet_file": final_packet_file,
                "capture_duration": capture_duration,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"PostgreSQL async capture failed: {str(e)}")
            
            # 确保停止抓包
            if self.is_capturing:
                try:
                    await self.packet_service.stop_capture()
                    self.is_capturing = False
                except Exception as stop_error:
                    logger.error(f"停止PostgreSQL抓包失败: {type(stop_error).__name__}: {stop_error}")
                    # 即使停止失败也要重置状态
                    self.is_capturing = False
            
            return {
                "success": False,
                "sql_query": sql_query,
                "error": str(e),
                "packet_file": None,
                "timestamp": datetime.now().isoformat()
            }
    
    async def start_capture_session(self, config_id: int) -> Dict[str, Any]:
        """启动抓包会话"""
        try:
            if self.is_capturing:
                return {
                    "success": False,
                    "error": "Capture session already active",
                    "current_file": self.current_capture_file
                }
            
            # 获取数据库配置
            config = await database_config_service.get_config(config_id)
            
            # 启动抓包
            capture_file = await self.packet_service.start_capture(
                target_host=config.host,
                target_port=config.port
            )
            self.current_capture_file = capture_file
            self.is_capturing = True
            
            logger.info(f"PostgreSQL capture session started: {capture_file}")
            
            return {
                "success": True,
                "capture_file": capture_file,
                "message": "PostgreSQL capture session started"
            }
            
        except Exception as e:
            logger.error(f"Failed to start PostgreSQL capture session: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def stop_capture_session(self) -> Dict[str, Any]:
        """停止抓包会话"""
        try:
            if not self.is_capturing:
                return {
                    "success": False,
                    "error": "No active capture session"
                }
            
            # 停止抓包
            final_packet_file = await self.packet_service.stop_capture()
            self.is_capturing = False
            self.current_capture_file = None
            
            logger.info(f"PostgreSQL capture session stopped: {final_packet_file}")
            
            return {
                "success": True,
                "packet_file": final_packet_file,
                "message": "PostgreSQL capture session stopped"
            }
            
        except Exception as e:
            logger.error(f"Failed to stop PostgreSQL capture session: {str(e)}")
            self.is_capturing = False
            self.current_capture_file = None
            return {
                "success": False,
                "error": str(e)
            }
    
    def get_capture_status(self) -> Dict[str, Any]:
        """获取抓包状态"""
        return {
            "is_capturing": self.is_capturing,
            "current_file": self.current_capture_file,
            "service_status": "active"
        }

# 创建全局服务实例
postgres_async_capture_service = PostgresAsyncCaptureService()

"""
GaussDB异步抓包服务
提供GaussDB数据库的异步抓包功能，支持SQL执行和数据包捕获
"""

import asyncio
import logging
from typing import Dict, Any, Optional
from datetime import datetime

from services.gaussdb_service_v2 import GaussDBServiceV2
from services.gaussdb_packet_capture_service import gaussdb_packet_service
from services.database_config_service import database_config_service
from services.capture_file_service import capture_file_service
from utils.execution_logger import ExecutionLogger

logger = logging.getLogger(__name__)


class GaussDBAsyncCaptureService:
    """GaussDB异步抓包服务"""
    
    def __init__(self):
        self.gaussdb_service: Optional[GaussDBServiceV2] = None
        self.packet_service = gaussdb_packet_service
        self.is_capturing = False
        
    async def configure_gaussdb_connection(self, config_id: int):
        """配置GaussDB连接"""
        try:
            # 保存配置ID，用于后续保存抓包文件时使用
            self._current_config_id = config_id

            # 获取数据库配置
            config = await database_config_service.get_config(config_id)
            if not config:
                raise Exception(f"Database config not found: {config_id}")

            if config.database_type.lower() != 'gaussdb':
                raise Exception(f"Expected GaussDB config, got: {config.database_type}")

            # 初始化GaussDB服务
            self.gaussdb_service = GaussDBServiceV2(
                host=config.host,
                port=config.port,
                user=config.user,
                password=config.password,
                database=config.database_name,
                use_c_executor=False  # 默认使用Python执行器
            )

            await self.gaussdb_service.initialize()
            logger.info(f"GaussDB async capture service configured for {config.host}:{config.port}")

        except Exception as e:
            logger.error(f"Failed to configure GaussDB connection: {str(e)}")
            raise
    
    async def execute_sql_with_async_capture(
        self,
        sql_query: str,
        config_id: int,
        capture_duration: int = 10,
        use_c_executor: bool = False,
        task_id: str = None,
        execution_id: str = None
    ) -> Dict[str, Any]:
        """执行GaussDB SQL查询并进行异步抓包"""
        try:
            logger.info(f"Starting GaussDB async capture for SQL: {sql_query}")

            # 配置数据库连接
            await self.configure_gaussdb_connection(config_id)

            # 获取数据库配置
            config = await database_config_service.get_config(config_id)

            # 设置抓包服务的执行上下文（如果提供了上下文信息）
            if task_id or execution_id:
                self.packet_service.set_execution_context(task_id, execution_id)
                logger.info(f"🔍 设置GaussDB抓包服务执行上下文: task_id={task_id}, execution_id={execution_id}")
            else:
                # 如果没有提供上下文，生成一个唯一的任务ID并设置
                generated_task_id = f"gaussdb_capture_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"
                self.packet_service.set_execution_context(generated_task_id, None)
                logger.info(f"🔍 生成GaussDB抓包服务任务ID: {generated_task_id}")

            # 强制重置抓包服务状态（防止之前的测试留下的状态）
            try:
                logger.info("🔧 开始强制重置GaussDB抓包服务状态")
                # 强制重置本地tcpdump服务状态
                self.packet_service.local_tcpdump_service.is_capturing = False
                self.packet_service.local_tcpdump_service.tcpdump_process = None
                self.packet_service.local_tcpdump_service.current_file = None
                await self.packet_service.local_tcpdump_service.force_stop()
                logger.info("🔧 GaussDB抓包服务状态强制重置完成")
            except Exception as reset_error:
                logger.warning(f"重置抓包服务状态失败: {reset_error}")
                # 即使重置失败，也要强制设置状态
                self.packet_service.local_tcpdump_service.is_capturing = False

            # 启动抓包
            logger.info(f"🔍 启动GaussDB抓包服务")
            capture_file = await self.packet_service.start_capture(
                target_host=config.host,
                target_port=config.port
            )

            if not capture_file:
                error_msg = "Failed to start GaussDB packet capture"
                logger.error(f"GaussDB抓包服务启动失败: {error_msg}")
                raise Exception(error_msg)

            self.is_capturing = True
            logger.info(f"GaussDB抓包已启动: {capture_file}")
            logger.info(f"✅ GaussDB抓包已启动: {capture_file}")

            # 等待抓包服务完全启动
            await asyncio.sleep(2)

            # 🔧 关键修复：确保在抓包期间建立新的数据库连接
            # 关闭现有的连接池和持久连接，强制建立新连接
            logger.info("🔧 为抓包场景准备数据库连接：关闭现有连接池")
            try:
                # 关闭连接池
                if hasattr(self.gaussdb_service, 'close_pool'):
                    self.gaussdb_service.close_pool()
                # 关闭持久连接
                if hasattr(self.gaussdb_service, 'close_persistent_connection'):
                    await self.gaussdb_service.close_persistent_connection()
                logger.info("🔧 现有连接已清理，将在抓包期间建立新连接")
            except Exception as cleanup_error:
                logger.warning(f"清理现有连接时出现警告: {cleanup_error}")

            # 短暂等待，确保连接完全关闭
            await asyncio.sleep(1)
            
            # 执行SQL查询
            execution_result = None
            try:
                if use_c_executor:
                    # 使用C语言执行器
                    self.gaussdb_service.use_c_executor = True
                    await self.gaussdb_service.initialize()

                    # 🔧 在抓包期间创建新的持久连接（确保能捕获连接建立过程）
                    logger.info("🔧 在抓包期间创建新的GaussDB C执行器连接")
                    connection_created = await self.gaussdb_service.create_persistent_connection()
                    if not connection_created:
                        raise Exception("Failed to create persistent GaussDB C executor connection")

                    try:
                        # 使用C执行器执行SQL
                        logger.info("🔧 使用新建立的连接执行SQL")
                        execution_result = await self.gaussdb_service.execute_sql_query_with_persistent_connection(sql_query)
                    finally:
                        # 🔧 在抓包期间关闭连接（确保能捕获连接关闭过程）
                        logger.info("🔧 在抓包期间关闭GaussDB C执行器连接")
                        await self.gaussdb_service.close_persistent_connection()
                        self.gaussdb_service.close_pool()
                        logger.info("GaussDB C执行器持久连接和连接池已清理")
                else:
                    # 🔧 使用Python执行器 - 在抓包期间建立新连接
                    logger.info("🔧 在抓包期间使用Python执行器执行SQL（将建立新连接）")
                    execution_result = await self.gaussdb_service.execute_sql_query(sql_query)

                logger.info(f"✅ GaussDB SQL执行完成")

            except Exception as e:
                error_str = str(e).lower()

                # 检查是否是测试用例的预期错误（约束违反等）
                is_expected_error = (
                    'violates not-null constraint' in error_str or
                    'violates unique constraint' in error_str or
                    'violates check constraint' in error_str or
                    'duplicate key value' in error_str or
                    'null value in column' in error_str or
                    'check constraint' in error_str
                )

                if is_expected_error:
                    logger.info(f"✅ GaussDB SQL执行完成 (预期的约束错误): {str(e)}")
                    execution_result = {
                        "error": str(e),
                        "success": False,
                        "expected_error": True,
                        "message": "约束违反 - 这是测试用例的预期行为"
                    }
                else:
                    logger.error(f"❌ GaussDB SQL执行失败: {str(e)}")
                    execution_result = {"error": str(e), "success": False}

            # 等待数据包捕获
            logger.info(f"🔍 等待数据包捕获 {capture_duration} 秒")
            await asyncio.sleep(capture_duration)
            
            # 停止抓包
            logger.info(f"🔍 停止GaussDB抓包")
            final_packet_file = await self.packet_service.stop_capture()
            self.is_capturing = False

            # 注意：抓包文件已经在 gaussdb_packet_capture_service.stop_capture() 中保存到数据库了
            # 这里不需要重复保存，避免文件路径问题
            if final_packet_file:
                logger.info(f"✅ GaussDB异步抓包完成，文件已自动保存到数据库: {final_packet_file}")
            else:
                logger.warning("⚠️ GaussDB异步抓包未生成文件（可能文件太小被删除）")

            logger.info(f"✅ GaussDB async capture completed: {final_packet_file}")

            return {
                "success": True,
                "sql_query": sql_query,
                "execution_result": execution_result,
                "packet_file": final_packet_file,
                "capture_duration": capture_duration,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"❌ GaussDB async capture failed: {str(e)}")
            
            # 确保停止抓包
            if self.is_capturing:
                try:
                    await self.packet_service.stop_capture()
                    self.is_capturing = False
                except Exception as stop_error:
                    logger.error(f"停止GaussDB抓包失败: {type(stop_error).__name__}: {stop_error}")
                    # 即使停止失败也要重置状态
                    self.is_capturing = False

            # 确保清理连接
            if self.gaussdb_service:
                try:
                    if hasattr(self.gaussdb_service, 'close_persistent_connection'):
                        await self.gaussdb_service.close_persistent_connection()
                    if hasattr(self.gaussdb_service, 'close_pool'):
                        self.gaussdb_service.close_pool()
                except Exception as cleanup_error:
                    logger.error(f"清理GaussDB连接失败: {type(cleanup_error).__name__}: {cleanup_error}")
                    # 清理失败不影响主流程
            
            return {
                "success": False,
                "sql_query": sql_query,
                "error": str(e),
                "packet_file": None,
                "timestamp": datetime.now().isoformat()
            }
    


    def get_capture_status(self) -> Dict[str, Any]:
        """获取抓包状态"""
        return {
            'is_capturing': self.is_capturing,
            'service_type': 'gaussdb_async',
            'packet_service_status': self.packet_service.get_capture_status()
        }


# 创建全局服务实例
gaussdb_async_capture_service = GaussDBAsyncCaptureService()

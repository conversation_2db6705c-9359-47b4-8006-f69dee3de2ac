"""
MongoDB数据包捕获服务 - 本地抓包
"""

import asyncio
import logging
import traceback
import os
from typing import Optional, Dict, Any
from datetime import datetime

from services.local_tcpdump_service import LocalTcpdumpService
from utils.local_network_utils import LocalNetworkUtils
from services.capture_file_service import capture_file_service
from models.capture_file import CaptureFileCreate

logger = logging.getLogger(__name__)

class MongoLocalPacketCaptureService:
    """MongoDB数据包捕获服务 - 本地抓包"""

    def __init__(self):
        self.is_capturing = False
        self.current_file: Optional[str] = None
        self.mongo_port = 27017  # MongoDB默认端口
        self.capture_dir = "captures"  # 本地抓包文件目录

        # 确保本地captures目录存在
        os.makedirs(self.capture_dir, exist_ok=True)

        # 本地tcpdump服务
        self.local_tcpdump_service = LocalTcpdumpService(self.capture_dir)

    async def start_local_capture(self, mongo_host: str = "**************", mongo_port: int = 27017) -> str:
        """
        启动本地MongoDB数据包捕获
        直接监控指定主机和端口的流量

        Args:
            mongo_host: MongoDB主机地址
            mongo_port: MongoDB端口

        Returns:
            str: 抓包文件路径
        """
        logger.info(f"Starting MongoDB packet capture - Target server: {mongo_host}:{mongo_port}")

        try:
            # 保存当前抓包配置，用于后续保存到数据库
            self._current_target_host = mongo_host
            self._current_target_port = mongo_port

            # 根据目标主机选择最合适的网络接口（本机使用 lo0/lo）
            interface = LocalNetworkUtils.pick_interface_for_target(mongo_host)
            logger.info(f"Selected local interface: {interface}")

            # 构建更严格的过滤表达式 - 仅监控目标主机与端口的流量，避免抓到无关27017流量
            # 原实现包含了 "or (tcp port 27017)"，会放大多流表现，这里收敛到精准匹配
            filter_expr = f"host {mongo_host} and tcp port {mongo_port}"
            logger.info(f"Monitoring traffic with strict filter: {filter_expr}")

            # 启动本地tcpdump
            capture_file = await self.local_tcpdump_service.start_capture(
                database_type="mongodb",
                target_port=mongo_port,
                interface=interface,
                filter_expression=filter_expr
            )

            self.current_file = capture_file
            self.is_capturing = True

            # 等待抓包服务完全启动
            logger.info("Waiting for packet capture service to fully initialize...")
            await asyncio.sleep(2)

            logger.info(f"MongoDB packet capture started successfully: {capture_file}")
            logger.info(f"Monitoring traffic to/from {mongo_host}:{mongo_port} and all port {mongo_port} traffic")
            return capture_file

        except Exception as e:
            logger.error(f"Failed to start MongoDB local capture: {str(e)}")
            raise Exception(f"MongoDB本地抓包启动失败: {str(e)}")

    async def start_capture(self, target_host: str = None, target_port: int = None, server_config_id: int = None) -> str:
        """启动MongoDB数据包捕获"""
        mongo_host = target_host or "**************"  # 默认使用远程MongoDB服务器
        mongo_port = target_port or 27017

        logger.info(f"MongoDB抓包配置 - Host: {mongo_host}, Port: {mongo_port}")

        # 如果检测到抓包状态异常，强制重置
        if self.is_capturing:
            logger.warning("检测到MongoDB抓包状态异常，强制重置状态")
            await self.stop_capture()

        # 启动抓包
        try:
            logger.info(f"Starting packet capture to monitor MongoDB server: {mongo_host}:{mongo_port}")
            return await self.start_local_capture(mongo_host, mongo_port)
        except Exception as e:
            logger.error(f"MongoDB capture failed: {str(e)}")
            raise Exception(f"MongoDB抓包启动失败: {str(e)}")

    # 为了兼容性，保留start_smart_capture方法，但实际调用本地抓包
    async def start_smart_capture(self, mongo_host: str, mongo_port: int, server_config_id: int = None) -> str:
        """启动智能MongoDB数据包捕获（兼容性方法，实际使用本地抓包）"""
        logger.info(f"MongoDB智能抓包（本地模式） - Host: {mongo_host}, Port: {mongo_port}")
        return await self.start_capture(mongo_host, mongo_port, server_config_id)

    async def stop_capture(self) -> Optional[str]:
        """停止MongoDB数据包捕获"""
        logger.info("Stopping MongoDB packet capture")

        try:
            if not self.is_capturing:
                logger.warning("MongoDB packet capture is not running")
                return None

            # 停止本地tcpdump
            captured_file = await self.local_tcpdump_service.stop_capture()

            self.is_capturing = False
            self.current_file = None

            if captured_file:
                # 兼容 LocalTcpdump 返回值可能已包含目录的情况
                # 1) 绝对路径：直接使用
                # 2) 相对且已包含目录（如 "captures/xxx.pcap"）：直接使用
                # 3) 仅文件名：拼接到本地目录
                if os.path.isabs(captured_file):
                    full_path = captured_file
                elif os.path.dirname(captured_file):
                    full_path = captured_file
                else:
                    full_path = os.path.join(self.capture_dir, captured_file)

                if os.path.exists(full_path):
                    file_size = os.path.getsize(full_path)
                    logger.info(f"MongoDB packet capture completed: {full_path} ({file_size} bytes)")

                    # 如果文件太小，删除空文件
                    if file_size <= 24:  # pcap文件头大小，表示没有实际数据包
                        logger.warning(f"MongoDB capture file is empty (only {file_size} bytes), deleting it")
                        try:
                            os.remove(full_path)
                            logger.info(f"Deleted empty capture file: {full_path}")
                        except Exception as e:
                            logger.error(f"Failed to delete empty capture file: {e}")
                        return None
                    elif file_size < 100:
                        logger.warning(f"MongoDB capture file is very small ({file_size} bytes), may contain no actual packets")

                    # 保存文件信息到数据库
                    await self._save_capture_file_to_db(full_path)

                    # 返回文件名（不包含路径），这样下载API就能正确处理
                    filename = os.path.basename(full_path)
                    logger.info(f"Returning filename for download: {filename}")
                    return filename
                else:
                    logger.warning(f"MongoDB capture file not found at: {full_path}")
            else:
                logger.warning("MongoDB capture file not found")
                return None

        except Exception as e:
            logger.error(f"Error stopping MongoDB packet capture: {str(e)}")
            return None

    def get_capture_status(self) -> Dict[str, Any]:
        """获取MongoDB抓包状态"""
        status = {
            'is_capturing': self.is_capturing,
            'current_file': self.current_file,
            'database_type': 'mongodb',
            'capture_method': 'local'
        }
        
        # 如果有本地tcpdump服务，获取其状态
        if hasattr(self, 'local_tcpdump_service'):
            tcpdump_status = self.local_tcpdump_service.get_capture_status()
            status.update(tcpdump_status)
        
        return status

    async def force_reset_state(self):
        """强制重置抓包状态"""
        logger.warning("Force resetting MongoDB capture state")
        
        try:
            # 停止本地tcpdump
            if hasattr(self, 'local_tcpdump_service'):
                await self.local_tcpdump_service.stop_capture()
        except Exception as e:
            logger.error(f"Error during force reset: {str(e)}")
        finally:
            self.is_capturing = False
            self.current_file = None
            logger.info("MongoDB capture state reset completed")

    async def _save_capture_file_to_db(self, capture_file_path: str):
        """保存抓包文件信息到数据库"""
        try:
            if not os.path.exists(capture_file_path):
                logger.warning(f"Capture file does not exist: {capture_file_path}")
                return

            file_size = os.path.getsize(capture_file_path)
            filename = os.path.basename(capture_file_path)

            # 从当前抓包配置中获取目标主机和端口信息
            target_host = getattr(self, '_current_target_host', '**************')
            target_port = getattr(self, '_current_target_port', 27017)

            # 使用统一路径管理器获取相对路径，确保数据库中存储的是相对路径
            from utils.path_manager import path_manager
            relative_file_path = path_manager.get_relative_capture_path(filename)

            capture_data = CaptureFileCreate(
                filename=filename,
                file_path=relative_file_path,  # 使用相对路径
                file_size=file_size,
                database_type="mongodb",
                target_host=target_host,
                target_port=target_port,
                description=f"MongoDB packet capture from {target_host}:{target_port}"
            )

            file_id = await capture_file_service.save_capture_file(capture_data)
            logger.info(f"Saved MongoDB capture file to database with ID: {file_id}")

        except Exception as e:
            logger.error(f"Failed to save capture file to database: {e}")
            # 不抛出异常，避免影响抓包流程

    async def cleanup(self):
        """清理资源"""
        try:
            if self.is_capturing:
                await self.stop_capture()
        except Exception as e:
            logger.error(f"Error during cleanup: {str(e)}")

    async def close(self):
        """关闭服务（兼容性方法）"""
        await self.cleanup()

    def __del__(self):
        """析构函数"""
        try:
            if self.is_capturing:
                # 注意：在析构函数中不能使用async/await
                logger.warning("MongoDB capture service destroyed while capturing")
        except Exception as e:

            logger.error(f"操作失败: {type(e).__name__}: {str(e)}")

            logger.error(f"异常堆栈跟踪: {traceback.format_exc()}")

"""
MongoDB异步抓包服务 - 提供MongoDB数据库的异步抓包功能
"""
import asyncio
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime

from services.mongo_local_packet_capture_service import MongoLocalPacketCaptureService
from services.mongo_service import MongoService
from services.simple_mongo_shell_service import SimpleMongoShellService
from services.database_config_service import database_config_service

logger = logging.getLogger(__name__)

class MongoAsyncCaptureService:
    """MongoDB异步抓包服务"""
    
    def __init__(self):
        """初始化MongoDB异步抓包服务"""
        self.packet_service = MongoLocalPacketCaptureService()
        self.mongo_service = None
        self.mongosh_service = None
        self.current_capture_file = None
        self.is_capturing = False
        logger.info("MongoDB Async Capture Service initialized")
    
    async def configure_mongo_connection(self, config_id: int):
        """配置MongoDB连接"""
        try:
            config = await database_config_service.get_config(config_id)
            if not config:
                raise Exception(f"Database config not found: {config_id}")
            
            # 创建MongoDB服务实例
            self.mongo_service = MongoService(
                host=config.host,
                port=config.port,
                user=config.user,
                password=config.password,
                database=config.database_name
            )
            
            # 创建mongosh服务实例
            connection_string = f"mongodb://{config.user}:{config.password}@{config.host}:{config.port}/{config.database_name}?authSource=admin&directConnection=true"
            self.mongosh_service = SimpleMongoShellService(connection_string=connection_string)
            
            # 测试连接
            is_connected = await self.mongo_service.check_connection()
            if not is_connected:
                raise Exception("MongoDB database connection failed")
            
            logger.info(f"MongoDB connection configured: {config.host}:{config.port}")
            
        except Exception as e:
            logger.error(f"Failed to configure MongoDB connection: {str(e)}")
            raise
    
    async def execute_mongo_with_async_capture(
        self,
        mongo_query: str,
        config_id: int,
        capture_duration: int = 10,
        executor_type: str = "mongosh"
    ) -> Dict[str, Any]:
        """执行MongoDB查询并进行异步抓包"""
        try:
            logger.info(f"Starting MongoDB async capture for query: {mongo_query} with {executor_type} executor")

            # 配置数据库连接
            await self.configure_mongo_connection(config_id)

            # 在开始抓包前，先清理可能存在的旧连接，避免将关闭握手/重置包录入pcap
            try:
                if executor_type == "c":
                    from services.mongo_c_executor_service import mongo_c_executor_service
                    await mongo_c_executor_service.close_persistent_connection()
                    logger.info("Pre-capture: cleaned any existing Mongo C executor persistent connections")
                else:
                    await self.mongo_service.close_persistent_connection()
                    logger.info("Pre-capture: cleaned any existing Mongo Python persistent connections")
            except Exception as pre_e:
                logger.debug(f"Pre-capture cleanup skipped or failed (safe to ignore): {pre_e}")
            # 等待连接完全关闭
            await asyncio.sleep(0.8)

            # 获取数据库配置
            config = await database_config_service.get_config(config_id)

            # 启动抓包
            capture_file = await self.packet_service.start_capture(
                target_host=config.host,
                target_port=config.port
            )
            self.current_capture_file = capture_file
            self.is_capturing = True

            logger.info(f"MongoDB packet capture started: {capture_file}")

            # 等待抓包服务启动
            await asyncio.sleep(2)

            # 在开始查询前，确保清理任何旧的连接
            if executor_type == "c":
                # 对于C执行器，先清理可能存在的旧连接
                try:
                    from services.mongo_c_executor_service import mongo_c_executor_service
                    await mongo_c_executor_service.close_persistent_connection()
                    logger.info("Cleaned up any existing persistent connections before starting capture")
                except Exception as e:
                    logger.debug(f"No existing connections to clean up: {e}")

                # 等待连接完全关闭
                await asyncio.sleep(1)

            # 根据执行器类型执行MongoDB查询
            execution_result = None
            if executor_type == "mongosh":
                # 使用mongosh执行器
                logger.info("Using mongosh executor")
                await asyncio.sleep(1)  # 等待抓包稳定
                
                try:
                    # 使用mongosh执行查询
                    execution_result = await self.mongosh_service.execute_mongo_query(mongo_query)
                    logger.info("MongoDB executed successfully with mongosh executor")
                except Exception as e:
                    logger.error(f"mongosh executor failed: {e}")
                    raise Exception(f"mongosh execution failed: {str(e)}")
                    
            elif executor_type == "c":
                # C执行器使用持久连接，需要更长的等待时间确保抓包捕获到连接建立过程
                logger.info("Using C executor, waiting additional time for packet capture to stabilize")
                await asyncio.sleep(2)  # 减少等待时间，因为已经清理了旧连接

                # 预处理MongoDB查询，将复杂的JavaScript for循环转换为C执行器能理解的格式
                processed_queries = self._preprocess_mongo_query_for_c_executor(mongo_query)
                logger.debug(f"[Mongo C Executor] Processed queries => {processed_queries}")

                # 使用C语言执行器
                from services.mongo_c_executor_service import mongo_c_executor_service
                if mongo_c_executor_service.is_available():
                    try:
                        # 创建持久连接
                        connection_created = await mongo_c_executor_service.create_persistent_connection(
                            config.host, config.port, config.user, config.password, config.database_name
                        )
                        if not connection_created:
                            raise Exception("Failed to create persistent MongoDB C executor connection")

                        # 执行预处理后的查询
                        if len(processed_queries) == 1:
                            # 单个查询
                            execution_result = await mongo_c_executor_service.execute_mongo_query_persistent(processed_queries[0])
                        else:
                            # 多个查询，需要逐个执行
                            execution_result = await self._execute_multiple_queries_with_c_executor(
                                mongo_c_executor_service, processed_queries
                            )
                        logger.info("MongoDB executed successfully with C executor")

                    except Exception as e:
                        logger.warning(f"C executor failed, falling back to mongosh: {e}")
                        execution_result = await self.mongosh_service.execute_mongo_query(mongo_query)

                    finally:
                        # 确保关闭持久连接，产生网络流量（参考PostgreSQL模式）
                        try:
                            await mongo_c_executor_service.close_persistent_connection()
                            logger.info("MongoDB C执行器持久连接已关闭")
                        except Exception as e:
                            logger.warning(f"Failed to close MongoDB C executor connection: {e}")
                else:
                    logger.warning("C executor not available, using mongosh executor as fallback")
                    execution_result = await self.mongosh_service.execute_mongo_query(mongo_query)
            else:
                # 默认情况，如果没有指定有效的执行器类型，使用mongosh
                logger.warning(f"Unknown executor type '{executor_type}', using mongosh executor as default")
                execution_result = await self.mongosh_service.execute_mongo_query(mongo_query)

            # 等待数据包捕获
            await asyncio.sleep(capture_duration)

            # 停止抓包
            final_packet_file = await self.packet_service.stop_capture()
            self.is_capturing = False

            # 检查抓包是否成功
            if not final_packet_file:
                error_msg = "MongoDB抓包失败：未生成有效的抓包文件。这可能表明数据库连接没有通过监控的网络接口，或者抓包过滤器与实际流量不匹配。"
                logger.error(error_msg)
                raise Exception(error_msg)

            logger.info(f"MongoDB async capture completed: {final_packet_file}")

            return {
                "success": True,
                "mongo_query": mongo_query,
                "execution_result": execution_result,
                "packet_file": final_packet_file,
                "capture_duration": capture_duration,
                "executor_type": executor_type,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"MongoDB async capture failed: {str(e)}")

            # 确保停止抓包
            if self.is_capturing:
                try:
                    await self.packet_service.stop_capture()
                    self.is_capturing = False
                except Exception as stop_error:
                    logger.error(f"停止MongoDB抓包失败: {type(stop_error).__name__}: {stop_error}")
                    # 即使停止失败也要重置状态
                    self.is_capturing = False

            return {
                "success": False,
                "mongo_query": mongo_query,
                "execution_result": None,  # 添加这个字段以保持一致性
                "error": str(e),
                "packet_file": None,
                "timestamp": datetime.now().isoformat()
            }

    async def execute_batch_with_single_capture(
        self,
        mongo_queries: List[str],
        config_id: int,
        executor_type: str = "python"
    ) -> Dict[str, Any]:
        """在一次抓包会话与一次数据库连接中执行多条Mongo语句。

        顺序：开始抓包 → 创建数据库连接 → 执行所有语句 → 关闭连接 → 停止抓包。
        注：为避免多次短连接，不使用一次性 mongosh，每步仅建立一次TCP连接。
        """
        try:
            # 预处理查询列表
            queries = [q for q in (mongo_queries or []) if isinstance(q, str) and q.strip()]
            if not queries:
                return {"success": True, "sql_results": [], "packet_file": None}

            # 配置连接
            await self.configure_mongo_connection(config_id)
            config = await database_config_service.get_config(config_id)

            # 抓包前清理任何已有持久连接，避免将关闭包写入本次pcap
            try:
                from services.mongo_c_executor_service import mongo_c_executor_service
                await mongo_c_executor_service.close_persistent_connection()
            except Exception:
                pass
            try:
                await self.mongo_service.close_persistent_connection()
            except Exception:
                pass

            # 启动抓包
            capture_file = await self.packet_service.start_capture(
                target_host=config.host,
                target_port=config.port
            )
            self.current_capture_file = capture_file
            self.is_capturing = True
            logger.info(f"[Mongo Batch] Packet capture started: {capture_file}")

            # 等待抓包稳定
            await asyncio.sleep(1.5)

            batch_results: Dict[str, Any] = {}
            sql_results: List[Dict[str, Any]] = []

            if executor_type == "c":
                # 使用C执行器持久连接
                from services.mongo_c_executor_service import mongo_c_executor_service
                if not mongo_c_executor_service.is_available():
                    logger.warning("C executor unavailable, falling back to Python persistent executor")
                    executor_type = "python"
                else:
                    try:
                        created = await mongo_c_executor_service.create_persistent_connection(
                            config.host, config.port, config.user, config.password, config.database_name
                        )
                        if not created:
                            raise Exception("Failed to create Mongo C executor connection")

                        # 统一将所有脚本预处理为可执行的原子命令序列
                        flattened: List[str] = []
                        for raw in queries:
                            flattened.extend(self._preprocess_mongo_query_for_c_executor(raw))

                        # 逐条执行（持久连接）
                        for q in flattened:
                            try:
                                r = await mongo_c_executor_service.execute_mongo_query_persistent(q)
                                sql_results.append({"mongo_query": q, "result": r, "success": r.get("success", False)})
                            except Exception as e:
                                logger.error(f"[Mongo C] Execute failed: {e}")
                                sql_results.append({"mongo_query": q, "error": str(e), "success": False})

                    finally:
                        try:
                            await mongo_c_executor_service.close_persistent_connection()
                        except Exception:
                            pass

            if executor_type != "c":
                # Python执行器持久连接
                # 创建全新连接确保握手进入本次pcap
                created = await self.mongo_service.create_fresh_connection()
                if not created:
                    raise Exception("Failed to create Mongo persistent connection")

                try:
                    # 扁平化并映射事务/会话标记
                    flattened_py: List[str] = []
                    for raw in queries:
                        flattened_py.extend(self._preprocess_mongo_query_for_python_executor(raw))

                    # 执行（持久连接）
                    batch_results = await self._execute_multiple_queries_with_python_persistent_connection(flattened_py)
                    # 归一化返回结构
                    for item in batch_results.get("results", []):
                        sql_results.append({
                            "mongo_query": item.get("query"),
                            "result": item.get("result"),
                            "success": item.get("success", True)
                        })
                finally:
                    # 关闭连接以让挥手包写入pcap
                    try:
                        await self.mongo_service.close_persistent_connection()
                        await asyncio.sleep(0.8)
                    except Exception:
                        pass

            # 停止抓包
            await asyncio.sleep(1.0)
            final_packet_file = await self.packet_service.stop_capture()
            self.is_capturing = False

            if not final_packet_file:
                raise Exception("MongoDB抓包失败：未生成有效的抓包文件")

            # 评估汇总成功状态
            success_overall = all(r.get("success", False) for r in sql_results) if sql_results else True

            return {
                "success": success_overall,
                "sql_results": sql_results,
                "packet_file": final_packet_file,
                "executor_type": "c" if executor_type == "c" else "python",
            }

        except Exception as e:
            logger.error(f"MongoDB batch single-capture execution failed: {e}")
            # 尝试停止抓包
            if self.is_capturing:
                try:
                    await self.packet_service.stop_capture()
                except Exception:
                    pass
                self.is_capturing = False
            return {
                "success": False,
                "error": str(e),
                "sql_results": [],
                "packet_file": None,
            }
    
    async def start_capture_session(self, config_id: int) -> Dict[str, Any]:
        """启动抓包会话"""
        try:
            if self.is_capturing:
                return {
                    "success": False,
                    "error": "Capture session already active",
                    "current_file": self.current_capture_file
                }
            
            # 获取数据库配置
            config = await database_config_service.get_config(config_id)
            
            # 启动抓包
            capture_file = await self.packet_service.start_capture(
                target_host=config.host,
                target_port=config.port
            )
            self.current_capture_file = capture_file
            self.is_capturing = True
            
            logger.info(f"MongoDB capture session started: {capture_file}")
            
            return {
                "success": True,
                "capture_file": capture_file,
                "message": "MongoDB capture session started"
            }
            
        except Exception as e:
            logger.error(f"Failed to start MongoDB capture session: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def stop_capture_session(self) -> Dict[str, Any]:
        """停止抓包会话"""
        try:
            if not self.is_capturing:
                return {
                    "success": False,
                    "error": "No active capture session"
                }
            
            # 停止抓包
            final_packet_file = await self.packet_service.stop_capture()
            self.is_capturing = False
            self.current_capture_file = None
            
            logger.info(f"MongoDB capture session stopped: {final_packet_file}")
            
            return {
                "success": True,
                "packet_file": final_packet_file,
                "message": "MongoDB capture session stopped"
            }
            
        except Exception as e:
            logger.error(f"Failed to stop MongoDB capture session: {str(e)}")
            self.is_capturing = False
            self.current_capture_file = None
            return {
                "success": False,
                "error": str(e)
            }
    
    def get_capture_status(self) -> Dict[str, Any]:
        """获取抓包状态"""
        return {
            "is_capturing": self.is_capturing,
            "current_file": self.current_capture_file,
            "service_status": "active"
        }

    def _preprocess_mongo_query_for_c_executor(self, mongo_query: str) -> list:
        """预处理MongoDB查询，移除for循环处理逻辑"""
        import re

        text = (mongo_query or "").replace("\r\n", "\n").strip()
        
        # 检查是否是完整的for循环语句，如果是则直接返回
        for_loop_pattern = r'for\s*\(\s*(?:var|let)?\s*\w+\s*=\s*\d+\s*;\s*\w+\s*<\s*\d+\s*;\s*\w+\+\+\s*\)\s*\{[^}]+\}'
        if re.search(for_loop_pattern, text):
            logger.info("Detected complete JavaScript for loop, returning as single command")
            return [text]
        
        # 在进一步切分前，先扫描并记录 session.getDatabase(...) 形式的集合别名
        alias_map = {}
        try:
            alias_pattern = re.compile(r"(?:var|let|const)?\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*=\s*session\.getDatabase\(\s*['\"]([a-zA-Z0-9_\-]+)['\"]\s*\)\.([a-zA-Z_][a-zA-Z0-9_]*)\s*;?",
                                       re.IGNORECASE)
            for m in alias_pattern.finditer(text):
                alias = m.group(1)
                dbname = m.group(2)
                coll = m.group(3)
                alias_map[alias] = (dbname, coll)
        except Exception as e:
            logger.warning(f"解析MongoDB别名模式时发生异常: {e}")

        # 按分号切分常规命令（不包含复杂JavaScript结构）
        # 注意：不要切分 db.eval() 或 function(){...} 中的分号
        commands = []
        try:
            # 简单按分号切分（在实际生产环境中，需要更复杂的解析器来处理引号、注释、函数等情况）
            # 移除末尾分号
            text = text.rstrip(';')
            raw_parts = text.split(";")
            for part in raw_parts:
                stripped = part.strip()
                if stripped:
                    # 检查是否是分片相关的批量操作
                    if 'initializeUnorderedBulkOp' in stripped or 'initializeOrderedBulkOp' in stripped:
                        # 对于批量操作，添加额外的延迟指令
                        commands.append(stripped)
                        commands.append("// SHARD_DELAY")
                    else:
                        commands.append(stripped)
        except Exception as e:
            logger.warning(f"分号切分命令时发生异常: {e}")
            # 回退到不分割
            commands = [text] if text else []

        # 对每个命令进行别名替换和预处理
        processed_commands = []
        for cmd in commands:
            # 替换别名（如 sessionDB.users.insertOne(...)）
            for alias, (dbname, collname) in alias_map.items():
                cmd = cmd.replace(f"{alias}.", f"db.{collname}.")

            # 修正常见的JavaScript表达式为固定值（便于C执行器处理）
            # 处理 Math.random() * 1000
            cmd = re.sub(r"Math\.random\(\)\s*\*\s*(\d+)", r"500", cmd)  # 固定为500

            # 处理 Math.floor(Math.random() * X) + Y
            cmd = re.sub(r"Math\.floor\(Math\.random\(\)\s*\*\s*\d+\)\s*\+\s*\d+", "50", cmd)  # 固定为50

            # 处理 new Date()
            cmd = cmd.replace("new Date()", '"2023-01-01T00:00:00Z"')
            
            # 处理分片相关的特殊指令
            if cmd == "// SHARD_DELAY":
                # 这是一个特殊指令，用于在C执行器中添加延迟
                continue

            processed_commands.append(cmd)

        return processed_commands

    async def _execute_multiple_queries_with_c_executor(self, mongo_c_service, queries: list) -> Dict[str, Any]:
        """使用C执行器执行多个查询"""
        results = []
        success_count = 0

        for i, query in enumerate(queries):
            try:
                result = await mongo_c_service.execute_mongo_query_persistent(query)
                results.append({
                    "query": query,
                    "result": result,
                    "success": result.get("success", False)
                })

                if result.get("success", False):
                    success_count += 1

            except Exception as e:
                logger.error(f"Failed to execute query {i+1}: {e}")
                results.append({
                    "query": query,
                    "result": {"error": str(e)},
                    "success": False
                })

        # 返回汇总结果
        return {
            "success": success_count > 0,
            "total_queries": len(queries),
            "successful_queries": success_count,
            "failed_queries": len(queries) - success_count,
            "results": results,
            "message": f"Executed {success_count}/{len(queries)} queries successfully"
        }

    def _preprocess_mongo_query_for_python_executor(self, mongo_query: str) -> list:
        """预处理MongoDB查询，Python执行器版本（复用C执行器的逻辑并支持session/事务）"""
        return self._preprocess_mongo_query_for_c_executor(mongo_query)

    async def _execute_multiple_queries_with_python_executor(self, queries: list) -> Dict[str, Any]:
        """使用Python执行器执行多个查询（常规连接）"""
        results = []
        success_count = 0

        for i, query in enumerate(queries):
            try:
                result = await self.mongo_service.execute_mongo_query(query)
                results.append({
                    "query": query,
                    "result": result,
                    "success": result.get("success", True) if result else False
                })

                if result and result.get("success", True):
                    success_count += 1

            except Exception as e:
                logger.error(f"Failed to execute query {i+1} with Python executor: {e}")
                results.append({
                    "query": query,
                    "result": {"error": str(e)},
                    "success": False
                })

        # 返回汇总结果
        return {
            "success": success_count > 0,
            "total_queries": len(queries),
            "successful_queries": success_count,
            "failed_queries": len(queries) - success_count,
            "results": results,
            "message": f"Executed {success_count}/{len(queries)} queries successfully with Python executor"
        }

    async def _execute_multiple_queries_with_python_persistent_connection(self, queries: list) -> Dict[str, Any]:
        """使用Python执行器持久连接执行多个查询"""
        results = []
        success_count = 0

        for i, query in enumerate(queries):
            try:
                # 处理特殊事务指令标记
                if query == '__MONGO_SESSION_START__':
                    result = await self.mongo_service.start_session_persistent()
                elif query == '__MONGO_TX_START__':
                    result = await self.mongo_service.start_transaction_persistent()
                elif query == '__MONGO_TX_COMMIT__':
                    result = await self.mongo_service.commit_transaction_persistent()
                elif query == '__MONGO_TX_ABORT__':
                    result = await self.mongo_service.abort_transaction_persistent()
                elif query == '__MONGO_SESSION_END__':
                    # 当前实现中不需要显式结束，会在关闭持久连接时清理
                    result = {"success": True, "message": "Session ended (noop)"}
                else:
                    result = await self.mongo_service.execute_mongo_query_with_persistent_connection(query)
                results.append({
                    "query": query,
                    "result": result,
                    "success": result.get("success", True) if isinstance(result, dict) else True
                })

                if (isinstance(result, dict) and result.get("success", True)) or (not isinstance(result, dict)):
                    success_count += 1

            except Exception as e:
                logger.error(f"Failed to execute query {i+1} with Python persistent connection: {e}")
                results.append({
                    "query": query,
                    "result": {"error": str(e)},
                    "success": False
                })

        # 返回汇总结果
        return {
            "success": success_count > 0,
            "total_queries": len(queries),
            "successful_queries": success_count,
            "failed_queries": len(queries) - success_count,
            "results": results,
            "message": f"Executed {success_count}/{len(queries)} queries successfully with Python persistent connection"
        }

# 创建全局服务实例
mongo_async_capture_service = MongoAsyncCaptureService()

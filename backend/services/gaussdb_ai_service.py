"""
GaussDB AI服务 - 管理GaussDB连接池复用和AI查询处理
"""

import logging
from typing import Dict, Any
from services.gaussdb_service_v2 import GaussDBServiceV2

logger = logging.getLogger(__name__)

class GaussDBAIService:
    """GaussDB AI服务 - 支持连接池复用"""

    def __init__(self):
        """初始化GaussDB AI服务"""
        # 缓存GaussDB服务实例以复用连接池
        self._gaussdb_service_cache = {}
        logger.info("GaussDB AI Service initialized with connection pool support")
    
    def _get_gaussdb_service(self, gaussdb_config: Dict[str, Any]):
        """获取或创建GaussDB服务实例（支持连接池复用）"""
        # 创建缓存键
        cache_key = f"{gaussdb_config['host']}:{gaussdb_config['port']}:{gaussdb_config['user']}:{gaussdb_config['database_name']}"
        
        # 检查缓存中是否已有实例
        if cache_key in self._gaussdb_service_cache:
            logger.debug(f"Reusing cached GaussDB service for {cache_key}")
            return self._gaussdb_service_cache[cache_key]
        
        # 创建新的GaussDB服务实例
        gaussdb_service = GaussDBServiceV2(
            host=gaussdb_config['host'],
            port=gaussdb_config['port'],
            user=gaussdb_config['user'],
            password=gaussdb_config['password'],
            database=gaussdb_config['database_name']
        )
        
        # 缓存实例
        self._gaussdb_service_cache[cache_key] = gaussdb_service
        logger.info(f"Created and cached new GaussDB service for {cache_key}")
        return gaussdb_service
    
    async def execute_gaussdb_query(self, sql_query: str, gaussdb_config: Dict[str, Any]) -> Dict[str, Any]:
        """执行GaussDB查询 - 使用连接池"""
        try:
            logger.info(f"Executing GaussDB query with connection pool: {sql_query}")

            # 获取GaussDB服务实例（复用连接池）
            gaussdb_service = self._get_gaussdb_service(gaussdb_config)
            
            # 确保服务已初始化
            if not hasattr(gaussdb_service, '_initialized') or not gaussdb_service._initialized:
                await gaussdb_service.initialize()
                gaussdb_service._initialized = True

            # 执行查询
            result = await gaussdb_service.execute_sql_query(sql_query)
            
            logger.info(f"GaussDB query executed successfully with connection pool")
            return result

        except Exception as e:
            logger.error(f"Failed to execute GaussDB query: {str(e)}")
            raise Exception(f"GaussDB query execution failed: {str(e)}")
    
    def close_all_pools(self):
        """关闭所有连接池"""
        try:
            for cache_key, service in self._gaussdb_service_cache.items():
                try:
                    service.close_pool()
                    logger.info(f"Closed connection pool for {cache_key}")
                except Exception as e:
                    logger.error(f"Error closing connection pool for {cache_key}: {e}")
            
            self._gaussdb_service_cache.clear()
            logger.info("All GaussDB connection pools closed")
            
        except Exception as e:
            logger.error(f"Error closing GaussDB connection pools: {str(e)}")

# 创建全局实例
gaussdb_ai_service = GaussDBAIService()

"""
全局tcpdump进程管理器
负责跟踪、管理和清理所有tcpdump进程
"""

import os
import signal
import asyncio
import subprocess
import logging
import time
from typing import Dict, List, Optional, Set
from dataclasses import dataclass
from threading import Lock

logger = logging.getLogger(__name__)

@dataclass
class TcpdumpProcess:
    """tcpdump进程信息"""
    pid: int
    task_id: str
    command: str
    start_time: float
    capture_file: Optional[str] = None
    database_type: Optional[str] = None
    is_remote: bool = False
    server_host: Optional[str] = None

class TcpdumpProcessManager:
    """全局tcpdump进程管理器"""
    
    def __init__(self):
        self.processes: Dict[int, TcpdumpProcess] = {}
        self.task_processes: Dict[str, Set[int]] = {}  # task_id -> set of pids
        self._lock = Lock()
        self._cleanup_task = None
        self._running = False
    
    async def start_manager(self):
        """启动管理器"""
        if self._running:
            return
        
        self._running = True
        # 启动定期清理任务
        self._cleanup_task = asyncio.create_task(self._periodic_cleanup())
        logger.info("tcpdump进程管理器已启动")
    
    async def stop_manager(self):
        """停止管理器"""
        self._running = False
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        
        # 清理所有进程
        await self.cleanup_all_processes()
        logger.info("tcpdump进程管理器已停止")
    
    def register_process(self, pid: int, task_id: str, command: str, 
                        capture_file: Optional[str] = None,
                        database_type: Optional[str] = None,
                        is_remote: bool = False,
                        server_host: Optional[str] = None):
        """注册tcpdump进程"""
        with self._lock:
            process = TcpdumpProcess(
                pid=pid,
                task_id=task_id,
                command=command,
                start_time=time.time(),
                capture_file=capture_file,
                database_type=database_type,
                is_remote=is_remote,
                server_host=server_host
            )
            
            self.processes[pid] = process
            
            if task_id not in self.task_processes:
                self.task_processes[task_id] = set()
            self.task_processes[task_id].add(pid)
            
            logger.info(f"注册tcpdump进程: PID={pid}, Task={task_id}, DB={database_type}")
    
    def unregister_process(self, pid: int):
        """注销tcpdump进程"""
        with self._lock:
            if pid in self.processes:
                process = self.processes[pid]
                task_id = process.task_id
                
                # 从进程字典中移除
                del self.processes[pid]
                
                # 从任务进程映射中移除
                if task_id in self.task_processes:
                    self.task_processes[task_id].discard(pid)
                    if not self.task_processes[task_id]:
                        del self.task_processes[task_id]
                
                logger.info(f"注销tcpdump进程: PID={pid}, Task={task_id}")
    
    async def cleanup_task_processes(self, task_id: str, reason: str = "任务结束"):
        """清理指定任务的所有tcpdump进程"""
        pids_to_cleanup = []
        
        with self._lock:
            if task_id in self.task_processes:
                pids_to_cleanup = list(self.task_processes[task_id])
        
        if not pids_to_cleanup:
            logger.debug(f"任务 {task_id} 没有需要清理的tcpdump进程")
            return
        
        logger.info(f"开始清理任务 {task_id} 的tcpdump进程: {pids_to_cleanup} (原因: {reason})")
        
        for pid in pids_to_cleanup:
            await self._kill_process(pid, reason)
            self.unregister_process(pid)
        
        logger.info(f"任务 {task_id} 的tcpdump进程清理完成")
    
    async def cleanup_all_processes(self, reason: str = "系统清理"):
        """清理所有tcpdump进程"""
        pids_to_cleanup = []
        
        with self._lock:
            pids_to_cleanup = list(self.processes.keys())
        
        if not pids_to_cleanup:
            logger.debug("没有需要清理的tcpdump进程")
            return
        
        logger.info(f"开始清理所有tcpdump进程: {pids_to_cleanup} (原因: {reason})")
        
        for pid in pids_to_cleanup:
            await self._kill_process(pid, reason)
            self.unregister_process(pid)
        
        logger.info("所有tcpdump进程清理完成")
    
    async def _kill_process(self, pid: int, reason: str = "清理"):
        """杀死指定的进程"""
        try:
            process_info = self.processes.get(pid)
            if not process_info:
                return
            
            logger.info(f"正在终止tcpdump进程 PID={pid} (原因: {reason})")
            
            # 检查进程是否还存在
            try:
                os.kill(pid, 0)  # 发送信号0检查进程是否存在
            except ProcessLookupError:
                logger.debug(f"进程 {pid} 已经不存在")
                return
            except PermissionError:
                logger.warning(f"没有权限检查进程 {pid}")
            
            # 尝试优雅停止
            try:
                if process_info.is_remote:
                    # 远程进程需要通过SSH杀死
                    logger.info(f"远程tcpdump进程 {pid} 需要通过SSH清理")
                    # 这里可以扩展远程清理逻辑
                else:
                    # 本地进程
                    try:
                        # 尝试杀死进程组
                        os.killpg(os.getpgid(pid), signal.SIGTERM)
                        logger.debug(f"发送SIGTERM信号到进程组 {pid}")
                    except (ProcessLookupError, PermissionError):
                        # 如果进程组不存在，尝试杀死单个进程
                        os.kill(pid, signal.SIGTERM)
                        logger.debug(f"发送SIGTERM信号到进程 {pid}")
                
                # 等待进程结束
                await asyncio.sleep(2)
                
                # 检查进程是否还在运行
                try:
                    os.kill(pid, 0)
                    # 如果还在运行，强制杀死
                    logger.warning(f"进程 {pid} 未响应SIGTERM，使用SIGKILL强制终止")
                    try:
                        os.killpg(os.getpgid(pid), signal.SIGKILL)
                    except (ProcessLookupError, PermissionError):
                        os.kill(pid, signal.SIGKILL)
                    await asyncio.sleep(1)
                except ProcessLookupError:
                    logger.debug(f"进程 {pid} 已成功终止")
                    
            except ProcessLookupError:
                logger.debug(f"进程 {pid} 已经不存在")
            except PermissionError:
                logger.error(f"没有权限终止进程 {pid}")
            except Exception as e:
                logger.error(f"终止进程 {pid} 时发生错误: {e}")
                
        except Exception as e:
            logger.error(f"清理进程 {pid} 失败: {e}")
    
    async def _periodic_cleanup(self):
        """定期清理任务"""
        while self._running:
            try:
                await asyncio.sleep(30)  # 每30秒检查一次
                await self._cleanup_orphaned_processes()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"定期清理任务出错: {e}")
    
    async def _cleanup_orphaned_processes(self):
        """清理孤儿进程"""
        orphaned_pids = []
        current_time = time.time()
        
        with self._lock:
            for pid, process in list(self.processes.items()):
                try:
                    # 检查进程是否还存在
                    os.kill(pid, 0)
                    
                    # 检查是否运行时间过长（超过1小时）
                    if current_time - process.start_time > 3600:
                        logger.warning(f"发现长时间运行的tcpdump进程: PID={pid}, 运行时间={current_time - process.start_time:.0f}秒")
                        orphaned_pids.append(pid)
                        
                except ProcessLookupError:
                    # 进程已经不存在，从管理器中移除
                    logger.debug(f"发现已终止的进程: PID={pid}")
                    orphaned_pids.append(pid)
                except PermissionError:
                    # 权限问题，跳过
                    pass
        
        # 清理孤儿进程
        for pid in orphaned_pids:
            await self._kill_process(pid, "孤儿进程清理")
            self.unregister_process(pid)
    
    def get_process_info(self, task_id: Optional[str] = None) -> List[Dict]:
        """获取进程信息"""
        with self._lock:
            if task_id:
                pids = self.task_processes.get(task_id, set())
                processes = [self.processes[pid] for pid in pids if pid in self.processes]
            else:
                processes = list(self.processes.values())
        
        return [
            {
                'pid': p.pid,
                'task_id': p.task_id,
                'command': p.command,
                'start_time': p.start_time,
                'running_time': time.time() - p.start_time,
                'capture_file': p.capture_file,
                'database_type': p.database_type,
                'is_remote': p.is_remote,
                'server_host': p.server_host
            }
            for p in processes
        ]
    
    async def force_cleanup_all_tcpdump(self):
        """强制清理系统中所有tcpdump进程（包括未注册的）"""
        try:
            # 查找所有tcpdump进程
            result = subprocess.run(['pgrep', '-f', 'tcpdump'], capture_output=True, text=True)
            if result.returncode == 0:
                pids = [int(pid.strip()) for pid in result.stdout.strip().split('\n') if pid.strip()]
                
                logger.info(f"发现系统中的tcpdump进程: {pids}")
                
                for pid in pids:
                    await self._kill_process_by_pid(pid, "系统强制清理")
                
                logger.info(f"强制清理了 {len(pids)} 个tcpdump进程")
            else:
                logger.info("系统中没有发现tcpdump进程")
                
        except Exception as e:
            logger.error(f"强制清理tcpdump进程失败: {e}")
    
    async def _kill_process_by_pid(self, pid: int, reason: str):
        """通过PID杀死进程（不需要在管理器中注册）"""
        try:
            logger.info(f"强制终止tcpdump进程 PID={pid} (原因: {reason})")
            
            # 检查进程是否存在
            try:
                os.kill(pid, 0)
            except ProcessLookupError:
                return
            
            # 尝试优雅停止
            try:
                os.killpg(os.getpgid(pid), signal.SIGTERM)
            except (ProcessLookupError, PermissionError):
                os.kill(pid, signal.SIGTERM)
            
            await asyncio.sleep(2)
            
            # 检查是否需要强制杀死
            try:
                os.kill(pid, 0)
                try:
                    os.killpg(os.getpgid(pid), signal.SIGKILL)
                except (ProcessLookupError, PermissionError):
                    os.kill(pid, signal.SIGKILL)
                await asyncio.sleep(1)
            except ProcessLookupError:
                pass
                
        except Exception as e:
            logger.error(f"强制终止进程 {pid} 失败: {e}")

# 全局实例
tcpdump_manager = TcpdumpProcessManager()

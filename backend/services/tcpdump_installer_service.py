"""
tcpdump自动安装服务 - 检查并自动安装tcpdump工具
"""

import asyncio
import logging
from typing import Dict, Any, Optional, Tuple
import paramiko
from models.server_config import ServerConfig

logger = logging.getLogger(__name__)

class TcpdumpInstallerService:
    """tcpdump自动安装服务 - 根据服务器类型自动安装tcpdump"""
    
    def __init__(self):
        self.ssh_client = None
        self.current_server_config = None
        self.os_info = None
        
    async def ensure_tcpdump_available(self, server_config: ServerConfig) -> bool:
        """确保tcpdump在目标服务器上可用"""
        try:
            self.current_server_config = server_config
            
            # 建立SSH连接
            await self._connect_ssh()
            
            # 检查tcpdump是否存在
            if await self._check_tcpdump_exists():
                logger.info("tcpdump already available on server")
                return True
            
            logger.warning("tcpdump not found, attempting to install...")
            
            # 检测操作系统类型
            os_type = await self._detect_os_type()
            
            # 根据操作系统类型安装tcpdump
            success = await self._install_tcpdump(os_type)
            
            if success:
                # 再次验证安装是否成功
                if await self._check_tcpdump_exists():
                    logger.info("tcpdump successfully installed and verified")
                    return True
                else:
                    logger.error("tcpdump installation verification failed")
                    return False
            else:
                logger.error("tcpdump installation failed")
                return False
                
        except Exception as e:
            logger.error(f"Failed to ensure tcpdump availability: {str(e)}")
            return False
        finally:
            await self._cleanup()
    
    async def _connect_ssh(self):
        """建立SSH连接"""
        try:
            if self.ssh_client:
                try:
                    # 测试现有连接
                    self.ssh_client.exec_command('echo test', timeout=5)
                    return
                except:
                    # 连接已断开，重新连接
                    self.ssh_client.close()
                    self.ssh_client = None
            
            # 建立新连接
            self.ssh_client = paramiko.SSHClient()
            self.ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            
            self.ssh_client.connect(
                hostname=self.current_server_config.host,
                port=self.current_server_config.port,
                username=self.current_server_config.username,
                password=self.current_server_config.password,
                timeout=10
            )
            
            logger.info(f"SSH connection established to {self.current_server_config.host}")
            
        except Exception as e:
            logger.error(f"Failed to establish SSH connection: {str(e)}")
            raise
    
    async def _check_tcpdump_exists(self) -> bool:
        """检查tcpdump是否存在"""
        try:
            # 检查tcpdump命令是否存在
            stdin, stdout, stderr = self.ssh_client.exec_command('which tcpdump')
            tcpdump_path = stdout.read().decode('utf-8').strip()
            
            if tcpdump_path:
                logger.info(f"tcpdump found at: {tcpdump_path}")
                
                # 检查tcpdump版本
                stdin, stdout, stderr = self.ssh_client.exec_command('tcpdump --version')
                version_output = stderr.read().decode('utf-8').strip()  # tcpdump版本信息通常在stderr
                logger.info(f"tcpdump version: {version_output}")
                
                return True
            else:
                logger.info("tcpdump not found in PATH")
                return False
                
        except Exception as e:
            logger.error(f"Error checking tcpdump existence: {str(e)}")
            return False
    
    async def _detect_os_type(self) -> str:
        """检测操作系统类型"""
        try:
            # 检查/etc/os-release文件
            stdin, stdout, stderr = self.ssh_client.exec_command('cat /etc/os-release')
            os_release = stdout.read().decode('utf-8')
            
            if 'ubuntu' in os_release.lower() or 'debian' in os_release.lower():
                os_type = 'debian'
            elif 'centos' in os_release.lower() or 'rhel' in os_release.lower() or 'red hat' in os_release.lower():
                os_type = 'rhel'
            elif 'fedora' in os_release.lower():
                os_type = 'fedora'
            elif 'alpine' in os_release.lower():
                os_type = 'alpine'
            elif 'arch' in os_release.lower():
                os_type = 'arch'
            else:
                # 尝试其他检测方法
                stdin, stdout, stderr = self.ssh_client.exec_command('uname -a')
                uname_output = stdout.read().decode('utf-8').lower()
                
                if 'ubuntu' in uname_output or 'debian' in uname_output:
                    os_type = 'debian'
                elif 'centos' in uname_output or 'rhel' in uname_output:
                    os_type = 'rhel'
                else:
                    os_type = 'unknown'
            
            logger.info(f"Detected OS type: {os_type}")
            self.os_info = {
                'type': os_type,
                'release_info': os_release
            }
            
            return os_type
            
        except Exception as e:
            logger.error(f"Failed to detect OS type: {str(e)}")
            return 'unknown'
    
    async def _install_tcpdump(self, os_type: str) -> bool:
        """根据操作系统类型安装tcpdump"""
        try:
            install_commands = self._get_install_commands(os_type)
            
            if not install_commands:
                logger.error(f"No install commands available for OS type: {os_type}")
                return False
            
            # 执行安装命令
            for cmd_info in install_commands:
                command = cmd_info['command']
                description = cmd_info['description']
                
                logger.info(f"Executing: {description}")
                logger.info(f"Command: {command}")
                
                stdin, stdout, stderr = self.ssh_client.exec_command(command, timeout=300)  # 5分钟超时
                
                # 等待命令完成
                exit_status = stdout.channel.recv_exit_status()
                stdout_output = stdout.read().decode('utf-8')
                stderr_output = stderr.read().decode('utf-8')
                
                logger.info(f"Command exit status: {exit_status}")
                if stdout_output:
                    logger.info(f"Command output: {stdout_output}")
                if stderr_output:
                    logger.info(f"Command stderr: {stderr_output}")
                
                if exit_status != 0:
                    logger.error(f"Command failed with exit status {exit_status}: {command}")
                    # 对于某些命令（如update），失败不一定意味着整个安装失败
                    if cmd_info.get('critical', True):
                        return False
                else:
                    logger.info(f"Command succeeded: {description}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to install tcpdump: {str(e)}")
            return False
    
    def _get_install_commands(self, os_type: str) -> list:
        """获取不同操作系统的tcpdump安装命令"""
        commands = {
            'debian': [
                {
                    'command': 'apt-get update',
                    'description': 'Update package list',
                    'critical': False  # update失败不影响安装
                },
                {
                    'command': 'apt-get install -y tcpdump',
                    'description': 'Install tcpdump using apt-get',
                    'critical': True
                }
            ],
            'rhel': [
                {
                    'command': 'yum update -y',
                    'description': 'Update package list',
                    'critical': False
                },
                {
                    'command': 'yum install -y tcpdump',
                    'description': 'Install tcpdump using yum',
                    'critical': True
                }
            ],
            'fedora': [
                {
                    'command': 'dnf update -y',
                    'description': 'Update package list',
                    'critical': False
                },
                {
                    'command': 'dnf install -y tcpdump',
                    'description': 'Install tcpdump using dnf',
                    'critical': True
                }
            ],
            'alpine': [
                {
                    'command': 'apk update',
                    'description': 'Update package list',
                    'critical': False
                },
                {
                    'command': 'apk add tcpdump',
                    'description': 'Install tcpdump using apk',
                    'critical': True
                }
            ],
            'arch': [
                {
                    'command': 'pacman -Sy',
                    'description': 'Update package list',
                    'critical': False
                },
                {
                    'command': 'pacman -S --noconfirm tcpdump',
                    'description': 'Install tcpdump using pacman',
                    'critical': True
                }
            ]
        }
        
        return commands.get(os_type, [])
    
    async def get_tcpdump_info(self, server_config: ServerConfig) -> Dict[str, Any]:
        """获取tcpdump信息"""
        try:
            self.current_server_config = server_config
            await self._connect_ssh()
            
            info = {
                'available': False,
                'path': None,
                'version': None,
                'os_type': None,
                'capabilities': []
            }
            
            # 检查tcpdump是否存在
            if await self._check_tcpdump_exists():
                info['available'] = True
                
                # 获取路径
                stdin, stdout, stderr = self.ssh_client.exec_command('which tcpdump')
                info['path'] = stdout.read().decode('utf-8').strip()
                
                # 获取版本
                stdin, stdout, stderr = self.ssh_client.exec_command('tcpdump --version')
                info['version'] = stderr.read().decode('utf-8').strip()
                
                # 检查权限和能力
                stdin, stdout, stderr = self.ssh_client.exec_command('tcpdump -D')
                interfaces_output = stdout.read().decode('utf-8')
                if interfaces_output:
                    info['capabilities'].append('list_interfaces')
                
                # 检查是否有root权限
                stdin, stdout, stderr = self.ssh_client.exec_command('id -u')
                uid = stdout.read().decode('utf-8').strip()
                if uid == '0':
                    info['capabilities'].append('root_access')
            
            # 获取操作系统信息
            info['os_type'] = await self._detect_os_type()
            
            return info
            
        except Exception as e:
            logger.error(f"Failed to get tcpdump info: {str(e)}")
            return {'available': False, 'error': str(e)}
        finally:
            await self._cleanup()
    
    async def _cleanup(self):
        """清理资源"""
        try:
            if self.ssh_client:
                self.ssh_client.close()
                self.ssh_client = None
        except Exception as e:
            logger.error(f"Cleanup error: {str(e)}")

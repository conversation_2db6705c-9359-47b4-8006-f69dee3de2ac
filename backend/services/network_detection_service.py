"""
智能网络检测和抓包策略服务
"""

import logging
import re
import asyncio
import paramiko
from typing import List, Optional, Dict, Any, Tuple
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.server_config import (
    ServerConfig, NetworkInterface, DatabaseDeployment, CaptureStrategy
)

logger = logging.getLogger(__name__)

class NetworkDetectionService:
    """智能网络检测和抓包策略服务"""
    
    def __init__(self):
        self.ssh_client = None
        
    async def detect_database_deployment(
        self,
        server_config: ServerConfig,
        database_type: str,
        database_host: str,
        database_port: int
    ) -> DatabaseDeployment:
        """检测数据库部署方式 - 支持MySQL、PostgreSQL、MongoDB、Oracle"""
        try:
            await self._connect_ssh(server_config)
            
            # 1. 检查Docker容器部署
            docker_deployment = await self._check_docker_deployment(
                database_type, database_host, database_port
            )
            if docker_deployment:
                logger.info(f"Detected {database_type} Docker deployment: {docker_deployment.to_dict()}")
                return docker_deployment
            
            # 2. 检查本地进程部署
            native_deployment = await self._check_native_deployment(
                database_type, database_host, database_port
            )
            if native_deployment:
                logger.info(f"Detected {database_type} native deployment: {native_deployment.to_dict()}")
                return native_deployment
            
            # 3. 如果都没检测到，返回默认配置
            logger.warning(f"Could not detect {database_type} deployment, using default configuration")
            return DatabaseDeployment(
                database_type=database_type,
                deployment_type='unknown',
                host=database_host,
                port=database_port
            )
            
        except Exception as e:
            logger.error(f"Failed to detect {database_type} deployment: {str(e)}")
            logger.error(f"Error details: {type(e).__name__}: {str(e)}")
            # 返回默认配置而不是抛出异常
            return DatabaseDeployment(
                database_type=database_type,
                deployment_type='unknown',
                host=database_host,
                port=database_port
            )
        finally:
            await self._cleanup()
    
    async def get_network_interfaces(self, server_config: ServerConfig) -> List[NetworkInterface]:
        """获取服务器网络接口信息"""
        try:
            await self._connect_ssh(server_config)
            
            interfaces = []
            
            # 获取网络接口信息
            cmd = "ip addr show"
            stdin, stdout, stderr = self.ssh_client.exec_command(cmd)
            output = stdout.read().decode('utf-8')
            
            # 解析网络接口信息
            interface_blocks = re.split(r'\n(?=\d+:)', output)
            
            for block in interface_blocks:
                if not block.strip():
                    continue
                    
                interface = self._parse_interface_block(block)
                if interface:
                    interfaces.append(interface)
            
            logger.info(f"Found {len(interfaces)} network interfaces")
            return interfaces
            
        except Exception as e:
            logger.error(f"Failed to get network interfaces: {str(e)}")
            raise
        finally:
            await self._cleanup()
    
    async def generate_capture_strategy(
        self, 
        server_config: ServerConfig,
        deployment: DatabaseDeployment
    ) -> CaptureStrategy:
        """生成最佳抓包策略"""
        try:
            await self._connect_ssh(server_config)
            
            # 获取网络接口信息
            interfaces = await self.get_network_interfaces(server_config)
            
            # 根据部署类型选择策略
            if deployment.deployment_type == 'docker':
                strategy = await self._generate_docker_strategy(deployment, interfaces)
            elif deployment.deployment_type == 'native':
                strategy = await self._generate_native_strategy(deployment, interfaces)
            else:
                strategy = await self._generate_fallback_strategy(deployment, interfaces)
            
            logger.info(f"Generated capture strategy: {strategy.to_dict()}")
            return strategy
            
        except Exception as e:
            logger.error(f"Failed to generate capture strategy: {str(e)}")
            raise
        finally:
            await self._cleanup()
    
    async def _connect_ssh(self, server_config: ServerConfig):
        """建立SSH连接"""
        try:
            if self.ssh_client:
                try:
                    # 测试现有连接
                    self.ssh_client.exec_command('echo test', timeout=5)
                    return
                except:
                    # 连接已断开，重新连接
                    self.ssh_client.close()
                    self.ssh_client = None
            
            self.ssh_client = paramiko.SSHClient()
            self.ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            
            logger.info(f"Connecting to server: {server_config.host}:{server_config.port}")
            self.ssh_client.connect(
                hostname=server_config.host,
                port=server_config.port,
                username=server_config.username,
                password=server_config.password,
                timeout=10
            )
            
            logger.info("SSH connection established successfully")
            
        except Exception as e:
            logger.error(f"Failed to establish SSH connection: {str(e)}")
            raise Exception(f"SSH connection failed: {str(e)}")
    
    async def _check_docker_deployment(
        self, 
        database_type: str, 
        database_host: str, 
        database_port: int
    ) -> Optional[DatabaseDeployment]:
        """检查Docker容器部署"""
        try:
            # 检查Docker是否可用
            stdin, stdout, stderr = self.ssh_client.exec_command("docker --version")
            if stdout.channel.recv_exit_status() != 0:
                return None
            
            # 搜索相关的Docker容器
            search_patterns = {
                'mysql': ['mysql', 'mariadb'],
                'postgresql': ['postgres', 'postgresql'],
                'mongodb': ['mongo', 'mongodb'],
                'oracle': ['oracle']
            }
            
            patterns = search_patterns.get(database_type, [database_type])
            
            for pattern in patterns:
                # 检查运行中的容器
                cmd = f"docker ps --format '{{{{.Names}}}}\\t{{{{.Image}}}}\\t{{{{.Ports}}}}' | grep -i {pattern}"
                stdin, stdout, stderr = self.ssh_client.exec_command(cmd)
                output = stdout.read().decode('utf-8').strip()
                
                if output:
                    lines = output.split('\n')
                    for line in lines:
                        parts = line.split('\t')
                        if len(parts) >= 3:
                            container_name = parts[0]
                            image = parts[1]
                            ports = parts[2]
                            
                            # 检查端口是否匹配
                            if str(database_port) in ports:
                                # 获取容器IP
                                container_ip = await self._get_container_ip(container_name)
                                
                                return DatabaseDeployment(
                                    database_type=database_type,
                                    deployment_type='docker',
                                    host=database_host,
                                    port=database_port,
                                    container_name=container_name,
                                    container_ip=container_ip
                                )
            
            return None
            
        except Exception as e:
            logger.debug(f"Docker deployment check failed: {str(e)}")
            return None
    
    async def _check_native_deployment(
        self, 
        database_type: str, 
        database_host: str, 
        database_port: int
    ) -> Optional[DatabaseDeployment]:
        """检查本地进程部署"""
        try:
            # 检查端口监听
            cmd = f"netstat -tlnp | grep :{database_port}"
            stdin, stdout, stderr = self.ssh_client.exec_command(cmd)
            output = stdout.read().decode('utf-8').strip()
            
            if output:
                # 解析进程信息
                lines = output.split('\n')
                for line in lines:
                    if f":{database_port}" in line:
                        # 提取进程信息
                        parts = line.split()
                        if len(parts) >= 7:
                            process_info = {
                                'listen_address': parts[3],
                                'process_info': parts[6] if parts[6] != '-' else None
                            }
                            
                            return DatabaseDeployment(
                                database_type=database_type,
                                deployment_type='native',
                                host=database_host,
                                port=database_port,
                                process_info=process_info
                            )
            
            return None
            
        except Exception as e:
            logger.debug(f"Native deployment check failed: {str(e)}")
            return None
    
    async def _get_container_ip(self, container_name: str) -> Optional[str]:
        """获取Docker容器IP地址"""
        try:
            cmd = f"docker inspect -f '{{{{.NetworkSettings.IPAddress}}}}' {container_name}"
            stdin, stdout, stderr = self.ssh_client.exec_command(cmd)
            container_ip = stdout.read().decode('utf-8').strip()

            return container_ip if container_ip else None

        except Exception as e:
            logger.debug(f"Failed to get container IP: {str(e)}")
            return None

    def _parse_interface_block(self, block: str) -> Optional[NetworkInterface]:
        """解析网络接口信息块"""
        try:
            lines = block.strip().split('\n')
            if not lines:
                return None

            # 解析接口名称和状态
            first_line = lines[0]
            interface_match = re.match(r'(\d+):\s*([^:@]+)[@:]?[^:]*:\s*<([^>]+)>', first_line)
            if not interface_match:
                return None

            interface_name = interface_match.group(2)
            flags = interface_match.group(3)
            is_up = 'UP' in flags

            # 确定接口类型
            interface_type = self._determine_interface_type(interface_name, flags)

            # 查找IP地址
            ip_address = None
            for line in lines[1:]:
                inet_match = re.search(r'inet\s+([0-9.]+)', line)
                if inet_match:
                    ip_address = inet_match.group(1)
                    break

            if not ip_address:
                ip_address = "N/A"

            return NetworkInterface(
                name=interface_name,
                ip_address=ip_address,
                is_up=is_up,
                interface_type=interface_type
            )

        except Exception as e:
            logger.debug(f"Failed to parse interface block: {str(e)}")
            return None

    def _determine_interface_type(self, name: str, flags: str) -> str:
        """确定网络接口类型"""
        if name == 'lo':
            return 'loopback'
        elif name.startswith('docker'):
            return 'docker'
        elif name.startswith('veth'):
            return 'virtual'
        elif name.startswith(('eth', 'ens', 'enp')):
            return 'physical'
        else:
            return 'unknown'

    async def _generate_docker_strategy(
        self,
        deployment: DatabaseDeployment,
        interfaces: List[NetworkInterface]
    ) -> CaptureStrategy:
        """为Docker部署生成抓包策略 - 确保捕获完整的握手和挥手包"""
        # 优先选择docker0接口
        docker_interface = next((iface for iface in interfaces if iface.name == 'docker0'), None)

        if docker_interface and deployment.container_ip:
            # 使用Docker网桥，但使用端口过滤而不是主机过滤
            # 重要：对于Docker容器，监听容器内部的标准端口，而不是主机映射端口
            if deployment.database_type == 'postgresql':
                container_port = 5432  # PostgreSQL容器内部端口
                filter_expr = f"tcp port {container_port}"
                logger.info(f"PostgreSQL Docker策略 - 使用docker0接口，监听容器内部端口: {container_port} (主机端口: {deployment.port})")
            elif deployment.database_type == 'gaussdb':
                container_port = 5432  # GaussDB容器内部端口（兼容PostgreSQL协议）
                filter_expr = f"tcp port {container_port}"
                logger.info(f"GaussDB Docker策略 - 使用docker0接口，监听容器内部端口: {container_port} (主机端口: {deployment.port})")
            elif deployment.database_type == 'mongodb':
                container_port = 27017  # MongoDB容器内部端口
                filter_expr = f"tcp port {container_port}"
                logger.info(f"MongoDB Docker策略 - 使用docker0接口，监听容器内部端口: {container_port} (主机端口: {deployment.port})")
            elif deployment.database_type == 'oracle':
                container_port = 1521  # Oracle容器内部端口
                filter_expr = f"tcp port {container_port}"
                logger.info(f"Oracle Docker策略 - 使用docker0接口，监听容器内部端口: {container_port} (主机端口: {deployment.port})")
            elif deployment.database_type == 'mysql':
                container_port = 3306  # MySQL容器内部端口
                filter_expr = f"tcp port {container_port}"
                logger.info(f"MySQL Docker策略 - 使用docker0接口，监听容器内部端口: {container_port} (主机端口: {deployment.port})")
            else:
                # 未知数据库类型，使用主机端口
                filter_expr = f"tcp port {deployment.port}"
                logger.info(f"Docker策略 - 使用docker0接口，端口过滤: {deployment.port}")
            confidence = 0.9
        else:
            # 回退到物理接口
            physical_interfaces = [iface for iface in interfaces if iface.interface_type == 'physical' and iface.is_up]
            if physical_interfaces:
                # 优先选择eth0或ens3
                preferred_names = ['eth0', 'ens3']
                docker_interface = None
                for name in preferred_names:
                    for iface in physical_interfaces:
                        if iface.name == name:
                            docker_interface = iface
                            break
                    if docker_interface:
                        break

                if not docker_interface:
                    docker_interface = physical_interfaces[0]

                # 对于Docker容器，即使使用物理接口，也要监听容器内部端口
                if deployment.database_type == 'postgresql':
                    container_port = 5432
                    filter_expr = f"tcp port {container_port}"
                    logger.info(f"PostgreSQL Docker策略 - 使用物理接口回退: {docker_interface.name}, 监听容器内部端口: {container_port}")
                elif deployment.database_type == 'gaussdb':
                    container_port = 5432
                    filter_expr = f"tcp port {container_port}"
                    logger.info(f"GaussDB Docker策略 - 使用物理接口回退: {docker_interface.name}, 监听容器内部端口: {container_port}")
                elif deployment.database_type == 'mongodb':
                    container_port = 27017
                    filter_expr = f"tcp port {container_port}"
                    logger.info(f"MongoDB Docker策略 - 使用物理接口回退: {docker_interface.name}, 监听容器内部端口: {container_port}")
                elif deployment.database_type == 'oracle':
                    container_port = 1521
                    filter_expr = f"tcp port {container_port}"
                    logger.info(f"Oracle Docker策略 - 使用物理接口回退: {docker_interface.name}, 监听容器内部端口: {container_port}")
                elif deployment.database_type == 'mysql':
                    container_port = 3306
                    filter_expr = f"tcp port {container_port}"
                    logger.info(f"MySQL Docker策略 - 使用物理接口回退: {docker_interface.name}, 监听容器内部端口: {container_port}")
                else:
                    filter_expr = f"tcp port {deployment.port}"
                    logger.info(f"Docker策略 - 使用物理接口回退: {docker_interface.name}, 端口: {deployment.port}")
                confidence = 0.5
            else:
                # 没有可用接口，返回None
                logger.error("No suitable network interface available for Docker strategy")
                return None

        return CaptureStrategy(
            interface=docker_interface.name,
            filter_expression=filter_expr,
            deployment_info=deployment,
            confidence=confidence,
            capture_options={
                'capture_handshake': True,  # 确保捕获TCP握手包
                'capture_teardown': True,   # 确保捕获TCP挥手包
                'buffer_size': 65536,       # 增大缓冲区
                'snaplen': 0               # 捕获完整数据包
            }
        )

    async def _generate_native_strategy(
        self,
        deployment: DatabaseDeployment,
        interfaces: List[NetworkInterface]
    ) -> CaptureStrategy:
        """为本地部署生成抓包策略 - 确保捕获完整的握手和挥手包"""
        # 优先选择物理网络接口
        physical_interfaces = [iface for iface in interfaces if iface.interface_type == 'physical' and iface.is_up]

        if physical_interfaces:
            # 使用第一个物理接口
            interface = physical_interfaces[0]
            # 使用端口过滤而不是主机过滤，确保捕获所有连接
            filter_expr = f"tcp port {deployment.port}"
            confidence = 0.8
        else:
            # 没有物理接口可用，返回None
            logger.error("No physical network interface available for native strategy")
            return None

        return CaptureStrategy(
            interface=interface.name,
            filter_expression=filter_expr,
            deployment_info=deployment,
            confidence=confidence,
            capture_options={
                'capture_handshake': True,  # 确保捕获TCP握手包
                'capture_teardown': True,   # 确保捕获TCP挥手包
                'buffer_size': 65536,       # 增大缓冲区
                'snaplen': 0               # 捕获完整数据包
            }
        )

    async def _generate_fallback_strategy(
        self,
        deployment: DatabaseDeployment,
        interfaces: List[NetworkInterface]
    ) -> CaptureStrategy:
        """生成回退抓包策略 - 使用最后可用的物理接口"""
        # 尝试找到任何可用的物理接口
        physical_interfaces = [iface for iface in interfaces if iface.interface_type == 'physical' and iface.is_up]

        if physical_interfaces:
            # 使用第一个可用的物理接口
            selected_interface = physical_interfaces[0]
            return CaptureStrategy(
                interface=selected_interface.name,
                filter_expression=f"tcp port {deployment.port}",
                deployment_info=deployment,
                confidence=0.2,
                capture_options={
                    'capture_handshake': True,  # 确保捕获TCP握手包
                    'capture_teardown': True,   # 确保捕获TCP挥手包
                    'buffer_size': 65536,       # 增大缓冲区
                    'snaplen': 0               # 捕获完整数据包
                }
            )
        else:
            # 没有可用的物理接口，抓包失败
            logger.error("No physical network interface available for fallback strategy")
            return None

    async def _cleanup(self):
        """清理SSH连接"""
        try:
            if self.ssh_client:
                self.ssh_client.close()
                self.ssh_client = None
        except Exception as e:
            logger.debug(f"Failed to cleanup SSH connection: {str(e)}")

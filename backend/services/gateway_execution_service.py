"""
网关执行服务 - 实现PCAP文件上传、Kafka消费和SQL对比功能
"""
import asyncio
import logging
import json
import os
import traceback
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
import paramiko
import subprocess
from pathlib import Path

try:
    from confluent_kafka import Consumer
    KAFKA_AVAILABLE = True
except ImportError:
    KAFKA_AVAILABLE = False
    Consumer = None

from models.gateway_server import GatewayServer
from models.test_case_management import TestCaseManagementResponse
from services.test_case_management_service import test_case_management_service
from services.gateway_server_service import gateway_server_service
from services.ai_service import ai_service

logger = logging.getLogger(__name__)


class GatewayExecutionService:
    """网关执行服务"""

    def __init__(self):
        self.temp_dir = "/tmp/gateway_execution"
        os.makedirs(self.temp_dir, exist_ok=True)

    async def execute_gateway_test(
        self,
        test_case_id: str,
        gateway_server_id: int,
        wait_time: int = 2
    ) -> Dict[str, Any]:
        """
        执行网关测试的完整流程：
        1. 上传最新执行记录的PCAP文件到网关服务器
        2. 消费Kafka数据并过滤
        3. 提取SQL并进行AI对比
        
        Args:
            test_case_id: 测试用例ID
            gateway_server_id: 网关服务器ID
            wait_time: Kafka消费等待时间（秒）
        
        Returns:
            执行结果字典
        """
        try:
            logger.info(f"开始网关测试执行 - 测试用例ID: {test_case_id}, 网关服务器ID: {gateway_server_id}")

            # 1. 获取测试用例和网关服务器信息
            test_case = await test_case_management_service.get_test_case(test_case_id)
            if not test_case:
                raise Exception(f"测试用例不存在: {test_case_id}")

            gateway_server = await gateway_server_service.get_gateway(gateway_server_id)
            if not gateway_server:
                raise Exception(f"网关服务器不存在: {gateway_server_id}")

            # 2. 检查网关服务器是否支持Kafka
            if not gateway_server.kafka_enabled:
                raise Exception(f"网关服务器 {gateway_server.name} 未启用Kafka功能")

            # 3. 上传PCAP文件到网关服务器
            logger.info("开始上传PCAP文件到网关服务器...")
            upload_result = await self._upload_pcap_files(test_case, gateway_server)
            if not upload_result["success"]:
                return {
                    "success": False,
                    "error": f"PCAP文件上传失败: {upload_result['error']}",
                    "step": "pcap_upload"
                }
            # 仅记录上传结果到日志，不在响应体中回传明细
            logger.info(
                f"PCAP上传完成: 数量={upload_result.get('uploaded_files_count', 0)}, 目录={upload_result.get('remote_directory', '')}"
            )
            if upload_result.get("uploaded_files"):
                for f in upload_result["uploaded_files"]:
                    logger.debug(f"上传文件: local={f.get('local_path')} -> remote={f.get('remote_path')} size={f.get('file_size')}")

            # 4. 等待PCAP处理产生Kafka消息
            pcap_processing_wait = max(wait_time, 5)  # 至少等待5秒让系统处理PCAP
            logger.info(f"PCAP文件上传完成，等待 {pcap_processing_wait} 秒让系统处理PCAP并产生Kafka消息...")
            await asyncio.sleep(pcap_processing_wait)
            
            # 5. 消费Kafka数据
            logger.info("开始消费Kafka数据...")
            kafka_consume_time = 15  # Kafka消费时间固定为15秒
            kafka_result = await self._consume_kafka_data(gateway_server, kafka_consume_time)
            if not kafka_result["success"]:
                return {
                    "success": False,
                    "error": f"Kafka数据消费失败: {kafka_result['error']}",
                    "step": "kafka_consume"
                }
            # 仅记录Kafka消费摘要到日志，不在响应体中回传明细
            logger.info(
                f"Kafka消费完成: 条数={kafka_result.get('messages_count', 0)}, 时长={kafka_result.get('consume_duration', kafka_consume_time)}s, 主题={gateway_server.kafka_topic}"
            )

            # 6. 提取并对比SQL
            logger.info("开始SQL对比...")
            # 构建 step_index -> pcap 元信息映射，供对比阶段标注 SQL 对应的 PCAP
            pcap_map = {}
            try:
                for f in upload_result.get("uploaded_files", []) or []:
                    step_idx = f.get("step_index")
                    if step_idx is not None:
                        pcap_map[step_idx] = {
                            "file_name": os.path.basename(f.get("local_path", "")) or os.path.basename(f.get("remote_path", "")),
                            "local_path": f.get("local_path"),
                            "remote_path": f.get("remote_path"),
                        }
            except Exception:
                pass

            sql_comparison_result = await self._compare_sql_statements(
                test_case,
                kafka_result["messages"],
                pcap_map
            )
            if not sql_comparison_result["success"]:
                return {
                    "success": False,
                    "error": f"SQL对比失败: {sql_comparison_result['error']}",
                    "step": "sql_compare"
                }

            # 7. 返回精简结果：仅回传提取集合与对比结果
            # 将提取集合（kafka_sql_set）打印到日志便于排查
            try:
                kafka_set_preview = sql_comparison_result.get("kafka_sql_set", [])[:10]
                logger.info(f"Kafka提取SQL唯一集合数: {sql_comparison_result.get('kafka_sql_count', 0)}，前10条预览: {kafka_set_preview}")
            except Exception:
                pass

            return {
                "success": True,
                "test_case_id": test_case_id,
                "gateway_server": {
                    "id": gateway_server.id,
                    "name": gateway_server.name
                },
                "sql_comparison": sql_comparison_result,
                "summary": {
                    "total_pcap_files": upload_result.get("uploaded_files_count", 0),
                    "sql_statements_compared": sql_comparison_result.get("compared_count", 0),
                    "sql_match_rate": sql_comparison_result.get("match_rate", 0.0)
                },
                "execution_time": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"网关测试执行失败: {str(e)}")
            logger.error(traceback.format_exc())
            return {
                "success": False,
                "error": str(e),
                "step": "execution_error"
            }

    async def _upload_pcap_files(
        self,
        test_case: TestCaseManagementResponse,
        gateway_server: GatewayServer
    ) -> Dict[str, Any]:
        """
        上传最新执行记录的PCAP文件到网关服务器
        
        Args:
            test_case: 测试用例信息
            gateway_server: 网关服务器信息
        
        Returns:
            上传结果字典
        """
        try:
            # 获取最新的执行记录
            if not test_case.last_execution_time:
                raise Exception("测试用例没有最新执行记录")

            # 使用 last_execution_capture_files 获取PCAP文件列表
            capture_files = test_case.last_execution_capture_files
            if not capture_files:
                logger.warning("没有找到PCAP文件信息")
                return {
                    "success": True,
                    "uploaded_files": [],
                    "uploaded_files_count": 0,
                    "message": "没有PCAP文件需要上传"
                }

            # 收集所有PCAP文件路径
            pcap_files = []
            base_capture_dir = "/Users/<USER>/Desktop/qz_code/ai_sql_pcap/backend/captures"  # 捕获文件基础目录
            
            if isinstance(capture_files, list):
                # 如果是列表格式
                for i, file_name in enumerate(capture_files):
                    if file_name:
                        # 拼接完整路径
                        if file_name.startswith('/'):
                            # 已经是绝对路径
                            full_path = file_name
                        else:
                            # 相对路径，拼接基础目录
                            full_path = os.path.join(base_capture_dir, file_name)
                        
                        if os.path.exists(full_path):
                            pcap_files.append({
                                "local_path": full_path,
                                "step_index": i + 1,
                                "step_name": f"step_{i + 1}_{Path(full_path).name}"
                            })
                        else:
                            logger.warning(f"PCAP文件不存在: {full_path}")
            elif isinstance(capture_files, dict):
                # 如果是字典格式 {step_index: file_path}
                for step_index, file_name in capture_files.items():
                    if file_name:
                        # 拼接完整路径
                        if file_name.startswith('/'):
                            # 已经是绝对路径
                            full_path = file_name
                        else:
                            # 相对路径，拼接基础目录
                            full_path = os.path.join(base_capture_dir, file_name)
                        
                        if os.path.exists(full_path):
                            pcap_files.append({
                                "local_path": full_path,
                                "step_index": step_index,
                                "step_name": f"step_{step_index}_{Path(full_path).name}"
                            })
                        else:
                            logger.warning(f"PCAP文件不存在: {full_path}")

            if not pcap_files:
                logger.warning("没有找到可上传的PCAP文件")
                return {
                    "success": True,
                    "uploaded_files": [],
                    "uploaded_files_count": 0,
                    "message": "没有PCAP文件需要上传"
                }

            # 通过SSH上传文件
            uploaded_files = []
            ssh_client = None
            sftp_client = None

            try:
                # 建立SSH连接
                ssh_client = paramiko.SSHClient()
                ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
                
                logger.info(f"连接到网关服务器: {gateway_server.host}:{gateway_server.port}")
                ssh_client.connect(
                    hostname=gateway_server.host,
                    port=gateway_server.port,
                    username=gateway_server.username,
                    password=gateway_server.password,
                    timeout=30
                )

                sftp_client = ssh_client.open_sftp()

                # 确保远程目录存在
                remote_base_dir = gateway_server.upload_path or "/tmp/pcap_files"
                
                # 直接使用配置的上传路径，不创建子目录
                test_case_dir = remote_base_dir
                
                # 确保远程目录存在
                try:
                    # 使用SSH命令创建目录（更可靠）
                    stdin, stdout, stderr = ssh_client.exec_command(f'mkdir -p {test_case_dir}')
                    exit_status = stdout.channel.recv_exit_status()
                    if exit_status != 0:
                        error_output = stderr.read().decode()
                        logger.warning(f"创建远程目录失败: {error_output}")
                except Exception as e:
                    logger.warning(f"创建远程目录失败: {e}")

                # 上传每个PCAP文件
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                for pcap_info in pcap_files:
                    local_path = pcap_info["local_path"]
                    # 在文件名前添加时间戳和测试用例ID的前8位，避免文件名冲突
                    original_filename = pcap_info['step_name']
                    test_case_prefix = test_case.id[:8]  # 取ID的前8位作为前缀
                    remote_filename = f"{test_case_prefix}_{timestamp}_{original_filename}"
                    remote_path = f"{test_case_dir}/{remote_filename}"
                    
                    logger.info(f"上传文件: {local_path} -> {remote_path}")
                    sftp_client.put(local_path, remote_path)
                    
                    uploaded_files.append({
                        "step_index": pcap_info["step_index"],
                        "local_path": local_path,
                        "remote_path": remote_path,
                        "file_size": os.path.getsize(local_path)
                    })

                logger.info(f"成功上传 {len(uploaded_files)} 个PCAP文件到网关服务器")
                return {
                    "success": True,
                    "uploaded_files": uploaded_files,
                    "uploaded_files_count": len(uploaded_files),
                    "remote_directory": test_case_dir,
                    "message": f"成功上传 {len(uploaded_files)} 个PCAP文件"
                }

            finally:
                if sftp_client:
                    sftp_client.close()
                if ssh_client:
                    ssh_client.close()

        except Exception as e:
            logger.error(f"PCAP文件上传失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "uploaded_files": [],
                "uploaded_files_count": 0
            }

    async def _consume_kafka_data(
        self,
        gateway_server: GatewayServer,
        wait_time: int = 2
    ) -> Dict[str, Any]:
        """
        消费Kafka数据，过滤掉cmd_type=login的消息
        
        Args:
            gateway_server: 网关服务器信息
            wait_time: 等待时间（秒）
        
        Returns:
            消费结果字典
        """
        try:
            if not KAFKA_AVAILABLE:
                raise Exception("confluent-kafka库未安装，无法消费Kafka数据。请运行: pip install confluent-kafka")

            # 配置 Kafka 消费者
            kafka_host = f"{gateway_server.kafka_host}:{gateway_server.kafka_port}"
            consumer_config = {
                'bootstrap.servers': kafka_host,
                'group.id': f'gateway_test_{datetime.now().strftime("%Y%m%d_%H%M%S_%f")}',  # 确保唯一
                'auto.offset.reset': 'latest',  # 默认策略
                'enable.auto.commit': False,
            }

            from confluent_kafka import Consumer, TopicPartition
            consumer = Consumer(consumer_config)
            consumer.subscribe([gateway_server.kafka_topic])
            
            # 等待分区分配
            logger.info("等待Kafka分区分配...")
            consumer.poll(timeout=2.0)
            
            # 获取分配的分区并设置offset到最近的消息（消费最近的数据，而不是等待新数据）
            partitions = consumer.assignment()
            if partitions:
                for tp in partitions:
                    # 获取该分区的低水位和高水位
                    low_water, high_water = consumer.get_watermark_offsets(tp)
                    logger.info(f"分区 {tp.partition}: 低水位={low_water}, 高水位={high_water}")
                    
                    if high_water > low_water:
                        # 从最近几条消息开始消费，避免消费太多历史数据
                        # 设置消费最近50条消息，如果消息少于50条则从头开始
                        recent_start = max(low_water, high_water - 50)
                        tp.offset = recent_start
                        logger.info(f"设置分区 {tp.partition} 从offset {recent_start} 开始消费（消费最近的数据）")
                    else:
                        # 如果没有消息，设置到最新位置等待新消息
                        tp.offset = high_water
                        logger.info(f"分区 {tp.partition} 暂无消息，设置到最新位置 {high_water} 等待新消息")
                
                consumer.assign(partitions)
            else:
                logger.warning("未分配到Kafka分区")

            logger.info(f"开始消费Kafka主题: {gateway_server.kafka_topic}, 等待时间: {wait_time}秒")

            # 消费消息
            messages = []
            start_time = datetime.now()
            end_time = start_time + timedelta(seconds=wait_time)

            # 在指定时间内消费消息
            while datetime.now() < end_time:
                msg = consumer.poll(timeout=1.0)
                
                if msg is None:
                    continue
                    
                if msg.error():
                    logger.error(f"Kafka消费错误: {msg.error()}")
                    continue
                
                try:
                    # 解析消息内容
                    message_value = msg.value().decode('utf-8') if msg.value() else None
                    if message_value:
                        # 尝试解析为JSON
                        try:
                            message_data = json.loads(message_value)
                        except json.JSONDecodeError:
                            # 如果不是JSON，作为普通字符串处理
                            message_data = {"raw_message": message_value}

                        # 过滤掉cmd_type=login的消息
                        cmd_type = message_data.get("cmd_type", "").lower()
                        if cmd_type != "login":
                            messages.append({
                                "timestamp": msg.timestamp()[1] if msg.timestamp()[1] > 0 else int(datetime.now().timestamp() * 1000),
                                "partition": msg.partition(),
                                "offset": msg.offset(),
                                "data": message_data,
                                "raw_value": message_value
                            })
                            logger.debug(f"收到有效消息 (过滤login): {cmd_type}")
                        else:
                            logger.debug(f"过滤login消息: {message_data}")

                except Exception as e:
                    logger.warning(f"解析Kafka消息失败: {e}")

            consumer.close()

            logger.info(f"Kafka消费完成，获得 {len(messages)} 条有效消息（已过滤login消息）")
            return {
                "success": True,
                "messages": messages,
                "messages_count": len(messages),
                "consume_duration": wait_time,
                "kafka_config": {
                    "host": gateway_server.kafka_host,
                    "port": gateway_server.kafka_port,
                    "topic": gateway_server.kafka_topic
                }
            }

        except Exception as e:
            logger.error(f"Kafka数据消费失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "messages": [],
                "messages_count": 0
            }

    async def _compare_sql_statements(
        self,
        test_case: TestCaseManagementResponse,
        kafka_messages: List[Dict[str, Any]],
        pcap_map: Optional[Dict[int, Dict[str, Any]]] = None,
    ) -> Dict[str, Any]:
        """
        从Kafka消息中提取SQL语句，与测试用例数据进行AI对比
        
        Args:
            test_case: 测试用例信息
            kafka_messages: Kafka消息列表
        
        Returns:
            SQL对比结果字典
        """
        try:
            # 1) 从Kafka消息中提取SQL并标准化为集合
            kafka_sql_statements: List[str] = []
            for message in kafka_messages:
                kafka_sql_statements.extend(self._extract_sql_from_message(message))

            def _normalize_sql(sql: str) -> str:
                # 去分号、压缩多空白、去首尾空白、统一大小写
                import re
                s = (sql or "").strip()
                if s.endswith(';'):
                    s = s[:-1]
                s = re.sub(r"\s+", " ", s)
                return s.strip().upper()

            kafka_sql_set = { _normalize_sql(s) for s in kafka_sql_statements if isinstance(s, str) and s.strip() }
            logger.info(f"从Kafka消息中提取到 {len(kafka_sql_statements)} 条SQL语句，规范化后唯一 {len(kafka_sql_set)} 条")

            # 2) 从测试用例中提取SQL，支持以分号分割的多条语句，并保留来源step_index
            test_case_entries: List[Dict[str, Any]] = []  # {sql, step_index}

            # a) 顶层 sql_statements（无法确定步骤）
            if getattr(test_case, 'sql_statements', None):
                for s in test_case.sql_statements:
                    if isinstance(s, str) and s.strip():
                        for p in [p.strip() for p in s.split(';')]:
                            if p:
                                test_case_entries.append({"sql": p, "step_index": None})

            # b) 分步提取（能够关联到具体PCAP）
            if getattr(test_case, 'test_steps', None):
                for step in test_case.test_steps:
                    step_idx = getattr(step, 'step_number', None)
                    sql_src = None
                    if hasattr(step, 'sql_statement') and step.sql_statement:
                        sql_src = step.sql_statement
                    elif hasattr(step, 'test_data') and isinstance(step.test_data, str):
                        sql_src = step.test_data
                    if isinstance(sql_src, str) and sql_src.strip():
                        for p in [p.strip() for p in sql_src.split(';')]:
                            if p:
                                test_case_entries.append({"sql": p, "step_index": step_idx})

            test_case_sql_statements: List[str] = [e["sql"] for e in test_case_entries]
            logger.info(f"从测试用例中解析得到 {len(test_case_sql_statements)} 条SQL语句（分号拆分后）")

            if not kafka_sql_set:
                return {
                    "success": True,
                    "compared_count": len(test_case_sql_statements),
                    "kafka_sql_count": 0,
                    "match_rate": 0.0,
                    "matches": [],
                    "unmatched_kafka": [],
                    "unmatched_test_case": test_case_sql_statements,
                    "unmatched_test_case_details": [
                        {
                            "sql": e["sql"],
                            "step_index": e.get("step_index"),
                            "pcap_file": (pcap_map or {}).get(e.get("step_index"), {}).get("file_name"),
                            "pcap_paths": (pcap_map or {}).get(e.get("step_index"), {})
                        } for e in test_case_entries
                    ],
                    "message": "Kafka消息中没有可用的SQL语句"
                }

            # 3) 先进行集合精确匹配，再对未匹配项使用AI兜底
            matches: List[Dict[str, Any]] = []
            unmatched_test_case: List[str] = []
            unmatched_test_case_details: List[Dict[str, Any]] = []
            ai_attempts = []

            for entry in test_case_entries:
                test_sql = entry["sql"]
                step_idx = entry.get("step_index")
                norm_test = _normalize_sql(test_sql)
                if norm_test in kafka_sql_set:
                    m = {
                        "kafka_sql": test_sql,
                        "matched_test_sql": test_sql,
                        "similarity_score": 1.0,
                        "semantic_match": True,
                        "differences": "",
                    }
                    # 附带PCAP来源
                    if step_idx is not None and pcap_map:
                        pm = pcap_map.get(step_idx, {})
                        m.update({
                            "step_index": step_idx,
                            "pcap_file": pm.get("file_name"),
                            "pcap_paths": pm,
                        })
                    matches.append(m)
                    continue

                # 集合未命中，走AI兜底
                try:
                    comparison_prompt = f"""
比较以下两个SQL是否等价，若等价请给出 similarity_score>=0.7：

SQL_A (测试用例)：
{test_sql}

SQL_B_集合（Kafka中）：
{list(kafka_sql_set)[:20]}

请判断 SQL_A 是否出现在 SQL_B_集合（或等价），返回JSON：
{{
  "semantic_match": true/false,
  "similarity_score": 0.0-1.0,
  "differences": "简要说明"
}}
"""
                    ai_result = await ai_service.analyze_sql_with_ai(comparison_prompt)
                    ai_attempts.append(test_sql)
                    if ai_result.get("success"):
                        analysis = ai_result.get("analysis", {})
                        if analysis.get("semantic_match") and analysis.get("similarity_score", 0.0) >= 0.7:
                            m = {
                                "kafka_sql": "<matched-by-ai>",
                                "matched_test_sql": test_sql,
                                "similarity_score": analysis.get("similarity_score", 0.7),
                                "semantic_match": True,
                                "differences": analysis.get("differences", ""),
                            }
                            if step_idx is not None and pcap_map:
                                pm = pcap_map.get(step_idx, {})
                                m.update({
                                    "step_index": step_idx,
                                    "pcap_file": pm.get("file_name"),
                                    "pcap_paths": pm,
                                })
                            matches.append(m)
                            continue
                except Exception as e:
                    logger.warning(f"AI 兜底比较失败: {e}")

                # 仍未命中则加入未匹配列表
                unmatched_test_case.append(test_sql)
                d = {
                    "sql": test_sql,
                    "step_index": step_idx,
                    "pcap_file": (pcap_map or {}).get(step_idx, {}).get("file_name") if step_idx is not None else None,
                    "pcap_paths": (pcap_map or {}).get(step_idx, {}) if step_idx is not None else {},
                }
                unmatched_test_case_details.append(d)

            total = len(test_case_sql_statements)
            match_rate = (len(matches) / total) if total > 0 else 0.0

            return {
                "success": True,
                "compared_count": total,
                "kafka_sql_count": len(kafka_sql_set),
                "kafka_sql_set": list(kafka_sql_set),
                "match_rate": match_rate,
                "matches": matches,
                "unmatched_kafka": [],
                "unmatched_test_case": unmatched_test_case,
                "unmatched_test_case_details": unmatched_test_case_details,
                "pcap_mapping": {k: v.get("file_name") for k, v in (pcap_map or {}).items()},
                "message": "已完成集合匹配与AI兜底"
            }

            # 4. 计算匹配率
            total_sql_count = len(kafka_sql_statements) + len(test_case_sql_statements)
            match_rate = (len(matches) * 2) / total_sql_count if total_sql_count > 0 else 0.0

            logger.info(f"SQL对比完成 - 匹配: {len(matches)}, 匹配率: {match_rate:.2%}")

            return {
                "success": True,
                "compared_count": len(kafka_sql_statements),
                "match_rate": match_rate,
                "matches": matches,
                "unmatched_kafka": unmatched_kafka,
                "unmatched_test_case": unmatched_test_case,
                "statistics": {
                    "kafka_sql_count": len(kafka_sql_statements),
                    "test_case_sql_count": len(test_case_sql_statements),
                    "matched_pairs": len(matches),
                    "unmatched_kafka_count": len(unmatched_kafka),
                    "unmatched_test_case_count": len(unmatched_test_case)
                }
            }

        except Exception as e:
            logger.error(f"SQL对比失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "compared_count": 0,
                "match_rate": 0.0,
                "matches": [],
                "unmatched_kafka": [],
                "unmatched_test_case": []
            }

    def _extract_sql_from_message(self, message: Dict[str, Any]) -> List[str]:
        """
        从Kafka消息中提取SQL语句
        
        Args:
            message: Kafka消息
        
        Returns:
            SQL语句列表
        """
        sql_statements: List[str] = []

        try:
            message_data = message.get("data", {}) or {}

            # 常见的SQL字段名（支持更多别名）
            sql_fields = ["sql", "query", "statement", "command", "sql_text"]
            sql_verbs = [
                "select", "insert", "update", "delete", "create", "alter", "drop", "explain",
                "begin", "commit", "rollback", "prepare", "execute", "deallocate"
            ]

            # 1) 先尝试在 data 顶层提取
            for field in sql_fields:
                sql_value = message_data.get(field)
                if isinstance(sql_value, str) and sql_value.strip():
                    sql_lower = sql_value.lower().strip()
                    if any(v in sql_lower for v in sql_verbs):
                        sql_statements.append(sql_value.strip())

            # 2) 再尝试在 data.req 中提取（适配 { data: { req: { sql: "..." } } } 结构）
            req = message_data.get("req", {}) or {}
            for field in sql_fields:
                sql_value = req.get(field)
                if isinstance(sql_value, str) and sql_value.strip():
                    sql_lower = sql_value.lower().strip()
                    if any(v in sql_lower for v in sql_verbs):
                        sql_statements.append(sql_value.strip())

            # 3) 如果上面都没命中，尝试从原始消息字符串中用正则提取完整SQL（非捕获分组 + finditer）
            if not sql_statements:
                raw_message = message.get("raw_value", "")
                if isinstance(raw_message, str) and raw_message:
                    import re
                    # 使用非捕获分组，匹配到分号或行尾；忽略大小写与跨行
                    pattern = re.compile(r"(?is)\b(?:select|insert|update|delete|create|alter|drop|explain|begin|commit|rollback|prepare|execute|deallocate)\b[\s\S]*?(?:;|$)")
                    for m in pattern.finditer(raw_message):
                        candidate = m.group(0).strip()
                        if len(candidate) > 10:
                            sql_statements.append(candidate)

        except Exception as e:
            logger.warning(f"从消息中提取SQL失败: {e}")

        # 去重并清理 + 解析 PREPARE 内层 SQL
        cleaned: List[str] = []
        seen = set()
        for s in (s.strip() for s in sql_statements if isinstance(s, str)):
            if not s:
                continue
            if s not in seen:
                seen.add(s)
                cleaned.append(s)

            # 若为 PREPARE 语句，尝试提取 "AS <INNER_SQL>" 并追加
            try:
                import re
                m = re.search(r"(?is)\bprepare\b\s+\w+\s+as\s+([\s\S]+?)(?:;|$)", s)
                if m:
                    inner_sql = m.group(1).strip()
                    if inner_sql and inner_sql not in seen:
                        seen.add(inner_sql)
                        cleaned.append(inner_sql)
            except Exception:
                pass

        return cleaned


# 创建全局服务实例
gateway_execution_service = GatewayExecutionService()

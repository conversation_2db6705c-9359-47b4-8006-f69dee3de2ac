"""
基于MySQL的服务器配置管理服务
"""

import logging
from typing import List, Optional, Dict, Any
from datetime import datetime
import os
import sys

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.server_config import ServerConfig
from services.mysql_service import MySQLService

logger = logging.getLogger(__name__)

class ServerConfigService:
    """服务器配置管理服务"""
    
    def __init__(self):
        self.mysql_service = MySQLService()
        
    async def initialize(self):
        """初始化服务"""
        try:
            # 检查是否有默认配置，如果没有则创建
            result = await self.mysql_service.execute_query(
                "SELECT COUNT(*) as count FROM server_configs WHERE is_default = TRUE"
            )
            
            if result['data'][0]['count'] == 0:
                # 创建默认服务器配置
                await self._create_default_config()
                
            logger.info("Server config service initialized successfully")
                
        except Exception as e:
            logger.error(f"Failed to initialize server config service: {e}")
            raise
    
    async def _create_default_config(self):
        """创建默认服务器配置"""
        default_config = {
            'name': '主服务器',
            'host': '**************',
            'port': 22,
            'username': 'root',
            'password': 'QZ@1005#1005',
            'description': '默认的远程服务器配置',
            'is_active': True,
            'is_default': True
        }
        
        await self.mysql_service.execute_query("""
            INSERT INTO server_configs 
            (name, host, port, username, password, description, is_active, is_default)
            VALUES (%(name)s, %(host)s, %(port)s, %(username)s, %(password)s, %(description)s, %(is_active)s, %(is_default)s)
        """, default_config)
        
        logger.info("Default server config created")
    
    async def create_config(self, config: ServerConfig) -> int:
        """创建服务器配置"""
        try:
            # 如果设置为默认，先取消其他默认配置
            if config.is_default:
                await self.mysql_service.execute_query(
                    "UPDATE server_configs SET is_default = FALSE"
                )

            result = await self.mysql_service.execute_query("""
                INSERT INTO server_configs
                (name, host, port, username, password, description, is_active, is_default)
                VALUES (%(name)s, %(host)s, %(port)s, %(username)s, %(password)s, %(description)s, %(is_active)s, %(is_default)s)
            """, {
                'name': config.name,
                'host': config.host,
                'port': config.port,
                'username': config.username,
                'password': config.password,
                'description': config.description,
                'is_active': config.is_active,
                'is_default': config.is_default
            })

            # 直接从INSERT结果中获取插入的ID
            config_id = result.get('insert_id')
            if not config_id:
                # 如果insert_id为空，尝试使用LAST_INSERT_ID()作为备选方案
                id_result = await self.mysql_service.execute_query("SELECT LAST_INSERT_ID() as id")
                config_id = id_result['data'][0]['id']

            logger.info(f"Server config created with ID: {config_id}")
            return config_id

        except Exception as e:
            logger.error(f"Failed to create server config: {e}")
            raise
    
    async def get_config(self, config_id: int) -> Optional[ServerConfig]:
        """获取服务器配置"""
        try:
            result = await self.mysql_service.execute_query(
                "SELECT * FROM server_configs WHERE id = %s AND is_active = TRUE",
                (config_id,)
            )
            
            if result['data']:
                row = result['data'][0]
                return self._row_to_config(row)
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to get server config: {e}")
            raise
    
    async def get_default_config(self) -> Optional[ServerConfig]:
        """获取默认服务器配置"""
        try:
            result = await self.mysql_service.execute_query(
                "SELECT * FROM server_configs WHERE is_default = TRUE AND is_active = TRUE"
            )
            
            if result['data']:
                row = result['data'][0]
                return self._row_to_config(row)
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to get default server config: {e}")
            raise
    
    async def list_configs(self) -> List[ServerConfig]:
        """获取所有服务器配置"""
        try:
            result = await self.mysql_service.execute_query(
                "SELECT * FROM server_configs WHERE is_active = TRUE ORDER BY is_default DESC, name ASC"
            )
            
            return [self._row_to_config(row) for row in result['data']]
            
        except Exception as e:
            logger.error(f"Failed to list server configs: {e}")
            raise
    
    async def update_config(self, config: ServerConfig) -> bool:
        """更新服务器配置"""
        try:
            # 如果设置为默认，先取消其他默认配置
            if config.is_default:
                await self.mysql_service.execute_query(
                    "UPDATE server_configs SET is_default = FALSE WHERE id != %s",
                    (config.id,)
                )
            
            await self.mysql_service.execute_query("""
                UPDATE server_configs SET
                    name = %s, host = %s, port = %s, username = %s, password = %s,
                    description = %s, is_active = %s, is_default = %s, updated_at = CURRENT_TIMESTAMP
                WHERE id = %s
            """, (
                config.name, config.host, config.port, config.username, config.password,
                config.description, config.is_active, config.is_default, config.id
            ))
            
            logger.info(f"Server config updated: {config.id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to update server config: {e}")
            raise
    
    async def delete_config(self, config_id: int) -> bool:
        """删除服务器配置（软删除）"""
        try:
            await self.mysql_service.execute_query(
                "UPDATE server_configs SET is_active = FALSE, updated_at = CURRENT_TIMESTAMP WHERE id = %s",
                (config_id,)
            )
            
            logger.info(f"Server config deleted: {config_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete server config: {e}")
            raise
    
    def _row_to_config(self, row: Dict[str, Any]) -> ServerConfig:
        """将数据库行转换为ServerConfig对象"""
        return ServerConfig(
            id=row['id'],
            name=row['name'],
            host=row['host'],
            port=row['port'],
            username=row['username'],
            password=row['password'],
            description=row.get('description', ''),
            is_active=bool(row['is_active']),
            is_default=bool(row['is_default']),
            created_at=str(row['created_at']),
            updated_at=str(row['updated_at'])
        )

# 全局服务实例
server_config_service = ServerConfigService()

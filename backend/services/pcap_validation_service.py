#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
抓包后自检服务 - 用于验证抓包是否成功捕获到预期的SQL语句
"""

import os
import json
import time
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from .pcap_sql_analyzer import PCAPSQLAnalyzer, AnalysisResult, SQLStatement, SQLType, DatabaseType

# 配置日志
logger = logging.getLogger(__name__)

class ValidationStatus(Enum):
    """验证状态枚举"""
    SUCCESS = "success"
    FAILED = "failed"
    PARTIAL = "partial"
    NO_SQL_FOUND = "no_sql_found"
    FILE_NOT_FOUND = "file_not_found"
    ANALYSIS_ERROR = "analysis_error"

@dataclass
class ExpectedSQL:
    """预期的SQL语句"""
    sql_pattern: str  # SQL模式或关键字
    sql_type: SQLType  # SQL类型
    database_type: DatabaseType  # 数据库类型
    required: bool = True  # 是否必须存在
    min_occurrences: int = 1  # 最少出现次数

@dataclass
class ValidationRule:
    """验证规则"""
    name: str
    description: str
    expected_sqls: List[ExpectedSQL]
    min_total_sql_count: int = 1  # 最少SQL总数
    min_confidence: float = 0.5  # 最低置信度要求

@dataclass
class ValidationResult:
    """验证结果"""
    pcap_file: str
    status: ValidationStatus
    success: bool
    total_sql_found: int
    expected_sql_matches: Dict[str, int]  # 预期SQL的匹配次数
    missing_sqls: List[str]  # 缺失的SQL
    analysis_result: Optional[AnalysisResult]
    validation_time: float
    error_message: Optional[str] = None
    recommendations: List[str] = None

class PCAPValidationService:
    """PCAP验证服务"""
    
    def __init__(self):
        """初始化验证服务"""
        self.analyzer = PCAPSQLAnalyzer()
        self.validation_rules = {}
        self._load_default_rules()
    
    def _load_default_rules(self):
        """加载默认验证规则"""
        # MySQL默认规则
        mysql_rules = ValidationRule(
            name="mysql_basic",
            description="MySQL基础SQL验证",
            expected_sqls=[
                ExpectedSQL("SELECT", SQLType.SELECT, DatabaseType.MYSQL, required=False),
                ExpectedSQL("INSERT", SQLType.INSERT, DatabaseType.MYSQL, required=False),
                ExpectedSQL("UPDATE", SQLType.UPDATE, DatabaseType.MYSQL, required=False),
                ExpectedSQL("DELETE", SQLType.DELETE, DatabaseType.MYSQL, required=False),
            ],
            min_total_sql_count=1,
            min_confidence=0.6
        )
        
        # PostgreSQL默认规则
        postgresql_rules = ValidationRule(
            name="postgresql_basic",
            description="PostgreSQL基础SQL验证",
            expected_sqls=[
                ExpectedSQL("SELECT", SQLType.SELECT, DatabaseType.POSTGRESQL, required=False),
                ExpectedSQL("INSERT", SQLType.INSERT, DatabaseType.POSTGRESQL, required=False),
                ExpectedSQL("UPDATE", SQLType.UPDATE, DatabaseType.POSTGRESQL, required=False),
                ExpectedSQL("DELETE", SQLType.DELETE, DatabaseType.POSTGRESQL, required=False),
            ],
            min_total_sql_count=1,
            min_confidence=0.6
        )
        
        # MongoDB默认规则
        mongodb_rules = ValidationRule(
            name="mongodb_basic",
            description="MongoDB基础查询验证",
            expected_sqls=[
                ExpectedSQL("find", SQLType.SELECT, DatabaseType.MONGODB, required=False),
                ExpectedSQL("insert", SQLType.INSERT, DatabaseType.MONGODB, required=False),
                ExpectedSQL("update", SQLType.UPDATE, DatabaseType.MONGODB, required=False),
                ExpectedSQL("delete", SQLType.DELETE, DatabaseType.MONGODB, required=False),
            ],
            min_total_sql_count=1,
            min_confidence=0.6
        )
        
        # Oracle默认规则
        oracle_rules = ValidationRule(
            name="oracle_basic",
            description="Oracle基础SQL验证",
            expected_sqls=[
                ExpectedSQL("SELECT", SQLType.SELECT, DatabaseType.ORACLE, required=False),
                ExpectedSQL("INSERT", SQLType.INSERT, DatabaseType.ORACLE, required=False),
                ExpectedSQL("UPDATE", SQLType.UPDATE, DatabaseType.ORACLE, required=False),
                ExpectedSQL("DELETE", SQLType.DELETE, DatabaseType.ORACLE, required=False),
            ],
            min_total_sql_count=1,
            min_confidence=0.6
        )
        
        # SQL Server默认规则
        sqlserver_rules = ValidationRule(
            name="sqlserver_basic",
            description="SQL Server基础SQL验证",
            expected_sqls=[
                ExpectedSQL("SELECT", SQLType.SELECT, DatabaseType.SQLSERVER, required=False),
                ExpectedSQL("INSERT", SQLType.INSERT, DatabaseType.SQLSERVER, required=False),
                ExpectedSQL("UPDATE", SQLType.UPDATE, DatabaseType.SQLSERVER, required=False),
                ExpectedSQL("DELETE", SQLType.DELETE, DatabaseType.SQLSERVER, required=False),
            ],
            min_total_sql_count=1,
            min_confidence=0.6
        )

        # GaussDB默认规则
        gaussdb_rules = ValidationRule(
            name="gaussdb_basic",
            description="GaussDB基础SQL验证",
            expected_sqls=[
                ExpectedSQL("SELECT", SQLType.SELECT, DatabaseType.GAUSSDB, required=False),
                ExpectedSQL("INSERT", SQLType.INSERT, DatabaseType.GAUSSDB, required=False),
                ExpectedSQL("UPDATE", SQLType.UPDATE, DatabaseType.GAUSSDB, required=False),
                ExpectedSQL("DELETE", SQLType.DELETE, DatabaseType.GAUSSDB, required=False),
            ],
            min_total_sql_count=1,
            min_confidence=0.6
        )
        
        self.validation_rules = {
            "mysql_basic": mysql_rules,
            "postgresql_basic": postgresql_rules,
            "mongodb_basic": mongodb_rules,
            "oracle_basic": oracle_rules,
            "sqlserver_basic": sqlserver_rules,
            "gaussdb_basic": gaussdb_rules
        }
    
    def validate_pcap_file(self, pcap_file: str, rule_name: str = None, 
                          custom_rule: ValidationRule = None) -> ValidationResult:
        """验证单个PCAP文件"""
        start_time = time.time()
        
        try:
            # 检查文件是否存在
            if not os.path.exists(pcap_file):
                return ValidationResult(
                    pcap_file=pcap_file,
                    status=ValidationStatus.FILE_NOT_FOUND,
                    success=False,
                    total_sql_found=0,
                    expected_sql_matches={},
                    missing_sqls=[],
                    analysis_result=None,
                    validation_time=time.time() - start_time,
                    error_message=f"文件不存在: {pcap_file}"
                )
            
            logger.info(f"开始验证PCAP文件: {pcap_file}")
            
            # 尝试从数据库获取抓包文件的端口信息
            database_type_for_analysis = None
            database_port_for_analysis = None

            try:
                # 从抓包文件记录中获取端口信息
                # 注意：这里不能使用await，因为这是同步函数
                # 如果需要异步获取，应该在调用此函数前获取信息
                logger.info(f"跳过数据库端口信息获取（同步函数限制）")
            except Exception as e:
                logger.warning(f"无法从数据库获取抓包文件端口信息: {e}")

            # 分析PCAP文件
            analysis_result = self.analyzer.analyze_pcap_file(
                pcap_file,
                database_type=database_type_for_analysis,
                database_port=database_port_for_analysis
            )
            
            if not analysis_result.success:
                return ValidationResult(
                    pcap_file=pcap_file,
                    status=ValidationStatus.ANALYSIS_ERROR,
                    success=False,
                    total_sql_found=0,
                    expected_sql_matches={},
                    missing_sqls=[],
                    analysis_result=analysis_result,
                    validation_time=time.time() - start_time,
                    error_message=f"分析失败: {analysis_result.error_message}"
                )
            
            # 选择验证规则
            if custom_rule:
                rule = custom_rule
            elif rule_name and rule_name in self.validation_rules:
                rule = self.validation_rules[rule_name]
            else:
                # 自动选择规则
                rule = self._auto_select_rule(analysis_result)
            
            # 执行验证
            validation_result = self._validate_with_rule(analysis_result, rule, start_time)
            
            logger.info(f"验证完成: {pcap_file}, 状态: {validation_result.status.value}")
            return validation_result
            
        except Exception as e:
            logger.error(f"验证PCAP文件失败: {str(e)}")
            return ValidationResult(
                pcap_file=pcap_file,
                status=ValidationStatus.ANALYSIS_ERROR,
                success=False,
                total_sql_found=0,
                expected_sql_matches={},
                missing_sqls=[],
                analysis_result=None,
                validation_time=time.time() - start_time,
                error_message=str(e)
            )
    
    def validate_multiple_pcap_files(self, pcap_files: List[str], 
                                   rule_name: str = None) -> List[ValidationResult]:
        """验证多个PCAP文件"""
        results = []
        
        for pcap_file in pcap_files:
            result = self.validate_pcap_file(pcap_file, rule_name)
            results.append(result)
        
        return results
    
    def _auto_select_rule(self, analysis_result: AnalysisResult) -> ValidationRule:
        """根据分析结果自动选择验证规则"""
        # 统计数据库类型
        db_types = {}
        for stmt in analysis_result.sql_statements:
            db_type = stmt.database_type.value
            db_types[db_type] = db_types.get(db_type, 0) + 1
        
        # 选择最常见的数据库类型对应的规则
        if db_types:
            most_common_db = max(db_types.keys(), key=lambda k: db_types[k])
            rule_name = f"{most_common_db}_basic"
            if rule_name in self.validation_rules:
                return self.validation_rules[rule_name]
        
        # 默认返回MySQL规则
        return self.validation_rules["mysql_basic"]
    
    def _validate_with_rule(self, analysis_result: AnalysisResult, 
                           rule: ValidationRule, start_time: float) -> ValidationResult:
        """使用指定规则进行验证"""
        total_sql_found = len(analysis_result.sql_statements)
        expected_sql_matches = {}
        missing_sqls = []
        recommendations = []
        
        # 检查总SQL数量
        if total_sql_found < rule.min_total_sql_count:
            recommendations.append(f"SQL语句数量不足，期望至少{rule.min_total_sql_count}条，实际找到{total_sql_found}条")
        
        # 检查每个预期SQL
        for expected_sql in rule.expected_sqls:
            matches = self._count_sql_matches(analysis_result.sql_statements, expected_sql)
            expected_sql_matches[expected_sql.sql_pattern] = matches
            
            if expected_sql.required and matches < expected_sql.min_occurrences:
                missing_sqls.append(f"{expected_sql.sql_pattern} ({expected_sql.sql_type.value})")
                recommendations.append(
                    f"缺少必需的{expected_sql.sql_type.value}语句，期望至少{expected_sql.min_occurrences}条"
                )
        
        # 检查置信度
        low_confidence_count = sum(1 for stmt in analysis_result.sql_statements 
                                 if stmt.confidence < rule.min_confidence)
        if low_confidence_count > 0:
            recommendations.append(f"有{low_confidence_count}条SQL语句置信度较低（<{rule.min_confidence}）")
        
        # 确定验证状态
        if total_sql_found == 0:
            status = ValidationStatus.NO_SQL_FOUND
            success = False
        elif missing_sqls:
            if len(missing_sqls) == len([sql for sql in rule.expected_sqls if sql.required]):
                status = ValidationStatus.FAILED
                success = False
            else:
                status = ValidationStatus.PARTIAL
                success = True
        else:
            status = ValidationStatus.SUCCESS
            success = True
        
        # 如果没有找到任何SQL但文件很小，可能是空包
        if total_sql_found == 0 and analysis_result.total_packets < 10:
            recommendations.append("抓包文件包含的数据包很少，可能是空包或抓包时间太短")
        
        return ValidationResult(
            pcap_file=analysis_result.pcap_file,
            status=status,
            success=success,
            total_sql_found=total_sql_found,
            expected_sql_matches=expected_sql_matches,
            missing_sqls=missing_sqls,
            analysis_result=analysis_result,
            validation_time=time.time() - start_time,
            recommendations=recommendations
        )
    
    def _count_sql_matches(self, statements: List[SQLStatement], 
                          expected_sql: ExpectedSQL) -> int:
        """统计匹配预期SQL的语句数量"""
        count = 0
        
        for stmt in statements:
            # 检查数据库类型
            if stmt.database_type != expected_sql.database_type:
                continue
            
            # 检查SQL类型
            if stmt.sql_type != expected_sql.sql_type:
                continue
            
            # 检查SQL模式
            if expected_sql.sql_pattern.upper() in stmt.sql.upper():
                count += 1
        
        return count
    
    def generate_validation_report(self, results: List[ValidationResult], 
                                 output_file: str = None) -> Dict[str, Any]:
        """生成验证报告"""
        report = {
            "summary": {
                "total_files": len(results),
                "successful_validations": sum(1 for r in results if r.success),
                "failed_validations": sum(1 for r in results if not r.success),
                "files_with_sql": sum(1 for r in results if r.total_sql_found > 0),
                "files_without_sql": sum(1 for r in results if r.total_sql_found == 0)
            },
            "validation_results": [],
            "recommendations": []
        }
        
        # 处理每个验证结果
        for result in results:
            result_info = {
                "file": result.pcap_file,
                "status": result.status.value,
                "success": result.success,
                "total_sql_found": result.total_sql_found,
                "expected_sql_matches": result.expected_sql_matches,
                "missing_sqls": result.missing_sqls,
                "validation_time": result.validation_time,
                "error": result.error_message,
                "recommendations": result.recommendations or []
            }
            
            if result.analysis_result:
                result_info["analysis"] = {
                    "total_packets": result.analysis_result.total_packets,
                    "database_packets": result.analysis_result.database_packets,
                    "analysis_time": result.analysis_result.analysis_time
                }
            
            report["validation_results"].append(result_info)
        
        # 生成总体建议
        failed_count = report["summary"]["failed_validations"]
        no_sql_count = report["summary"]["files_without_sql"]
        
        if failed_count > 0:
            report["recommendations"].append(f"有{failed_count}个文件验证失败，建议检查抓包配置和网络接口")
        
        if no_sql_count > 0:
            report["recommendations"].append(f"有{no_sql_count}个文件未找到SQL语句，可能需要调整抓包过滤条件")
        
        # 保存报告
        if output_file:
            try:
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(report, f, indent=2, ensure_ascii=False, default=str)
                logger.info(f"验证报告已保存到: {output_file}")
            except Exception as e:
                logger.error(f"保存验证报告失败: {str(e)}")
        
        return report
    
    def add_custom_rule(self, rule: ValidationRule):
        """添加自定义验证规则"""
        self.validation_rules[rule.name] = rule
        logger.info(f"添加自定义验证规则: {rule.name}")
    
    def get_available_rules(self) -> List[str]:
        """获取可用的验证规则列表"""
        return list(self.validation_rules.keys())

"""
Oracle Text2SQL 系统 - 基于LangChain + DeepSeek的专业SQL生成服务
"""

import logging
from typing import Dict, Any, List, Optional
from langchain.prompts import PromptTemplate
from langchain.schema import BaseOutputParser
from langchain.llms.base import LLM
from langchain.chains import <PERSON><PERSON>hain
from openai import OpenAI
from utils.config import Config

logger = logging.getLogger(__name__)

class DeepSeekLLM(LLM):
    """DeepSeek LLM适配器，用于LangChain"""

    def __init__(self):
        super().__init__()
        self._client = OpenAI(
            api_key=Config.DEEPSEEK_API_KEY,
            base_url=Config.DEEPSEEK_BASE_URL
        )
    
    def _call(self, prompt: str, stop: Optional[List[str]] = None) -> str:
        """调用DeepSeek API"""
        try:
            response = self._client.chat.completions.create(
                model="deepseek-chat",
                messages=[
                    {"role": "system", "content": "你是一个专业的Oracle数据库专家，精通SQL语句生成和优化。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=2000
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            logger.error(f"DeepSeek API调用失败: {e}")
            raise
    
    @property
    def _llm_type(self) -> str:
        return "deepseek"

class OracleSQLOutputParser(BaseOutputParser):
    """Oracle SQL输出解析器"""
    
    def parse(self, text: str) -> Dict[str, Any]:
        """解析AI输出为结构化的SQL信息"""
        try:
            # 清理文本：全局移除分号
            text = text.strip().replace(';', '')
            
            # 移除markdown标记
            text = text.replace('```sql', '').replace('```', '').strip()
            
            # 分析SQL类型和语句 - 改进的多语句解析
            statements = []

            # 首先尝试按行分割
            lines = text.split('\n')
            current_statement = ""

            for line in lines:
                line = line.strip()
                if not line or line.startswith('--'):
                    continue

                # 检查是否是新的SQL语句开始
                line_upper = line.upper()
                if (line_upper.startswith(('SELECT', 'INSERT', 'UPDATE', 'DELETE', 'CREATE', 'DROP', 'ALTER'))
                    and current_statement.strip()):
                    # 保存当前语句
                    stmt = current_statement.strip()
                    if stmt:
                        statements.append(stmt)
                    current_statement = line + " "
                else:
                    current_statement += line + " "

                # 检查语句是否结束（已全局移除分号，无需特殊处理）
                # 保留逻辑以支持多语句分割

            # 处理最后一个语句
            if current_statement.strip():
                stmt = current_statement.strip()
                if stmt:
                    statements.append(stmt)

            # 如果按行分割没有得到多个语句，尝试按SQL关键字分割
            if len(statements) <= 1 and any(keyword in text.upper() for keyword in ['INSERT', 'UPDATE', 'DELETE', 'CREATE']):
                # 使用正则表达式分割SQL语句
                import re
                # 匹配SQL语句开始的模式
                pattern = r'\b(SELECT|INSERT|UPDATE|DELETE|CREATE|DROP|ALTER)\b'
                parts = re.split(pattern, text, flags=re.IGNORECASE)

                statements = []
                for i in range(1, len(parts), 2):  # 跳过空的部分
                    if i + 1 < len(parts):
                        stmt = (parts[i] + parts[i + 1]).strip().rstrip(';')
                        if stmt:
                            statements.append(stmt)
            
            if not statements:
                raise ValueError("未找到有效的SQL语句")
            
            # 分析SQL类型
            sql_types = []
            for stmt in statements:
                stmt_upper = stmt.upper().strip()
                if stmt_upper.startswith('SELECT'):
                    sql_types.append('SELECT')
                elif stmt_upper.startswith('INSERT'):
                    sql_types.append('INSERT')
                elif stmt_upper.startswith('UPDATE'):
                    sql_types.append('UPDATE')
                elif stmt_upper.startswith('DELETE'):
                    sql_types.append('DELETE')
                elif stmt_upper.startswith('CREATE'):
                    sql_types.append('CREATE')
                elif stmt_upper.startswith('DROP'):
                    sql_types.append('DROP')
                elif stmt_upper.startswith('ALTER'):
                    sql_types.append('ALTER')
                else:
                    sql_types.append('UNKNOWN')
            
            return {
                'statements': statements,
                'sql_types': sql_types,
                'is_multi_statement': len(statements) > 1,
                'primary_type': sql_types[0] if sql_types else 'UNKNOWN',
                'raw_sql': '\n'.join(statements)
            }
            
        except Exception as e:
            logger.error(f"SQL解析失败: {e}")
            return {
                'statements': [text],
                'sql_types': ['UNKNOWN'],
                'is_multi_statement': False,
                'primary_type': 'UNKNOWN',
                'raw_sql': text,
                'parse_error': str(e)
            }

class OracleText2SQLService:
    """Oracle Text2SQL服务 - 基于LangChain的专业SQL生成"""
    
    def __init__(self):
        """初始化Text2SQL服务"""
        self.llm = DeepSeekLLM()
        self.sql_parser = OracleSQLOutputParser()
        
        # Oracle专用提示模板
        self.sql_generation_template = PromptTemplate(
            input_variables=["natural_query", "schema_info"],
            template="""
你是一个专业的Oracle数据库专家。请将自然语言查询转换为准确的Oracle SQL语句。

数据库架构信息:
{schema_info}

自然语言查询: {natural_query}

Oracle SQL生成规则:
1. 严格遵循Oracle SQL语法规范
2. 对于CREATE TABLE，使用Oracle数据类型：NUMBER, VARCHAR2, DATE, CLOB等
3. 对于多语句操作，每个语句占一行，不要添加分号
4. 使用Oracle特有函数：SYSDATE, ROWNUM, NVL等
5. 字符串使用单引号，标识符可以使用双引号
6. 对于分页，使用ROWNUM或ROW_NUMBER()
7. 对于序列，使用Oracle SEQUENCE语法
8. 创建表时使用唯一的表名（添加时间戳），避免表名冲突
9. **重要：如果需要创建表，必须先执行DROP TABLE table_name CASCADE CONSTRAINTS删除可能存在的同名表**

**创建表的标准格式：**
DROP TABLE table_name CASCADE CONSTRAINTS
CREATE TABLE table_name (字段定义...)

示例输出格式:
DROP TABLE students_20250731 CASCADE CONSTRAINTS
CREATE TABLE students_20250731 (student_id NUMBER PRIMARY KEY, name VARCHAR2(100), age NUMBER)
INSERT INTO students_20250731 VALUES (1, '张三', 20)
INSERT INTO students_20250731 VALUES (2, '李四', 21)

请直接返回SQL语句，不要包含任何解释文本:
"""
        )
        
        # 创建SQL生成链
        self.sql_chain = LLMChain(
            llm=self.llm,
            prompt=self.sql_generation_template,
            output_parser=self.sql_parser
        )
        
        logger.info("Oracle Text2SQL服务初始化完成")
    
    async def generate_sql(self, natural_query: str, schema_info: str = "") -> Dict[str, Any]:
        """生成Oracle SQL语句"""
        try:
            logger.info(f"Text2SQL生成SQL: {natural_query}")
            
            # 如果没有提供schema信息，使用默认的Oracle系统表信息
            if not schema_info:
                schema_info = """
Oracle系统表和视图:
- USER_TABLES: 用户表信息
- ALL_TABLES: 所有可访问的表
- USER_TAB_COLUMNS: 用户表列信息
- ALL_USERS: 所有用户信息
- DUAL: Oracle虚拟表，用于测试

常用Oracle数据类型:
- NUMBER: 数字类型
- VARCHAR2(n): 可变长字符串
- DATE: 日期类型
- CLOB: 大文本类型
"""
            
            # 调用LangChain生成SQL
            result = await self._run_chain_async(
                natural_query=natural_query,
                schema_info=schema_info
            )
            
            logger.info(f"Text2SQL生成完成: {result.get('primary_type')} 类型，{len(result.get('statements', []))} 个语句")
            
            return {
                'success': True,
                'sql_info': result,
                'sql_query': result.get('raw_sql'),
                'statements': result.get('statements', []),
                'is_multi_statement': result.get('is_multi_statement', False),
                'primary_type': result.get('primary_type', 'UNKNOWN')
            }
            
        except Exception as e:
            logger.error(f"Text2SQL生成失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'sql_query': None,
                'statements': [],
                'is_multi_statement': False,
                'primary_type': 'ERROR'
            }
    
    async def _run_chain_async(self, **kwargs) -> Dict[str, Any]:
        """异步运行LangChain"""
        import asyncio
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.sql_chain.run, kwargs)
    
    async def validate_sql(self, sql_statements: List[str]) -> Dict[str, Any]:
        """验证SQL语句的语法和逻辑"""
        try:
            validation_results = []
            
            for i, stmt in enumerate(sql_statements):
                result = {
                    'statement_index': i,
                    'statement': stmt,
                    'is_valid': True,
                    'warnings': [],
                    'suggestions': []
                }
                
                # 基本语法检查
                stmt_upper = stmt.upper().strip()
                
                # 检查常见问题
                if not stmt_upper:
                    result['is_valid'] = False
                    result['warnings'].append("空语句")
                
                if stmt_upper.startswith('CREATE TABLE') and 'PRIMARY KEY' not in stmt_upper:
                    result['suggestions'].append("建议添加主键约束")
                
                if stmt_upper.startswith('INSERT') and 'VALUES' not in stmt_upper:
                    result['is_valid'] = False
                    result['warnings'].append("INSERT语句缺少VALUES子句")
                
                validation_results.append(result)
            
            overall_valid = all(r['is_valid'] for r in validation_results)
            
            return {
                'success': True,
                'overall_valid': overall_valid,
                'validation_results': validation_results,
                'total_statements': len(sql_statements),
                'valid_statements': sum(1 for r in validation_results if r['is_valid'])
            }
            
        except Exception as e:
            logger.error(f"SQL验证失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'overall_valid': False
            }
    
    async def optimize_sql(self, sql_statements: List[str]) -> Dict[str, Any]:
        """优化SQL语句"""
        try:
            optimized_statements = []
            
            for stmt in sql_statements:
                # 基本优化
                optimized = stmt.strip()
                
                # 移除多余的空格
                optimized = ' '.join(optimized.split())
                
                # Oracle特定优化
                if optimized.upper().startswith('SELECT'):
                    # 添加ROWNUM限制建议
                    if 'ROWNUM' not in optimized.upper() and 'WHERE' in optimized.upper():
                        pass  # 保持原样，不自动添加ROWNUM
                
                optimized_statements.append(optimized)
            
            return {
                'success': True,
                'original_statements': sql_statements,
                'optimized_statements': optimized_statements,
                'optimization_applied': len(optimized_statements)
            }
            
        except Exception as e:
            logger.error(f"SQL优化失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'optimized_statements': sql_statements
            }

# 创建全局Text2SQL服务实例
oracle_text2sql_service = OracleText2SQLService()

"""
抓包文件管理服务
"""

import os
import logging
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from services.mysql_service import MySQLService
from models.capture_file import CaptureFile, CaptureFileCreate, CaptureFileResponse
from utils.config import Config
from utils.execution_logger import ExecutionLogger
from models.execution_log import LogCategory
from utils.path_manager import path_manager

logger = logging.getLogger(__name__)


class CaptureFileService:
    """抓包文件管理服务"""
    
    def __init__(self):
        self.mysql_service = None
        self.capture_dir = path_manager.get_captures_dir()
        
    async def _get_mysql_service(self) -> MySQLService:
        """获取MySQL服务实例"""
        if self.mysql_service is None:
            mysql_config = Config.get_mysql_config()
            self.mysql_service = MySQLService(**mysql_config)
            await self.mysql_service.initialize()
        return self.mysql_service
    
    async def initialize(self):
        """初始化服务，创建数据表"""
        try:
            mysql_service = await self._get_mysql_service()
            
            # 创建抓包文件表
            create_table_sql = """
            CREATE TABLE IF NOT EXISTS capture_files (
                id INT AUTO_INCREMENT PRIMARY KEY,
                filename VARCHAR(255) NOT NULL UNIQUE,
                file_path VARCHAR(500) NOT NULL,
                file_size BIGINT NOT NULL DEFAULT 0,
                database_type VARCHAR(50) NOT NULL,
                target_host VARCHAR(255) NOT NULL,
                target_port INT NOT NULL,
                created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                status VARCHAR(20) DEFAULT 'completed',
                description TEXT,
                INDEX idx_database_type (database_type),
                INDEX idx_created_time (created_time),
                INDEX idx_status (status)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """
            
            await mysql_service.execute_query(create_table_sql)
            logger.info("Capture files table initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize capture file service: {e}")
            raise
    
    async def save_capture_file(self, capture_data: CaptureFileCreate, task_id: Optional[str] = None, execution_id: Optional[str] = None) -> int:
        """保存抓包文件信息到数据库"""

        # 创建执行日志记录器
        exec_logger = ExecutionLogger(
            task_id=task_id,
            execution_id=execution_id,
            database_type=capture_data.database_type
        )

        try:
            await exec_logger.file_operation_info_async(f"开始保存抓包文件到数据库: {capture_data.filename}",
                                                       capture_file=capture_data.filename)

            # 验证物理文件是否存在
            file_path = capture_data.file_path
            if not os.path.isabs(file_path):
                # 相对于backend目录
                backend_root = os.path.dirname(os.path.dirname(__file__))
                file_path = os.path.join(backend_root, file_path)

            if not os.path.exists(file_path):
                error_msg = f"Physical file does not exist: {file_path}"
                await exec_logger.file_operation_error_async(f"抓包文件物理文件不存在: {capture_data.filename}",
                                                            capture_file=capture_data.filename, error_details=error_msg)
                raise Exception(error_msg)

            await exec_logger.file_operation_info_async(f"抓包文件物理文件验证成功: {capture_data.filename}",
                                                       capture_file=capture_data.filename)

            # 验证文件大小
            actual_size = os.path.getsize(file_path)
            if actual_size != capture_data.file_size:
                await exec_logger.file_operation_info_async(f"文件大小不匹配，使用实际大小: 预期={capture_data.file_size}, 实际={actual_size}",
                                                           capture_file=capture_data.filename)
                logger.warning(f"File size mismatch: expected {capture_data.file_size}, actual {actual_size}")
                capture_data.file_size = actual_size  # 使用实际文件大小

            mysql_service = await self._get_mysql_service()

            insert_sql = """
            INSERT INTO capture_files
            (filename, file_path, file_size, database_type, target_host, target_port, description)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
            ON DUPLICATE KEY UPDATE
            file_path = VALUES(file_path),
            file_size = VALUES(file_size),
            database_type = VALUES(database_type),
            target_host = VALUES(target_host),
            target_port = VALUES(target_port),
            description = VALUES(description)
            """

            params = (
                capture_data.filename,
                capture_data.file_path,
                capture_data.file_size,
                capture_data.database_type,
                capture_data.target_host,
                capture_data.target_port,
                capture_data.description
            )

            result = await mysql_service.execute_query(insert_sql, params)

            if result.get('affected_rows', 0) > 0 or result.get('insert_id'):
                # 获取插入的ID
                file_id = result.get('insert_id')
                if not file_id:
                    select_id_sql = "SELECT LAST_INSERT_ID() as id"
                    id_result = await mysql_service.execute_query(select_id_sql)
                    file_id = id_result['data'][0]['id'] if id_result.get('data') else None

                await exec_logger.file_operation_info_async(f"抓包文件成功保存到数据库: {capture_data.filename}, ID: {file_id}",
                                                           capture_file=capture_data.filename)
                logger.info(f"Saved capture file: {capture_data.filename} with ID: {file_id}")
                return file_id
            else:
                error_msg = "Failed to insert capture file record"
                await exec_logger.file_operation_error_async(f"抓包文件数据库插入失败: {capture_data.filename}",
                                                            capture_file=capture_data.filename, error_details=error_msg)
                raise Exception(error_msg)

        except Exception as e:
            await exec_logger.file_operation_error_async(f"保存抓包文件失败: {capture_data.filename}",
                                                        capture_file=capture_data.filename,
                                                        error_details=str(e), include_stack_trace=True)
            logger.error(f"Failed to save capture file: {e}")
            raise
    
    async def get_capture_files(self, 
                               database_type: Optional[str] = None,
                               limit: int = 100,
                               offset: int = 0) -> Dict[str, Any]:
        """获取抓包文件列表"""
        try:
            mysql_service = await self._get_mysql_service()
            
            # 构建查询条件
            where_clause = "WHERE 1=1"
            params = []
            
            if database_type:
                where_clause += " AND database_type = %s"
                params.append(database_type)
            
            # 查询总数
            count_sql = f"SELECT COUNT(*) as total FROM capture_files {where_clause}"
            count_result = await mysql_service.execute_query(count_sql, params)
            total = count_result['data'][0]['total'] if count_result.get('data') else 0
            
            # 查询文件列表
            select_sql = f"""
            SELECT id, filename, file_path, file_size, database_type, 
                   target_host, target_port, created_time, status, description
            FROM capture_files 
            {where_clause}
            ORDER BY created_time DESC
            LIMIT %s OFFSET %s
            """
            
            params.extend([limit, offset])
            result = await mysql_service.execute_query(select_sql, params)
            
            files = []
            if result.get('data'):
                for row in result['data']:
                    # 格式化文件大小
                    formatted_size = self._format_file_size(row['file_size'])
                    
                    # 计算相对时间
                    relative_time = self._get_relative_time(row['created_time'])
                    
                    file_info = CaptureFileResponse(
                        id=row['id'],
                        filename=row['filename'],
                        file_path=row['file_path'],
                        file_size=row['file_size'],
                        database_type=row['database_type'],
                        target_host=row['target_host'],
                        target_port=row['target_port'],
                        created_time=row['created_time'].isoformat() if row['created_time'] else '',
                        status=row['status'],
                        description=row['description'],
                        formatted_size=formatted_size,
                        relative_time=relative_time
                    )
                    files.append(file_info.dict())
            
            return {
                'files': files,
                'total': total,
                'limit': limit,
                'offset': offset
            }
            
        except Exception as e:
            logger.error(f"Failed to get capture files: {e}")
            raise
    
    async def get_capture_file_by_filename(self, filename: str) -> Optional[CaptureFile]:
        """根据文件名获取抓包文件信息"""
        try:
            mysql_service = await self._get_mysql_service()
            
            select_sql = """
            SELECT id, filename, file_path, file_size, database_type, 
                   target_host, target_port, created_time, status, description
            FROM capture_files 
            WHERE filename = %s
            """
            
            result = await mysql_service.execute_query(select_sql, (filename,))
            
            if result.get('data') and len(result['data']) > 0:
                row = result['data'][0]
                return CaptureFile(
                    id=row['id'],
                    filename=row['filename'],
                    file_path=row['file_path'],
                    file_size=row['file_size'],
                    database_type=row['database_type'],
                    target_host=row['target_host'],
                    target_port=row['target_port'],
                    created_time=row['created_time'],
                    status=row['status'],
                    description=row['description']
                )
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to get capture file by filename: {e}")
            raise

    async def get_capture_file_by_path(self, file_path: str) -> Optional[Dict[str, Any]]:
        """根据文件路径获取抓包文件信息"""
        try:
            mysql_service = await self._get_mysql_service()

            # 尝试多种路径匹配方式
            possible_paths = [
                file_path,
                os.path.basename(file_path),  # 只用文件名匹配
                file_path.replace('backend/backend/captures/', 'backend/captures/'),  # 路径标准化
                file_path.replace('backend/captures/', 'backend/backend/captures/'),  # 路径标准化
            ]

            for path in possible_paths:
                select_sql = """
                SELECT id, filename, file_path, file_size, database_type, target_host, target_port,
                       created_time, status, description
                FROM capture_files
                WHERE file_path = %s OR filename = %s
                ORDER BY created_time DESC
                LIMIT 1
                """

                result = await mysql_service.execute_query(select_sql, (path, os.path.basename(path)))

                if result and result.get('data'):
                    row = result['data'][0]
                    return {
                        'id': row[0],
                        'filename': row[1],
                        'file_path': row[2],
                        'file_size': row[3],
                        'database_type': row[4],
                        'target_host': row[5],
                        'target_port': row[6],
                        'created_time': row[7],
                        'status': row[8],
                        'description': row[9]
                    }

            logger.debug(f"No capture file found for path: {file_path}")
            return None

        except Exception as e:
            logger.error(f"Failed to get capture file by path: {e}")
            return None

    async def delete_capture_file(self, filename: str) -> bool:
        """删除抓包文件（数据库记录和物理文件）"""
        try:
            # 先获取文件信息
            file_info = await self.get_capture_file_by_filename(filename)
            if not file_info:
                return False
            
            mysql_service = await self._get_mysql_service()
            
            # 删除数据库记录
            delete_sql = "DELETE FROM capture_files WHERE filename = %s"
            result = await mysql_service.execute_query(delete_sql, (filename,))
            
            if result.get('affected_rows', 0) > 0:
                # 删除物理文件
                try:
                    # 如果是相对路径，转换为绝对路径
                    file_path = file_info.file_path
                    if not os.path.isabs(file_path):
                        # 相对于backend目录（因为抓包服务在backend目录下运行）
                        backend_root = os.path.dirname(os.path.dirname(__file__))
                        file_path = os.path.join(backend_root, file_path)

                    if os.path.exists(file_path):
                        os.remove(file_path)
                        logger.info(f"Deleted physical file: {file_path}")
                    else:
                        logger.warning(f"Physical file not found: {file_path}")
                except Exception as e:
                    logger.warning(f"Failed to delete physical file {file_info.file_path}: {e}")
                
                logger.info(f"Deleted capture file record: {filename}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to delete capture file: {e}")
            raise

    async def verify_file_consistency(self) -> Dict[str, Any]:
        """验证数据库记录与物理文件的一致性"""
        try:
            result = await self.get_capture_files(limit=1000)
            files = result.get('files', [])

            inconsistent_files = []
            missing_files = []
            size_mismatch_files = []

            for file_info in files:
                file_path = file_info['file_path']

                # 如果是相对路径，转换为绝对路径
                if not os.path.isabs(file_path):
                    backend_root = os.path.dirname(os.path.dirname(__file__))
                    file_path = os.path.join(backend_root, file_path)

                if not os.path.exists(file_path):
                    missing_files.append({
                        'filename': file_info['filename'],
                        'expected_path': file_path,
                        'database_size': file_info['file_size']
                    })
                else:
                    actual_size = os.path.getsize(file_path)
                    if actual_size != file_info['file_size']:
                        size_mismatch_files.append({
                            'filename': file_info['filename'],
                            'file_path': file_path,
                            'database_size': file_info['file_size'],
                            'actual_size': actual_size
                        })

            if missing_files or size_mismatch_files:
                inconsistent_files = missing_files + size_mismatch_files

            return {
                'total_files': len(files),
                'consistent_files': len(files) - len(inconsistent_files),
                'missing_files': len(missing_files),
                'size_mismatch_files': len(size_mismatch_files),
                'inconsistent_files': inconsistent_files
            }

        except Exception as e:
            logger.error(f"Failed to verify file consistency: {e}")
            raise
    
    def _format_file_size(self, size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        size = float(size_bytes)
        
        while size >= 1024.0 and i < len(size_names) - 1:
            size /= 1024.0
            i += 1
        
        return f"{size:.1f} {size_names[i]}"
    
    def _get_relative_time(self, created_time: datetime) -> str:
        """获取相对时间"""
        if not created_time:
            return "未知"

        from utils.timezone_utils import get_current_time, to_china_timezone
        now = get_current_time()
        # 确保created_time也是+8时区
        if created_time.tzinfo is None:
            created_time = to_china_timezone(created_time)

        diff = now - created_time

        if diff.days > 0:
            return f"{diff.days}天前"
        elif diff.seconds > 3600:
            hours = diff.seconds // 3600
            return f"{hours}小时前"
        elif diff.seconds > 60:
            minutes = diff.seconds // 60
            return f"{minutes}分钟前"
        else:
            return "刚刚"


# 全局服务实例
capture_file_service = CaptureFileService()

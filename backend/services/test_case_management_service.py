"""
测试用例管理服务
"""
import json
import uuid
import os
import tempfile
import csv
from typing import List, Optional, Dict, Any, <PERSON><PERSON>
from datetime import datetime
import logging
import traceback
import asyncio
import aiomysql
from contextlib import asynccontextmanager
import pandas as pd

from models.test_case_management import (
    TestCaseManagementCreate,
    TestCaseManagementUpdate,
    TestCaseManagementResponse,
    TestCaseManagementQuery,
    TestCaseManagementListResponse,
    TestCaseExecutionRecord,
    TestCaseLastExecution,
    TestCaseStatistics,
    TestStep,
    PacketPattern,
    PacketValidationRule,
    PriorityEnum,
    StatusEnum,
    ReviewStatusEnum,
    ExecutionResultEnum,
    AutomationLevelEnum,
    DatabaseTypeEnum
)
from utils.config import Config

logger = logging.getLogger(__name__)


class TestCaseManagementService:
    """测试用例管理服务"""
    
    def __init__(self):
        self.config = Config()
        self.pool = None
    
    async def init_pool(self):
        """初始化数据库连接池"""
        if self.pool is None:
            try:
                self.pool = await aiomysql.create_pool(
                    host=self.config.MYSQL_HOST,
                    port=self.config.MYSQL_PORT,
                    user=self.config.MYSQL_USER,
                    password=self.config.MYSQL_PASSWORD,
                    db=self.config.MYSQL_DATABASE,
                    charset='utf8mb4',
                    autocommit=True,
                    maxsize=10,
                    minsize=1
                )
                logger.info("Test case management database pool initialized")
            except Exception as e:
                logger.error(f"Failed to initialize database pool: {e}")
                raise
    
    @asynccontextmanager
    async def get_connection(self):
        """获取数据库连接"""
        if self.pool is None:
            await self.init_pool()
        
        async with self.pool.acquire() as conn:
            async with conn.cursor(aiomysql.DictCursor) as cursor:
                yield cursor
    
    async def create_test_case(self, test_case: TestCaseManagementCreate) -> TestCaseManagementResponse:
        """创建测试用例"""
        try:
            test_case_id = str(uuid.uuid4())

            # 转换测试步骤为JSON
            if hasattr(test_case, 'test_steps') and test_case.test_steps:
                if isinstance(test_case.test_steps, list) and len(test_case.test_steps) > 0:
                    if hasattr(test_case.test_steps[0], 'dict'):
                        # Pydantic 对象列表
                        test_steps_json = json.dumps([step.dict() for step in test_case.test_steps], ensure_ascii=False)
                    else:
                        # 字典对象列表
                        test_steps_json = json.dumps(test_case.test_steps, ensure_ascii=False)
                else:
                    test_steps_json = json.dumps([], ensure_ascii=False)
            else:
                test_steps_json = json.dumps([], ensure_ascii=False)

            test_data_json = json.dumps(test_case.test_data, ensure_ascii=False) if test_case.test_data else None
            tags_json = json.dumps(test_case.tags, ensure_ascii=False) if test_case.tags else None
            
            async with self.get_connection() as cursor:
                sql = """
                INSERT INTO test_cases_management (
                    id, title, module, priority, status, preconditions, test_steps,
                    expected_result, test_data, tags, author, reviewer, review_status,
                    review_comments, estimated_time, automation_level, test_environment,
                    related_requirements, related_defects, notes, database_type, database_version
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                )
                """

                await cursor.execute(sql, (
                    test_case_id, test_case.title, test_case.module, test_case.priority.value,
                    test_case.status.value, test_case.preconditions, test_steps_json,
                    test_case.expected_result, test_data_json, tags_json, test_case.author,
                    test_case.reviewer, test_case.review_status.value, test_case.review_comments,
                    test_case.estimated_time, test_case.automation_level.value,
                    test_case.test_environment, test_case.related_requirements,
                    test_case.related_defects, test_case.notes, test_case.database_type, test_case.database_version
                ))
                
                # 获取创建的测试用例
                return await self.get_test_case(test_case_id)
                
        except Exception as e:
            logger.error(f"Failed to create test case: {e}")
            raise
    
    async def get_test_case(self, test_case_id: str) -> Optional[TestCaseManagementResponse]:
        """获取测试用例详情"""
        try:
            async with self.get_connection() as cursor:
                sql = "SELECT * FROM test_cases_management WHERE id = %s"
                await cursor.execute(sql, (test_case_id,))
                result = await cursor.fetchone()
                
                if not result:
                    return None
                
                return self._convert_db_result_to_response(result)
                
        except Exception as e:
            logger.error(f"Failed to get test case {test_case_id}: {e}")
            raise
    
    async def update_test_case(self, test_case_id: str, test_case: TestCaseManagementUpdate) -> Optional[TestCaseManagementResponse]:
        """更新测试用例"""
        try:
            # 构建更新字段
            update_fields = []
            update_values = []

            # 处理不同类型的输入数据
            if hasattr(test_case, 'dict'):
                # Pydantic 模型对象
                test_case_data = test_case.dict(exclude_unset=True)
            elif isinstance(test_case, dict):
                # 已经是字典对象
                test_case_data = {k: v for k, v in test_case.items() if v is not None}
            else:
                # 其他类型，尝试转换为字典
                test_case_data = dict(test_case)

            for field, value in test_case_data.items():
                if field == 'test_steps' and value:
                    update_fields.append("test_steps = %s")
                    # 处理测试步骤数据
                    if isinstance(value, list) and len(value) > 0:
                        if hasattr(value[0], 'dict'):
                            # Pydantic 对象列表
                            update_values.append(json.dumps([step.dict() for step in value], ensure_ascii=False))
                        else:
                            # 字典对象列表
                            update_values.append(json.dumps(value, ensure_ascii=False))
                    else:
                        update_values.append(json.dumps(value, ensure_ascii=False))
                elif field == 'test_data' and value:
                    update_fields.append("test_data = %s")
                    update_values.append(json.dumps(value, ensure_ascii=False))
                elif field == 'tags' and value:
                    update_fields.append("tags = %s")
                    update_values.append(json.dumps(value, ensure_ascii=False))
                elif hasattr(value, 'value'):  # 枚举类型
                    update_fields.append(f"{field} = %s")
                    update_values.append(value.value)
                else:
                    update_fields.append(f"{field} = %s")
                    update_values.append(value)
            
            if not update_fields:
                return await self.get_test_case(test_case_id)
            
            update_fields.append("updated_at = NOW()")
            update_values.append(test_case_id)
            
            async with self.get_connection() as cursor:
                sql = f"UPDATE test_cases_management SET {', '.join(update_fields)} WHERE id = %s"
                await cursor.execute(sql, update_values)
                
                if cursor.rowcount == 0:
                    return None
                
                return await self.get_test_case(test_case_id)
                
        except Exception as e:
            logger.error(f"Failed to update test case {test_case_id}: {e}")
            raise
    
    async def delete_test_case(self, test_case_id: str) -> bool:
        """删除测试用例"""
        try:
            async with self.get_connection() as cursor:
                sql = "DELETE FROM test_cases_management WHERE id = %s"
                await cursor.execute(sql, (test_case_id,))
                return cursor.rowcount > 0

        except Exception as e:
            logger.error(f"Failed to delete test case {test_case_id}: {e}")
            raise

    async def batch_delete_test_cases(self, test_case_ids: List[str]) -> int:
        """批量删除测试用例"""
        try:
            if not test_case_ids:
                return 0

            async with self.get_connection() as cursor:
                placeholders = ','.join(['%s'] * len(test_case_ids))
                sql = f"DELETE FROM test_cases_management WHERE id IN ({placeholders})"
                await cursor.execute(sql, test_case_ids)
                return cursor.rowcount

        except Exception as e:
            logger.error(f"Failed to batch delete test cases: {e}")
            raise
    
    async def list_test_cases(self, query: TestCaseManagementQuery) -> TestCaseManagementListResponse:
        """获取测试用例列表"""
        try:
            # 构建查询条件
            where_conditions = []
            where_values = []
            
            if query.keyword:
                where_conditions.append("(title LIKE %s OR preconditions LIKE %s OR expected_result LIKE %s OR notes LIKE %s)")
                keyword_pattern = f"%{query.keyword}%"
                where_values.extend([keyword_pattern] * 4)
            
            if query.module:
                where_conditions.append("module = %s")
                where_values.append(query.module)
            
            if query.priority:
                where_conditions.append("priority = %s")
                where_values.append(query.priority.value)
            
            if query.status:
                where_conditions.append("status = %s")
                where_values.append(query.status.value)
            
            if query.author:
                where_conditions.append("author = %s")
                where_values.append(query.author)
            
            if query.reviewer:
                where_conditions.append("reviewer = %s")
                where_values.append(query.reviewer)
            
            if query.review_status:
                where_conditions.append("review_status = %s")
                where_values.append(query.review_status.value)
            
            if query.automation_level:
                where_conditions.append("automation_level = %s")
                where_values.append(query.automation_level.value)

            if query.database_type:
                where_conditions.append("database_type = %s")
                where_values.append(query.database_type.value)

            if query.database_version:
                where_conditions.append("database_version LIKE %s")
                where_values.append(f"%{query.database_version}%")

            if query.auto_execute is not None:
                where_conditions.append("auto_execute = %s")
                where_values.append(query.auto_execute)

            if query.created_start:
                where_conditions.append("created_at >= %s")
                where_values.append(query.created_start)

            if query.created_end:
                where_conditions.append("created_at <= %s")
                where_values.append(query.created_end)

            if query.tags:
                # 标签搜索，使用JSON_CONTAINS
                for tag in query.tags:
                    where_conditions.append("JSON_CONTAINS(tags, %s)")
                    where_values.append(json.dumps(tag))
            
            where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"
            
            async with self.get_connection() as cursor:
                # 1) 统计总数（保持不变）
                count_sql = f"SELECT COUNT(*) as total FROM test_cases_management WHERE {where_clause}"
                await cursor.execute(count_sql, where_values)
                total_result = await cursor.fetchone()
                total = total_result['total']

                # 2) 先仅查询ID进行排序分页，减少排序内存占用
                offset = (query.page - 1) * query.page_size
                id_sql = f"""
                SELECT id FROM test_cases_management
                WHERE {where_clause}
                ORDER BY updated_at DESC
                LIMIT %s OFFSET %s
                """
                await cursor.execute(id_sql, where_values + [query.page_size, offset])
                id_rows = await cursor.fetchall()
                ids = [row['id'] for row in id_rows]

                if not ids:
                    return TestCaseManagementListResponse(
                        items=[],
                        total=total,
                        page=query.page,
                        page_size=query.page_size,
                        total_pages=(total + query.page_size - 1) // query.page_size
                    )

                # 3) 回表查询详情（按ID集合），再按ID顺序在内存中还原排序
                placeholders = ','.join(['%s'] * len(ids))
                details_sql = f"SELECT * FROM test_cases_management WHERE id IN ({placeholders})"
                await cursor.execute(details_sql, ids)
                results = await cursor.fetchall()

                # 以ID为键建立索引，按ids顺序还原
                result_map = {row['id']: row for row in results}
                ordered_rows = [result_map[i] for i in ids if i in result_map]

                items = [self._convert_db_result_to_response(row) for row in ordered_rows]
                total_pages = (total + query.page_size - 1) // query.page_size

                return TestCaseManagementListResponse(
                    items=items,
                    total=total,
                    page=query.page,
                    page_size=query.page_size,
                    total_pages=total_pages
                )
                
        except Exception as e:
            logger.error(f"Failed to list test cases: {e}")
            raise
    
    def _convert_db_result_to_response(self, result: Dict[str, Any]) -> TestCaseManagementResponse:
        """将数据库结果转换为响应模型"""
        # 解析JSON字段
        test_steps_data = json.loads(result['test_steps']) if result['test_steps'] else []
        test_steps = [TestStep(**step) for step in test_steps_data]

        test_data = json.loads(result['test_data']) if result['test_data'] else None
        tags = json.loads(result['tags']) if result['tags'] else None

        # 解析新增的JSON字段，提供默认值
        sql_statements = json.loads(result.get('sql_statements')) if result.get('sql_statements') else None
        expected_packet_patterns = None
        if result.get('expected_packet_patterns'):
            try:
                patterns_data = json.loads(result['expected_packet_patterns'])
                expected_packet_patterns = [PacketPattern(**pattern) for pattern in patterns_data]
            except:
                expected_packet_patterns = None

        packet_validation_rules = None
        if result.get('packet_validation_rules'):
            try:
                rules_data = json.loads(result['packet_validation_rules'])
                packet_validation_rules = [PacketValidationRule(**rule) for rule in rules_data]
            except:
                packet_validation_rules = None

        # 解析执行记录相关的JSON字段
        last_execution_capture_files = None
        if result.get('last_execution_capture_files'):
            try:
                last_execution_capture_files = json.loads(result['last_execution_capture_files'])
            except:
                last_execution_capture_files = None

        return TestCaseManagementResponse(
            id=result['id'],
            title=result['title'],
            module=result['module'],
            priority=PriorityEnum(result['priority']),
            status=StatusEnum(result['status']),
            preconditions=result['preconditions'],
            test_steps=test_steps,
            expected_result=result['expected_result'],
            test_data=test_data,
            tags=tags,
            author=result['author'],
            reviewer=result['reviewer'],
            review_status=ReviewStatusEnum(result['review_status']),
            review_comments=result['review_comments'],
            execution_count=result['execution_count'],
            success_count=result['success_count'],
            failure_count=result['failure_count'],
            last_execution_time=result['last_execution_time'],
            last_execution_result=ExecutionResultEnum(result['last_execution_result']) if result['last_execution_result'] else None,
            last_execution_capture_files=last_execution_capture_files,
            estimated_time=result['estimated_time'],
            actual_time=result['actual_time'],
            automation_level=AutomationLevelEnum(result['automation_level']),
            test_environment=result['test_environment'],
            # 新增字段，提供默认值
            database_type=DatabaseTypeEnum(result.get('database_type', 'mysql')),
            target_database_config_id=result.get('target_database_config_id'),
            sql_statements=sql_statements,
            expected_packet_patterns=expected_packet_patterns,
            packet_validation_rules=packet_validation_rules,
            capture_duration=result.get('capture_duration', 30),
            auto_execute=result.get('auto_execute', False),
            related_requirements=result['related_requirements'],
            related_defects=result['related_defects'],
            notes=result['notes'],
            database_version=result.get('database_version'),
            created_at=result['created_at'],
            updated_at=result['updated_at']
        )


    async def record_execution(self, execution_record: TestCaseExecutionRecord) -> bool:
        """记录测试用例执行结果"""
        try:
            async with self.get_connection() as cursor:
                # 更新测试用例的执行统计
                if execution_record.execution_result == ExecutionResultEnum.PASS:
                    sql = """
                    UPDATE test_cases_management
                    SET execution_count = execution_count + 1,
                        success_count = success_count + 1,
                        last_execution_time = NOW(),
                        last_execution_result = %s,
                        actual_time = %s
                    WHERE id = %s
                    """
                else:
                    sql = """
                    UPDATE test_cases_management
                    SET execution_count = execution_count + 1,
                        failure_count = failure_count + 1,
                        last_execution_time = NOW(),
                        last_execution_result = %s,
                        actual_time = %s
                    WHERE id = %s
                    """

                await cursor.execute(sql, (
                    execution_record.execution_result.value,
                    execution_record.execution_time,
                    execution_record.test_case_id
                ))

                return cursor.rowcount > 0

        except Exception as e:
            logger.error(f"Failed to record execution: {e}")
            raise

    async def update_last_execution(
        self,
        test_case_id: str,
        execution_id: str,
        execution_result: ExecutionResultEnum,
        config_id: Optional[int] = None,
        capture_files: Optional[List[str]] = None,
        duration: int = 0,
        execution_details: Optional[Dict[str, Any]] = None
    ) -> bool:
        """更新测试用例的最近执行记录"""
        try:
            async with self.get_connection() as cursor:
                # 更新测试用例的最近执行信息
                sql = """
                UPDATE test_cases_management
                SET last_execution_time = NOW(),
                    last_execution_result = %s,
                    last_execution_id = %s,
                    last_execution_config_id = %s,
                    last_execution_capture_files = %s,
                    last_execution_duration = %s,
                    last_execution_details = %s,
                    execution_count = execution_count + 1,
                    success_count = CASE WHEN %s = 'pass' THEN success_count + 1 ELSE success_count END,
                    failure_count = CASE WHEN %s != 'pass' THEN failure_count + 1 ELSE failure_count END
                WHERE id = %s
                """

                capture_files_json = json.dumps(capture_files) if capture_files else None
                execution_details_json = json.dumps(execution_details, default=str) if execution_details else None

                await cursor.execute(sql, (
                    execution_result.value,
                    execution_id,
                    config_id,
                    capture_files_json,
                    duration,
                    execution_details_json,
                    execution_result.value,
                    execution_result.value,
                    test_case_id
                ))

                return cursor.rowcount > 0

        except Exception as e:
            logger.error(f"Failed to update last execution: {e}")
            raise

    async def get_last_execution(self, test_case_id: str) -> Optional[TestCaseLastExecution]:
        """获取测试用例的最近执行记录"""
        try:
            async with self.get_connection() as cursor:
                sql = """
                SELECT
                    tcm.last_execution_id,
                    tcm.last_execution_time,
                    tcm.last_execution_result,
                    tcm.last_execution_config_id,
                    tcm.last_execution_capture_files,
                    tcm.last_execution_duration,
                    tcm.last_execution_details,
                    tcm.success_count,
                    tcm.execution_count,
                    dc.name as database_config_name
                FROM test_cases_management tcm
                LEFT JOIN database_configs dc ON tcm.last_execution_config_id = dc.id
                WHERE tcm.id = %s AND tcm.last_execution_id IS NOT NULL
                """

                await cursor.execute(sql, (test_case_id,))
                result = await cursor.fetchone()

                if not result:
                    return None

                # 解析抓包文件列表
                capture_files = []
                if result['last_execution_capture_files']:
                    try:
                        capture_files = json.loads(result['last_execution_capture_files'])
                    except:
                        capture_files = []

                # 解析执行详情
                execution_details = None
                if result['last_execution_details']:
                    try:
                        execution_details = json.loads(result['last_execution_details'])
                    except:
                        execution_details = None

                # 计算成功率
                success_rate = 0.0
                if result['execution_count'] > 0:
                    success_rate = (result['success_count'] / result['execution_count']) * 100

                return TestCaseLastExecution(
                    execution_id=result['last_execution_id'],
                    execution_time=result['last_execution_time'],
                    execution_result=ExecutionResultEnum(result['last_execution_result']),
                    database_config_id=result['last_execution_config_id'],
                    database_config_name=result['database_config_name'],
                    capture_files=capture_files,
                    duration=result['last_execution_duration'],
                    success_rate=success_rate,
                    execution_details=execution_details
                )

        except Exception as e:
            logger.error(f"Failed to get last execution: {e}")
            return None

    async def get_statistics(self) -> TestCaseStatistics:
        """获取测试用例统计信息"""
        try:
            async with self.get_connection() as cursor:
                # 总数量
                await cursor.execute("SELECT COUNT(*) as total FROM test_cases_management")
                total_result = await cursor.fetchone()
                total_count = total_result['total']

                # 状态分布
                await cursor.execute("SELECT status, COUNT(*) as count FROM test_cases_management GROUP BY status")
                status_results = await cursor.fetchall()
                status_distribution = {result['status']: result['count'] for result in status_results}

                # 优先级分布
                await cursor.execute("SELECT priority, COUNT(*) as count FROM test_cases_management GROUP BY priority")
                priority_results = await cursor.fetchall()
                priority_distribution = {result['priority']: result['count'] for result in priority_results}

                # 自动化程度分布
                await cursor.execute("SELECT automation_level, COUNT(*) as count FROM test_cases_management GROUP BY automation_level")
                automation_results = await cursor.fetchall()
                automation_distribution = {result['automation_level']: result['count'] for result in automation_results}

                # 模块分布
                await cursor.execute("SELECT module, COUNT(*) as count FROM test_cases_management GROUP BY module ORDER BY count DESC LIMIT 10")
                module_results = await cursor.fetchall()
                module_distribution = {result['module']: result['count'] for result in module_results}

                # 执行率和通过率
                await cursor.execute("SELECT COUNT(*) as executed FROM test_cases_management WHERE execution_count > 0")
                executed_result = await cursor.fetchone()
                executed_count = executed_result['executed']
                execution_rate = (executed_count / total_count * 100) if total_count > 0 else 0

                await cursor.execute("SELECT SUM(success_count) as total_success, SUM(execution_count) as total_executions FROM test_cases_management")
                pass_rate_result = await cursor.fetchone()
                total_success = pass_rate_result['total_success'] or 0
                total_executions = pass_rate_result['total_executions'] or 0
                pass_rate = (total_success / total_executions * 100) if total_executions > 0 else 0

                return TestCaseStatistics(
                    total_count=total_count,
                    status_distribution=status_distribution,
                    priority_distribution=priority_distribution,
                    automation_distribution=automation_distribution,
                    module_distribution=module_distribution,
                    execution_rate=round(execution_rate, 2),
                    pass_rate=round(pass_rate, 2)
                )

        except Exception as e:
            logger.error(f"Failed to get statistics: {e}")
            raise

    async def get_modules(self) -> List[str]:
        """获取所有模块列表"""
        try:
            async with self.get_connection() as cursor:
                sql = "SELECT DISTINCT module FROM test_cases_management ORDER BY module"
                await cursor.execute(sql)
                results = await cursor.fetchall()
                return [result['module'] for result in results]

        except Exception as e:
            logger.error(f"Failed to get modules: {e}")
            raise

    async def get_authors(self) -> List[str]:
        """获取所有创建者列表"""
        try:
            async with self.get_connection() as cursor:
                sql = "SELECT DISTINCT author FROM test_cases_management WHERE author IS NOT NULL ORDER BY author"
                await cursor.execute(sql)
                results = await cursor.fetchall()
                return [result['author'] for result in results]

        except Exception as e:
            logger.error(f"Failed to get authors: {e}")
            raise

    async def get_reviewers(self) -> List[str]:
        """获取所有评审者列表"""
        try:
            async with self.get_connection() as cursor:
                sql = "SELECT DISTINCT reviewer FROM test_cases_management WHERE reviewer IS NOT NULL ORDER BY reviewer"
                await cursor.execute(sql)
                results = await cursor.fetchall()
                return [result['reviewer'] for result in results]

        except Exception as e:
            logger.error(f"Failed to get reviewers: {e}")
            raise

    async def update_test_case_status(self, test_case_id: str, status: StatusEnum) -> bool:
        """更新单个测试用例状态"""
        try:
            async with self.get_connection() as cursor:
                sql = "UPDATE test_cases_management SET status = %s, updated_at = NOW() WHERE id = %s"
                await cursor.execute(sql, (status.value, test_case_id))
                return cursor.rowcount > 0

        except Exception as e:
            logger.error(f"Failed to update test case status: {e}")
            raise



    async def duplicate_test_case(self, test_case_id: str, new_title: str) -> Optional[TestCaseManagementResponse]:
        """复制测试用例"""
        try:
            # 获取原测试用例
            original = await self.get_test_case(test_case_id)
            if not original:
                return None

            # 创建新的测试用例
            new_test_case = TestCaseManagementCreate(
                title=new_title,
                module=original.module,
                priority=original.priority,
                status=StatusEnum.DRAFT,  # 复制的用例默认为草稿状态
                preconditions=original.preconditions,
                test_steps=original.test_steps,
                expected_result=original.expected_result,
                test_data=original.test_data,
                tags=original.tags,
                author=original.author,
                estimated_time=original.estimated_time,
                automation_level=original.automation_level,
                test_environment=original.test_environment,
                related_requirements=original.related_requirements,
                notes=f"复制自: {original.title}"
            )

            return await self.create_test_case(new_test_case)

        except Exception as e:
            logger.error(f"Failed to duplicate test case {test_case_id}: {e}")
            raise

    async def export_test_cases(self, query: TestCaseManagementQuery, format: str = "excel") -> str:
        """导出测试用例"""
        try:
            # 直接查询所有符合条件的测试用例，不使用分页
            test_cases = await self._get_all_test_cases_for_export(query)

            # 如果没有数据，创建空的导出文件
            if not test_cases:
                logger.info("没有找到符合条件的测试用例，将导出空文件")
                export_data = []
            else:
                # 准备导出数据
                export_data = []
                for test_case in test_cases:
                    # 处理测试步骤（包含每步的SQL与测试数据缩进展示）
                    test_steps_str = ""
                    any_step_has_data = False
                    if test_case.test_steps:
                        step_lines = []
                        for i, step in enumerate(test_case.test_steps, 1):
                            main = f"{i}. {step.action}"
                            if getattr(step, 'expected_result', None):
                                main += f" (预期: {step.expected_result})"
                            step_lines.append(main)

                            # 追加每步SQL
                            sql_stmt = getattr(step, 'sql_statement', None)
                            if sql_stmt:
                                any_step_has_data = True
                                step_lines.append(f"    SQL语句: {sql_stmt}")

                            # 追加每步测试数据
                            step_data = getattr(step, 'test_data', None)
                            if step_data:
                                any_step_has_data = True
                                # 多行友好显示
                                for line in str(step_data).splitlines() or [str(step_data)]:
                                    step_lines.append(f"    测试数据: {line}")

                        test_steps_str = "\n".join(step_lines)

                    # 处理标签
                    tags_str = ", ".join(test_case.tags) if test_case.tags else ""

                    # 处理测试数据：优先用用例顶层测试数据；否则汇总步骤中的SQL/测试数据
                    test_data_str = ""
                    if test_case.test_data:
                        test_data_str = json.dumps(test_case.test_data, ensure_ascii=False, indent=2)
                    else:
                        # 汇总来自步骤的数据/SQL
                        collect_lines = []
                        if test_case.test_steps:
                            for i, step in enumerate(test_case.test_steps, 1):
                                sql_stmt = getattr(step, 'sql_statement', None)
                                step_data = getattr(step, 'test_data', None)
                                if sql_stmt or step_data:
                                    collect_lines.append(f"步骤{i}:")
                                if sql_stmt:
                                    collect_lines.append(f"  SQL: {sql_stmt}")
                                if step_data:
                                    for line in str(step_data).splitlines() or [str(step_data)]:
                                        collect_lines.append(f"  数据: {line}")

                        # 追加用例级 SQL 列表（如有）
                        if getattr(test_case, 'sql_statements', None):
                            if not collect_lines:
                                collect_lines.append("用例SQL:")
                            for sql in test_case.sql_statements:
                                collect_lines.append(f"  {sql}")

                        if collect_lines:
                            test_data_str = "\n".join(collect_lines)
 
                    export_data.append({
                        "ID": test_case.id,
                        "标题": test_case.title,
                        "模块": test_case.module,
                        "优先级": self._get_priority_display(test_case.priority),
                        "状态": self._get_status_display(test_case.status),
                        "前置条件": test_case.preconditions or "",
                        "测试步骤": test_steps_str,
                        "预期结果": test_case.expected_result or "",
                        "测试数据": test_data_str,
                        "标签": tags_str,
                        "创建者": test_case.author or "",
                        "评审者": test_case.reviewer or "",
                        "评审状态": self._get_review_status_display(test_case.review_status),
                        "评审意见": test_case.review_comments or "",
                        "预估时间": test_case.estimated_time or "",
                        "自动化程度": self._get_automation_level_display(test_case.automation_level),
                        "测试环境": test_case.test_environment or "",
                        "相关需求": test_case.related_requirements or "",
                        "相关缺陷": test_case.related_defects or "",
                        "备注": test_case.notes or "",
                        "数据库类型": self._get_database_type_display(test_case.database_type),
                        "数据库版本": test_case.database_version or "",
                        "创建时间": test_case.created_at.strftime("%Y-%m-%d %H:%M:%S") if test_case.created_at else "",
                        "更新时间": test_case.updated_at.strftime("%Y-%m-%d %H:%M:%S") if test_case.updated_at else ""
                    })


            # 根据格式导出
            if format == "json":
                return await self._export_to_json(export_data)
            elif format == "csv":
                return await self._export_to_csv(export_data)
            elif format == "excel":
                return await self._export_to_excel(export_data)
            else:
                raise ValueError(f"不支持的导出格式: {format}")

        except Exception as e:
            logger.error(f"Failed to export test cases: {e}")
            raise

    async def _export_to_json(self, data: List[Dict[str, Any]]) -> str:
        """导出为JSON格式"""
        temp_dir = tempfile.gettempdir()
        file_path = os.path.join(temp_dir, f"test_cases_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")

        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)

        return file_path

    async def _export_to_csv(self, data: List[Dict[str, Any]]) -> str:
        """导出为CSV格式"""
        temp_dir = tempfile.gettempdir()
        file_path = os.path.join(temp_dir, f"test_cases_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv")

        with open(file_path, 'w', newline='', encoding='utf-8-sig') as f:
            if data:
                writer = csv.DictWriter(f, fieldnames=data[0].keys())
                writer.writeheader()
                writer.writerows(data)
            else:
                # 写入空的CSV文件，包含标题行
                fieldnames = [
                    "ID", "标题", "模块", "优先级", "状态", "前置条件", "测试步骤", "预期结果",
                    "测试数据", "标签", "创建者", "评审者", "评审状态", "评审意见", "预估时间",
                    "自动化程度", "测试环境", "相关需求", "相关缺陷", "备注", "数据库类型",
                    "数据库版本", "创建时间", "更新时间"
                ]
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()

        return file_path

    async def _export_to_excel(self, data: List[Dict[str, Any]]) -> str:
        """导出为Excel格式"""
        temp_dir = tempfile.gettempdir()
        file_path = os.path.join(temp_dir, f"test_cases_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx")

        if data:
            df = pd.DataFrame(data)
        else:
            # 创建空的DataFrame，包含列标题
            columns = [
                "ID", "标题", "模块", "优先级", "状态", "前置条件", "测试步骤", "预期结果",
                "测试数据", "标签", "创建者", "评审者", "评审状态", "评审意见", "预估时间",
                "自动化程度", "测试环境", "相关需求", "相关缺陷", "备注", "数据库类型",
                "数据库版本", "创建时间", "更新时间"
            ]
            df = pd.DataFrame(columns=columns)

        with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='测试用例', index=False)

            # 调整列宽
            worksheet = writer.sheets['测试用例']
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except Exception as e:

                        logger.error(f"操作失败: {type(e).__name__}: {str(e)}")

                        logger.error(f"异常堆栈跟踪: {traceback.format_exc()}")
                adjusted_width = min(max_length + 2, 50)  # 最大宽度50
                worksheet.column_dimensions[column_letter].width = adjusted_width

        return file_path

    def _get_priority_display(self, priority: PriorityEnum) -> str:
        """获取优先级显示名称"""
        priority_map = {
            PriorityEnum.LOW: "低",
            PriorityEnum.MEDIUM: "中",
            PriorityEnum.HIGH: "高",
            PriorityEnum.CRITICAL: "紧急"
        }
        return priority_map.get(priority, str(priority) if priority else "")

    def _get_status_display(self, status: StatusEnum) -> str:
        """获取状态显示名称"""
        status_map = {
            StatusEnum.DRAFT: "草稿",
            StatusEnum.ACTIVE: "激活",
            StatusEnum.COMPLETED: "已完成",
            StatusEnum.FAILED: "失败",
            StatusEnum.DEPRECATED: "已废弃",
            StatusEnum.ARCHIVED: "已归档"
        }
        return status_map.get(status, str(status) if status else "")

    def _get_review_status_display(self, review_status: ReviewStatusEnum) -> str:
        """获取评审状态显示名称"""
        review_status_map = {
            ReviewStatusEnum.PENDING: "待评审",
            ReviewStatusEnum.APPROVED: "已通过",
            ReviewStatusEnum.REJECTED: "已拒绝"
        }
        return review_status_map.get(review_status, str(review_status) if review_status else "")

    def _get_automation_level_display(self, automation_level: AutomationLevelEnum) -> str:
        """获取自动化程度显示名称"""
        automation_level_map = {
            AutomationLevelEnum.MANUAL: "手动",
            AutomationLevelEnum.SEMI_AUTO: "半自动",
            AutomationLevelEnum.AUTO: "全自动"
        }
        return automation_level_map.get(automation_level, str(automation_level) if automation_level else "")

    def _get_database_type_display(self, database_type: DatabaseTypeEnum) -> str:
        """获取数据库类型显示名称"""
        database_type_map = {
            DatabaseTypeEnum.MYSQL: "MySQL",
            DatabaseTypeEnum.POSTGRESQL: "PostgreSQL",
            DatabaseTypeEnum.MONGODB: "MongoDB",
            DatabaseTypeEnum.ORACLE: "Oracle",
            DatabaseTypeEnum.GAUSSDB: "GaussDB"
        }
        return database_type_map.get(database_type, str(database_type) if database_type else "")

    async def _get_all_test_cases_for_export(self, query: TestCaseManagementQuery) -> List[TestCaseManagementResponse]:
        """获取所有符合条件的测试用例用于导出（不分页）"""
        try:
            # 构建查询条件
            where_conditions = []
            where_values = []

            if query.keyword:
                where_conditions.append("(title LIKE %s OR preconditions LIKE %s OR expected_result LIKE %s OR notes LIKE %s)")
                keyword_pattern = f"%{query.keyword}%"
                where_values.extend([keyword_pattern] * 4)

            if query.module:
                where_conditions.append("module = %s")
                where_values.append(query.module)

            if query.priority:
                where_conditions.append("priority = %s")
                where_values.append(query.priority.value)

            if query.status:
                where_conditions.append("status = %s")
                where_values.append(query.status.value)

            if query.author:
                where_conditions.append("author = %s")
                where_values.append(query.author)

            if query.reviewer:
                where_conditions.append("reviewer = %s")
                whereValues.append(query.reviewer)

            if query.review_status:
                where_conditions.append("review_status = %s")
                where_values.append(query.review_status.value)

            if query.automation_level:
                where_conditions.append("automation_level = %s")
                where_values.append(query.automation_level.value)

            if query.database_type:
                where_conditions.append("database_type = %s")
                where_values.append(query.database_type.value)

            if query.database_version:
                where_conditions.append("database_version = %s")
                where_values.append(query.database_version)

            if query.auto_execute is not None:
                where_conditions.append("auto_execute = %s")
                where_values.append(query.auto_execute)

            if query.tags:
                # 标签搜索 - 检查JSON数组中是否包含指定标签
                tag_conditions = []
                for tag in query.tags:
                    tag_conditions.append("JSON_CONTAINS(tags, %s)")
                    where_values.append(json.dumps(tag, ensure_ascii=False))
                if tag_conditions:
                    where_conditions.append(f"({' OR '.join(tag_conditions)})")

            # 构建WHERE子句
            where_clause = ""
            if where_conditions:
                where_clause = "WHERE " + " AND ".join(where_conditions)

            async with self.get_connection() as cursor:
                # 查询所有符合条件的记录
                sql = f"""
                SELECT id, title, module, priority, status, preconditions, test_steps,
                       expected_result, test_data, tags, author, reviewer, review_status,
                       review_comments, execution_count, success_count, failure_count,
                       last_execution_time, last_execution_result, last_execution_capture_files,
                       estimated_time, actual_time, automation_level, test_environment,
                       database_type, target_database_config_id, sql_statements,
                       expected_packet_patterns, packet_validation_rules, capture_duration,
                       auto_execute, related_requirements, related_defects, notes,
                       database_version, created_at, updated_at
                FROM test_cases_management
                {where_clause}
                ORDER BY created_at DESC
                """

                await cursor.execute(sql, where_values)
                results = await cursor.fetchall()

                # 转换为响应模型
                items = []
                for result in results:
                    items.append(self._convert_db_result_to_response(result))

                return items

        except Exception as e:
            logger.error(f"Failed to get all test cases for export: {e}")
            raise

    async def get_database_types_and_versions(self) -> Dict[str, Any]:
        """获取现有测试用例的数据库类型和版本信息"""
        try:
            async with self.get_connection() as cursor:
                sql = """
                SELECT 
                    database_type,
                    database_version,
                    COUNT(*) as case_count
                FROM test_cases_management 
                WHERE database_type IS NOT NULL 
                    AND database_type != '' 
                    AND database_version IS NOT NULL 
                    AND database_version != ''
                GROUP BY database_type, database_version
                ORDER BY database_type, database_version
                """
                await cursor.execute(sql)
                _ = await cursor.fetchall()  # 统计结果当前未使用，可扩展

            # 再次获取只为列出所有版本（可与上面合并，这里保持最小改动）
            async with self.get_connection() as cursor:
                sql_versions = """
                SELECT 
                    database_type,
                    database_version
                FROM test_cases_management 
                WHERE database_type IS NOT NULL 
                    AND database_type != '' 
                    AND database_version IS NOT NULL 
                    AND database_version != ''
                ORDER BY database_type, database_version
                """
                await cursor.execute(sql_versions)
                results = await cursor.fetchall()

            db_types_data: Dict[str, set] = {}
            for row in results:
                db_type = row['database_type']
                db_version = row['database_version']
                db_types_data.setdefault(db_type, set()).add(db_version)

            data_converted = {k: sorted(list(v)) for k, v in db_types_data.items()}
            return {
                'data': data_converted,
                'total_types': len(data_converted),
                'total_cases': len(results)
            }
        except Exception as e:
            logger.error(f"Failed to get database types and versions: {e}")
            raise

# === 服务实例化 ===
try:
    test_case_management_service = TestCaseManagementService()
except Exception as _init_e:
    logger.error(f"Failed to instantiate TestCaseManagementService: {_init_e}")
    test_case_management_service = None

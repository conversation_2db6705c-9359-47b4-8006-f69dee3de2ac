"""
PostgreSQL MCP代理服务 - 使用langchain集成MCP工具，实现自然语言到SQL的转换和执行
"""
import asyncio
import logging
from typing import Dict, Any, Optional
from langchain.agents import AgentExecutor, create_openai_tools_agent
from langchain.prompts import ChatPromptTemplate
from langchain_openai import ChatOpenAI

from tools.postgres_mcp_tools import (
    PostgresMCPQueryTool, PostgresMCPSchemaTool, PostgresMCPPacketCaptureTool
)
from services.postgres_service import PostgresService
from services.postgres_local_packet_capture_service import PostgresLocalPacketCaptureService
from services.database_config_service import database_config_service
from utils.config import Config

logger = logging.getLogger(__name__)

class PostgresMCPAgentService:
    """PostgreSQL MCP代理服务 - 使用langchain集成MCP工具，实现自然语言到SQL的转换和执行"""
    
    def __init__(self):
        """初始化PostgreSQL MCP代理服务"""
        self.llm = self._initialize_llm()
        self.postgres_service = None
        self.tools = self._initialize_tools()
        self.agent_executor = self._create_agent()
        logger.info("PostgreSQL MCP Agent Service initialized")
    
    def _initialize_llm(self):
        """初始化语言模型"""
        try:
            # 使用DeepSeek API
            return ChatOpenAI(
                model="deepseek-chat",
                openai_api_key=Config.DEEPSEEK_API_KEY,
                openai_api_base=Config.DEEPSEEK_BASE_URL,
                temperature=0.1,
                max_tokens=4096
            )
        except Exception as e:
            logger.error(f"Failed to initialize LLM: {str(e)}")
            raise
    
    def _initialize_tools(self):
        """初始化工具列表"""
        return [
            PostgresMCPQueryTool(self.postgres_service),
            PostgresMCPSchemaTool(self.postgres_service),
            PostgresMCPPacketCaptureTool()
        ]
    
    def _create_agent(self):
        """创建langchain代理"""
        try:
            # 创建提示模板
            prompt = ChatPromptTemplate.from_messages([
                ("system", """你是一个专业的PostgreSQL数据库专家和助手。你可以：

1. 将自然语言转换为准确的PostgreSQL SQL查询语句
2. 执行SQL查询并返回结果
3. 获取数据库架构信息（数据库列表、表列表、表结构）
4. 控制数据包捕获功能

可用工具：
- postgres_mcp_query: 执行PostgreSQL SQL查询，可选择是否同时进行数据包捕获
- postgres_mcp_schema: 获取PostgreSQL数据库架构信息
- postgres_mcp_packet_capture: 控制数据包捕获（start/stop/status）

当用户要求执行查询并抓包时，请使用postgres_mcp_query工具并设置capture_packets=True。
当用户只是询问数据库结构时，使用postgres_mcp_schema工具。
当用户明确要求控制抓包时，使用postgres_mcp_packet_capture工具。

请根据用户的需求选择合适的工具，并提供清晰的结果说明。"""),
                ("user", "{input}"),
                ("assistant", "{agent_scratchpad}")
            ])
            
            # 创建代理
            agent = create_openai_tools_agent(self.llm, self.tools, prompt)
            
            return AgentExecutor(
                agent=agent,
                tools=self.tools,
                verbose=True,
                max_iterations=5,
                handle_parsing_errors=True
            )
            
        except Exception as e:
            logger.error(f"Failed to create agent: {str(e)}")
            raise
    
    async def configure_postgres_connection(self, config_id: int):
        """配置PostgreSQL连接"""
        try:
            config = await database_config_service.get_config(config_id)
            if not config:
                raise Exception(f"Database config not found: {config_id}")
            
            # 创建PostgreSQL服务实例
            self.postgres_service = PostgresService(
                host=config.host,
                port=config.port,
                user=config.user,
                password=config.password,
                database=config.database_name
            )
            
            # 初始化连接
            await self.postgres_service.initialize()
            
            # 测试连接
            is_connected = await self.postgres_service.check_connection()
            if not is_connected:
                raise Exception("PostgreSQL database connection failed")
            
            logger.info(f"PostgreSQL connection configured: {config.host}:{config.port}")

            # 重新创建工具和代理，使用新的服务实例
            self.tools = self._initialize_tools()
            self.agent_executor = self._create_agent()

        except Exception as e:
            logger.error(f"Failed to configure PostgreSQL connection: {str(e)}")
            raise
    
    async def process_natural_language_query(
        self, 
        natural_query: str, 
        capture_packets: bool = False,
        database_context: Optional[str] = None
    ) -> Dict[str, Any]:
        """处理自然语言查询"""
        try:
            logger.info(f"Processing natural language query: {natural_query}")
            
            # 构建输入
            input_text = natural_query
            if capture_packets:
                input_text += " (请同时进行数据包捕获)"
            
            if database_context:
                input_text = f"在数据库 {database_context} 中，{input_text}"
            
            # 执行代理
            result = await asyncio.get_event_loop().run_in_executor(
                None, 
                lambda: self.agent_executor.invoke({"input": input_text})
            )
            
            return {
                "success": True,
                "query": natural_query,
                "result": result.get("output", ""),
                "intermediate_steps": result.get("intermediate_steps", [])
            }
            
        except Exception as e:
            logger.error(f"Failed to process natural language query: {str(e)}")
            return {
                "success": False,
                "query": natural_query,
                "error": str(e)
            }
    
    async def execute_sql_with_capture(
        self, 
        sql_query: str, 
        capture_packets: bool = True
    ) -> Dict[str, Any]:
        """执行PostgreSQL SQL查询并可选择进行数据包捕获"""
        try:
            logger.info(f"Executing PostgreSQL SQL with capture: {sql_query}")
            
            # 直接使用MCP查询工具
            query_tool = PostgresMCPQueryTool(self.postgres_service)
            result = await query_tool._arun(sql_query, capture_packets)
            
            return {
                "success": True,
                "sql_query": sql_query,
                "result": result,
                "capture_enabled": capture_packets
            }
            
        except Exception as e:
            logger.error(f"Failed to execute PostgreSQL SQL with capture: {str(e)}")
            return {
                "success": False,
                "sql_query": sql_query,
                "error": str(e)
            }

# 创建全局服务实例
postgres_mcp_agent_service = PostgresMCPAgentService()

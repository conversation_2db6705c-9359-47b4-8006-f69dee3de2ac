"""
MCP MySQL服务 - 动态配置MySQL连接并执行操作
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional
from services.database_config_service import database_config_service

logger = logging.getLogger(__name__)

class MCPMySQLService:
    """MCP MySQL服务 - 根据配置动态连接和执行MySQL操作"""
    
    def __init__(self):
        self.current_config = None
        self.mysql_service = None
    
    async def configure_mysql_connection(self, config_id: int) -> Dict[str, Any]:
        """根据配置ID动态配置MySQL连接"""
        try:
            # 获取数据库配置
            config = await database_config_service.get_config(config_id)
            if not config:
                raise Exception(f"Database config not found: {config_id}")
            
            # 动态导入MySQL服务
            from services.mysql_service import MySQLService
            
            # 创建MySQL服务实例
            self.mysql_service = MySQLService(
                host=config.host,
                port=config.port,
                user=config.user,
                password=config.password,
                database=config.database_name
            )
            
            # 初始化连接
            await self.mysql_service.initialize()
            
            self.current_config = config
            
            logger.info(f"MCP MySQL configured for {config.host}:{config.port}/{config.database_name}")
            
            return {
                "success": True,
                "config": {
                    "host": config.host,
                    "port": config.port,
                    "database": config.database_name,
                    "user": config.user
                }
            }
            
        except Exception as e:
            logger.error(f"Failed to configure MySQL connection: {str(e)}")
            raise Exception(f"MySQL configuration failed: {str(e)}")
    
    async def execute_sql_operations(self, sql_statements: str) -> Dict[str, Any]:
        """执行SQL操作"""
        try:
            if not self.mysql_service:
                raise Exception("MySQL service not configured. Call configure_mysql_connection first.")
            
            # 分割多个SQL语句
            statements = self._split_sql_statements(sql_statements)
            
            results = []
            for i, statement in enumerate(statements):
                if not statement.strip():
                    continue
                
                try:
                    logger.info(f"Executing SQL {i+1}/{len(statements)}: {statement[:100]}...")
                    result = await self.mysql_service.execute_query(statement)
                    
                    results.append({
                        "statement": statement,
                        "success": True,
                        "result": result,
                        "affected_rows": len(result) if isinstance(result, list) else 1
                    })
                    
                except Exception as e:
                    logger.warning(f"SQL execution failed: {statement[:50]}... - {str(e)}")
                    results.append({
                        "statement": statement,
                        "success": False,
                        "error": str(e)
                    })
            
            # 统计结果
            successful = sum(1 for r in results if r["success"])
            failed = len(results) - successful
            
            return {
                "success": True,
                "total_statements": len(results),
                "successful": successful,
                "failed": failed,
                "results": results
            }
            
        except Exception as e:
            logger.error(f"Failed to execute SQL operations: {str(e)}")
            raise Exception(f"SQL execution failed: {str(e)}")
    
    def _split_sql_statements(self, sql_text: str) -> List[str]:
        """分割SQL语句"""
        try:
            # 简单的SQL分割逻辑
            statements = []
            
            # 按分号分割
            parts = sql_text.split(';')
            
            for part in parts:
                statement = part.strip()
                if statement and not statement.startswith('--'):
                    # 确保语句以分号结尾（除了某些特殊语句）
                    if not statement.endswith(';') and not any(
                        statement.upper().startswith(cmd) for cmd in ['USE ', 'SHOW ', 'DESCRIBE ', 'DESC ']
                    ):
                        statement += ';'
                    statements.append(statement)
            
            return statements
            
        except Exception as e:
            logger.error(f"Failed to split SQL statements: {str(e)}")
            return [sql_text]  # 返回原始文本作为单个语句
    
    async def get_database_info(self) -> Dict[str, Any]:
        """获取数据库信息"""
        try:
            if not self.mysql_service:
                raise Exception("MySQL service not configured")
            
            # 获取数据库列表
            databases = await self.mysql_service.execute_query("SHOW DATABASES")
            
            # 获取当前数据库的表列表
            tables = []
            if self.current_config and self.current_config.database_name:
                try:
                    tables = await self.mysql_service.execute_query("SHOW TABLES")
                except Exception as e:
                    logger.warning(f"获取数据库表列表失败，可能数据库不存在: {str(e)}")
            
            return {
                "success": True,
                "current_database": self.current_config.database_name if self.current_config else None,
                "databases": databases,
                "tables": tables,
                "connection_info": {
                    "host": self.current_config.host if self.current_config else None,
                    "port": self.current_config.port if self.current_config else None
                }
            }
            
        except Exception as e:
            logger.error(f"Failed to get database info: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def close_connection(self):
        """关闭当前连接，强制下次重新配置"""
        try:
            if self.mysql_service:
                await self.mysql_service.close()
                self.mysql_service = None
                logger.info("MCP MySQL connection closed (will force reconfiguration)")
            # 清除配置以强制重新配置
            self.current_config = None
        except Exception as e:
            logger.error(f"Error closing MCP MySQL connection: {str(e)}")

    async def close(self):
        """关闭连接"""
        try:
            if self.mysql_service:
                await self.mysql_service.close()
                self.mysql_service = None
            self.current_config = None
            logger.info("MCP MySQL service closed")
        except Exception as e:
            logger.error(f"Error closing MCP MySQL service: {str(e)}")

# 创建全局实例
mcp_mysql_service = MCPMySQLService()

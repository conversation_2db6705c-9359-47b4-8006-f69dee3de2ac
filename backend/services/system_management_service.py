import asyncio
import subprocess
import logging
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from services.mysql_protocol_service import MySQLProtocolService
from services.remote_packet_capture_service import RemotePacketCaptureService

logger = logging.getLogger(__name__)

@dataclass
class DatabaseDeployment:
    """数据库部署信息"""
    name: str
    host: str
    port: int
    user: str
    password: str
    database: str
    container_name: Optional[str] = None
    container_id: Optional[str] = None
    deployment_type: str = "direct"  # direct, docker, k8s
    ssh_host: Optional[str] = None
    ssh_user: Optional[str] = None
    ssh_password: Optional[str] = None

class SystemManagementService:
    """MySQL系统管理操作服务 - 支持容器化部署"""
    
    def __init__(self):
        self.deployments: Dict[str, DatabaseDeployment] = {}
        self.protocol_services: Dict[str, MySQLProtocolService] = {}
    
    def add_deployment(self, deployment: DatabaseDeployment):
        """添加数据库部署配置"""
        self.deployments[deployment.name] = deployment
        logger.info(f"Added deployment: {deployment.name} ({deployment.deployment_type})")
    
    def remove_deployment(self, name: str):
        """移除数据库部署配置"""
        if name in self.deployments:
            del self.deployments[name]
            if name in self.protocol_services:
                del self.protocol_services[name]
            logger.info(f"Removed deployment: {name}")
    
    async def get_protocol_service(self, deployment_name: str) -> MySQLProtocolService:
        """获取指定部署的协议服务"""
        if deployment_name not in self.deployments:
            raise Exception(f"Deployment '{deployment_name}' not found")
        
        if deployment_name not in self.protocol_services:
            deployment = self.deployments[deployment_name]
            db_config = {
                'host': deployment.host,
                'port': deployment.port,
                'user': deployment.user,
                'password': deployment.password,
                'database': deployment.database
            }
            self.protocol_services[deployment_name] = MySQLProtocolService(database_config=db_config)
        
        return self.protocol_services[deployment_name]
    
    async def execute_system_command(self, deployment_name: str, command: str, **kwargs) -> Dict[str, Any]:
        """执行系统管理命令"""
        try:
            deployment = self.deployments.get(deployment_name)
            if not deployment:
                raise Exception(f"Deployment '{deployment_name}' not found")
            
            logger.info(f"Executing system command '{command}' on deployment '{deployment_name}'")
            
            # 获取协议服务
            protocol_service = await self.get_protocol_service(deployment_name)
            
            # 执行协议命令
            result = await protocol_service.send_command(command, **kwargs)
            
            # 根据部署类型添加额外的系统信息
            if deployment.deployment_type == "docker":
                container_info = await self._get_container_info(deployment)
                result['container_info'] = container_info
            elif deployment.deployment_type == "k8s":
                pod_info = await self._get_pod_info(deployment)
                result['pod_info'] = pod_info
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to execute system command: {e}")
            raise
    
    async def _get_container_info(self, deployment: DatabaseDeployment) -> Dict[str, Any]:
        """获取Docker容器信息"""
        try:
            container_target = deployment.container_name or deployment.container_id
            if not container_target:
                return {"error": "No container name or ID specified"}
            
            # 获取容器状态
            cmd = f"docker inspect {container_target}"
            if deployment.ssh_host:
                cmd = f"ssh {deployment.ssh_user}@{deployment.ssh_host} '{cmd}'"
            
            process = await asyncio.create_subprocess_shell(
                cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                import json
                container_info = json.loads(stdout.decode())[0]
                return {
                    "container_id": container_info.get("Id", "")[:12],
                    "status": container_info.get("State", {}).get("Status", "unknown"),
                    "image": container_info.get("Config", {}).get("Image", "unknown"),
                    "created": container_info.get("Created", "unknown"),
                    "ports": container_info.get("NetworkSettings", {}).get("Ports", {}),
                    "mounts": [mount.get("Source") + ":" + mount.get("Destination") 
                              for mount in container_info.get("Mounts", [])],
                }
            else:
                return {"error": f"Failed to get container info: {stderr.decode()}"}
                
        except Exception as e:
            logger.error(f"Failed to get container info: {e}")
            return {"error": str(e)}
    
    async def _get_pod_info(self, deployment: DatabaseDeployment) -> Dict[str, Any]:
        """获取Kubernetes Pod信息"""
        try:
            pod_name = deployment.container_name
            if not pod_name:
                return {"error": "No pod name specified"}
            
            # 获取Pod状态
            cmd = f"kubectl get pod {pod_name} -o json"
            if deployment.ssh_host:
                cmd = f"ssh {deployment.ssh_user}@{deployment.ssh_host} '{cmd}'"
            
            process = await asyncio.create_subprocess_shell(
                cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                import json
                pod_info = json.loads(stdout.decode())
                return {
                    "pod_name": pod_info.get("metadata", {}).get("name", "unknown"),
                    "namespace": pod_info.get("metadata", {}).get("namespace", "default"),
                    "status": pod_info.get("status", {}).get("phase", "unknown"),
                    "node": pod_info.get("spec", {}).get("nodeName", "unknown"),
                    "image": pod_info.get("spec", {}).get("containers", [{}])[0].get("image", "unknown"),
                    "created": pod_info.get("metadata", {}).get("creationTimestamp", "unknown"),
                }
            else:
                return {"error": f"Failed to get pod info: {stderr.decode()}"}
                
        except Exception as e:
            logger.error(f"Failed to get pod info: {e}")
            return {"error": str(e)}
    
    async def start_system_capture(self, deployment_name: str, capture_dir: str = "/tmp") -> str:
        """启动系统级抓包（支持容器环境）"""
        try:
            deployment = self.deployments.get(deployment_name)
            if not deployment:
                raise Exception(f"Deployment '{deployment_name}' not found")

            # 创建远程抓包服务（使用默认配置）
            capture_service = RemotePacketCaptureService(capture_dir=capture_dir)
            
            # 启动抓包（传递目标主机和端口）
            packet_file = await capture_service.start_capture(
                target_host=deployment.host,
                target_port=deployment.port
            )
            
            logger.info(f"Started system capture for {deployment_name}: {packet_file}")
            return packet_file
            
        except Exception as e:
            logger.error(f"Failed to start system capture: {e}")
            raise
    
    async def _get_container_interface(self, deployment: DatabaseDeployment) -> str:
        """获取Docker容器的网络接口"""
        try:
            container_target = deployment.container_name or deployment.container_id
            cmd = f"docker exec {container_target} ip route | grep default | awk '{{print $5}}'"
            
            if deployment.ssh_host:
                cmd = f"ssh {deployment.ssh_user}@{deployment.ssh_host} '{cmd}'"
            
            process = await asyncio.create_subprocess_shell(
                cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                interface = stdout.decode().strip()
                return interface or "eth0"
            else:
                logger.warning(f"Failed to get container interface: {stderr.decode()}")
                return "eth0"
                
        except Exception as e:
            logger.error(f"Failed to get container interface: {e}")
            return "eth0"
    
    async def _get_pod_interface(self, deployment: DatabaseDeployment) -> str:
        """获取Kubernetes Pod的网络接口"""
        try:
            pod_name = deployment.container_name
            cmd = f"kubectl exec {pod_name} -- ip route | grep default | awk '{{print $5}}'"
            
            if deployment.ssh_host:
                cmd = f"ssh {deployment.ssh_user}@{deployment.ssh_host} '{cmd}'"
            
            process = await asyncio.create_subprocess_shell(
                cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                interface = stdout.decode().strip()
                return interface or "eth0"
            else:
                logger.warning(f"Failed to get pod interface: {stderr.decode()}")
                return "eth0"
                
        except Exception as e:
            logger.error(f"Failed to get pod interface: {e}")
            return "eth0"
    
    async def get_system_status(self, deployment_name: str) -> Dict[str, Any]:
        """获取系统状态信息"""
        try:
            deployment = self.deployments.get(deployment_name)
            if not deployment:
                raise Exception(f"Deployment '{deployment_name}' not found")
            
            # 获取MySQL状态
            protocol_service = await self.get_protocol_service(deployment_name)
            mysql_status = await protocol_service.send_command("STATISTICS")
            
            # 获取系统信息
            system_info = {}
            if deployment.deployment_type == "docker":
                system_info = await self._get_container_info(deployment)
            elif deployment.deployment_type == "k8s":
                system_info = await self._get_pod_info(deployment)
            
            return {
                "deployment_name": deployment_name,
                "deployment_type": deployment.deployment_type,
                "mysql_status": mysql_status,
                "system_info": system_info,
                "timestamp": asyncio.get_event_loop().time()
            }
            
        except Exception as e:
            logger.error(f"Failed to get system status: {e}")
            raise
    
    async def close(self):
        """关闭所有连接"""
        for service in self.protocol_services.values():
            await service.close()
        self.protocol_services.clear()
        logger.info("System management service closed")

# 全局系统管理服务实例
system_management_service = SystemManagementService()

"""
执行日志服务
"""
import logging
import traceback
import inspect
from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import desc, and_, or_, func
from services.mysql_service import MySQLService
from models.execution_log import (
    ExecutionLogCreate, ExecutionLogResponse,
    ExecutionLogQuery, ExecutionLogStats, LogLevel, LogCategory
)
from utils.config import Config

logger = logging.getLogger(__name__)


class ExecutionLogService:
    """执行日志服务"""

    def __init__(self):
        self.mysql_service: Optional[MySQLService] = None

    async def initialize(self):
        """初始化服务"""
        try:
            # 使用管理数据库配置
            self.mysql_service = MySQLService(
                host=Config.MYSQL_HOST,
                port=Config.MYSQL_PORT,
                user=Config.MYSQL_USER,
                password=Config.MYSQL_PASSWORD,
                database=Config.MYSQL_DATABASE
            )
            logger.info("ExecutionLogService initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize ExecutionLogService: {e}")
            raise
    
    def _get_caller_info(self) -> tuple:
        """获取调用者信息"""
        try:
            # 获取调用栈
            frame = inspect.currentframe()
            # 跳过当前方法和log方法
            caller_frame = frame.f_back.f_back.f_back if frame and frame.f_back and frame.f_back.f_back else None
            
            if caller_frame:
                module_name = caller_frame.f_globals.get('__name__', 'unknown')
                function_name = caller_frame.f_code.co_name
                line_number = caller_frame.f_lineno
                return module_name, function_name, line_number
            
            return 'unknown', 'unknown', 0
        except Exception:
            return 'unknown', 'unknown', 0
    
    async def log(
        self,
        level: LogLevel,
        category: LogCategory,
        message: str,
        task_id: Optional[str] = None,
        test_case_id: Optional[str] = None,
        execution_id: Optional[str] = None,
        database_config_id: Optional[int] = None,
        sql_query: Optional[str] = None,
        capture_file: Optional[str] = None,
        error_details: Optional[str] = None,
        database_type: Optional[str] = None,
        target_host: Optional[str] = None,
        target_port: Optional[int] = None,
        include_stack_trace: bool = False
    ) -> Optional[int]:
        """记录执行日志"""
        try:
            if not self.mysql_service:
                await self.initialize()

            # 获取调用者信息
            module_name, function_name, line_number = self._get_caller_info()

            # 获取堆栈跟踪（如果需要）
            stack_trace = None
            if include_stack_trace or level in [LogLevel.ERROR, LogLevel.CRITICAL]:
                stack_trace = traceback.format_stack()[-5:]  # 只保留最近5层调用栈
                stack_trace = ''.join(stack_trace)

            # 插入到数据库
            sql = """
                INSERT INTO execution_logs (
                    task_id, test_case_id, execution_id, database_config_id,
                    level, category, message, module_name, function_name, line_number,
                    sql_query, capture_file, error_details, stack_trace,
                    database_type, target_host, target_port
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """

            params = (
                task_id, test_case_id, execution_id, database_config_id,
                level.value, category.value, message, module_name, function_name, line_number,
                sql_query, capture_file, error_details, stack_trace,
                database_type, target_host, target_port
            )

            result = await self.mysql_service.execute_query(sql, params)

            # 同时输出到标准日志
            log_msg = f"[{category.value}] {message}"
            if task_id:
                log_msg = f"[Task:{task_id}] {log_msg}"

            if level == LogLevel.DEBUG:
                logger.debug(log_msg)
            elif level == LogLevel.INFO:
                logger.info(log_msg)
            elif level == LogLevel.WARNING:
                logger.warning(log_msg)
            elif level == LogLevel.ERROR:
                logger.error(log_msg)
            elif level == LogLevel.CRITICAL:
                logger.critical(log_msg)

            return result.get('lastrowid') if result else None

        except Exception as e:
            logger.error(f"Failed to save execution log: {e}")
            # 确保即使日志保存失败，也要输出到标准日志
            logger.error(f"Original log message: [{category.value}] {message}")
            return None
    
    async def get_logs(self, query: ExecutionLogQuery) -> Dict[str, Any]:
        """查询执行日志"""
        try:
            if not self.mysql_service:
                await self.initialize()

            # 构建查询条件
            where_conditions = []
            params = []

            if query.task_id:
                where_conditions.append("task_id = %s")
                params.append(query.task_id)
            if query.test_case_id:
                where_conditions.append("test_case_id = %s")
                params.append(query.test_case_id)
            if query.execution_id:
                where_conditions.append("execution_id = %s")
                params.append(query.execution_id)
            if query.database_config_id:
                where_conditions.append("database_config_id = %s")
                params.append(query.database_config_id)
            if query.level:
                where_conditions.append("level = %s")
                params.append(query.level.value)
            if query.category:
                where_conditions.append("category = %s")
                params.append(query.category.value)
            if query.database_type:
                where_conditions.append("database_type = %s")
                params.append(query.database_type)
            if query.start_time:
                where_conditions.append("created_time >= %s")
                params.append(query.start_time)
            if query.end_time:
                where_conditions.append("created_time <= %s")
                params.append(query.end_time)
            if query.keyword:
                where_conditions.append("message LIKE %s")
                params.append(f"%{query.keyword}%")

            # 构建WHERE子句
            where_clause = ""
            if where_conditions:
                where_clause = "WHERE " + " AND ".join(where_conditions)

            # 获取总数
            count_sql = f"SELECT COUNT(*) as total FROM execution_logs {where_clause}"
            count_result = await self.mysql_service.execute_query(count_sql, params)
            total_count = count_result['data'][0]['total'] if count_result and count_result['data'] else 0

            # 分页查询
            offset = (query.page - 1) * query.page_size
            data_sql = f"""
                SELECT * FROM execution_logs
                {where_clause}
                ORDER BY created_time DESC
                LIMIT %s OFFSET %s
            """
            data_params = params + [query.page_size, offset]
            data_result = await self.mysql_service.execute_query(data_sql, data_params)

            logs = data_result['data'] if data_result and data_result['data'] else []

            return {
                "logs": logs,
                "total": total_count,
                "page": query.page,
                "page_size": query.page_size,
                "total_pages": (total_count + query.page_size - 1) // query.page_size
            }

        except Exception as e:
            logger.error(f"Failed to query execution logs: {e}")
            raise
    
    async def get_stats(self,
                       task_id: Optional[str] = None,
                       hours: int = 24) -> Dict[str, Any]:
        """获取执行日志统计"""
        try:
            if not self.mysql_service:
                await self.initialize()

            # 时间范围
            start_time = datetime.now() - timedelta(hours=hours)

            # 构建基础条件
            where_conditions = ["created_time >= %s"]
            params = [start_time]

            if task_id:
                where_conditions.append("task_id = %s")
                params.append(task_id)

            where_clause = "WHERE " + " AND ".join(where_conditions)

            # 总数统计
            total_sql = f"SELECT COUNT(*) as total FROM execution_logs {where_clause}"
            total_result = await self.mysql_service.execute_query(total_sql, params)
            total_count = total_result['data'][0]['total'] if total_result and total_result['data'] else 0

            # 按级别统计
            level_sql = f"""
                SELECT level, COUNT(*) as count
                FROM execution_logs {where_clause}
                GROUP BY level
            """
            level_result = await self.mysql_service.execute_query(level_sql, params)
            level_stats = level_result['data'] if level_result and level_result['data'] else []

            error_count = sum(item['count'] for item in level_stats if item['level'] == 'ERROR')
            warning_count = sum(item['count'] for item in level_stats if item['level'] == 'WARNING')
            info_count = sum(item['count'] for item in level_stats if item['level'] == 'INFO')
            debug_count = sum(item['count'] for item in level_stats if item['level'] == 'DEBUG')

            # 按分类统计
            category_sql = f"""
                SELECT category, COUNT(*) as count
                FROM execution_logs {where_clause}
                GROUP BY category
            """
            category_result = await self.mysql_service.execute_query(category_sql, params)
            category_stats = category_result['data'] if category_result and category_result['data'] else []
            categories = {item['category']: item['count'] for item in category_stats}

            # 最近的错误日志
            error_where = where_conditions + ["level = 'ERROR'"]
            error_where_clause = "WHERE " + " AND ".join(error_where)
            error_params = params + []

            recent_errors_sql = f"""
                SELECT * FROM execution_logs {error_where_clause}
                ORDER BY created_time DESC
                LIMIT 10
            """
            recent_errors_result = await self.mysql_service.execute_query(recent_errors_sql, error_params)
            recent_errors = recent_errors_result['data'] if recent_errors_result and recent_errors_result['data'] else []

            return {
                "total_count": total_count,
                "error_count": error_count,
                "warning_count": warning_count,
                "info_count": info_count,
                "debug_count": debug_count,
                "categories": categories,
                "recent_errors": recent_errors
            }

        except Exception as e:
            logger.error(f"Failed to get execution log stats: {e}")
            raise
    
    async def cleanup_old_logs(self, days: int = 30) -> int:
        """清理旧日志"""
        try:
            if not self.mysql_service:
                await self.initialize()

            cutoff_time = datetime.now() - timedelta(days=days)

            # 删除旧日志
            delete_sql = "DELETE FROM execution_logs WHERE created_time < %s"
            result = await self.mysql_service.execute_query(delete_sql, (cutoff_time,))

            deleted_count = result.get('affected_rows', 0) if result else 0

            logger.info(f"Cleaned up {deleted_count} old execution logs")
            return deleted_count

        except Exception as e:
            logger.error(f"Failed to cleanup old logs: {e}")
            raise


# 全局日志服务实例
execution_log_service = ExecutionLogService()

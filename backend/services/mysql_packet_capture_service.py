"""
MySQL数据包捕获服务 - 支持AI控制网卡抓包和完整握手挥手包捕获
"""

import os
import time
import asyncio
import logging
import traceback
from typing import Optional, Dict, Any, List
import paramiko
from models.server_config import ServerConfig
from services.network_detection_service import NetworkDetectionService
from services.server_config_service import ServerConfigService
from services.tcpdump_installer_service import TcpdumpInstallerService
from utils.path_manager import path_manager

logger = logging.getLogger(__name__)

class MySQLPacketCaptureService:
    """MySQL数据包捕获服务 - 支持AI智能网卡检测和完整TCP会话捕获"""
    
    def __init__(self, capture_dir: str = None):
        # 使用统一路径管理器，确保使用绝对路径
        if capture_dir is None:
            capture_dir = path_manager.get_captures_dir()
        self.capture_dir = os.path.abspath(capture_dir)
        self.is_capturing = False
        self.current_file = None
        self.ssh_client = None
        self.tcpdump_pid = None
        
        # AI智能抓包相关
        self.network_detection_service = NetworkDetectionService()
        self.server_config_service = ServerConfigService()
        self.tcpdump_installer_service = TcpdumpInstallerService()
        self.current_server_config = None
        self.current_capture_strategy = None
        self.current_retry_count = 0
        self.max_retry_attempts = 3
        self.failed_strategies = []
        
        # 确保捕获目录存在
        os.makedirs(capture_dir, exist_ok=True)
    
    async def start_smart_capture(
        self, 
        mysql_host: str, 
        mysql_port: int, 
        server_config_id: int = None
    ) -> str:
        """启动AI智能MySQL数据包捕获 - 自动检测最佳网络接口"""
        try:
            # 重置重试状态
            self.current_retry_count = 0
            self.failed_strategies = []
            
            # 获取服务器配置
            if server_config_id:
                self.current_server_config = await self.server_config_service.get_config(server_config_id)
            else:
                # 使用默认服务器配置
                self.current_server_config = await self.server_config_service.get_default_config()

            if not self.current_server_config:
                logger.warning("No server configuration found, using legacy configuration")
                return await self._start_legacy_capture(mysql_host, mysql_port)

            logger.info(f"Using server config: {self.current_server_config.host}:{self.current_server_config.port}")
            
            # 智能重试机制
            for attempt in range(self.max_retry_attempts):
                try:
                    logger.info(f"Starting MySQL smart capture attempt {attempt + 1}/{self.max_retry_attempts}")
                    
                    # 检测数据库部署方式
                    deployment = await self.network_detection_service.detect_database_deployment(
                        self.current_server_config, 'mysql', mysql_host, mysql_port
                    )
                    
                    # 生成抓包策略
                    self.current_capture_strategy = await self.network_detection_service.generate_capture_strategy(
                        self.current_server_config, deployment
                    )
                    
                    logger.info(f"Generated MySQL capture strategy: interface={self.current_capture_strategy.interface}, "
                               f"filter={self.current_capture_strategy.filter_expression}, "
                               f"confidence={self.current_capture_strategy.confidence}")
                    
                    # 执行抓包
                    result = await self._execute_smart_capture(mysql_host, mysql_port)
                    
                    if result:
                        # 验证抓包质量
                        if await self._validate_capture_quality(result):
                            logger.info(f"Smart MySQL capture successful: {result}")
                            return result
                        else:
                            logger.warning(f"Capture quality validation failed, attempt {attempt + 1}")
                            self.current_retry_count += 1
                            if attempt < self.max_retry_attempts - 1:
                                await self._prepare_next_retry_strategy(mysql_host, mysql_port)
                    else:
                        self.current_retry_count += 1
                        
                except Exception as e:
                    logger.error(f"Smart capture attempt {attempt + 1} failed: {str(e)}")
                    self.current_retry_count += 1
                    if attempt < self.max_retry_attempts - 1:
                        await asyncio.sleep(2)  # 等待后重试
            
            # 所有智能尝试都失败，使用简化的智能抓包
            logger.warning("All smart capture attempts failed, using simplified smart capture")
            return await self._start_simplified_smart_capture(mysql_host, mysql_port)
            
        except Exception as e:
            logger.error(f"Failed to start smart MySQL packet capture: {str(e)}")
            await self._cleanup()
            raise Exception(f"Smart MySQL packet capture failed: {str(e)}")

    async def _start_simplified_smart_capture(self, mysql_host: str, mysql_port: int) -> str:
        """简化的智能抓包 - 确保能正常工作"""
        try:
            if self.is_capturing:
                logger.warning("检测到抓包状态异常，强制重置状态（简化智能抓包）")
                await self.force_reset_state()

            # 生成文件名
            timestamp = int(time.time())
            filename = f"mysql_capture_simplified_{timestamp}.pcap"
            local_file = os.path.join(self.capture_dir, filename)
            remote_file = f"/tmp/{filename}"

            logger.info(f"Starting simplified smart capture: {local_file}")

            # 建立SSH连接
            await self._connect_ssh_with_config()

            # 启动简化的智能远程抓包
            await self._start_simplified_remote_capture(remote_file, mysql_host, mysql_port)

            self.is_capturing = True
            self.current_file = local_file

            logger.info(f"Simplified smart MySQL packet capture started: {local_file}")
            return local_file

        except Exception as e:
            logger.error(f"Failed to start simplified smart MySQL packet capture: {str(e)}")
            await self._cleanup()
            raise Exception(f"Simplified smart MySQL packet capture failed: {str(e)}")

    async def _execute_smart_capture(self, mysql_host: str, mysql_port: int) -> Optional[str]:
        """执行智能抓包"""
        try:
            # 如果已在抓包，先停止之前的抓包
            if self.is_capturing:
                logger.warning("MySQL packet capture is already running, stopping previous capture")
                await self.stop_capture()
                await asyncio.sleep(1)  # 等待停止完成

            # 生成唯一的文件名
            timestamp = int(time.time())
            filename = f"mysql_capture_{timestamp}_attempt_{self.current_retry_count + 1}.pcap"
            local_file = os.path.join(self.capture_dir, filename)
            remote_file = f"/tmp/{filename}"

            logger.info(f"Starting smart capture attempt {self.current_retry_count + 1}: {local_file}")

            # 建立SSH连接
            await self._connect_ssh_with_config()

            # 启动智能远程抓包
            await self._start_smart_remote_capture(remote_file)

            self.is_capturing = True
            self.current_file = local_file

            return local_file

        except Exception as e:
            logger.error(f"Failed to execute smart MySQL capture: {str(e)}")
            await self._cleanup()
            raise
    
    async def start_capture(self, target_host: str = None, target_port: int = None, server_config_id: int = None) -> str:
        """启动MySQL数据包捕获 - 兼容原有接口"""
        mysql_host = target_host or "**************"
        mysql_port = target_port or 3306

        logger.info(f"MySQL抓包配置 - Host: {mysql_host}, Port: {mysql_port}, 原始参数 - target_host: {target_host}, target_port: {target_port}")

        # 如果检测到抓包状态异常，强制重置
        if self.is_capturing:
            logger.warning("检测到抓包状态异常，强制重置状态")
            await self.force_reset_state()
        
        # 使用智能抓包（包含简化回退）
        return await self.start_smart_capture(mysql_host, mysql_port, server_config_id)
    
    async def _start_legacy_capture(self, mysql_host: str, mysql_port: int) -> str:
        """传统MySQL抓包方式 - 作为回退方案"""
        try:
            if self.is_capturing:
                logger.warning("检测到抓包状态异常，强制重置状态（传统抓包）")
                await self.force_reset_state()
            
            # 生成文件名
            timestamp = int(time.time())
            filename = f"mysql_capture_legacy_{timestamp}.pcap"
            local_file = os.path.join(self.capture_dir, filename)
            remote_file = f"/tmp/{filename}"
            
            # 建立SSH连接
            await self._connect_ssh_legacy()
            
            # 启动传统远程抓包
            await self._start_legacy_remote_capture(remote_file, mysql_host, mysql_port)
            
            self.is_capturing = True
            self.current_file = local_file
            
            logger.info(f"Legacy MySQL packet capture started: {local_file}")
            return local_file
            
        except Exception as e:
            logger.error(f"Failed to start legacy MySQL packet capture: {str(e)}")
            await self._cleanup()
            raise Exception(f"Legacy MySQL packet capture failed: {str(e)}")
    
    async def stop_capture(self) -> Optional[str]:
        """停止MySQL数据包捕获"""
        captured_file = None

        try:
            if not self.is_capturing:
                logger.warning("MySQL packet capture is not running")
                return None

            captured_file = self.current_file

            # 停止远程抓包（不抛出异常）
            try:
                await self._stop_remote_capture()
                logger.info("Remote capture stopped successfully")
            except Exception as e:
                logger.warning(f"Failed to stop remote capture, but continuing: {e}")

            # 下载抓包文件（不抛出异常）
            if self.current_file:
                try:
                    # 使用正确的远程文件路径
                    if hasattr(self, '_current_remote_file') and self._current_remote_file:
                        remote_file = self._current_remote_file
                    else:
                        remote_file = f"/tmp/{os.path.basename(self.current_file)}"

                    await self._download_capture_file(remote_file, self.current_file)
                    logger.info("Capture file downloaded successfully")
                except Exception as e:
                    logger.warning(f"Failed to download capture file, but continuing: {e}")

            # 清理资源
            try:
                await self._cleanup()
            except Exception as e:
                logger.warning(f"Cleanup failed, but continuing: {e}")

            # 重置状态
            self.is_capturing = False
            self.current_file = None

            logger.info(f"MySQL packet capture stopped: {captured_file}")
            return captured_file

        except Exception as e:
            logger.error(f"Unexpected error in stop_capture: {str(e)}")
            # 确保状态被重置
            self.is_capturing = False
            self.current_file = None
            try:
                await self._cleanup()
            except Exception as e:

                logger.error(f"操作失败: {type(e).__name__}: {str(e)}")

                logger.error(f"异常堆栈跟踪: {traceback.format_exc()}")
            # 返回文件路径而不是抛出异常
            logger.warning("Returning capture file despite errors")
            return captured_file
    
    def get_capture_status(self) -> Dict[str, Any]:
        """获取抓包状态"""
        return {
            'is_capturing': self.is_capturing,
            'current_file': self.current_file,
            'capture_dir': self.capture_dir,
            'current_strategy': self.current_capture_strategy.to_dict() if self.current_capture_strategy else None,
            'retry_count': self.current_retry_count,
            'failed_strategies': self.failed_strategies
        }
    
    def get_current_file(self) -> Optional[str]:
        """获取当前抓包文件"""
        return self.current_file

    async def force_reset_state(self):
        """强制重置抓包状态 - 用于异常情况下的状态恢复"""
        try:
            logger.info("强制重置MySQL抓包状态")

            # 尝试清理资源
            try:
                await self._cleanup()
            except Exception as e:
                logger.warning(f"清理资源时出错: {e}")

            # 重置状态
            self.is_capturing = False
            self.current_file = None
            self.tcpdump_pid = None

            logger.info("MySQL抓包状态已强制重置")

        except Exception as e:
            logger.error(f"强制重置状态时出错: {e}")
            # 无论如何都要重置基本状态
            self.is_capturing = False
            self.current_file = None
    
    def is_capturing_active(self) -> bool:
        """检查是否正在抓包"""
        return self.is_capturing

    async def _connect_ssh_with_config(self):
        """使用服务器配置建立SSH连接"""
        try:
            if self.ssh_client:
                try:
                    # 测试现有连接
                    self.ssh_client.exec_command('echo test', timeout=5)
                    logger.debug("Existing SSH connection is still valid")
                    return
                except:
                    # 连接已断开，重新连接
                    logger.info("Existing SSH connection is broken, reconnecting...")
                    self.ssh_client.close()
                    self.ssh_client = None

            if not self.current_server_config:
                raise Exception("No server configuration available for SSH connection")

            # 建立新连接
            logger.info(f"Establishing SSH connection to {self.current_server_config.host}:{self.current_server_config.port}")
            self.ssh_client = paramiko.SSHClient()
            self.ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())

            self.ssh_client.connect(
                hostname=self.current_server_config.host,
                port=self.current_server_config.port,
                username=self.current_server_config.username,
                password=self.current_server_config.password,
                timeout=10
            )

            logger.info(f"SSH connection established successfully to {self.current_server_config.host}")

        except Exception as e:
            logger.error(f"Failed to establish SSH connection to {self.current_server_config.host if self.current_server_config else 'unknown'}: {str(e)}")
            self.ssh_client = None
            raise

    async def _connect_ssh_legacy(self):
        """传统SSH连接方式"""
        try:
            if self.ssh_client:
                try:
                    self.ssh_client.exec_command('echo test', timeout=5)
                    return
                except:
                    self.ssh_client.close()
                    self.ssh_client = None

            self.ssh_client = paramiko.SSHClient()
            self.ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())

            # 使用默认配置
            self.ssh_client.connect(
                hostname="**************",
                port=22,
                username="root",
                password="QZ@1005#1005",
                timeout=10
            )

            logger.info("Legacy SSH connection established")

        except Exception as e:
            logger.error(f"Failed to establish legacy SSH connection: {str(e)}")
            raise

    async def _start_smart_remote_capture(self, remote_file: str):
        """启动智能远程MySQL抓包 - 使用AI生成的策略"""
        try:
            if not self.current_capture_strategy:
                raise Exception("No capture strategy available")

            # 构建智能tcpdump命令
            interface = self.current_capture_strategy.interface
            filter_expr = self.current_capture_strategy.filter_expression

            # 获取抓包选项
            capture_options = self.current_capture_strategy.capture_options or {}
            buffer_size = capture_options.get('buffer_size', 65536)
            snaplen = capture_options.get('snaplen', 0)

            # 构建完整的tcpdump命令，确保捕获握手和挥手包
            tcpdump_cmd = (
                f"nohup tcpdump -i {interface} -w {remote_file} "
                f"-s {snaplen} -B {buffer_size} "
                f"'{filter_expr}' "
                f"> /tmp/tcpdump_mysql.log 2>&1 & echo $!"
            )

            logger.info(f"Starting smart MySQL tcpdump: {tcpdump_cmd}")

            # 确保tcpdump可用
            if not await self._ensure_tcpdump_available():
                raise Exception("tcpdump is not available and could not be installed")

            # 执行tcpdump命令
            stdin, stdout, stderr = self.ssh_client.exec_command(tcpdump_cmd)
            output = stdout.read().decode('utf-8').strip()

            if output:
                self.tcpdump_pid = output
                logger.info(f"Smart MySQL tcpdump started with PID: {self.tcpdump_pid}")
            else:
                # 尝试获取PID
                get_pid_cmd = f"pgrep -f 'tcpdump.*{remote_file}'"
                stdin, stdout, stderr = self.ssh_client.exec_command(get_pid_cmd)
                pid_output = stdout.read().decode('utf-8').strip()

                if pid_output:
                    self.tcpdump_pid = pid_output.split('\n')[0]
                    logger.info(f"MySQL tcpdump started with PID: {self.tcpdump_pid}")
                else:
                    logger.warning("Could not get tcpdump PID")

        except Exception as e:
            logger.error(f"Failed to start smart remote MySQL capture: {str(e)}")
            raise

    async def _start_simplified_remote_capture(self, remote_file: str, mysql_host: str, mysql_port: int):
        """启动简化的智能远程MySQL抓包 - 按优先级尝试所有网卡"""
        # 获取按优先级排序的网络接口列表
        prioritized_interfaces = await self._get_prioritized_interfaces(mysql_host)

        # 构建过滤表达式
        if mysql_port != 3306:
            # 如果是非标准端口，可能是Docker端口映射，同时监听两个端口
            filter_expr = f"(tcp port {mysql_port} or tcp port 3306)"
        else:
            filter_expr = f"tcp port {mysql_port}"

        # 按优先级尝试每个网络接口
        last_error = None
        for i, interface in enumerate(prioritized_interfaces):
            try:
                logger.info(f"Attempting capture on interface {interface} (priority {i+1}/{len(prioritized_interfaces)})")

                # 为每个接口尝试生成唯一的远程文件名
                interface_remote_file = f"{remote_file}_{interface}"

                tcpdump_cmd = (
                    f"nohup tcpdump -i {interface} -w {interface_remote_file} "
                    f"-s 0 -B 65536 "
                    f"'{filter_expr}' "
                    f"> /tmp/tcpdump_mysql_simplified_{interface}.log 2>&1 & echo $!"
                )

                logger.info(f"简化智能抓包配置 - Host: {mysql_host}, Port: {mysql_port}, Interface: {interface}")
                logger.info(f"Starting tcpdump on {interface}: {tcpdump_cmd}")

                # 确保tcpdump可用
                if not await self._ensure_tcpdump_available():
                    raise Exception("tcpdump is not available and could not be installed")

                # 执行tcpdump命令
                stdin, stdout, stderr = self.ssh_client.exec_command(tcpdump_cmd)
                output = stdout.read().decode('utf-8').strip()
                error_output = stderr.read().decode('utf-8').strip()

                if error_output:
                    logger.warning(f"tcpdump stderr output on {interface}: {error_output}")
                    # 检查是否是致命错误
                    if any(fatal_error in error_output.lower() for fatal_error in
                           ['no such device', 'permission denied', 'operation not permitted']):
                        logger.warning(f"Fatal error on interface {interface}, trying next interface")
                        continue

                logger.info(f"tcpdump command output on {interface}: '{output}'")

                # 验证tcpdump进程启动
                success = await self._verify_tcpdump_process(output, interface_remote_file, interface)

                if success:
                    # 更新远程文件路径为成功的接口
                    # 这里需要更新实例变量，以便后续下载使用正确的文件名
                    self._current_remote_file = interface_remote_file
                    logger.info(f"✅ Successfully started capture on interface {interface}")
                    return
                else:
                    logger.warning(f"❌ Failed to verify tcpdump process on interface {interface}")
                    continue

            except Exception as e:
                last_error = e
                logger.warning(f"❌ Failed to start capture on interface {interface}: {str(e)}")
                continue

        # 如果所有接口都失败了
        error_msg = f"Failed to start capture on all {len(prioritized_interfaces)} interfaces. Last error: {last_error}"
        logger.error(error_msg)
        raise Exception(error_msg)

    async def _verify_tcpdump_process(self, command_output: str, remote_file: str, interface: str) -> bool:
        """验证tcpdump进程是否成功启动"""
        try:
            if command_output:
                self.tcpdump_pid = command_output
                logger.info(f"tcpdump started on {interface} with PID: {self.tcpdump_pid}")

                # 验证进程是否真的在运行
                await asyncio.sleep(1)  # 等待进程启动
                check_cmd = f"ps -p {self.tcpdump_pid} -o pid,cmd"
                _, check_stdout, _ = self.ssh_client.exec_command(check_cmd)
                check_output = check_stdout.read().decode('utf-8').strip()

                if self.tcpdump_pid in check_output:
                    logger.info(f"✅ Verified tcpdump process running on {interface}: {check_output}")
                    return True
                else:
                    logger.warning(f"❌ tcpdump process {self.tcpdump_pid} not found on {interface}")
                    return False

            else:
                # 尝试通过进程名获取PID
                get_pid_cmd = f"pgrep -f 'tcpdump.*{remote_file}'"
                _, stdout, _ = self.ssh_client.exec_command(get_pid_cmd)
                pid_output = stdout.read().decode('utf-8').strip()

                if pid_output:
                    self.tcpdump_pid = pid_output.split('\n')[0]
                    logger.info(f"✅ Found tcpdump process on {interface} with PID: {self.tcpdump_pid}")
                    return True
                else:
                    logger.warning(f"❌ Could not find tcpdump process on {interface}")
                    return False

        except Exception as e:
            logger.error(f"❌ Error verifying tcpdump process on {interface}: {str(e)}")
            return False

            if output:
                self.tcpdump_pid = output
                logger.info(f"Simplified smart MySQL tcpdump started with PID: {self.tcpdump_pid}")

                # 验证进程是否真的在运行
                await asyncio.sleep(1)  # 等待进程启动
                check_cmd = f"ps -p {self.tcpdump_pid} -o pid,cmd"
                _, check_stdout, _ = self.ssh_client.exec_command(check_cmd)
                check_output = check_stdout.read().decode('utf-8').strip()

                if self.tcpdump_pid in check_output:
                    logger.info(f"Verified tcpdump process is running: {check_output}")
                else:
                    logger.warning(f"tcpdump process {self.tcpdump_pid} not found in process list")

            else:
                # 尝试获取PID
                get_pid_cmd = f"pgrep -f 'tcpdump.*{remote_file}'"
                _, stdout, _ = self.ssh_client.exec_command(get_pid_cmd)
                pid_output = stdout.read().decode('utf-8').strip()

                if pid_output:
                    self.tcpdump_pid = pid_output.split('\n')[0]
                    logger.info(f"Simplified smart MySQL tcpdump started with PID: {self.tcpdump_pid}")
                else:
                    logger.warning("Could not get tcpdump PID for simplified smart capture")
                    # 检查是否有tcpdump进程在运行
                    check_all_cmd = "pgrep -f tcpdump"
                    _, check_stdout, _ = self.ssh_client.exec_command(check_all_cmd)
                    all_pids = check_stdout.read().decode('utf-8').strip()
                    if all_pids:
                        logger.info(f"Found other tcpdump processes: {all_pids}")
                    else:
                        logger.warning("No tcpdump processes found at all")

        except Exception as e:
            logger.error(f"Failed to start simplified smart remote MySQL capture: {str(e)}")
            raise

    async def _get_prioritized_interfaces(self, mysql_host: str) -> List[str]:
        """获取按优先级排序的网络接口列表 - 与AI Docker环境策略一致"""
        try:
            # 检查可用的网络接口
            check_interfaces_cmd = "ip addr show | grep -E '^[0-9]+:' | awk '{print $2}' | sed 's/:$//'"
            _, stdout, _ = self.ssh_client.exec_command(check_interfaces_cmd)
            available_interfaces = stdout.read().decode('utf-8').strip().split('\n')
            available_interfaces = [iface.strip() for iface in available_interfaces if iface.strip()]
            logger.info(f"Available network interfaces: {available_interfaces}")

            # 按照AI Docker环境的优先级策略排序
            prioritized_interfaces = []

            # 1. 🥇 docker0接口 - 最优选择（适合Docker部署）
            if 'docker0' in available_interfaces:
                prioritized_interfaces.append('docker0')
                logger.info("Added docker0 interface (highest priority for Docker deployments)")

            # 2. 🥈 物理网络接口 - 按优先顺序
            physical_priority = ['eth0', 'ens3', 'enp0s3', 'ens33']
            for preferred in physical_priority:
                if preferred in available_interfaces and preferred not in prioritized_interfaces:
                    prioritized_interfaces.append(preferred)
                    logger.info(f"Added physical interface: {preferred}")

            # 3. 🥉 路由表推荐的接口
            try:
                route_cmd = f"ip route get {mysql_host} 2>/dev/null | head -1"
                _, stdout, _ = self.ssh_client.exec_command(route_cmd)
                route_output = stdout.read().decode('utf-8').strip()

                if route_output and 'dev' in route_output:
                    parts = route_output.split()
                    for i, part in enumerate(parts):
                        if part == 'dev' and i + 1 < len(parts):
                            route_interface = parts[i + 1]
                            if (route_interface in available_interfaces and
                                route_interface not in prioritized_interfaces and
                                route_interface != 'any'):
                                prioritized_interfaces.append(route_interface)
                                logger.info(f"Added route-based interface: {route_interface}")
                            break
            except Exception as e:
                logger.warning(f"Failed to get route-based interface: {e}")

            # 4. 🏃 其他可用接口（除了lo和any）
            for interface in available_interfaces:
                if (interface not in prioritized_interfaces and
                    interface not in ['lo', 'any'] and
                    interface.strip()):
                    prioritized_interfaces.append(interface)
                    logger.info(f"Added fallback interface: {interface}")

            # 5. 🚨 最后回退：回环接口
            if 'lo' in available_interfaces and 'lo' not in prioritized_interfaces:
                prioritized_interfaces.append('lo')
                logger.info("Added lo interface as final fallback")

            if not prioritized_interfaces:
                logger.error("No suitable network interfaces found")
                return ['docker0']  # 默认回退

            logger.info(f"Interface priority order: {prioritized_interfaces}")
            return prioritized_interfaces

        except Exception as e:
            logger.error(f"Failed to get prioritized interfaces: {e}")
            return ['docker0', 'ens3', 'eth0', 'lo']  # 默认优先级列表

    async def _select_best_interface(self, mysql_host: str) -> str:
        """选择最佳网络接口 - 兼容性方法"""
        interfaces = await self._get_prioritized_interfaces(mysql_host)
        return interfaces[0] if interfaces else 'docker0'

    async def _start_legacy_remote_capture(self, remote_file: str, mysql_host: str, mysql_port: int):
        """启动传统远程MySQL抓包 - 按优先级尝试所有网卡"""
        # 获取按优先级排序的网络接口列表
        prioritized_interfaces = await self._get_prioritized_interfaces(mysql_host)

        # 构建过滤表达式
        if mysql_port != 3306:
            # 如果是非标准端口，可能是Docker端口映射，同时监听两个端口
            filter_expr = f"(tcp port {mysql_port} or tcp port 3306)"
        else:
            filter_expr = f"tcp port {mysql_port}"

        # 按优先级尝试每个网络接口
        last_error = None
        for i, interface in enumerate(prioritized_interfaces):
            try:
                logger.info(f"Attempting legacy capture on interface {interface} (priority {i+1}/{len(prioritized_interfaces)})")

                # 为每个接口尝试生成唯一的远程文件名
                interface_remote_file = f"{remote_file}_legacy_{interface}"

                tcpdump_cmd = (
                    f"nohup tcpdump -i {interface} -w {interface_remote_file} "
                    f"-s 0 -B 65536 "
                    f"'{filter_expr}' "
                    f"> /tmp/tcpdump_mysql_legacy_{interface}.log 2>&1 & echo $!"
                )

                logger.info(f"传统抓包配置 - Host: {mysql_host}, Port: {mysql_port}, Interface: {interface}")
                logger.info(f"Starting legacy tcpdump on {interface}: {tcpdump_cmd}")

                # 确保tcpdump可用
                if not await self._ensure_tcpdump_available():
                    raise Exception("tcpdump is not available and could not be installed")

                # 执行tcpdump命令
                stdin, stdout, stderr = self.ssh_client.exec_command(tcpdump_cmd)
                output = stdout.read().decode('utf-8').strip()
                error_output = stderr.read().decode('utf-8').strip()

                if error_output:
                    logger.warning(f"tcpdump stderr output on {interface}: {error_output}")
                    # 检查是否是致命错误
                    if any(fatal_error in error_output.lower() for fatal_error in
                           ['no such device', 'permission denied', 'operation not permitted']):
                        logger.warning(f"Fatal error on interface {interface}, trying next interface")
                        continue

                logger.info(f"tcpdump command output on {interface}: '{output}'")

                # 验证tcpdump进程启动
                success = await self._verify_tcpdump_process(output, interface_remote_file, interface)

                if success:
                    # 更新远程文件路径为成功的接口
                    self._current_remote_file = interface_remote_file
                    logger.info(f"✅ Successfully started legacy capture on interface {interface}")
                    return
                else:
                    logger.warning(f"❌ Failed to verify legacy tcpdump process on interface {interface}")
                    continue

            except Exception as e:
                last_error = e
                logger.warning(f"❌ Failed to start legacy capture on interface {interface}: {str(e)}")
                continue

        # 如果所有接口都失败了
        error_msg = f"Failed to start legacy capture on all {len(prioritized_interfaces)} interfaces. Last error: {last_error}"
        logger.error(error_msg)
        raise Exception(error_msg)

    async def _stop_remote_capture(self):
        """停止远程MySQL抓包"""
        try:
            # 检查SSH连接是否有效
            if not self.ssh_client:
                logger.warning("SSH client is None, attempting to reconnect for cleanup")
                try:
                    # 尝试重新连接以进行清理
                    if self.current_server_config:
                        await self._connect_ssh_with_config()
                    else:
                        await self._connect_ssh_legacy()
                except Exception as e:
                    logger.error(f"Failed to reconnect SSH for cleanup: {e}")
                    # 如果无法重连，直接返回，不抛出异常
                    return

            # 测试SSH连接是否可用
            try:
                self.ssh_client.exec_command('echo test', timeout=5)
            except Exception as e:
                logger.warning(f"SSH connection test failed: {e}, attempting to reconnect")
                try:
                    if self.current_server_config:
                        await self._connect_ssh_with_config()
                    else:
                        await self._connect_ssh_legacy()
                except Exception as reconnect_error:
                    logger.error(f"Failed to reconnect SSH: {reconnect_error}")
                    return

            if self.tcpdump_pid:
                # 使用PID停止tcpdump
                kill_cmd = f"kill {self.tcpdump_pid}"
                stdin, stdout, stderr = self.ssh_client.exec_command(kill_cmd)
                await asyncio.sleep(1)
                logger.info(f"Stopped tcpdump process {self.tcpdump_pid}")
            else:
                # 如果没有PID，尝试杀死所有tcpdump进程
                kill_cmd = "pkill -f tcpdump"
                stdin, stdout, stderr = self.ssh_client.exec_command(kill_cmd)
                await asyncio.sleep(1)
                logger.info("Stopped all tcpdump processes")

        except Exception as e:
            logger.error(f"Failed to stop remote MySQL capture: {str(e)}")
            # 不再抛出异常，避免任务失败
            logger.warning("Continuing despite stop capture failure")

    async def _download_capture_file(self, remote_file: str, local_file: str):
        """下载抓包文件 - 带重试机制"""
        max_retries = 3
        retry_delay = 2

        for attempt in range(max_retries):
            try:
                logger.info(f"Downloading capture file (attempt {attempt + 1}/{max_retries}): {remote_file}")

                # 确保SSH连接有效
                if not await self._ensure_ssh_connection():
                    logger.error(f"Cannot establish SSH connection for download (attempt {attempt + 1})")
                    if attempt < max_retries - 1:
                        await asyncio.sleep(retry_delay)
                        continue
                    else:
                        return

                sftp = self.ssh_client.open_sftp()

                # 检查远程文件是否存在和大小
                try:
                    remote_stat = sftp.stat(remote_file)
                    remote_size = remote_stat.st_size
                    logger.info(f"Remote file size: {remote_size} bytes")

                    if remote_size <= 24:
                        logger.warning(f"Remote file too small ({remote_size} bytes), skipping download")
                        sftp.close()
                        return

                except FileNotFoundError:
                    logger.warning(f"Remote capture file not found: {remote_file}")
                    sftp.close()
                    if attempt < max_retries - 1:
                        await asyncio.sleep(retry_delay)
                        continue
                    else:
                        return

                # 下载文件
                await asyncio.get_event_loop().run_in_executor(
                    None, sftp.get, remote_file, local_file
                )

                # 验证下载的文件
                if os.path.exists(local_file):
                    local_size = os.path.getsize(local_file)
                    logger.info(f"Downloaded file size: {local_size} bytes")

                    if local_size > 24:
                        logger.info(f"Successfully downloaded MySQL capture file: {local_file}")

                        # 删除远程文件
                        try:
                            sftp.remove(remote_file)
                            logger.info(f"Removed remote file: {remote_file}")
                        except Exception as e:
                            logger.warning(f"Failed to remove remote file: {e}")

                        sftp.close()
                        return
                    else:
                        logger.warning(f"Downloaded file too small ({local_size} bytes), retrying...")
                        os.remove(local_file)  # 删除无效文件
                else:
                    logger.warning("Downloaded file does not exist locally")

                sftp.close()

                # 如果不是最后一次尝试，等待后重试
                if attempt < max_retries - 1:
                    logger.info(f"Retrying download in {retry_delay} seconds...")
                    await asyncio.sleep(retry_delay)

            except Exception as e:
                logger.error(f"Download attempt {attempt + 1} failed: {str(e)}")
                if attempt < max_retries - 1:
                    logger.info(f"Retrying download in {retry_delay} seconds...")
                    await asyncio.sleep(retry_delay)
                else:
                    logger.error("All download attempts failed")

    async def _ensure_ssh_connection(self) -> bool:
        """确保SSH连接有效"""
        try:
            if not self.ssh_client:
                logger.info("SSH client is None, establishing new connection")
                await self._connect_ssh_with_config()
                return True

            # 测试现有连接
            try:
                self.ssh_client.exec_command('echo test', timeout=5)
                return True
            except Exception as e:
                logger.warning(f"SSH connection test failed: {e}, reconnecting...")
                await self._connect_ssh_with_config()
                return True

        except Exception as e:
            logger.error(f"Failed to ensure SSH connection: {e}")
            return False

    async def _validate_capture_quality(self, capture_file: str) -> bool:
        """验证抓包质量 - 检查是否包含有效的MySQL协议数据"""
        try:
            if not os.path.exists(capture_file):
                logger.warning(f"Capture file does not exist: {capture_file}")
                return False

            file_size = os.path.getsize(capture_file)
            if file_size <= 24:  # 只有pcap文件头，没有数据包
                logger.warning(f"Capture file too small (only header): {file_size} bytes")
                return False

            # 使用tcpdump分析抓包文件内容
            try:
                # 检查是否包含TCP连接
                tcp_check_cmd = f"tcpdump -r {capture_file} -c 10 2>/dev/null | wc -l"
                process = await asyncio.create_subprocess_shell(
                    tcp_check_cmd,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                stdout, stderr = await process.communicate()

                packet_count = int(stdout.decode().strip()) if stdout else 0

                if packet_count == 0:
                    logger.warning(f"No packets found in capture file: {capture_file}")
                    return False

                # 检查是否包含MySQL端口的流量
                mysql_check_cmd = f"tcpdump -r {capture_file} -c 5 'tcp port 3308 or tcp port 3306' 2>/dev/null | wc -l"
                process = await asyncio.create_subprocess_shell(
                    mysql_check_cmd,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                stdout, stderr = await process.communicate()

                mysql_packet_count = int(stdout.decode().strip()) if stdout else 0

                if mysql_packet_count > 0:
                    logger.info(f"Capture quality validation passed: {file_size} bytes, {packet_count} packets, {mysql_packet_count} MySQL packets")
                    return True
                else:
                    logger.warning(f"No MySQL packets found in capture file: {packet_count} total packets")
                    return False

            except Exception as e:
                logger.warning(f"Could not analyze capture file content: {e}")
                # 如果无法分析内容，至少检查文件大小
                if file_size > 100:
                    logger.info(f"Capture quality validation passed (size-based): {file_size} bytes")
                    return True
                else:
                    return False

        except Exception as e:
            logger.error(f"Capture quality validation error: {str(e)}")
            return False

    async def _prepare_next_retry_strategy(self, mysql_host: str, mysql_port: int):
        """准备下一次重试的策略"""
        try:
            # 记录失败的策略
            if self.current_capture_strategy:
                self.failed_strategies.append({
                    'interface': self.current_capture_strategy.interface,
                    'filter': self.current_capture_strategy.filter_expression,
                    'attempt': self.current_retry_count
                })

            # 清理当前抓包状态
            await self._cleanup()

            # 等待一段时间再重试
            await asyncio.sleep(2)

            logger.info(f"Preparing MySQL retry attempt {self.current_retry_count + 1}/{self.max_retry_attempts}")

        except Exception as e:
            logger.error(f"Failed to prepare next retry strategy: {str(e)}")

    async def _cleanup(self):
        """清理资源"""
        try:
            if self.ssh_client:
                self.ssh_client.close()
                self.ssh_client = None

            self.tcpdump_pid = None

        except Exception as e:
            logger.error(f"Cleanup error: {str(e)}")

    async def _ensure_tcpdump_available(self) -> bool:
        """确保tcpdump在目标服务器上可用"""
        try:
            # 获取当前服务器配置
            server_config = self.current_server_config
            if not server_config:
                # 使用默认配置
                configs = await self.server_config_service.list_configs()
                server_config = configs[0] if configs else None

                if not server_config:
                    logger.warning("No server configuration available, using legacy config")
                    # 创建临时配置用于tcpdump检查
                    from models.server_config import ServerConfig
                    server_config = ServerConfig(
                        host="**************",
                        port=22,
                        username="root",
                        password="QZ@1005#1005"
                    )

            # 使用tcpdump安装服务检查并安装
            success = await self.tcpdump_installer_service.ensure_tcpdump_available(server_config)

            if success:
                logger.info("tcpdump is available for MySQL packet capture")
            else:
                logger.error("Failed to ensure tcpdump availability for MySQL")

            return success

        except Exception as e:
            logger.error(f"Error ensuring tcpdump availability: {str(e)}")
            return False

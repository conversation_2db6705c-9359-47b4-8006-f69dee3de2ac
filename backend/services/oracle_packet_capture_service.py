"""
Oracle数据包捕获服务 - 本地抓包
"""

import asyncio
import logging
import os
from typing import Optional, Dict, Any
from datetime import datetime

from services.local_tcpdump_service import LocalTcpdumpService
from utils.local_network_utils import LocalNetworkUtils
from services.capture_file_service import capture_file_service
from models.capture_file import CaptureFileCreate

logger = logging.getLogger(__name__)

class OraclePacketCaptureService:
    """Oracle数据包捕获服务 - 本地抓包"""

    def __init__(self):
        self.is_capturing = False
        self.current_file: Optional[str] = None
        self.oracle_port = 1521  # Oracle默认端口
        self.capture_dir = "captures"  # 本地抓包文件目录

        # 确保本地captures目录存在
        os.makedirs(self.capture_dir, exist_ok=True)

        # 本地tcpdump服务
        self.local_tcpdump_service = LocalTcpdumpService(self.capture_dir)

    async def start_local_capture(self, oracle_host: str = "**************", oracle_port: int = 1521, step_identifier: str = None) -> str:
        """
        启动本地Oracle数据包捕获
        直接监控指定主机和端口的流量

        Args:
            oracle_host: Oracle主机地址
            oracle_port: Oracle端口
            step_identifier: 步骤标识符，用于生成唯一的文件名

        Returns:
            str: 抓包文件路径
        """
        logger.info(f"Starting Oracle packet capture - Target server: {oracle_host}:{oracle_port}, Step: {step_identifier}")

        try:
            # 保存当前抓包配置，用于后续保存到数据库
            self._current_target_host = oracle_host
            self._current_target_port = oracle_port
            self._current_step_identifier = step_identifier

            # 获取最佳本地网络接口
            interface = LocalNetworkUtils.get_best_local_interface()
            logger.info(f"Selected local interface: {interface}")

            # 构建过滤表达式 - 监控指定主机和端口的流量
            filter_expr = f"host {oracle_host} and tcp port {oracle_port}"
            logger.info(f"Monitoring traffic: {filter_expr}")

            # 启动本地tcpdump，传入步骤标识符
            capture_file = await self.local_tcpdump_service.start_capture(
                database_type="oracle",
                target_port=oracle_port,
                interface=interface,
                filter_expression=filter_expr,
                step_identifier=step_identifier
            )

            self.current_file = capture_file
            self.is_capturing = True

            logger.info(f"Oracle packet capture started successfully: {capture_file}")
            logger.info(f"Monitoring traffic to/from {oracle_host}:{oracle_port}")
            return capture_file

        except Exception as e:
            logger.error(f"Failed to start Oracle packet capture: {str(e)}")
            raise Exception(f"Oracle抓包启动失败: {str(e)}")

    async def start_smart_capture(self, target_host: str = None, target_port: int = None, server_config_id: int = None, step_identifier: str = None) -> str:
        """启动智能Oracle数据包捕获（兼容性方法，实际使用本地抓包）"""
        oracle_host = target_host or "**************"
        oracle_port = target_port or 1521
        logger.info(f"Oracle智能抓包（本地模式） - Host: {oracle_host}, Port: {oracle_port}, Step: {step_identifier}")
        return await self.start_capture(target_host=oracle_host, target_port=oracle_port, server_config_id=server_config_id, step_identifier=step_identifier)

    async def start_capture(self, target_host: str = None, target_port: int = None, server_config_id: int = None, step_identifier: str = None) -> str:
        """启动Oracle数据包捕获"""
        oracle_host = target_host or "**************"  # 默认使用远程Oracle服务器
        oracle_port = target_port or 1521

        logger.info(f"Oracle抓包配置 - Host: {oracle_host}, Port: {oracle_port}, Step: {step_identifier}")

        # 如果检测到抓包状态异常，强制重置
        if self.is_capturing:
            logger.warning("检测到Oracle抓包状态异常，强制重置状态")
            await self.stop_capture()

        # 启动抓包
        try:
            logger.info(f"Starting packet capture to monitor Oracle server: {oracle_host}:{oracle_port}")
            return await self.start_local_capture(oracle_host, oracle_port, step_identifier)
        except Exception as e:
            logger.error(f"Oracle capture failed: {str(e)}")
            raise Exception(f"Oracle抓包启动失败: {str(e)}")

    async def stop_capture(self) -> str:
        """停止Oracle数据包捕获"""
        try:
            if not self.is_capturing:
                logger.warning("Oracle packet capture is not running")
                return self.current_file or ""

            # 停止本地tcpdump服务
            if self.local_tcpdump_service.is_capturing:
                capture_file = await self.local_tcpdump_service.stop_capture()
                logger.info(f"Local Oracle capture stopped: {capture_file}")

                # 检查文件大小，如果太小则删除
                if capture_file and os.path.exists(capture_file):
                    file_size = os.path.getsize(capture_file)
                    if file_size <= 24:  # pcap文件头大小，表示没有实际数据包
                        logger.warning(f"Oracle capture file is empty (only {file_size} bytes), deleting it")
                        try:
                            os.remove(capture_file)
                            logger.info(f"Deleted empty capture file: {capture_file}")
                        except Exception as e:
                            logger.error(f"Failed to delete empty capture file: {e}")
                        capture_file = None
                    else:
                        # 保存文件信息到数据库
                        try:
                            await self._save_capture_file_to_db(capture_file)
                            logger.info(f"Oracle capture file saved to database: {capture_file}")
                        except Exception as e:
                            logger.error(f"Failed to save capture file to database: {e}")
                            # 如果保存到数据库失败，不影响返回文件路径

                self.is_capturing = False
                self.current_file = None
                
                # 返回相对路径（不包含captures/前缀），这样下载API就能正确处理
                if capture_file:
                    filename = os.path.basename(capture_file)
                    logger.info(f"Returning filename for download: {filename}")
                    return filename
                else:
                    return ""
            else:
                logger.warning("No local capture process found")
                self.is_capturing = False
                self.current_file = None
                return ""

        except Exception as e:
            logger.error(f"Failed to stop Oracle capture: {str(e)}")
            self.is_capturing = False
            self.current_file = None
            raise

    def get_capture_status(self) -> Dict[str, Any]:
        """获取抓包状态"""
        return {
            'is_capturing': self.is_capturing,
            'current_file': self.current_file,
            'local_capture': True
        }

    def is_capturing_active(self) -> bool:
        """检查是否正在抓包"""
        return self.is_capturing

    async def _save_capture_file_to_db(self, capture_file_path: str):
        """保存抓包文件信息到数据库"""
        try:
            if not os.path.exists(capture_file_path):
                logger.warning(f"Capture file does not exist: {capture_file_path}")
                return

            file_size = os.path.getsize(capture_file_path)
            filename = os.path.basename(capture_file_path)

            # 从当前抓包配置中获取目标主机和端口信息
            target_host = getattr(self, '_current_target_host', '**************')
            target_port = getattr(self, '_current_target_port', 1521)

            # 使用统一路径管理器获取相对路径，确保数据库中存储的是相对路径
            from utils.path_manager import path_manager
            relative_file_path = path_manager.get_relative_capture_path(filename)

            capture_data = CaptureFileCreate(
                filename=filename,
                file_path=relative_file_path,  # 使用相对路径
                file_size=file_size,
                database_type="oracle",
                target_host=target_host,
                target_port=target_port,
                description=f"Oracle packet capture from {target_host}:{target_port}"
            )

            file_id = await capture_file_service.save_capture_file(capture_data)
            logger.info(f"Saved Oracle capture file to database with ID: {file_id}")

        except Exception as e:
            logger.error(f"Failed to save capture file to database: {e}")
            # 不抛出异常，避免影响抓包流程

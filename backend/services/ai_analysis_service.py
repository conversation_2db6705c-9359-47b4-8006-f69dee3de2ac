"""
AI分析服务 - 用于分析数据库抓包文件
"""

import os
import logging
import subprocess
import re
from typing import Dict, Any, List
from openai import OpenAI

logger = logging.getLogger(__name__)

class AIAnalysisService:
    """AI分析服务"""
    
    def __init__(self):
        # 初始化DeepSeek客户端
        self.client = OpenAI(
            api_key="sk-b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8",
            base_url="https://api.deepseek.com"
        )
    
    def _call_deepseek(self, prompt: str) -> str:
        """调用DeepSeek API"""
        try:
            response = self.client.chat.completions.create(
                model="deepseek-chat",
                messages=[
                    {"role": "system", "content": "你是一个专业的数据库和网络协议分析专家，能够分析数据包内容并提取SQL语句。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=4096
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            logger.error(f"DeepSeek API call failed: {str(e)}")
            raise
    
    async def analyze_oracle_packets(self, capture_file: str) -> Dict[str, Any]:
        """分析Oracle抓包文件"""
        try:
            logger.info(f"Starting Oracle packet analysis for: {capture_file}")
            
            # 检查文件是否存在
            if not os.path.exists(capture_file):
                return {
                    'success': False,
                    'error': f'Capture file not found: {capture_file}'
                }
            
            # 获取文件信息
            file_size = os.path.getsize(capture_file)
            logger.info(f"Oracle capture file size: {file_size} bytes")
            
            # 检查是否为空包（24字节或更小）
            if file_size <= 24:
                return {
                    'success': False,
                    'error': 'Capture file is empty or too small (24 bytes or less)',
                    'file_size': file_size
                }
            
            # 使用tcpdump分析包内容
            packet_analysis = await self._analyze_packets_with_tcpdump(capture_file)
            
            if not packet_analysis['success']:
                return packet_analysis
            
            # 提取SQL语句
            sql_statements = await self._extract_sql_statements(packet_analysis['packet_data'])
            
            # 生成分析摘要
            analysis_summary = await self._generate_analysis_summary(
                sql_statements, 
                packet_analysis['packet_count'],
                file_size
            )
            
            return {
                'success': True,
                'sql_statements': sql_statements,
                'analysis_summary': analysis_summary,
                'packet_count': packet_analysis['packet_count'],
                'file_size': file_size
            }
            
        except Exception as e:
            logger.error(f"Oracle packet analysis failed: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def _analyze_packets_with_tcpdump(self, capture_file: str) -> Dict[str, Any]:
        """使用tcpdump分析数据包"""
        try:
            # 使用tcpdump读取包内容（ASCII格式）
            cmd = ['tcpdump', '-r', capture_file, '-A', '-n']
            
            result = subprocess.run(
                cmd, 
                capture_output=True, 
                text=True, 
                timeout=30
            )
            
            if result.returncode != 0:
                logger.error(f"tcpdump failed: {result.stderr}")
                return {
                    'success': False,
                    'error': f'tcpdump analysis failed: {result.stderr}'
                }
            
            packet_data = result.stdout
            
            # 统计数据包数量
            packet_count = len([line for line in packet_data.split('\n') if 'IP ' in line and '>' in line])
            
            logger.info(f"Analyzed {packet_count} packets from Oracle capture")
            
            return {
                'success': True,
                'packet_data': packet_data,
                'packet_count': packet_count
            }
            
        except subprocess.TimeoutExpired:
            return {
                'success': False,
                'error': 'tcpdump analysis timed out'
            }
        except Exception as e:
            logger.error(f"tcpdump analysis error: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def _extract_sql_statements(self, packet_data: str) -> List[str]:
        """从数据包数据中提取SQL语句"""
        try:
            sql_statements = []
            
            # 使用正则表达式查找常见的SQL关键字
            sql_patterns = [
                r'SELECT\s+.*?FROM\s+\w+',
                r'INSERT\s+INTO\s+\w+.*?VALUES',
                r'UPDATE\s+\w+\s+SET\s+.*?WHERE',
                r'DELETE\s+FROM\s+\w+.*?WHERE',
                r'CREATE\s+TABLE\s+\w+',
                r'DROP\s+TABLE\s+\w+',
                r'ALTER\s+TABLE\s+\w+',
                r'SHOW\s+\w+',
                r'DESC\s+\w+',
                r'DESCRIBE\s+\w+'
            ]
            
            # 在数据包内容中搜索SQL语句
            for line in packet_data.split('\n'):
                # 清理行内容，移除非打印字符
                clean_line = re.sub(r'[^\x20-\x7E]', ' ', line).strip()
                
                if clean_line:
                    for pattern in sql_patterns:
                        matches = re.findall(pattern, clean_line, re.IGNORECASE)
                        for match in matches:
                            # 清理和格式化SQL语句
                            sql = re.sub(r'\s+', ' ', match.strip())
                            if sql and len(sql) > 5:  # 过滤太短的匹配
                                sql_statements.append(sql)
            
            # 去重并排序
            sql_statements = list(set(sql_statements))
            sql_statements.sort()
            
            logger.info(f"Extracted {len(sql_statements)} SQL statements from Oracle packets")
            
            # 如果没有找到SQL语句，使用AI分析
            if not sql_statements:
                sql_statements = await self._ai_extract_sql_statements(packet_data)
            
            return sql_statements
            
        except Exception as e:
            logger.error(f"SQL extraction error: {str(e)}")
            return []
    
    async def _ai_extract_sql_statements(self, packet_data: str) -> List[str]:
        """使用AI从数据包中提取SQL语句"""
        try:
            # 截取数据包内容的前2000个字符用于AI分析
            sample_data = packet_data[:2000] if len(packet_data) > 2000 else packet_data
            
            prompt = f"""
请分析以下Oracle数据包内容，提取其中的SQL语句：

数据包内容：
{sample_data}

请找出所有可能的SQL语句，包括：
- SELECT查询
- INSERT插入
- UPDATE更新  
- DELETE删除
- CREATE/DROP/ALTER DDL语句
- Oracle特有的语句

请只返回找到的SQL语句，每行一个，不要添加额外的解释。
如果没有找到SQL语句，请返回"未发现SQL语句"。
            """
            
            result = self._call_deepseek(prompt)
            
            # 解析AI返回的结果
            sql_statements = []
            for line in result.split('\n'):
                line = line.strip()
                if line and not line.startswith('未发现') and not line.startswith('没有'):
                    # 移除可能的序号或标记
                    line = re.sub(r'^\d+\.\s*', '', line)
                    line = re.sub(r'^-\s*', '', line)
                    if len(line) > 5:
                        sql_statements.append(line)
            
            logger.info(f"AI extracted {len(sql_statements)} SQL statements")
            return sql_statements
            
        except Exception as e:
            logger.error(f"AI SQL extraction error: {str(e)}")
            return []
    
    async def _generate_analysis_summary(self, sql_statements: List[str], packet_count: int, file_size: int) -> str:
        """生成分析摘要"""
        try:
            if not sql_statements:
                return f"分析了 {packet_count} 个数据包（文件大小：{file_size} 字节），未发现SQL语句。"
            
            prompt = f"""
请为以下Oracle数据包分析结果生成一个简洁的摘要：

数据包数量：{packet_count}
文件大小：{file_size} 字节
发现的SQL语句：
{chr(10).join(f"- {sql}" for sql in sql_statements)}

请生成一个简洁的中文摘要，包括：
1. 数据包基本信息
2. SQL语句类型和数量统计
3. 主要操作类型

摘要应该在100字以内。
            """
            
            summary = self._call_deepseek(prompt)
            return summary
            
        except Exception as e:
            logger.error(f"Summary generation error: {str(e)}")
            return f"分析了 {packet_count} 个数据包，发现 {len(sql_statements)} 条SQL语句。"

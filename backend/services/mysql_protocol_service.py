import asyncio
from typing import Dict, Any, Optional
import logging
from utils.config import Config
from services.mysql_service import MySQLService

logger = logging.getLogger(__name__)

class MySQLProtocolService:
    """MySQL协议命令服务 - 通过SQL实现协议命令功能"""

    # 支持的所有MySQL协议命令
    SUPPORTED_COMMANDS = {
        'REFRESH': 7,               # 刷新缓存（如权限、日志）
        'SHUTDOWN': 8,              # 关闭服务器（需权限）
        'STATISTICS': 9,            # 获取服务器状态信息
        'PROCESS_LIST': 10,         # 查看活动线程
        'KILL_THREAD': 12,          # 终止指定线程
        'CHANGE_USER': 17,          # 切换连接用户
        'SEND_BINLOG': 18,          # 主从复制中传输binlog
        'SEND_TABLE': 19,           # 复制中传输表结构
        'SLAVE_CONNECT': 20,        # 从库连接主库
        'REGISTER_SLAVE': 21,       # 从库向主库注册自身信息
        'SEND_BLOB': 24,            # 发送大对象数据
        'SET_OPTION': 27,           # 设置客户端选项
        'FETCH_DATA': 28,           # 获取预处理语句的结果集
        'SEND_BINLOG_GTID': 30,     # 基于GTID的binlog传输
        'RESET_CONNECTION': 31,     # 重置会话状态
        'NATIVE_CLONING': 32,       # 本地克隆插件
    }

    def __init__(self, database_config: Optional[Dict[str, Any]] = None):
        self.mysql_service: Optional[MySQLService] = None
        self.database_config = database_config

    async def _get_mysql_service(self) -> MySQLService:
        """获取MySQL服务实例"""
        if self.mysql_service is None:
            if self.database_config:
                self.mysql_service = MySQLService(**self.database_config)
            else:
                mysql_config = Config.get_mysql_config()
                self.mysql_service = MySQLService(**mysql_config)
        return self.mysql_service
    
    async def connect(self) -> bool:
        """建立MySQL连接"""
        try:
            mysql_service = await self._get_mysql_service()
            is_connected = await mysql_service.check_connection()
            if is_connected:
                logger.info(f"MySQL protocol service connected: {Config.MYSQL_HOST}:{Config.MYSQL_PORT}")
                return True
            else:
                logger.error("Failed to connect to MySQL")
                return False
        except Exception as e:
            logger.error(f"Failed to connect to MySQL: {str(e)}")
            return False
    
    async def send_command(self, command_name: str, **kwargs) -> Dict[str, Any]:
        """发送MySQL协议命令（通过SQL实现）"""
        try:
            mysql_service = await self._get_mysql_service()
            logger.info(f"Executing protocol command: {command_name}")

            # 根据命令名称执行对应的SQL
            cmd = command_name.upper()

            if cmd == 'REFRESH' or cmd == 'REFRESH_PRIVILEGES':
                result = await mysql_service.execute_query("FLUSH PRIVILEGES")
            elif cmd == 'REFRESH_LOGS':
                result = await mysql_service.execute_query("FLUSH LOGS")
            elif cmd == 'REFRESH_ALL':
                result = await mysql_service.execute_query("FLUSH PRIVILEGES; FLUSH LOGS; FLUSH TABLES")
            elif cmd == 'SHUTDOWN':
                result = await mysql_service.execute_query("SHUTDOWN")
            elif cmd == 'STATISTICS':
                result = await mysql_service.execute_query("SHOW STATUS")
            elif cmd == 'PROCESS_LIST':
                result = await mysql_service.execute_query("SHOW PROCESSLIST")
            elif cmd == 'KILL_THREAD':
                thread_id = kwargs.get('thread_id', 0)
                result = await mysql_service.execute_query(f"KILL {thread_id}")
            elif cmd == 'CHANGE_USER':
                # 切换用户需要重新认证，这里返回提示信息
                result = {"type": "info", "message": "CHANGE_USER command requires reconnection with new credentials"}
            elif cmd == 'SEND_BINLOG':
                result = await mysql_service.execute_query("SHOW BINARY LOGS")
            elif cmd == 'SEND_TABLE':
                # 显示表结构信息
                table_name = kwargs.get('table_name', 'information_schema.tables')
                result = await mysql_service.execute_query(f"DESCRIBE {table_name}")
            elif cmd == 'SLAVE_CONNECT':
                result = await mysql_service.execute_query("SHOW SLAVE STATUS")
            elif cmd == 'REGISTER_SLAVE':
                result = await mysql_service.execute_query("SHOW SLAVE HOSTS")
            elif cmd == 'SEND_BLOB':
                # 显示大对象相关信息
                result = await mysql_service.execute_query("SHOW VARIABLES LIKE '%blob%'")
            elif cmd == 'SET_OPTION':
                option = kwargs.get('option', 'autocommit=1')
                result = await mysql_service.execute_query(f"SET {option}")
            elif cmd == 'FETCH_DATA':
                # 获取预处理语句信息
                result = await mysql_service.execute_query("SHOW STATUS LIKE 'Prepared_stmt%'")
            elif cmd == 'SEND_BINLOG_GTID':
                result = await mysql_service.execute_query("SHOW VARIABLES LIKE '%gtid%'")
            elif cmd == 'RESET_CONNECTION':
                result = await mysql_service.execute_query("RESET CONNECTION")
            elif cmd == 'NATIVE_CLONING':
                result = await mysql_service.execute_query("SHOW PLUGINS LIKE '%clone%'")
            else:
                raise Exception(f"Unsupported command: {command_name}")

            # 转换结果格式
            return {
                'success': True,
                'command': command_name,
                'message': f'{command_name} command executed successfully',
                'response_type': 'SQL_RESULT',
                'sql_result': result
            }

        except Exception as e:
            logger.error(f"Failed to execute command {command_name}: {str(e)}")
            return {
                'success': False,
                'command': command_name,
                'error_message': str(e),
                'response_type': 'ERROR'
            }
    
    async def close(self):
        """关闭连接"""
        if self.mysql_service:
            await self.mysql_service.close()
            self.mysql_service = None
            logger.info("MySQL protocol service closed")

    # 便捷方法 - 支持所有16个协议命令
    async def refresh_privileges(self) -> Dict[str, Any]:
        """刷新权限 (协议号: 7)"""
        return await self.send_command('REFRESH_PRIVILEGES')

    async def refresh_logs(self) -> Dict[str, Any]:
        """刷新日志 (协议号: 7)"""
        return await self.send_command('REFRESH_LOGS')

    async def refresh_all(self) -> Dict[str, Any]:
        """刷新所有缓存 (协议号: 7)"""
        return await self.send_command('REFRESH_ALL')

    async def shutdown_server(self) -> Dict[str, Any]:
        """关闭服务器 (协议号: 8)"""
        return await self.send_command('SHUTDOWN')

    async def get_statistics(self) -> Dict[str, Any]:
        """获取服务器统计信息 (协议号: 9)"""
        return await self.send_command('STATISTICS')

    async def get_process_list(self) -> Dict[str, Any]:
        """获取进程列表 (协议号: 10)"""
        return await self.send_command('PROCESS_LIST')

    async def kill_thread(self, thread_id: int) -> Dict[str, Any]:
        """终止指定线程 (协议号: 12)"""
        return await self.send_command('KILL_THREAD', thread_id=thread_id)

    async def change_user(self) -> Dict[str, Any]:
        """切换连接用户 (协议号: 17)"""
        return await self.send_command('CHANGE_USER')

    async def send_binlog(self) -> Dict[str, Any]:
        """主从复制中传输binlog (协议号: 18)"""
        return await self.send_command('SEND_BINLOG')

    async def send_table(self, table_name: str = 'information_schema.tables') -> Dict[str, Any]:
        """复制中传输表结构 (协议号: 19)"""
        return await self.send_command('SEND_TABLE', table_name=table_name)

    async def slave_connect(self) -> Dict[str, Any]:
        """从库连接主库 (协议号: 20)"""
        return await self.send_command('SLAVE_CONNECT')

    async def register_slave(self) -> Dict[str, Any]:
        """从库向主库注册自身信息 (协议号: 21)"""
        return await self.send_command('REGISTER_SLAVE')

    async def send_blob(self) -> Dict[str, Any]:
        """发送大对象数据 (协议号: 24)"""
        return await self.send_command('SEND_BLOB')

    async def set_option(self, option: str = 'autocommit=1') -> Dict[str, Any]:
        """设置客户端选项 (协议号: 27)"""
        return await self.send_command('SET_OPTION', option=option)

    async def fetch_data(self) -> Dict[str, Any]:
        """获取预处理语句的结果集 (协议号: 28)"""
        return await self.send_command('FETCH_DATA')

    async def send_binlog_gtid(self) -> Dict[str, Any]:
        """基于GTID的binlog传输 (协议号: 30)"""
        return await self.send_command('SEND_BINLOG_GTID')

    async def reset_connection(self) -> Dict[str, Any]:
        """重置会话状态 (协议号: 31)"""
        return await self.send_command('RESET_CONNECTION')

    async def native_cloning(self) -> Dict[str, Any]:
        """本地克隆插件 (协议号: 32)"""
        return await self.send_command('NATIVE_CLONING')

    def get_supported_commands(self) -> Dict[str, int]:
        """获取支持的所有协议命令"""
        return self.SUPPORTED_COMMANDS.copy()

#!/usr/bin/env python3
"""
MySQL原生客户端命令服务
在服务器上执行真正的MySQL客户端命令（如mysqladmin、mysql等），并抓取网络数据包
"""

import asyncio
import logging
import time
import os
from typing import Dict, Any, Optional
import paramiko
from scp import SCPClient

logger = logging.getLogger(__name__)

class MySQLNativeCommandService:
    """MySQL原生客户端命令服务"""
    
    def __init__(self, mysql_host: str = "**************", mysql_port: int = 3306, 
                 mysql_user: str = "root", mysql_password: str = "123456",
                 ssh_host: str = "**************", ssh_user: str = "root", 
                 ssh_password: str = "QZ@1005#1005"):
        self.mysql_host = mysql_host
        self.mysql_port = mysql_port
        self.mysql_user = mysql_user
        self.mysql_password = mysql_password
        self.ssh_host = ssh_host
        self.ssh_user = ssh_user
        self.ssh_password = ssh_password
        self.ssh_client = None
        
        # MySQL协议命令映射到实际的客户端命令
        self.protocol_commands = {
            'REFRESH': self._refresh_command,
            'SHUTDOWN': self._shutdown_command,
            'STATISTICS': self._statistics_command,
            'PROCESS_LIST': self._process_list_command,
            'KILL_THREAD': self._kill_thread_command,
            'CHANGE_USER': self._change_user_command,
            'SEND_BINLOG': self._send_binlog_command,
            'SEND_TABLE': self._send_table_command,
            'SLAVE_CONNECT': self._slave_connect_command,
            'REGISTER_SLAVE': self._register_slave_command,
            'SEND_BLOB': self._send_blob_command,
            'SET_OPTION': self._set_option_command,
            'FETCH_DATA': self._fetch_data_command,
            'SEND_BINLOG_GTID': self._send_binlog_gtid_command,
            'RESET_CONNECTION': self._reset_connection_command,
            'NATIVE_CLONING': self._native_cloning_command,
        }
    
    async def connect_ssh(self) -> bool:
        """连接到SSH服务器"""
        try:
            self.ssh_client = paramiko.SSHClient()
            self.ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            self.ssh_client.connect(
                hostname=self.ssh_host,
                username=self.ssh_user,
                password=self.ssh_password,
                timeout=10
            )
            logger.info(f"SSH连接成功: {self.ssh_host}")
            return True
        except Exception as e:
            logger.error(f"SSH连接失败: {str(e)}")
            return False
    
    async def execute_protocol_command(self, command_name: str, **kwargs) -> Dict[str, Any]:
        """执行MySQL协议命令"""
        if not self.ssh_client:
            if not await self.connect_ssh():
                return {
                    'success': False,
                    'command': command_name,
                    'error_message': 'SSH连接失败',
                    'response_type': 'ERROR'
                }
        
        command_func = self.protocol_commands.get(command_name.upper())
        if not command_func:
            return {
                'success': False,
                'command': command_name,
                'error_message': f'不支持的协议命令: {command_name}',
                'response_type': 'ERROR'
            }
        
        try:
            # 生成唯一的抓包文件名
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            pcap_file = f"/tmp/mysql_protocol_{command_name.lower()}_{timestamp}.pcap"
            
            # 启动抓包
            await self._start_packet_capture(pcap_file)

            # 等待抓包启动
            await asyncio.sleep(3)

            # 执行命令
            result = await command_func(**kwargs)

            # 等待数据包传输完成
            await asyncio.sleep(3)

            # 停止抓包
            await self._stop_packet_capture()

            # 等待文件写入完成
            await asyncio.sleep(1)
            
            # 下载抓包文件
            local_pcap_file = await self._download_pcap_file(pcap_file)
            
            # 清理远程文件
            await self._cleanup_remote_file(pcap_file)
            
            result['pcap_file'] = local_pcap_file
            result['command'] = command_name
            result['response_type'] = 'NATIVE_COMMAND'
            
            return result
            
        except Exception as e:
            logger.error(f"执行协议命令失败: {str(e)}")
            return {
                'success': False,
                'command': command_name,
                'error_message': str(e),
                'response_type': 'ERROR'
            }
    
    async def _start_packet_capture(self, pcap_file: str):
        """启动数据包捕获"""
        # 先检查可用的网络接口
        check_interfaces_cmd = "ip addr show | grep -E '^[0-9]+:' | awk '{print $2}' | sed 's/:$//'"
        stdin, stdout, stderr = self.ssh_client.exec_command(check_interfaces_cmd)
        interfaces = stdout.read().decode('utf-8').strip().split('\n')
        logger.info(f"可用网络接口: {interfaces}")

        # 优先使用lo回环接口（因为MySQL连接是本地的），然后是ens3，避免使用any
        if 'lo' in interfaces:
            interface = 'lo'
        elif 'ens3' in interfaces:
            interface = 'ens3'
        elif 'docker0' in interfaces:
            interface = 'docker0'
        elif 'eth0' in interfaces:
            interface = 'eth0'
        else:
            # 如果没有找到合适的接口，使用第一个可用接口
            interface = interfaces[0] if interfaces else 'lo'

        # 使用更详细的抓包命令，包含主机过滤
        capture_cmd = f"nohup tcpdump -i {interface} -w {pcap_file} -s 0 'host {self.mysql_host} and port {self.mysql_port}' > /tmp/tcpdump.log 2>&1 &"
        stdin, stdout, stderr = self.ssh_client.exec_command(capture_cmd)

        # 等待更长时间确保tcpdump启动
        await asyncio.sleep(2)

        # 检查tcpdump是否成功启动
        check_cmd = "pgrep -f tcpdump"
        stdin, stdout, stderr = self.ssh_client.exec_command(check_cmd)
        tcpdump_pids = stdout.read().decode('utf-8').strip()

        if tcpdump_pids:
            logger.info(f"启动抓包成功: {pcap_file}, 接口: {interface}, PID: {tcpdump_pids}")
        else:
            logger.error(f"启动抓包失败: {pcap_file}")
            # 检查错误日志
            stdin, stdout, stderr = self.ssh_client.exec_command("cat /tmp/tcpdump.log")
            error_log = stdout.read().decode('utf-8')
            logger.error(f"tcpdump错误日志: {error_log}")
    
    async def _stop_packet_capture(self):
        """停止数据包捕获"""
        # 更精确地停止tcpdump进程
        stop_cmd = "pkill -f 'tcpdump.*port.*3306'"
        stdin, stdout, stderr = self.ssh_client.exec_command(stop_cmd)
        await asyncio.sleep(1)

        # 确认tcpdump已停止
        check_cmd = "pgrep -f tcpdump"
        stdin, stdout, stderr = self.ssh_client.exec_command(check_cmd)
        remaining_pids = stdout.read().decode('utf-8').strip()

        if remaining_pids:
            logger.warning(f"仍有tcpdump进程运行: {remaining_pids}")
            # 强制杀死
            force_stop_cmd = f"kill -9 {remaining_pids}"
            stdin, stdout, stderr = self.ssh_client.exec_command(force_stop_cmd)

        logger.info("停止抓包完成")
    
    async def _download_pcap_file(self, remote_file: str) -> str:
        """下载抓包文件到本地"""
        try:
            # 确保本地captures目录存在
            local_dir = "backend/captures"
            os.makedirs(local_dir, exist_ok=True)
            
            # 生成本地文件名
            filename = os.path.basename(remote_file)
            local_file = os.path.join(local_dir, filename)
            
            # 使用SCP下载文件
            with SCPClient(self.ssh_client.get_transport()) as scp:
                scp.get(remote_file, local_file)
            
            logger.info(f"抓包文件下载成功: {local_file}")
            return filename
            
        except Exception as e:
            logger.error(f"下载抓包文件失败: {str(e)}")
            return None
    
    async def _cleanup_remote_file(self, remote_file: str):
        """清理远程文件"""
        try:
            cleanup_cmd = f"rm -f {remote_file}"
            stdin, stdout, stderr = self.ssh_client.exec_command(cleanup_cmd)
        except Exception as e:
            logger.warning(f"清理远程文件失败: {str(e)}")
    
    async def _execute_mysql_command(self, command: str) -> Dict[str, Any]:
        """执行MySQL命令并返回结果"""
        try:
            stdin, stdout, stderr = self.ssh_client.exec_command(command)
            
            # 等待命令执行完成
            exit_status = stdout.channel.recv_exit_status()
            
            stdout_data = stdout.read().decode('utf-8')
            stderr_data = stderr.read().decode('utf-8')
            
            if exit_status == 0:
                return {
                    'success': True,
                    'output': stdout_data,
                    'exit_code': exit_status
                }
            else:
                return {
                    'success': False,
                    'output': stdout_data,
                    'error': stderr_data,
                    'exit_code': exit_status
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'exit_code': -1
            }
    
    # 协议命令实现
    async def _refresh_command(self, **kwargs) -> Dict[str, Any]:
        """Refresh命令 (协议号: 7) - 刷新缓存"""
        cmd = f"mysqladmin -h {self.mysql_host} -P {self.mysql_port} -u {self.mysql_user} -p{self.mysql_password} refresh"
        result = await self._execute_mysql_command(cmd)
        result['protocol_number'] = 7
        result['description'] = '刷新缓存（如权限、日志）'
        return result
    
    async def _shutdown_command(self, **kwargs) -> Dict[str, Any]:
        """Shutdown命令 (协议号: 8) - 关闭服务器"""
        cmd = f"mysqladmin -h {self.mysql_host} -P {self.mysql_port} -u {self.mysql_user} -p{self.mysql_password} shutdown"
        result = await self._execute_mysql_command(cmd)
        result['protocol_number'] = 8
        result['description'] = '关闭服务器（需权限）'
        result['warning'] = '⚠️ 危险命令：将关闭MySQL服务器'
        return result
    
    async def _statistics_command(self, **kwargs) -> Dict[str, Any]:
        """Statistics命令 (协议号: 9) - 获取服务器状态"""
        cmd = f"mysqladmin -h {self.mysql_host} -P {self.mysql_port} -u {self.mysql_user} -p{self.mysql_password} status"
        result = await self._execute_mysql_command(cmd)
        result['protocol_number'] = 9
        result['description'] = '获取服务器状态信息'
        return result
    
    async def _process_list_command(self, **kwargs) -> Dict[str, Any]:
        """Process List命令 (协议号: 10) - 查看活动线程"""
        cmd = f"mysqladmin -h {self.mysql_host} -P {self.mysql_port} -u {self.mysql_user} -p{self.mysql_password} processlist"
        result = await self._execute_mysql_command(cmd)
        result['protocol_number'] = 10
        result['description'] = '查看活动线程（等价于 SHOW PROCESSLIST）'
        return result
    
    async def _kill_thread_command(self, thread_id: int = None, **kwargs) -> Dict[str, Any]:
        """Kill Thread命令 (协议号: 12) - 终止指定线程"""
        if not thread_id:
            return {
                'success': False,
                'error': '需要指定线程ID',
                'protocol_number': 12
            }
        
        cmd = f"mysqladmin -h {self.mysql_host} -P {self.mysql_port} -u {self.mysql_user} -p{self.mysql_password} kill {thread_id}"
        result = await self._execute_mysql_command(cmd)
        result['protocol_number'] = 12
        result['description'] = f'终止指定线程（线程ID: {thread_id}）'
        result['warning'] = f'⚠️ 危险命令：将终止线程 {thread_id}'
        return result
    
    async def _change_user_command(self, **kwargs) -> Dict[str, Any]:
        """Change User命令 (协议号: 17) - 切换连接用户"""
        # 使用mysql客户端模拟用户切换
        cmd = f"mysql -h {self.mysql_host} -P {self.mysql_port} -u {self.mysql_user} -p{self.mysql_password} -e 'SELECT USER(), CONNECTION_ID();'"
        result = await self._execute_mysql_command(cmd)
        result['protocol_number'] = 17
        result['description'] = '切换连接用户（重新认证）'
        return result
    
    async def _send_binlog_command(self, **kwargs) -> Dict[str, Any]:
        """Send Binlog命令 (协议号: 18) - 传输binlog"""
        cmd = f"mysqladmin -h {self.mysql_host} -P {self.mysql_port} -u {self.mysql_user} -p{self.mysql_password} flush-logs"
        result = await self._execute_mysql_command(cmd)
        result['protocol_number'] = 18
        result['description'] = '主从复制中传输 binlog（传统方式）'
        return result
    
    async def _send_table_command(self, **kwargs) -> Dict[str, Any]:
        """Send Table命令 (协议号: 19) - 传输表结构"""
        cmd = f"mysql -h {self.mysql_host} -P {self.mysql_port} -u {self.mysql_user} -p{self.mysql_password} -e 'SHOW TABLES;'"
        result = await self._execute_mysql_command(cmd)
        result['protocol_number'] = 19
        result['description'] = '复制中传输表结构（已弃用，但协议仍支持）'
        return result
    
    async def _slave_connect_command(self, **kwargs) -> Dict[str, Any]:
        """Slave Connect命令 (协议号: 20) - 从库连接主库"""
        cmd = f"mysql -h {self.mysql_host} -P {self.mysql_port} -u {self.mysql_user} -p{self.mysql_password} -e 'SHOW SLAVE STATUS\\G'"
        result = await self._execute_mysql_command(cmd)
        result['protocol_number'] = 20
        result['description'] = '从库连接主库（用于复制链路）'
        return result
    
    async def _register_slave_command(self, **kwargs) -> Dict[str, Any]:
        """Register Slave命令 (协议号: 21) - 注册从库"""
        cmd = f"mysql -h {self.mysql_host} -P {self.mysql_port} -u {self.mysql_user} -p{self.mysql_password} -e 'SHOW MASTER STATUS;'"
        result = await self._execute_mysql_command(cmd)
        result['protocol_number'] = 21
        result['description'] = '从库向主库注册自身信息'
        return result
    
    async def _send_blob_command(self, **kwargs) -> Dict[str, Any]:
        """Send BLOB命令 (协议号: 24) - 发送大对象数据"""
        cmd = f"mysql -h {self.mysql_host} -P {self.mysql_port} -u {self.mysql_user} -p{self.mysql_password} -e 'SELECT @@max_allowed_packet;'"
        result = await self._execute_mysql_command(cmd)
        result['protocol_number'] = 24
        result['description'] = '发送大对象数据（通常用于预处理语句）'
        return result
    
    async def _set_option_command(self, **kwargs) -> Dict[str, Any]:
        """Set Option命令 (协议号: 27) - 设置客户端选项"""
        cmd = f"mysql -h {self.mysql_host} -P {self.mysql_port} -u {self.mysql_user} -p{self.mysql_password} -e 'SET NAMES utf8mb4;'"
        result = await self._execute_mysql_command(cmd)
        result['protocol_number'] = 27
        result['description'] = '设置客户端选项（如字符集）'
        return result
    
    async def _fetch_data_command(self, **kwargs) -> Dict[str, Any]:
        """Fetch Data命令 (协议号: 28) - 获取预处理语句结果集"""
        cmd = f"mysql -h {self.mysql_host} -P {self.mysql_port} -u {self.mysql_user} -p{self.mysql_password} -e 'SELECT 1;'"
        result = await self._execute_mysql_command(cmd)
        result['protocol_number'] = 28
        result['description'] = '获取预处理语句的结果集'
        return result
    
    async def _send_binlog_gtid_command(self, **kwargs) -> Dict[str, Any]:
        """Send Binlog GTID命令 (协议号: 30) - 基于GTID的binlog传输"""
        cmd = f"mysql -h {self.mysql_host} -P {self.mysql_port} -u {self.mysql_user} -p{self.mysql_password} -e 'SELECT @@gtid_mode;'"
        result = await self._execute_mysql_command(cmd)
        result['protocol_number'] = 30
        result['description'] = '基于 GTID 的 binlog 传输（MySQL 5.6+）'
        return result
    
    async def _reset_connection_command(self, **kwargs) -> Dict[str, Any]:
        """Reset Connection命令 (协议号: 31) - 重置会话状态"""
        cmd = f"mysql -h {self.mysql_host} -P {self.mysql_port} -u {self.mysql_user} -p{self.mysql_password} -e 'RESET CONNECTION;'"
        result = await self._execute_mysql_command(cmd)
        result['protocol_number'] = 31
        result['description'] = '重置会话状态（MySQL 5.7+）'
        return result
    
    async def _native_cloning_command(self, **kwargs) -> Dict[str, Any]:
        """Native Cloning命令 (协议号: 32) - 本地克隆插件"""
        cmd = f"mysql -h {self.mysql_host} -P {self.mysql_port} -u {self.mysql_user} -p{self.mysql_password} -e 'SHOW PLUGINS LIKE \\'clone\\';'"
        result = await self._execute_mysql_command(cmd)
        result['protocol_number'] = 32
        result['description'] = '本地克隆插件（MySQL 8.0+，用于快速创建副本）'
        return result
    
    def close(self):
        """关闭SSH连接"""
        if self.ssh_client:
            self.ssh_client.close()
            self.ssh_client = None
            logger.info("SSH连接已关闭")

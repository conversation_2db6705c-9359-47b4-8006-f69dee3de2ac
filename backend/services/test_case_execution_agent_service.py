"""
测试用例执行Agent服务 - 使用LangChain agents实现测试用例的自动化执行和抓包
"""
import asyncio
import json
import logging
import os
import traceback
from typing import Dict, Any, List, Optional
from langchain.agents import AgentExecutor, create_openai_tools_agent
from langchain.prompts import Chat<PERSON>romptTemplate
from langchain_openai import ChatOpenAI
from langchain.schema import HumanMessage, SystemMessage

from tools.test_case_execution_tools import (
    TestCaseParserTool, SQLClassifierTool, ExecutionOrderTool,
    ExecutionPhase, SQLType, CaptureStrategy
)
from models.test_case_management import ExecutionResultEnum
from services.mysql_service import MySQLService
from services.mysql_local_packet_capture_service import MySQLLocalPacketCaptureService
from services.mysql_mcp_agent_service import mysql_mcp_agent_service
from services.mysql_async_capture_service import mysql_async_capture_service
# Oracle服务延迟导入，避免在不需要时触发Oracle客户端初始化
# from services.oracle_service import OracleService
# from services.oracle_packet_capture_service import OraclePacketCaptureService
# from services.oracle_mcp_agent_service import oracle_mcp_agent_service
# from services.oracle_async_capture_service import oracle_async_capture_service
from services.postgres_mcp_agent_service import postgres_mcp_agent_service
from services.postgres_async_capture_service import postgres_async_capture_service
from services.mongo_mcp_agent_service import mongo_mcp_agent_service
from services.mongo_async_capture_service import mongo_async_capture_service
from services.database_config_service import database_config_service
from utils.config import Config

logger = logging.getLogger(__name__)

class SmartPacketCaptureController:
    """智能抓包控制器 - 根据数据库类型选择合适的抓包agents"""

    def __init__(self):
        self.current_capture_file = None
        self.is_capturing = False
        self.current_database_type = None
        self.current_config_id = None
        self.task_id = None
        self.execution_id = None

    def set_execution_context(self, task_id: str = None, execution_id: str = None):
        """设置执行上下文"""
        self.task_id = task_id
        self.execution_id = execution_id

    async def execute_sql_with_capture(
        self,
        sql_statements: List[str],
        database_type: str,
        config_id: int,
        use_c_executor: bool = False
    ) -> Dict[str, Any]:
        """执行SQL语句并进行智能抓包"""
        try:
            logger.info(f"使用{database_type}智能抓包执行SQL: {len(sql_statements)}条语句")

            # 统一使用基础抓包，确保每个步骤生成一个pcap文件
            # 而不是每个SQL语句生成一个pcap文件
            if database_type.lower() == "mysql":
                return await self._execute_mysql_with_capture(sql_statements, config_id)
            elif database_type.lower() == "oracle":
                return await self._execute_oracle_with_capture(sql_statements, config_id)
            elif database_type.lower() == "postgresql":
                # 修改：使用基础抓包方法，确保每个步骤只生成一个pcap文件
                return await self._execute_postgresql_basic_capture(sql_statements, await database_config_service.get_config(config_id), use_c_executor)
            elif database_type.lower() == "mongodb":
                return await self._execute_mongo_with_capture(sql_statements, config_id, use_c_executor)
            elif database_type.lower() == "gaussdb":
                return await self._execute_gaussdb_with_capture(sql_statements, config_id, use_c_executor)
            else:
                # 对于其他数据库类型，使用基础抓包
                return await self._execute_basic_capture(sql_statements, database_type, config_id, use_c_executor)

        except Exception as e:
            logger.error(f"智能抓包执行失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "sql_results": [],
                "capture_file": None
            }

    async def _execute_mysql_with_capture(
        self,
        sql_statements: List[str],
        config_id: int
    ) -> Dict[str, Any]:
        """使用MySQL agents执行SQL并抓包"""
        try:
            logger.info("=== 开始MySQL智能抓包 - 一个步骤统一抓包 ===")
            logger.info(f"SQL语句数量: {len(sql_statements)}")
            logger.info(f"配置ID: {config_id}")
            
            # 配置MySQL MCP Agent连接
            await mysql_mcp_agent_service.configure_mysql_connection(config_id)

            # 启动抓包服务 - 在第一个SQL执行前启动
            from services.mysql_local_packet_capture_service import MySQLLocalPacketCaptureService
            packet_service = MySQLLocalPacketCaptureService()
            
            # 获取数据库配置
            config = await database_config_service.get_config(config_id)
            capture_file = await packet_service.start_capture(config.host, config.port)
            logger.info(f"MySQL抓包已启动: {capture_file}")
            
            # 等待抓包服务完全启动
            await asyncio.sleep(2)

            sql_results = []
            
            # 在同一个抓包会话中执行所有SQL语句
            for i, sql in enumerate(sql_statements):
                if sql.strip():
                    logger.info(f"执行SQL {i+1}/{len(sql_statements)}: {sql[:100]}...")
                    
                    try:
                        # 使用MySQL异步抓包服务执行SQL，但不启动新的抓包
                        result = await mysql_async_capture_service.execute_sql_with_async_capture(
                            sql, config_id, use_existing_capture=True
                        )

                        sql_results.append({
                            "sql": sql,
                            # 兼容新老字段：execution_result 与 query_result
                            "result": result.get("execution_result", result.get("query_result")),
                            "success": result.get("success", False)
                        })
                        
                        logger.info(f"SQL {i+1} 执行完成")
                        
                        # 在SQL之间添加短暂延迟，确保抓包稳定
                        if i < len(sql_statements) - 1:
                            await asyncio.sleep(0.5)
                            
                    except Exception as e:
                        logger.error(f"SQL {i+1} 执行失败: {str(e)}")
                        sql_results.append({
                            "sql": sql,
                            "error": str(e),
                            "success": False
                        })

            # 等待所有数据包被捕获
            await asyncio.sleep(1.5)
            
            # 停止抓包
            final_capture_file = await packet_service.stop_capture()
            logger.info(f"MySQL抓包完成: {final_capture_file}")

            return {
                "success": True,
                "sql_results": sql_results,
                "capture_file": final_capture_file,
                "all_capture_files": [final_capture_file] if final_capture_file else []
            }

        except Exception as e:
            logger.error(f"MySQL智能抓包执行失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "sql_results": [],
                "capture_file": None
            }

    async def _execute_oracle_with_capture(
        self,
        sql_statements: List[str],
        config_id: int
    ) -> Dict[str, Any]:
        """使用Oracle agents执行SQL并抓包 - 每个步骤仅生成一个pcap文件"""
        try:
            logger.info("=== 开始Oracle智能抓包 - 每个步骤仅生成一个pcap文件 ===")
            logger.info(f"SQL语句数量: {len(sql_statements)}")
            logger.info(f"配置ID: {config_id}")
            
            from services.oracle_packet_capture_service import OraclePacketCaptureService
            from services.database_config_service import database_config_service
            from services.oracle_service import OracleService

            logger.info("Oracle服务导入成功")

            # 获取数据库配置
            config = await database_config_service.get_config(config_id)
            if not config:
                raise Exception(f"Database config not found: {config_id}")
            
            logger.info(f"数据库配置获取成功: {config.host}:{config.port}")

            # 单次抓包（覆盖本步骤所有SQL）
            oracle_packet_service = OraclePacketCaptureService()
            sql_results: List[Dict[str, Any]] = []
            final_capture_file: Optional[str] = None

            try:
                # 1) 启动抓包
                logger.info("步骤级抓包: 启动Oracle抓包…")
                _ = await oracle_packet_service.start_capture(
                    config.host,
                    config.port,
                    step_identifier=None
                )
                await asyncio.sleep(1.5)  # 等待抓包完全启动

                # 2) 建立一次新的持久连接（确保握手在本次pcap内）
                step_oracle_service = OracleService(
                    host=config.host,
                    port=config.port,
                    user=config.user,
                    password=config.password,
                    service_name=config.database_name
                )
                # 使用 fresh 连接以确保能捕获握手
                logger.info("步骤级抓包: 创建新的Oracle持久连接…")
                created = await step_oracle_service.create_fresh_connection()
                if not created:
                    raise Exception("Failed to create Oracle persistent connection for step capture")
                await asyncio.sleep(0.5)  # 等待握手数据包

                # 2.1) 预处理：为包含 CREATE TABLE 的语句做幂等化（先尝试 DROP IF EXISTS）
                import re
                create_table_names: List[str] = []
                for raw_sql in sql_statements:
                    if not isinstance(raw_sql, str):
                        continue
                    m = re.search(r"CREATE\s+TABLE\s+([\w$#]+)", raw_sql, re.IGNORECASE)
                    if m:
                        tbl = m.group(1)
                        create_table_names.append(tbl)

                if create_table_names:
                    logger.info(f"检测到待创建表: {create_table_names}，执行DROP IF EXISTS以保证结构一致…")
                    for tbl in create_table_names:
                        plsql_drop = (
                            "BEGIN\n"
                            f"  EXECUTE IMMEDIATE 'DROP TABLE {tbl} CASCADE CONSTRAINTS PURGE';\n"
                            "EXCEPTION\n"
                            "  WHEN OTHERS THEN\n"
                            "    IF SQLCODE != -942 THEN RAISE; END IF;\n"
                            "END;"
                        )
                        try:
                            _ = await step_oracle_service.execute_query_with_persistent_connection(plsql_drop)
                            await asyncio.sleep(0.1)
                            logger.info(f"已预清理表: {tbl}")
                        except Exception as pre_err:
                            logger.warning(f"预清理表失败({tbl}): {pre_err}")

                # 3) 顺序执行所有SQL（同一连接），期间保持抓包
                for idx, raw_sql in enumerate(sql_statements, 1):
                    if not isinstance(raw_sql, str) or not raw_sql.strip():
                        continue
                    # 去掉结尾分号，避免 ORA-00933
                    sql = raw_sql.strip().rstrip(';')
                    try:
                        logger.info(f"执行本步骤SQL {idx}/{len(sql_statements)}: {sql[:120]}…")
                        result = await step_oracle_service.execute_query_with_persistent_connection(sql)
                        # 统一result结构
                        normalized = {
                            "sql": raw_sql,
                            "result": result,
                            "success": True
                        }
                        sql_results.append(normalized)
                    except Exception as sql_err:
                        err_str = str(sql_err)
                        logger.error(f"执行SQL失败: {err_str}")

                        sql_upper = sql.strip().upper()
                        treated_as_success = False
                        treated_message = None

                        # 幂等容错：
                        # - ORA-00955: 对象已存在（CREATE TABLE时可视为成功）
                        if 'ORA-00955' in err_str and sql_upper.startswith('CREATE TABLE'):
                            treated_as_success = True
                            treated_message = 'Table already exists, treated as success'

                        # - ORA-00001: 唯一约束冲突（重复插入可视为成功）
                        if not treated_as_success and 'ORA-00001' in err_str and sql_upper.startswith('INSERT INTO'):
                            treated_as_success = True
                            treated_message = 'Duplicate key, treated as success'

                        # - ORA-00942: 表或视图不存在（对 DROP TABLE 视为成功）
                        if not treated_as_success and 'ORA-00942' in err_str and sql_upper.startswith('DROP TABLE'):
                            treated_as_success = True
                            treated_message = 'Table not exists, treated as success'

                        if treated_as_success:
                            sql_results.append({
                                "sql": raw_sql,
                                "result": {"message": treated_message},
                                "success": True
                            })
                        else:
                            sql_results.append({
                                "sql": raw_sql,
                                "error": err_str,
                                "success": False
                            })

                # 4) 关闭持久连接（挥手包进入同一pcap）
                logger.info("步骤级抓包: 关闭Oracle持久连接…")
                await step_oracle_service.close_persistent_connection()
                await asyncio.sleep(0.8)

            finally:
                # 5) 停止抓包并拿到单个pcap文件
                try:
                    final_capture_file = await oracle_packet_service.stop_capture()
                    logger.info(f"步骤级抓包完成，pcap: {final_capture_file}")
                except Exception as stop_err:
                    logger.error(f"停止Oracle抓包失败: {stop_err}")

            # 生成返回（仅一个pcap）
            step_success = all(r.get("success", False) for r in sql_results) if sql_results else True
            single_list = [final_capture_file] if final_capture_file else []

            return {
                "success": step_success,
                "sql_results": sql_results,
                "capture_file": final_capture_file,
                "capture_files": single_list,
                "all_capture_files": single_list,
                "capture_info": {
                    "captured_steps": [
                        f"执行{len([s for s in sql_statements if isinstance(s, str) and s.strip()])}条SQL",
                        "单次抓包覆盖整个步骤",
                        "同一持久连接握手+查询+挥手"
                    ],
                    "total_sql_count": len([s for s in sql_statements if isinstance(s, str) and s.strip()]),
                    "total_capture_files": 1 if final_capture_file else 0,
                    "capture_strategy": "per_step_single_capture"
                }
            }

        except Exception as e:
            logger.error(f"Oracle智能抓包执行失败: {str(e)}")
            import traceback
            logger.error(f"Oracle抓包错误堆栈: {traceback.format_exc()}")
            return {
                "success": False,
                "error": str(e),
                "sql_results": [],
                "capture_file": None,
                "capture_files": [],
                "all_capture_files": []
            }

    async def _validate_oracle_capture_file(self, capture_file: str, sql: str, step_number: int):
        """验证Oracle抓包文件内容"""
        try:
            if not os.path.exists(capture_file):
                logger.warning(f"步骤{step_number}抓包文件不存在: {capture_file}")
                return
            
            file_size = os.path.getsize(capture_file)
            logger.info(f"步骤{step_number}抓包文件大小: {file_size} 字节")
            
            if file_size < 100:
                logger.warning(f"步骤{step_number}抓包文件过小，可能没有捕获到数据")
                return
            
            # 使用strings命令搜索SQL内容
            try:
                import subprocess
                result = subprocess.run(
                    ['strings', capture_file], 
                    capture_output=True, 
                    text=True
                )
                
                if result.returncode == 0:
                    content = result.stdout
                    
                    # 搜索SQL关键字
                    sql_keywords = ['SELECT', 'INSERT', 'UPDATE', 'DELETE', 'CREATE', 'DROP', 'ALTER', 'TABLE', 'FROM', 'WHERE']
                    found_keywords = []
                    
                    for keyword in sql_keywords:
                        if keyword in content:
                            found_keywords.append(keyword)
                    
                    # 搜索清理后的SQL（移除分号）
                    cleaned_sql = sql.strip().rstrip(';')
                    sql_found = cleaned_sql in content
                    
                    logger.info(f"步骤{step_number}抓包验证结果:")
                    logger.info(f"  文件大小: {file_size} 字节")
                    logger.info(f"  包含SQL关键字: {', '.join(found_keywords) if found_keywords else '无'}")
                    logger.info(f"  包含完整SQL: {'✅ 是' if sql_found else '❌ 否'}")
                    
                    if not sql_found:
                        logger.warning(f"步骤{step_number}抓包文件可能没有包含目标SQL")
                        
                else:
                    logger.warning(f"步骤{step_number}无法读取抓包文件内容")
                    
            except Exception as e:
                logger.warning(f"步骤{step_number}验证抓包文件时出错: {str(e)}")
                
        except Exception as e:
            logger.error(f"步骤{step_number}验证抓包文件失败: {str(e)}")

    async def _execute_postgres_with_capture(
        self,
        sql_statements: List[str],
        config_id: int,
        use_c_executor: bool = False
    ) -> Dict[str, Any]:
        """使用PostgreSQL agents执行SQL并抓包"""
        try:
            # 配置PostgreSQL MCP Agent连接
            await postgres_mcp_agent_service.configure_postgres_connection(config_id)

            sql_results = []
            capture_files = []

            for sql in sql_statements:
                if sql.strip():
                    # 使用PostgreSQL异步抓包服务执行SQL
                    result = await postgres_async_capture_service.execute_sql_with_async_capture(
                        sql, config_id, use_c_executor=use_c_executor
                    )

                    sql_results.append({
                        "sql": sql,
                        "result": result.get("execution_result"),
                        "success": result.get("success", False)
                    })

                    # 收集抓包文件
                    if result.get("packet_file"):
                        capture_files.append(result["packet_file"])

            # 返回最后一个抓包文件作为主要抓包文件
            main_capture_file = capture_files[-1] if capture_files else None

            return {
                "success": True,
                "sql_results": sql_results,
                "capture_file": main_capture_file,
                "all_capture_files": capture_files
            }

        except Exception as e:
            logger.error(f"PostgreSQL智能抓包执行失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "sql_results": [],
                "capture_file": None
            }

    async def _execute_mongo_with_capture(
        self,
        mongo_statements: List[str],
        config_id: int,
        use_c_executor: bool = False
    ) -> Dict[str, Any]:
        """使用MongoDB agents执行查询并抓包（每个步骤：一次抓包+一次连接，覆盖所有语句）"""
        try:
            # 配置MongoDB MCP Agent连接（保持不变）
            await mongo_mcp_agent_service.configure_mongo_connection(config_id)

            # 选择执行器：优先C执行器，否则Python持久连接；不再使用每条语句一个mongosh
            exec_type = "c" if use_c_executor else "python"

            # 在单次抓包与单次连接内执行整步语句
            batch_result = await mongo_async_capture_service.execute_batch_with_single_capture(
                mongo_statements, config_id, executor_type=exec_type
            )

            if not batch_result.get("success", False):
                raise Exception(batch_result.get("error", "Mongo批量抓包执行失败"))

            # 规范化返回
            sql_results = []
            for item in batch_result.get("sql_results", []):
                sql_results.append({
                    "sql": item.get("mongo_query"),
                    "mongo_query": item.get("mongo_query"),
                    "result": item.get("result"),
                    "success": item.get("success", False)
                })

            packet_file = batch_result.get("packet_file")

            return {
                "success": True,
                "sql_results": sql_results,
                "capture_file": packet_file,
                "all_capture_files": [packet_file] if packet_file else []
            }

        except Exception as e:
            logger.error(f"MongoDB智能抓包执行失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "sql_results": [],
                "capture_file": None
            }

    async def _execute_gaussdb_with_capture(
        self,
        sql_statements: List[str],
        config_id: int,
        use_c_executor: bool = False
    ) -> Dict[str, Any]:
        """使用GaussDB统一抓包执行SQL - 每个步骤只生成一个pcap文件"""
        try:
            logger.info("=== 开始GaussDB统一抓包 - 一个步骤统一抓包 ===")
            logger.info(f"SQL语句数量: {len(sql_statements)}")
            logger.info(f"配置ID: {config_id}")

            # 导入必要的服务
            from services.gaussdb_packet_capture_service import gaussdb_packet_service
            # 使用 V2 工厂函数按配置实例化服务，避免导入不存在的单例
            from services.gaussdb_service_v2 import get_gaussdb_service_v2

            # 获取数据库配置
            config = await database_config_service.get_config(config_id)

            # 设置抓包服务的执行上下文
            if self.task_id or self.execution_id:
                gaussdb_packet_service.set_execution_context(self.task_id, self.execution_id)
                logger.info(f"设置GaussDB抓包服务执行上下文: task_id={self.task_id}, execution_id={self.execution_id}")

            # 启动抓包服务 - 在第一个SQL执行前启动
            capture_file = await gaussdb_packet_service.start_capture(config.host, config.port)
            logger.info(f"GaussDB抓包已启动: {capture_file}")

            # 等待抓包服务完全启动
            await asyncio.sleep(2)

            # 根据配置创建 GaussDB 服务实例（V2）
            gaussdb_service = get_gaussdb_service_v2(
                host=config.host,
                port=config.port,
                user=config.user,
                password=config.password,
                database=config.database_name,
                use_c_executor=use_c_executor
            )
            await gaussdb_service.initialize()

            # 执行所有SQL语句
            sql_results = []
            for sql in sql_statements:
                if sql.strip():
                    try:
                        logger.info(f"执行GaussDB SQL: {sql}")
                        # 使用GaussDB服务执行SQL（在抓包期间）
                        result = await gaussdb_service.execute_query_for_capture(sql)
                        sql_results.append({
                            "sql": sql,
                            "result": result,
                            "success": True
                        })
                        logger.info(f"SQL执行成功: {sql}")
                    except Exception as sql_error:
                        logger.error(f"SQL执行失败: {sql}, 错误: {str(sql_error)}")
                        sql_results.append({
                            "sql": sql,
                            "error": str(sql_error),
                            "success": False
                        })

            # 等待数据包捕获完成
            await asyncio.sleep(2)

            # 停止抓包
            logger.info("停止GaussDB抓包")
            final_capture_file = await gaussdb_packet_service.stop_capture()

            logger.info(f"GaussDB统一抓包完成: {final_capture_file}")

            # 统一返回 capture_files 字段，便于上层聚合与保存
            capture_files = [final_capture_file] if final_capture_file else []

            return {
                "success": True,
                "sql_results": sql_results,
                "capture_file": final_capture_file,
                "capture_files": capture_files,
                "all_capture_files": capture_files
            }

        except Exception as e:
            logger.error(f"GaussDB统一抓包执行失败: {str(e)}")
            # 确保在异常情况下也停止抓包
            try:
                from services.gaussdb_packet_capture_service import gaussdb_packet_service
                await gaussdb_packet_service.stop_capture()
            except Exception as e:
                logger.warning(f"停止GaussDB抓包时发生异常: {e}")
            return {
                "success": False,
                "error": str(e),
                "sql_results": [],
                "capture_file": None
            }

    async def _execute_basic_capture(
        self,
        sql_statements: List[str],
        database_type: str,
        config_id: int,
        use_c_executor: bool = False
    ) -> Dict[str, Any]:
        """使用基础抓包服务执行SQL"""
        try:
            # 获取数据库配置
            config = await database_config_service.get_config(config_id)

            # 根据数据库类型选择合适的抓包服务和数据库服务
            if database_type.lower() == "mysql":
                return await self._execute_mysql_basic_capture(sql_statements, config)
            elif database_type.lower() == "gaussdb":
                return await self._execute_gaussdb_with_capture(sql_statements, config_id, use_c_executor)
            elif database_type.lower() == "postgresql":
                return await self._execute_postgresql_basic_capture(sql_statements, config, use_c_executor)
            elif database_type.lower() == "oracle":
                return await self._execute_oracle_basic_capture(sql_statements, config)
            elif database_type.lower() == "mongodb":
                return await self._execute_mongodb_basic_capture(sql_statements, config)
            else:
                # 对于其他数据库类型，使用MySQL作为回退（可能需要进一步扩展）
                logger.warning(f"不支持的数据库类型 {database_type}，使用MySQL作为回退")
                return await self._execute_mysql_basic_capture(sql_statements, config)

        except Exception as e:
            logger.error(f"基础抓包执行失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "sql_results": [],
                "capture_file": None
            }

    async def _execute_mysql_basic_capture(self, sql_statements: List[str], config) -> Dict[str, Any]:
        """执行MySQL基础抓包"""
        try:
            # 启动MySQL抓包
            packet_service = MySQLLocalPacketCaptureService()
            capture_file = await packet_service.start_capture(config.host, config.port)

            # 执行SQL
            mysql_service = MySQLService(
                host=config.host,
                port=config.port,
                user=config.user,
                password=config.password,
                database=config.database_name
            )

            sql_results = []
            for sql in sql_statements:
                if sql.strip():
                    try:
                        result = await mysql_service.execute_query(sql)
                        sql_results.append({
                            "sql": sql,
                            "result": result,
                            "success": True
                        })
                    except Exception as sql_error:
                        sql_results.append({
                            "sql": sql,
                            "error": str(sql_error),
                            "success": False
                        })

            # 停止抓包
            await asyncio.sleep(1)  # 等待数据包捕获
            final_capture_file = await packet_service.stop_capture()

            # 保存抓包文件到数据库
            if final_capture_file and os.path.exists(final_capture_file):
                try:
                    from services.capture_file_service import capture_file_service
                    from models.capture_file import CaptureFileCreate
                    from utils.path_manager import path_manager

                    filename = os.path.basename(final_capture_file)
                    file_size = os.path.getsize(final_capture_file)

                    capture_data = CaptureFileCreate(
                        filename=filename,
                        file_path=path_manager.get_relative_capture_path(filename),
                        file_size=file_size,
                        database_type="mysql",
                        target_host=config.host,
                        target_port=config.port,
                        description=f"批量执行任务生成的mysql抓包文件"
                    )

                    await capture_file_service.save_capture_file(capture_data)
                    logger.info(f"MySQL抓包文件已保存到数据库: {filename}")
                except Exception as save_error:
                    logger.error(f"保存MySQL抓包文件到数据库失败: {save_error}")

            return {
                "success": True,
                "sql_results": sql_results,
                "capture_file": os.path.basename(final_capture_file) if final_capture_file else None
            }

        except Exception as e:
            logger.error(f"MySQL基础抓包执行失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "sql_results": [],
                "capture_file": None
            }

    async def _execute_gaussdb_basic_capture(self, sql_statements: List[str], config,
                                            use_c_executor: bool = False) -> Dict[str, Any]:
        """执行GaussDB基础抓包"""
        try:
            # 先初始化GaussDB服务 - 但不建立连接
            from services.gaussdb_service_v2 import GaussDBServiceV2
            gaussdb_service = GaussDBServiceV2(
                host=config.host,
                port=config.port,
                user=config.user,
                password=config.password,
                database=config.database_name,
                use_c_executor=use_c_executor
            )
            await gaussdb_service.initialize()

            # 启动GaussDB抓包 - 在连接建立之前启动
            from services.gaussdb_packet_capture_service import gaussdb_packet_service
            capture_file = await gaussdb_packet_service.start_capture(config.host, config.port)

            # 等待抓包服务完全启动
            await asyncio.sleep(1)
            logger.info("GaussDB抓包已启动，开始建立数据库连接")

            # 创建持久连接 - 这时抓包已经在监听
            connection_created = await gaussdb_service.create_persistent_connection()
            if not connection_created:
                # 如果连接失败，停止抓包
                await gaussdb_packet_service.stop_capture()
                raise Exception("Failed to create persistent GaussDB connection")

            try:
                sql_results = []
                for sql in sql_statements:
                    if sql.strip():
                        try:
                            # 使用持久连接执行SQL
                            result = await gaussdb_service.execute_sql_query_with_persistent_connection(sql)
                            sql_results.append({
                                "sql": sql,
                                "result": result,
                                "success": True
                            })
                        except Exception as sql_error:
                            # 检查是否是可以忽略的错误（如表已存在）
                            error_str = str(sql_error).lower()
                            sql_upper = sql.upper().strip()

                            # 对于CREATE TABLE，如果错误是"表已存在"，视为成功
                            if (sql_upper.startswith('CREATE TABLE') and
                                ('already exists' in error_str or
                                 ('relation' in error_str and 'already exists' in error_str) or
                                 'existing name' in error_str)):
                                logger.warning(f"表已存在，视为成功: {sql}")
                                sql_results.append({
                                    "sql": sql,
                                    "result": {"message": "Table already exists, treated as success"},
                                    "success": True
                                })
                            else:
                                sql_results.append({
                                    "sql": sql,
                                    "error": str(sql_error),
                                    "success": False
                                })

                # 停止抓包
                await asyncio.sleep(1)  # 等待数据包捕获
                final_capture_file = await gaussdb_packet_service.stop_capture()

                return {
                    "success": True,
                    "sql_results": sql_results,
                    "capture_file": final_capture_file
                }

            finally:
                # 确保关闭持久连接和连接池
                await gaussdb_service.close_persistent_connection()
                gaussdb_service.close_pool()
                logger.info("GaussDB持久连接和连接池已清理")

        except Exception as e:
            logger.error(f"GaussDB基础抓包执行失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "sql_results": [],
                "capture_file": None
            }

    async def _execute_oracle_basic_capture(self, sql_statements: List[str], config) -> Dict[str, Any]:
        """执行Oracle基础抓包 - 每个步骤单独抓包，每个步骤后断开连接"""
        try:
            # 延迟导入Oracle服务
            from services.oracle_service import OracleService
            from services.oracle_packet_capture_service import OraclePacketCaptureService

            logger.info("开始Oracle基础抓包 - 每个步骤单独抓包，每个步骤后断开连接")

            # 存储所有抓包文件
            all_capture_files = []
            sql_results = []

            # 逐个执行SQL步骤，每个步骤单独抓包
            for i, sql in enumerate(sql_statements, 1):
                if not sql.strip():
                    continue

                step_capture_file = None
                step_oracle_service = None
                
                try:
                    logger.info(f"=== 开始执行第{i}个SQL步骤 ===")
                    logger.info(f"SQL语句: {sql[:100]}...")

                    # 步骤1: 启动抓包
                    logger.info(f"步骤{i}.1: 启动Oracle抓包...")
                    oracle_packet_service = OraclePacketCaptureService()
                    step_capture_file = await oracle_packet_service.start_capture(
                        config.host, 
                        config.port, 
                        step_identifier=f"step_{i}"
                    )
                    logger.info(f"步骤{i}抓包已启动: {step_capture_file}")

                    # 等待抓包启动完成
                    await asyncio.sleep(1)

                    # 步骤2: 创建Oracle服务实例并连接数据库
                    logger.info(f"步骤{i}.2: 连接Oracle数据库...")
                    step_oracle_service = OracleService(
                        host=config.host,
                        port=config.port,
                        user=config.user,
                        password=config.password,
                        service_name=config.database_name
                    )
                    
                    # 连接数据库 - 这个过程会被抓包
                    await step_oracle_service.connect()
                    logger.info(f"步骤{i}数据库连接成功")

                    # 等待连接数据包被捕获
                    await asyncio.sleep(0.5)

                    # 步骤3: 执行SQL语句
                    logger.info(f"步骤{i}.3: 执行SQL语句...")
                    result = await step_oracle_service.execute_query(sql)
                    sql_results.append({
                        "sql": sql,
                        "result": result,
                        "success": True,
                        "step_number": i
                    })
                    logger.info(f"步骤{i} SQL语句执行成功")

                    # 等待SQL执行数据包被捕获
                    await asyncio.sleep(0.5)

                    # 步骤4: 断开数据库连接
                    logger.info(f"步骤{i}.4: 断开Oracle数据库连接...")
                    await step_oracle_service.disconnect()
                    logger.info(f"步骤{i}数据库连接已断开")

                    # 等待断开连接数据包被捕获
                    await asyncio.sleep(0.5)

                    # 步骤5: 停止抓包
                    logger.info(f"步骤{i}.5: 停止抓包...")
                    final_step_capture_file = await oracle_packet_service.stop_capture()
                    if final_step_capture_file:
                        all_capture_files.append(final_step_capture_file)
                        logger.info(f"步骤{i}抓包已完成: {final_step_capture_file}")
                    else:
                        logger.warning(f"步骤{i}抓包文件为空")

                    logger.info(f"=== 第{i}个SQL步骤执行完成 ===")

                except Exception as step_error:
                    logger.error(f"步骤{i}执行失败: {str(step_error)}")
                    
                    # 记录失败的步骤
                    sql_results.append({
                        "sql": sql,
                        "error": str(step_error),
                        "success": False,
                        "step_number": i
                    })

                    # 确保在异常情况下也停止抓包和断开连接
                    try:
                        if 'oracle_packet_service' in locals():
                            await oracle_packet_service.stop_capture()
                            logger.info(f"步骤{i}异常情况下已停止抓包")
                    except Exception as stop_error:
                        logger.error(f"步骤{i}停止抓包时出错: {str(stop_error)}")

                    try:
                        if step_oracle_service:
                            await step_oracle_service.disconnect()
                            logger.info(f"步骤{i}异常情况下已断开数据库连接")
                    except Exception as disconnect_error:
                        logger.error(f"步骤{i}断开数据库连接时出错: {str(disconnect_error)}")

                finally:
                    # 确保Oracle服务被清理
                    if step_oracle_service:
                        try:
                            await step_oracle_service.disconnect()
                        except Exception as e:
                            logger.debug(f"步骤{i}清理Oracle服务时出错: {e}")

            logger.info(f"所有Oracle步骤执行完成，共{len(sql_results)}个步骤，{len(all_capture_files)}个抓包文件")

            return {
                "success": True,
                "sql_results": sql_results,
                "capture_files": all_capture_files,  # 返回所有抓包文件
                "capture_info": {
                    "captured_steps": [
                        f"执行{len([s for s in sql_statements if s.strip()])}个SQL步骤",
                        "每个步骤单独抓包和断开连接"
                    ],
                    "total_sql_count": len([s for s in sql_statements if s.strip()]),
                    "total_capture_files": len(all_capture_files),
                    "step_capture_files": all_capture_files
                }
            }

        except Exception as e:
            logger.error(f"Oracle基础抓包执行失败: {str(e)}")

            # 确保在异常情况下也停止抓包
            try:
                if 'oracle_packet_service' in locals():
                    await oracle_packet_service.stop_capture()
                    logger.info("异常情况下已停止Oracle抓包")
            except Exception as stop_error:
                logger.error(f"停止抓包时出错: {str(stop_error)}")

            return {
                "success": False,
                "error": str(e),
                "sql_results": [],
                "capture_files": []
            }

    async def _execute_postgresql_basic_capture(self, sql_statements: List[str], config,
                                               use_c_executor: bool = False) -> Dict[str, Any]:
        """执行PostgreSQL基础抓包"""
        try:
            # 启动PostgreSQL抓包
            from services.postgres_local_packet_capture_service import PostgresLocalPacketCaptureService
            postgres_packet_service = PostgresLocalPacketCaptureService()
            capture_file = await postgres_packet_service.start_capture(config.host, config.port)

            # 如果使用C语言执行器
            if use_c_executor:
                # 使用GaussDB的C执行器服务（因为都是基于libpq）
                from services.gaussdb_service_v2 import GaussDBServiceV2
                executor_service = GaussDBServiceV2(
                    host=config.host,
                    port=config.port,
                    user=config.user,
                    password=config.password,
                    database=config.database_name,
                    use_c_executor=True
                )
                await executor_service.initialize()

                # 创建持久连接
                connection_created = await executor_service.create_persistent_connection()
                if not connection_created:
                    raise Exception("Failed to create persistent PostgreSQL C executor connection")

                try:
                    sql_results = []
                    for sql in sql_statements:
                        if sql.strip():
                            try:
                                # 使用C执行器持久连接执行SQL
                                result = await executor_service.execute_sql_query_with_persistent_connection(sql)
                                sql_results.append({
                                    "sql": sql,
                                    "result": result,
                                    "success": True
                                })
                            except Exception as sql_error:
                                # 检查是否是可以忽略的错误（如表已存在）
                                error_str = str(sql_error).lower()
                                sql_upper = sql.upper().strip()

                                # 对于CREATE TABLE，如果错误是"表已存在"，视为成功
                                if (sql_upper.startswith('CREATE TABLE') and
                                    ('already exists' in error_str or
                                     ('relation' in error_str and 'already exists' in error_str) or
                                     'existing name' in error_str)):
                                    logger.warning(f"表已存在，视为成功: {sql}")
                                    sql_results.append({
                                        "sql": sql,
                                        "result": {"message": "Table already exists, treated as success"},
                                        "success": True
                                    })
                                else:
                                    sql_results.append({
                                        "sql": sql,
                                        "error": str(sql_error),
                                        "success": False
                                    })

                finally:
                    # 确保关闭持久连接和连接池
                    await executor_service.close_persistent_connection()
                    executor_service.close_pool()
                    logger.info("PostgreSQL C执行器持久连接和连接池已清理")

            else:
                # 使用Python执行器 - 为抓包创建独立的PostgreSQL服务实例
                # 这样可以确保在抓包期间建立全新的TCP连接
                from services.postgres_service import PostgresService

                # 创建新的PostgreSQL服务实例，不使用全局单例
                postgres_service_instance = PostgresService(
                    host=config.host,
                    port=config.port,
                    user=config.user,
                    password=config.password,
                    database=config.database_name
                )

                logger.info("为抓包创建了独立的PostgreSQL服务实例，确保建立新的TCP连接")

                # 强制断开所有可能的PostgreSQL连接以确保抓包能捕获握手包
                await self._force_disconnect_all_postgres_connections(config)
                logger.info("已强制断开所有PostgreSQL连接，等待抓包开始后建立新连接")

                sql_results = []
                connection_established = False

                for sql in sql_statements:
                    if sql.strip():
                        try:
                            # 在第一次执行SQL时建立连接，确保抓包能捕获握手包
                            if not connection_established:
                                logger.info("在抓包期间建立PostgreSQL连接以捕获握手包")
                                await postgres_service_instance.create_persistent_connection()
                                connection_established = True
                                logger.info("PostgreSQL连接已建立，开始执行SQL")

                            # 使用持久连接执行查询
                            result = await postgres_service_instance.execute_sql_query_with_persistent_connection(sql)
                            sql_results.append({
                                "sql": sql,
                                "result": result,
                                "success": True
                            })
                        except Exception as sql_error:
                            # 记录详细错误信息
                            error_str = str(sql_error).lower()
                            sql_upper = sql.upper().strip()
                            
                            logger.error(f"SQL执行失败: {sql[:100]}...")
                            logger.error(f"错误详情: {sql_error}")

                            # 检查是否是可以忽略的错误（如表已存在）
                            if (sql_upper.startswith('CREATE TABLE') and
                                ('already exists' in error_str or
                                 ('relation' in error_str and 'already exists' in error_str) or
                                 'existing name' in error_str)):
                                logger.warning(f"表已存在，视为成功: {sql}")
                                sql_results.append({
                                    "sql": sql,
                                    "result": {"message": "Table already exists, treated as success"},
                                    "success": True
                                })
                            # 检查是否是事务中止错误
                            elif 'current transaction is aborted' in error_str:
                                logger.warning(f"事务中止，重新建立连接: {sql}")
                                try:
                                    # 关闭当前连接
                                    await postgres_service_instance.close_persistent_connection()
                                    # 重新建立连接
                                    await postgres_service_instance.create_persistent_connection()
                                    logger.info("PostgreSQL连接已重新建立")
                                    
                                    # 重试执行当前SQL
                                    try:
                                        result = await postgres_service_instance.execute_sql_query_with_persistent_connection(sql)
                                        sql_results.append({
                                            "sql": sql,
                                            "result": result,
                                            "success": True
                                        })
                                        logger.info(f"SQL重试执行成功: {sql[:50]}...")
                                    except Exception as retry_error:
                                        logger.error(f"SQL重试仍然失败: {retry_error}")
                                        sql_results.append({
                                            "sql": sql,
                                            "error": str(retry_error),
                                            "success": False
                                        })
                                except Exception as reconnect_error:
                                    logger.error(f"重新连接失败: {reconnect_error}")
                                    sql_results.append({
                                        "sql": sql,
                                        "error": str(sql_error),
                                        "success": False
                                    })
                            else:
                                sql_results.append({
                                    "sql": sql,
                                    "error": str(sql_error),
                                    "success": False
                                })

                # 在停止抓包之前关闭连接以捕获挥手包
                try:
                    logger.info("准备关闭PostgreSQL持久连接以捕获挥手包…")
                    await postgres_service_instance.close_persistent_connection()
                    postgres_service_instance.close_pool()
                    # 等待一小段时间，给FIN包写入pcap
                    await asyncio.sleep(0.8)
                    logger.info("PostgreSQL连接已关闭")
                except Exception as cleanup_error:
                    logger.warning(f"PostgreSQL连接清理失败: {cleanup_error}")

            # 停止抓包（此时连接已关闭，FIN/RST 应已写入pcap）
            await asyncio.sleep(1)  # 等待数据包捕获
            final_capture_file = await postgres_packet_service.stop_capture()


            return {
                "success": True,
                "sql_results": sql_results,
                "capture_file": final_capture_file,
                "capture_files": [final_capture_file] if final_capture_file else [],
                "all_capture_files": [final_capture_file] if final_capture_file else []
            }

        except Exception as e:
            logger.error(f"PostgreSQL基础抓包执行失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "sql_results": [],
                "capture_file": None
            }

    async def _execute_mongodb_basic_capture(self, mongo_statements: List[str], config) -> Dict[str, Any]:
        """执行MongoDB基础抓包"""
        try:
            # 启动MongoDB抓包
            from services.mongo_local_packet_capture_service import MongoLocalPacketCaptureService
            packet_service = MongoLocalPacketCaptureService()
            capture_file = await packet_service.start_capture(config.host, config.port)

            # 执行MongoDB查询
            from services.mongo_service import MongoService
            mongo_service = MongoService(
                host=config.host,
                port=config.port,
                user=config.user,
                password=config.password,
                database=config.database_name
            )

            mongo_results = []
            for mongo_query in mongo_statements:
                if mongo_query.strip():
                    try:
                        # 在分片环境中，对于for循环操作添加特殊处理
                        if ('for(' in mongo_query and '{' in mongo_query and '}' in mongo_query) or \
                           ('initializeUnorderedBulkOp' in mongo_query) or \
                           ('initializeOrderedBulkOp' in mongo_query):
                            # 确保分片集合已完全初始化
                            logger.info("Detected bulk operation or for loop, adding delay for shard initialization")
                            await asyncio.sleep(2.0)  # 增加延迟时间以确保分片完全初始化
                        
                        result = await mongo_service.execute_mongo_query(mongo_query)
                        mongo_results.append({
                            "mongo_query": mongo_query,
                            "result": result,
                            "success": True
                        })
                        
                        # 在分片环境中，每次操作后添加延迟
                        if 'sh.' in mongo_query or 'insert' in mongo_query.lower() or 'bulk' in mongo_query.lower():
                            await asyncio.sleep(0.5)  # 增加延迟时间以避免并发问题
                            
                    except Exception as mongo_error:
                        logger.error(f"MongoDB query failed: {mongo_query}, error: {str(mongo_error)}")
                        mongo_results.append({
                            "mongo_query": mongo_query,
                            "error": str(mongo_error),
                            "success": False
                        })

            # 停止抓包
            await asyncio.sleep(1)  # 等待数据包捕获
            final_capture_file = await packet_service.stop_capture()

            # 检查抓包是否成功
            if not final_capture_file:
                raise Exception("MongoDB抓包失败：未生成有效的抓包文件。这可能表明数据库连接没有通过监控的网络接口，或者抓包过滤器与实际流量不匹配。")

            # 检查是否有任何成功的操作
            success_count = sum(1 for r in mongo_results if r.get("success", False))
            has_success = success_count > 0
            
            # 记录详细结果
            logger.info(f"MongoDB execution completed. Success count: {success_count}, Total: {len(mongo_results)}")
            for result in mongo_results:
                if result.get("success"):
                    logger.info(f"Successful query: {result['mongo_query']}")
                else:
                    logger.error(f"Failed query: {result['mongo_query']}, Error: {result.get('error', 'Unknown')}")
            
            # 即使部分操作失败，只要有成功操作就算整体成功
            return {
                "success": has_success,
                "sql_results": mongo_results,  # 保持一致的字段名
                "capture_file": final_capture_file
            }

        except Exception as e:
            logger.error(f"MongoDB基础抓包执行失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "sql_results": [],
                "capture_file": None
            }

    async def _force_disconnect_all_postgres_connections(self, config):
        """强制断开所有PostgreSQL连接以确保抓包能捕获握手包（SmartPacketCaptureController内缺失方法补充）"""
        try:
            logger.info("开始强制断开所有PostgreSQL连接 (controller scope)...")

            # 1) 断开全局PostgreSQL服务实例的连接
            try:
                from services.postgres_service import postgres_service
                await postgres_service.close_persistent_connection()
                postgres_service.close_pool()
                logger.info("已断开全局PostgreSQL服务连接")
            except Exception as e:
                logger.warning(f"断开全局PostgreSQL服务连接失败: {e}")

            # 2) 断开按需初始化的PostgreSQL服务连接（来自启动脚本）
            try:
                import start_with_worker
                if hasattr(start_with_worker, 'postgres_service') and start_with_worker.postgres_service:
                    await start_with_worker.postgres_service.close_persistent_connection()
                    start_with_worker.postgres_service.close_pool()
                    logger.info("已断开按需初始化的PostgreSQL服务连接")
            except Exception as e:
                logger.warning(f"断开按需初始化PostgreSQL服务连接失败: {e}")

            # 3) 等待确保连接完全关闭
            await asyncio.sleep(2)
            logger.info("所有PostgreSQL连接断开完成 (controller scope)")

        except Exception as e:
            logger.error(f"强制断开PostgreSQL连接时出错 (controller scope): {e}")
            # 即使失败也不阻断后续流程

class TestCaseExecutionAgentService:
    """测试用例执行Agent服务 - 主协调器"""
    
    def __init__(self):
        """初始化测试用例执行Agent服务"""
        self.llm = self._initialize_llm()
        self.tools = self._initialize_tools()
        self.agent_executor = self._create_agent()
        self.smart_capture_controller = SmartPacketCaptureController()
        self.mysql_service = None
        logger.info("测试用例执行Agent服务初始化完成")
    
    def _initialize_llm(self):
        """初始化语言模型"""
        try:
            return ChatOpenAI(
                model="deepseek-chat",
                openai_api_key=Config.DEEPSEEK_API_KEY,
                openai_api_base=Config.DEEPSEEK_BASE_URL,
                temperature=0.1,
                max_tokens=4096
            )
        except Exception as e:
            logger.error(f"初始化LLM失败: {str(e)}")
            raise
    
    def _initialize_tools(self):
        """初始化工具列表"""
        return [
            TestCaseParserTool(),
            SQLClassifierTool(),
            ExecutionOrderTool()
        ]
    
    def _create_agent(self):
        """创建LangChain代理"""
        try:
            prompt = ChatPromptTemplate.from_messages([
                ("system", """你是一个专业的数据库测试用例执行专家。你可以：

1. 解析测试用例JSON结构，提取测试步骤和SQL语句
2. 对SQL语句进行分类，识别需要抓包的查询
3. 管理测试步骤的执行顺序，确保正确的依赖关系
4. 协调数据包捕获和SQL执行

可用工具：
- test_case_parser: 解析测试用例JSON，提取步骤信息
- sql_classifier: 分类SQL语句，识别抓包需求
- execution_order_manager: 管理执行顺序和依赖关系

执行流程：
1. 首先解析测试用例，提取所有步骤和SQL语句
2. 对SQL语句进行分类，识别需要抓包的查询
3. 生成执行计划，确保正确的执行顺序
4. 按计划执行，在需要时启动/停止抓包

请根据用户的测试用例，生成详细的执行计划。"""),
                ("user", "{input}"),
                ("assistant", "{agent_scratchpad}")
            ])
            
            agent = create_openai_tools_agent(self.llm, self.tools, prompt)
            
            return AgentExecutor(
                agent=agent,
                tools=self.tools,
                verbose=True,
                max_iterations=10,
                handle_parsing_errors=True
            )
            
        except Exception as e:
            logger.error(f"创建代理失败: {str(e)}")
            raise
    
    async def execute_test_case(
        self,
        test_case_json: str,
        config_id: int,
        capture_enabled: bool = True,
        test_case_id: str = None,
        use_c_executor: bool = False
    ) -> Dict[str, Any]:
        """执行测试用例"""
        import time
        start_time = time.time()

        try:
            logger.info("=== 开始执行测试用例 ===")
            logger.info(f"测试用例ID: {test_case_id}")
            logger.info(f"数据库配置ID: {config_id}")
            logger.info(f"抓包启用: {capture_enabled}")
            logger.info(f"使用C执行器: {use_c_executor}")
            logger.debug(f"测试用例JSON长度: {len(test_case_json)} 字符")
            logger.debug(f"测试用例JSON前200字符: {test_case_json[:200]}...")

            # 检查test_case_json是否为空或只包含空对象
            if not test_case_json or test_case_json.strip() in ['{}', '']:
                logger.warning(f"测试用例JSON为空，尝试从数据库获取完整内容: {test_case_id}")
                if test_case_id:
                    try:
                        # 使用用例管理服务获取完整用例（修复: 移除不存在的 services.test_case_service 依赖）
                        from services.test_case_management_service import test_case_management_service
                        full_test_case = await test_case_management_service.get_test_case(test_case_id)
                        if full_test_case:
                            # 构建完整的测试用例JSON
                            import json
                            # Pydantic 对象序列化：test_steps 可能为对象列表，这里交给 json.dumps 由模型提供的 dict 方法处理
                            test_case_json = json.dumps({
                                "id": full_test_case.id,
                                "title": full_test_case.title,
                                "test_steps": [s.dict() if hasattr(s, 'dict') else s for s in (full_test_case.test_steps or [])],
                                "database_type": getattr(full_test_case, 'database_type', None),
                                "expected_result": full_test_case.expected_result
                            }, ensure_ascii=False, default=str)
                            logger.info(f"成功从数据库获取测试用例内容，包含 {len(full_test_case.test_steps or [])} 个测试步骤")
                        else:
                            logger.error(f"无法从数据库获取测试用例: {test_case_id}")
                    except Exception as e:
                        logger.error(f"从数据库获取测试用例失败: {e}")
                else:
                    logger.error("测试用例JSON为空且未提供test_case_id，无法获取测试用例内容")

            # 配置数据库连接
            logger.info("正在配置数据库连接...")
            await self._configure_database_connection(config_id)
            logger.info("数据库连接配置完成")

            # 直接解析测试用例，跳过复杂的Agent分析
            logger.info("正在解析测试用例...")
            execution_plan = await self._simple_parse_test_case(test_case_json, config_id)
            logger.info(f"测试用例解析完成，生成 {len(execution_plan) if execution_plan else 0} 个执行步骤")

            if not execution_plan:
                logger.error("测试用例解析失败，无法生成执行计划")
                return {
                    "success": False,
                    "error": "无法解析测试用例"
                }

            # 执行测试步骤
            logger.info(f"开始执行 {len(execution_plan)} 个测试步骤...")
            execution_results = await self._execute_steps(
                execution_plan, config_id, capture_enabled, use_c_executor
            )
            logger.info(f"测试步骤执行完成，共 {len(execution_results)} 个结果")

            # 计算执行耗时
            duration = int(time.time() - start_time)

            # 收集所有抓包文件
            import os
            all_capture_files = []
            for result in execution_results:
                if result.get("capture_file"):
                    capture_file = result["capture_file"]
                    # 如果是完整路径，提取文件名；如果已经是文件名，直接使用
                    if capture_file and os.path.sep in capture_file:
                        capture_filename = os.path.basename(capture_file)
                    else:
                        capture_filename = capture_file
                    all_capture_files.append(capture_filename)
                if result.get("all_capture_files"):
                    for capture_file in result["all_capture_files"]:
                        # 如果是完整路径，提取文件名；如果已经是文件名，直接使用
                        if capture_file and os.path.sep in capture_file:
                            capture_filename = os.path.basename(capture_file)
                        else:
                            capture_filename = capture_file
                        all_capture_files.append(capture_filename)

            # 去重
            all_capture_files = list(set(all_capture_files))

            # 执行步骤校验（如果启用了抓包）
            step_validation_result = None
            if capture_enabled and all_capture_files:
                try:
                    from services.step_sql_validation_service import StepSQLValidationService
                    step_validation_service = StepSQLValidationService()

                    # 使用第一个抓包文件进行校验
                    main_capture_file = all_capture_files[0]
                    capture_file_path = os.path.join("captures", main_capture_file)

                    if os.path.exists(capture_file_path):
                        logger.info(f"开始执行步骤校验，使用抓包文件: {capture_file_path}")

                        # 获取数据库类型
                        database_type = None
                        database_port = None
                        if config_id:
                            try:
                                from services.database_config_service import DatabaseConfigService
                                db_service = DatabaseConfigService()
                                config = await db_service.get_database_config(config_id)
                                if config:
                                    database_type = config.get("database_type", "").lower()
                                    database_port = config.get("port")
                            except Exception as e:
                                logger.warning(f"获取数据库配置失败: {e}")

                        step_validation_summary = step_validation_service.validate_steps_with_pcap(
                            execution_steps=execution_plan,
                            pcap_file=capture_file_path,
                            database_type=database_type,
                            database_port=database_port
                        )

                        step_validation_result = {
                            "total_steps": step_validation_summary.total_steps,
                            "matched_steps": step_validation_summary.matched_steps,
                            "partial_matched_steps": step_validation_summary.partial_matched_steps,
                            "not_found_steps": step_validation_summary.not_found_steps,
                            "similar_steps": step_validation_summary.similar_steps,
                            "error_steps": step_validation_summary.error_steps,
                            "overall_success_rate": step_validation_summary.overall_success_rate,
                            "pcap_file": main_capture_file
                        }

                        logger.info(f"步骤校验完成，成功率: {step_validation_summary.overall_success_rate:.2%}")
                    else:
                        logger.warning(f"抓包文件不存在，跳过步骤校验: {capture_file_path}")

                except Exception as e:
                    logger.error(f"步骤校验失败: {str(e)}")
                    step_validation_result = {"error": str(e)}

            # 计算执行结果
            success_count = sum(1 for result in execution_results if result.get("success", False))
            total_count = len(execution_results)
            execution_result_str = "pass" if success_count == total_count else "fail"
            success_rate = (success_count / total_count * 100) if total_count > 0 else 0

            # 更新最近执行记录
            if test_case_id:
                try:
                    from services.test_case_management_service import test_case_management_service
                    from models.test_case_management import TestCaseLastExecution, ExecutionResultEnum
                    import uuid
                    from datetime import datetime

                    execution_id = str(uuid.uuid4())

                    # 转换为枚举类型
                    execution_result = ExecutionResultEnum.PASS if execution_result_str == "pass" else ExecutionResultEnum.FAIL

                    # 构建完整的执行详情
                    execution_details = {
                        "success": execution_result == ExecutionResultEnum.PASS,
                        "execution_results": execution_results,
                        "summary": {
                            "total_steps": len(execution_results),
                            "successful_steps": len([r for r in execution_results if r.get("success", False)]),
                            "failed_steps": len([r for r in execution_results if not r.get("success", True)]),
                            "success_rate": success_rate / 100.0,  # 转换为小数
                            "capture_files": all_capture_files,
                            "total_captures": len(all_capture_files)
                        },
                        "capture_files": all_capture_files,
                        "duration": duration,
                        "success_rate": success_rate,
                        "step_validation": step_validation_result  # 添加步骤校验结果
                    }

                    await test_case_management_service.update_last_execution(
                        test_case_id=test_case_id,
                        execution_id=execution_id,
                        execution_result=execution_result,
                        config_id=config_id,
                        capture_files=all_capture_files,
                        duration=duration,
                        execution_details=execution_details
                    )
                    logger.info(f"已更新测试用例 {test_case_id} 的最近执行记录")

                    # 根据执行结果更新测试用例状态
                    logger.info(f"检查执行结果: execution_result={execution_result}, execution_result_str={execution_result_str}")
                    if execution_result == ExecutionResultEnum.PASS:
                        from models.test_case_management import StatusEnum
                        logger.info(f"开始更新测试用例 {test_case_id} 状态为已完成")
                        await test_case_management_service.update_test_case_status(test_case_id, StatusEnum.COMPLETED)
                        logger.info(f"已更新测试用例 {test_case_id} 状态为已完成")
                    else:
                        from models.test_case_management import StatusEnum
                        logger.info(f"测试用例 {test_case_id} 执行失败，更新状态为失败")
                        await test_case_management_service.update_test_case_status(test_case_id, StatusEnum.FAILED)
                        logger.info(f"已更新测试用例 {test_case_id} 状态为失败")

                except Exception as e:
                    logger.error(f"更新最近执行记录失败: {str(e)}")

            return {
                "success": execution_result_str == "pass",
                "execution_plan": execution_plan,
                "execution_results": execution_results,
                "summary": self._generate_execution_summary(execution_results),
                "capture_files": all_capture_files,
                "duration": duration,
                "success_rate": success_rate,
                "step_validation": step_validation_result  # 添加步骤校验结果
            }

        except Exception as e:
            logger.error(f"测试用例执行失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }

    async def _simple_parse_test_case(self, test_case_json: str, config_id: int = None) -> List[Dict[str, Any]]:
        """简单解析测试用例，直接使用工具而不是Agent"""
        logger.info("=== 开始简单解析测试用例 ===")
        logger.info(f"配置ID: {config_id}")

        try:
            from tools.test_case_execution_tools import TestCaseParserTool

            # 获取数据库类型
            database_type = None
            if config_id:
                logger.info(f"正在获取数据库配置信息...")
                try:
                    config = await database_config_service.get_config(config_id)
                    if config:
                        database_type = config.database_type.lower()
                        logger.info(f"数据库类型: {database_type}")
                        logger.info(f"数据库配置: {config.host}:{config.port}")
                    else:
                        logger.warning(f"未找到配置ID {config_id} 对应的数据库配置")
                except Exception as e:
                    logger.warning(f"获取数据库类型失败: {e}")
            else:
                logger.warning("未提供配置ID，将使用默认解析器")

            logger.info("正在创建测试用例解析器...")
            parser = TestCaseParserTool()
            logger.info("调用解析器解析测试用例...")
            result = parser._run(test_case_json, database_type)
            logger.info("解析器执行完成")

            import json

            # 检查解析结果是否为错误信息
            logger.info("正在检查解析结果...")
            if result.startswith("解析失败:"):
                logger.error(f"测试用例解析失败: {result}")
                raise Exception(result)

            try:
                logger.info("正在解析JSON结果...")
                parsed_result = json.loads(result)
                logger.info("JSON结果解析成功")
            except json.JSONDecodeError as e:
                logger.error(f"解析结果不是有效的JSON: {result}")
                raise Exception(f"无法解析测试用例: JSON格式错误 - {str(e)}")

            # 记录解析结果详情
            test_case_id = parsed_result.get("test_case_id")
            title = parsed_result.get("title")
            total_steps = parsed_result.get("total_steps", 0)
            parsed_steps = parsed_result.get("parsed_steps", [])

            logger.info(f"解析结果详情:")
            logger.info(f"  - 测试用例ID: {test_case_id}")
            logger.info(f"  - 标题: {title}")
            logger.info(f"  - 总步骤数: {total_steps}")
            logger.info(f"  - 解析步骤数: {len(parsed_steps)}")

            if parsed_steps:
                logger.info("=== 简单解析测试用例完成 ===")
                return parsed_steps
            else:
                logger.warning("解析结果中没有找到parsed_steps，返回空列表")
                return []

        except Exception as e:
            logger.error(f"=== 简单解析测试用例失败 ===")
            logger.error(f"错误信息: {str(e)}")
            import traceback
            logger.error(f"错误堆栈: {traceback.format_exc()}")
            return []

    async def _analyze_test_case(self, test_case_json: str) -> Dict[str, Any]:
        """使用Agent分析测试用例"""
        try:
            # 构建分析请求
            analysis_request = f"""
请分析以下测试用例，生成详细的执行计划：

测试用例JSON:
{test_case_json}

请执行以下步骤：
1. 解析测试用例结构，提取所有测试步骤
2. 分类每个步骤中的SQL语句
3. 生成执行计划，确保正确的执行顺序

请提供详细的分析结果。
            """
            
            # 执行Agent分析
            result = await asyncio.get_event_loop().run_in_executor(
                None, 
                lambda: self.agent_executor.invoke({"input": analysis_request})
            )
            
            # 解析Agent输出
            agent_output = result.get("output", "")
            
            # 提取执行计划（这里需要解析Agent的输出）
            execution_plan = self._extract_execution_plan_from_output(agent_output)
            
            return {
                "success": True,
                "agent_analysis": agent_output,
                "execution_plan": execution_plan
            }
            
        except Exception as e:
            logger.error(f"测试用例分析失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _extract_execution_plan_from_output(self, agent_output: str) -> List[Dict[str, Any]]:
        """从Agent输出中提取执行计划"""
        # 这里需要解析Agent的输出，提取结构化的执行计划
        # 简化实现，实际应该解析JSON格式的输出
        try:
            # 尝试从输出中提取JSON
            import re
            json_match = re.search(r'\{.*\}', agent_output, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                parsed_data = json.loads(json_str)
                return parsed_data.get("execution_order", [])
        except Exception as e:

            logger.error(f"操作失败: {type(e).__name__}: {str(e)}")

            logger.error(f"异常堆栈跟踪: {traceback.format_exc()}")
        # 如果解析失败，返回空计划
        return []
    
    async def _configure_database_connection(self, config_id: int):
        """配置数据库连接"""
        try:
            config = await database_config_service.get_config(config_id)
            if not config:
                raise Exception(f"数据库配置不存在: {config_id}")

            database_type = config.database_type.lower()

            if database_type == "mysql":
                # 创建MySQL服务实例
                self.mysql_service = MySQLService(
                    host=config.host,
                    port=config.port,
                    user=config.user,
                    password=config.password,
                    database=config.database_name
                )

                # 测试连接
                is_connected = await self.mysql_service.check_connection()
                if not is_connected:
                    raise Exception("MySQL数据库连接失败")

            elif database_type == "gaussdb":
                # 创建GaussDB服务实例
                from services.gaussdb_service_v2 import GaussDBServiceV2
                self.gaussdb_service = GaussDBServiceV2(
                    host=config.host,
                    port=config.port,
                    user=config.user,
                    password=config.password,
                    database=config.database_name
                )
                await self.gaussdb_service.initialize()

                # 测试连接
                is_connected = await self.gaussdb_service.check_connection()
                if not is_connected:
                    raise Exception("GaussDB数据库连接失败")

            elif database_type == "postgresql":
                # 创建PostgreSQL服务实例
                from services.postgres_service import PostgresService
                self.postgres_service = PostgresService(
                    host=config.host,
                    port=config.port,
                    user=config.user,
                    password=config.password,
                    database=config.database_name
                )
                await self.postgres_service.initialize()

                # 测试连接
                is_connected = await self.postgres_service.check_connection()
                if not is_connected:
                    raise Exception("PostgreSQL数据库连接失败")

            elif database_type == "oracle":
                # 延迟导入Oracle服务
                from services.oracle_service import OracleService

                # 创建Oracle服务实例
                try:
                    self.oracle_service = OracleService(
                        host=config.host,
                        port=config.port,
                        user=config.user,
                        password=config.password,
                        service_name=config.database_name  # Oracle使用service_name
                    )
                except AttributeError as e:
                    if "_safe_oracle_init" in str(e):
                        logger.error(f"Oracle服务创建失败，缺少_safe_oracle_init方法: {e}")
                        # 尝试重新导入并创建
                        import importlib
                        import services.oracle_service
                        importlib.reload(services.oracle_service)
                        from services.oracle_service import OracleService

                        self.oracle_service = OracleService(
                            host=config.host,
                            port=config.port,
                            user=config.user,
                            password=config.password,
                            service_name=config.database_name
                        )
                        logger.info("Oracle服务重新创建成功")
                    else:
                        raise
                await self.oracle_service.connect()

                # 测试连接
                test_result = await self.oracle_service.test_connection()
                if not test_result.get('success', False):
                    raise Exception("Oracle数据库连接失败")

            elif database_type == "mongodb":
                # 创建MongoDB服务实例
                from services.mongo_service import MongoService
                self.mongo_service = MongoService(
                    host=config.host,
                    port=config.port,
                    user=config.user,
                    password=config.password,
                    database=config.database_name
                )

                # 测试连接
                is_connected = await self.mongo_service.check_connection()
                if not is_connected:
                    raise Exception("MongoDB数据库连接失败")

            else:
                raise Exception(f"不支持的数据库类型: {database_type}")

            logger.info(f"数据库连接配置成功: {config.host}:{config.port} ({database_type})")

        except Exception as e:
            logger.error(f"配置数据库连接失败: {str(e)}")
            raise
    
    async def _execute_steps(
        self,
        execution_plan: List[Dict[str, Any]],
        config_id: int,
        capture_enabled: bool,
        use_c_executor: bool = False
    ) -> List[Dict[str, Any]]:
        """执行测试步骤"""
        results = []
        current_capture_file = None
        
        try:
            for step in execution_plan:
                step_result = await self._execute_single_step(
                    step, config_id, capture_enabled, use_c_executor
                )
                results.append(step_result)

                # 记录步骤执行结果，但继续执行后续步骤
                if not step_result.get("success", False):
                    logger.warning(f"步骤 {step.get('step_number')} 执行失败，但继续执行后续步骤")
                else:
                    logger.info(f"步骤 {step.get('step_number')} 执行成功")

            return results
            
        except Exception as e:
            logger.error(f"执行步骤失败: {str(e)}")
            return results
    
    async def _execute_single_step(
        self,
        step: Dict[str, Any],
        config_id: int,
        capture_enabled: bool,
        use_c_executor: bool = False
    ) -> Dict[str, Any]:
        """执行单个测试步骤"""
        try:
            step_number = step.get("step_number")
            sql_statements = step.get("sql_statements", [])
            capture_strategy = step.get("capture_strategy", "no_capture")

            logger.info(f"执行步骤 {step_number}: {step.get('action')}")

            # 判断是否需要抓包
            # 修改为：如果启用抓包，则所有步骤都需要抓包
            need_capture = capture_enabled
            logger.info(f"步骤 {step_number} 抓包状态: need_capture={need_capture}, capture_enabled={capture_enabled}, sql_statements={len(sql_statements)}")

            if need_capture and sql_statements:
                logger.info(f"步骤 {step_number} 使用智能抓包控制器执行SQL")
                # 获取数据库配置以确定数据库类型
                config = await database_config_service.get_config(config_id)
                database_type = config.database_type
                logger.info(f"步骤 {step_number} 数据库类型: {database_type}")

                # 设置智能抓包控制器的执行上下文
                task_id = getattr(self, 'task_id', None)
                execution_id = getattr(self, 'execution_id', None)
                self.smart_capture_controller.set_execution_context(task_id, execution_id)
                logger.info(f"步骤 {step_number} 智能抓包控制器执行上下文设置完成")

                # 针对 MongoDB：如果 test_data 包含事务/session 片段，则以整段脚本执行，避免拆分导致会话丢失
                statements_to_run = sql_statements
                try:
                    if isinstance(database_type, str) and database_type.lower() == "mongodb":
                        raw_text = step.get("test_data") or ""
                        text_l = raw_text.lower()
                        has_tx_markers = any(k in text_l for k in [
                            "startsession(", "starttransaction(", "committransaction(", "aborttransaction(", "getmongo()", "session.getdatabase("
                        ])
                        multi_stmt = ";" in raw_text
                        if raw_text and (has_tx_markers or multi_stmt):
                            # 作为一条脚本交给抓包执行器，内部预处理会处理会话/事务与变量集合
                            statements_to_run = [raw_text]
                            logger.info(f"步骤 {step_number} 检测到Mongo事务/会话脚本，改为整段执行，共1条")
                except Exception as _e:
                    logger.warning(f"步骤 {step_number} 事务脚本检测失败，回退按拆分语句执行: {_e}")

                # 使用智能抓包控制器执行SQL/脚本
                logger.info(f"步骤 {step_number} 开始调用智能抓包控制器...")
                capture_result = await self.smart_capture_controller.execute_sql_with_capture(
                    statements_to_run, database_type, config_id, use_c_executor
                )
                logger.info(f"步骤 {step_number} 智能抓包控制器执行完成，结果: {capture_result}")

                # 处理抓包文件 - 支持单个文件或多个文件
                capture_file = capture_result.get("capture_file")
                capture_files = capture_result.get("capture_files", [])
                logger.info(f"步骤 {step_number} 抓包文件: capture_file={capture_file}, capture_files={capture_files}")
                
                # 如果返回的是多个抓包文件，优先使用capture_files
                if capture_files and isinstance(capture_files, list):
                    # 多个抓包文件的情况（如Oracle每个步骤单独抓包）
                    logger.info(f"步骤 {step_number} 返回多个抓包文件")
                    return {
                        "success": capture_result.get("success", False),
                        "step_number": step_number,
                        "action": step.get("action"),
                        "sql_results": capture_result.get("sql_results", []),
                        "capture_file": capture_files[0] if capture_files else None,  # 保持向后兼容
                        "capture_files": capture_files,  # 新增：支持多个抓包文件
                        "all_capture_files": capture_files,  # 兼容现有代码
                        "capture_enabled": True,
                        "failed_sqls": len([r for r in capture_result.get("sql_results", []) if not r.get("success", True)])
                    }
                else:
                    # 单个抓包文件的情况（保持原有逻辑）
                    logger.info(f"步骤 {step_number} 返回单个抓包文件")
                    return {
                        "success": capture_result.get("success", False),
                        "step_number": step_number,
                        "action": step.get("action"),
                        "sql_results": capture_result.get("sql_results", []),
                        "capture_file": capture_file,
                        "capture_files": [capture_file] if capture_file else [],  # 新增：支持多个抓包文件
                        "all_capture_files": [capture_file] if capture_file else [],  # 兼容现有代码
                        "capture_enabled": True,
                        "failed_sqls": len([r for r in capture_result.get("sql_results", []) if not r.get("success", True)])
                    }
            else:
                logger.info(f"步骤 {step_number} 不使用抓包，直接执行SQL")
                # 不需要抓包，直接执行SQL
                await self._configure_database_connection(config_id)

                # 获取数据库配置以确定数据库类型
                config = await database_config_service.get_config(config_id)
                database_type = config.database_type.lower()

                sql_results = []
                for sql in sql_statements:
                    if sql.strip():
                        try:
                            if database_type == "mysql":
                                sql_result = await self.mysql_service.execute_query(sql)
                            elif database_type == "gaussdb":
                                sql_result = await self.gaussdb_service.execute_sql_query(sql)
                            elif database_type == "postgresql":
                                sql_result = await self.postgres_service.execute_query(sql)
                            elif database_type == "oracle":
                                sql_result = await self.oracle_service.execute_query(sql)
                            elif database_type == "mongodb":
                                sql_result = await self.mongo_service.execute_mongo_query(sql)
                            else:
                                raise Exception(f"不支持的数据库类型: {database_type}")

                            sql_results.append({
                                "sql": sql,
                                "result": sql_result,
                                "success": True
                            })
                        except Exception as sql_error:
                            # 检查是否是可以忽略的错误（如表已存在）
                            error_str = str(sql_error).lower()
                            sql_upper = sql.upper().strip()

                            # 对于CREATE TABLE，如果错误是"表已存在"，视为成功
                            if (sql_upper.startswith('CREATE TABLE') and
                                ('already exists' in error_str or
                                 ('relation' in error_str and 'already exists' in error_str) or
                                 'existing name' in error_str)):
                                logger.warning(f"表已存在，视为成功: {sql}")
                                sql_results.append({
                                    "sql": sql,
                                    "result": {"message": "Table already exists, treated as success"},
                                    "success": True
                                })
                            else:
                                logger.error(f"SQL执行失败: {sql} - {str(sql_error)}")
                                sql_results.append({
                                    "sql": sql,
                                    "error": str(sql_error),
                                    "success": False
                                })

                # 检查是否有SQL执行失败
                failed_sqls = [r for r in sql_results if not r.get("success", True)]
                step_success = len(failed_sqls) == 0

                return {
                    "success": step_success,
                    "step_number": step_number,
                    "action": step.get("action"),
                    "sql_results": sql_results,
                    "capture_file": None,
                    "capture_enabled": False,
                    "failed_sqls": len(failed_sqls)
                }

        except Exception as e:
            logger.error(f"执行步骤失败: {str(e)}")
            return {
                "success": False,
                "step_number": step.get("step_number"),
                "action": step.get("action", ""),
                "error": str(e)
            }
    
    def _generate_execution_summary(self, execution_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成执行摘要"""
        total_steps = len(execution_results)
        successful_steps = sum(1 for r in execution_results if r.get("success", False))
        failed_steps = total_steps - successful_steps
        
        # 收集所有抓包文件 - 支持单个文件和多个文件
        capture_files = []
        for result in execution_results:
            # 处理单个抓包文件
            if result.get("capture_file"):
                capture_files.append(result["capture_file"])
            
            # 处理多个抓包文件
            if result.get("capture_files"):
                capture_files.extend(result["capture_files"])
            
            # 兼容现有代码
            if result.get("all_capture_files"):
                capture_files.extend(result["all_capture_files"])
        
        # 去重
        capture_files = list(set([f for f in capture_files if f]))
        
        return {
            "total_steps": total_steps,
            "successful_steps": successful_steps,
            "failed_steps": failed_steps,
            "success_rate": successful_steps / total_steps if total_steps > 0 else 0,
            "capture_files": capture_files,
            "total_captures": len(capture_files)
        }

    async def cleanup_database_connections(self):
        """清理所有数据库连接和连接池"""
        try:
            logger.info("开始清理测试用例执行服务的数据库连接...")

            # 清理GaussDB连接池
            if hasattr(self, 'gaussdb_service') and self.gaussdb_service:
                try:
                    await self.gaussdb_service.close_persistent_connection()
                    self.gaussdb_service.close_pool()
                    logger.info("GaussDB连接池已清理")
                except Exception as e:
                    logger.error(f"清理GaussDB连接池失败: {e}")

            # 清理Oracle连接
            if hasattr(self, 'oracle_service') and self.oracle_service:
                try:
                    await self.oracle_service.disconnect()
                    logger.info("Oracle连接已断开")
                except Exception as e:
                    logger.error(f"断开Oracle连接失败: {e}")

            # 清理MySQL连接（如果有连接池的话）
            if hasattr(self, 'mysql_service') and self.mysql_service:
                try:
                    # MySQL服务通常不需要特殊清理，但为了一致性保留
                    logger.info("MySQL连接清理完成")
                except Exception as e:
                    logger.error(f"清理MySQL连接失败: {e}")

            # 清理PostgreSQL连接
            if hasattr(self, 'postgres_service') and self.postgres_service:
                try:
                    # PostgreSQL服务清理（如果有的话）
                    logger.info("PostgreSQL连接清理完成")
                except Exception as e:
                    logger.error(f"清理PostgreSQL连接失败: {e}")

            # 清理MongoDB连接
            if hasattr(self, 'mongo_service') and self.mongo_service:
                try:
                    # MongoDB服务清理（如果有的话）
                    logger.info("MongoDB连接清理完成")
                except Exception as e:
                    logger.error(f"清理MongoDB连接失败: {e}")

            logger.info("测试用例执行服务的数据库连接清理完成")

        except Exception as e:
            logger.error(f"清理数据库连接失败: {str(e)}")

    async def _force_disconnect_all_postgres_connections(self, config):
        """强制断开所有PostgreSQL连接以确保抓包能捕获握手包"""
        try:
            logger.info("开始强制断开所有PostgreSQL连接...")

            # 1. 断开全局PostgreSQL服务实例的连接
            try:
                from services.postgres_service import postgres_service
                await postgres_service.close_persistent_connection()
                postgres_service.close_pool()
                logger.info("已断开全局PostgreSQL服务连接")
            except Exception as e:
                logger.warning(f"断开全局PostgreSQL服务连接失败: {e}")

            # 2. 断开按需初始化的PostgreSQL服务连接
            try:
                import start_with_worker
                if hasattr(start_with_worker, 'postgres_service') and start_with_worker.postgres_service:
                    await start_with_worker.postgres_service.close_persistent_connection()
                    start_with_worker.postgres_service.close_pool()
                    logger.info("已断开按需初始化的PostgreSQL服务连接")
            except Exception as e:
                logger.warning(f"断开按需初始化PostgreSQL服务连接失败: {e}")

            # 3. 等待一段时间确保连接完全关闭
            await asyncio.sleep(2)

            logger.info("所有PostgreSQL连接断开完成")

        except Exception as e:
            logger.error(f"强制断开PostgreSQL连接时出错: {e}")
            # 即使断开连接失败，也继续执行，因为可能没有现有连接

# 创建全局服务实例
test_case_execution_agent_service = TestCaseExecutionAgentService()

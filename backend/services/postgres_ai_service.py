import logging
from typing import Dict, Any, List
from openai import OpenAI
from utils.config import Config

logger = logging.getLogger(__name__)

class PostgresAIService:
    """PostgreSQL AI服务 - 专门处理PostgreSQL相关的自然语言转换"""
    
    def __init__(self):
        """初始化PostgreSQL AI服务"""
        self.client = OpenAI(
            api_key=Config.DEEPSEEK_API_KEY,
            base_url=Config.DEEPSEEK_BASE_URL
        )
        logger.info("PostgreSQL AI Service initialized")
    
    def _call_deepseek(self, prompt: str) -> str:
        """调用DeepSeek API"""
        try:
            response = self.client.chat.completions.create(
                model="deepseek-chat",
                messages=[
                    {"role": "system", "content": "你是一个专业的PostgreSQL专家，能够将自然语言转换为准确的PostgreSQL SQL语句。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=4096
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            logger.error(f"DeepSeek API call failed: {str(e)}")
            raise
    
    async def parse_natural_language_to_sql(self, natural_query: str) -> str:
        """将自然语言转换为PostgreSQL SQL语句"""
        try:
            logger.info(f"Parsing natural language query to PostgreSQL SQL: {natural_query}")

            # 构建PostgreSQL专用提示词
            prompt = f"""
请将以下自然语言查询转换为PostgreSQL SQL语句。

自然语言查询: {natural_query}

要求:
1. 如果是查询操作，返回SELECT语句
2. 如果是插入操作，返回INSERT语句
3. 如果是更新操作，返回UPDATE语句
4. 如果是删除操作，返回DELETE语句
5. **重要：如果是创建表操作，必须先返回DROP TABLE IF EXISTS table_name;语句，然后返回CREATE TABLE语句**
6. 如果是修改表结构，返回ALTER TABLE语句
7. 如果是创建索引，返回CREATE INDEX语句
8. 如果是数据库管理操作，返回相应的PostgreSQL管理命令

**创建表的标准格式：**
DROP TABLE IF EXISTS table_name;
CREATE TABLE table_name (字段定义...);

注意事项:
- 使用PostgreSQL特有的语法和函数
- 字符串使用单引号
- 日期时间使用PostgreSQL格式
- 支持PostgreSQL特有的数据类型（如SERIAL、UUID、JSONB等）
- 支持PostgreSQL特有的操作符和函数
- 如果涉及模糊查询，使用ILIKE操作符
- 如果需要分页，使用LIMIT和OFFSET
- 如果需要排序，使用ORDER BY

常见表名假设（如果查询中没有明确指定）:
- 用户相关: users
- 订单相关: orders
- 产品相关: products
- 文章相关: articles
- 评论相关: comments

请直接返回PostgreSQL SQL语句，不要添加额外的解释和注释。
重要：不要在SQL语句中包含任何注释（如#、--、/* */等），只返回纯净的可执行SQL语句。
            """

            # 调用DeepSeek API
            result = self._call_deepseek(prompt)

            # 提取SQL语句
            sql_query = self._extract_sql_from_response(result)

            # 后处理：确保CREATE TABLE语句前包含DROP TABLE语句
            import re
            if re.search(r'\bCREATE\s+TABLE\s+(\w+)', sql_query, re.IGNORECASE):
                # 提取表名
                match = re.search(r'\bCREATE\s+TABLE\s+(\w+)', sql_query, re.IGNORECASE)
                if match:
                    table_name = match.group(1)
                    drop_statement = f"DROP TABLE IF EXISTS {table_name};"

                    # 检查是否已经包含DROP TABLE语句
                    if not re.search(rf'\bDROP\s+TABLE\s+.*{table_name}', sql_query, re.IGNORECASE):
                        # 在CREATE TABLE前添加DROP TABLE语句
                        sql_query = drop_statement + "\n" + sql_query
                        logger.info(f"Added DROP TABLE statement for table: {table_name}")

            logger.info(f"Generated PostgreSQL SQL: {sql_query}")
            return sql_query

        except Exception as e:
            logger.error(f"Failed to parse natural language to PostgreSQL SQL: {str(e)}")
            raise Exception(f"PostgreSQL AI parsing failed: {str(e)}")
    
    def _extract_sql_from_response(self, response: str) -> str:
        """从AI响应中提取SQL语句"""
        try:
            # 移除可能的markdown代码块标记
            response = response.strip()
            if response.startswith('```sql'):
                response = response[6:]
            elif response.startswith('```'):
                response = response[3:]

            if response.endswith('```'):
                response = response[:-3]

            # 清理SQL语句
            sql_query = response.strip()

            # 移除注释行（以#或--开头的行）
            lines = sql_query.split('\n')
            cleaned_lines = []
            for line in lines:
                line = line.strip()
                # 跳过注释行
                if line.startswith('#') or line.startswith('--') or line.startswith('/*'):
                    continue
                # 移除行内注释
                if '#' in line:
                    line = line.split('#')[0].strip()
                if '--' in line:
                    line = line.split('--')[0].strip()
                if line:  # 只添加非空行
                    cleaned_lines.append(line)

            sql_query = ' '.join(cleaned_lines)

            # 确保SQL语句以分号结尾（如果不是以分号结尾）
            if sql_query and not sql_query.endswith(';'):
                sql_query += ';'

            return sql_query

        except Exception as e:
            logger.error(f"Failed to extract SQL from response: {str(e)}")
            return response.strip()
    
    async def analyze_postgres_query_intent(self, natural_query: str) -> Dict[str, Any]:
        """分析PostgreSQL查询意图"""
        try:
            prompt = f"""
请分析以下PostgreSQL自然语言查询的意图：

查询: {natural_query}

请分析并返回以下信息：
1. 操作类型 (SELECT/INSERT/UPDATE/DELETE/CREATE/ALTER/DROP等)
2. 涉及的表名称
3. 查询条件或操作内容
4. 是否需要抓包分析

请用简洁的文字描述。
            """

            result = self._call_deepseek(prompt)

            return {
                'query': natural_query,
                'analysis': result,
                'requires_capture': True  # 默认需要抓包
            }

        except Exception as e:
            logger.error(f"Failed to analyze PostgreSQL query intent: {str(e)}")
            return {
                'query': natural_query,
                'analysis': f"Analysis failed: {str(e)}",
                'requires_capture': True
            }
    
    async def generate_postgres_explanation(self, sql_query: str, result: Dict[str, Any]) -> str:
        """生成PostgreSQL查询结果的解释"""
        try:
            prompt = f"""
请解释以下PostgreSQL查询的执行结果：

SQL查询: {sql_query}
执行结果: {result}

请用自然语言解释：
1. 查询做了什么操作
2. 返回了什么结果
3. 结果的含义

请用简洁明了的中文回答。
            """

            explanation = self._call_deepseek(prompt)
            return explanation

        except Exception as e:
            logger.error(f"Failed to generate PostgreSQL explanation: {str(e)}")
            return f"无法生成解释: {str(e)}"
    
    async def suggest_postgres_optimizations(self, sql_query: str) -> List[str]:
        """建议PostgreSQL优化"""
        try:
            prompt = f"""
请分析以下PostgreSQL查询并提供优化建议：

SQL查询: {sql_query}

请提供：
1. 性能优化建议
2. 索引建议
3. 查询重写建议
4. PostgreSQL特有的优化建议（如使用EXPLAIN ANALYZE、分区表、物化视图等）

请用中文回答，每个建议用一行表示。
            """

            suggestions = self._call_deepseek(prompt)

            # 将建议按行分割
            return [line.strip() for line in suggestions.split('\n') if line.strip()]

        except Exception as e:
            logger.error(f"Failed to generate PostgreSQL suggestions: {str(e)}")
            return [f"无法生成建议: {str(e)}"]

# 创建全局实例
postgres_ai_service = PostgresAIService()

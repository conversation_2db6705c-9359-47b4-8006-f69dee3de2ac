import asyncio
import json
import subprocess
import tempfile
import os
import logging
from typing import Dict, Any, List, Optional
from pathlib import Path

logger = logging.getLogger(__name__)

class MongoShellService:
    """使用 mongosh 执行 MongoDB 查询的服务"""
    
    def __init__(self, connection_string: str = None):
        """
        初始化 MongoDB Shell 服务
        
        Args:
            connection_string: MongoDB 连接字符串，格式如: ************************:port/database
        """
        self.connection_string = connection_string or "********************************************************************************************"
        self.mongosh_path = self._find_mongosh()
        self.current_database = "test"  # 默认数据库
        
    def _find_mongosh(self) -> str:
        """查找 mongosh 可执行文件路径"""
        # 尝试几个常见路径
        possible_paths = [
            "/usr/local/bin/mongosh",
            "/usr/bin/mongosh",
            "mongosh"  # 如果在 PATH 中
        ]
        
        for path in possible_paths:
            try:
                result = subprocess.run([path, "--version"], 
                                      capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    logger.info(f"Found mongosh at: {path}")
                    return path
            except (subprocess.TimeoutExpired, FileNotFoundError):
                continue
                
        raise Exception("mongosh not found. Please install MongoDB Shell.")
    
    async def execute_mongo_query(self, query: str) -> Dict[str, Any]:
        """
        执行 MongoDB 查询
        
        Args:
            query: MongoDB 查询语句
            
        Returns:
            查询结果字典
        """
        try:
            # 预处理查询
            processed_query = self._preprocess_query(query)
            
            # 执行查询
            result = await self._execute_with_mongosh(processed_query)
            
            # 解析结果
            return self._parse_result(result, query)
            
        except Exception as e:
            logger.error(f"Failed to execute MongoDB query: {str(e)}")
            raise Exception(f"MongoDB query execution failed: {str(e)}")
    
    def _preprocess_query(self, query: str) -> str:
        """预处理查询语句"""
        query = query.strip().rstrip(';')
        
        # 处理 use 命令
        if query.startswith('use '):
            db_name = query[4:].strip()
            self.current_database = db_name
            return f"use {db_name}"
        
        # 处理 show 命令
        if query.startswith('show '):
            return query
        
        # 对于 db.runCommand({ping: 1}) 这样的测试命令，不需要切换数据库
        if 'ping' in query:
            return query
            
        # 确保查询以适当的数据库上下文执行
        if query.startswith('db.') and not query.startswith(f'use {self.current_database}'):
            # 如果查询使用 db. 但没有指定数据库，添加 use 命令
            return f"use {self.current_database}; {query}"
            
        return query
    
    async def _execute_with_mongosh(self, query: str) -> str:
        """使用 mongosh 执行查询"""
        try:
            # 创建临时脚本文件
            with tempfile.NamedTemporaryFile(mode='w', suffix='.js', delete=False) as temp_file:
                # 对于特殊命令，直接执行而不包装在 try-catch 中
                if any(cmd in query.lower() for cmd in ['use ', 'show ', 'db.runcommand']):
                    script_content = f"""
// 直接执行命令
{query}
print(JSON.stringify({{
    type: 'command',
    operation: '{self._extract_operation(query)}',
    acknowledged: true
}}));
"""
                else:
                    # 对于其他查询，使用 try-catch 包装
                    script_content = f"""
try {{
    // 执行查询
    let result = {query};
    
    // 格式化输出
    if (result !== null && result !== undefined) {{
        if (typeof result === 'object') {{
            if (result.acknowledged !== undefined) {{
                // 写入操作结果
                print(JSON.stringify({{
                    type: 'modification',
                    operation: '{self._extract_operation(query)}',
                    acknowledged: result.acknowledged,
                    insertedId: result.insertedId ? result.insertedId.toString() : undefined,
                    insertedIds: result.insertedIds ? Object.values(result.insertedIds).map(id => id.toString()) : undefined,
                    modifiedCount: result.modifiedCount,
                    deletedCount: result.deletedCount,
                    matchedCount: result.matchedCount,
                    upsertedCount: result.upsertedCount
                }}));
            }} else if (result.hasNext && typeof result.hasNext === 'function') {{
                // 游标结果
                let docs = [];
                while (result.hasNext() && docs.length < 20) {{
                    docs.push(result.next());
                }}
                print(JSON.stringify({{
                    type: 'query',
                    operation: 'find',
                    documents: docs,
                    count: docs.length
                }}));
            }} else if (Array.isArray(result)) {{
                // 数组结果
                print(JSON.stringify({{
                    type: 'query',
                    operation: '{self._extract_operation(query)}',
                    documents: result,
                    count: result.length
                }}));
            }} else {{
                // 其他对象结果
                print(JSON.stringify({{
                    type: 'command',
                    operation: '{self._extract_operation(query)}',
                    result: result
                }}));
            }}
        }} else {{
            // 基本类型结果
            print(JSON.stringify({{
                type: 'value',
                operation: '{self._extract_operation(query)}',
                value: result
            }}));
        }}
    }} else {{
        // null/undefined 结果
        print(JSON.stringify({{
            type: 'command',
            operation: '{self._extract_operation(query)}',
            acknowledged: true
        }}));
    }}
}} catch (error) {{
    print(JSON.stringify({{
        type: 'error',
        operation: '{self._extract_operation(query)}',
        error: error.message || error.toString()
    }}));
}}
"""
                
                temp_file.write(script_content)
                temp_file_path = temp_file.name
            
            # 执行 mongosh 命令
            cmd = [
                self.mongosh_path,
                self.connection_string,
                "--quiet",
                "--file", temp_file_path
            ]
            
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            # 清理临时文件
            try:
                os.unlink(temp_file_path)
            except:
                pass
            
            if process.returncode != 0:
                error_msg = stderr.decode() if stderr else "Unknown error"
                raise Exception(f"mongosh execution failed: {error_msg}")
            
            output = stdout.decode().strip()
            return output
            
        except Exception as e:
            logger.error(f"Failed to execute with mongosh: {str(e)}")
            raise
    
    def _extract_operation(self, query: str) -> str:
        """从查询中提取操作类型"""
        query = query.lower().strip()
        
        if 'insertone' in query:
            return 'insertOne'
        elif 'insertmany' in query:
            return 'insertMany'
        elif 'updateone' in query:
            return 'updateOne'
        elif 'updatemany' in query:
            return 'updateMany'
        elif 'deleteone' in query:
            return 'deleteOne'
        elif 'deletemany' in query:
            return 'deleteMany'
        elif 'find' in query:
            return 'find'
        elif 'aggregate' in query:
            return 'aggregate'
        elif 'count' in query:
            return 'count'
        elif 'distinct' in query:
            return 'distinct'
        elif 'show' in query:
            return 'show'
        elif 'use' in query:
            return 'use'
        elif 'starttransaction' in query:
            return 'startTransaction'
        elif 'committransaction' in query:
            return 'commitTransaction'
        elif 'aborttransaction' in query:
            return 'abortTransaction'
        elif 'startsession' in query:
            return 'startSession'
        elif 'bulkwrite' in query:
            return 'bulkWrite'
        elif 'sh.' in query:
            return 'shard'
        else:
            return 'command'
    
    def _parse_result(self, output: str, original_query: str) -> Dict[str, Any]:
        """解析 mongosh 输出结果"""
        try:
            if not output.strip():
                # 空输出，可能是成功的命令
                return {
                    'type': 'command',
                    'operation': self._extract_operation(original_query),
                    'acknowledged': True
                }
            
            # 尝试解析 JSON 输出
            try:
                result = json.loads(output)
                return result
            except json.JSONDecodeError:
                # 如果不是 JSON，尝试解析其他格式
                return self._parse_non_json_output(output, original_query)
                
        except Exception as e:
            logger.error(f"Failed to parse mongosh output: {str(e)}, output: {output}")
            return {
                'type': 'error',
                'operation': self._extract_operation(original_query),
                'error': f"Failed to parse output: {str(e)}",
                'raw_output': output
            }
    
    def _parse_non_json_output(self, output: str, original_query: str) -> Dict[str, Any]:
        """解析非 JSON 格式的输出"""
        lines = output.strip().split('\n')
        
        # 处理 show 命令的输出
        if original_query.startswith('show '):
            if 'databases' in original_query:
                # show databases 输出
                databases = []
                for line in lines:
                    if line.strip() and not line.startswith('admin') and not line.startswith('config'):
                        parts = line.split()
                        if len(parts) >= 2:
                            databases.append({
                                'name': parts[0],
                                'sizeOnDisk': parts[1] if len(parts) > 1 else '0'
                            })
                return {
                    'type': 'command',
                    'operation': 'listDatabases',
                    'databases': databases
                }
            elif 'collections' in original_query:
                # show collections 输出
                collections = [line.strip() for line in lines if line.strip()]
                return {
                    'type': 'command',
                    'operation': 'listCollections',
                    'collections': collections
                }
        
        # 默认返回原始输出
        return {
            'type': 'command',
            'operation': self._extract_operation(original_query),
            'acknowledged': True,
            'raw_output': output
        }
    
    async def test_connection(self) -> bool:
        """测试连接"""
        try:
            result = await self.execute_mongo_query("db.runCommand({ping: 1})")
            return result.get('type') != 'error'
        except:
            return False
    
    async def close(self):
        """关闭连接（对于 mongosh 服务来说不需要特殊处理）"""
        pass

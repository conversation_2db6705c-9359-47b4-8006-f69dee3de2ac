"""
Docker环境构建服务
"""

import logging
import re
import asyncio
import paramiko
import json
from typing import Dict, Any, Optional
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.server_config import ServerConfig
from services.ai_service import AIService
from utils.docker_network_utils import docker_network_utils

logger = logging.getLogger(__name__)

class DockerEnvironmentService:
    """Docker环境构建服务"""

    def __init__(self):
        self.ssh_client = None
        self.ai_service = AIService()

    async def build_database_environment(
        self,
        server_config: ServerConfig,
        ai_prompt: str,
        database_type: str
    ) -> Dict[str, Any]:
        """使用AI构建Docker数据库环境"""
        try:
            await self._connect_ssh(server_config)

            # 1. 使用AI生成Docker命令
            docker_command = await self._generate_docker_command(ai_prompt, database_type)

            # 2. 检查镜像是否存在
            image_name = self._extract_image_name(docker_command)
            if not await self._check_image_exists(image_name):
                logger.info(f"Image {image_name} not found, pulling...")
                await self._pull_image(image_name)

            # 3. 执行Docker命令
            result = await self._execute_docker_command(docker_command, database_type)

            # 4. 验证容器状态并获取实际容器名称
            container_info = await self._get_container_info(result['container_id'])

            # 使用实际的容器名称替换可能包含shell命令的名称
            actual_container_name = result['container_name']
            if container_info and 'name' in container_info:
                actual_container_name = container_info['name']

            return {
                'success': True,
                'container_name': actual_container_name,
                'container_id': result['container_id'],
                'port': result['port'],
                'username': result['username'],
                'password': result['password'],
                'database_name': result['database_name'],
                'docker_command': docker_command,
                'container_info': container_info,
                'message': f"Docker {database_type} environment created successfully"
            }

        except Exception as e:
            logger.error(f"Failed to build Docker environment: {str(e)}")
            return {
                'success': False,
                'container_name': '',
                'container_id': '',
                'port': 0,
                'username': '',
                'password': '',
                'database_name': '',
                'docker_command': '',
                'message': f"Failed to build environment: {str(e)}"
            }
        finally:
            await self._cleanup()

    async def build_database_environment_with_image(
        self,
        server_config: ServerConfig,
        image_repository: str,
        image_tag: str,
        database_type: str,
        ai_prompt: str = ""
    ) -> Dict[str, Any]:
        """使用指定镜像构建Docker数据库环境"""
        try:
            await self._connect_ssh(server_config)

            # 1. 构造镜像名称
            image_name = f"{image_repository}:{image_tag}"

            # 2. 检查镜像是否存在
            if not await self._check_image_exists(image_name):
                logger.info(f"Image {image_name} not found, pulling...")
                await self._pull_image(image_name)

            # 3. 生成Docker命令（使用指定镜像）
            docker_command = await self._generate_docker_command_with_image(
                image_name, database_type, ai_prompt
            )

            # 4. 执行Docker命令
            result = await self._execute_docker_command(docker_command, database_type)

            # 5. 验证容器状态并获取实际容器名称
            container_info = await self._get_container_info(result['container_id'])

            # 使用实际的容器名称替换可能包含shell命令的名称
            actual_container_name = result['container_name']
            if container_info and 'name' in container_info:
                actual_container_name = container_info['name']

            return {
                'success': True,
                'container_name': actual_container_name,
                'container_id': result['container_id'],
                'port': result['port'],
                'username': result['username'],
                'password': result['password'],
                'database_name': result['database_name'],
                'docker_command': docker_command,
                'container_info': container_info,
                'image_name': image_name,
                'message': f"Docker {database_type} environment created successfully with image {image_name}"
            }

        except Exception as e:
            logger.error(f"Failed to build Docker environment with image: {str(e)}")
            return {
                'success': False,
                'container_name': '',
                'container_id': '',
                'port': 0,
                'username': '',
                'password': '',
                'database_name': '',
                'docker_command': '',
                'container_info': None,
                'image_name': f"{image_repository}:{image_tag}",
                'message': f"Failed to build Docker environment with image: {str(e)}"
            }
        finally:
            await self._cleanup()

    async def _generate_docker_command(self, ai_prompt: str, database_type: str) -> str:
        """使用AI生成Docker命令"""
        try:
            import time
            timestamp = int(time.time())

            system_prompt = f"""你是一个Docker专家，需要根据用户需求生成{database_type}数据库的Docker运行命令。

要求：
1. 必须包含完整的docker run命令
2. 必须指定端口映射，使用标准端口（MySQL:3306, PostgreSQL:5432, MongoDB:27017）
3. 必须设置用户名和密码
4. 必须设置数据库名称（如果适用）
5. 必须使用官方镜像
6. 容器名称必须包含时间戳确保唯一性，格式：数据库类型-服务器-{timestamp}
7. 考虑数据持久化，使用数据卷
8. **重要：必须使用 --network bridge 参数确保容器连接到docker0网络，以便抓包工具能够捕获网络流量**

支持的数据库类型：
- mysql: 使用mysql:8.0.39镜像，端口3306
- postgresql: 使用pgvector/pgvector:pg17镜像，端口5432
- gaussdb: 使用镜像enmotech/opengauss,端口5432
- mongodb: 使用mongo:latest镜像，端口27017
- oracle: 使用registry.cn-hangzhou.aliyuncs.com/helowin/oracle_11g镜像，端口1521

请只返回docker run命令，不要包含其他解释。

示例格式（注意必须包含 --network bridge）：
MySQL: docker run -d --name mysql-server-{timestamp} --network bridge -p 3306:3306 -e MYSQL_ROOT_PASSWORD=password123 -e MYSQL_USER=tester -e MYSQL_PASSWORD=password123 -e MYSQL_DATABASE=testdb -v mysql_data_{timestamp}:/var/lib/mysql mysql:8.0
PostgreSQL: docker run -d --name postgres-server-{timestamp} --network bridge -p 5432:5432 -e POSTGRES_PASSWORD=password123 -e POSTGRES_DB=testdb -v postgres_data_{timestamp}:/var/lib/postgresql/data pgvector/pgvector:pg17
MongoDB: docker run -d --name mongo-server-{timestamp} --network bridge -p 27017:27017 -e MONGO_INITDB_ROOT_USERNAME=admin -e MONGO_INITDB_ROOT_PASSWORD=password123 -v mongo_data_{timestamp}:/data/db mongo:latest --bind_ip_all
Oracle: docker run -d --name oracle-server-{timestamp} --network bridge -p 1521:1521 -e ORACLE_SID=ORCL -e ORACLE_USER=system -e ORACLE_PWD=password123 -e ORACLE_ALLOW_REMOTE=true -v oracle_data_{timestamp}:/u01/app/oracle registry.cn-hangzhou.aliyuncs.com/helowin/oracle_11g
GaussDB: docker run -d --name gaussdb-server-{timestamp} --network bridge -p 5432:5432 -e GS_PASSWORD=Gauss@123456 -v gaussdb_data_{timestamp}:/var/lib/opengauss enmotech/opengauss:3.0.0
"""

            user_prompt = f"数据库类型: {database_type}\n用户需求: {ai_prompt}"

            response = await self.ai_service.generate_response(
                system_prompt=system_prompt,
                user_prompt=user_prompt
            )

            # 提取Docker命令
            response_text = response.strip()

            # 查找docker run命令
            docker_command = None
            lines = response_text.split('\n')
            for line in lines:
                line = line.strip()
                if line.startswith('docker run'):
                    docker_command = line
                    break

            # 如果没有找到完整的docker run命令，尝试从整个响应中提取
            if not docker_command:
                import re
                # 使用正则表达式查找docker run命令
                match = re.search(r'docker run[^\n]*', response_text, re.IGNORECASE)
                if match:
                    docker_command = match.group(0)

            if not docker_command:
                logger.error(f"AI response: {response_text}")
                # 尝试更宽松的匹配，查找包含docker的行
                for line in lines:
                    line = line.strip()
                    if 'docker' in line.lower() and ('run' in line.lower() or 'postgres' in line.lower()):
                        docker_command = line
                        break

                if not docker_command:
                    raise Exception(f"AI生成的命令格式不正确，无法找到docker run命令。AI响应: {response_text[:200]}...")

            # 确保Docker命令包含bridge网络配置
            docker_command = docker_network_utils.fix_common_network_issues(docker_command)

            # 若为MongoDB，强制添加副本集与认证参数，确保支持Change Streams
            if database_type and database_type.lower() == 'mongodb':
                docker_command = self._ensure_mongo_replset_and_auth(docker_command)

            logger.info(f"Generated Docker command: {docker_command}")
            return docker_command

        except Exception as e:
            logger.error(f"Failed to generate Docker command: {str(e)}")
            raise

    def _generate_gaussdb_password(self) -> str:
        """生成符合GaussDB要求的密码"""
        # GaussDB密码要求：至少8位，包含大写字母、小写字母、数字、特殊字符
        return "Gauss@123456"

    def _generate_oracle_docker_command(self, image_name: str, timestamp: int, ai_prompt: str = "") -> str:
        """生成Oracle专用的Docker命令，兼容多种Oracle镜像"""
        try:
            container_name = f"oracle-server-{timestamp}"

            # 解析用户需求中的端口、用户名和密码
            port = 1521
            username = "system"  # Oracle默认管理员用户，但可以被用户覆盖
            password = "Oracle123"  # 默认密码
            oracle_sid = "ORCL"  # 默认SID

            if ai_prompt:
                import re
                # 提取端口
                port_match = re.search(r'端口.*?(\d+)', ai_prompt)
                if port_match:
                    port = int(port_match.group(1))

                # 提取用户名
                username_match = re.search(r'用户名.*?([A-Za-z0-9_]+)', ai_prompt)
                if username_match:
                    username = username_match.group(1)

                # 提取密码
                password_match = re.search(r'密码.*?([A-Za-z0-9@#$%^&*()_+\-=\[\]{};:,.<>?/]+)', ai_prompt)
                if password_match:
                    password = password_match.group(1)

                # 提取SID
                sid_match = re.search(r'SID.*?([A-Za-z0-9_]+)', ai_prompt) or re.search(r'数据库.*?([A-Za-z0-9_]+)', ai_prompt)
                if sid_match:
                    oracle_sid = sid_match.group(1)

                logger.info(f"Oracle配置: 用户名={username}, 端口={port}, SID={oracle_sid}")

            # 根据不同镜像类型生成兼容的Docker命令
            if 'oracle11g' in image_name.lower() or 'helowin' in image_name.lower():
                # Oracle 11g镜像 (如 registry.cn-hangzhou.aliyuncs.com/helowin/oracle_11g)
                docker_command = (
                    f"docker run -d "
                    f"--name {container_name} "
                    f"--network bridge "
                    f"--privileged "
                    f"-p {port}:1521 "
                    f"-e ORACLE_SID={oracle_sid} "
                    f"-e ORACLE_PWD={password} "
                    f"-e ORACLE_USER={username} "
                    f"-e ORACLE_PASSWORD={password} "
                    f"-e ORACLE_ALLOW_REMOTE=true "
                    f"-v oracle_data_{timestamp}:/u01/app/oracle "
                    f"--shm-size=1g "
                    f"{image_name}"
                )
            elif 'oracle19c' in image_name.lower() or 'johnte' in image_name.lower():
                # Oracle 19c镜像 (如 johnte/oracle19c)
                docker_command = (
                    f"docker run -d "
                    f"--name {container_name} "
                    f"--network bridge "
                    f"--privileged "
                    f"-p {port}:1521 "
                    f"-e ORACLE_SID={oracle_sid} "
                    f"-e ORACLE_PWD={password} "
                    f"-e ORACLE_PASSWORD={password} "
                    f"-e ORACLE_USER={username} "  # 添加标准的Oracle用户环境变量
                    f"-e DB_PASSWD={password} "    # 兼容johnte/oracle19c镜像
                    f"-e DB_USER={username} "      # 兼容johnte/oracle19c镜像
                    f"-e ORACLE_ALLOW_REMOTE=true "
                    f"-e ORACLE_DISABLE_ASYNCH_IO=true "
                    f"-v oracle_data_{timestamp}:/u01/app/oracle "
                    f"-v oracle_logs_{timestamp}:/u01/app/oracle/diag "
                    f"--shm-size=1g "
                    f"{image_name}"
                )
            else:
                # 通用Oracle镜像配置
                docker_command = (
                    f"docker run -d "
                    f"--name {container_name} "
                    f"--network bridge "
                    f"--privileged "
                    f"-p {port}:1521 "
                    f"-e ORACLE_SID={oracle_sid} "
                    f"-e ORACLE_PWD={password} "
                    f"-e ORACLE_USER={username} "
                    f"-e ORACLE_PASSWORD={password} "
                    f"-e ORACLE_ALLOW_REMOTE=true "
                    f"-v oracle_data_{timestamp}:/u01/app/oracle "
                    f"--shm-size=1g "
                    f"{image_name}"
                )

            logger.info(f"Generated Oracle Docker command for {image_name}: {docker_command}")
            return docker_command

        except Exception as e:
            logger.error(f"Failed to generate Oracle Docker command: {str(e)}")
            raise
    def _generate_gaussdb_docker_command(self, image_name: str, timestamp: int, ai_prompt: str = "") -> str:
        try:
            gaussdb_password = self._generate_gaussdb_password()
            container_name = f"gaussdb-server-{timestamp}"

            # 解析用户需求中的端口和密码
            port = 5432
            username = "gaussdb"
            if ai_prompt:
                # 提取端口
                import re
                port_match = re.search(r'端口.*?(\d+)', ai_prompt)
                if port_match:
                    port = int(port_match.group(1))

                # 提取密码（如果用户指定了符合要求的密码）
                password_match = re.search(r'密码.*?([A-Za-z0-9@#$%^&*()_+\-=\[\]{};:,.<>?]+)', ai_prompt)
                if password_match:
                    user_password = password_match.group(1)
                    # 验证密码是否符合GaussDB要求
                    if (len(user_password) >= 8 and
                        re.search(r'[A-Z]', user_password) and
                        re.search(r'[a-z]', user_password) and
                        re.search(r'\d', user_password) and
                        re.search(r'[^A-Za-z0-9]', user_password)):
                        gaussdb_password = user_password
                        logger.info(f"Using user-specified password for GaussDB")
                    else:
                        logger.warning(f"User password doesn't meet GaussDB requirements, using default: {gaussdb_password}")

            # 构建优化的GaussDB Docker命令
            docker_command = (
                f"docker run -d "
                f"--name {container_name} "
                f"--network bridge "
                f"--privileged "
                f"-p {port}:5432 "
                f"-e GS_PASSWORD={gaussdb_password} "
                f"-e GS_USERNAME=gaussdb "
                f"-e GS_DATABASE=postgres "
                f"-e PGPORT=5432 "
                f"-e OPENGAUSS_USER=gaussdb "
                f"-e OPENGAUSS_PASSWORD={gaussdb_password} "
                f"-v gaussdb_data_{timestamp}:/var/lib/opengauss "
                f"-v gaussdb_logs_{timestamp}:/var/log/opengauss "
                f"--shm-size=256m "
                f"--ulimit nofile=1048576:1048576 "
                f"{image_name}"
            )

            logger.info(f"Generated optimized GaussDB Docker command: {docker_command}")
            return docker_command

        except Exception as e:
            logger.error(f"Failed to generate GaussDB Docker command: {str(e)}")
            raise

    async def _generate_generic_docker_command(self, image_name: str, database_type: str, timestamp: int, ai_prompt: str = "") -> str:
        """为其他数据库类型生成Docker命令"""
        try:
            system_prompt = f"""你是一个Docker专家，需要根据用户需求生成{database_type}数据库的Docker运行命令。

要求：
1. 必须使用指定的镜像: {image_name}
2. 必须包含完整的docker run命令
3. 必须指定端口映射，使用标准端口（MySQL:3306, PostgreSQL:5432, MongoDB:27017, Oracle:1521）
4. 必须设置用户名和密码
5. 必须设置数据库名称（如果适用）
6. 容器名称必须包含时间戳确保唯一性，格式：数据库类型-server-{timestamp}
7. 考虑数据持久化，使用数据卷
8. **重要：必须使用 --network bridge 参数确保容器连接到docker0网络，以便抓包工具能够捕获网络流量**

请只返回docker run命令，不要包含其他解释。

示例格式（注意必须包含 --network bridge 和使用指定镜像）：
docker run -d --name {database_type}-server-{timestamp} --network bridge -p [端口]:[端口] [环境变量] -v [数据卷] {image_name}"""

            user_prompt = f"数据库类型: {database_type}\n镜像: {image_name}\n用户需求: {ai_prompt if ai_prompt else '创建标准的数据库容器'}"

            response = await self.ai_service.generate_response(
                system_prompt=system_prompt,
                user_prompt=user_prompt
            )

            # 提取Docker命令
            response_text = response.strip()

            # 查找docker run命令
            docker_command = None
            lines = response_text.split('\n')
            for line in lines:
                line = line.strip()
                if line.startswith('docker run'):
                    docker_command = line
                    break

            # 如果没有找到完整的docker run命令，尝试从整个响应中提取
            if not docker_command:
                import re
                # 使用正则表达式查找docker run命令
                match = re.search(r'docker run[^\n]*', response_text, re.IGNORECASE)
                if match:
                    docker_command = match.group(0)

            if not docker_command:
                logger.error(f"AI response: {response_text}")
                raise Exception(f"AI生成的命令格式不正确，无法找到docker run命令。AI响应: {response_text[:200]}...")

            # 确保Docker命令包含bridge网络配置
            if '--network' not in docker_command:
                # 在docker run后面添加--network bridge参数
                docker_command = docker_command.replace('docker run', 'docker run --network bridge')

            # 若为MongoDB镜像或数据库类型为mongodb，则确保副本集与认证参数
            if (database_type and database_type.lower() == 'mongodb') or ('mongo' in image_name.lower()):
                docker_command = self._ensure_mongo_replset_and_auth(docker_command)

            logger.info(f"Generated Docker command with image {image_name}: {docker_command}")
            return docker_command

        except Exception as e:
            logger.error(f"Failed to generate generic Docker command: {str(e)}")
            raise

    async def _generate_docker_command_with_image(self, image_name: str, database_type: str, ai_prompt: str = "") -> str:
        """使用指定镜像生成Docker命令"""
        try:
            import time
            timestamp = int(time.time())

            # 为不同数据库类型生成特定的Docker命令
            if database_type.lower() == 'gaussdb' or 'gauss' in image_name.lower():
                return self._generate_gaussdb_docker_command(image_name, timestamp, ai_prompt)
            elif database_type.lower() == 'oracle' or 'oracle' in image_name.lower():
                return self._generate_oracle_docker_command(image_name, timestamp, ai_prompt)
            else:
                # 其他数据库类型使用AI生成
                return await self._generate_generic_docker_command(image_name, database_type, timestamp, ai_prompt)

        except Exception as e:
            logger.error(f"Failed to generate Docker command with image: {str(e)}")
            raise

    def _ensure_mongo_replset_and_auth(self, docker_command: str) -> str:
        """确保MongoDB容器以副本集方式启动并具备认证参数。
        - 添加 --replSet rs0 --bind_ip_all
        - 补全 MONGO_INITDB_ROOT_USERNAME/PASSWORD
        通过在镜像名之前插入 -e 环境变量，避免误替换 volume 名称。
        """
        try:
            parts = docker_command.split()
            image_name = self._extract_image_name(docker_command)
            # 找到镜像名在命令中的索引（最后一次出现）
            idx = None
            for i in range(len(parts) - 1, -1, -1):
                if parts[i] == image_name or parts[i].startswith(image_name.split(':')[0]):
                    idx = i
                    break
            if idx is None:
                return docker_command

            # 构建需要插入的环境变量
            insert_envs = []
            lower_cmd = docker_command.lower()
            if 'mongo_initdb_root_username' not in lower_cmd:
                insert_envs += ['-e', 'MONGO_INITDB_ROOT_USERNAME=admin']
            if 'mongo_initdb_root_password' not in lower_cmd:
                insert_envs += ['-e', 'MONGO_INITDB_ROOT_PASSWORD=password123']

            if insert_envs:
                parts = parts[:idx] + insert_envs + parts[idx:]

            # 重新拼接命令
            cmd = ' '.join(parts)

            # 对于MongoDB，当启用认证时，不要同时启用副本集，避免keyFile错误
            # 只添加bind_ip_all参数，确保可以从外部连接
            if '--bind_ip_all' not in lower_cmd:
                cmd = cmd + ' --bind_ip_all'

            return cmd
        except Exception:
            # 出现问题时返回原命令，避免阻塞
            return docker_command


    def _extract_image_name(self, docker_command: str) -> str:
        """从Docker命令中提取镜像名称"""
        try:
            # 分割命令为参数列表
            parts = docker_command.split()

            # 找到最后一个不以-开头的参数，通常就是镜像名
            image_name = None
            for i in range(len(parts) - 1, -1, -1):
                part = parts[i]
                # 跳过选项和选项值
                if not part.startswith('-') and '=' not in part:
                    # 检查是否看起来像镜像名
                    if '/' in part or ':' in part or part in ['mysql', 'postgres', 'mongo', 'redis', 'nginx']:
                        image_name = part
                        break
                    # 如果是常见的数据库镜像名
                    elif part in ['mysql', 'postgresql', 'mongodb', 'redis']:
                        image_name = part
                        break

            if not image_name:
                # 备用方案：查找常见的镜像名模式
                for part in parts:
                    if any(db in part.lower() for db in ['mysql', 'postgres', 'mongo']):
                        if not part.startswith('-'):
                            image_name = part
                            break

            if image_name:
                # 如果没有标签，添加latest
                if ':' not in image_name:
                    image_name += ':latest'
                return image_name
            else:
                raise Exception("无法从Docker命令中提取镜像名称")

        except Exception as e:
            logger.error(f"Failed to extract image name from command: {docker_command}")
            logger.error(f"Error: {str(e)}")
            raise

    async def _check_image_exists(self, image_name: str) -> bool:
        """检查Docker镜像是否存在"""
        try:
            # 方法1: 使用docker images命令检查
            cmd = f"docker images {image_name} --format '{{{{.Repository}}}}:{{{{.Tag}}}}'"
            stdin, stdout, stderr = self.ssh_client.exec_command(cmd)
            output = stdout.read().decode('utf-8').strip()

            if output:
                logger.info(f"Image {image_name} found using docker images command")
                return True

            # 方法2: 如果方法1失败，尝试使用docker inspect
            cmd2 = f"docker inspect {image_name} > /dev/null 2>&1 && echo 'exists'"
            stdin, stdout, stderr = self.ssh_client.exec_command(cmd2)
            output2 = stdout.read().decode('utf-8').strip()

            if output2 == 'exists':
                logger.info(f"Image {image_name} found using docker inspect command")
                return True

            # 方法3: 检查是否有类似的镜像（处理tag问题）
            repository = image_name.split(':')[0] if ':' in image_name else image_name
            cmd3 = f"docker images {repository} --format '{{{{.Repository}}}}:{{{{.Tag}}}}'"
            stdin, stdout, stderr = self.ssh_client.exec_command(cmd3)
            output3 = stdout.read().decode('utf-8').strip()

            if output3:
                logger.info(f"Found similar images for {repository}: {output3}")
                # 检查是否包含我们要找的镜像
                if image_name in output3:
                    logger.info(f"Image {image_name} found in similar images list")
                    return True

            logger.info(f"Image {image_name} not found")
            return False

        except Exception as e:
            logger.error(f"Failed to check image existence: {str(e)}")
            return False

    async def _pull_image(self, image_name: str):
        """拉取Docker镜像"""
        try:
            cmd = f"docker pull {image_name}"
            logger.info(f"Pulling Docker image: {image_name}")

            stdin, stdout, stderr = self.ssh_client.exec_command(cmd)

            # 等待拉取完成
            exit_status = stdout.channel.recv_exit_status()

            if exit_status != 0:
                error_output = stderr.read().decode('utf-8')
                raise Exception(f"Failed to pull image: {error_output}")

            logger.info(f"Successfully pulled image: {image_name}")

        except Exception as e:
            logger.error(f"Failed to pull image: {str(e)}")
            raise

    async def _execute_docker_command(self, docker_command: str, database_type: str = None) -> Dict[str, Any]:
        """执行Docker命令，包含容器状态检查和自动清理"""
        try:
            # 解析Docker命令以提取信息
            parsed_info = self._parse_docker_command(docker_command, database_type)
            container_name = parsed_info.get('container_name', '')
            port = parsed_info.get('port', 0)

            # 1. 清理所有Created状态的容器
            await self._cleanup_created_containers()

            # 2. 清理所有Exited状态的容器
            await self._cleanup_exited_containers()

            # 3. 如果是PostgreSQL，清理可能损坏的数据卷
            # 如果没有传递database_type参数，则从Docker命令中推断
            if database_type is None:
                database_type = self._infer_database_type(docker_command)
            if database_type and database_type.lower() == 'postgresql':
                await self._cleanup_postgresql_volumes()

            # 4. 检查端口冲突并自动选择可用端口
            if port > 0:
                available_port = await self._find_available_port(port)
                if available_port != port:
                    logger.info(f"Port {port} is occupied, using alternative port {available_port}")
                    # 更新Docker命令中的端口映射
                    docker_command = self._update_port_in_command(docker_command, port, available_port)
                    parsed_info['port'] = available_port
                    port = available_port
                await self._handle_port_conflict(port)

            # 5. 检查并清理同名容器
            if container_name:
                await self._cleanup_existing_container(container_name)

            logger.info(f"Executing Docker command: {docker_command}")

            # 6. 执行Docker命令
            stdin, stdout, stderr = self.ssh_client.exec_command(docker_command)
            exit_status = stdout.channel.recv_exit_status()

            if exit_status != 0:
                error_output = stderr.read().decode('utf-8')
                logger.error(f"Docker command failed: {error_output}")
                raise Exception(f"Docker command failed: {error_output}")

            container_id = stdout.read().decode('utf-8').strip()
            parsed_info['container_id'] = container_id

            # 7. 等待容器启动并检查状态
            # 对于GaussDB，需要更长的启动时间
            if database_type and database_type.lower() == 'gaussdb':
                logger.info("GaussDB container starting, waiting 10 seconds...")
                await asyncio.sleep(10)
            else:
                await asyncio.sleep(3)  # 等待容器启动

            container_status = await self._check_container_status(container_id)

            if container_status == 'created':
                logger.warning(f"Container {container_id} is in Created state, attempting to start it...")
                # 尝试启动容器
                start_cmd = f"docker start {container_id}"
                stdin, stdout, stderr = self.ssh_client.exec_command(start_cmd)
                start_exit_status = stdout.channel.recv_exit_status()

                if start_exit_status != 0:
                    start_error = stderr.read().decode('utf-8')
                    logger.error(f"Failed to start container {container_id}: {start_error}")
                    await self._remove_container(container_id)
                    raise Exception(f"Container failed to start: {start_error}")

                # 再次检查状态
                await asyncio.sleep(2)
                container_status = await self._check_container_status(container_id)

            if container_status != 'running':
                logger.warning(f"Container {container_id} is not running (status: {container_status})")
                # 获取容器日志
                logs = await self._get_container_logs(container_id)
                logger.error(f"Container logs: {logs}")

                # 对于GaussDB容器，如果状态不是running但也不是exited，给它更多时间
                if database_type and database_type.lower() == 'gaussdb' and container_status not in ['exited', 'dead']:
                    logger.info("GaussDB container may still be initializing, waiting additional 20 seconds...")
                    await asyncio.sleep(20)
                    container_status = await self._check_container_status(container_id)

                    if container_status == 'running':
                        logger.info(f"GaussDB container {container_id} started successfully after extended wait")
                    elif container_status not in ['exited', 'dead']:
                        logger.warning(f"GaussDB container {container_id} status: {container_status}, proceeding anyway...")
                    else:
                        # 删除失败的容器
                        await self._remove_container(container_id)
                        raise Exception(f"GaussDB container failed to start. Status: {container_status}. Logs: {logs}")
                else:
                    # 删除失败的容器
                    await self._remove_container(container_id)
                    raise Exception(f"Container failed to start properly. Status: {container_status}. Logs: {logs}")

            logger.info(f"Docker container created and running: {container_id}")

            # 特殊处理：Oracle容器启动后的用户设置
            if database_type and database_type.lower() == 'oracle':
                await self._setup_oracle_users(parsed_info)

            return parsed_info

        except Exception as e:
            logger.error(f"Failed to execute Docker command: {str(e)}")
            raise

    async def _setup_oracle_users(self, parsed_info: Dict[str, Any]) -> None:
        """设置Oracle用户，支持自定义用户名和密码"""
        try:
            container_name = parsed_info.get('container_name', '')
            username = parsed_info.get('username', 'system')
            password = parsed_info.get('password', 'Oracle123')

            if not container_name:
                logger.warning("No container name provided for Oracle user setup")
                return

            logger.info(f"Setting up Oracle users for container {container_name} with username: {username}")

            # 等待Oracle数据库完全启动（Oracle需要更长时间）
            await asyncio.sleep(30)

            # Oracle用户设置命令
            setup_commands = [
                f"export ORACLE_SID=ORCL",
                # 先解锁并设置system用户密码
                f"echo \"ALTER USER system ACCOUNT UNLOCK;\" | sqlplus -S / as sysdba",
                f"echo \"ALTER USER system IDENTIFIED BY {password};\" | sqlplus -S / as sysdba",
            ]

            # 如果用户名不是system，创建自定义用户
            if username.lower() != 'system':
                logger.info(f"Creating custom Oracle user: {username}")
                setup_commands.extend([
                    # 检查用户是否存在，如果存在则删除
                    f"echo \"DROP USER {username} CASCADE;\" | sqlplus -S system/{password} || true",
                    # 创建新用户
                    f"echo \"CREATE USER {username} IDENTIFIED BY {password};\" | sqlplus -S system/{password}",
                    # 授予必要权限
                    f"echo \"GRANT CONNECT, RESOURCE, DBA TO {username};\" | sqlplus -S system/{password}",
                    f"echo \"GRANT CREATE SESSION TO {username};\" | sqlplus -S system/{password}",
                    f"echo \"GRANT UNLIMITED TABLESPACE TO {username};\" | sqlplus -S system/{password}",
                    # 解锁用户账户
                    f"echo \"ALTER USER {username} ACCOUNT UNLOCK;\" | sqlplus -S system/{password}",
                ])
                logger.info(f"Custom Oracle user '{username}' will be created with full privileges")

            for cmd in setup_commands:
                # 修复shell命令转义问题 - 使用双引号并正确转义内部引号
                escaped_cmd = cmd.replace('"', '\\"')
                full_cmd = f'docker exec {container_name} bash -c "{escaped_cmd}"'
                logger.info(f"Executing Oracle setup command: {escaped_cmd}")

                stdin, stdout, stderr = self.ssh_client.exec_command(full_cmd)
                exit_status = stdout.channel.recv_exit_status()

                if exit_status != 0:
                    error_output = stderr.read().decode('utf-8')
                    logger.warning(f"Oracle setup command failed (non-critical): {error_output}")
                else:
                    output = stdout.read().decode('utf-8')
                    logger.info(f"Oracle setup command output: {output}")

            logger.info(f"Oracle user setup completed for container {container_name}, username: {username}")

        except Exception as e:
            logger.error(f"Failed to setup Oracle users: {str(e)}")
            # 不抛出异常，因为这是非关键操作

    def _parse_docker_command(self, docker_command: str, database_type: str = None) -> Dict[str, Any]:
        """解析Docker命令提取配置信息"""
        try:
            result = {
                'container_name': '',
                'port': 0,
                'username': 'root',
                'password': '',
                'database_name': ''
            }

            # 提取容器名称
            name_match = re.search(r'--name\s+([^\s]+)', docker_command)
            if name_match:
                result['container_name'] = name_match.group(1)

            # 提取端口映射
            port_match = re.search(r'-p\s+(\d+):\d+', docker_command)
            if port_match:
                result['port'] = int(port_match.group(1))

            # 提取环境变量
            env_patterns = {
                'password': [
                    r'-e\s+MYSQL_ROOT_PASSWORD=([^\s]+)',
                    r'-e\s+POSTGRES_PASSWORD=([^\s]+)',
                    r'-e\s+MONGO_INITDB_ROOT_PASSWORD=([^\s]+)',
                    r'-e\s+ORACLE_PASSWORD=([^\s]+)',
                    r'-e\s+ORACLE_PWD=([^\s]+)',  # Oracle密码（PWD格式）
                    r'-e\s+GS_PASSWORD=([^\s]+)',  # GaussDB密码
                    r'--env\s+MYSQL_ROOT_PASSWORD=([^\s]+)',
                    r'--env\s+POSTGRES_PASSWORD=([^\s]+)',
                    r'--env\s+MONGO_INITDB_ROOT_PASSWORD=([^\s]+)',
                    r'--env\s+ORACLE_PASSWORD=([^\s]+)',
                    r'--env\s+ORACLE_PWD=([^\s]+)',  # Oracle密码（PWD格式）
                    r'--env\s+GS_PASSWORD=([^\s]+)'  # GaussDB密码
                ],
                'database_name': [
                    r'-e\s+MYSQL_DATABASE=([^\s]+)',
                    r'-e\s+POSTGRES_DB=([^\s]+)',
                    r'-e\s+MONGO_INITDB_DATABASE=([^\s]+)',
                    r'-e\s+ORACLE_SID=([^\s]+)',
                    r'-e\s+GS_DATABASE=([^\s]+)',  # GaussDB数据库名
                    r'--env\s+MYSQL_DATABASE=([^\s]+)',
                    r'--env\s+POSTGRES_DB=([^\s]+)',
                    r'--env\s+MONGO_INITDB_DATABASE=([^\s]+)',
                    r'--env\s+ORACLE_SID=([^\s]+)',
                    r'--env\s+GS_DATABASE=([^\s]+)'  # GaussDB数据库名
                ],
                'username': [
                    r'-e\s+MYSQL_USER=([^\s]+)',
                    r'-e\s+POSTGRES_USER=([^\s]+)',
                    r'-e\s+MONGO_INITDB_ROOT_USERNAME=([^\s]+)',
                    r'-e\s+ORACLE_USER=([^\s]+)',  # Oracle用户（ORACLE_USER格式）
                    r'-e\s+GS_USERNAME=([^\s]+)',  # GaussDB用户名
                    r'-e\s+DB_USER=([^\s]+)',      # 兼容其他Oracle镜像的用户名格式
                    r'--env\s+MYSQL_USER=([^\s]+)',
                    r'--env\s+POSTGRES_USER=([^\s]+)',
                    r'--env\s+MONGO_INITDB_ROOT_USERNAME=([^\s]+)',
                    r'--env\s+ORACLE_USER=([^\s]+)',  # Oracle用户（ORACLE_USER格式）
                    r'--env\s+GS_USERNAME=([^\s]+)',  # GaussDB用户名
                    r'--env\s+DB_USER=([^\s]+)'       # 兼容其他Oracle镜像的用户名格式
                ]
            }

            for key, patterns in env_patterns.items():
                for pattern in patterns:
                    match = re.search(pattern, docker_command)
                    if match:
                        result[key] = match.group(1)
                        break

            # 设置默认值
            if not result['password']:
                # 根据数据库类型设置默认密码
                if 'gauss' in docker_command.lower():
                    result['password'] = self._generate_gaussdb_password()  # GaussDB符合要求的密码
                else:
                    result['password'] = 'password123'  # 其他数据库默认密码

            if not result['database_name']:
                # 根据数据库类型设置默认数据库名
                if 'mysql' in docker_command.lower():
                    result['database_name'] = 'testdb'  # MySQL使用专用数据库，避免权限问题
                elif 'postgres' in docker_command.lower() or 'gauss' in docker_command.lower():
                    result['database_name'] = 'postgres'
                elif 'mongo' in docker_command.lower():
                    result['database_name'] = 'admin'
                elif 'oracle' in docker_command.lower():
                    result['database_name'] = 'helowin'  # Oracle默认SID
                else:
                    result['database_name'] = 'test'

            # 根据数据库类型设置默认用户名（但不强制覆盖已解析的用户名）
            if not result['username'] or result['username'] == 'root':
                if 'postgres' in docker_command.lower():
                    result['username'] = 'postgres'
                elif 'gauss' in docker_command.lower():
                    result['username'] = 'gaussdb'  # GaussDB默认用户
                elif 'mongo' in docker_command.lower():
                    result['username'] = 'admin'
                elif 'oracle' in docker_command.lower():
                    # Oracle: 如果已经从环境变量中解析到用户名，则保持；否则使用system作为默认值
                    if not result['username'] or result['username'] == 'root':
                        result['username'] = 'system'  # Oracle默认用户
                else:
                    result['username'] = 'root'

            # 特殊处理：对于GaussDB，强制使用正确的用户名
            if database_type == 'gaussdb' or 'gauss' in docker_command.lower():
                result['username'] = 'gaussdb'  # GaussDB远程连接必须使用gaussdb用户，不能使用omm用户

            return result

        except Exception as e:
            logger.error(f"Failed to parse Docker command: {str(e)}")
            raise

    def _infer_database_type(self, docker_command: str) -> str:
        """从Docker命令中推断数据库类型"""
        try:
            docker_command_lower = docker_command.lower()

            # 检查镜像名称来推断数据库类型
            if 'mysql' in docker_command_lower:
                return 'mysql'
            elif 'postgres' in docker_command_lower or 'pgvector' in docker_command_lower:
                return 'postgresql'
            elif 'gaussdb' in docker_command_lower or 'gauss' in docker_command_lower:
                return 'gaussdb'
            elif 'mongo' in docker_command_lower:
                return 'mongodb'
            elif 'oracle' in docker_command_lower:
                return 'oracle'
            else:
                # 默认返回unknown，不会触发特殊清理逻辑
                return 'unknown'

        except Exception as e:
            logger.warning(f"Failed to infer database type from command: {str(e)}")
            return 'unknown'

    async def _get_container_info(self, container_name: str) -> Dict[str, Any]:
        """获取容器信息"""
        try:
            cmd = f"docker inspect {container_name}"
            stdin, stdout, stderr = self.ssh_client.exec_command(cmd)

            output = stdout.read().decode('utf-8').strip()
            error_output = stderr.read().decode('utf-8').strip()

            if error_output:
                logger.warning(f"Docker inspect stderr: {error_output}")

            if not output:
                logger.warning(f"Empty output from docker inspect {container_name}")
                return {}

            try:
                container_info = json.loads(output)[0]
            except (json.JSONDecodeError, IndexError) as e:
                logger.error(f"Failed to parse docker inspect output: {e}")
                logger.debug(f"Raw output: {output[:200]}...")
                return {}

            return {
                'id': container_info['Id'][:12],
                'name': container_info['Name'].lstrip('/'),
                'status': container_info['State']['Status'],
                'ip_address': container_info.get('NetworkSettings', {}).get('IPAddress', ''),
                'ports': container_info.get('NetworkSettings', {}).get('Ports', {})
            }

        except Exception as e:
            logger.error(f"Failed to get container info: {str(e)}")
            return {}

    async def _connect_ssh(self, server_config: ServerConfig):
        """建立SSH连接"""
        try:
            if self.ssh_client:
                try:
                    self.ssh_client.exec_command('echo test', timeout=5)
                    return
                except:
                    self.ssh_client.close()
                    self.ssh_client = None

            self.ssh_client = paramiko.SSHClient()
            self.ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())

            logger.info(f"Connecting to server: {server_config.host}:{server_config.port}")
            self.ssh_client.connect(
                hostname=server_config.host,
                port=server_config.port,
                username=server_config.username,
                password=server_config.password,
                timeout=10
            )

            logger.info("SSH connection established successfully")

        except Exception as e:
            logger.error(f"Failed to establish SSH connection: {str(e)}")
            raise Exception(f"SSH connection failed: {str(e)}")

    async def _cleanup(self):
        """清理SSH连接"""
        try:
            if self.ssh_client:
                self.ssh_client.close()
                self.ssh_client = None
        except Exception as e:
            logger.debug(f"Failed to cleanup SSH connection: {str(e)}")

    async def _cleanup_created_containers(self):
        """清理所有Created状态的容器"""
        try:
            # 查找所有Created状态的容器
            check_cmd = "docker ps -a | grep 'Created' | awk '{print $1}'"
            stdin, stdout, stderr = self.ssh_client.exec_command(check_cmd)
            output = stdout.read().decode('utf-8').strip()

            if output:
                container_ids = output.split('\n')
                container_ids = [cid.strip() for cid in container_ids if cid.strip()]

                if container_ids:
                    logger.info(f"Found {len(container_ids)} containers in Created state, removing them...")

                    # 批量删除Created状态的容器
                    for container_id in container_ids:
                        if container_id:
                            logger.info(f"Removing Created container: {container_id}")
                            await self._remove_container(container_id)

                    logger.info("All Created containers have been removed")
                else:
                    logger.debug("No containers in Created state found")
            else:
                logger.debug("No containers in Created state found")

        except Exception as e:
            logger.error(f"Failed to cleanup Created containers: {str(e)}")

    async def _cleanup_exited_containers(self):
        """清理所有Exited状态的容器"""
        try:
            # 查找所有Exited状态的容器
            check_cmd = "docker ps -a | grep 'Exited' | awk '{print $1}'"
            stdin, stdout, stderr = self.ssh_client.exec_command(check_cmd)
            output = stdout.read().decode('utf-8').strip()

            if output:
                container_ids = output.split('\n')
                container_ids = [cid.strip() for cid in container_ids if cid.strip()]

                if container_ids:
                    logger.info(f"Found {len(container_ids)} containers in Exited state, removing them...")

                    # 批量删除Exited状态的容器
                    for container_id in container_ids:
                        if container_id:
                            logger.info(f"Removing Exited container: {container_id}")
                            await self._remove_container(container_id)

                    logger.info("All Exited containers have been removed")
                else:
                    logger.debug("No containers in Exited state found")
            else:
                logger.debug("No containers in Exited state found")

        except Exception as e:
            logger.error(f"Failed to cleanup Exited containers: {str(e)}")

    async def _cleanup_postgresql_volumes(self):
        """清理未使用的PostgreSQL相关数据卷，防止数据库文件版本冲突"""
        try:
            logger.info("Cleaning up unused PostgreSQL data volumes...")

            # 清理所有未使用的数据卷（包括PostgreSQL相关的）
            prune_cmd = "docker volume prune -f"
            stdin, stdout, stderr = self.ssh_client.exec_command(prune_cmd)
            stdout.channel.recv_exit_status()  # 等待命令完成

            logger.info("Unused PostgreSQL data volumes cleanup completed")

        except Exception as e:
            logger.warning(f"Failed to cleanup PostgreSQL volumes: {str(e)}")

    async def _handle_port_conflict(self, port: int):
        """处理端口冲突"""
        try:
            # 检查端口是否被占用 - 使用更宽松的匹配模式
            check_cmd = f"docker ps --format '{{{{.Names}}}}\\t{{{{.Ports}}}}' | grep '{port}' || true"
            stdin, stdout, stderr = self.ssh_client.exec_command(check_cmd)
            output = stdout.read().decode('utf-8').strip()

            if output:
                logger.warning(f"Port {port} is in use by existing containers:")
                logger.warning(output)

                # 提取使用该端口的容器名称
                lines = output.split('\n')
                for line in lines:
                    if str(port) in line and '\t' in line:
                        parts = line.split('\t')
                        if len(parts) >= 2:
                            container_name = parts[0].strip()
                            if container_name and container_name != 'NAMES':
                                logger.info(f"Stopping container {container_name} that uses port {port}")
                                await self._remove_container(container_name)

            # 额外检查：使用 docker port 命令查找占用端口的容器
            port_check_cmd = f"docker ps -q | xargs -I {{}} docker port {{}} | grep ':{port}' | head -5 || true"
            stdin, stdout, stderr = self.ssh_client.exec_command(port_check_cmd)
            port_output = stdout.read().decode('utf-8').strip()

            if port_output:
                logger.warning(f"Additional port check found containers using port {port}")
                # 获取这些容器的名称并停止
                get_containers_cmd = f"docker ps --format '{{{{.Names}}}}' | head -10"
                stdin, stdout, stderr = self.ssh_client.exec_command(get_containers_cmd)
                container_names = stdout.read().decode('utf-8').strip().split('\n')

                for container_name in container_names:
                    if container_name.strip():
                        # 检查这个容器是否使用了目标端口
                        check_container_port_cmd = f"docker port {container_name.strip()} | grep ':{port}' || true"
                        stdin, stdout, stderr = self.ssh_client.exec_command(check_container_port_cmd)
                        container_port_output = stdout.read().decode('utf-8').strip()

                        if container_port_output:
                            logger.info(f"Stopping container {container_name.strip()} that uses port {port}")
                            await self._remove_container(container_name.strip())

        except Exception as e:
            logger.error(f"Failed to handle port conflict for port {port}: {str(e)}")

    async def _find_available_port(self, preferred_port: int, max_attempts: int = 10) -> int:
        """查找可用端口，如果首选端口被占用则尝试相邻端口"""
        try:
            for i in range(max_attempts):
                test_port = preferred_port + i

                # 检查端口是否被Docker容器占用
                check_cmd = f"docker ps --format '{{{{.Ports}}}}' | grep ':{test_port}->' || true"
                stdin, stdout, stderr = self.ssh_client.exec_command(check_cmd)
                output = stdout.read().decode('utf-8').strip()

                if not output:
                    # 端口未被Docker容器占用，进一步检查系统端口
                    netstat_cmd = f"netstat -tuln | grep ':{test_port} ' || true"
                    stdin, stdout, stderr = self.ssh_client.exec_command(netstat_cmd)
                    netstat_output = stdout.read().decode('utf-8').strip()

                    if not netstat_output:
                        logger.info(f"Found available port: {test_port}")
                        return test_port
                    else:
                        logger.debug(f"Port {test_port} is occupied by system process")
                else:
                    logger.debug(f"Port {test_port} is occupied by Docker container")

            # 如果找不到可用端口，返回一个较大的端口号
            fallback_port = preferred_port + 1000
            logger.warning(f"Could not find available port near {preferred_port}, using fallback port {fallback_port}")
            return fallback_port

        except Exception as e:
            logger.error(f"Failed to find available port: {e}")
            return preferred_port

    def _update_port_in_command(self, docker_command: str, old_port: int, new_port: int) -> str:
        """更新Docker命令中的端口映射"""
        try:
            import re
            # 匹配 -p old_port:container_port 格式
            pattern = rf'-p\s+{old_port}:(\d+)'
            match = re.search(pattern, docker_command)

            if match:
                container_port = match.group(1)
                old_mapping = f"-p {old_port}:{container_port}"
                new_mapping = f"-p {new_port}:{container_port}"
                updated_command = docker_command.replace(old_mapping, new_mapping)
                logger.info(f"Updated port mapping from {old_mapping} to {new_mapping}")
                return updated_command
            else:
                logger.warning(f"Could not find port mapping pattern for port {old_port} in command")
                return docker_command

        except Exception as e:
            logger.error(f"Failed to update port in command: {e}")
            return docker_command

    async def _cleanup_existing_container(self, container_name: str):
        """清理现有的同名容器"""
        try:
            # 检查容器是否存在
            check_cmd = f"docker ps -a --filter name=^{container_name}$ --format '{{{{.Names}}}}\\t{{{{.Status}}}}'"
            stdin, stdout, stderr = self.ssh_client.exec_command(check_cmd)
            output = stdout.read().decode('utf-8').strip()

            if output:
                logger.info(f"Found existing container: {output}")
                await self._remove_container(container_name)

        except Exception as e:
            logger.error(f"Failed to cleanup existing container {container_name}: {str(e)}")
    async def _setup_mongodb_replica_set(self, parsed_info: Dict[str, Any]) -> None:
        """容器启动后执行 rs.initiate() 初始化副本集（幂等）。"""
        container_name = parsed_info.get('container_name') or parsed_info.get('container_id')
        if not container_name:
            logger.warning("MongoDB replica set init skipped: no container identifier")
            return
        init_js = (
            "if (rs.status().ok !== 1) { rs.initiate({_id: 'rs0', members: [{ _id: 0, host: 'localhost:27017' }]}); }"
        )
        # 在容器内执行 mongo shell 初始化；忽略非致命错误，等待几秒再检查
        cmd = f"docker exec {container_name} bash -lc \"mongo --quiet --eval \"{init_js}\"\""
        stdin, stdout, stderr = self.ssh_client.exec_command(cmd)
        stdout.channel.recv_exit_status()
        await asyncio.sleep(2)
        # 再次检查状态（可选）
        check_cmd = f"docker exec {container_name} bash -lc \"mongo --quiet --eval \"JSON.stringify(rs.status())\"\""
        stdin, stdout, stderr = self.ssh_client.exec_command(check_cmd)
        _ = stdout.read().decode('utf-8').strip()
        logger.info("MongoDB replica set init attempted.")


    async def _remove_container(self, container_identifier: str):
        """删除容器（强制删除）"""
        try:
            remove_cmd = f"docker rm -f {container_identifier}"
            stdin, stdout, stderr = self.ssh_client.exec_command(remove_cmd)
            exit_status = stdout.channel.recv_exit_status()

            if exit_status == 0:
                logger.info(f"Successfully removed container: {container_identifier}")
            else:
                error_output = stderr.read().decode('utf-8')
                logger.warning(f"Failed to remove container {container_identifier}: {error_output}")

        except Exception as e:
            logger.error(f"Error removing container {container_identifier}: {str(e)}")

    async def _check_container_status(self, container_id: str) -> str:
        """检查容器状态"""
        try:
            status_cmd = f"docker inspect {container_id} --format '{{{{.State.Status}}}}'"
            stdin, stdout, stderr = self.ssh_client.exec_command(status_cmd)
            exit_status = stdout.channel.recv_exit_status()

            if exit_status == 0:
                status = stdout.read().decode('utf-8').strip()
                return status
            else:
                return 'unknown'

        except Exception as e:
            logger.error(f"Failed to check container status: {str(e)}")
            return 'error'

    async def _get_container_logs(self, container_id: str) -> str:
        """获取容器日志"""
        try:
            logs_cmd = f"docker logs {container_id} 2>&1 | tail -20"
            stdin, stdout, stderr = self.ssh_client.exec_command(logs_cmd)
            logs = stdout.read().decode('utf-8').strip()
            return logs

        except Exception as e:
            logger.error(f"Failed to get container logs: {str(e)}")
            return f"Failed to get logs: {str(e)}"

"""
Docker镜像管理服务
"""

import logging
import json
import asyncio
from typing import List, Dict, Any, Optional
from datetime import datetime
import paramiko

from services.mysql_service import MySQLService
from utils.config import Config
from models.server_config import ServerConfig

logger = logging.getLogger(__name__)

class DockerImageService:
    """Docker镜像管理服务"""
    
    def __init__(self):
        mysql_config = Config.get_mysql_config()
        self.mysql_service = MySQLService(**mysql_config)
        self.ssh_client = None
        
    async def initialize(self):
        """初始化服务"""
        try:
            await self.mysql_service.initialize()
            logger.info("Docker image service initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize docker image service: {str(e)}")
            raise
    
    async def _connect_ssh(self, server_config: ServerConfig):
        """连接SSH"""
        try:
            if self.ssh_client:
                self.ssh_client.close()
                
            self.ssh_client = paramiko.SSHClient()
            self.ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            
            self.ssh_client.connect(
                hostname=server_config.host,
                port=server_config.port,
                username=server_config.username,
                password=server_config.password,
                timeout=30
            )
            
            logger.info(f"SSH connected to {server_config.host}:{server_config.port}")
            
        except Exception as e:
            logger.error(f"SSH connection failed: {str(e)}")
            raise Exception(f"SSH connection failed: {str(e)}")
    
    async def get_server_docker_images(self, server_config: ServerConfig) -> List[Dict[str, Any]]:
        """获取服务器的Docker镜像列表"""
        try:
            await self._connect_ssh(server_config)
            
            # 首先检查Docker是否可用
            check_docker_command = "docker --version"
            stdin, stdout, stderr = self.ssh_client.exec_command(check_docker_command)
            exit_status = stdout.channel.recv_exit_status()

            if exit_status != 0:
                error_output = stderr.read().decode('utf-8')
                logger.error(f"Docker not available: {error_output}")
                raise Exception(f"Docker not available on server: {error_output}")

            docker_version = stdout.read().decode('utf-8').strip()
            logger.info(f"Docker version: {docker_version}")

            # 直接使用简单的docker images命令，因为格式化输出在SSH中可能有问题
            docker_command = "docker images"
            logger.info(f"Executing command: {docker_command}")

            stdin, stdout, stderr = self.ssh_client.exec_command(docker_command)
            exit_status = stdout.channel.recv_exit_status()

            if exit_status != 0:
                error_output = stderr.read().decode('utf-8')
                logger.error(f"Docker images command failed: {error_output}")
                raise Exception(f"Docker images command failed: {error_output}")

            output = stdout.read().decode('utf-8')
            logger.info(f"Docker images output: {output}")

            lines = output.strip().split('\n')
            logger.info(f"Output lines count: {len(lines)}")

            images = []
            # 跳过表头
            for i, line in enumerate(lines[1:], 1):
                if line.strip() and not line.startswith('REPOSITORY'):
                    parts = line.split()
                    logger.info(f"Line {i}: '{line}' -> parts count: {len(parts)}")
                    if len(parts) >= 5:
                        # Docker images输出格式: REPOSITORY TAG IMAGE_ID CREATED SIZE
                        # 但CREATED可能包含多个词，所以SIZE是最后一个部分
                        image_info = {
                            'image_id': parts[2],
                            'repository': parts[0],
                            'tag': parts[1],
                            'size_str': parts[-1],  # SIZE是最后一个部分
                            'created_at': ' '.join(parts[3:-1]) if len(parts) > 4 else parts[3] if len(parts) > 3 else 'unknown',
                            'size': self._parse_size(parts[-1])  # SIZE是最后一个部分
                        }
                        images.append(image_info)
                        logger.info(f"Added image: {image_info['repository']}:{image_info['tag']}")
                    else:
                        logger.warning(f"Skipping line with insufficient parts: {line}")

            logger.info(f"Found {len(images)} Docker images on server {server_config.host}")
            return images
            
        except Exception as e:
            logger.error(f"Failed to get Docker images: {str(e)}")
            raise
        finally:
            if self.ssh_client:
                self.ssh_client.close()
                self.ssh_client = None
    
    def _parse_size(self, size_str: str) -> int:
        """解析镜像大小字符串为字节数"""
        try:
            size_str = size_str.upper().strip()
            if 'GB' in size_str:
                return int(float(size_str.replace('GB', '')) * 1024 * 1024 * 1024)
            elif 'MB' in size_str:
                return int(float(size_str.replace('MB', '')) * 1024 * 1024)
            elif 'KB' in size_str:
                return int(float(size_str.replace('KB', '')) * 1024)
            elif 'B' in size_str:
                return int(size_str.replace('B', ''))
            else:
                return 0
        except:
            return 0
    
    async def update_server_docker_images(self, server_config_id: int, server_config: ServerConfig) -> Dict[str, Any]:
        """更新服务器的Docker镜像信息到数据库"""
        try:
            # 获取最新的镜像列表
            images = await self.get_server_docker_images(server_config)
            
            # 清除该服务器的旧镜像记录
            await self.mysql_service.execute_query(
                "DELETE FROM server_docker_images WHERE server_config_id = %s",
                (server_config_id,)
            )
            
            # 插入新的镜像记录
            inserted_count = 0
            for image in images:
                try:
                    await self.mysql_service.execute_query("""
                        INSERT INTO server_docker_images 
                        (server_config_id, image_id, repository, tag, size, created_time, image_info)
                        VALUES (%s, %s, %s, %s, %s, %s, %s)
                    """, (
                        server_config_id,
                        image['image_id'],
                        image['repository'],
                        image['tag'],
                        image['size'],
                        None,  # created_time 暂时设为None，因为Docker的时间格式需要解析
                        json.dumps(image)
                    ))
                    inserted_count += 1
                except Exception as e:
                    logger.warning(f"Failed to insert image {image['image_id']}: {str(e)}")
            
            logger.info(f"Updated {inserted_count} Docker images for server {server_config_id}")
            
            return {
                'success': True,
                'total_images': len(images),
                'inserted_count': inserted_count,
                'message': f'Successfully updated {inserted_count} Docker images'
            }
            
        except Exception as e:
            logger.error(f"Failed to update Docker images: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'message': f'Failed to update Docker images: {str(e)}'
            }
    
    async def get_stored_docker_images(self, server_config_id: int, search_query: str = None) -> List[Dict[str, Any]]:
        """从数据库获取存储的Docker镜像列表"""
        try:
            base_sql = """
                SELECT id, server_config_id, image_id, repository, tag, size, 
                       created_time, last_updated, is_available, image_info,
                       created_at, updated_at
                FROM server_docker_images 
                WHERE server_config_id = %s
            """
            params = [server_config_id]
            
            if search_query:
                base_sql += " AND (repository LIKE %s OR tag LIKE %s OR image_id LIKE %s)"
                search_pattern = f"%{search_query}%"
                params.extend([search_pattern, search_pattern, search_pattern])
            
            base_sql += " ORDER BY repository, tag"
            
            result = await self.mysql_service.execute_query(base_sql, params)
            
            if result and result.get('data'):
                return result['data']
            else:
                return []
                
        except Exception as e:
            logger.error(f"Failed to get stored Docker images: {str(e)}")
            return []
    
    async def search_docker_images(self, server_config_id: int, search_query: str) -> List[Dict[str, Any]]:
        """搜索Docker镜像"""
        return await self.get_stored_docker_images(server_config_id, search_query)
    
    async def get_database_images(self, server_config_id: int) -> List[Dict[str, Any]]:
        """获取数据库相关的Docker镜像"""
        try:
            database_keywords = ['mysql', 'postgres', 'mongo', 'oracle', 'redis', 'mariadb', 'gauss', 'opengauss']
            
            sql = """
                SELECT id, server_config_id, image_id, repository, tag, size, 
                       created_time, last_updated, is_available, image_info,
                       created_at, updated_at
                FROM server_docker_images 
                WHERE server_config_id = %s AND (
            """
            
            conditions = []
            params = [server_config_id]
            
            for keyword in database_keywords:
                conditions.append("repository LIKE %s")
                params.append(f"%{keyword}%")
            
            sql += " OR ".join(conditions) + ") ORDER BY repository, tag"
            
            result = await self.mysql_service.execute_query(sql, params)
            
            if result and result.get('data'):
                return result['data']
            else:
                return []
                
        except Exception as e:
            logger.error(f"Failed to get database images: {str(e)}")
            return []
    
    async def close(self):
        """关闭服务"""
        if self.ssh_client:
            self.ssh_client.close()
        await self.mysql_service.close()

# 创建全局实例
docker_image_service = DockerImageService()

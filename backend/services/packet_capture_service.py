import asyncio
import subprocess
import os
import signal
import time
from datetime import datetime
from typing import Optional
import logging
import traceback

logger = logging.getLogger(__name__)

class PacketCaptureService:
    """数据包捕获服务"""
    
    def __init__(self, capture_dir: str = "captures"):
        self.capture_dir = capture_dir
        self.current_process: Optional[subprocess.Popen] = None
        self.current_file: Optional[str] = None
        self.mysql_host = "**************"
        self.mysql_port = 3307
        
        # 创建捕获目录
        os.makedirs(self.capture_dir, exist_ok=True)
    
    def _generate_filename(self) -> str:
        """生成pcap文件名"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"mysql_capture_{timestamp}.pcap"
    
    async def start_capture(self) -> str:
        """启动数据包捕获"""
        if self.is_capturing():
            logger.warning("Packet capture is already running")
            return self.current_file
        
        filename = self._generate_filename()
        filepath = os.path.join(self.capture_dir, filename)
        
        # 构建tcpdump命令
        # 捕获到/从MySQL服务器的所有TCP流量，确保捕获完整数据包
        # 优先使用docker0接口，然后是eth0、ens3，避免使用any接口
        interface = "docker0"  # 默认使用docker0接口
        try:
            # 检查可用的网络接口
            import subprocess
            result = subprocess.run(['ip', 'addr', 'show'], capture_output=True, text=True)
            if result.returncode == 0:
                interfaces = result.stdout
                if 'docker0' in interfaces:
                    interface = "docker0"
                elif 'eth0' in interfaces:
                    interface = "eth0"
                elif 'ens3' in interfaces:
                    interface = "ens3"
                else:
                    interface = "lo"  # 回环接口作为最后选择
        except Exception as e:
            logger.warning(f"Failed to detect network interfaces, using default docker0: {e}")

        cmd = [
            "tcpdump",
            "-i", interface,  # 使用检测到的最佳网络接口
            "-w", filepath,  # 写入文件
            "-s", "0",  # 捕获完整数据包
            "-v",  # 详细输出
            "-n",  # 不解析主机名
            f"host {self.mysql_host} and port {self.mysql_port}"
        ]
        
        try:
            logger.info(f"Starting packet capture with command: {' '.join(cmd)}")
            
            # 启动tcpdump进程
            self.current_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                preexec_fn=os.setsid  # 创建新的进程组
            )
            
            self.current_file = filepath
            
            # 等待一小段时间确保tcpdump启动
            await asyncio.sleep(0.5)
            
            # 检查进程是否正常启动
            if self.current_process.poll() is not None:
                stdout, stderr = self.current_process.communicate()
                error_msg = f"tcpdump failed to start: {stderr.decode()}"
                logger.error(error_msg)
                raise Exception(error_msg)
            
            logger.info(f"Packet capture started successfully: {filepath}")
            return filepath
            
        except Exception as e:
            logger.error(f"Failed to start packet capture: {str(e)}")
            self.current_process = None
            self.current_file = None
            raise
    
    async def stop_capture(self) -> Optional[str]:
        """停止数据包捕获"""
        if not self.is_capturing():
            logger.warning("No packet capture is currently running")
            return None
        
        try:
            logger.info("Stopping packet capture...")
            
            # 发送SIGTERM信号给进程组
            os.killpg(os.getpgid(self.current_process.pid), signal.SIGTERM)
            
            # 等待进程结束
            try:
                self.current_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                # 如果进程没有在5秒内结束，强制杀死
                logger.warning("tcpdump didn't terminate gracefully, forcing kill")
                os.killpg(os.getpgid(self.current_process.pid), signal.SIGKILL)
                self.current_process.wait()
            
            captured_file = self.current_file
            self.current_process = None
            self.current_file = None
            
            logger.info(f"Packet capture stopped: {captured_file}")
            return captured_file
            
        except Exception as e:
            logger.error(f"Error stopping packet capture: {str(e)}")
            self.current_process = None
            self.current_file = None
            raise
    
    def is_capturing(self) -> bool:
        """检查是否正在捕获"""
        if self.current_process is None:
            return False
        
        # 检查进程是否还在运行
        return self.current_process.poll() is None
    
    def get_current_file(self) -> Optional[str]:
        """获取当前捕获文件路径"""
        return self.current_file
    
    def list_capture_files(self) -> list:
        """列出所有捕获文件"""
        try:
            files = []
            for filename in os.listdir(self.capture_dir):
                if filename.endswith('.pcap'):
                    filepath = os.path.join(self.capture_dir, filename)
                    stat = os.stat(filepath)
                    files.append({
                        'filename': filename,
                        'filepath': filepath,
                        'size': stat.st_size,
                        'created': datetime.fromtimestamp(stat.st_ctime).isoformat(),
                        'modified': datetime.fromtimestamp(stat.st_mtime).isoformat()
                    })
            
            # 按创建时间排序
            files.sort(key=lambda x: x['created'], reverse=True)
            return files
            
        except Exception as e:
            logger.error(f"Error listing capture files: {str(e)}")
            return []
    
    async def analyze_pcap_file(self, filepath: str) -> dict:
        """分析pcap文件"""
        try:
            from scapy.all import rdpcap, TCP
            
            logger.info(f"Analyzing pcap file: {filepath}")
            
            # 读取pcap文件
            packets = rdpcap(filepath)
            
            analysis = {
                'total_packets': len(packets),
                'mysql_packets': 0,
                'connections': set(),
                'queries': [],
                'file_size': os.path.getsize(filepath)
            }
            
            for packet in packets:
                if TCP in packet:
                    # 检查是否是MySQL端口
                    if packet[TCP].dport == self.mysql_port or packet[TCP].sport == self.mysql_port:
                        analysis['mysql_packets'] += 1
                        
                        # 记录连接信息
                        src_ip = packet['IP'].src
                        dst_ip = packet['IP'].dst
                        analysis['connections'].add(f"{src_ip}:{packet[TCP].sport} -> {dst_ip}:{packet[TCP].dport}")
                        
                        # 尝试提取MySQL查询（简单实现）
                        if packet[TCP].payload:
                            payload = bytes(packet[TCP].payload)
                            # MySQL查询通常以特定字节开始
                            if len(payload) > 5 and payload[4] == 0x03:  # COM_QUERY
                                try:
                                    query = payload[5:].decode('utf-8', errors='ignore')
                                    if query.strip():
                                        analysis['queries'].append({
                                            'timestamp': packet.time,
                                            'query': query.strip()
                                        })
                                except Exception as e:

                                    logger.error(f"操作失败: {type(e).__name__}: {str(e)}")

                                    logger.error(f"异常堆栈跟踪: {traceback.format_exc()}")
            # 转换set为list以便JSON序列化
            analysis['connections'] = list(analysis['connections'])
            
            logger.info(f"Analysis complete: {analysis['total_packets']} total packets, {analysis['mysql_packets']} MySQL packets")
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing pcap file: {str(e)}")
            return {'error': str(e)}

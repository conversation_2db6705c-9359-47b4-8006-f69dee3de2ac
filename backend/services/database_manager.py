import json
import os
from typing import Dict, List, Optional, Any
import logging
from dataclasses import dataclass, asdict
from utils.config import Config

logger = logging.getLogger(__name__)

@dataclass
class DatabaseConfig:
    """数据库配置数据类"""
    name: str                    # 配置名称
    host: str                   # 主机地址
    port: int                   # 端口号
    user: str                   # 用户名
    password: str               # 密码
    database: str               # 数据库名
    description: str = ""       # 描述
    is_default: bool = False    # 是否为默认配置
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DatabaseConfig':
        """从字典创建实例"""
        return cls(**data)

class DatabaseManager:
    """数据库配置管理器"""
    
    def __init__(self, config_file: str = "database_configs.json"):
        self.config_file = os.path.join(Config.CAPTURE_DIR, config_file)
        self.configs: Dict[str, DatabaseConfig] = {}
        self._load_configs()
        self._ensure_default_configs()
    
    def _load_configs(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    for name, config_data in data.items():
                        self.configs[name] = DatabaseConfig.from_dict(config_data)
                logger.info(f"Loaded {len(self.configs)} database configurations")
            else:
                logger.info("No existing database configuration file found")
        except Exception as e:
            logger.error(f"Failed to load database configurations: {e}")
            self.configs = {}
    
    def _save_configs(self):
        """保存配置文件"""
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            
            # 转换为可序列化的格式
            data = {name: config.to_dict() for name, config in self.configs.items()}
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            logger.info(f"Saved {len(self.configs)} database configurations")
        except Exception as e:
            logger.error(f"Failed to save database configurations: {e}")
            raise
    
    def _ensure_default_configs(self):
        """确保有默认配置"""
        # 添加当前环境变量配置
        current_config = DatabaseConfig(
            name="current",
            host=Config.MYSQL_HOST,
            port=Config.MYSQL_PORT,
            user=Config.MYSQL_USER,
            password=Config.MYSQL_PASSWORD,
            database=Config.MYSQL_DATABASE,
            description="当前环境变量配置",
            is_default=True
        )
        
        # 添加新的测试数据库配置
        test_config = DatabaseConfig(
            name="test_db",
            host="**************",
            port=3306,
            user="root",
            password="123456",
            database="ai_sql_pcap",
            description="测试数据库配置"
        )
        
        # 如果没有配置，添加默认配置
        if not self.configs:
            self.configs["current"] = current_config
            self.configs["test_db"] = test_config
            self._save_configs()
        
        # 确保至少有一个默认配置
        if not any(config.is_default for config in self.configs.values()):
            if "current" in self.configs:
                self.configs["current"].is_default = True
            elif self.configs:
                list(self.configs.values())[0].is_default = True
            self._save_configs()
    
    def add_database(self, config: DatabaseConfig) -> bool:
        """添加数据库配置"""
        try:
            if config.name in self.configs:
                logger.warning(f"Database configuration '{config.name}' already exists")
                return False
            
            self.configs[config.name] = config
            self._save_configs()
            logger.info(f"Added database configuration: {config.name}")
            return True
        except Exception as e:
            logger.error(f"Failed to add database configuration: {e}")
            return False
    
    def update_database(self, name: str, config: DatabaseConfig) -> bool:
        """更新数据库配置"""
        try:
            if name not in self.configs:
                logger.warning(f"Database configuration '{name}' not found")
                return False
            
            # 保持原有的默认状态
            config.is_default = self.configs[name].is_default
            self.configs[name] = config
            self._save_configs()
            logger.info(f"Updated database configuration: {name}")
            return True
        except Exception as e:
            logger.error(f"Failed to update database configuration: {e}")
            return False
    
    def remove_database(self, name: str) -> bool:
        """删除数据库配置"""
        try:
            if name not in self.configs:
                logger.warning(f"Database configuration '{name}' not found")
                return False
            
            was_default = self.configs[name].is_default
            del self.configs[name]
            
            # 如果删除的是默认配置，设置新的默认配置
            if was_default and self.configs:
                list(self.configs.values())[0].is_default = True
            
            self._save_configs()
            logger.info(f"Removed database configuration: {name}")
            return True
        except Exception as e:
            logger.error(f"Failed to remove database configuration: {e}")
            return False
    
    def get_database(self, name: str) -> Optional[DatabaseConfig]:
        """获取数据库配置"""
        return self.configs.get(name)
    
    def get_default_database(self) -> Optional[DatabaseConfig]:
        """获取默认数据库配置"""
        for config in self.configs.values():
            if config.is_default:
                return config
        return None
    
    def set_default_database(self, name: str) -> bool:
        """设置默认数据库"""
        try:
            if name not in self.configs:
                logger.warning(f"Database configuration '{name}' not found")
                return False
            
            # 清除所有默认标记
            for config in self.configs.values():
                config.is_default = False
            
            # 设置新的默认
            self.configs[name].is_default = True
            self._save_configs()
            logger.info(f"Set default database: {name}")
            return True
        except Exception as e:
            logger.error(f"Failed to set default database: {e}")
            return False
    
    def list_databases(self) -> List[DatabaseConfig]:
        """列出所有数据库配置"""
        return list(self.configs.values())
    
    def get_database_names(self) -> List[str]:
        """获取所有数据库配置名称"""
        return list(self.configs.keys())
    
    def test_connection(self, name: str) -> Dict[str, Any]:
        """测试数据库连接"""
        try:
            config = self.get_database(name)
            if not config:
                return {"success": False, "error": f"Database configuration '{name}' not found"}
            
            # 导入MySQL服务进行连接测试
            from services.mysql_service import MySQLService
            
            mysql_config = {
                'host': config.host,
                'port': config.port,
                'user': config.user,
                'password': config.password,
                'database': config.database
            }
            
            mysql_service = MySQLService(**mysql_config)
            
            # 异步测试连接
            import asyncio
            async def test():
                try:
                    is_connected = await mysql_service.check_connection()
                    if is_connected:
                        # 获取数据库信息
                        databases = await mysql_service.get_databases()
                        tables = await mysql_service.get_tables()
                        await mysql_service.close()
                        
                        return {
                            "success": True,
                            "message": "Connection successful",
                            "databases": databases,
                            "tables": tables,
                            "config": config.to_dict()
                        }
                    else:
                        await mysql_service.close()
                        return {"success": False, "error": "Connection failed"}
                except Exception as e:
                    await mysql_service.close()
                    return {"success": False, "error": str(e)}
            
            # 如果在事件循环中，直接返回协程
            try:
                loop = asyncio.get_running_loop()
                # 在已有事件循环中，创建任务
                task = loop.create_task(test())
                return {"success": True, "message": "Connection test started", "task": task}
            except RuntimeError:
                # 没有运行的事件循环，创建新的
                return asyncio.run(test())
                
        except Exception as e:
            logger.error(f"Failed to test database connection: {e}")
            return {"success": False, "error": str(e)}
    
    def export_config(self, name: str) -> Optional[Dict[str, Any]]:
        """导出数据库配置"""
        config = self.get_database(name)
        if config:
            return config.to_dict()
        return None
    
    def import_config(self, config_data: Dict[str, Any]) -> bool:
        """导入数据库配置"""
        try:
            config = DatabaseConfig.from_dict(config_data)
            return self.add_database(config)
        except Exception as e:
            logger.error(f"Failed to import database configuration: {e}")
            return False

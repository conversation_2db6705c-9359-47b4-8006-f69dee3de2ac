#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
增强的Oracle协议分析器 - 正确处理TNS协议分片和重组SQL语句
"""

import os
import re
import struct
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from collections import defaultdict

from .pcap_sql_analyzer import SQLStatement, DatabaseType, SQLType

logger = logging.getLogger(__name__)

@dataclass
class TNSConnection:
    """TNS连接信息"""
    source_ip: str
    source_port: int
    dest_ip: str
    dest_port: int
    packets: List[Tuple[int, bytes, float]]  # (packet_num, payload, timestamp)
    reassembled_data: Optional[bytes] = None
    connection_id: str = ""

class EnhancedOracleProtocolAnalyzer:
    """增强的Oracle协议分析器"""
    
    def __init__(self):
        self.database_type = DatabaseType.ORACLE
        self.connections: Dict[str, TNSConnection] = {}
        self.connection_packets: Dict[str, List[Tuple[int, bytes, float]]] = defaultdict(list)
        
    def analyze_pcap_file(self, pcap_file: str, database_type: str = None, database_port: int = None) -> List[SQLStatement]:
        """分析PCAP文件，正确处理TNS协议分片"""
        statements = []
        
        try:
            from scapy.all import rdpcap
            
            logger.info(f"开始增强Oracle PCAP分析: {pcap_file}")
            
            # 读取数据包
            packets = rdpcap(pcap_file)
            logger.info(f"读取到 {len(packets)} 个数据包")
            
            # 第一遍：收集所有Oracle数据包并按连接分组
            self._collect_oracle_packets(packets)
            
            # 第二遍：重组每个连接的数据包
            self._reassemble_connections()
            
            # 第三遍：从重组数据中提取SQL语句
            statements = self._extract_sql_statements()
            
            logger.info(f"分析完成，找到 {len(statements)} 个SQL语句")
            
        except ImportError:
            logger.error("scapy库未安装，无法进行PCAP分析")
        except Exception as e:
            logger.error(f"增强Oracle分析失败: {str(e)}")
        
        return statements
    
    def _collect_oracle_packets(self, packets):
        """收集Oracle数据包并按连接分组"""
        logger.info("开始收集Oracle数据包...")
        
        for i, packet in enumerate(packets):
            if packet.haslayer('TCP'):
                # 检查Oracle端口
                if packet['TCP'].dport == 1521 or packet['TCP'].sport == 1521:
                    if packet.haslayer('TCP'):
                        payload = bytes(packet['TCP'].payload)
                        if payload:
                            # 确定连接方向
                            if packet['TCP'].sport == 1521:  # 从Oracle服务器发出
                                source_ip = packet['IP'].dst
                                source_port = packet['TCP'].dport
                                dest_ip = packet['IP'].src
                                dest_port = packet['TCP'].sport
                            else:  # 发往Oracle服务器
                                source_ip = packet['IP'].src
                                source_port = packet['TCP'].sport
                                dest_ip = packet['IP'].dst
                                dest_port = packet['TCP'].dport
                            
                            # 创建连接标识符
                            conn_key = f"{source_ip}:{source_port}-{dest_ip}:{dest_port}"
                            
                            if conn_key not in self.connections:
                                self.connections[conn_key] = TNSConnection(
                                    source_ip=source_ip,
                                    source_port=source_port,
                                    dest_ip=dest_ip,
                                    dest_port=dest_port,
                                    packets=[],
                                    connection_id=conn_key
                                )
                            
                            # 添加数据包
                            timestamp = float(packet.time) if hasattr(packet, 'time') else 0.0
                            self.connections[conn_key].packets.append((i, payload, timestamp))
        
        logger.info(f"找到 {len(self.connections)} 个Oracle连接")
        
        # 按时间戳排序每个连接的数据包
        for conn_key, conn in self.connections.items():
            conn.packets.sort(key=lambda x: x[2])  # 按时间戳排序
            logger.info(f"连接 {conn_key}: {len(conn.packets)} 个数据包")
    
    def _reassemble_connections(self):
        """重组每个连接的数据包"""
        logger.info("开始重组TNS连接...")
        
        for conn_key, conn in self.connections.items():
            logger.info(f"重组连接 {conn_key}")
            
            # 尝试重组数据包
            reassembled = self._reassemble_connection_packets(conn)
            if reassembled:
                conn.reassembled_data = reassembled
                logger.info(f"连接 {conn_key} 重组完成，长度: {len(reassembled)} 字节")
            else:
                logger.warning(f"连接 {conn_key} 重组失败")
    
    def _reassemble_connection_packets(self, conn: TNSConnection) -> Optional[bytes]:
        """重组单个连接的数据包"""
        try:
            all_payloads = []
            
            for packet_num, payload, timestamp in conn.packets:
                if len(payload) >= 8:
                    try:
                        # 尝试解析TNS头
                        packet_length = struct.unpack('>H', payload[:2])[0]
                        packet_type = payload[4]
                        
                        # 如果是DATA包且有数据部分
                        if packet_type == 6 and len(payload) > 8:
                            data_part = payload[8:]
                            if data_part:
                                all_payloads.append(data_part)
                                logger.debug(f"数据包 {packet_num}: TNS DATA包，数据长度={len(data_part)}")
                        
                        # 如果包长度字段有效，尝试使用它
                        elif packet_length > 0 and packet_length <= len(payload):
                            data_part = payload[8:packet_length]
                            if data_part:
                                all_payloads.append(data_part)
                                logger.debug(f"数据包 {packet_num}: 有效长度={packet_length}，数据长度={len(data_part)}")
                        
                        # 如果TNS头解析失败，尝试其他方法
                        else:
                            # 尝试查找SQL关键词来确定数据部分
                            sql_keywords = [b'SELECT', b'INSERT', b'UPDATE', b'DELETE', b'CREATE', b'DROP', b'ALTER']
                            for keyword in sql_keywords:
                                if keyword in payload:
                                    # 找到SQL关键词，提取从关键词开始的部分
                                    pos = payload.find(keyword)
                                    if pos > 0:
                                        data_part = payload[pos:]
                                        all_payloads.append(data_part)
                                        logger.debug(f"数据包 {packet_num}: 找到SQL关键词，提取数据长度={len(data_part)}")
                                        break
                            else:
                                # 如果没有找到SQL关键词，使用整个载荷
                                all_payloads.append(payload)
                                logger.debug(f"数据包 {packet_num}: 使用完整载荷，长度={len(payload)}")
                        
                        # 额外检查：即使TNS头长度是0，也要检查是否包含SQL语句
                        if packet_length == 0 and len(payload) > 8:
                            # 检查数据部分是否包含SQL关键词
                            data_part = payload[8:]
                            sql_keywords = [b'SELECT', b'INSERT', b'UPDATE', b'DELETE', b'CREATE', b'DROP', b'ALTER']
                            for keyword in sql_keywords:
                                if keyword in data_part:
                                    # 找到SQL关键词，提取从关键词开始的部分
                                    pos = data_part.find(keyword)
                                    if pos > 0:
                                        sql_data_part = data_part[pos:]
                                        all_payloads.append(sql_data_part)
                                        logger.debug(f"数据包 {packet_num}: TNS头长度为0但包含SQL，提取数据长度={len(sql_data_part)}")
                                        break
                    
                    except struct.error:
                        # TNS头解析失败，尝试查找SQL关键词
                        sql_keywords = [b'SELECT', b'INSERT', b'UPDATE', b'DELETE', b'CREATE', b'DROP', b'ALTER']
                        for keyword in sql_keywords:
                            if keyword in payload:
                                pos = payload.find(keyword)
                                if pos > 0:
                                    data_part = payload[pos:]
                                    all_payloads.append(data_part)
                                    logger.debug(f"数据包 {packet_num}: TNS头解析失败，找到SQL关键词，提取数据长度={len(data_part)}")
                                    break
                        else:
                            # 使用整个载荷
                            all_payloads.append(payload)
                            logger.debug(f"数据包 {packet_num}: TNS头解析失败，使用完整载荷")
            
            if all_payloads:
                # 合并所有载荷
                reassembled = b''.join(all_payloads)
                return reassembled
            
        except Exception as e:
            logger.error(f"重组连接失败: {str(e)}")
        
        return None
    
    def _extract_sql_statements(self) -> List[SQLStatement]:
        """从重组数据中提取SQL语句"""
        statements = []
        
        for conn_key, conn in self.connections.items():
            if conn.reassembled_data:
                logger.info(f"分析重组数据包 {conn_key}，长度: {len(conn.reassembled_data)} 字节")
                
                # 尝试多种方式提取SQL语句
                sql_statements = self._extract_sql_from_data(conn.reassembled_data)
                
                for sql, sql_type, confidence in sql_statements:
                    if sql and len(sql.strip()) > 10:
                        statement = SQLStatement(
                            sql=sql.strip(),
                            sql_type=sql_type,
                            database_type=self.database_type,
                            timestamp=0.0,
                            packet_number=0,
                            source_ip=conn.source_ip,
                            dest_ip=conn.dest_ip,
                            source_port=conn.source_port,
                            dest_port=conn.dest_port,
                            confidence=confidence
                        )
                        statements.append(statement)
                        logger.info(f"找到SQL语句: {sql[:100]}...")
        
        return statements
    
    def _extract_sql_from_data(self, data: bytes) -> List[Tuple[str, SQLType, float]]:
        """从数据中提取SQL语句"""
        sql_statements = []
        
        try:
            # 尝试多种编码方式
            encodings = ['utf-8', 'latin1', 'ascii']
            
            for encoding in encodings:
                try:
                    decoded = data.decode(encoding, errors='ignore')
                    if decoded.strip():
                        # 查找SQL语句
                        found_sqls = self._find_complete_sql_statements(decoded)
                        for sql, sql_type in found_sqls:
                            sql_statements.append((sql, sql_type, 0.9))
                        
                        if found_sqls:
                            logger.info(f"使用 {encoding} 编码找到 {len(found_sqls)} 个SQL语句")
                            break
                        
                except UnicodeDecodeError:
                    continue
            
            # 如果没有找到，尝试在原始字节中查找
            if not sql_statements:
                sql_statements = self._find_sql_in_bytes(data)
        
        except Exception as e:
            logger.error(f"从数据提取SQL失败: {str(e)}")
        
        return sql_statements
    
    def _find_complete_sql_statements(self, text: str) -> List[Tuple[str, SQLType]]:
        """查找完整的SQL语句"""
        sql_statements = []
        
        # 增强的SQL模式匹配，尝试找到完整的语句
        sql_patterns = [
            # 基础SQL模式 - 更精确的结束标记
            (r'\b(SELECT\s+.+?(?:FROM\s+\w+|;|\s*$))', SQLType.SELECT),
            (r'\b(INSERT\s+INTO\s+.+?(?:VALUES|SELECT|;|\s*$))', SQLType.INSERT),
            (r'\b(UPDATE\s+.+?(?:SET|;|\s*$))', SQLType.UPDATE),
            (r'\b(DELETE\s+FROM\s+.+?(?:WHERE|;|\s*$))', SQLType.DELETE),
            (r'\b(CREATE\s+(?:TABLE|SEQUENCE|INDEX|VIEW|PROCEDURE|FUNCTION|PACKAGE)\s+.+?(?:;|\s*$))', SQLType.CREATE),
            (r'\b(DROP\s+(?:TABLE|SEQUENCE|INDEX|VIEW|PROCEDURE|FUNCTION|PACKAGE)\s+.+?(?:;|\s*$))', SQLType.DROP),
            (r'\b(ALTER\s+(?:TABLE|SESSION)\s+.+?(?:;|\s*$))', SQLType.ALTER),
            # Oracle特有模式
            (r'\b(BEGIN\s+.+?END\s*;)', SQLType.UNKNOWN),
            (r'\b(DECLARE\s+.+?BEGIN\s+.+?END\s*;)', SQLType.UNKNOWN),
            (r'\b(MERGE\s+INTO\s+.+?(?:;|\s*$))', SQLType.UNKNOWN),
            (r'\b(TRUNCATE\s+TABLE\s+.+?(?:;|\s*$))', SQLType.UNKNOWN),
        ]
        
        for pattern, sql_type in sql_patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE | re.DOTALL)
            for match in matches:
                sql = match.group(1).strip()
                if sql and len(sql) > 10:
                    # 清理SQL语句
                    sql = self._clean_sql_statement(sql)
                    if sql:
                        sql_statements.append((sql, sql_type))
        
        return sql_statements
    
    def _find_sql_in_bytes(self, data: bytes) -> List[Tuple[str, SQLType, float]]:
        """在字节数据中查找SQL关键词"""
        sql_statements = []
        
        try:
            text = data.decode('utf-8', errors='ignore')
            
            # 查找SQL关键词的位置
            sql_keywords = {
                'SELECT': SQLType.SELECT,
                'INSERT': SQLType.INSERT,
                'UPDATE': SQLType.UPDATE,
                'DELETE': SQLType.DELETE,
                'CREATE': SQLType.CREATE,
                'DROP': SQLType.DROP,
                'ALTER': SQLType.ALTER,
            }
            
            for keyword, sql_type in sql_keywords.items():
                if keyword in text.upper():
                    # 尝试提取包含关键词的上下文
                    start_pos = text.upper().find(keyword)
                    if start_pos >= 0:
                        # 提取关键词周围的文本，尝试找到完整语句
                        context_start = max(0, start_pos - 50)
                        context_end = min(len(text), start_pos + 300)
                        context = text[context_start:context_end]
                        
                        # 尝试提取完整SQL语句
                        sql_statement = self._extract_complete_sql_from_context(context, keyword)
                        if sql_statement and len(sql_statement) > 20:
                            sql_statements.append((sql_statement, sql_type, 0.8))
                            logger.info(f"在字节数据中找到完整SQL: {keyword}")
        
        except Exception as e:
            logger.error(f"在字节数据中查找SQL失败: {str(e)}")
        
        return sql_statements
    
    def _extract_complete_sql_from_context(self, context: str, keyword: str) -> str:
        """从上下文中提取完整的SQL语句"""
        try:
            # 查找关键词位置
            pos = context.upper().find(keyword.upper())
            if pos < 0:
                return ""
            
            # 从关键词开始，尝试找到语句结束
            start = pos
            end = len(context)
            
            # 查找可能的语句结束标记
            end_markers = [';', '\n', '\r', '\t', ' ', '(', ')', 'FROM', 'WHERE', 'SET', 'VALUES']
            
            for marker in end_markers:
                marker_pos = context.find(marker, start + len(keyword))
                if marker_pos > 0 and marker_pos < end:
                    # 对于某些标记，需要包含它们
                    if marker in ['FROM', 'WHERE', 'SET', 'VALUES']:
                        end = marker_pos + len(marker)
                    else:
                        end = marker_pos
            
            # 如果没找到结束标记，使用固定长度
            if end == len(context):
                end = min(start + 200, len(context))
            
            sql = context[start:end].strip()
            
            # 清理SQL语句
            sql = self._clean_sql_statement(sql)
            
            return sql
            
        except Exception as e:
            logger.debug(f"提取完整SQL失败: {str(e)}")
            return ""
    
    def _clean_sql_statement(self, sql: str) -> str:
        """清理SQL语句"""
        try:
            # 移除换行符和制表符
            sql = sql.replace('\n', ' ').replace('\r', ' ').replace('\t', ' ')
            
            # 标准化空格
            sql = ' '.join(sql.split())
            
            # 移除末尾的分号
            sql = sql.rstrip(';')
            
            # 确保语句以SQL关键词开始
            sql_keywords = ['SELECT', 'INSERT', 'UPDATE', 'DELETE', 'CREATE', 'DROP', 'ALTER', 'BEGIN', 'DECLARE', 'MERGE', 'TRUNCATE', 'GRANT', 'REVOKE']
            sql_upper = sql.upper()
            
            for keyword in sql_keywords:
                if sql_upper.startswith(keyword):
                    return sql
            
            # 如果没有以SQL关键词开始，返回空字符串
            return ""
            
        except Exception as e:
            logger.debug(f"清理SQL语句失败: {str(e)}")
            return ""

    def analyze(self, payload: bytes, timestamp: float, packet_number: int,
                source_ip: str, dest_ip: str, source_port: int, dest_port: int) -> List[SQLStatement]:
        """兼容原有接口的分析方法"""
        # 对于单个数据包分析，使用原有的逻辑
        statements = []
        
        try:
            if len(payload) < 8:
                return statements

            # Oracle TNS协议分析
            try:
                packet_length = struct.unpack('>H', payload[:2])[0]
                packet_checksum = struct.unpack('>H', payload[2:4])[0]
                packet_type = payload[4]

                # TNS Data包 (type = 6)
                if packet_type == 6 and len(payload) > 8:
                    # 尝试在数据部分查找SQL
                    data_part = payload[8:]
                    payload_str = data_part.decode('utf-8', errors='ignore')

                    # Oracle SQL模式 - 增强版
                    sql_patterns = [
                        # 基础SQL模式
                        r'\b(SELECT\s+.+?(?:FROM|;|\s*$))',
                        r'\b(INSERT\s+INTO\s+.+?(?:VALUES|SELECT|;|\s*$))',
                        r'\b(UPDATE\s+.+?(?:SET|;|\s*$))',
                        r'\b(DELETE\s+FROM\s+.+?(?:WHERE|;|\s*$))',
                        r'\b(CREATE\s+(?:TABLE|SEQUENCE|INDEX|VIEW|PROCEDURE|FUNCTION|PACKAGE)\s+.+?(?:;|\s*$))',
                        r'\b(DROP\s+(?:TABLE|SEQUENCE|INDEX|VIEW|PROCEDURE|FUNCTION|PACKAGE)\s+.+?(?:;|\s*$))',
                        r'\b(ALTER\s+TABLE\s+.+?(?:;|\s*$))',
                        # Oracle特有模式
                        r'\b(BEGIN\s+.+?END\s*;)',
                        r'\b(DECLARE\s+.+?BEGIN\s+.+?END\s*;)',
                        r'\b(MERGE\s+INTO\s+.+?(?:;|\s*$))',
                        r'\b(TRUNCATE\s+TABLE\s+.+?(?:;|\s*$))',
                        r'\b(GRANT\s+.+?(?:TO|;|\s*$))',
                        r'\b(REVOKE\s+.+?(?:FROM|;|\s*$))',
                        # PL/SQL块
                        r'(DECLARE\s+.+?BEGIN\s+.+?END\s*/)',
                        r'(BEGIN\s+.+?END\s*/)'
                    ]

                    for pattern in sql_patterns:
                        matches = re.finditer(pattern, payload_str, re.IGNORECASE | re.DOTALL)
                        for match in matches:
                            sql = self._clean_sql_statement(match.group(1))
                            if sql and len(sql) > 10:
                                sql_type = self._classify_sql_type(sql)
                                confidence = 0.8

                                statements.append(SQLStatement(
                                    sql=sql,
                                    sql_type=sql_type,
                                    database_type=self.database_type,
                                    timestamp=timestamp,
                                    packet_number=packet_number,
                                    source_ip=source_ip,
                                    dest_ip=dest_ip,
                                    source_port=source_port,
                                    dest_port=dest_port,
                                    confidence=confidence
                                ))

            except (struct.error, UnicodeDecodeError):
                # 如果TNS解析失败，尝试通用SQL模式匹配
                try:
                    payload_str = payload.decode('utf-8', errors='ignore')
                    sql_patterns = [
                        r'\b(SELECT\s+.+?(?:FROM|;|$))',
                        r'\b(INSERT\s+INTO\s+.+?(?:VALUES|;|$))',
                        r'\b(UPDATE\s+.+?(?:SET|;|$))',
                        r'\b(DELETE\s+FROM\s+.+?(?:WHERE|;|$))'
                    ]

                    for pattern in sql_patterns:
                        matches = re.finditer(pattern, payload_str, re.IGNORECASE | re.DOTALL)
                        for match in matches:
                            sql = self._clean_sql_statement(match.group(1))
                            if sql and len(sql) > 10:
                                sql_type = self._classify_sql_type(sql)
                                confidence = 0.6

                                if not any(stmt.sql == sql for stmt in statements):
                                    statements.append(SQLStatement(
                                        sql=sql,
                                        sql_type=sql_type,
                                        database_type=self.database_type,
                                        timestamp=timestamp,
                                        packet_number=packet_number,
                                        source_ip=source_ip,
                                        dest_ip=dest_ip,
                                        source_port=source_port,
                                        dest_port=dest_port,
                                        confidence=confidence
                                    ))
                except UnicodeDecodeError:
                    pass

        except Exception as e:
            logger.debug(f"Oracle协议分析错误: {str(e)}")

        return statements
    
    def _classify_sql_type(self, sql: str) -> SQLType:
        """分类SQL语句类型"""
        sql_upper = sql.strip().upper()
        
        if sql_upper.startswith('SELECT') or sql_upper.startswith('WITH'):
            return SQLType.SELECT
        elif sql_upper.startswith('INSERT'):
            return SQLType.INSERT
        elif sql_upper.startswith('UPDATE'):
            return SQLType.UPDATE
        elif sql_upper.startswith('DELETE'):
            return SQLType.DELETE
        elif sql_upper.startswith('CREATE'):
            return SQLType.CREATE
        elif sql_upper.startswith('DROP'):
            return SQLType.DROP
        elif sql_upper.startswith('ALTER'):
            return SQLType.ALTER
        else:
            return SQLType.UNKNOWN

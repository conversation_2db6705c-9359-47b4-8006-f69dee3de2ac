import asyncio
import logging
from typing import Dict, Any, List, Optional
from langchain.agents import Agent<PERSON><PERSON>cut<PERSON>, create_openai_tools_agent
from langchain.prompts import Chat<PERSON>romptTemplate
from langchain_openai import ChatOpenAI
from langchain.schema import HumanMessage, SystemMessage
from tools.mysql_mcp_tools import MySQLMCPQueryTool, MySQLMCPSchemaTool, MySQLMCPPacketCaptureTool
from services.mysql_service import MySQLService
from utils.config import Config
from services.bulk_data_generator_service import bulk_data_generator_service

logger = logging.getLogger(__name__)

class MySQLMCPAgentService:
    """MySQL MCP代理服务 - 使用langchain集成MCP工具，实现自然语言到SQL的转换和执行"""
    
    def __init__(self):
        """初始化MySQL MCP代理服务"""
        self.llm = self._initialize_llm()
        self.tools = self._initialize_tools()
        self.agent_executor = self._create_agent()
        self.mysql_service = None
        logger.info("MySQL MCP Agent Service initialized")
    
    def _initialize_llm(self):
        """初始化语言模型"""
        try:
            # 使用DeepSeek API
            return ChatOpenAI(
                model="deepseek-chat",
                openai_api_key=Config.DEEPSEEK_API_KEY,
                openai_api_base=Config.DEEPSEEK_BASE_URL,
                temperature=0.1,
                max_tokens=4096
            )
        except Exception as e:
            logger.error(f"Failed to initialize LLM: {str(e)}")
            raise
    
    def _initialize_tools(self):
        """初始化工具列表"""
        return [
            MySQLMCPQueryTool(),
            MySQLMCPSchemaTool(),
            MySQLMCPPacketCaptureTool()
        ]
    
    def _create_agent(self):
        """创建langchain代理"""
        try:
            # 创建提示模板
            prompt = ChatPromptTemplate.from_messages([
                ("system", """你是一个专业的MySQL数据库专家和助手。你可以：

1. 将自然语言转换为准确的SQL查询语句
2. 执行SQL查询并返回结果
3. 获取数据库架构信息（数据库列表、表列表、表结构）
4. 控制数据包捕获功能

可用工具：
- mysql_mcp_query: 执行SQL查询，可选择是否同时进行数据包捕获
- mysql_mcp_schema: 获取数据库架构信息
- mysql_mcp_packet_capture: 控制数据包捕获（start/stop/status）

当用户要求执行查询并抓包时，请使用mysql_mcp_query工具并设置capture_packets=True。
当用户只是询问数据库结构时，使用mysql_mcp_schema工具。
当用户明确要求控制抓包时，使用mysql_mcp_packet_capture工具。

请根据用户的自然语言请求，选择合适的工具并提供准确的参数。"""),
                ("user", "{input}"),
                ("assistant", "{agent_scratchpad}")
            ])
            
            # 创建代理
            agent = create_openai_tools_agent(self.llm, self.tools, prompt)
            
            # 创建代理执行器
            return AgentExecutor(
                agent=agent,
                tools=self.tools,
                verbose=True,
                max_iterations=5,
                handle_parsing_errors=True
            )
            
        except Exception as e:
            logger.error(f"Failed to create agent: {str(e)}")
            raise
    
    def _get_mysql_service(self):
        """获取MySQL服务实例"""
        if self.mysql_service is None:
            # 如果有当前配置，使用当前配置；否则使用默认配置
            if hasattr(self, 'current_config') and self.current_config:
                self.mysql_service = MySQLService(
                    host=self.current_config.host,
                    port=self.current_config.port,
                    user=self.current_config.user,
                    password=self.current_config.password,
                    database=self.current_config.database_name
                )
            else:
                mysql_config = Config.get_mysql_config()
                self.mysql_service = MySQLService(**mysql_config)
        return self.mysql_service

    async def configure_mysql_connection(self, config_id: int):
        """根据配置ID动态配置MySQL连接"""
        try:
            from services.database_config_service import database_config_service

            # 获取数据库配置
            config = await database_config_service.get_config(config_id)
            if not config:
                raise Exception(f"Database config not found: {config_id}")

            # 保存当前配置
            self.current_config = config

            # 重置MySQL服务实例，强制重新创建
            self.mysql_service = None

            logger.info(f"MCP Agent MySQL configured for {config.host}:{config.port}/{config.database_name}")

            return {
                "success": True,
                "config": {
                    "host": config.host,
                    "port": config.port,
                    "database": config.database_name,
                    "user": config.user
                }
            }

        except Exception as e:
            logger.error(f"Failed to configure MCP Agent MySQL connection: {str(e)}")
            raise Exception(f"MCP Agent MySQL configuration failed: {str(e)}")

    async def process_natural_language_query(
        self, 
        natural_query: str, 
        capture_packets: bool = False,
        database_context: Optional[str] = None
    ) -> Dict[str, Any]:
        """处理自然语言查询"""
        try:
            logger.info(f"Processing natural language query: {natural_query}")
            
            # 构建输入
            input_text = natural_query
            if capture_packets:
                input_text += " (请同时进行数据包捕获)"
            
            if database_context:
                input_text = f"在数据库 {database_context} 中，{input_text}"
            
            # 执行代理
            result = await asyncio.get_event_loop().run_in_executor(
                None, 
                lambda: self.agent_executor.invoke({"input": input_text})
            )
            
            return {
                "success": True,
                "query": natural_query,
                "result": result.get("output", ""),
                "intermediate_steps": result.get("intermediate_steps", [])
            }
            
        except Exception as e:
            logger.error(f"Failed to process natural language query: {str(e)}")
            return {
                "success": False,
                "query": natural_query,
                "error": str(e)
            }
    
    async def execute_sql_with_capture(
        self, 
        sql_query: str, 
        capture_packets: bool = True
    ) -> Dict[str, Any]:
        """执行SQL查询并可选择进行数据包捕获"""
        try:
            logger.info(f"Executing SQL with capture: {sql_query}")
            
            # 直接使用MCP查询工具
            query_tool = MySQLMCPQueryTool()
            result = await query_tool._arun(sql_query, capture_packets)
            
            return {
                "success": True,
                "sql_query": sql_query,
                "result": result,
                "capture_enabled": capture_packets
            }
            
        except Exception as e:
            logger.error(f"Failed to execute SQL with capture: {str(e)}")
            return {
                "success": False,
                "sql_query": sql_query,
                "error": str(e)
            }
    
    async def get_database_schema(
        self, 
        database: Optional[str] = None, 
        table: Optional[str] = None
    ) -> Dict[str, Any]:
        """获取数据库架构信息"""
        try:
            logger.info(f"Getting database schema: database={database}, table={table}")
            
            # 使用MCP架构工具
            schema_tool = MySQLMCPSchemaTool()
            result = await schema_tool._arun(database, table)
            
            return {
                "success": True,
                "database": database,
                "table": table,
                "schema": result
            }
            
        except Exception as e:
            logger.error(f"Failed to get database schema: {str(e)}")
            return {
                "success": False,
                "database": database,
                "table": table,
                "error": str(e)
            }
    
    async def generate_sql_from_natural_language(
        self,
        natural_query: str,
        database_context: Optional[str] = None,
        config_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """从自然语言生成SQL查询（不执行）"""
        try:
            logger.info(f"Generating SQL from natural language: {natural_query}")
            
            # 首先检查是否为大批量操作请求
            if bulk_data_generator_service.detect_bulk_operation(natural_query):
                logger.info("Detected bulk operation request, using bulk data generator")
                
                # 使用大批量数据生成服务处理
                bulk_result = await bulk_data_generator_service.process_bulk_operation_request(
                    natural_query, config_id
                )
                
                if bulk_result.get("success"):
                    return {
                        "success": True,
                        "natural_query": natural_query,
                        "sql_query": bulk_result.get("generated_sql"),
                        "database_context": database_context,
                        "is_bulk_operation": True,
                        "bulk_details": {
                            "detected_quantity": bulk_result.get("detected_quantity"),
                            "detected_table": bulk_result.get("detected_table"),
                            "detected_operation": bulk_result.get("detected_operation"),
                            "estimated_execution_time": bulk_result.get("estimated_execution_time")
                        }
                    }
                else:
                    # 如果大批量处理失败，继续使用常规AI处理
                    logger.warning(f"Bulk operation processing failed: {bulk_result.get('error')}, falling back to AI")

            # 获取数据库架构信息作为上下文
            schema_context = ""
            if database_context and config_id:
                try:
                    # 使用指定配置的MySQL服务
                    from services.database_config_service import database_config_service
                    config = await database_config_service.get_config(config_id)
                    if config:
                        mysql_service = MySQLService(
                            host=config.host,
                            port=config.port,
                            user=config.user,
                            password=config.password,
                            database=config.database_name
                        )
                        await mysql_service.initialize()

                        # 获取数据库信息
                        databases = await mysql_service.get_databases()
                        tables = await mysql_service.get_tables(database_context)

                        schema_context = f"\n数据库架构信息:\n"
                        schema_context += f"当前数据库: {database_context}\n"
                        schema_context += f"可用数据库: {', '.join(databases)}\n"
                        schema_context += f"当前数据库中的表: {', '.join([t.get('name', str(t)) if isinstance(t, dict) else str(t) for t in tables])}\n"

                        # 获取几个主要表的结构
                        for table_info in tables[:3]:
                            table_name = table_info.get('name') if isinstance(table_info, dict) else str(table_info)
                            try:
                                structure = await mysql_service.get_table_structure(table_name, database_context)
                                schema_context += f"\n表 {table_name} 结构:\n{structure}\n"
                            except Exception as e:
                                logger.warning(f"Failed to get structure for table {table_name}: {str(e)}")

                        await mysql_service.close()

                except Exception as e:
                    logger.warning(f"Failed to get database schema: {str(e)}")

            # 构建增强的提示
            prompt = f"""请将以下自然语言转换为完整的、可执行的MySQL SQL查询语句。

自然语言查询: {natural_query}
{schema_context}

要求:
1. 生成完整的、语法正确的MySQL SQL语句
2. **重要：如果是CREATE TABLE语句，必须先生成DROP TABLE IF EXISTS table_name;语句，然后再生成CREATE TABLE语句**
3. CREATE TABLE语句必须包含完整的字段定义和数据类型
4. 如果是INSERT语句，提供具体的示例数据
5. 每个语句必须以分号结尾
6. 只返回SQL语句，不要包含任何解释文字

**创建表的标准格式：**
DROP TABLE IF EXISTS table_name;
CREATE TABLE table_name (字段定义...);

请返回准确的SQL查询语句："""

            # 调用LLM生成SQL
            response = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.llm.invoke([HumanMessage(content=prompt)])
            )

            sql_query = response.content.strip()

            # 清理SQL查询（移除可能的markdown格式）
            if sql_query.startswith("```sql"):
                sql_query = sql_query[6:]
            if sql_query.endswith("```"):
                sql_query = sql_query[:-3]
            sql_query = sql_query.strip()

            # 后处理：确保CREATE TABLE语句前包含DROP TABLE语句
            import re
            if re.search(r'\bCREATE\s+TABLE\s+(\w+)', sql_query, re.IGNORECASE):
                # 提取表名
                match = re.search(r'\bCREATE\s+TABLE\s+(\w+)', sql_query, re.IGNORECASE)
                if match:
                    table_name = match.group(1)
                    drop_statement = f"DROP TABLE IF EXISTS {table_name};"

                    # 检查是否已经包含DROP TABLE语句
                    if not re.search(rf'\bDROP\s+TABLE\s+.*{table_name}', sql_query, re.IGNORECASE):
                        # 在CREATE TABLE前添加DROP TABLE语句
                        sql_query = drop_statement + "\n" + sql_query
                        logger.info(f"Added DROP TABLE statement for table: {table_name}")

            return {
                "success": True,
                "natural_query": natural_query,
                "sql_query": sql_query,
                "database_context": database_context
            }

        except Exception as e:
            logger.error(f"Failed to generate SQL from natural language: {str(e)}")
            return {
                "success": False,
                "natural_query": natural_query,
                "error": str(e)
            }

# 全局MySQL MCP代理服务实例
mysql_mcp_agent_service = MySQLMCPAgentService()

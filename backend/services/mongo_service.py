import logging
import traceback
import asyncio
from typing import Dict, Any, List, Optional
from datetime import timezone, timedelta
from pymongo import MongoClient
from pymongo.errors import ConnectionFailure, OperationFailure, ServerSelectionTimeoutError
import re
import json
from bson import ObjectId
from bson import json_util as bson_json_util
from urllib.parse import quote_plus
from utils.config import Config
from utils.environment_detector import get_environment_detector
import subprocess
import tempfile
import os

logger = logging.getLogger(__name__)

class MongoService:
    """MongoDB服务类"""
    
    def __init__(self, host: str = None, port: int = None, user: str = None,
                 password: str = None, database: str = None, auth_source: str = None):
        """初始化MongoDB服务"""
        # 连接参数
        self.host = host or Config.MONGO_HOST
        self.port = port or Config.MONGO_PORT
        self.user = user or Config.MONGO_USER
        self.password = password or Config.MONGO_PASSWORD
        self.database = database or Config.MONGO_DATABASE
        self.auth_source = auth_source or Config.MONGO_AUTH_SOURCE

        # 非持久连接
        self.client = None
        self.db = None

        # 持久连接（抓包用）
        self._persistent_client = None
        self._persistent_db = None
        self._persistent_session = None

        # 会话和事务管理
        self._sessions = {}  # 存储活跃的会话
        self._current_session = None  # 当前会话
        self._transaction_stack = []  # 事务堆栈
        
        # 批量操作管理
        self._bulk_operations = {}  # 存储活跃的批量操作
        
        # JavaScript解析器
        self._js_context = {}  # JavaScript变量上下文

        # 环境检测和C执行器配置
        self.env_detector = get_environment_detector()
        self.use_c_executor, self.c_executor_path = self.env_detector.should_use_c_executor('mongodb')

        logger.info(f"MongoDB Service initialized for {self.host}:{self.port}")
        logger.info(f"Environment: {self.env_detector.system} ({self.env_detector.machine})")
        logger.info(f"Docker: {self.env_detector.is_docker}")
        logger.info(f"C Executor: {'Enabled' if self.use_c_executor else 'Disabled'}")
        if self.c_executor_path:
            logger.info(f"C Executor Path: {self.c_executor_path}")
    
    async def initialize(self):
        """初始化MongoDB连接"""
        try:
            await self.connect()
            logger.info("MongoDB service initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize MongoDB service: {str(e)}")
            raise
    
    async def connect(self) -> bool:
        """连接到MongoDB"""
        try:
            # 构建连接URI
            if self.user and self.password:
                # 对用户名和密码进行URL编码以处理特殊字符
                encoded_user = quote_plus(self.user)
                encoded_password = quote_plus(self.password)
                uri = f"mongodb://{encoded_user}:{encoded_password}@{self.host}:{self.port}/{self.database}?authSource={self.auth_source}"
            else:
                uri = f"mongodb://{self.host}:{self.port}/{self.database}"
            
            # 创建客户端连接，使用更温和的连接参数
            # 注意：不设置w=0，因为事务需要acknowledged write concern
            self.client = MongoClient(
                uri,
                serverSelectionTimeoutMS=5000,  # 5秒超时
                connectTimeoutMS=5000,
                socketTimeoutMS=5000,
                tz_aware=True,  # 启用时区感知
                tzinfo=timezone(timedelta(hours=8)),  # 设置为+8时区
                maxPoolSize=1,  # 限制连接池大小
                minPoolSize=0,  # 最小连接池大小
                maxIdleTimeMS=30000,  # 30秒空闲超时
                waitQueueTimeoutMS=5000,  # 等待队列超时
                heartbeatFrequencyMS=10000,  # 心跳频率
                retryWrites=False,  # 禁用重试写入
                retryReads=False   # 禁用重试读取
            )
            
            # 测试连接
            await asyncio.get_event_loop().run_in_executor(
                None, self.client.admin.command, 'ping'
            )
            
            # 选择数据库
            self.db = self.client[self.database]
            
            logger.info(f"Connected to MongoDB: {self.host}:{self.port}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to MongoDB: {str(e)}")
            return False
    
    async def check_connection(self) -> bool:
        """检查MongoDB连接状态"""
        try:
            if not self.client:
                return await self.connect()
            
            # 执行ping命令测试连接
            await asyncio.get_event_loop().run_in_executor(
                None, self.client.admin.command, 'ping'
            )
            return True
            
        except Exception as e:
            logger.error(f"MongoDB connection check failed: {str(e)}")
            return False

    async def create_persistent_connection(self) -> bool:
        """创建持久连接用于抓包"""
        try:
            if self._persistent_client:
                # 检查现有连接是否有效
                try:
                    await asyncio.get_event_loop().run_in_executor(
                        None, self._persistent_client.admin.command, 'ping'
                    )
                    logger.info("Existing MongoDB persistent connection is valid")
                    return True
                except:
                    # 现有连接无效，关闭并重新创建
                    try:
                        self._persistent_client.close()
                    except Exception as e:

                        logger.error(f"操作失败: {type(e).__name__}: {str(e)}")

                        logger.error(f"异常堆栈跟踪: {traceback.format_exc()}")
                    self._persistent_client = None
                    self._persistent_db = None

            # 构建连接URI
            if self.user and self.password:
                # 对用户名和密码进行URL编码以处理特殊字符
                encoded_user = quote_plus(self.user)
                encoded_password = quote_plus(self.password)
                uri = f"mongodb://{encoded_user}:{encoded_password}@{self.host}:{self.port}/{self.database}?authSource={self.auth_source}"
            else:
                uri = f"mongodb://{self.host}:{self.port}/{self.database}"

            # 创建新的持久连接，使用更温和的连接参数
            self._persistent_client = MongoClient(
                uri,
                serverSelectionTimeoutMS=5000,  # 5秒超时
                connectTimeoutMS=5000,
                socketTimeoutMS=5000,
                maxPoolSize=1,  # 限制连接池大小
                minPoolSize=0,  # 最小连接池大小
                maxIdleTimeMS=30000,  # 30秒空闲超时
                waitQueueTimeoutMS=5000,  # 等待队列超时
                heartbeatFrequencyMS=10000,  # 心跳频率
                retryWrites=False,  # 禁用重试写入
                retryReads=False   # 禁用重试读取
            )

            # 测试连接
            await asyncio.get_event_loop().run_in_executor(
                None, self._persistent_client.admin.command, 'ping'
            )

            # 选择数据库
            self._persistent_db = self._persistent_client[self.database]

            logger.info("Persistent MongoDB connection created successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to create persistent MongoDB connection: {str(e)}")
            self._persistent_client = None
            self._persistent_db = None
            return False

    async def create_fresh_connection(self) -> bool:
        """创建全新的连接用于抓包（确保能捕获握手包）"""
        try:
            # 强制关闭现有连接
            if self._persistent_client:
                try:
                    self._persistent_client.close()
                except Exception as e:
                    logger.debug(f"Error closing existing MongoDB connection: {e}")
                self._persistent_client = None
                self._persistent_db = None

            # 等待一小段时间确保连接完全关闭
            await asyncio.sleep(0.5)

            # 构建连接URI
            if self.user and self.password:
                # 对用户名和密码进行URL编码以处理特殊字符
                encoded_user = quote_plus(self.user)
                encoded_password = quote_plus(self.password)
                uri = f"mongodb://{encoded_user}:{encoded_password}@{self.host}:{self.port}/{self.database}?authSource={self.auth_source}"
            else:
                uri = f"mongodb://{self.host}:{self.port}/{self.database}"

            # 创建全新的连接，使用更温和的连接参数
            self._persistent_client = MongoClient(
                uri,
                serverSelectionTimeoutMS=5000,  # 5秒超时
                connectTimeoutMS=5000,
                socketTimeoutMS=5000,
                maxPoolSize=1,  # 限制连接池大小
                minPoolSize=0,  # 最小连接池大小
                maxIdleTimeMS=30000,  # 30秒空闲超时
                waitQueueTimeoutMS=5000,  # 等待队列超时
                heartbeatFrequencyMS=10000,  # 心跳频率
                retryWrites=False,  # 禁用重试写入
                retryReads=False   # 禁用重试读取
            )

            # 测试连接
            await asyncio.get_event_loop().run_in_executor(
                None, self._persistent_client.admin.command, 'ping'
            )

            # 选择数据库
            self._persistent_db = self._persistent_client[self.database]

            logger.info("Fresh MongoDB connection created successfully for packet capture")
            return True

        except Exception as e:
            logger.error(f"Failed to create fresh MongoDB connection: {str(e)}")
            self._persistent_client = None
            self._persistent_db = None
            return False

    async def close_persistent_connection(self):
        """关闭持久连接 - 使用多种策略避免RST包"""
        try:
            if self._persistent_client:
                # 若存在事务会话，先尝试结束
                try:
                    if self._persistent_session is not None:
                        try:
                            self._persistent_session.end_session()
                        except Exception as e:
                            logger.warning(f"结束MongoDB会话时发生异常: {e}")
                        self._persistent_session = None
                except Exception as e:
                    logger.warning(f"关闭MongoDB持久连接时发生异常: {e}")
                # 策略1: 逐步减少连接活动
                try:
                    # 执行一个轻量级命令确保连接活跃
                    await asyncio.get_event_loop().run_in_executor(
                        None, self._persistent_client.admin.command, {'ping': 1}
                    )
                    logger.debug("MongoDB ping successful before closing")

                    # 等待命令完成
                    await asyncio.sleep(0.1)

                except Exception as e:
                    logger.debug(f"Ping failed before closing: {e}")

                # 策略2: 尝试优雅的会话结束
                try:
                    # 执行endSessions命令（如果支持）
                    await asyncio.get_event_loop().run_in_executor(
                        None, self._persistent_client.admin.command, {'endSessions': []}
                    )
                    logger.debug("MongoDB endSessions executed")
                except:
                    # 如果不支持endSessions，尝试logout
                    try:
                        await asyncio.get_event_loop().run_in_executor(
                            None, self._persistent_client.admin.command, {'logout': 1}
                        )
                        logger.debug("MongoDB logout executed")
                    except:
                        logger.debug("Neither endSessions nor logout supported")

                # 策略3: 让连接自然空闲一段时间
                logger.debug("Waiting for connection to idle...")
                await asyncio.sleep(0.5)

                # 策略4: 使用温和的关闭方式
                logger.debug("Closing MongoDB connection...")

                # 不直接调用close()，而是让连接自然超时
                # 将客户端引用设为None，让垃圾回收器处理
                old_client = self._persistent_client
                self._persistent_client = None
                self._persistent_db = None

                # 在后台线程中关闭连接，避免阻塞
                def close_in_background():
                    try:
                        old_client.close()
                    except Exception as e:

                        logger.error(f"操作失败: {type(e).__name__}: {str(e)}")

                        logger.error(f"异常堆栈跟踪: {traceback.format_exc()}")
                await asyncio.get_event_loop().run_in_executor(None, close_in_background)

                # 最后等待确保关闭完成
                await asyncio.sleep(0.5)

                logger.info("Persistent MongoDB connection closed with graceful strategy")
        except Exception as e:
            logger.error(f"Error closing persistent MongoDB connection: {str(e)}")
            self._persistent_client = None
            self._persistent_db = None
            self._persistent_session = None

    async def force_close_connection(self):
        """强制关闭连接（确保能捕获挥手包）"""
        try:
            if self._persistent_client:
                logger.info("Force closing MongoDB connection for packet capture")

                # 执行一个简单查询确保连接活跃
                try:
                    await asyncio.get_event_loop().run_in_executor(
                        None, self._persistent_client.admin.command, {'ping': 1}
                    )
                    logger.debug("MongoDB connection is active before closing")
                except Exception as e:
                    logger.debug(f"MongoDB connection test failed before closing: {e}")

                # 强制关闭连接
                self._persistent_client.close()
                self._persistent_client = None
                self._persistent_db = None
                self._persistent_session = None

                # 等待一小段时间确保挥手包被发送
                await asyncio.sleep(0.5)

                logger.info("MongoDB connection force closed successfully")
            else:
                logger.warning("No persistent MongoDB connection to close")

        except Exception as e:
            logger.error(f"Error during MongoDB force close: {str(e)}")
            self._persistent_client = None
            self._persistent_db = None

    async def execute_mongo_query(self, query: str) -> Dict[str, Any]:
        """
        执行MongoDB查询
        
        Args:
            query (str): MongoDB查询语句
            
        Returns:
            Dict[str, Any]: 查询结果
        """
        try:
            query = query.strip()
            if not query:
                return {"success": False, "error": "Empty query"}

            # 预处理：移除多余的换行符和空白符，但保持语句结构
            # 特别处理复杂的事务语句
            query = self._preprocess_complex_query(query)

            # 检查是否是for循环语句，如果是则直接处理
            if query.startswith('for') and '(' in query and ')' in query and '{' in query:
                logger.info(f"Directly handling for loop query: {query}")
                return await self._handle_for_loop_operation(query)
            
            # 检查是否是变量声明语句
            if any(query.startswith(prefix) for prefix in ['var ', 'let ', 'const ', '=']):
                logger.info(f"Handling variable declaration: {query}")
                return await self._handle_variable_declaration(query)

            # 检查是否是会话相关操作（包括复杂的事务语句）
            if any(op in query for op in ['startSession', 'startTransaction', 'commitTransaction', 'abortTransaction']):
                logger.info(f"Handling session operation: {query}")
                return await self._handle_session_operation(query)

            # 检查是否是复杂的事务语句（以var session开头并包含多个语句）
            if 'var session' in query and ('startTransaction' in query or 'updateOne' in query):
                logger.info(f"Handling complex transaction query: {query}")
                return await self._handle_complex_transaction_query(query)

            # 检查是否是分片相关操作
            if query.startswith('sh.'):
                logger.info(f"Handling shard operation: {query}")
                return await self._execute_shard_operation(query)

            # 检查是否是批量操作
            if any(op in query for op in ['initializeUnorderedBulkOp', 'initializeOrderedBulkOp']):
                logger.info(f"Handling bulk operation: {query}")
                return await self._handle_bulk_operation(query)

            # 检查是否是数据库/集合操作
            db_coll_patterns = [
                r'db\.[a-zA-Z_][a-zA-Z0-9_]*\.[a-zA-Z_][a-zA-Z0-9_]*\(',
                r'db\.[a-zA-Z_][a-zA-Z0-9_]*\('
            ]
            
            if any(re.search(pattern, query) for pattern in db_coll_patterns):
                logger.info(f"Handling database/collection operation: {query}")
                return await self._execute_db_operation(query)

            # 如果以上都不匹配，尝试使用持久连接执行
            logger.info(f"Executing query with persistent connection: {query}")
            return await self.execute_mongo_query_with_persistent_connection(query)

        except Exception as e:
            logger.error(f"Failed to execute MongoDB query: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }

    def _preprocess_complex_query(self, query: str) -> str:
        """预处理复杂查询，特别是多行的JavaScript事务语句"""
        try:
            # 移除多余的空白行，但保持语句间的分隔
            lines = [line.strip() for line in query.split('\n') if line.strip()]
            if len(lines) <= 1:
                return query
            
            # 如果是多行语句，尝试连接为单行
            processed = ' '.join(lines)
            
            # 处理常见的分隔符
            processed = processed.replace('; ', '; ')
            
            # 确保分号分隔的语句格式正确
            if ';' in processed and not processed.endswith(';'):
                processed += ';'
            
            logger.info(f"Preprocessed complex query: {processed}")
            return processed
            
        except Exception as e:
            logger.warning(f"Failed to preprocess query, using original: {e}")
            return query

    async def _handle_complex_transaction_query(self, query: str) -> Dict[str, Any]:
        """处理复杂的事务查询"""
        try:
            logger.info(f"Handling complex transaction query: {query}")
            
            # 确保持久连接存在
            if not self._persistent_client:
                await self.create_persistent_connection()
            
            # 如果查询包含分号，按分号分割并依次执行
            if ';' in query:
                statements = [stmt.strip() for stmt in query.split(';') if stmt.strip()]
                results = []
                
                for i, stmt in enumerate(statements):
                    try:
                        logger.info(f"Executing transaction statement {i+1}: {stmt}")
                        result = await self._execute_single_transaction_statement(stmt)
                        results.append(result)
                    except Exception as e:
                        logger.error(f"Failed to execute statement {i+1}: {e}")
                        results.append({
                            'type': 'error',
                            'statement': stmt,
                            'error': str(e),
                            'acknowledged': False
                        })
                        # 对于事务语句，如果有错误就停止执行
                        if any(op in stmt for op in ['startTransaction', 'updateOne', 'commitTransaction']):
                            break
                
                return {
                    'type': 'transaction',
                    'operation': 'complex_transaction',
                    'statements_executed': len(results),
                    'total_statements': len(statements),
                    'results': results,
                    'acknowledged': any(r.get('acknowledged', False) for r in results)
                }
            else:
                # 单个语句，直接执行
                result = await self._execute_single_transaction_statement(query)
                return result

        except Exception as e:
            logger.error(f"Failed to handle complex transaction query: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }

    async def _execute_single_transaction_statement(self, statement: str) -> Dict[str, Any]:
        """执行单个事务语句"""
        try:
            logger.info(f"Executing single transaction statement: {statement}")
            
            # 检查是否是startTransaction语句
            if statement.startswith('startTransaction'):
                if self._persistent_session is None:
                    self._persistent_session = self._persistent_client.start_session()
                self._persistent_session.start_transaction()
                return {
                    'type': 'transaction',
                    'operation': 'startTransaction',
                    'acknowledged': True
                }
            
            # 检查是否是commitTransaction语句
            if statement.startswith('commitTransaction'):
                if self._persistent_session is not None:
                    self._persistent_session.commit_transaction()
                    self._persistent_session = None
                return {
                    'type': 'transaction',
                    'operation': 'commitTransaction',
                    'acknowledged': True
                }
            
            # 检查是否是abortTransaction语句
            if statement.startswith('abortTransaction'):
                if self._persistent_session is not None:
                    self._persistent_session.abort_transaction()
                    self._persistent_session = None
                return {
                    'type': 'transaction',
                    'operation': 'abortTransaction',
                    'acknowledged': True
                }
            
            # 执行其他语句
            result = await self._execute_db_operation_persistent(statement)
            return result

        except Exception as e:
            logger.error(f"Failed to execute single transaction statement: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }

    async def _handle_for_loop_operation(self, query: str) -> Dict[str, Any]:
        """处理for循环语句"""
        try:
            logger.info(f"Handling for loop operation: {query}")
            
            # 使用C执行器执行for循环语句
            if self.use_c_executor:
                result = await self._execute_c_executor(query)
                return result
            
            # 使用Python执行for循环语句
            else:
                result = await self._execute_python_executor(query)
                return result

        except Exception as e:
            logger.error(f"Failed to handle for loop operation: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }

    async def _handle_variable_declaration(self, query: str) -> Dict[str, Any]:
        """处理变量声明语句"""
        try:
            logger.info(f"Handling variable declaration: {query}")
            
            # 使用C执行器执行变量声明语句
            if self.use_c_executor:
                result = await self._execute_c_executor(query)
                return result
            
            # 使用Python执行变量声明语句
            else:
                result = await self._execute_python_executor(query)
                return result

        except Exception as e:
            logger.error(f"Failed to handle variable declaration: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }

    async def _handle_session_operation(self, query: str) -> Dict[str, Any]:
        """处理会话相关操作"""
        try:
            logger.info(f"Handling session operation: {query}")
            
            # 使用C执行器执行会话相关操作
            if self.use_c_executor:
                result = await self._execute_c_executor(query)
                return result
            
            # 使用Python执行会话相关操作
            else:
                result = await self._execute_python_executor(query)
                return result

        except Exception as e:
            logger.error(f"Failed to handle session operation: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }

    async def _execute_shard_operation(self, query: str) -> Dict[str, Any]:
        """执行分片相关操作"""
        try:
            logger.info(f"Executing shard operation: {query}")
            
            # 使用C执行器执行分片相关操作
            if self.use_c_executor:
                result = await self._execute_c_executor(query)
                return result
            
            # 使用Python执行分片相关操作
            else:
                result = await self._execute_python_executor(query)
                return result

        except Exception as e:
            logger.error(f"Failed to execute shard operation: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }

    async def _handle_bulk_operation(self, query: str) -> Dict[str, Any]:
        """处理批量操作"""
        try:
            logger.info(f"Handling bulk operation: {query}")
            
            # 使用C执行器执行批量操作
            if self.use_c_executor:
                result = await self._execute_c_executor(query)
                return result
            
            # 使用Python执行批量操作
            else:
                result = await self._execute_python_executor(query)
                return result

        except Exception as e:
            logger.error(f"Failed to handle bulk operation: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }

    async def _execute_db_operation(self, query: str) -> Dict[str, Any]:
        """执行数据库/集合操作"""
        try:
            logger.info(f"Executing database/collection operation: {query}")
            
            # 使用C执行器执行数据库/集合操作
            if self.use_c_executor:
                result = await self._execute_c_executor(query)
                return result
            
            # 使用Python执行数据库/集合操作
            else:
                result = await self._execute_python_executor(query)
                return result

        except Exception as e:
            logger.error(f"Failed to execute database/collection operation: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }

    async def _execute_db_operation_persistent(self, query: str) -> Dict[str, Any]:
        """使用持久连接执行数据库/集合操作"""
        try:
            logger.info(f"Executing database/collection operation with persistent connection: {query}")
            
            # 使用C执行器执行数据库/集合操作
            if self.use_c_executor:
                result = await self._execute_c_executor(query)
                return result
            
            # 使用Python执行数据库/集合操作
            else:
                result = await self._execute_python_executor(query)
                return result

        except Exception as e:
            logger.error(f"Failed to execute database/collection operation with persistent connection: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }

    async def execute_mongo_query_with_persistent_connection(self, query: str) -> Dict[str, Any]:
        """
        使用持久连接执行MongoDB查询
        
        Args:
            query (str): MongoDB查询语句
            
        Returns:
            Dict[str, Any]: 查询结果
        """
        try:
            query = query.strip()
            if not query:
                return {"success": False, "error": "Empty query"}

            # 检查是否是for循环语句，如果是则直接处理
            # 特别处理完整for循环语句，避免被错误地分割处理
            # 使用更复杂的正则表达式匹配完整的for循环语句
            for_loop_pattern = r'for\s*\(\s*(?:var|let)?\s*\w+\s*=\s*\d+\s*;\s*\w+\s*<\s*\d+\s*;\s*\w+\+\+\s*\)\s*\{[^}]+\}'
            if re.search(for_loop_pattern, query):
                logger.info(f"Directly handling for loop query with persistent connection: {query}")
                return await self._handle_for_loop_operation(query)
            
            # 检查是否是变量声明语句
            if any(query.startswith(prefix) for prefix in ['var ', 'let ', 'const ', '=']):
                logger.info(f"Handling variable declaration with persistent connection: {query}")
                # 变量声明在持久连接中暂不支持，使用普通方式处理
                return await self._handle_variable_declaration(query)

            # 检查是否是会话相关操作
            if any(op in query for op in ['startSession', 'startTransaction', 'commitTransaction', 'abortTransaction']):
                logger.info(f"Handling session operation with persistent connection: {query}")
                return await self._handle_session_operation(query)

            # 检查是否是分片相关操作
            if query.startswith('sh.'):
                logger.info(f"Handling shard operation with persistent connection: {query}")
                return await self._execute_shard_operation(query)

            # 检查是否是批量操作
            if any(op in query for op in ['initializeUnorderedBulkOp', 'initializeOrderedBulkOp']):
                logger.info(f"Handling bulk operation with persistent connection: {query}")
                return await self._handle_bulk_operation(query)

            # 检查是否是数据库/集合操作
            db_coll_patterns = [
                r'db\.[a-zA-Z_][a-zA-Z0-9_]*\.[a-zA-Z_][a-zA-Z0-9_]*\(',
                r'db\.[a-zA-Z_][a-zA-Z0-9_]*\('
            ]
            
            # 特殊处理for循环语句（即使不以for开头，也可能包含for循环）
            # 但要确保不是已经处理过的完整for循环语句的一部分
            if 'for' in query and '(' in query and '{' in query and not query.startswith('for'):
                # 再次检查是否是完整的for循环语句
                if re.search(for_loop_pattern, query):
                    logger.info(f"Handling for loop query with persistent connection: {query}")
                    return await self._handle_for_loop_operation(query)
            
            if any(re.search(pattern, query) for pattern in db_coll_patterns):
                logger.info(f"Handling database/collection operation with persistent connection: {query}")
                return await self._execute_db_operation_persistent(query)

            # 如果以上都不匹配，尝试使用普通连接执行
            logger.info(f"Falling back to normal connection for query: {query}")
            return await self._execute_db_operation(query)

        except Exception as e:
            logger.error(f"Failed to execute MongoDB query with persistent connection: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }

    async def _execute_c_executor(self, query: str) -> Dict[str, Any]:
        """使用C执行器执行MongoDB查询"""
        try:
            logger.info(f"Executing query with C executor: {query}")
            
            # 创建临时文件存储查询
            with tempfile.NamedTemporaryFile(delete=False) as temp_file:
                temp_file.write(query.encode('utf-8'))
                temp_file_path = temp_file.name

            # 构建C执行器命令
            command = [self.c_executor_path, temp_file_path]

            # 执行命令
            result = subprocess.run(command, capture_output=True, text=True)

            # 删除临时文件
            os.remove(temp_file_path)

            # 解析结果
            if result.returncode == 0:
                return json.loads(result.stdout)
            else:
                return {
                    "success": False,
                    "error": result.stderr
                }

        except Exception as e:
            logger.error(f"Failed to execute query with C executor: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }

    async def _execute_python_executor(self, query: str) -> Dict[str, Any]:
        """使用Python执行MongoDB查询"""
        try:
            logger.info(f"Executing query with Python executor: {query}")
            
            # 使用eval执行查询
            result = eval(query, self._js_context)
            
            # 如果结果是ObjectId，转换为字符串
            if isinstance(result, ObjectId):
                result = str(result)
            
            return {
                "success": True,
                "result": result
            }

        except Exception as e:
            logger.error(f"Failed to execute query with Python executor: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }

    async def _execute_single_transaction_statement(self, statement: str) -> Dict[str, Any]:
        """执行单个事务语句"""
        try:
            # 处理单个事务语句
            return await self.execute_mongo_query_with_persistent_connection(statement)
                
        except Exception as e:
            logger.error(f"Complex transaction query failed: {str(e)}")
            return {
                'type': 'transaction',
                'operation': 'complex_transaction',
                'error': str(e),
                'acknowledged': False
            }

            if not statement:
                return {
                    'type': 'noop',
                    'operation': 'empty',
                    'acknowledged': True
                }
            
            # 会话创建
            if 'var session = db.getMongo().startSession()' in statement:
                return await self.start_session_persistent()
            
            # 事务开始
            elif 'session.startTransaction()' in statement:
                return await self.start_transaction_persistent()
            
            # 事务提交
            elif 'session.commitTransaction()' in statement:
                return await self.commit_transaction_persistent()
            
            # 事务回滚
            elif 'session.abortTransaction()' in statement:
                return await self.abort_transaction_persistent()
            
            # 集合操作（如updateOne）
            elif 'var accounts = session.getDatabase(' in statement:
                # 解析获取集合变量
                pattern = r"var\s+(\w+)\s*=\s*session\.getDatabase\(['\"]([^'\"]+)['\"]\)\.(\w+)"
                match = re.search(pattern, statement)
                if match:
                    var_name = match.group(1)
                    db_name = match.group(2) 
                    collection_name = match.group(3)
                    
                    # 存储到JavaScript上下文
                    self._js_context[var_name] = {
                        'type': 'collection',
                        'database': db_name,
                        'collection': collection_name,
                        'session': self._persistent_session
                    }
                    
                    return {
                        'type': 'session',
                        'operation': 'getDatabase',
                        'variable': var_name,
                        'database': db_name,
                        'collection': collection_name,
                        'acknowledged': True
                    }
            
            # 使用变量执行操作
            elif any(var_name + '.' in statement for var_name in self._js_context.keys()):
                for var_name, var_info in self._js_context.items():
                    if statement.startswith(var_name + '.') and var_info.get('type') == 'collection':
                        operation_part = statement[len(var_name) + 1:]
                        
                        # 获取集合
                        db_name = var_info['database']
                        collection_name = var_info['collection']
                        session = var_info['session']
                        
                        if not self._persistent_client:
                            await self.create_persistent_connection()
                        
                        collection = self._persistent_client[db_name][collection_name]
                        
                        # 执行操作
                        return await self._execute_collection_operation_with_session(
                            collection, operation_part, session
                        )
            
            # 其他类型的语句，尝试常规处理
            else:
                return await self.execute_mongo_query_with_persistent_connection(statement)
                
        except Exception as e:
            logger.error(f"Single transaction statement failed: {str(e)}")
            return {
                'type': 'error',
                'statement': statement,
                'error': str(e),
                'acknowledged': False
            }

    async def execute_query(self, mongo_query: str) -> Dict[str, Any]:
        """执行查询的通用方法（兼容性别名）"""
        return await self.execute_mongo_query(mongo_query)

    async def execute_mongo_query_with_persistent_connection(self, query: str) -> Dict[str, Any]:
        """
        使用持久连接执行MongoDB查询
        
        Args:
            query (str): MongoDB查询语句
            
        Returns:
            Dict[str, Any]: 查询结果
        """
        try:
            query = query.strip()
            if not query:
                return {"success": False, "error": "Empty query"}

            # 检查是否是for循环语句，如果是则直接处理
            # 特别处理完整for循环语句，避免被错误地分割处理
            if query.startswith('for') and '(' in query and ')' in query and '{' in query:
                logger.info(f"Directly handling for loop query with persistent connection: {query}")
                return await self._handle_for_loop_operation(query)
            
            # 检查是否是变量声明语句
            if any(query.startswith(prefix) for prefix in ['var ', 'let ', 'const ', '=']):
                logger.info(f"Handling variable declaration with persistent connection: {query}")
                # 变量声明在持久连接中暂不支持，使用普通方式处理
                return await self._handle_variable_declaration(query)

            # 检查是否是会话相关操作
            if any(op in query for op in ['startSession', 'startTransaction', 'commitTransaction', 'abortTransaction']):
                logger.info(f"Handling session operation with persistent connection: {query}")
                return await self._handle_session_operation(query)

            # 检查是否是分片相关操作
            if query.startswith('sh.'):
                logger.info(f"Handling shard operation with persistent connection: {query}")
                return await self._execute_shard_operation(query)

            # 检查是否是批量操作
            if any(op in query for op in ['initializeUnorderedBulkOp', 'initializeOrderedBulkOp']):
                logger.info(f"Handling bulk operation with persistent connection: {query}")
                return await self._handle_bulk_operation(query)

            # 检查是否是数据库/集合操作
            db_coll_patterns = [
                r'db\.[a-zA-Z_][a-zA-Z0-9_]*\.[a-zA-Z_][a-zA-Z0-9_]*\(',
                r'db\.[a-zA-Z_][a-zA-Z0-9_]*\('
            ]
            
            # 特殊处理for循环语句（即使不以for开头，也可能包含for循环）
            # 但要确保不是已经处理过的完整for循环语句的一部分
            if 'for' in query and '(' in query and '{' in query and not query.startswith('for'):
                logger.info(f"Handling for loop query with persistent connection: {query}")
                return await self._handle_for_loop_operation(query)
            
            if any(re.search(pattern, query) for pattern in db_coll_patterns):
                logger.info(f"Handling database/collection operation with persistent connection: {query}")
                return await self._execute_db_operation_persistent(query)

            # 如果以上都不匹配，尝试使用普通连接执行
            logger.info(f"Falling back to normal connection for query: {query}")
            return await self._execute_db_operation(query)

        except Exception as e:
            logger.error(f"Failed to execute MongoDB query with persistent connection: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }

    # ===== 持久连接会话/事务支持 =====
    async def start_session_persistent(self) -> Dict[str, Any]:
        try:
            if self._persistent_client is None:
                raise Exception("No persistent connection for session")
            # 创建新会话，若已有则复用
            if self._persistent_session is None:
                def _start():
                    return self._persistent_client.start_session()
                self._persistent_session = await asyncio.get_event_loop().run_in_executor(None, _start)
            return {"success": True, "message": "Session started"}
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def start_transaction_persistent(self) -> Dict[str, Any]:
        try:
            if self._persistent_session is None:
                raise Exception("No active session")
            def _start_tx():
                self._persistent_session.start_transaction()
            await asyncio.get_event_loop().run_in_executor(None, _start_tx)
            return {"success": True, "message": "Transaction started"}
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def commit_transaction_persistent(self) -> Dict[str, Any]:
        try:
            if self._persistent_session is None:
                raise Exception("No active session")
            def _commit():
                self._persistent_session.commit_transaction()
            await asyncio.get_event_loop().run_in_executor(None, _commit)
            return {"success": True, "message": "Transaction committed"}
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def abort_transaction_persistent(self) -> Dict[str, Any]:
        try:
            if self._persistent_session is None:
                raise Exception("No active session")
            def _abort():
                self._persistent_session.abort_transaction()
            await asyncio.get_event_loop().run_in_executor(None, _abort)
            return {"success": True, "message": "Transaction aborted"}
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def execute_query_for_capture(self, mongo_query: str) -> Dict[str, Any]:
        """专门用于抓包的MongoDB查询执行方法（包含完整的连接生命周期）"""
        try:
            logger.info("Starting MongoDB query execution for packet capture")

            # 1. 创建全新连接（确保捕获握手包）
            if not await self.create_fresh_connection():
                raise Exception("Failed to create fresh MongoDB connection for packet capture")

            # 2. 等待一小段时间确保连接建立完成
            await asyncio.sleep(0.5)

            # 3. 执行MongoDB查询
            logger.info(f"Executing MongoDB query for capture: {mongo_query}")
            result = await self.execute_mongo_query_with_persistent_connection(mongo_query)

            # 4. 等待一小段时间确保查询完成
            await asyncio.sleep(0.5)

            logger.info("MongoDB query execution for packet capture completed")
            return result

        except Exception as e:
            logger.error(f"MongoDB query execution for capture failed: {str(e)}")
            raise

    async def _parse_and_execute_query_persistent(self, mongo_query: str) -> Dict[str, Any]:
        """使用持久连接解析并执行MongoDB查询"""
        try:
            # 移除可能的分号和空白字符
            query = mongo_query.strip().rstrip(';')

            # 单条命令容错：支持 changeStream.close() 或任意变量的 .close() 作为无操作成功
            # 以及对 db.<coll>.watch(...) 或赋值形式 "const cs = db.<coll>.watch(...)" 的模拟
            if re.search(r"\.close\(\)\s*$", query):
                return {'type': 'noop', 'operation': 'close', 'acknowledged': True}
            if 'watch(' in query:
                return {'type': 'command', 'operation': 'watch', 'acknowledged': True, 'simulated': True}

            # ==================== 新功能支持 ====================
            
            # 1. 会话和事务操作
            if ('session' in query or 'startTransaction' in query or 
                'commitTransaction' in query or 'abortTransaction' in query or
                'getMongo().startSession()' in query):
                return await self._handle_session_operation(query)
            
            # 2. 批量操作 - 只检查真正的bulk操作关键词，排除集合名包含bulk的情况
            if ('initializeUnorderedBulkOp' in query or 
                'initializeOrderedBulkOp' in query or 
                (re.search(r'(var|let|const)\s+\w*bulk\w*\s*=', query) and '.execute()' in query)):
                return await self._handle_bulk_operation(query)
            
            # 3. 增强的分片操作
            if query.startswith('sh.'):
                return await self._handle_enhanced_shard_operation(query)
            
            # 4. for循环解析
            if query.startswith('for(') or 'for(' in query:
                return await self._handle_for_loop_operation(query)
            
            # 5. 变量声明和赋值
            if query.startswith('var ') or query.startswith('let ') or query.startswith('const '):
                return await self._handle_variable_declaration(query)
            
            # ==================== 原有功能 ====================

            # 如果包含多条命令，按批处理
            if ';' in mongo_query:
                return await self._execute_multiple_commands_persistent(mongo_query)

            # 解析不同类型的MongoDB操作
            if query.startswith('db.'):
                # 处理db.adminCommand({listDatabases: 1})这样的特殊命令
                if 'adminCommand' in query and 'listDatabases' in query:
                    return await self._execute_show_command_persistent('show databases')
                return await self._execute_db_operation_persistent(query)
            elif query.startswith('use '):
                return await self._execute_use_command_persistent(query)
            elif query.startswith('show '):
                return await self._execute_show_command_persistent(query)
            else:
                # 尝试处理一些常见的自然语言转换结果
                if 'find' in query.lower() or 'select' in query.lower():
                    # 尝试转换为find操作
                    return await self._execute_db_operation_persistent(f"db.collection.find()")
                else:
                    raise Exception(f"Unsupported MongoDB query format: {query}")

        except Exception as e:
            logger.error(f"Failed to parse and execute persistent MongoDB query: {str(e)}")
            raise

    async def _parse_and_execute_query(self, mongo_query: str) -> Dict[str, Any]:
        """解析并执行MongoDB查询"""
        try:
            # 移除可能的分号和空白字符
            query = mongo_query.strip().rstrip(';')

            # 单条命令容错：支持 changeStream.close() 或任意变量的 .close() 作为无操作成功
            # 以及对 db.<coll>.watch(...) 或赋值形式 "const cs = db.<coll>.watch(...)" 的模拟
            if re.search(r"\.close\(\)\s*$", query):
                return {'type': 'noop', 'operation': 'close', 'acknowledged': True}
            if 'watch(' in query:
                return {'type': 'command', 'operation': 'watch', 'acknowledged': True, 'simulated': True}

            # 检查会话操作
            if any(keyword in query for keyword in ['startSession', 'endSession', 'startTransaction', 'commitTransaction', 'abortTransaction']):
                return await self._handle_session_operation(query)

            # 检查批量操作 - 只检查真正的bulk操作关键词，排除集合名包含bulk的情况
            if ('initializeOrderedBulkOp' in query or 
                'initializeUnorderedBulkOp' in query or 
                (re.search(r'(var|let|const)\s+\w*bulk\w*\s*=', query) and ('.insert(' in query or '.execute()' in query))):
                return await self._handle_bulk_operation(query)

            # 检查JavaScript for循环（优先级高于变量声明）
            if 'for' in query and '{' in query and '}' in query and 'db.' in query:
                logger.info("Detected JavaScript for loop, executing iteratively")
                return await self._handle_for_loop_operation(query)

            # 检查变量声明（但排除for循环）
            if any(keyword in query for keyword in ['var ', 'let ', 'const ']) and 'for(' not in query:
                return await self._handle_variable_declaration(query)

            # 检查是否包含多个MongoDB命令（用分号分隔）
            # 放宽条件：只要包含分号，就按多命令处理（支持 watch()/close() 等非 db. 起始语句）
            if ';' in query:
                return await self._execute_multiple_commands(query)

            # 解析不同类型的MongoDB操作
            if query.startswith('db.'):
                # 处理db.adminCommand({listDatabases: 1})这样的特殊命令
                if 'adminCommand' in query and 'listDatabases' in query:
                    return await self._execute_show_command('show databases')
                return await self._execute_db_operation(query)
            elif query.startswith('sh.'):
                # 处理分片操作 - 使用增强版本
                return await self._handle_enhanced_shard_operation(query)
            elif query.startswith('use '):
                return await self._execute_use_command(query)
            elif query.startswith('show '):
                return await self._execute_show_command(query)
            else:
                # 尝试处理一些常见的自然语言转换结果
                if 'listDatabases' in query or 'list databases' in query:
                    return await self._execute_show_command('show databases')
                elif 'listCollections' in query or 'list collections' in query:
                    return await self._execute_show_command('show collections')
                else:
                    raise Exception(f"Unsupported MongoDB operation: {query}")

        except Exception as e:
            logger.error(f"Failed to parse MongoDB query: {str(e)}")
            raise

    async def _execute_chained_operation(self, query: str) -> Dict[str, Any]:
        """执行链式操作，如 db.test.find().limit(5) 或 db.test.find().explain()"""
        try:
            # 解析基础操作和链式方法
            # 例如: db.test.find().limit(5) -> base: db.test.find(), chains: [limit(5)]

            # 首先找到基础操作，使用更精确的解析
            base_operation, remaining_query = self._parse_base_operation_with_chains(query)

            # 提取集合名和基础操作
            pattern = r'db\.(\w+)\.(\w+)\((.*)\)'
            match = re.match(pattern, base_operation)

            if not match:
                raise Exception(f"Invalid base operation format: {base_operation}")

            collection_name = match.group(1)
            operation = match.group(2)
            params = match.group(3)

            collection = self.db[collection_name] if self.db is not None else None

            # 解析链式方法
            chain_methods = self._parse_chain_methods(remaining_query)

            # 执行基础操作
            if operation == 'find':
                result = await self._execute_find_with_chains(collection, params, chain_methods)
            elif operation == 'count':
                # 对于count操作，直接执行count
                result = await self._execute_count_documents(collection, params)
            elif operation == 'aggregate':
                result = await self._execute_aggregate_with_chains(collection, params, chain_methods)
            else:
                raise Exception(f"Chained operations not supported for: {operation}")

            return result

        except Exception as e:
            logger.error(f"Failed to execute chained operation: {str(e)}")
            raise
    
    async def _execute_db_operation(self, query: str) -> Dict[str, Any]:
        """执行db.collection操作或db级别操作"""
        try:
            # 处理链式调用，如 db.test.find().limit(5) / .explain() / .count()
            # 但排除直接的count操作，如 db.test.count()
            if (('.limit(' in query or '.skip(' in query or '.sort(' in query or
                '.explain(' in query) or 
                ('.count(' in query and ').count(' in query)):  # 只有链式的count才处理，如 find().count()
                return await self._execute_chained_operation(query)

            # 首先检查是否是数据库级别的操作（如 db.createCollection）
            db_level_pattern = r'db\.(\w+)\((.*)\)'
            db_match = re.match(db_level_pattern, query)

            if db_match:
                operation = db_match.group(1)
                params = db_match.group(2)

                # 处理数据库级别的操作
                if operation == 'createCollection':
                    return await self._execute_create_collection(params)
                elif operation == 'createView':
                    return await self._execute_create_view(params)
                elif operation == 'dropDatabase':
                    return await self._execute_drop_database(params)
                elif operation == 'runCommand':
                    return await self._execute_run_command(params)
                else:
                    # 如果不是已知的数据库级别操作，继续尝试集合级别操作
                    pass

            # 提取集合名和操作
            pattern = r'db\.(\w+)\.(\w+)\((.*)\)'
            match = re.match(pattern, query)

            if not match:
                raise Exception(f"Invalid MongoDB query format: {query}")

            collection_name = match.group(1)
            operation = match.group(2)
            params = match.group(3)

            # 如果没有数据库连接，传递None给操作方法
            collection = self.db[collection_name] if self.db is not None else None
            
            # 根据操作类型执行相应的方法
            if operation == 'find':
                return await self._execute_find(collection, params)
            elif operation == 'findOne':
                return await self._execute_find_one(collection, params)
            elif operation == 'insert':
                return await self._execute_insert_one(collection, params)
            elif operation == 'insertOne':
                return await self._execute_insert_one(collection, params)
            elif operation == 'insertMany':
                return await self._execute_insert_many(collection, params)
            elif operation == 'updateOne':
                return await self._execute_update_one(collection, params)
            elif operation == 'updateMany':
                return await self._execute_update_many(collection, params)
            elif operation == 'deleteOne':
                return await self._execute_delete_one(collection, params)
            elif operation == 'deleteMany':
                return await self._execute_delete_many(collection, params)
            elif operation == 'aggregate':
                return await self._execute_aggregate(collection, params)
            elif operation == 'countDocuments':
                return await self._execute_count_documents(collection, params)
            elif operation == 'count':
                return await self._execute_count_documents(collection, params)
            elif operation == 'createIndex':
                return await self._execute_create_index(collection, params)
            elif operation == 'drop':
                return await self._execute_drop(collection, params)
            elif operation == 'getShardDistribution':
                return await self._execute_shard_distribution(collection, params)
            elif operation == 'stats':
                return await self._execute_collection_stats(collection, params)
            elif operation == 'explain':
                return await self._execute_explain(collection, params)
            else:
                raise Exception(f"Unsupported operation: {operation}")
                
        except Exception as e:
            logger.error(f"Failed to execute db operation: {str(e)}")
            raise
    
    async def _execute_find(self, collection, params: str) -> Dict[str, Any]:
        """执行find操作"""
        try:
            # 解析参数
            if params.strip():
                filter_doc = self._parse_json_params(params)
            else:
                filter_doc = {}
            
            # 执行查询
            cursor = await asyncio.get_event_loop().run_in_executor(
                None, collection.find, filter_doc
            )
            
            # 转换结果
            documents = []
            for doc in cursor:
                # 转换ObjectId为字符串
                doc = self._convert_objectid_to_str(doc)
                documents.append(doc)
            
            return {
                'type': 'query',
                'operation': 'find',
                'collection': collection.name,
                'data': documents,
                'count': len(documents)
            }
            
        except Exception as e:
            logger.error(f"Find operation failed: {str(e)}")
            raise

    async def _execute_find_with_chains(self, collection, params: str, chain_methods: list) -> Dict[str, Any]:
        """执行带链式方法的find操作"""
        try:
            # 解析基础查询参数
            if params.strip():
                filter_doc = self._parse_json_params(params)
            else:
                filter_doc = {}

            # 如果没有集合连接，返回模拟结果
            if collection is None:
                return {
                    'type': 'query',
                    'operation': 'find_with_chains',
                    'filter': filter_doc,
                    'chain_methods': chain_methods,
                    'results': [],
                    'simulated': True
                }

            # 创建游标
            cursor = await asyncio.get_event_loop().run_in_executor(
                None, collection.find, filter_doc
            )

            # 应用链式方法
            limit_value = None
            skip_value = None
            sort_spec = None
            explain_mode = None

            has_count = False
            for method_info in chain_methods:
                if isinstance(method_info, dict):
                    method_name = method_info['method']
                    method_params = method_info['params']
                else:
                    # 兼容旧格式
                    method_name, method_params = method_info

                if method_name == 'limit':
                    try:
                        limit_value = int(method_params) if method_params else 0
                    except ValueError:
                        raise Exception(f"Invalid limit value: {method_params}")
                elif method_name == 'skip':
                    try:
                        skip_value = int(method_params) if method_params else 0
                    except ValueError:
                        raise Exception(f"Invalid skip value: {method_params}")
                elif method_name == 'sort':
                    if method_params.strip():
                        sort_spec = self._parse_json_params(method_params)
                elif method_name == 'explain':
                    # 解析explain参数，如'executionStats'
                    explain_mode = method_params.strip().strip("'\"") if method_params.strip() else 'queryPlanner'
                elif method_name == 'count':
                    has_count = True
                else:
                    logger.warning(f"Unsupported chain method: {method_name}")

            # 如果存在count()链式调用，返回计数而不是具体文档
            if has_count:
                count = await asyncio.get_event_loop().run_in_executor(
                    None, collection.count_documents, filter_doc
                )
                return {
                    'type': 'query',
                    'operation': 'countDocuments',
                    'collection': collection.name,
                    'count': count,
                    'message': f"Collection has {count} document(s)"
                }

            # 如果有explain调用，返回执行计划而不是实际数据
            if explain_mode:
                # 应用其他链式操作到游标
                if sort_spec:
                    cursor = cursor.sort(list(sort_spec.items()))
                if skip_value:
                    cursor = cursor.skip(skip_value)
                if limit_value:
                    cursor = cursor.limit(limit_value)

                # 获取执行计划
                try:
                    if explain_mode == 'executionStats':
                        explain_result = await asyncio.get_event_loop().run_in_executor(
                            None, cursor.explain
                        )
                    else:
                        explain_result = await asyncio.get_event_loop().run_in_executor(
                            None, cursor.explain
                        )
                except Exception as e:
                    logger.warning(f"Explain operation failed: {str(e)}, using collection.explain() instead")
                    # 作为备选方案，直接从集合获取explain
                    explain_result = await asyncio.get_event_loop().run_in_executor(
                        None, lambda: collection.find(filter_doc).explain()
                    )

                return {
                    'type': 'explain',
                    'operation': 'find',
                    'collection': collection.name,
                    'explain_mode': explain_mode,
                    'execution_plan': explain_result,
                    'acknowledged': True
                }

            # 应用链式操作到游标
            if sort_spec:
                cursor = cursor.sort(list(sort_spec.items()))
            if skip_value:
                cursor = cursor.skip(skip_value)
            if limit_value:
                cursor = cursor.limit(limit_value)

            # 转换结果
            documents = []
            for doc in cursor:
                # 转换ObjectId为字符串
                doc = self._convert_objectid_to_str(doc)
                documents.append(doc)

            return {
                'type': 'query',
                'operation': 'find',
                'collection': collection.name,
                'data': documents,
                'count': len(documents),
                'applied_chains': [f"{name}({params})" for name, params in chain_methods]
            }

        except Exception as e:
            logger.error(f"Find with chains operation failed: {str(e)}")
            raise

    async def _execute_aggregate_with_chains(self, collection, params: str, chain_methods: list) -> Dict[str, Any]:
        """执行带链式方法的aggregate操作（暂时不支持）"""
        raise Exception("Chained operations for aggregate are not yet supported")

    async def _execute_aggregate(self, collection, params: str) -> Dict[str, Any]:
        """执行aggregate操作"""
        try:
            # 解析聚合管道
            if params.strip():
                pipeline = self._parse_json_params(params)
            else:
                pipeline = []

            # 如果没有集合连接，返回模拟结果
            if collection is None:
                return {
                    'type': 'aggregate',
                    'operation': 'aggregate',
                    'pipeline': pipeline,
                    'results': [],
                    'simulated': True
                }

            # 执行聚合查询
            cursor = await asyncio.get_event_loop().run_in_executor(
                None, collection.aggregate, pipeline
            )

            # 转换结果
            documents = []
            for doc in cursor:
                # 转换ObjectId为字符串
                doc = self._convert_objectid_to_str(doc)
                documents.append(doc)

            return {
                'type': 'aggregate',
                'operation': 'aggregate',
                'pipeline': pipeline,
                'results': documents,
                'count': len(documents)
            }

        except Exception as e:
            logger.error(f"Aggregate operation failed: {str(e)}")
            raise
    
    async def _execute_insert_one(self, collection, params: str) -> Dict[str, Any]:
        """执行insertOne操作"""
        try:
            document = self._parse_json_params(params)

            result = await asyncio.get_event_loop().run_in_executor(
                None, collection.insert_one, document
            )

            return {
                'type': 'modification',
                'operation': 'insertOne',
                'collection': collection.name,
                'inserted_id': str(result.inserted_id),
                'acknowledged': result.acknowledged,
                'message': f"Successfully inserted 1 document"
            }

        except Exception as e:
            logger.error(f"Insert operation failed: {str(e)}")
            raise

    async def _execute_insert_many(self, collection, params: str) -> Dict[str, Any]:
        """执行insertMany操作"""
        try:
            documents = self._parse_json_params(params)

            # 确保documents是一个列表
            if not isinstance(documents, list):
                raise ValueError("insertMany requires an array of documents")

            result = await asyncio.get_event_loop().run_in_executor(
                None, collection.insert_many, documents
            )

            return {
                'type': 'modification',
                'operation': 'insertMany',
                'collection': collection.name,
                'inserted_ids': [str(id) for id in result.inserted_ids],
                'acknowledged': result.acknowledged,
                'message': f"Successfully inserted {len(result.inserted_ids)} documents"
            }

        except Exception as e:
            logger.error(f"Insert many operation failed: {str(e)}")
            raise
    
    def _parse_json_params(self, params: str) -> Dict[str, Any]:
        """解析JSON参数"""
        try:
            # 移除外层括号和空白字符
            params = params.strip()
            if params.startswith('(') and params.endswith(')'):
                params = params[1:-1]

            # 如果参数为空，返回空字典
            if not params.strip():
                return {}

            # 简单的JavaScript表达式处理
            params = self._process_simple_javascript(params)

            # 预处理 Mongo Shell 语法 -> Extended JSON
            # 支持: ISODate("..."), new Date("..."), ObjectId("..."), NumberLong(...), NumberInt(...)
            def _shell_to_extjson(s: str) -> str:
                import re
                s = re.sub(r"ISODate\(\s*'([^']+)'\s*\)", r'{"$date":"\1"}', s)
                s = re.sub(r'ISODate\(\s*"([^"]+)"\s*\)', r'{"$date":"\1"}', s)
                s = re.sub(r"new\s+Date\(\s*'([^']+)'\s*\)", r'{"$date":"\1"}', s)
                s = re.sub(r'new\s+Date\(\s*"([^"]+)"\s*\)', r'{"$date":"\1"}', s)
                s = re.sub(r"ObjectId\(\s*'([^']+)'\s*\)", r'{"$oid":"\1"}', s)
                s = re.sub(r'ObjectId\(\s*"([^"]+)"\s*\)', r'{"$oid":"\1"}', s)
                s = re.sub(r"NumberLong\(\s*'?(\d+)'?\s*\)", r"\1", s)
                s = re.sub(r"NumberInt\(\s*(\d+)\s*\)", r"\1", s)
                return s

            params = _shell_to_extjson(params)

            # 检查是否包含变量（如 {userId: i, data: 'Sample data ' + i}）
            if self._contains_variables(params):
                logger.warning(f"Parameters contain variables that need evaluation: {params}")
                # 尝试修复常见的变量模式
                params = self._fix_variable_params(params)

            # 首先尝试直接解析JSON
            try:
                return bson_json_util.loads(params)
            except Exception:
                # 如果失败，尝试修复JavaScript对象格式
                import re

                # 先修复单引号为双引号
                fixed_params = re.sub(r"'([^']*)'", r'"\1"', params)

                # 匹配未加引号的属性名（包括MongoDB操作符如$gt, $lt等）
                fixed_params = re.sub(r'(\$?\w+):', r'"\1":', fixed_params)

                # 修复MongoDB操作符（如$gt, $lt等）
                fixed_params = re.sub(r'"\$(\w+)":', r'"$\1":', fixed_params)

                # 再次尝试解析
                return bson_json_util.loads(fixed_params)

        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON params: {params}, error: {str(e)}")
            raise Exception(f"Invalid JSON format in parameters: {params}")
        except Exception as e:
            logger.error(f"Failed to parse JSON params: {params}, error: {str(e)}")
            raise Exception(f"Invalid JSON format in parameters: {params}")

    def _process_simple_javascript(self, params: str) -> str:
        """处理简单的JavaScript表达式"""
        import random
        import time
        from datetime import datetime, timezone
        
        result = params
        
        # Math.random() - 替换为随机数
        result = re.sub(r'Math\.random\(\)', lambda m: str(random.random()), result)
        
        # Math.floor() - 替换为整数
        def replace_floor(match):
            inner = match.group(1)
            try:
                # 如果内部包含已处理的随机数，直接计算
                value = eval(inner) if inner.replace('.', '').replace('-', '').isdigit() else random.randint(0, 100)
                return str(int(value))
            except:
                return str(random.randint(0, 100))
        
        result = re.sub(r'Math\.floor\(([^)]+)\)', replace_floor, result)
        
        # new Date() - 替换为MongoDB可识别的时间字符串
        if 'new Date()' in result:
            from datetime import datetime
            current_time = datetime.now()
            # 使用MongoDB可识别的简单格式
            result = result.replace('new Date()', f'new Date("{current_time.isoformat()}")')
        
        return result

    def _parse_javascript_params(self, params: str) -> str:
        """解析JavaScript参数格式"""
        try:
            # 移除外层括号和空白字符
            params = params.strip()
            if params.startswith('(') and params.endswith(')'):
                params = params[1:-1]
            
            # 处理简单的JavaScript表达式
            params = self._process_simple_javascript(params)
            
            return params
            
        except Exception as e:
            logger.error(f"Failed to parse JavaScript params: {str(e)}")
            return params

    def _contains_variables(self, params: str) -> bool:
        """检查参数是否包含变量"""
        import re
        # 检查是否包含未引号的变量名（如 userId: i）
        variable_patterns = [
            r'\w+:\s*[a-zA-Z_]\w*(?!\s*["\'])',  # 变量赋值模式 (key: variable)
            r'\+\s*[a-zA-Z_]\w*',  # 字符串拼接模式 (+ variable)
            r'[a-zA-Z_]\w*\s*\+',  # 变量加法模式 (variable +)
            r':\s*[a-zA-Z_]\w*(?=\s*[,}])',  # 单独的变量值 (: variable,)
        ]

        for pattern in variable_patterns:
            if re.search(pattern, params):
                return True
        return False

    def _fix_variable_params(self, params: str) -> str:
        """修复包含变量的参数"""
        import re

        # 先修复单引号为双引号
        params = re.sub(r"'([^']*)'", r'"\1"', params)

        # 替换变量 i 为 1
        params = re.sub(r'\b(i|index|idx)\b(?!\s*["\'])', '1', params)

        # 处理字符串拼接表达式
        # 匹配模式: "text" + variable 或 variable + "text"
        # 例如: "user" + i -> "user1", i + "@example.com" -> "<EMAIL>"

        # 处理 "text" + variable 模式
        params = re.sub(r'"([^"]*?)"\s*\+\s*[a-zA-Z_]\w*', r'"\g<1>1"', params)

        # 处理 variable + "text" 模式
        params = re.sub(r'[a-zA-Z_]\w*\s*\+\s*"([^"]*?)"', r'"1\g<1>"', params)

        # 处理 Math.floor(Math.random() * 50) + 18 这样的表达式
        # 替换为固定值 35 (18 + 17)
        params = re.sub(r'Math\.floor\(Math\.random\(\)\s*\*\s*\d+\)\s*\+\s*\d+', '35', params)

        # 处理 Math.random() * 50 这样的表达式
        params = re.sub(r'Math\.random\(\)\s*\*\s*\d+', '25', params)

        # 处理 Math.random() 这样的表达式
        params = re.sub(r'Math\.random\(\)', '0.5', params)

        # 处理剩余的变量引用，替换为合理的默认值
        params = re.sub(r'\b[a-zA-Z_]\w*(?!\s*["\'])(?=\s*[,}])', '1', params)

        # 清理可能的语法错误
        # 移除多余的 + 号
        params = re.sub(r'\+\s*\+', '+', params)

        # 修复可能的引号不匹配问题
        # 如果字符串以引号开始但没有结束引号，添加结束引号
        params = re.sub(r'"([^"]*?)(?=\s*[,}])', r'"\1"', params)

        return params

    async def _execute_javascript_for_loop(self, query: str) -> List[Dict[str, Any]]:
        """执行JavaScript for循环，生成多个MongoDB操作"""
        import re

        results = []

        # 解析for循环参数
        for_pattern = r'for\s*\(\s*let\s+(\w+)\s*=\s*(\d+);\s*\1\s*<\s*(\d+);\s*\1\+\+\s*\)'
        for_match = re.search(for_pattern, query)

        if not for_match:
            logger.warning(f"Cannot parse for loop pattern: {query}")
            return results

        var_name = for_match.group(1)  # 通常是 'i'
        start_val = int(for_match.group(2))  # 开始值
        end_val = int(for_match.group(3))    # 结束值

        # 提取for循环体中的MongoDB命令
        loop_body_pattern = r'for\s*\([^)]*\)\s*\{\s*([^}]*)\s*\}'
        body_match = re.search(loop_body_pattern, query, re.DOTALL)

        if not body_match:
            logger.warning(f"Cannot extract for loop body: {query}")
            return results

        loop_body = body_match.group(1).strip()

        # 提取MongoDB命令模式
        mongo_cmd_pattern = r'db\.(\w+)\.(\w+)\(([^)]*)\)'
        mongo_match = re.search(mongo_cmd_pattern, loop_body)

        if not mongo_match:
            logger.warning(f"Cannot find MongoDB command in loop body: {loop_body}")
            return results

        collection_name = mongo_match.group(1)
        operation = mongo_match.group(2)
        params_template = mongo_match.group(3)

        # 执行循环，但限制最大执行次数以避免性能问题
        max_iterations = min(end_val - start_val, 100)  # 最多执行100次

        for i in range(start_val, start_val + max_iterations):
            try:
                # 替换参数中的变量
                params_str = params_template.replace(var_name, str(i))

                # 处理字符串拼接
                params_str = re.sub(r"'([^']*?)'\s*\+\s*" + str(i), f"'{r'\1'}{i}'", params_str)
                params_str = re.sub(str(i) + r"\s*\+\s*'([^']*?)'", f"'{i}{r'\1'}'", params_str)

                # 处理Math.random()等表达式
                params_str = re.sub(r'Math\.floor\(Math\.random\(\)\s*\*\s*\d+\)\s*\+\s*\d+', str(18 + i % 50), params_str)

                # 构造完整的MongoDB命令
                mongo_cmd = f"db.{collection_name}.{operation}({params_str})"

                # 执行单个MongoDB命令
                result = await self._execute_single_mongodb_command(mongo_cmd)
                results.append(result)

            except Exception as e:
                logger.error(f"Error executing iteration {i}: {str(e)}")
                continue

        return results

    async def _execute_multiple_commands(self, query: str) -> Dict[str, Any]:
        """执行多个MongoDB命令（用分号分隔）"""
        try:
            # 按分号分割命令
            commands = [cmd.strip() for cmd in query.split(';') if cmd.strip()]
            results = []

            for cmd in commands:
                if cmd.startswith('db.'):
                    # 忽略 changeStream.close() 这类关闭调用
                    if re.search(r"\.close\(\)\s*$", cmd):
                        results.append({'type': 'noop', 'operation': 'close', 'acknowledged': True})
                        continue
                    # 简单模拟 watch() 创建，不真正保持流
                    if re.search(r"\.watch\(.*\)$", cmd):
                        results.append({'type': 'command', 'operation': 'watch', 'acknowledged': True, 'simulated': True})
                        continue
                    result = await self._execute_db_operation(cmd)
                    results.append(result)
                elif cmd.startswith('use '):
                    result = await self._execute_use_command(cmd)
                    results.append(result)
                elif cmd.startswith('show '):
                    result = await self._execute_show_command(cmd)
                    results.append(result)
                else:
                    # 处理类似 const cs = db.coll.watch([...]); cs.close();
                    if re.search(r"\.close\(\)\s*$", cmd):
                        results.append({'type': 'noop', 'operation': 'close', 'acknowledged': True})
                        continue
                    if re.search(r"watch\(.*\)$", cmd):
                        results.append({'type': 'command', 'operation': 'watch', 'acknowledged': True, 'simulated': True})
                        continue
                    logger.warning(f"Skipping unsupported command: {cmd}")

            return {
                'type': 'batch',
                'operation': 'multiple_commands',
                'results': results,
                'count': len(results)
            }

        except Exception as e:
            logger.error(f"Error executing multiple commands: {str(e)}")
            raise

    async def _execute_single_mongodb_command(self, mongo_cmd: str) -> Dict[str, Any]:
        """执行单个MongoDB命令"""
        try:
            return await self._execute_db_operation(mongo_cmd)
        except Exception as e:
            logger.error(f"Error executing single MongoDB command: {str(e)}")
            raise

    def _convert_objectid_to_str(self, doc: Dict[str, Any]) -> Dict[str, Any]:
        """将ObjectId转换为字符串"""
        if isinstance(doc, dict):
            for key, value in doc.items():
                if isinstance(value, ObjectId):
                    doc[key] = str(value)
                elif isinstance(value, dict):
                    doc[key] = self._convert_objectid_to_str(value)
                elif isinstance(value, list):
                    doc[key] = [self._convert_objectid_to_str(item) if isinstance(item, dict) else item for item in value]
        return doc

    async def _execute_shard_distribution(self, collection, params: str) -> Dict[str, Any]:
        """执行getShardDistribution操作"""
        try:
            # getShardDistribution是一个集合级别的方法，返回分片分布信息
            # 对于非分片集合，返回基本统计信息
            stats = await asyncio.get_event_loop().run_in_executor(
                None, collection.estimated_document_count
            )

            return {
                'type': 'admin',
                'operation': 'getShardDistribution',
                'collection': collection.name,
                'data': {
                    'sharded': False,
                    'estimated_count': stats,
                    'message': 'Collection is not sharded or sharding info not available'
                },
                'count': 1
            }

        except Exception as e:
            logger.error(f"Shard distribution operation failed: {str(e)}")
            # 返回默认信息而不是抛出异常
            return {
                'type': 'admin',
                'operation': 'getShardDistribution',
                'collection': collection.name,
                'data': {
                    'sharded': False,
                    'message': f'Unable to get shard distribution: {str(e)}'
                },
                'count': 0
            }

    async def _execute_collection_stats(self, collection, params: str) -> Dict[str, Any]:
        """执行stats操作"""
        try:
            # 获取集合统计信息
            db = collection.database
            stats_result = await asyncio.get_event_loop().run_in_executor(
                None, db.command, "collStats", collection.name
            )

            return {
                'type': 'admin',
                'operation': 'stats',
                'collection': collection.name,
                'data': stats_result,
                'count': 1
            }

        except Exception as e:
            logger.error(f"Collection stats operation failed: {str(e)}")
            raise

    async def _execute_explain(self, collection, params: str) -> Dict[str, Any]:
        """执行explain操作"""
        try:
            # 解析查询参数
            if params.strip():
                filter_doc = self._parse_json_params(params)
            else:
                filter_doc = {}

            # 如果没有集合连接，返回模拟结果
            if collection is None:
                return {
                    'type': 'explain',
                    'operation': 'explain',
                    'filter': filter_doc,
                    'data': {'simulated': True, 'executionStats': {'totalDocsExamined': 0}},
                    'simulated': True
                }

            # 执行explain
            cursor = collection.find(filter_doc)
            explain_result = await asyncio.get_event_loop().run_in_executor(
                None, cursor.explain
            )

            return {
                'type': 'admin',
                'operation': 'explain',
                'collection': collection.name,
                'data': explain_result,
                'count': 1
            }

        except Exception as e:
            logger.error(f"Explain operation failed: {str(e)}")
            raise

    async def get_databases(self) -> List[str]:
        """获取数据库列表"""
        try:
            if not await self.check_connection():
                raise Exception("MongoDB connection failed")
            
            db_list = await asyncio.get_event_loop().run_in_executor(
                None, self.client.list_database_names
            )
            
            return db_list
            
        except Exception as e:
            logger.error(f"Failed to get databases: {str(e)}")
            raise
    
    async def get_collections(self, database: str = None) -> List[str]:
        """获取集合列表"""
        try:
            if not await self.check_connection():
                raise Exception("MongoDB connection failed")
            
            db = self.client[database] if database else self.db
            
            collection_list = await asyncio.get_event_loop().run_in_executor(
                None, db.list_collection_names
            )
            
            return collection_list
            
        except Exception as e:
            logger.error(f"Failed to get collections: {str(e)}")
            raise
    
    async def _execute_find_one(self, collection, params: str) -> Dict[str, Any]:
        """执行findOne操作"""
        try:
            if params.strip():
                filter_doc = self._parse_json_params(params)
            else:
                filter_doc = {}

            document = await asyncio.get_event_loop().run_in_executor(
                None, collection.find_one, filter_doc
            )

            if document:
                document = self._convert_objectid_to_str(document)

            return {
                'type': 'query',
                'operation': 'findOne',
                'collection': collection.name,
                'data': [document] if document else [],
                'count': 1 if document else 0
            }

        except Exception as e:
            logger.error(f"FindOne operation failed: {str(e)}")
            raise

    async def _execute_update_one(self, collection, params: str) -> Dict[str, Any]:
        """执行updateOne操作"""
        try:
            # 解析参数 - updateOne需要filter和update，可能有第3个options（如 {session} 或 {upsert:true}）
            raw_params = params
            params_list = self._parse_multiple_params(params)
            if len(params_list) < 2:
                raise Exception("updateOne requires filter and update parameters")

            filter_doc = params_list[0]
            update_doc = params_list[1]

            options = {}
            if len(params_list) >= 3:
                # 第三个参数解析后的字典（若能解析）
                try:
                    options = params_list[2] or {}
                except Exception:
                    options = {}

            # 处理session与常见选项
            kwargs = {}
            # 如果原始参数中包含 session 字样，且存在持久会话，则传递会话
            if ('session' in raw_params) and (self._persistent_session is not None):
                kwargs['session'] = self._persistent_session
            # 其他选项（如 upsert）
            if isinstance(options, dict):
                if 'upsert' in options:
                    kwargs['upsert'] = bool(options.get('upsert'))

            def _exec():
                return collection.update_one(filter_doc, update_doc, **kwargs)

            result = await asyncio.get_event_loop().run_in_executor(None, _exec)

            return {
                'type': 'modification',
                'operation': 'updateOne',
                'collection': collection.name,
                'matched_count': result.matched_count,
                'modified_count': result.modified_count,
                'acknowledged': result.acknowledged,
                'message': f"Matched {result.matched_count}, modified {result.modified_count} document(s)"
            }

        except Exception as e:
            logger.error(f"UpdateOne operation failed: {str(e)}")
            raise

    async def _execute_update_many(self, collection, params: str) -> Dict[str, Any]:
        """执行updateMany操作"""
        try:
            raw_params = params
            params_list = self._parse_multiple_params(params)
            if len(params_list) < 2:
                raise Exception("updateMany requires filter and update parameters")

            filter_doc = params_list[0]
            update_doc = params_list[1]

            options = {}
            if len(params_list) >= 3:
                try:
                    options = params_list[2] or {}
                except Exception:
                    options = {}

            kwargs = {}
            if ('session' in raw_params) and (self._persistent_session is not None):
                kwargs['session'] = self._persistent_session
            if isinstance(options, dict) and 'upsert' in options:
                kwargs['upsert'] = bool(options.get('upsert'))

            def _exec():
                return collection.update_many(filter_doc, update_doc, **kwargs)

            result = await asyncio.get_event_loop().run_in_executor(None, _exec)

            return {
                'type': 'modification',
                'operation': 'updateMany',
                'collection': collection.name,
                'matched_count': result.matched_count,
                'modified_count': result.modified_count,
                'acknowledged': result.acknowledged,
                'message': f"Matched {result.matched_count}, modified {result.modified_count} document(s)"
            }

        except Exception as e:
            logger.error(f"UpdateMany operation failed: {str(e)}")
            raise

    async def _execute_delete_one(self, collection, params: str) -> Dict[str, Any]:
        """执行deleteOne操作"""
        try:
            filter_doc = self._parse_json_params(params)

            result = await asyncio.get_event_loop().run_in_executor(
                None, collection.delete_one, filter_doc
            )

            return {
                'type': 'modification',
                'operation': 'deleteOne',
                'collection': collection.name,
                'deleted_count': result.deleted_count,
                'acknowledged': result.acknowledged,
                'message': f"Successfully deleted {result.deleted_count} document(s)"
            }

        except Exception as e:
            logger.error(f"DeleteOne operation failed: {str(e)}")
            raise

    async def _execute_delete_many(self, collection, params: str) -> Dict[str, Any]:
        """执行deleteMany操作"""
        try:
            filter_doc = self._parse_json_params(params)

            def _exec():
                kwargs = {}
                if self._persistent_session is not None and 'session' in params:
                    kwargs['session'] = self._persistent_session
                return collection.delete_many(filter_doc, **kwargs)

            result = await asyncio.get_event_loop().run_in_executor(None, _exec)

            return {
                'type': 'modification',
                'operation': 'deleteMany',
                'collection': collection.name,
                'deleted_count': result.deleted_count,
                'acknowledged': result.acknowledged,
                'message': f"Successfully deleted {result.deleted_count} document(s)"
            }

        except Exception as e:
            logger.error(f"DeleteMany operation failed: {str(e)}")
            raise

    async def _execute_count(self, collection, params: str) -> Dict[str, Any]:
        """执行countDocuments操作"""
        try:
            if params.strip():
                filter_doc = self._parse_json_params(params)
            else:
                filter_doc = {}

            # 如果没有集合连接，返回模拟结果
            if collection is None:
                return {
                    'type': 'count',
                    'operation': 'countDocuments',
                    'filter': filter_doc,
                    'count': 0,
                    'simulated': True
                }

            count = await asyncio.get_event_loop().run_in_executor(
                None, collection.count_documents, filter_doc
            )

            return {
                'type': 'query',
                'operation': 'countDocuments',
                'collection': collection.name,
                'count': count,
                'message': f"Collection has {count} document(s)"
            }

        except Exception as e:
            logger.error(f"Count operation failed: {str(e)}")
            raise

    async def _execute_drop(self, collection, params: str) -> Dict[str, Any]:
        """执行drop操作 - 删除整个集合"""
        try:
            collection_name = collection.name

            # 若目标数据库为 admin，在分片集群通常不允许删除集合；返回跳过而非抛错
            try:
                db_name = collection.database.name
            except Exception:
                db_name = ''
            if db_name == 'admin':
                logger.info("Skipping drop on 'admin' database collection")
                return {
                    'type': 'modification',
                    'operation': 'drop',
                    'collection': collection_name,
                    'message': "Skipping drop of collection in 'admin' database",
                    'acknowledged': True,
                    'skipped': True
                }

            # drop操作通常不需要参数，但为了安全起见，我们检查一下
            if params.strip():
                logger.warning(f"Drop operation received unexpected parameters: {params}")

            # 执行drop操作
            result = await asyncio.get_event_loop().run_in_executor(
                None, collection.drop
            )

            return {
                'type': 'modification',
                'operation': 'drop',
                'collection': collection_name,
                'message': f"Successfully dropped collection '{collection_name}'",
                'acknowledged': True
            }

        except OperationFailure as e:
            # 在分片集群或权限限制下，删除可能报 code 20 IllegalOperation
            msg = str(e)
            if getattr(e, 'code', None) == 20 or 'IllegalOperation' in msg or "Cannot drop collection in 'admin' database" in msg:
                logger.info(f"Gracefully skipping drop due to IllegalOperation: {msg}")
                return {
                    'type': 'modification',
                    'operation': 'drop',
                    'collection': collection.name if collection else 'unknown',
                    'message': f"Skipping drop due to IllegalOperation: {msg}",
                    'acknowledged': True,
                    'skipped': True
                }
            logger.error(f"Drop operation failed: {str(e)}")
            raise

    async def _execute_count_documents(self, collection, params: str) -> Dict[str, Any]:
        """执行countDocuments操作（与_count同名以匹配持久分支调用）"""
        try:
            if params.strip():
                filter_doc = self._parse_json_params(params)
            else:
                filter_doc = {}

            count = await asyncio.get_event_loop().run_in_executor(
                None, collection.count_documents, filter_doc
            )

            return {
                'type': 'query',
                'operation': 'countDocuments',
                'collection': collection.name,
                'count': count,
                'message': f"Collection has {count} document(s)"
            }
        except Exception as e:
            logger.error(f"countDocuments operation failed: {str(e)}")
            raise

    async def _execute_distinct(self, collection, params: str) -> Dict[str, Any]:
        """执行distinct操作"""
        try:
            # 解析参数：键名和可选过滤条件，如: 'status', {age: {$gt: 18}}
            params = params.strip()
            if params.startswith('(') and params.endswith(')'):
                params = params[1:-1]

            # 顶层逗号分割（最多两个参数）
            parts = []
            bracket = 0
            current = ''
            in_string = False
            quote = ''
            for ch in params:
                if in_string:
                    current += ch
                    if ch == quote:
                        in_string = False
                    continue
                if ch in ('"', "'"):
                    in_string = True
                    quote = ch
                    current += ch
                    continue
                if ch == '{':
                    bracket += 1
                elif ch == '}':
                    bracket -= 1
                if ch == ',' and bracket == 0:
                    parts.append(current.strip())
                    current = ''
                else:
                    current += ch
            if current.strip():
                parts.append(current.strip())

            if not parts:
                raise Exception("distinct requires at least a field name")

            field = parts[0].strip().strip("'\"")
            filter_doc = self._parse_json_params(parts[1]) if len(parts) > 1 and parts[1].strip() else {}

            values = await asyncio.get_event_loop().run_in_executor(
                None, collection.distinct, field, filter_doc
            )

            return {
                'type': 'query',
                'operation': 'distinct',
                'collection': collection.name,
                'field': field,
                'values': values,
                'count': len(values)
            }
        except Exception as e:
            logger.error(f"Distinct operation failed: {str(e)}")
            raise

    def _parse_multiple_params(self, params: str) -> List[Dict[str, Any]]:
        """解析多个JSON参数"""
        try:
            params = params.strip()
            if params.startswith('(') and params.endswith(')'):
                params = params[1:-1]

            # 简单的参数分割 - 寻找顶级逗号
            result = []
            bracket_count = 0
            current_param = ""

            for char in params:
                if char == '{':
                    bracket_count += 1
                elif char == '}':
                    bracket_count -= 1
                elif char == ',' and bracket_count == 0:
                    if current_param.strip():
                        # 使用_parse_json_params来处理单个参数，包含格式修复
                        param_dict = self._parse_json_params(current_param.strip())
                        result.append(param_dict)
                    current_param = ""
                    continue

                current_param += char

            # 添加最后一个参数
            if current_param.strip():
                # 使用_parse_json_params来处理单个参数，包含格式修复
                param_dict = self._parse_json_params(current_param.strip())
                result.append(param_dict)

            return result

        except Exception as e:
            logger.error(f"Failed to parse multiple params: {params}, error: {str(e)}")
            raise Exception(f"Invalid parameter format: {params}")

    async def _execute_use_command(self, query: str) -> Dict[str, Any]:
        """执行use命令"""
        try:
            # 提取数据库名
            db_name = query.replace('use ', '').strip()
            self.db = self.client[db_name]

            return {
                'type': 'command',
                'operation': 'use',
                'message': f"Switched to database: {db_name}"
            }

        except Exception as e:
            logger.error(f"Use command failed: {str(e)}")
            raise

    async def _execute_show_command(self, query: str) -> Dict[str, Any]:
        """执行show命令"""
        try:
            if 'databases' in query or 'dbs' in query:
                databases = await self.get_databases()
                return {
                    'type': 'query',
                    'operation': 'show databases',
                    'data': [{'name': db} for db in databases],
                    'count': len(databases)
                }
            elif 'collections' in query:
                collections = await self.get_collections()
                return {
                    'type': 'query',
                    'operation': 'show collections',
                    'data': [{'name': coll} for coll in collections],
                    'count': len(collections)
                }
            else:
                raise Exception(f"Unsupported show command: {query}")

        except Exception as e:
            logger.error(f"Show command failed: {str(e)}")
            raise

    async def close(self):
        """关闭MongoDB连接 - 使用多种策略避免RST包"""
        try:
            # 清理新功能的资源
            await self.cleanup_sessions()
            self.clear_javascript_context()
            
            if self.client:
                # 策略1: 逐步减少连接活动
                try:
                    # 执行一个轻量级命令确保连接活跃
                    await asyncio.get_event_loop().run_in_executor(
                        None, self.client.admin.command, {'ping': 1}
                    )
                    logger.debug("MongoDB ping successful before closing")

                    # 等待命令完成
                    await asyncio.sleep(0.1)

                except Exception as e:
                    logger.debug(f"Ping failed before closing: {e}")

                # 策略2: 尝试优雅的会话结束
                try:
                    # 执行endSessions命令（如果支持）
                    await asyncio.get_event_loop().run_in_executor(
                        None, self.client.admin.command, {'endSessions': []}
                    )
                    logger.debug("MongoDB endSessions executed")
                except:
                    # 如果不支持endSessions，尝试logout
                    try:
                        await asyncio.get_event_loop().run_in_executor(
                            None, self.client.admin.command, {'logout': 1}
                        )
                        logger.debug("MongoDB logout executed")
                    except:
                        logger.debug("Neither endSessions nor logout supported")

                # 策略3: 让连接自然空闲一段时间
                logger.debug("Waiting for connection to idle...")
                await asyncio.sleep(0.5)

                # 策略4: 使用温和的关闭方式
                logger.debug("Closing MongoDB connection...")

                # 不直接调用close()，而是让连接自然超时
                # 将客户端引用设为None，让垃圾回收器处理
                old_client = self.client
                self.client = None
                self.db = None

                # 在后台线程中关闭连接，避免阻塞
                def close_in_background():
                    try:
                        old_client.close()
                    except Exception as e:

                        logger.error(f"操作失败: {type(e).__name__}: {str(e)}")

                        logger.error(f"异常堆栈跟踪: {traceback.format_exc()}")
                await asyncio.get_event_loop().run_in_executor(None, close_in_background)

                # 最后等待确保关闭完成
                await asyncio.sleep(0.5)

                logger.info("MongoDB connection closed with graceful strategy")
        except Exception as e:
            logger.error(f"Error closing MongoDB connection: {str(e)}")

    # 持久连接的执行方法
    async def _execute_db_operation_persistent(self, query: str) -> Dict[str, Any]:
        """使用持久连接执行db操作"""
        try:
            # 处理链式调用，如 db.test.find().limit(5) / .explain() / .count()
            # 但排除直接的count操作，如 db.test.count()
            if (('.limit(' in query or '.skip(' in query or '.sort(' in query or
                '.explain(' in query) or 
                ('.count(' in query and ').count(' in query)):  # 只有链式的count才处理，如 find().count()
                return await self._execute_chained_operation_persistent(query)

            # 首先检查是否是数据库级别的操作（如 db.createCollection）
            db_level_pattern = r'db\.(\w+)\((.*)\)'
            db_match = re.match(db_level_pattern, query)

            if db_match:
                operation = db_match.group(1)
                params = db_match.group(2)

                # 处理数据库级别的操作
                if operation == 'createCollection':
                    return await self._execute_create_collection_persistent(params)
                elif operation == 'createView':
                    return await self._execute_create_view_persistent(params)
                elif operation == 'dropDatabase':
                    return await self._execute_drop_database_persistent(params)
                elif operation == 'runCommand':
                    return await self._execute_run_command_persistent(params)
                else:
                    # 如果不是已知的数据库级别操作，继续尝试集合级别操作
                    pass

            # 提取集合名和操作
            pattern = r'db\.(\w+)\.(\w+)\((.*)\)'
            match = re.match(pattern, query)

            if not match:
                raise Exception(f"Invalid MongoDB query format: {query}")

            collection_name = match.group(1)
            operation = match.group(2)
            params = match.group(3)

            # 使用持久连接的数据库实例
            collection = self._persistent_db[collection_name]

            # 根据操作类型执行相应的方法
            if operation == 'find':
                return await self._execute_find(collection, params)
            elif operation == 'findOne':
                return await self._execute_find_one(collection, params)
            elif operation == 'insert':
                return await self._execute_insert_one(collection, params)
            elif operation == 'insertOne':
                return await self._execute_insert_one(collection, params)
            elif operation == 'insertMany':
                return await self._execute_insert_many(collection, params)
            elif operation == 'updateOne':
                return await self._execute_update_one(collection, params)
            elif operation == 'updateMany':
                return await self._execute_update_many(collection, params)
            elif operation == 'deleteOne':
                return await self._execute_delete_one(collection, params)
            elif operation == 'deleteMany':
                return await self._execute_delete_many(collection, params)
            elif operation == 'countDocuments':
                return await self._execute_count_documents(collection, params)
            elif operation == 'count':
                return await self._execute_count_documents(collection, params)
            elif operation == 'distinct':
                return await self._execute_distinct(collection, params)
            elif operation == 'createIndex':
                return await self._execute_create_index(collection, params)
            elif operation == 'drop':
                return await self._execute_drop(collection, params)
            elif operation == 'getShardDistribution':
                return await self._execute_shard_distribution(collection, params)
            elif operation == 'stats':
                return await self._execute_collection_stats(collection, params)
            elif operation == 'explain':
                return await self._execute_explain(collection, params)
            else:
                raise Exception(f"Unsupported MongoDB operation: {operation}")

        except Exception as e:
            logger.error(f"Persistent db operation failed: {str(e)}")
            raise

    async def _execute_multiple_commands_persistent(self, query: str) -> Dict[str, Any]:
        """在持久连接下执行多个MongoDB命令（用分号分隔）"""
        try:
            commands = [cmd.strip() for cmd in query.split(';') if cmd.strip()]
            results = []

            for cmd in commands:
                if cmd.startswith('db.'):
                    # 忽略 changeStream.close() 这类关闭调用
                    if re.search(r"\.close\(\)\s*$", cmd):
                        results.append({'type': 'noop', 'operation': 'close', 'acknowledged': True})
                        continue
                    # 简单模拟 watch() 创建，不真正保持流
                    if re.search(r"\.watch\(.*\)$", cmd):
                        results.append({'type': 'command', 'operation': 'watch', 'acknowledged': True, 'simulated': True})
                        continue
                    result = await self._execute_db_operation_persistent(cmd)
                    results.append(result)
                elif cmd.startswith('use '):
                    result = await self._execute_use_command_persistent(cmd)
                    results.append(result)
                elif cmd.startswith('show '):
                    result = await self._execute_show_command_persistent(cmd)
                    results.append(result)
                else:
                    if re.search(r"\.close\(\)\s*$", cmd):
                        results.append({'type': 'noop', 'operation': 'close', 'acknowledged': True})
                        continue
                    if re.search(r"watch\(.*\)$", cmd):
                        results.append({'type': 'command', 'operation': 'watch', 'acknowledged': True, 'simulated': True})
                        continue
                    logger.warning(f"Skipping unsupported command in persistent mode: {cmd}")

            return {
                'type': 'batch',
                'operation': 'multiple_commands',
                'results': results,
                'count': len(results)
            }

        except Exception as e:
            logger.error(f"Error executing multiple commands (persistent): {str(e)}")
            raise

    async def _execute_chained_operation_persistent(self, query: str) -> Dict[str, Any]:
        """使用持久连接执行链式操作"""
        try:
            # 解析基础查询和链式方法
            base_pattern = r'db\.(\w+)\.find\((.*?)\)'
            base_match = re.search(base_pattern, query)

            if not base_match:
                raise Exception(f"Invalid chained query format: {query}")

            collection_name = base_match.group(1)
            find_params = base_match.group(2)

            # 使用持久连接的数据库实例
            collection = self._persistent_db[collection_name]

            # 解析find参数
            if find_params.strip():
                filter_doc = self._parse_json_params(find_params)
            else:
                filter_doc = {}

            # 执行基础查询
            cursor = await asyncio.get_event_loop().run_in_executor(
                None, collection.find, filter_doc
            )

            # 解析链式方法
            chain_methods = []

            # 查找所有链式方法
            chain_pattern = r'\.(\w+)\((.*?)\)'
            chain_matches = re.findall(chain_pattern, query[base_match.end():])

            sort_spec = None
            skip_value = None
            limit_value = None

            for method_name, method_params in chain_matches:
                chain_methods.append((method_name, method_params))

                if method_name == 'sort':
                    if method_params.strip():
                        sort_spec = self._parse_json_params(method_params)
                elif method_name == 'skip':
                    skip_value = int(method_params) if method_params.strip() else 0
                elif method_name == 'limit':
                    limit_value = int(method_params) if method_params.strip() else 0

            # 应用链式操作到游标
            if sort_spec:
                cursor = cursor.sort(list(sort_spec.items()))
            if skip_value:
                cursor = cursor.skip(skip_value)
            if limit_value:
                cursor = cursor.limit(limit_value)

            # 转换结果
            documents = []
            for doc in cursor:
                # 转换ObjectId为字符串
                doc = self._convert_objectid_to_str(doc)
                documents.append(doc)

            return {
                'type': 'query',
                'operation': 'find',
                'collection': collection.name,
                'data': documents,
                'count': len(documents),
                'applied_chains': [f"{name}({params})" for name, params in chain_methods]
            }

        except Exception as e:
            logger.error(f"Persistent chained operation failed: {str(e)}")
            raise

    async def _execute_use_command_persistent(self, query: str) -> Dict[str, Any]:
        """使用持久连接执行use命令"""
        try:
            # 提取数据库名
            db_name = query.replace('use ', '').strip()
            self._persistent_db = self._persistent_client[db_name]

            return {
                'type': 'command',
                'operation': 'use',
                'message': f"Switched to database: {db_name} (persistent connection)"
            }

        except Exception as e:
            logger.error(f"Persistent use command failed: {str(e)}")
            raise

    async def _execute_show_command_persistent(self, query: str) -> Dict[str, Any]:
        """使用持久连接执行show命令"""
        try:
            if 'databases' in query or 'dbs' in query:
                databases = await self._get_databases_persistent()
                return {
                    'type': 'query',
                    'operation': 'show databases',
                    'data': [{'name': db} for db in databases],
                    'count': len(databases)
                }
            elif 'collections' in query:
                collections = await self._get_collections_persistent()
                return {
                    'type': 'query',
                    'operation': 'show collections',
                    'data': [{'name': coll} for coll in collections],
                    'count': len(collections)
                }
            else:
                raise Exception(f"Unsupported show command: {query}")

        except Exception as e:
            logger.error(f"Persistent show command failed: {str(e)}")
            raise

    async def _get_databases_persistent(self) -> List[str]:
        """使用持久连接获取数据库列表"""
        try:
            databases = await asyncio.get_event_loop().run_in_executor(
                None, self._persistent_client.list_database_names
            )
            return databases
        except Exception as e:
            logger.error(f"Failed to get databases with persistent connection: {str(e)}")
            raise

    async def _get_collections_persistent(self) -> List[str]:
        """使用持久连接获取集合列表"""
        try:
            collections = await asyncio.get_event_loop().run_in_executor(
                None, self._persistent_db.list_collection_names
            )
            return collections
        except Exception as e:
            logger.error(f"Failed to get collections with persistent connection: {str(e)}")
            raise

    async def _execute_create_index(self, collection, params: str) -> Dict[str, Any]:
        """执行createIndex操作"""
        try:
            # MongoDB createIndex语法: db.collection.createIndex({key: 1}, {unique: true})
            # 需要解析两个参数：索引键定义和索引选项
            
            # 移除外层括号
            params = params.strip()
            if params.startswith('(') and params.endswith(')'):
                params = params[1:-1]
            
            # 分割两个参数
            if ',' in params:
                # 找到第一个参数的结束位置（第一个完整的JSON对象）
                brace_count = 0
                first_param_end = -1
                in_string = False
                escape_next = False
                
                for i, char in enumerate(params):
                    if escape_next:
                        escape_next = False
                        continue
                    
                    if char == '\\':
                        escape_next = True
                        continue
                    
                    if char == '"' and not escape_next:
                        in_string = not in_string
                        continue
                    
                    if not in_string:
                        if char == '{':
                            brace_count += 1
                        elif char == '}':
                            brace_count -= 1
                            if brace_count == 0:
                                first_param_end = i + 1
                                break
                
                if first_param_end > 0:
                    keys_param = params[:first_param_end].strip()
                    options_param = params[first_param_end:].strip()
                    
                    # 移除options_param开头的逗号
                    if options_param.startswith(','):
                        options_param = options_param[1:].strip()
                    
                    # 解析索引键定义
                    keys = self._parse_json_params(keys_param)
                    
                    # 解析索引选项（如果有的话）
                    options = {}
                    if options_param and options_param.strip():
                        try:
                            options = self._parse_json_params(options_param)
                        except Exception as e:
                            logger.warning(f"Failed to parse index options '{options_param}': {e}")
                            # 如果选项解析失败，使用默认选项
                            options = {}
                else:
                    # 如果无法分割，尝试将整个参数作为索引键定义
                    keys = self._parse_json_params(params)
                    options = {}
            else:
                # 只有一个参数，作为索引键定义
                keys = self._parse_json_params(params)
                options = {}
            
            # 执行创建索引操作
            if options:
                # 如果有选项，将选项作为关键字参数传递
                # MongoDB Python驱动的create_index方法支持关键字参数
                result = await asyncio.get_event_loop().run_in_executor(
                    None, lambda: collection.create_index(keys, **options)
                )
            else:
                # 如果没有选项，只传递索引键定义
                result = await asyncio.get_event_loop().run_in_executor(
                    None, collection.create_index, keys
                )

            return {
                'type': 'modification',
                'operation': 'createIndex',
                'collection': collection.name,
                'index_name': result,
                'keys': keys,
                'options': options,
                'message': f"Successfully created index: {result}"
            }

        except Exception as e:
            logger.error(f"Create index operation failed: {str(e)}")
            raise

    async def _execute_create_collection(self, params: str) -> Dict[str, Any]:
        """执行createCollection操作（支持可选参数，如 timeseries 等）"""
        try:
            # 解析集合名称与可选参数
            raw = params.strip()
            if raw.startswith('(') and raw.endswith(')'):
                raw = raw[1:-1].strip()

            # 提取第一个参数（集合名，字符串）与剩余部分
            name: Optional[str] = None
            rest = ''
            if raw.startswith("'") or raw.startswith('"'):
                quote = raw[0]
                # 寻找匹配的结束引号（忽略转义）
                i = 1
                while i < len(raw):
                    if raw[i] == '\\' and i + 1 < len(raw):
                        i += 2
                        continue
                    if raw[i] == quote:
                        break
                    i += 1
                end = i if i < len(raw) else len(raw)
                name = raw[1:end]
                rest = raw[end + 1:].lstrip()
            else:
                # 容错：按逗号切分
                parts = [p.strip() for p in raw.split(',', 1)]
                name = parts[0].strip("'\"") if parts else ''
                rest = parts[1] if len(parts) > 1 else ''

            options: Dict[str, Any] = {}
            if rest.startswith(','):
                rest = rest[1:].lstrip()
            if rest:
                try:
                    options = self._parse_json_params(rest)
                except Exception as e:
                    logger.warning(f"Failed to parse createCollection options '{rest}': {e}")
                    options = {}

            # 如果没有数据库连接，返回模拟结果
            if self.db is None:
                return {
                    'type': 'command',
                    'operation': 'createCollection',
                    'collection_name': name,
                    'options': options,
                    'acknowledged': True,
                    'simulated': True
                }

            # 检查集合是否已存在
            existing_collections = await asyncio.get_event_loop().run_in_executor(
                None, self.db.list_collection_names
            )

            if name in existing_collections:
                return {
                    'type': 'modification',
                    'operation': 'createCollection',
                    'collection': name,
                    'message': f"Collection {name} already exists",
                    'acknowledged': True,
                    'already_exists': True
                }

            # 创建集合（使用lambda以支持kwargs）
            def _create():
                if options:
                    return self.db.create_collection(name, **options)
                return self.db.create_collection(name)

            await asyncio.get_event_loop().run_in_executor(None, _create)

            return {
                'type': 'modification',
                'operation': 'createCollection',
                'collection': name,
                'options': options,
                'message': f"Successfully created collection: {name}",
                'acknowledged': True,
                'already_exists': False
            }

        except Exception as e:
            logger.error(f"Create collection operation failed: {str(e)}")
            raise

    async def _execute_drop_database(self, params: str) -> Dict[str, Any]:
        """执行dropDatabase操作"""
        try:
            # dropDatabase通常不需要参数
            database_name = self.db.name

            # 避免对 admin 数据库执行 drop（集群/权限通常不允许）
            if database_name == 'admin':
                logger.info("Skipping dropDatabase on 'admin' database")
                return {
                    'type': 'modification',
                    'operation': 'dropDatabase',
                    'database': database_name,
                    'message': "Skipping drop of 'admin' database",
                    'acknowledged': True,
                    'skipped': True
                }

            # 删除数据库
            await asyncio.get_event_loop().run_in_executor(
                None, self.client.drop_database, database_name
            )

            return {
                'type': 'modification',
                'operation': 'dropDatabase',
                'database': database_name,
                'message': f"Successfully dropped database: {database_name}",
                'acknowledged': True
            }

        except Exception as e:
            logger.error(f"Drop database operation failed: {str(e)}")
            raise

    async def _execute_run_command(self, params: str) -> Dict[str, Any]:
        """执行runCommand操作"""
        try:
            # 解析命令参数
            command = self._parse_json_params(params)

            # 执行命令
            result = await asyncio.get_event_loop().run_in_executor(
                None, self.db.command, command
            )

            return {
                'type': 'command',
                'operation': 'runCommand',
                'command': command,
                'result': result,
                'acknowledged': True
            }

        except Exception as e:
            logger.error(f"Run command operation failed: {str(e)}")
            raise

    def _parse_base_operation_with_chains(self, query: str) -> tuple:
        """解析基础操作和剩余的链式调用部分"""
        # 查找db.collection.method的起始位置
        pattern = r'db\.\w+\.\w+\('
        match = re.search(pattern, query)

        if not match:
            raise Exception(f"Cannot find base operation in: {query}")

        start = match.start()
        paren_start = match.end() - 1  # 指向开括号
        paren_count = 1
        i = paren_start + 1

        # 找到匹配的闭括号
        while i < len(query) and paren_count > 0:
            if query[i] == '(':
                paren_count += 1
            elif query[i] == ')':
                paren_count -= 1
            i += 1

        if paren_count != 0:
            raise Exception(f"Unmatched parentheses in: {query}")

        base_operation = query[start:i]
        remaining_query = query[i:]

        return base_operation, remaining_query

    def _parse_chain_methods(self, remaining_query: str) -> list:
        """解析链式方法调用"""
        chain_methods = []

        # 查找所有的.method()调用
        pattern = r'\.(\w+)\(([^)]*)\)'
        matches = re.findall(pattern, remaining_query)

        for method_name, method_params in matches:
            chain_methods.append({
                'method': method_name,
                'params': method_params.strip()
            })

        return chain_methods

    async def _execute_create_collection_persistent(self, params: str) -> Dict[str, Any]:
        """使用持久连接执行createCollection操作（支持可选参数）"""
        try:
            # 解析集合名称与可选参数
            raw = params.strip()
            if raw.startswith('(') and raw.endswith(')'):
                raw = raw[1:-1].strip()

            name: Optional[str] = None
            rest = ''
            if raw.startswith("'") or raw.startswith('"'):
                quote = raw[0]
                i = 1
                while i < len(raw):
                    if raw[i] == '\\' and i + 1 < len(raw):
                        i += 2
                        continue
                    if raw[i] == quote:
                        break
                    i += 1
                end = i if i < len(raw) else len(raw)
                name = raw[1:end]
                rest = raw[end + 1:].lstrip()
            else:
                parts = [p.strip() for p in raw.split(',', 1)]
                name = parts[0].strip("'\"") if parts else ''
                rest = parts[1] if len(parts) > 1 else ''

            options: Dict[str, Any] = {}
            if rest.startswith(','):
                rest = rest[1:].lstrip()
            if rest:
                try:
                    options = self._parse_json_params(rest)
                except Exception as e:
                    logger.warning(f"Failed to parse createCollection options (persistent) '{rest}': {e}")
                    options = {}

            # 检查集合是否已存在
            existing_collections = await asyncio.get_event_loop().run_in_executor(
                None, self._persistent_db.list_collection_names
            )

            if name in existing_collections:
                return {
                    'type': 'modification',
                    'operation': 'createCollection',
                    'collection': name,
                    'message': f"Collection {name} already exists",
                    'acknowledged': True,
                    'already_exists': True
                }

            # 创建集合（lambda 以支持 kwargs，如 timeseries）
            def _create():
                if options:
                    return self._persistent_db.create_collection(name, **options)
                return self._persistent_db.create_collection(name)

            await asyncio.get_event_loop().run_in_executor(None, _create)

            return {
                'type': 'modification',
                'operation': 'createCollection',
                'collection': name,
                'options': options,
                'message': f"Successfully created collection: {name}",
                'acknowledged': True,
                'already_exists': False
            }

        except Exception as e:
            logger.error(f"Create collection operation failed (persistent): {str(e)}")
            raise

    async def _execute_drop_database_persistent(self, params: str) -> Dict[str, Any]:
        """使用持久连接执行dropDatabase操作"""
        try:
            # dropDatabase通常不需要参数
            database_name = self._persistent_db.name

            # 避免对 admin 数据库执行 drop（集群/权限通常不允许）
            if database_name == 'admin':
                logger.info("Skipping dropDatabase on 'admin' database (persistent)")
                return {
                    'type': 'modification',
                    'operation': 'dropDatabase',
                    'database': database_name,
                    'message': "Skipping drop of 'admin' database",
                    'acknowledged': True,
                    'skipped': True
                }

            # 删除数据库
            await asyncio.get_event_loop().run_in_executor(
                None, self._persistent_client.drop_database, database_name
            )

            return {
                'type': 'modification',
                'operation': 'dropDatabase',
                'database': database_name,
                'message': f"Successfully dropped database: {database_name}",
                'acknowledged': True
            }

        except Exception as e:
            logger.error(f"Drop database operation failed: {str(e)}")
            raise

    async def _execute_run_command_persistent(self, params: str) -> Dict[str, Any]:
        """使用持久连接执行runCommand操作"""
        try:
            # 解析命令参数
            command = self._parse_json_params(params)

            # 执行命令
            result = await asyncio.get_event_loop().run_in_executor(
                None, self._persistent_db.command, command
            )

            return {
                'type': 'command',
                'operation': 'runCommand',
                'command': command,
                'result': result,
                'acknowledged': True
            }

        except Exception as e:
            logger.error(f"Run command operation failed: {str(e)}")
            raise

    async def _execute_create_view_persistent(self, params: str) -> Dict[str, Any]:
        """使用持久连接执行创建视图操作"""
        try:
            import re
            params_clean = params.strip()
            pattern = r"'([^']+)',\s*'([^']+)',\s*(\[.*\])"
            match = re.match(pattern, params_clean)
            if not match:
                raise Exception("Invalid createView parameters format")

            view_name = match.group(1)
            source_collection = match.group(2)
            pipeline_str = match.group(3)

            # 如果没有持久数据库连接，返回模拟结果
            if self._persistent_db is None:
                return {
                    'type': 'command',
                    'operation': 'createView',
                    'view_name': view_name,
                    'source': source_collection,
                    'pipeline': pipeline_str,
                    'acknowledged': True,
                    'simulated': True
                }

            pipeline = self._parse_json_params(pipeline_str)

            # 由于run_in_executor不直接支持kwargs，改用lambda封装
            def _create_view():
                return self._persistent_db.create_collection(view_name, viewOn=source_collection, pipeline=pipeline)

            await asyncio.get_event_loop().run_in_executor(None, _create_view)

            return {
                'type': 'command',
                'operation': 'createView',
                'view_name': view_name,
                'source': source_collection,
                'pipeline': pipeline,
                'acknowledged': True
            }

        except Exception as e:
            logger.error(f"Create view operation failed (persistent): {str(e)}")
            raise

    async def _execute_create_view(self, params: str) -> Dict[str, Any]:
        """执行创建视图操作"""
        try:
            # 解析参数：viewName, source, pipeline
            # 格式：'viewName', 'source', [pipeline]
            import re

            # 简单的参数解析
            params_clean = params.strip()

            # 使用正则表达式解析参数
            # 匹配格式：'viewName', 'source', [pipeline]
            pattern = r"'([^']+)',\s*'([^']+)',\s*(\[.*\])"
            match = re.match(pattern, params_clean)

            if not match:
                raise Exception("Invalid createView parameters format")

            view_name = match.group(1)
            source_collection = match.group(2)
            pipeline_str = match.group(3)

            # 如果没有数据库连接，返回模拟结果
            if self.db is None:
                return {
                    'type': 'command',
                    'operation': 'createView',
                    'view_name': view_name,
                    'source': source_collection,
                    'pipeline': pipeline_str,
                    'acknowledged': True,
                    'simulated': True
                }

            # 解析管道
            pipeline = self._parse_json_params(pipeline_str)

            # 创建视图（通过lambda传递关键字参数）
            def _create_view():
                return self.db.create_collection(view_name, viewOn=source_collection, pipeline=pipeline)

            await asyncio.get_event_loop().run_in_executor(None, _create_view)

            return {
                'type': 'command',
                'operation': 'createView',
                'view_name': view_name,
                'source': source_collection,
                'pipeline': pipeline,
                'acknowledged': True
            }

        except Exception as e:
            logger.error(f"Create view operation failed: {str(e)}")
            raise

    async def _execute_shard_operation(self, query: str) -> Dict[str, Any]:
        """执行分片操作"""
        try:
            # 解析分片操作
            if query.startswith('sh.enableSharding('):
                # 提取数据库名称
                pattern = r"sh\.enableSharding\('([^']+)'\)"
                match = re.match(pattern, query)
                if not match:
                    raise Exception("Invalid enableSharding format")

                db_name = match.group(1)

                # 如果没有客户端连接，返回模拟结果
                if not self.client:
                    return {
                        'type': 'command',
                        'operation': 'enableSharding',
                        'database': db_name,
                        'acknowledged': True,
                        'simulated': True
                    }

                # 执行enableSharding命令
                admin_db = self.client.admin
                result = await asyncio.get_event_loop().run_in_executor(
                    None, admin_db.command, {"enableSharding": db_name}
                )

                return {
                    'type': 'command',
                    'operation': 'enableSharding',
                    'database': db_name,
                    'result': result,
                    'acknowledged': True
                }

            elif query.startswith('sh.shardCollection('):
                # 提取集合名称和分片键
                pattern = r"sh\.shardCollection\('([^']+)',\s*(\{.*\})\)"
                match = re.match(pattern, query)
                if not match:
                    raise Exception("Invalid shardCollection format")

                collection_name = match.group(1)
                shard_key_str = match.group(2)

                # 如果没有客户端连接，返回模拟结果
                if not self.client:
                    return {
                        'type': 'command',
                        'operation': 'shardCollection',
                        'collection': collection_name,
                        'shard_key': shard_key_str,
                        'acknowledged': True,
                        'simulated': True
                    }

                shard_key = self._parse_json_params(shard_key_str)

                # 执行shardCollection命令
                admin_db = self.client.admin
                result = await asyncio.get_event_loop().run_in_executor(
                    None, admin_db.command, {
                        "shardCollection": collection_name,
                        "key": shard_key
                    }
                )

                return {
                    'type': 'command',
                    'operation': 'shardCollection',
                    'collection': collection_name,
                    'shard_key': shard_key,
                    'result': result,
                    'acknowledged': True
                }
            else:
                raise Exception(f"Unsupported shard operation: {query}")

        except Exception as e:
            logger.error(f"Shard operation failed: {str(e)}")
            raise

    # ======================= JavaScript解析器 =======================
    
    # ======================= 会话和事务管理 =======================
    
    async def _handle_session_operation(self, query: str) -> Dict[str, Any]:
        """处理会话操作"""
        try:
            # 预处理复杂查询
            query = self._preprocess_complex_query(query)
            
            if not self.client:
                await self.connect()
            
            # 如果查询包含多个语句（用分号分隔），处理为复杂事务
            if ';' in query and any(op in query for op in ['startSession', 'startTransaction', 'updateOne', 'commitTransaction', 'abortTransaction']):
                return await self._handle_complex_transaction_query(query)
            
            # 支持多种会话启动方式
            if (query.startswith('var session = db.getMongo().startSession()') or 
                query.startswith('db.getMongo().startSession()')):
                # 创建新会话
                session = await asyncio.get_event_loop().run_in_executor(
                    None, self.client.start_session
                )
                session_id = f"session_{len(self._sessions)}"
                self._sessions[session_id] = session
                self._current_session = session
                
                return {
                    'type': 'session',
                    'operation': 'startSession',
                    'session_id': session_id,
                    'acknowledged': True
                }
            
            elif query.startswith('session.startTransaction()'):
                # 开始事务
                if not self._current_session:
                    raise Exception("No active session found")
                
                # 为分片集群设置适当的事务选项
                from pymongo import WriteConcern
                from pymongo.read_concern import ReadConcern
                
                await asyncio.get_event_loop().run_in_executor(
                    None, 
                    lambda: self._current_session.start_transaction(
                        read_concern=ReadConcern("local"),
                        write_concern=WriteConcern(w=1)
                    )
                )
                self._transaction_stack.append(self._current_session)
                
                return {
                    'type': 'transaction',
                    'operation': 'startTransaction',
                    'session_id': self._get_session_id(self._current_session),
                    'acknowledged': True
                }
            
            elif query.startswith('session.commitTransaction()'):
                # 提交事务
                if not self._transaction_stack:
                    raise Exception("No active transaction found")
                
                session = self._transaction_stack.pop()
                await asyncio.get_event_loop().run_in_executor(
                    None, session.commit_transaction
                )
                
                return {
                    'type': 'transaction',
                    'operation': 'commitTransaction',
                    'session_id': self._get_session_id(session),
                    'acknowledged': True
                }
            
            elif query.startswith('session.abortTransaction()'):
                # 回滚事务
                if not self._transaction_stack:
                    raise Exception("No active transaction found")
                
                session = self._transaction_stack.pop()
                await asyncio.get_event_loop().run_in_executor(
                    None, session.abort_transaction
                )
                
                return {
                    'type': 'transaction',
                    'operation': 'abortTransaction',
                    'session_id': self._get_session_id(session),
                    'acknowledged': True
                }
            
            elif 'session.getDatabase(' in query:
                # 获取会话数据库
                pattern = r'var\s+(\w+)\s*=\s*session\.getDatabase\("([^"]+)"\)\.(\w+)'
                match = re.match(pattern, query)
                if match:
                    var_name = match.group(1)
                    db_name = match.group(2)
                    collection_name = match.group(3)
                    
                    # 存储会话数据库引用
                    self._js_context[var_name] = {
                        'type': 'collection',
                        'database': db_name,
                        'collection': collection_name,
                        'session': self._current_session
                    }
                    
                    return {
                        'type': 'session',
                        'operation': 'getDatabase',
                        'variable': var_name,
                        'database': db_name,
                        'collection': collection_name,
                        'acknowledged': True
                    }
            
            else:
                # 检查是否是会话变量操作 (如 sessionDB.insertOne(...))
                for var_name, var_info in self._js_context.items():
                    if query.startswith(var_name + '.') and var_info.get('type') == 'collection':
                        # 提取操作部分
                        operation_part = query[len(var_name) + 1:]  # 去掉变量名和点
                        
                        # 构造实际的MongoDB查询
                        db_name = var_info['database']
                        collection_name = var_info['collection']
                        session = var_info['session']
                        
                        # 创建会话数据库查询
                        actual_query = f"db.{collection_name}.{operation_part}"
                        
                        # 使用会话执行操作
                        if not self.client:
                            await self.connect()
                        
                        # 在事务中，使用默认的acknowledged write concern
                        # 不能在事务中使用unacknowledged write concern
                        session_db = self.client[db_name]
                        collection = session_db[collection_name]
                        
                        # 执行操作
                        result = await self._execute_collection_operation_with_session(
                            collection, operation_part, session
                        )
                        
                        return {
                            'type': 'session_operation',
                            'operation': operation_part,
                            'variable': var_name,
                            'database': db_name,
                            'collection': collection_name,
                            'result': result,
                            'acknowledged': True
                        }
                
                # 检查是否是结束会话操作
                if query.startswith('session.endSession()'):
                    if not self._current_session:
                        raise Exception("No active session found")
                    
                    # 结束会话
                    await asyncio.get_event_loop().run_in_executor(
                        None, self._current_session.end_session
                    )
                    
                    # 清理会话引用
                    session_id = self._get_session_id(self._current_session)
                    if session_id in self._sessions:
                        del self._sessions[session_id]
                    self._current_session = None
                    
                    return {
                        'type': 'session',
                        'operation': 'endSession',
                        'session_id': session_id,
                        'acknowledged': True
                    }
                
                raise Exception(f"Unsupported session operation: {query}")
                
        except Exception as e:
            logger.error(f"Session operation failed: {str(e)}")
            raise

    def _get_session_id(self, session) -> str:
        """获取会话ID"""
        for session_id, stored_session in self._sessions.items():
            if stored_session == session:
                return session_id
        return "unknown"

    # ======================= 批量操作支持 =======================
    
    async def _handle_bulk_operation(self, query: str) -> Dict[str, Any]:
        """处理批量操作"""
        try:
            if query.startswith('var bulk = '):
                # 初始化批量操作
                pattern = r'var\s+(\w+)\s*=\s*db\.(\w+)\.initialize(\w+)BulkOp\(\)'
                match = re.match(pattern, query)
                if not match:
                    raise Exception("Invalid bulk operation initialization")
                
                var_name = match.group(1)
                collection_name = match.group(2)
                bulk_type = match.group(3)  # Ordered 或 Unordered
                
                if not self.client:
                    await self.connect()
                
                collection = self.db[collection_name]
                
                # 创建批量操作对象
                if bulk_type.lower() == 'ordered':
                    bulk_op = collection.initialize_ordered_bulk_op()
                else:
                    bulk_op = collection.initialize_unordered_bulk_op()
                
                self._bulk_operations[var_name] = {
                    'bulk_op': bulk_op,
                    'collection': collection_name,
                    'type': bulk_type.lower(),
                    'operations': []
                }
                
                # 在分片环境中添加延迟
                if 'sh.' in query or 'shard' in query.lower():
                    await asyncio.sleep(0.5)
                
                return {
                    'type': 'bulk',
                    'operation': 'initialize',
                    'variable': var_name,
                    'collection': collection_name,
                    'bulk_type': bulk_type.lower(),
                    'acknowledged': True
                }
            
            elif '.insert(' in query or '.insertOne(' in query:
                # 批量插入操作
                pattern = r'(\w+)\.(insert|insertOne)\((\{.*\})\)'
                match = re.search(pattern, query)
                if not match:
                    raise Exception("Invalid bulk insert operation")
                
                var_name = match.group(1)
                operation_type = match.group(2)  # insert 或 insertOne
                doc_str = match.group(3)
                
                if var_name not in self._bulk_operations:
                    raise Exception(f"Bulk operation variable '{var_name}' not found")
                
                # 解析文档
                doc_str = self._parse_javascript_params(doc_str)
                document = self._parse_json_params(doc_str)
                
                # 添加到批量操作
                bulk_info = self._bulk_operations[var_name]
                bulk_info['bulk_op'].insert(document)
                bulk_info['operations'].append({
                    'type': 'insert',
                    'document': document
                })
                
                return {
                    'type': 'bulk',
                    'operation': 'insert',
                    'variable': var_name,
                    'document': document,
                    'acknowledged': True
                }
            
            elif '.execute()' in query:
                # 执行批量操作
                pattern = r'(\w+)\.execute\(\)'
                match = re.search(pattern, query)
                if not match:
                    raise Exception("Invalid bulk execute operation")
                
                var_name = match.group(1)
                
                if var_name not in self._bulk_operations:
                    raise Exception(f"Bulk operation variable '{var_name}' not found")
                
                bulk_info = self._bulk_operations[var_name]
                
                # 在分片环境中添加延迟
                if 'sh.' in query or 'shard' in query.lower():
                    await asyncio.sleep(0.5)
                
                # 执行批量操作，添加重试机制
                max_retries = 3
                retry_count = 0
                result = None
                last_error = None
                
                while retry_count < max_retries:
                    try:
                        result = await asyncio.get_event_loop().run_in_executor(
                            None, bulk_info['bulk_op'].execute
                        )
                        last_error = None
                        break
                    except Exception as e:
                        last_error = e
                        retry_count += 1
                        if retry_count < max_retries:
                            logger.warning(f"Bulk operation failed, retrying ({retry_count}/{max_retries}): {str(e)}")
                            await asyncio.sleep(0.5 * retry_count)  # 指数退避
                
                # 如果所有重试都失败了，抛出异常
                if last_error:
                    raise last_error
                
                # 清理批量操作
                del self._bulk_operations[var_name]
                
                return {
                    'type': 'bulk',
                    'operation': 'execute',
                    'variable': var_name,
                    'result': {
                        'writeErrors': result.get('writeErrors', []),
                        'writeConcernErrors': result.get('writeConcernErrors', []),
                        'nInserted': result.get('nInserted', 0),
                        'nUpserted': result.get('nUpserted', 0),
                        'nMatched': result.get('nMatched', 0),
                        'nModified': result.get('nModified', 0),
                        'nRemoved': result.get('nRemoved', 0),
                        'upserted': result.get('upserted', [])
                    },
                    'operations_count': len(bulk_info['operations']),
                    'acknowledged': True
                }
            
            else:
                raise Exception(f"Unsupported bulk operation: {query}")
                
        except Exception as e:
            logger.error(f"Bulk operation failed: {str(e)}")
            # 返回错误信息但不抛出异常，允许继续执行
            return {
                'type': 'bulk',
                'operation': 'error',
                'error': str(e),
                'acknowledged': False
            }

    # ======================= 增强的分片功能 =======================
    
    async def _handle_enhanced_shard_operation(self, query: str) -> Dict[str, Any]:
        """处理增强的分片操作"""
        try:
            if not self.client:
                await self.connect()
            
            if query.startswith('sh.status()'):
                # 获取分片状态
                admin_db = self.client.admin
                
                try:
                    # 获取分片信息
                    shards_result = await asyncio.get_event_loop().run_in_executor(
                        None, admin_db.command, 'listShards'
                    )
                    
                    # 获取分片数据库信息
                    databases_result = await asyncio.get_event_loop().run_in_executor(
                        None, admin_db.command, 'listDatabases'
                    )
                    
                    return {
                        'type': 'command',
                        'operation': 'shardStatus',
                        'shards': shards_result.get('shards', []),
                        'databases': databases_result.get('databases', []),
                        'acknowledged': True
                    }
                    
                except Exception as e:
                    # 如果不是分片集群，返回模拟状态
                    logger.warning(f"Not a sharded cluster: {str(e)}")
                    return {
                        'type': 'command',
                        'operation': 'shardStatus',
                        'message': 'Not a sharded cluster',
                        'shards': [],
                        'databases': [],
                        'acknowledged': True,
                        'simulated': True
                    }
            
            elif query.startswith('sh.addShard('):
                # 添加分片
                pattern = r"sh\.addShard\('([^']+)'\)"
                match = re.match(pattern, query)
                if not match:
                    raise Exception("Invalid addShard format")
                
                shard_connection = match.group(1)
                
                admin_db = self.client.admin
                result = await asyncio.get_event_loop().run_in_executor(
                    None, admin_db.command, {
                        'addShard': shard_connection
                    }
                )
                
                return {
                    'type': 'command',
                    'operation': 'addShard',
                    'shard_connection': shard_connection,
                    'result': result,
                    'acknowledged': True
                }
            
            elif query.startswith('sh.removeShard('):
                # 移除分片
                pattern = r"sh\.removeShard\('([^']+)'\)"
                match = re.match(pattern, query)
                if not match:
                    raise Exception("Invalid removeShard format")
                
                shard_name = match.group(1)
                
                admin_db = self.client.admin
                result = await asyncio.get_event_loop().run_in_executor(
                    None, admin_db.command, {
                        'removeShard': shard_name
                    }
                )
                
                return {
                    'type': 'command',
                    'operation': 'removeShard',
                    'shard_name': shard_name,
                    'result': result,
                    'acknowledged': True
                }
            
            else:
                # 调用原有的分片操作处理
                return await self._execute_shard_operation(query)
                
        except Exception as e:
            logger.error(f"Enhanced shard operation failed: {str(e)}")
            raise

    async def cleanup_sessions(self):
        """清理会话资源"""
        try:
            for session_id, session in self._sessions.items():
                try:
                    await asyncio.get_event_loop().run_in_executor(
                        None, session.end_session
                    )
                except Exception as e:
                    logger.warning(f"Failed to end session {session_id}: {str(e)}")
            
            self._sessions.clear()
            self._current_session = None
            self._transaction_stack.clear()
            self._bulk_operations.clear()
            
            logger.info("MongoDB sessions and resources cleaned up")
            
        except Exception as e:
            logger.error(f"Failed to cleanup sessions: {str(e)}")

    # ======================= For循环和变量声明支持 =======================
    
    async def _handle_for_loop_operation(self, query: str) -> Dict[str, Any]:
        """处理for循环操作"""
        import re  # 将import语句移到函数开始处，避免变量作用域问题
        
        try:
            logger.info(f"Starting to handle for loop operation: {query}")
            
            # 解析for循环模式: for(let i=0; i<100; i++){db.collection.insertOne({index:i, value:Math.random()})}
            # 支持多种格式，包括带分号结尾的语句
            # 统一先移除末尾分号再进行匹配
            cleaned_query = query.rstrip(';')
            
            pattern = r'for\s*\(\s*let\s+(\w+)\s*=\s*(\d+)\s*;\s*\1\s*<\s*(\d+)\s*;\s*\1\+\+\s*\)\s*\{\s*(.+?)\s*\}'
            match = re.search(pattern, cleaned_query)
            
            if not match:
                # 尝试简化模式，支持var声明
                pattern = r'for\s*\(\s*(?:var|let)?\s*(\w+)\s*=\s*(\d+)\s*;\s*\1\s*<\s*(\d+)\s*;\s*\1\+\+\s*\)\s*\{\s*(.+?)\s*\}'
                match = re.search(pattern, cleaned_query)
            
            if not match:
                # 尝试不带声明的模式: for(i=0;i<1000;i++)
                pattern = r'for\s*\(\s*(\w+)\s*=\s*(\d+)\s*;\s*\1\s*<\s*(\d+)\s*;\s*\1\+\+\s*\)\s*\{\s*(.+?)\s*\}'
                match = re.search(pattern, cleaned_query)
            
            if not match:
                raise Exception(f"Cannot parse for loop pattern: {query}")

            var_name = match.group(1)
            start_val = int(match.group(2))
            end_val = int(match.group(3))
            loop_body = match.group(4)
            
            logger.info(f"Parsed for loop - variable: {var_name}, range: {start_val}-{end_val}, body: {loop_body}")
            
            results = []
            executed_count = 0
            # 限制最大迭代次数以避免性能问题，但在分片测试中可以适当增加
            max_iterations = min(end_val - start_val, 500)  # 增加到500以适应分片测试
            
            # 在分片环境中，添加一个更长的延迟以确保分片集合完全初始化
            await asyncio.sleep(1.0)
            
            last_success_index = -1
            consecutive_failures = 0
            max_consecutive_failures = 10  # 增加连续失败阈值
            
            # 处理Math.random()表达式（提前处理，避免在循环中重复处理）
            processed_loop_body = re.sub(r'Math\.random\(\)\s*\*\s*(\d+(?:\.\d+)?)', 
                                       str(round(500)), loop_body)  # 固定为500
            logger.info(f"Processed loop body: {processed_loop_body}")
            
            for i in range(start_val, min(start_val + max_iterations, end_val)):
                try:
                    # 使用正则表达式进行精确替换，避免替换单词的一部分
                    # 使用单词边界确保只替换整个单词
                    iteration_query = re.sub(r'\b' + re.escape(var_name) + r'\b', str(i), processed_loop_body)
                    
                    logger.info(f"Executing iteration {i}: {iteration_query}")
                    
                    # 执行单次迭代
                    result = await self._parse_and_execute_query_persistent(iteration_query)
                    results.append({
                        'iteration': i,
                        'result': result
                    })
                    executed_count += 1
                    last_success_index = i
                    consecutive_failures = 0  # 重置连续失败计数
                    
                    # 在分片环境中，每次插入后添加小延迟以避免并发问题
                    if 'insert' in iteration_query.lower() and ('sh.' in query or 'shard' in query.lower()):
                        await asyncio.sleep(0.2)  # 增加延迟时间
                        
                except Exception as e:
                    logger.warning(f"Error executing iteration {i}: {str(e)}")
                    results.append({
                        'iteration': i,
                        'error': str(e)
                    })
                    consecutive_failures += 1
                    
                    # 如果连续失败次数超过阈值，则停止执行
                    if consecutive_failures >= max_consecutive_failures:
                        logger.warning(f"Too many consecutive failures ({consecutive_failures}), stopping loop execution")
                        break
                    
                    # 在失败时添加更长的延迟
                    await asyncio.sleep(0.5)
            
            # 如果没有成功执行任何迭代，但有错误，则抛出异常
            if executed_count == 0 and results:
                first_error = results[0].get('error', 'Unknown error')
                raise Exception(f"For loop execution failed: {first_error}")
            
            return {
                'type': 'loop',
                'operation': 'for',
                'variable': var_name,
                'range': {'start': start_val, 'end': min(start_val + max_iterations, end_val)},
                'executed_iterations': executed_count,
                'total_planned': end_val - start_val,
                'limited_to': max_iterations,
                'last_success_index': last_success_index,
                'results': results[-5:] if len(results) > 5 else results,  # 只返回最后5个结果
                'acknowledged': executed_count > 0  # 只有至少一次成功执行才算acknowledged
            }
            
        except Exception as e:
            logger.error(f"For loop operation failed: {str(e)}")
            # 即使失败也返回结果，但标记为未确认
            return {
                'type': 'loop',
                'operation': 'for',
                'error': str(e),
                'acknowledged': False
            }

    async def _handle_for_loop_operation(self, query: str) -> Dict[str, Any]:
        """处理for循环操作 - 使用专用执行器"""
        try:
            logger.info(f"Handling for loop operation with dedicated executor: {query}")
            
            # 导入for循环执行器
            from services.mongo_for_loop_executor import MongoForLoopExecutor
            
            # 创建执行器实例
            executor = MongoForLoopExecutor(self)
            
            try:
                # 执行for循环
                result = await executor.execute_for_loop(query)
                return result
            finally:
                # 关闭执行器连接
                await executor.close()
                
        except Exception as e:
            logger.error(f"For loop operation failed: {str(e)}")
            return {
                'type': 'loop',
                'operation': 'for',
                'error': str(e),
                'acknowledged': False
            }

    async def _handle_variable_declaration(self, query: str) -> Dict[str, Any]:
        """处理变量声明和赋值"""
        try:
            # 解析变量声明模式
            patterns = [
                r'(var|let|const)\s+(\w+)\s*=\s*(.+)',
                r'(\w+)\s*=\s*(.+)'  # 简单赋值
            ]
            
            for pattern in patterns:
                match = re.match(pattern, query)
                if match:
                    if len(match.groups()) == 3:
                        var_type = match.group(1)
                        var_name = match.group(2)
                        var_value = match.group(3)
                    else:
                        var_type = 'assignment'
                        var_name = match.group(1)
                        var_value = match.group(2)
                    break
            else:
                raise Exception(f"Cannot parse variable declaration: {query}")
            
            # 如果变量值是函数调用，尝试执行
            if any(op in var_value for op in ['db.', 'getMongo()', 'initializeUnorderedBulkOp', 'initializeOrderedBulkOp']):
                try:
                    result = await self._parse_and_execute_query_persistent(var_value)
                    self._js_context[var_name] = {
                        'type': 'result',
                        'value': result,
                        'declaration': var_type
                    }
                    return {
                        'type': 'variable',
                        'operation': 'declaration',
                        'name': var_name,
                        'declaration_type': var_type,
                        'value_type': 'executed',
                        'result': result,
                        'acknowledged': True
                    }
                except Exception as e:
                    # 如果执行失败，存储为文本值
                    self._js_context[var_name] = {
                        'type': 'expression',
                        'value': var_value,
                        'declaration': var_type
                    }
                    return {
                        'type': 'variable',
                        'operation': 'declaration',
                        'name': var_name,
                        'declaration_type': var_type,
                        'value_type': 'expression',
                        'expression': var_value,
                        'error': str(e),
                        'acknowledged': True
                    }
            else:
                # 存储为简单值
                processed_value = self._evaluate_javascript_expression(var_value)
                self._js_context[var_name] = {
                    'type': 'value',
                    'value': processed_value,
                    'declaration': var_type
                }
                return {
                    'type': 'variable',
                    'operation': 'declaration',
                    'name': var_name,
                    'declaration_type': var_type,
                    'value_type': 'literal',
                    'value': processed_value,
                    'acknowledged': True
                }
            
        except Exception as e:
            logger.error(f"Variable declaration failed: {str(e)}")
            raise

    def get_javascript_context(self) -> Dict[str, Any]:
        """获取JavaScript上下文"""
        return self._js_context.copy()

    def clear_javascript_context(self):
        """清理JavaScript上下文"""
        self._js_context.clear()
        logger.info("JavaScript context cleared")

    def _evaluate_javascript_expression(self, expression: str) -> Any:
        """评估简单的JavaScript表达式"""
        try:
            # 处理简单的数字和字符串
            if expression.isdigit():
                return int(expression)
            
            # 处理简单的字符串（带引号）
            if (expression.startswith('"') and expression.endswith('"')) or \
               (expression.startswith("'") and expression.endswith("'")):
                return expression[1:-1]
            
            # 处理简单的布尔值
            if expression.lower() == 'true':
                return True
            elif expression.lower() == 'false':
                return False
            elif expression.lower() == 'null':
                return None
            
            # 处理简单的数学表达式（如Math.random()）
            if 'Math.random()' in expression:
                import random
                result = expression.replace('Math.random()', str(random.random()))
                try:
                    return eval(result)
                except:
                    return result
            
            # 对于其他情况，直接返回原始表达式
            return expression
            
        except Exception as e:
            logger.warning(f"Failed to evaluate JavaScript expression '{expression}': {e}")
            return expression

    async def _execute_collection_operation_with_session(self, collection, operation: str, session) -> Dict[str, Any]:
        """使用会话执行集合操作"""
        try:
            # 解析操作
            pattern = r'(\w+)\((.*)\)'
            match = re.match(pattern, operation)
            if not match:
                raise Exception(f"Invalid operation format: {operation}")
            
            op_name = match.group(1)
            params = match.group(2).strip()
            
            # 注意：调用方已经确保collection没有write concern
            # 直接使用传入的collection
            
            # 根据操作类型执行
            if op_name == 'insertOne':
                # 解析插入文档
                if params:
                    params = self._parse_javascript_params(params)
                    document = self._parse_json_params(params)
                else:
                    document = {}
                
                # 使用会话执行插入操作
                result = await asyncio.get_event_loop().run_in_executor(
                    None, lambda: collection.insert_one(document, session=session)
                )
                
                return {
                    'operation': 'insertOne',
                    'inserted_id': str(result.inserted_id),
                    'acknowledged': True  # 在事务中总是返回True
                }
            
            elif op_name == 'insertMany':
                # 解析插入文档数组
                if params:
                    params = self._parse_javascript_params(params)
                    documents = self._parse_json_params(params)
                    if not isinstance(documents, list):
                        documents = [documents]
                else:
                    documents = []
                
                # 使用会话执行批量插入
                result = await asyncio.get_event_loop().run_in_executor(
                    None, lambda: collection.insert_many(documents, session=session)
                )
                
                return {
                    'operation': 'insertMany',
                    'inserted_ids': [str(id) for id in result.inserted_ids],
                    'acknowledged': True,
                    'inserted_count': len(result.inserted_ids)
                }
            
            elif op_name == 'updateOne':
                # 解析更新参数
                if params:
                    params = self._parse_javascript_params(params)
                    params_obj = self._parse_json_params(f"[{params}]")
                    filter_doc = params_obj[0] if len(params_obj) > 0 else {}
                    update_doc = params_obj[1] if len(params_obj) > 1 else {}
                else:
                    filter_doc = {}
                    update_doc = {}
                
                # 使用会话执行更新操作
                result = await asyncio.get_event_loop().run_in_executor(
                    None, lambda: collection.update_one(filter_doc, update_doc, session=session)
                )
                
                return {
                    'operation': 'updateOne',
                    'matched_count': result.matched_count,
                    'modified_count': result.modified_count,
                    'acknowledged': True
                }
            
            elif op_name == 'deleteOne':
                # 解析删除条件
                if params:
                    params = self._parse_javascript_params(params)
                    filter_doc = self._parse_json_params(params)
                else:
                    filter_doc = {}
                
                # 使用会话执行删除操作
                result = await asyncio.get_event_loop().run_in_executor(
                    None, lambda: collection.delete_one(filter_doc, session=session)
                )
                
                return {
                    'operation': 'deleteOne',
                    'deleted_count': result.deleted_count,
                    'acknowledged': True
                }
            
            elif op_name == 'find':
                # 使用会话执行查询操作
                if params:
                    params = self._parse_javascript_params(params)
                    filter_doc = self._parse_json_params(params)
                else:
                    filter_doc = {}
                
                cursor = collection.find(filter_doc, session=session)
                documents = await asyncio.get_event_loop().run_in_executor(
                    None, lambda: list(cursor)
                )
                
                # 转换为JSON可序列化格式
                result_docs = []
                for doc in documents:
                    result_docs.append(bson_json_util.loads(bson_json_util.dumps(doc)))
                
                return {
                    'operation': 'find',
                    'documents': result_docs,
                    'count': len(result_docs)
                }
            
            else:
                raise Exception(f"Unsupported session operation: {op_name}")
                
        except Exception as e:
            logger.error(f"Session collection operation failed: {str(e)}")
            raise

# 注意：不创建默认实例，避免启动时连接外部数据库
# mongo_service 实例应该在需要时动态创建

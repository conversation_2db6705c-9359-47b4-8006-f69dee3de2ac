"""
Oracle MCP Agent服务 - 使用Model Context Protocol处理Oracle数据库操作
"""
import asyncio
import logging
from typing import Dict, Any, Optional, List
from services.oracle_service import OracleService
from services.oracle_packet_capture_service import OraclePacketCaptureService
from services.database_config_service import DatabaseConfigService
from tools.oracle_mcp_tools import OracleMCPQueryTool
from utils.config import Config

logger = logging.getLogger(__name__)

class OracleMCPAgentService:
    """Oracle MCP Agent服务"""
    
    def __init__(self):
        self.oracle_service = None
        self.db_config_service = DatabaseConfigService()
        self.current_config_id = None
        self.mcp_query_tool = OracleMCPQueryTool()
        logger.info("Oracle MCP Agent Service initialized")
    
    async def configure_oracle_connection(self, config_id: int) -> bool:
        """配置Oracle连接"""
        try:
            logger.info(f"Configuring Oracle connection for config_id: {config_id}")
            
            # 获取数据库配置
            config = await self.db_config_service.get_config(config_id)
            if not config:
                raise Exception(f"Database config not found: {config_id}")
            
            # 创建Oracle服务实例
            self.oracle_service = OracleService(
                host=config.host,
                port=config.port,
                user=config.user,
                password=config.password,
                service_name=config.database_name
            )
            
            # 测试连接
            test_result = await self.oracle_service.execute_query("SELECT 1 FROM DUAL")
            if not test_result.get('success', False):
                raise Exception(f"Oracle connection test failed: {test_result.get('error', 'Unknown error')}")
            
            self.current_config_id = config_id
            logger.info(f"Oracle connection configured successfully for config_id: {config_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to configure Oracle connection: {str(e)}")
            return False
    
    async def execute_sql_with_capture(
        self, 
        sql_query: str, 
        capture_packets: bool = True
    ) -> Dict[str, Any]:
        """执行Oracle SQL查询并可选择进行数据包捕获"""
        try:
            # 修复Oracle SQL分号问题 - 移除末尾分号
            sql_query = sql_query.strip().rstrip(';')
            logger.info(f"Executing Oracle SQL with capture (cleaned): {sql_query}")
            
            # 使用MCP查询工具
            result = await self.mcp_query_tool._arun(
                sql_query, 
                capture_packets=capture_packets,
                config_id=self.current_config_id
            )
            
            return {
                "success": True,
                "sql_query": sql_query,
                "result": result,
                "capture_enabled": capture_packets
            }
            
        except Exception as e:
            logger.error(f"Failed to execute Oracle SQL with capture: {str(e)}")
            return {
                "success": False,
                "sql_query": sql_query,
                "error": str(e)
            }
    
    async def execute_sql(self, sql_query: str) -> Dict[str, Any]:
        """执行Oracle SQL查询（不抓包）"""
        try:
            # 修复Oracle SQL分号问题 - 移除末尾分号
            sql_query = sql_query.strip().rstrip(';')
            logger.info(f"Executing Oracle SQL (cleaned): {sql_query}")
            
            if not self.oracle_service:
                raise Exception("Oracle connection not configured")
            
            result = await self.oracle_service.execute_query(sql_query)
            return {
                "success": True,
                "sql_query": sql_query,
                "result": result
            }
            
        except Exception as e:
            logger.error(f"Failed to execute Oracle SQL: {str(e)}")
            return {
                "success": False,
                "sql_query": sql_query,
                "error": str(e)
            }
    
    async def get_oracle_schema_info(self) -> Dict[str, Any]:
        """获取Oracle数据库架构信息"""
        try:
            if not self.oracle_service:
                raise Exception("Oracle connection not configured")
            
            # 获取表列表
            tables = await self.oracle_service.get_tables()
            
            # 获取用户信息
            user_info = await self.oracle_service.execute_query("SELECT USER FROM DUAL")
            current_user = user_info.get('data', [{}])[0].get('USER', 'Unknown')
            
            return {
                "success": True,
                "current_user": current_user,
                "tables": tables,
                "table_count": len(tables)
            }
            
        except Exception as e:
            logger.error(f"Failed to get Oracle schema info: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def process_natural_language_query(self, natural_query: str) -> Dict[str, Any]:
        """处理自然语言查询"""
        try:
            logger.info(f"Processing natural language query: {natural_query}")
            
            # 这里可以集成AI服务来生成SQL
            # 暂时返回简单的响应
            return {
                "success": True,
                "query": natural_query,
                "generated_sql": f"-- Generated SQL for: {natural_query}",
                "message": "Natural language processing not yet implemented"
            }
            
        except Exception as e:
            logger.error(f"Failed to process natural language query: {str(e)}")
            return {
                "success": False,
                "query": natural_query,
                "error": str(e)
            }

# 创建全局实例
oracle_mcp_agent_service = OracleMCPAgentService()

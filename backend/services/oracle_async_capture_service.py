import asyncio
import logging
from typing import Dict, Any, Optional
from services.oracle_service import OracleService
from services.oracle_packet_capture_service import OraclePacketCaptureService
from services.database_config_service import DatabaseConfigService
from utils.config import Config

logger = logging.getLogger(__name__)

class OracleAsyncCaptureService:
    """Oracle异步抓包服务 - 处理Oracle查询并触发异步抓包"""
    
    def __init__(self):
        self.oracle_service = None
        self.packet_service = OraclePacketCaptureService()
        self.db_config_service = DatabaseConfigService()
        logger.info("Oracle Async Capture Service initialized")
    
    def _get_oracle_service(self, config_id: Optional[int] = None):
        """获取Oracle服务实例"""
        if config_id and hasattr(self, '_current_config_id') and self._current_config_id == config_id:
            # 如果已经为当前config_id配置过，直接返回
            return self.oracle_service
        elif config_id:
            # 需要重新配置Oracle服务
            return None  # 强制重新创建
        elif self.oracle_service is None:
            # 使用默认配置
            oracle_config = Config.get_oracle_config()
            self.oracle_service = OracleService(**oracle_config)
        return self.oracle_service

    async def _configure_oracle_service(self, config_id: int):
        """根据配置ID配置Oracle服务"""
        try:
            # 获取数据库配置
            config = await self.db_config_service.get_config(config_id)
            if not config:
                raise Exception(f"Database config not found: {config_id}")

            # 创建新的Oracle服务实例
            self.oracle_service = OracleService(
                host=config.host,
                port=config.port,
                user=config.user,
                password=config.password,
                service_name=config.database_name  # Oracle使用service_name
            )
            
            # 连接到Oracle数据库
            await self.oracle_service.connect()
            
            # 保存当前配置ID
            self._current_config_id = config_id
            
            logger.info(f"Oracle service configured for config_id: {config_id}")
            return self.oracle_service
            
        except Exception as e:
            logger.error(f"Failed to configure Oracle service: {str(e)}")
            raise

    async def execute_sql_with_async_capture(
        self,
        sql_query: str,
        config_id: Optional[int] = None,
        enable_capture: bool = True
    ) -> Dict[str, Any]:
        """执行Oracle SQL查询并进行异步抓包"""
        try:
            logger.info(f"执行Oracle SQL异步抓包: {sql_query}, 抓包启用: {enable_capture}")

            # 获取数据库配置
            db_config = None
            if config_id:
                db_config = await self.db_config_service.get_config(config_id)

            # 启动抓包（如果启用）
            packet_file = None
            if enable_capture:
                try:
                    # 使用配置的Oracle地址
                    oracle_host = "**************"  # 默认Oracle服务器
                    oracle_port = 1521

                    # 如果有数据库配置，使用数据库配置的地址
                    if db_config:
                        oracle_host = db_config.host
                        oracle_port = db_config.port

                    logger.info(f"Starting packet capture for Oracle at {oracle_host}:{oracle_port}")
                    packet_file = await self.packet_service.start_capture(
                        oracle_host, oracle_port
                    )
                    logger.info(f"Started packet capture: {packet_file}")
                except Exception as e:
                    logger.warning(f"Failed to start packet capture: {str(e)}")
            else:
                logger.info("抓包已禁用，仅执行SQL")

            # 执行SQL
            execution_result = None
            try:
                if enable_capture:
                    await asyncio.sleep(1)  # 等待抓包启动

                # 配置并获取Oracle服务
                if config_id:
                    oracle_service = await self._configure_oracle_service(config_id)
                else:
                    oracle_service = self._get_oracle_service()

                execution_result = await oracle_service.execute_query(sql_query)

                if enable_capture:
                    await asyncio.sleep(2)  # 等待数据包捕获

            except Exception as e:
                logger.error(f"SQL execution failed: {str(e)}")
                execution_result = {"error": str(e)}

            # 停止抓包（如果启用）
            final_packet_file = None
            if enable_capture and packet_file:
                try:
                    final_packet_file = await self.packet_service.stop_capture()
                    logger.info(f"Packet capture completed: {final_packet_file}")
                except Exception as e:
                    logger.warning(f"Failed to stop packet capture: {str(e)}")

            return {
                "success": True,
                "sql_query": sql_query,
                "execution_result": execution_result,
                "packet_file": final_packet_file,
                "config_id": config_id,
                "capture_enabled": enable_capture
            }

        except Exception as e:
            logger.error(f"Oracle异步抓包执行失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "sql_query": sql_query,
                "execution_result": None,
                "packet_file": None
            }

    async def execute_sql_without_capture(
        self,
        sql_query: str,
        config_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """执行Oracle SQL查询但不进行抓包（用于统一抓包模式）"""
        return await self.execute_sql_with_async_capture(
            sql_query, config_id, enable_capture=False
        )

    async def process_natural_language_query(
        self, 
        natural_query: str, 
        config_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """处理自然语言查询并执行Oracle SQL"""
        try:
            logger.info(f"Processing Oracle natural language query: {natural_query}")
            
            # 使用Oracle AI服务生成SQL
            from services.oracle_ai_service import OracleAIService
            oracle_ai_service = OracleAIService()
            
            # 获取Oracle配置
            oracle_config = {}
            if config_id:
                db_config = await self.db_config_service.get_config(config_id)
                if db_config:
                    oracle_config = {
                        'host': db_config.host,
                        'port': db_config.port,
                        'user': db_config.user,
                        'password': db_config.password,
                        'service_name': db_config.database_name
                    }
            
            # 生成Oracle查询
            generation_result = await oracle_ai_service.generate_oracle_query(
                natural_query, oracle_config
            )
            
            if not generation_result.get('success', False):
                return {
                    "success": False,
                    "error": generation_result.get('error', 'Failed to generate Oracle query'),
                    "query": natural_query
                }
            
            generated_sql = generation_result.get('sql_query', '')
            logger.info(f"Generated Oracle SQL: {generated_sql}")
            
            # 执行生成的SQL并抓包
            execution_result = await self.execute_sql_with_async_capture(
                generated_sql, config_id
            )
            
            return {
                "success": True,
                "query": natural_query,
                "generated_sql": generated_sql,
                "execution_result": execution_result.get("execution_result"),
                "packet_file": execution_result.get("packet_file"),
                "sql_generation_result": generation_result
            }
            
        except Exception as e:
            logger.error(f"Failed to process Oracle natural language query: {str(e)}")
            return {
                "success": False,
                "query": natural_query,
                "error": str(e)
            }

# 创建全局实例
oracle_async_capture_service = OracleAsyncCaptureService()

"""
MongoDB C语言执行器服务
提供MongoDB C语言执行器的Python接口，支持持久连接和异步执行
"""

import asyncio
import json
import logging
import os
import subprocess
from pathlib import Path
from typing import Dict, Any, Optional
from utils.environment_detector import get_environment_detector

logger = logging.getLogger(__name__)


class MongoCExecutorService:
    """MongoDB C语言执行器服务"""
    
    def __init__(self):
        self.executor_path = None
        self.persistent_process = None
        self.is_connected = False
        self.env_detector = get_environment_detector()
        self._initialize_executor()
    
    def _initialize_executor(self):
        """初始化C语言执行器"""
        logger.info("Initializing MongoDB C executor...")
        logger.info(f"Environment: {self.env_detector.system} ({self.env_detector.machine})")
        logger.info(f"Docker: {self.env_detector.is_docker}")

        # 使用环境检测器获取执行器路径
        executor_path = self.env_detector.get_c_executor_path('mongodb')

        if executor_path:
            self.executor_path = executor_path
            logger.info(f"Found compatible MongoDB C executor at: {self.executor_path}")
        else:
            logger.warning("No compatible MongoDB C executor found")
            # 尝试编译
            current_dir = Path(__file__).parent.parent
            executor_dir = current_dir / "c_executors"
            self._compile_executor(executor_dir)
    
    def _compile_executor(self, executor_dir: Path):
        """编译C语言执行器"""
        try:
            logger.info("Attempting to compile MongoDB C executor...")
            logger.info(f"Target environment: {self.env_detector.system} {self.env_detector.machine}")

            # 检查源文件是否存在
            source_file = executor_dir / "mongo_libmongoc_executor.c"
            if not source_file.exists():
                logger.error(f"Source file not found: {source_file}")
                return

            # 根据环境选择编译命令
            if self.env_detector.is_docker or self.env_detector.is_linux:
                # Linux环境编译
                compile_cmd = ["make", "-C", str(executor_dir), "mongo"]
                logger.info("Using Linux compilation command")
            elif self.env_detector.is_macos:
                # macOS环境编译
                compile_cmd = ["make", "-C", str(executor_dir), "mongo"]
                logger.info("Using macOS compilation command")
            else:
                logger.error(f"Unsupported compilation environment: {self.env_detector.system}")
                return

            result = subprocess.run(
                compile_cmd,
                capture_output=True,
                text=True,
                timeout=120  # 增加超时时间
            )

            if result.returncode == 0:
                # 重新检查执行器是否可用
                executor_path = self.env_detector.get_c_executor_path('mongodb')
                if executor_path:
                    self.executor_path = executor_path
                    logger.info("MongoDB C executor compiled and verified successfully")
                else:
                    logger.error("Compilation succeeded but executable is not compatible")
            else:
                logger.error(f"Compilation failed: {result.stderr}")
                logger.debug(f"Compilation stdout: {result.stdout}")

        except subprocess.TimeoutExpired:
            logger.error("Compilation timeout (120s)")
        except Exception as e:
            logger.error(f"Compilation error: {e}")
    
    async def create_persistent_connection(self, host: str, port: int, user: str, 
                                         password: str, database: str) -> bool:
        """创建持久连接"""
        if not self.executor_path:
            raise Exception("MongoDB C executor not available")
        
        try:
            # 启动持久进程
            self.persistent_process = await asyncio.create_subprocess_exec(
                self.executor_path,
                stdin=asyncio.subprocess.PIPE,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            # 发送连接命令
            connect_cmd = f"connect {host} {port} {user} {password} {database}\n"
            self.persistent_process.stdin.write(connect_cmd.encode())
            await self.persistent_process.stdin.drain()
            
            # 跳过C执行器的欢迎信息，读取到JSON响应
            response_line = None
            max_attempts = 10
            for attempt in range(max_attempts):
                line = await self.persistent_process.stdout.readline()
                line_str = line.decode().strip()

                # 跳过非JSON行（欢迎信息等）
                if line_str.startswith('{') and line_str.endswith('}'):
                    response_line = line_str
                    break
                elif (not line_str or
                      line_str.startswith('MongoDB C Executor') or
                      line_str.startswith('Commands:') or
                      line_str.startswith('  ') or
                      'connect <host>' in line_str or
                      'execute <mongo_query>' in line_str or
                      'close' in line_str and 'quit' in line_str):
                    continue
                else:
                    # 可能是错误信息
                    logger.debug(f"Skipping C executor output: {line_str}")

            if not response_line:
                raise Exception("Failed to get valid JSON response from C executor")

            response = json.loads(response_line)
            
            if response.get('success'):
                self.is_connected = True
                logger.info(f"MongoDB persistent connection established: {host}:{port}")
                return True
            else:
                logger.error(f"Failed to create persistent connection: {response.get('error')}")
                await self._cleanup_persistent_process()
                return False
                
        except Exception as e:
            logger.error(f"Failed to create persistent connection: {e}")
            await self._cleanup_persistent_process()
            return False
    
    async def execute_mongo_query_persistent(self, mongo_query: str) -> Dict[str, Any]:
        """使用持久连接执行MongoDB查询"""
        if not self.persistent_process or not self.is_connected:
            raise Exception("No persistent connection available")
        
        try:
            # 发送查询命令
            execute_cmd = f"execute {mongo_query}\n"
            self.persistent_process.stdin.write(execute_cmd.encode())
            await self.persistent_process.stdin.drain()
            
            # 读取JSON响应（跳过可能的非JSON输出）
            response_line = None
            max_attempts = 5
            for attempt in range(max_attempts):
                line = await self.persistent_process.stdout.readline()
                line_str = line.decode().strip()

                # 查找JSON响应
                if line_str.startswith('{') and line_str.endswith('}'):
                    response_line = line_str
                    break
                elif not line_str:
                    continue
                else:
                    logger.debug(f"Skipping non-JSON output: {line_str}")

            if not response_line:
                raise Exception("Failed to get valid JSON response from C executor")

            response = json.loads(response_line)
            
            return response
            
        except Exception as e:
            logger.error(f"Failed to execute persistent MongoDB query: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def close_persistent_connection(self):
        """关闭持久连接"""
        if self.persistent_process and self.is_connected:
            try:
                # 发送关闭命令
                close_cmd = "close\n"
                self.persistent_process.stdin.write(close_cmd.encode())
                await self.persistent_process.stdin.drain()

                # 等待一小段时间让连接正常关闭
                await asyncio.sleep(0.5)

                # 读取响应
                try:
                    response_line = None
                    max_attempts = 3
                    for attempt in range(max_attempts):
                        line = await asyncio.wait_for(
                            self.persistent_process.stdout.readline(),
                            timeout=1.0
                        )
                        line_str = line.decode().strip()

                        if line_str.startswith('{') and line_str.endswith('}'):
                            response_line = line_str
                            break
                        elif not line_str:
                            continue

                    if response_line:
                        response = json.loads(response_line)
                        logger.info("MongoDB persistent connection closed gracefully")
                    else:
                        logger.warning("No valid JSON response for close command")

                except asyncio.TimeoutError:
                    logger.warning("Close command response timeout, connection may already be closed")

            except Exception as e:
                logger.error(f"Error closing persistent connection: {e}")
            finally:
                # 标记为未连接状态，避免重复关闭
                self.is_connected = False
                await self._cleanup_persistent_process()
    
    async def _cleanup_persistent_process(self):
        """清理持久进程"""
        if self.persistent_process:
            try:
                # 如果连接仍然活跃，发送退出命令
                if self.persistent_process.stdin and not self.persistent_process.stdin.is_closing():
                    self.persistent_process.stdin.write(b"quit\n")
                    await self.persistent_process.stdin.drain()

                    # 关闭stdin以信号进程结束
                    self.persistent_process.stdin.close()
                    await self.persistent_process.stdin.wait_closed()

                # 等待进程正常退出，给更多时间
                await asyncio.wait_for(self.persistent_process.wait(), timeout=10.0)
                logger.info("MongoDB C executor process terminated gracefully")

            except asyncio.TimeoutError:
                logger.warning("Persistent process did not terminate gracefully within 10s, sending SIGTERM")
                try:
                    self.persistent_process.terminate()
                    await asyncio.wait_for(self.persistent_process.wait(), timeout=5.0)
                    logger.info("MongoDB C executor process terminated with SIGTERM")
                except asyncio.TimeoutError:
                    logger.warning("Process did not respond to SIGTERM, using SIGKILL")
                    self.persistent_process.kill()
                    await self.persistent_process.wait()
            except Exception as e:
                logger.error(f"Error cleaning up persistent process: {e}")
            finally:
                self.persistent_process = None
                self.is_connected = False
    
    async def execute_single_mongo_query(self, host: str, port: int, user: str, 
                                       password: str, database: str, 
                                       mongo_query: str) -> Dict[str, Any]:
        """执行单个MongoDB查询（非持久连接）"""
        if not self.executor_path:
            return {
                "success": False,
                "error": "MongoDB C executor not available"
            }
        
        try:
            # 创建临时进程执行查询
            process = await asyncio.create_subprocess_exec(
                self.executor_path,
                "connect", host, str(port), user, password, database,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                return {
                    "success": False,
                    "error": f"Connection failed: {stderr.decode()}"
                }
            
            # 连接成功后执行查询
            execute_result = await asyncio.create_subprocess_exec(
                self.executor_path,
                "execute", mongo_query,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await execute_result.communicate()
            
            if execute_result.returncode == 0:
                response = json.loads(stdout.decode().strip())
                return response
            else:
                return {
                    "success": False,
                    "error": f"Execution failed: {stderr.decode()}"
                }
                
        except Exception as e:
            logger.error(f"Failed to execute single MongoDB query: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def is_available(self) -> bool:
        """检查C执行器是否可用"""
        return self.executor_path is not None and os.path.exists(self.executor_path)
    
    async def test_connection(self, host: str, port: int, user: str, 
                            password: str, database: str) -> Dict[str, Any]:
        """测试数据库连接"""
        if not self.executor_path:
            return {
                "success": False,
                "error": "MongoDB C executor not available"
            }
        
        try:
            # 使用ping命令测试连接
            test_query = "db.adminCommand({ping: 1})"
            result = await self.execute_single_mongo_query(
                host, port, user, password, database, test_query
            )
            
            if result.get('success'):
                return {
                    "success": True,
                    "message": "MongoDB connection test successful"
                }
            else:
                return {
                    "success": False,
                    "error": f"Connection test failed: {result.get('error')}"
                }
                
        except Exception as e:
            logger.error(f"Connection test failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }


# 创建全局服务实例
mongo_c_executor_service = MongoCExecutorService()

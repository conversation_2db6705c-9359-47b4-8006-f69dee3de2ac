"""
测试用例AI生成服务
使用Lang<PERSON>hain和DeepSeek实现三个Agent：测试用例生成、评审、重新生成
"""

import logging
import traceback
import json
from typing import Dict, Any, List, Optional
from langchain.prompts import PromptTemplate
from langchain.schema import BaseOutputParser
from langchain.llms.base import LLM
from langchain.chains import LLMChain
from langchain.agents import Tool, AgentExecutor, create_react_agent
from langchain.memory import ConversationBufferMemory
from openai import OpenAI
from utils.config import Config
from models.test_case_management import TestStep, PriorityEnum, StatusEnum, AutomationLevelEnum, DatabaseTypeEnum

logger = logging.getLogger(__name__)

class DeepSeekLLM(LLM):
    """DeepSeek LLM适配器，用于LangChain"""

    def __init__(self):
        super().__init__()
        self._client = OpenAI(
            api_key=Config.DEEPSEEK_API_KEY,
            base_url=Config.DEEPSEEK_BASE_URL
        )
    
    def _call(self, prompt: str, stop: Optional[List[str]] = None) -> str:
        """调用DeepSeek API"""
        try:
            response = self._client.chat.completions.create(
                model="deepseek-chat",
                messages=[
                    {"role": "system", "content": "你是一个专业的软件测试专家，精通SQL协议和数据库测试用例设计。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=8192
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            logger.error(f"DeepSeek API调用失败: {e}")
            raise
    
    @property
    def _llm_type(self) -> str:
        return "deepseek"

class TestCaseOutputParser(BaseOutputParser):
    """测试用例输出解析器"""
    
    def parse(self, text: str) -> Dict[str, Any]:
        """解析测试用例生成结果"""
        try:
            # 尝试解析JSON格式
            if text.strip().startswith('{') and text.strip().endswith('}'):
                return json.loads(text)
            
            # 如果不是JSON格式，尝试从文本中提取信息
            lines = text.strip().split('\n')
            result = {
                "title": "",
                "module": "SQL测试",
                "priority": "medium",
                "preconditions": "",
                "test_steps": [],
                "expected_result": "",
                "test_data": {},
                "tags": ["SQL测试", "数据库测试"],
                "automation_level": "manual",
                "database_type": "mysql"
            }
            
            current_section = None
            step_number = 1
            
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                    
                if line.startswith("标题:") or line.startswith("用例标题:"):
                    result["title"] = line.split(":", 1)[1].strip()
                elif line.startswith("模块:") or line.startswith("所属模块:"):
                    result["module"] = line.split(":", 1)[1].strip()
                elif line.startswith("优先级:"):
                    priority = line.split(":", 1)[1].strip().lower()
                    if priority in ["low", "medium", "high", "critical"]:
                        result["priority"] = priority
                elif line.startswith("前置条件:"):
                    current_section = "preconditions"
                    result["preconditions"] = line.split(":", 1)[1].strip()
                elif line.startswith("测试步骤:"):
                    current_section = "steps"
                elif line.startswith("预期结果:"):
                    current_section = "expected"
                    result["expected_result"] = line.split(":", 1)[1].strip()
                elif current_section == "steps" and (line.startswith(f"{step_number}.") or line.startswith(f"步骤{step_number}")):
                    # 解析测试步骤
                    step_content = line.split(".", 1)[1].strip() if "." in line else line
                    result["test_steps"].append({
                        "step_number": step_number,
                        "action": step_content,
                        "expected_result": "",
                        "test_data": ""
                    })
                    step_number += 1
                elif current_section == "preconditions":
                    result["preconditions"] += " " + line
                elif current_section == "expected":
                    result["expected_result"] += " " + line
            
            return result
            
        except Exception as e:
            logger.error(f"解析测试用例失败: {e}")
            return {
                "title": "解析失败的测试用例",
                "module": "SQL测试",
                "priority": "medium",
                "preconditions": "",
                "test_steps": [],
                "expected_result": "",
                "test_data": {},
                "tags": ["SQL测试"],
                "automation_level": "manual",
                "database_type": "mysql"
            }

class TestCaseGenerationAgent:
    """测试用例生成Agent"""
    
    def __init__(self, llm: DeepSeekLLM):
        self.llm = llm
        self.parser = TestCaseOutputParser()
        
        # 测试用例生成提示词模板 - 统一格式，优化SQL提取
        self.generation_template = PromptTemplate(
            input_variables=["requirement", "database_type", "operation_type"],
            template="""
你是一个专业的软件测试专家，精通SQL协议和数据库测试用例设计。

请根据以下需求生成详细的测试用例：

需求描述: {requirement}
数据库类型: {database_type}
操作类型: {operation_type}

**重要：统一测试用例格式要求**
为了确保SQL语句能够被准确提取和执行，请严格按照以下格式生成测试用例：

1. **操作描述格式**：
   - action字段只包含简洁的操作描述，不包含SQL语句
   - 例如："创建测试表 - 创建带约束的account表"
   - 例如："插入测试数据 - 插入3条用户记录"
   - 例如："执行查询操作 - 查询各部门平均薪资"

2. **SQL语句存放**：
   - 所有SQL语句必须放在test_data字段中
   - 如果是单个SQL语句，直接放入test_data
   - 如果是多个SQL语句，用分号分隔放入test_data
   - SQL语句必须完整、可执行，不能省略任何部分

3. **数据库特性支持**：
   - 对于GaussDB，重点关注其兼容PostgreSQL的特性以及华为特有的功能
   - 对于PostgreSQL，使用标准PostgreSQL语法
   - 对于不同数据库版本，生成相应版本特性的测试用例

**重要：数据库操作前置条件要求**
在生成测试用例时，必须严格遵循以下数据准备原则：

1. **前置条件必须详细具体**：
   - 详细描述所需的表结构（字段名、数据类型、约束、索引）
   - 明确说明测试数据的具体内容和数量
   - 说明测试环境的初始状态（表是否存在、数据是否为空）

2. **删除操作测试**：
   - 必须先创建目标表（如果不存在），提供完整的CREATE TABLE语句
   - 必须先插入要删除的测试数据，提供具体的INSERT语句和数据内容
   - 确保要删除的数据确实存在于表中，提供验证查询
   - 说明删除条件和预期影响的记录数

3. **更新操作测试**：
   - 必须先创建目标表（如果不存在），提供完整的CREATE TABLE语句
   - 必须先插入要更新的测试数据，提供具体的INSERT语句和原始数据
   - 确保要更新的记录确实存在于表中，提供验证查询
   - 提供更新前后的数据对比，明确更新条件和新值

4. **插入操作测试**：
   - 必须先创建目标表（如果不存在），提供完整的CREATE TABLE语句
   - 必须提供完整的表结构定义（包含字段类型、约束、默认值、索引等）
   - 如果涉及外键约束，需要先准备关联表的数据，提供具体的关联表结构和数据
   - 说明插入数据的具体内容和预期结果

5. **查询操作测试**：
   - 必须先创建目标表（如果不存在），提供完整的CREATE TABLE语句
   - 必须先插入足够的测试数据以验证查询结果，提供具体的INSERT语句
   - 数据应该覆盖各种查询场景（正常数据、边界数据、空值、重复数据等）
   - 明确说明测试数据的分布和特征，便于验证查询结果

6. **表结构操作测试**：
   - 创建表前需要检查表是否已存在，提供检查语句
   - 删除表前需要确保表存在，提供验证语句
   - 修改表结构前需要确保表和相关字段存在，提供当前结构信息

请生成一个完整的测试用例，包含以下内容：

1. 用例标题：简洁明确地描述测试目标，如果涉及多种场景请体现在标题中
2. 所属模块：根据操作类型确定模块名称
3. 优先级：根据重要性设置为low/medium/high/critical
4. 前置条件：执行测试前需要满足的条件，必须包括：
   - 数据库连接信息：主机地址、端口、数据库名、用户名、所需权限
   - 环境状态：测试开始前数据库的状态（表是否存在、数据是否为空）
   - 依赖条件：所需的数据库版本、特性支持、配置要求
   - 权限要求：执行测试所需的数据库权限（CREATE、INSERT、UPDATE、DELETE等）

5. 测试步骤：详细的操作步骤，每个步骤必须包含：
   - **第一步必须是环境检查和数据准备**：
     * action: "环境检查和数据准备 - 创建测试表并插入数据"
     * test_data: "CREATE TABLE test_table (...); INSERT INTO test_table VALUES (...);"
     * expected_result: "表创建成功，X条测试数据插入成功"
   - **主要测试步骤**：
     * action: "执行主要操作 - [具体操作描述]"
     * test_data: "[完整的SQL语句]"
     * expected_result: "[具体的预期结果，包括返回记录数、关键字段值等]"
   - **结果验证步骤**：
     * action: "验证操作结果 - 检查操作是否成功"
     * test_data: "[验证查询的SQL语句]"
     * expected_result: "[验证查询的具体预期结果]"
   - **最后一步必须是环境清理**：
     * action: "环境清理 - 删除测试数据和表"
     * test_data: "DELETE FROM test_table; DROP TABLE test_table;"
     * expected_result: "测试数据清理成功，数据库恢复到初始状态"

6. 预期结果：每个步骤的预期结果必须具体明确：
   - 不能使用模糊描述如"操作成功"
   - 必须说明具体的返回值、影响行数、数据内容
   - 对于查询操作，必须说明返回记录数和关键字段的值
   - 例如："查询返回3条记录，Finance部门平均薪资60000.00，IT部门平均薪资52500.00，HR部门平均薪资45000.00"

7. 测试数据：提供完整的测试数据示例：
   - 完整的表结构定义（字段名、数据类型、约束、索引）
   - 具体的测试数据内容（不少于3-5条记录）
   - 数据的业务含义和测试目的

重点关注以下SQL协议和数据库操作相关的测试点：
- SQL语法正确性
- 数据完整性约束
- 事务处理
- 并发控制
- 性能测试
- 安全性测试
- 错误处理
- 复合查询的正确性
- 查询优化和索引使用

请以结构化的格式返回，便于解析：

标题: [测试用例标题]
模块: [所属模块]
优先级: [优先级]
前置条件: [详细的前置条件描述，包括数据库连接、表结构、测试数据等]
测试步骤:
1. 操作: 环境检查和数据准备 - [具体描述]
   预期结果: [具体的预期结果]
   测试数据: [完整的SQL语句，多个语句用分号分隔]
2. 操作: 执行主要操作 - [具体描述]
   预期结果: [具体的预期结果]
   测试数据: [完整的SQL语句]
3. 操作: 验证操作结果 - [具体描述]
   预期结果: [具体的预期结果]
   测试数据: [验证查询的SQL语句]
4. 操作: 环境清理 - [具体描述]
   预期结果: [具体的预期结果]
   测试数据: [清理操作的SQL语句]
...
预期结果: [整体预期结果]

**格式要求**：
- action字段只包含操作描述，不包含SQL语句
- 所有SQL语句必须放在test_data字段中
- 测试数据（test_data字段）中不得包含任何注释（如--、#、/* ... */、//等），无论是MySQL、PostgreSQL、Oracle、MongoDB、GaussDB等数据库类型，均需保证测试数据无注释。
- SQL语句必须完整、可执行
- 多个SQL语句用分号分隔
"""
        )
        
        # 创建生成链
        self.generation_chain = LLMChain(
            llm=self.llm,
            prompt=self.generation_template,
            output_parser=self.parser
        )
    
    async def generate_test_case(self, requirement: str, database_type: str = "mysql", operation_type: str = "查询") -> Dict[str, Any]:
        """生成测试用例"""
        try:
            logger.info(f"生成测试用例 - 需求: {requirement}, 数据库: {database_type}, 操作: {operation_type}")
            
            result = await self._run_chain_async(
                requirement=requirement,
                database_type=database_type,
                operation_type=operation_type
            )
            
            # 确保返回的数据格式正确
            if isinstance(result, dict):
                # 补充默认值
                result.setdefault("author", "AI生成")
                result.setdefault("review_status", "pending")
                result.setdefault("estimated_time", 30)
                result.setdefault("test_environment", "测试环境")
                result.setdefault("database_type", database_type.lower())
                
                # 确保test_steps格式正确
                if "test_steps" in result and isinstance(result["test_steps"], list):
                    for i, step in enumerate(result["test_steps"]):
                        if isinstance(step, dict):
                            step.setdefault("step_number", i + 1)
                            step.setdefault("expected_result", "操作成功")
                            step.setdefault("test_data", "")

                # 标准化测试用例格式
                result = self.standardize_test_case_format(result)

                logger.info(f"测试用例生成成功: {result.get('title', '未知标题')}")
                return result
            else:
                logger.error(f"生成结果格式错误: {type(result)}")
                return self._create_default_test_case(requirement, database_type, operation_type)
                
        except Exception as e:
            logger.error(f"生成测试用例失败: {e}")
            return self._create_default_test_case(requirement, database_type, operation_type)
    
    def _create_default_test_case(self, requirement: str, database_type: str, operation_type: str) -> Dict[str, Any]:
        """创建默认测试用例"""
        # 根据操作类型生成不同的测试步骤
        test_steps = []

        if operation_type.lower() in ["删除", "delete"]:
            test_steps = [
                {
                    "step_number": 1,
                    "action": f"连接到{database_type}数据库",
                    "expected_result": "连接成功",
                    "test_data": ""
                },
                {
                    "step_number": 2,
                    "action": "创建测试表（如果不存在）：CREATE TABLE test_table (id INT PRIMARY KEY, name VARCHAR(50))",
                    "expected_result": "表创建成功或已存在",
                    "test_data": "表结构定义"
                },
                {
                    "step_number": 3,
                    "action": "插入测试数据：INSERT INTO test_table (id, name) VALUES (1, 'test_data')",
                    "expected_result": "测试数据插入成功",
                    "test_data": "测试记录"
                },
                {
                    "step_number": 4,
                    "action": f"执行{operation_type}操作：DELETE FROM test_table WHERE id = 1",
                    "expected_result": "删除操作成功完成",
                    "test_data": "删除条件"
                },
                {
                    "step_number": 5,
                    "action": "验证删除结果：SELECT * FROM test_table WHERE id = 1",
                    "expected_result": "查询结果为空，确认数据已删除",
                    "test_data": "验证查询"
                }
            ]
        elif operation_type.lower() in ["更新", "update"]:
            test_steps = [
                {
                    "step_number": 1,
                    "action": f"连接到{database_type}数据库",
                    "expected_result": "连接成功",
                    "test_data": ""
                },
                {
                    "step_number": 2,
                    "action": "创建测试表（如果不存在）：CREATE TABLE test_table (id INT PRIMARY KEY, name VARCHAR(50))",
                    "expected_result": "表创建成功或已存在",
                    "test_data": "表结构定义"
                },
                {
                    "step_number": 3,
                    "action": "插入测试数据：INSERT INTO test_table (id, name) VALUES (1, 'old_value')",
                    "expected_result": "测试数据插入成功",
                    "test_data": "原始测试记录"
                },
                {
                    "step_number": 4,
                    "action": f"执行{operation_type}操作：UPDATE test_table SET name = 'new_value' WHERE id = 1",
                    "expected_result": "更新操作成功完成",
                    "test_data": "更新条件和新值"
                },
                {
                    "step_number": 5,
                    "action": "验证更新结果：SELECT * FROM test_table WHERE id = 1",
                    "expected_result": "查询结果显示name字段已更新为'new_value'",
                    "test_data": "验证查询"
                }
            ]
        elif operation_type.lower() in ["插入", "insert"]:
            test_steps = [
                {
                    "step_number": 1,
                    "action": f"连接到{database_type}数据库",
                    "expected_result": "连接成功",
                    "test_data": ""
                },
                {
                    "step_number": 2,
                    "action": "创建测试表（如果不存在）：CREATE TABLE test_table (id INT PRIMARY KEY, name VARCHAR(50))",
                    "expected_result": "表创建成功或已存在",
                    "test_data": "表结构定义"
                },
                {
                    "step_number": 3,
                    "action": f"执行{operation_type}操作：INSERT INTO test_table (id, name) VALUES (1, 'test_value')",
                    "expected_result": "插入操作成功完成",
                    "test_data": "插入的测试数据"
                },
                {
                    "step_number": 4,
                    "action": "验证插入结果：SELECT * FROM test_table WHERE id = 1",
                    "expected_result": "查询结果显示数据已成功插入",
                    "test_data": "验证查询"
                }
            ]
        else:  # 默认查询操作
            test_steps = [
                {
                    "step_number": 1,
                    "action": f"连接到{database_type}数据库",
                    "expected_result": "连接成功",
                    "test_data": ""
                },
                {
                    "step_number": 2,
                    "action": "创建测试表（如果不存在）：CREATE TABLE test_table (id INT PRIMARY KEY, name VARCHAR(50))",
                    "expected_result": "表创建成功或已存在",
                    "test_data": "表结构定义"
                },
                {
                    "step_number": 3,
                    "action": "插入测试数据：INSERT INTO test_table (id, name) VALUES (1, 'test_data'), (2, 'test_data2')",
                    "expected_result": "测试数据插入成功",
                    "test_data": "测试记录集"
                },
                {
                    "step_number": 4,
                    "action": f"执行{operation_type}操作：SELECT * FROM test_table",
                    "expected_result": "查询操作成功完成，返回预期数据",
                    "test_data": "查询条件"
                }
            ]

        return {
            "title": f"{operation_type}操作测试用例",
            "module": f"{database_type.upper()}测试",
            "priority": "medium",
            "preconditions": f"数据库连接正常，{database_type}服务运行正常，具备创建表和操作数据的权限",
            "test_steps": test_steps,
            "expected_result": f"{operation_type}操作正常完成，数据正确",
            "test_data": {},
            "tags": [f"{database_type}测试", f"{operation_type}测试"],
            "author": "AI生成",
            "review_status": "pending",
            "estimated_time": 30,
            "automation_level": "manual",
            "test_environment": "测试环境",
            "database_type": database_type.lower()
        }
    
    async def _run_chain_async(self, **kwargs) -> Dict[str, Any]:
        """异步运行LangChain"""
        import asyncio
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.generation_chain.run, kwargs)

class BatchTestCaseGenerationAgent:
    """批量测试用例生成Agent"""

    def __init__(self, llm: DeepSeekLLM):
        self.llm = llm

        # 批量生成提示词模板
        self.batch_generation_template = PromptTemplate(
            input_variables=["requirement", "database_type", "operation_type"],
            template="""
你是一个专业的软件测试专家，精通SQL协议和数据库测试用例设计。

请根据以下需求生成详细的测试用例：

需求描述: {requirement}
数据库类型: {database_type}
操作类型: {operation_type}

**生成策略**：
1. 仔细分析需求描述，理解用户的真实测试需求
2. 如果需求中明确提到了具体数量（如"生成3个用例"、"写5个测试"等），则按照指定数量生成
3. 如果需求中没有明确数量，则根据测试场景的复杂度和覆盖度需要，智能决定生成数量：
   - 简单场景（如基础CRUD操作）：生成2-4个用例覆盖主要场景
   - 复杂场景（如事务处理、性能测试、安全测试）：生成4-8个用例覆盖各种情况
   - 综合场景（如完整的功能模块测试）：生成6-10个用例确保全面覆盖
   - **综合操作类型**：当操作类型为"综合"时，生成包含增删改查、事务处理、性能测试等多种操作类型的完整测试套件，通常生成8-15个用例
4. 每个测试用例应该覆盖不同的测试场景，避免重复
5. 优先覆盖核心功能和常见异常情况
6. **数据库特性支持**：
   - 对于GaussDB，重点关注其兼容PostgreSQL的特性以及华为特有的功能
   - 对于不同数据库版本，生成相应版本特性的测试用例

**重要：数据库操作前置条件要求**
在生成每个测试用例时，必须严格遵循以下数据准备原则：

1. **前置条件必须详细具体**：
   - 明确数据库连接信息（主机、端口、数据库名、用户权限）
   - 详细描述所需的表结构（字段名、数据类型、约束、索引）
   - 明确说明测试数据的具体内容和数量
   - 说明测试环境的初始状态（表是否存在、数据是否为空）

2. **删除操作测试**：
   - 必须先创建目标表（如果不存在），提供完整的CREATE TABLE语句
   - 必须先插入要删除的测试数据，提供具体的INSERT语句和数据内容
   - 确保要删除的数据确实存在于表中，提供验证查询
   - 说明删除条件和预期影响的记录数

3. **更新操作测试**：
   - 必须先创建目标表（如果不存在），提供完整的CREATE TABLE语句
   - 必须先插入要更新的测试数据，提供具体的INSERT语句和原始数据
   - 确保要更新的记录确实存在于表中，提供验证查询
   - 提供更新前后的数据对比，明确更新条件和新值

4. **插入操作测试**：
   - 必须先创建目标表（如果不存在），提供完整的CREATE TABLE语句
   - 必须提供完整的表结构定义（包含字段类型、约束、默认值、索引等）
   - 如果涉及外键约束，需要先准备关联表的数据，提供具体的关联表结构和数据
   - 说明插入数据的具体内容和预期结果

5. **查询操作测试**：
   - 必须先创建目标表（如果不存在），提供完整的CREATE TABLE语句
   - 必须先插入足够的测试数据以验证查询结果，提供具体的INSERT语句
   - 数据应该覆盖各种查询场景（正常数据、边界数据、空值、重复数据等）
   - 明确说明测试数据的分布和特征，便于验证查询结果

6. **表结构操作测试**：
   - 创建表前需要检查表是否已存在，提供检查语句
   - 删除表前需要确保表存在，提供验证语句
   - 修改表结构前需要确保表和相关字段存在，提供当前结构信息

**操作类型特殊处理**：
- **综合操作类型**：当操作类型为"综合"时，生成包含以下所有类型的完整测试套件：
  1. 基础CRUD操作（CREATE, INSERT, SELECT, UPDATE, DELETE）
  2. 复杂查询（JOIN, 子查询, 聚合函数, 分组排序）
  3. 事务处理（BEGIN, COMMIT, ROLLBACK, 隔离级别）
  4. 约束测试（主键, 外键, 唯一约束, 检查约束）
  5. 索引操作（创建索引, 查询优化）
  6. 存储过程/函数（如果数据库支持）
  7. 性能测试（大数据量, 并发操作）
  8. 异常处理（语法错误, 权限错误, 资源不足）
  9. 数据库特有功能（如GaussDB的分布式特性）

- **其他操作类型**：按照传统方式处理，如查询测试包含：
  1. 基础SELECT查询
  2. 条件查询（WHERE子句）
  3. 排序查询（ORDER BY）
  4. 分组查询（GROUP BY）
  5. 聚合函数查询（COUNT, SUM, AVG等）
  6. 连接查询（INNER JOIN, LEFT JOIN等）
  7. 子查询
  8. 复合查询（多表关联）
  9. 性能测试查询
  10. 错误处理测试

请以JSON格式返回测试用例列表：

```json
[
  {{
    "title": "测试用例1标题",
    "module": "所属模块",
    "priority": "优先级",
    "preconditions": "详细的前置条件，必须包括：数据库连接信息(主机:端口/数据库名)、用户权限要求、环境初始状态、依赖条件",
    "test_steps": [
      {{
        "step_number": 1,
        "action": "环境检查和数据准备 - 创建测试表并插入数据",
        "expected_result": "数据库连接成功，表创建成功，X条测试数据插入成功，验证查询返回X条记录",
        "test_data": "CREATE TABLE test_table (id SERIAL PRIMARY KEY, name VARCHAR(50)); INSERT INTO test_table (name) VALUES ('test1'), ('test2'), ('test3');"
      }},
      {{
        "step_number": 2,
        "action": "执行主要操作 - [具体操作描述，不包含SQL语句]",
        "expected_result": "具体的预期结果，包括返回记录数、关键字段值、影响行数等",
        "test_data": "SELECT * FROM test_table WHERE name LIKE 'test%';"
      }},
      {{
        "step_number": 3,
        "action": "验证操作结果 - 检查操作是否成功",
        "expected_result": "验证查询返回具体结果，确认操作成功",
        "test_data": "SELECT COUNT(*) FROM test_table;"
      }},
      {{
        "step_number": 4,
        "action": "环境清理 - 删除测试数据和表",
        "expected_result": "测试数据清理成功，数据库恢复到初始状态",
        "test_data": "DELETE FROM test_table; DROP TABLE test_table;"
      }}
    ],
    "expected_result": "整体预期结果",
    "test_data": {{}},
    "tags": ["标签1", "标签2"]
  }}
]
```

确保每个测试用例都有：
- 明确的测试目标和业务场景
- 详细具体的前置条件（包括数据库连接信息、权限要求、环境状态）
- 完整的数据准备步骤（第一步必须是环境检查和数据准备）
- **重要格式要求**：
  * action字段只包含简洁的操作描述，不包含SQL语句
  * 所有SQL语句必须放在test_data字段中
  * 测试数据（test_data字段）中不得包含任何注释（如--、#、/* ... */、//等），无论是MySQL、PostgreSQL、Oracle、MongoDB、GaussDB等数据库类型，均需保证测试数据无注释。
  * SQL语句必须完整、可执行，不能省略任何部分
  * 多个SQL语句用分号分隔
- 详细明确的预期结果（具体的数值、记录数、字段值）
- 完整的测试数据和表结构定义（字段类型、约束、索引）
- 结果验证步骤（验证操作是否成功）
- 环境清理步骤（最后一步必须是清理数据和恢复环境）
- 每个步骤的test_data字段必须包含相关的具体SQL语句，不能为空
"""
        )

        # 创建批量生成链
        self.batch_generation_chain = LLMChain(
            llm=self.llm,
            prompt=self.batch_generation_template
        )

    async def generate_batch_test_cases(self, requirement: str, database_type: str = "mysql", database_version: str = "", operation_type: str = "查询") -> List[Dict[str, Any]]:
        """批量生成测试用例"""
        try:
            logger.info(f"批量生成测试用例 - 需求: {requirement}")

            # 构建完整的数据库描述
            db_description = database_type
            if database_version:
                db_description = f"{database_type} {database_version}"

            result = await self._run_batch_chain_async(
                requirement=requirement,
                database_type=db_description,
                operation_type=operation_type
            )

            logger.info(f"AI返回的原始结果: {result[:500]}...")  # 记录前500个字符

            # 尝试解析JSON结果
            if isinstance(result, str):
                # 提取JSON部分
                import re
                import json

                json_str = None

                # 首先尝试提取JSON代码块
                json_match = re.search(r'```json\s*(.*?)\s*```', result, re.DOTALL)
                if json_match:
                    json_str = json_match.group(1)
                    logger.info("成功提取JSON代码块")
                else:
                    # 如果没有代码块，尝试查找JSON数组
                    json_array_match = re.search(r'(\[.*\])', result, re.DOTALL)
                    if json_array_match:
                        json_str = json_array_match.group(1)
                        logger.info("成功提取JSON数组")
                    else:
                        # 最后尝试使用原始结果
                        json_str = result.strip()
                        logger.info("未找到JSON代码块或数组，使用原始结果")

                try:
                    test_cases = json.loads(json_str)
                    if isinstance(test_cases, list):
                        # 为每个测试用例添加默认值并标准化格式
                        for test_case in test_cases:
                            test_case.setdefault("author", "AI生成")
                            test_case.setdefault("review_status", "pending")
                            test_case.setdefault("estimated_time", 30)
                            test_case.setdefault("test_environment", "测试环境")
                            test_case.setdefault("database_type", database_type.lower())
                            test_case.setdefault("automation_level", "manual")

                            # 标准化测试用例格式
                            test_case = self.standardize_test_case_format(test_case)

                        logger.info(f"批量生成测试用例成功，共生成 {len(test_cases)} 个用例")
                        return test_cases
                    else:
                        logger.error(f"解析结果不是列表格式: {type(test_cases)}")
                        raise Exception(f"AI返回的结果格式错误，期望列表但得到: {type(test_cases)}")
                except json.JSONDecodeError as e:
                    logger.error(f"JSON解析失败: {e}")
                    logger.error(f"尝试解析的JSON字符串: {json_str[:500]}...")

                    # 尝试修复常见的JSON格式错误
                    try:
                        fixed_json_str = self._fix_json_format(json_str)
                        logger.info("尝试修复JSON格式错误...")
                        test_cases = json.loads(fixed_json_str)

                        if isinstance(test_cases, list):
                            # 为每个测试用例添加默认值
                            for test_case in test_cases:
                                test_case.setdefault("author", "AI生成")
                                test_case.setdefault("review_status", "pending")
                                test_case.setdefault("estimated_time", 30)
                                test_case.setdefault("test_environment", "测试环境")
                                test_case.setdefault("database_type", database_type.lower())
                                test_case.setdefault("automation_level", "manual")

                            logger.info(f"JSON修复成功，批量生成测试用例成功，共生成 {len(test_cases)} 个用例")
                            return test_cases
                        else:
                            logger.error(f"修复后的结果不是列表格式: {type(test_cases)}")
                            raise Exception(f"AI返回的结果格式错误，期望列表但得到: {type(test_cases)}")
                    except Exception as fix_error:
                        logger.error(f"JSON修复也失败: {fix_error}")

                        # 尝试更智能的修复方法
                        try:
                            smart_fixed_json = self._smart_fix_json(json_str)
                            logger.info("尝试智能修复JSON格式错误...")
                            test_cases = json.loads(smart_fixed_json)

                            if isinstance(test_cases, list):
                                # 为每个测试用例添加默认值
                                for test_case in test_cases:
                                    test_case.setdefault("author", "AI生成")
                                    test_case.setdefault("review_status", "pending")
                                    test_case.setdefault("estimated_time", 30)
                                    test_case.setdefault("test_environment", "测试环境")
                                    test_case.setdefault("database_type", database_type.lower())
                                    test_case.setdefault("automation_level", "manual")

                                logger.info(f"智能JSON修复成功，批量生成测试用例成功，共生成 {len(test_cases)} 个用例")
                                return test_cases
                            else:
                                logger.error(f"智能修复后的结果不是列表格式: {type(test_cases)}")
                                raise Exception(f"AI返回的结果格式错误，期望列表但得到: {type(test_cases)}")
                        except Exception as smart_fix_error:
                            logger.error(f"智能JSON修复也失败: {smart_fix_error}")
                            raise Exception(f"AI返回的JSON格式错误: {str(e)}")
            else:
                logger.error(f"AI返回的结果不是字符串格式: {type(result)}")
                raise Exception(f"AI返回的结果格式错误，期望字符串但得到: {type(result)}")

        except Exception as e:
            logger.error(f"批量生成测试用例失败: {e}")
            raise  # 重新抛出异常，而不是返回空列表

    def standardize_test_case_format(self, test_case: Dict[str, Any]) -> Dict[str, Any]:
        """标准化测试用例格式，确保action和test_data字段符合规范"""
        try:
            logger.info(f"开始标准化测试用例格式: {test_case.get('title', '未知标题')}")

            # 深拷贝测试用例以避免修改原始数据
            import copy
            standardized_case = copy.deepcopy(test_case)

            # 处理测试步骤
            if 'test_steps' in standardized_case and isinstance(standardized_case['test_steps'], list):
                for step in standardized_case['test_steps']:
                    if isinstance(step, dict):
                        self._standardize_test_step(step)

            logger.info(f"测试用例格式标准化完成: {standardized_case.get('title', '未知标题')}")
            return standardized_case

        except Exception as e:
            logger.error(f"测试用例格式标准化失败: {e}")
            return test_case  # 返回原始测试用例

    def _standardize_test_step(self, step: Dict[str, Any]) -> None:
        """标准化单个测试步骤的格式"""
        action = step.get('action', '')
        test_data = step.get('test_data', '')

        # 检查action中是否包含SQL语句
        sql_in_action = self._extract_sql_from_action(action)

        if sql_in_action:
            # 如果action中包含SQL，需要重新分配
            if not test_data or not self._contains_sql(test_data):
                # test_data为空或不包含SQL，将SQL从action移到test_data
                step['test_data'] = '; '.join(sql_in_action)
                step['action'] = self._clean_action_from_sql(action, sql_in_action)
                logger.info(f"已将SQL从action移动到test_data: {step['action']}")
            else:
                # test_data已有内容，只清理action中的SQL
                step['action'] = self._clean_action_from_sql(action, sql_in_action)
                logger.info(f"已清理action中的SQL语句: {step['action']}")

        # 确保test_data包含有效的SQL或描述
        if not step.get('test_data'):
            step['test_data'] = ""

    def _extract_sql_from_action(self, action: str) -> List[str]:
        """从action字段中提取SQL语句"""
        if not action:
            return []

        sql_statements = []

        # SQL关键词模式
        sql_patterns = [
            r'(CREATE\s+TABLE\s+[^;]+(?:;|$))',
            r'(INSERT\s+INTO\s+[^;]+(?:;|$))',
            r'(SELECT\s+[^;]+(?:;|$))',
            r'(UPDATE\s+[^;]+(?:;|$))',
            r'(DELETE\s+FROM\s+[^;]+(?:;|$))',
            r'(DROP\s+TABLE\s+[^;]+(?:;|$))',
            r'(ALTER\s+TABLE\s+[^;]+(?:;|$))',
        ]

        import re
        for pattern in sql_patterns:
            matches = re.findall(pattern, action, re.IGNORECASE | re.DOTALL)
            for match in matches:
                sql_statements.append(match.strip().rstrip(';'))

        return sql_statements

    def _contains_sql(self, text: str) -> bool:
        """检查文本是否包含SQL语句"""
        if not text:
            return False

        text_upper = text.upper().strip()
        sql_keywords = ['SELECT', 'INSERT', 'UPDATE', 'DELETE', 'CREATE', 'DROP', 'ALTER']

        return any(keyword in text_upper for keyword in sql_keywords)

    def _clean_action_from_sql(self, action: str, sql_statements: List[str]) -> str:
        """从action中清理SQL语句，保留操作描述"""
        cleaned_action = action

        # 移除SQL语句
        for sql in sql_statements:
            cleaned_action = cleaned_action.replace(sql, '').replace(sql + ';', '')

        # 清理多余的空格和符号
        import re
        cleaned_action = re.sub(r'\s+', ' ', cleaned_action)
        cleaned_action = re.sub(r'\s*-\s*$', '', cleaned_action)  # 移除末尾的 " - "
        cleaned_action = cleaned_action.strip()

        # 如果清理后为空，生成默认描述
        if not cleaned_action:
            if sql_statements:
                first_sql = sql_statements[0].upper()
                if first_sql.startswith('CREATE'):
                    cleaned_action = "创建数据库对象"
                elif first_sql.startswith('INSERT'):
                    cleaned_action = "插入测试数据"
                elif first_sql.startswith('SELECT'):
                    cleaned_action = "执行查询操作"
                elif first_sql.startswith('UPDATE'):
                    cleaned_action = "更新数据"
                elif first_sql.startswith('DELETE'):
                    cleaned_action = "删除数据"
                elif first_sql.startswith('DROP'):
                    cleaned_action = "删除数据库对象"
                else:
                    cleaned_action = "执行数据库操作"

        return cleaned_action

    def _fix_json_format(self, json_str: str) -> str:
        """修复常见的JSON格式错误"""
        try:
            # 移除多余的空白字符
            json_str = json_str.strip()

            # 修复常见的逗号问题
            import re

            # 1. 修复缺少逗号的问题（在}后面跟着{的情况）
            json_str = re.sub(r'}\s*{', '},{', json_str)

            # 2. 修复缺少逗号的问题（在]后面跟着{的情况）
            json_str = re.sub(r']\s*{', '],{', json_str)

            # 3. 修复缺少逗号的问题（在}后面跟着"的情况）
            json_str = re.sub(r'}\s*"', '},"', json_str)

            # 4. 修复缺少逗号的问题（在"后面跟着"的情况，但不在值内部）
            json_str = re.sub(r'"\s*"([^:]*?):', '","\1:', json_str)

            # 5. 修复缺少逗号的问题（在数字后面跟着"的情况）
            json_str = re.sub(r'(\d)\s*"', r'\1,"', json_str)

            # 6. 修复缺少逗号的问题（在]后面跟着"的情况）
            json_str = re.sub(r']\s*"', '],"', json_str)

            # 7. 修复多余的逗号问题（在}或]前面的逗号）
            json_str = re.sub(r',\s*}', '}', json_str)
            json_str = re.sub(r',\s*]', ']', json_str)

            # 8. 修复引号问题（将单引号替换为双引号）
            json_str = re.sub(r"'([^']*)':", r'"\1":', json_str)
            json_str = re.sub(r":\s*'([^']*)'", r': "\1"', json_str)

            # 9. 修复布尔值和null值的大小写问题
            json_str = re.sub(r'\bTrue\b', 'true', json_str)
            json_str = re.sub(r'\bFalse\b', 'false', json_str)
            json_str = re.sub(r'\bNone\b', 'null', json_str)

            # 10. 修复数组中缺少逗号的问题（更精确的匹配）
            json_str = re.sub(r'"\s+"([^:]*?):', '", "\1:', json_str)

            # 11. 修复对象属性之间缺少逗号的问题
            json_str = re.sub(r'(["}])\s*"([^"]*?)"\s*:', r'\1, "\2":', json_str)

            logger.info(f"JSON修复后的前500个字符: {json_str[:500]}...")
            return json_str

        except Exception as e:
            logger.error(f"JSON修复过程中出错: {e}")
            return json_str

    def _smart_fix_json(self, json_str: str) -> str:
        """智能修复JSON格式错误，逐行分析并修复"""
        try:
            lines = json_str.split('\n')
            fixed_lines = []
            in_string = False
            escape_next = False
            brace_stack = []

            for i, line in enumerate(lines):
                fixed_line = line

                # 跟踪字符串状态和括号匹配
                for j, char in enumerate(line):
                    if escape_next:
                        escape_next = False
                        continue

                    if char == '\\':
                        escape_next = True
                        continue

                    if char == '"' and not escape_next:
                        in_string = not in_string

                    if not in_string:
                        if char in '{[':
                            brace_stack.append(char)
                        elif char in '}]':
                            if brace_stack:
                                brace_stack.pop()

                # 检查是否需要在行末添加逗号
                if not in_string and i < len(lines) - 1:
                    stripped = fixed_line.strip()
                    next_line_stripped = lines[i + 1].strip() if i + 1 < len(lines) else ""

                    # 如果当前行以 } 或 ] 或 " 结尾，下一行以 { 或 " 开头，可能需要逗号
                    if (stripped.endswith(('}', ']', '"')) and
                        next_line_stripped.startswith(('{', '"')) and
                        not stripped.endswith(',') and
                        stripped != ']' and stripped != '}'):

                        # 检查是否在数组或对象内部
                        if brace_stack and (brace_stack[-1] == '[' or brace_stack[-1] == '{'):
                            fixed_line = fixed_line.rstrip() + ','

                fixed_lines.append(fixed_line)

            result = '\n'.join(fixed_lines)
            logger.info(f"智能JSON修复后的前500个字符: {result[:500]}...")
            return result

        except Exception as e:
            logger.error(f"智能JSON修复过程中出错: {e}")
            return json_str

    async def _run_batch_chain_async(self, **kwargs) -> str:
        """异步运行批量生成链"""
        import asyncio
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.batch_generation_chain.run, kwargs)

class TestCaseReviewAgent:
    """测试用例评审Agent"""

    def __init__(self, llm: DeepSeekLLM):
        self.llm = llm

        # 测试用例评审提示词模板
        self.review_template = PromptTemplate(
            input_variables=["test_case_content", "database_type"],
            template="""
你是一个资深的软件测试专家和质量保证工程师，专门负责测试用例的评审工作。

请对以下测试用例进行全面评审：

测试用例内容:
{test_case_content}

数据库类型: {database_type}

请从以下维度进行评审：

1. **完整性评审**：
   - 测试用例是否包含所有必要的元素（标题、步骤、预期结果等）
   - 测试步骤是否完整，是否遗漏关键步骤
   - 前置条件是否明确

2. **前置条件和数据准备评审**（重点关注）：
   - 前置条件是否详细具体：数据库连接信息、用户权限、环境状态、依赖条件
   - 对于删除操作：是否提供了完整的CREATE TABLE语句，是否提供了具体的INSERT语句和测试数据内容
   - 对于更新操作：是否提供了完整的表结构，是否提供了原始数据和更新前后的对比
   - 对于插入操作：是否提供了完整的表结构定义（字段类型、约束、索引），是否考虑了外键关联
   - 对于查询操作：是否提供了足够的测试数据，数据是否覆盖了查询的各种场景
   - 是否包含了环境检查步骤，确保测试前的状态符合预期
   - 是否包含了结果验证步骤，确保操作结果的正确性
   - 是否包含了环境清理步骤，确保测试后环境的干净
   - 测试数据是否覆盖了各种场景（正常数据、边界数据、空值、重复数据等）
   - 每个步骤的预期结果是否具体明确，不能使用模糊描述

3. **准确性评审**：
   - SQL语句语法是否正确
   - 测试步骤是否符合{database_type}数据库的特性
   - 预期结果是否合理
   - 表结构定义是否完整和正确

4. **可执行性评审**：
   - 测试步骤是否清晰明确，便于执行
   - 测试数据是否充分
   - 是否考虑了异常情况
   - 步骤顺序是否合理（数据准备 → 主要测试 → 结果验证 → 数据清理）

5. **覆盖性评审**：
   - 是否覆盖了主要的测试场景
   - 是否考虑了边界条件
   - 是否包含负面测试

6. **规范性评审**：
   - 用例描述是否规范
   - 优先级设置是否合理
   - 标签分类是否准确

**特别关注数据库操作的逻辑性**：
- 删除和更新操作必须确保目标数据存在
- 插入操作必须确保目标表存在
- 查询操作必须确保有数据可查
- 所有操作都应该有相应的验证步骤

请提供评审结果，包括：
- 总体评分（1-10分）
- 具体问题和改进建议
- 是否通过评审（通过/需要修改/不通过）

评审结果格式：
评分: [1-10分]
评审状态: [通过/需要修改/不通过]
问题和建议:
1. [具体问题1及改进建议]
2. [具体问题2及改进建议]
...
总结: [总体评价和建议]
"""
        )

        # 创建评审链
        self.review_chain = LLMChain(
            llm=self.llm,
            prompt=self.review_template
        )

    async def review_test_case(self, test_case: Dict[str, Any]) -> Dict[str, Any]:
        """评审测试用例"""
        try:
            logger.info(f"评审测试用例: {test_case.get('title', '未知标题')}")

            # 格式化测试用例内容
            test_case_content = self._format_test_case_for_review(test_case)
            database_type = test_case.get('database_type', 'mysql')

            review_result = await self._run_review_chain_async(
                test_case_content=test_case_content,
                database_type=database_type
            )

            # 解析评审结果
            parsed_result = self._parse_review_result(review_result)

            logger.info(f"测试用例评审完成: {parsed_result.get('status', '未知状态')}")
            return parsed_result

        except Exception as e:
            logger.error(f"评审测试用例失败: {e}")
            return {
                "score": 5,
                "status": "需要修改",
                "issues": ["评审过程中出现错误，请检查测试用例格式"],
                "summary": f"评审失败: {str(e)}"
            }

    def _format_test_case_for_review(self, test_case: Dict[str, Any]) -> str:
        """格式化测试用例内容用于评审"""
        content = f"""
标题: {test_case.get('title', '')}
模块: {test_case.get('module', '')}
优先级: {test_case.get('priority', '')}
前置条件: {test_case.get('preconditions', '')}

测试步骤:
"""

        for i, step in enumerate(test_case.get('test_steps', []), 1):
            if isinstance(step, dict):
                content += f"{i}. {step.get('action', '')}\n"
                if step.get('expected_result'):
                    content += f"   预期结果: {step.get('expected_result')}\n"
            else:
                content += f"{i}. {step}\n"

        content += f"\n整体预期结果: {test_case.get('expected_result', '')}"
        content += f"\n测试数据: {test_case.get('test_data', {})}"
        content += f"\n标签: {test_case.get('tags', [])}"

        return content

    def _parse_review_result(self, review_text: str) -> Dict[str, Any]:
        """解析评审结果"""
        try:
            lines = review_text.strip().split('\n')
            result = {
                "score": 7,
                "status": "需要修改",
                "issues": [],
                "summary": ""
            }

            current_section = None

            for line in lines:
                line = line.strip()
                if not line:
                    continue

                if line.startswith("评分:"):
                    try:
                        score_text = line.split(":", 1)[1].strip()
                        score = int(''.join(filter(str.isdigit, score_text)))
                        result["score"] = max(1, min(10, score))
                    except Exception as e:

                        logger.error(f"操作失败: {type(e).__name__}: {str(e)}")

                        logger.error(f"异常堆栈跟踪: {traceback.format_exc()}")
                elif line.startswith("评审状态:"):
                    status = line.split(":", 1)[1].strip()
                    if status in ["通过", "需要修改", "不通过"]:
                        result["status"] = status
                elif line.startswith("问题和建议:"):
                    current_section = "issues"
                elif line.startswith("总结:"):
                    current_section = "summary"
                    result["summary"] = line.split(":", 1)[1].strip()
                elif current_section == "issues" and (line.startswith(("1.", "2.", "3.", "4.", "5.", "-", "•"))):
                    issue = line.split(".", 1)[1].strip() if "." in line else line.lstrip("-•").strip()
                    result["issues"].append(issue)
                elif current_section == "summary":
                    result["summary"] += " " + line

            return result

        except Exception as e:
            logger.error(f"解析评审结果失败: {e}")
            return {
                "score": 5,
                "status": "需要修改",
                "issues": ["评审结果解析失败"],
                "summary": "评审结果格式错误"
            }

    async def _run_review_chain_async(self, **kwargs) -> str:
        """异步运行评审链"""
        import asyncio
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.review_chain.run, kwargs)

class TestCaseRegenerationAgent:
    """测试用例重新生成Agent"""

    def __init__(self, llm: DeepSeekLLM):
        self.llm = llm
        self.parser = TestCaseOutputParser()

        # 重新生成提示词模板
        self.regeneration_template = PromptTemplate(
            input_variables=["original_test_case", "review_feedback", "database_type"],
            template="""
你是一个专业的软件测试专家，需要根据评审反馈改进测试用例。

原始测试用例:
{original_test_case}

评审反馈:
{review_feedback}

数据库类型: {database_type}

请根据评审反馈，重新生成改进后的测试用例。需要：

1. 解决评审中提出的所有问题
2. 保持测试用例的核心目标不变
3. 提高测试用例的质量和可执行性
4. 确保符合{database_type}数据库的特性
5. 增强测试覆盖度

**重要：数据库操作前置条件要求**
在改进测试用例时，必须严格遵循以下数据准备原则：

1. **删除操作测试**：
   - 必须先创建目标表（如果不存在）
   - 必须先插入要删除的测试数据
   - 确保要删除的数据确实存在于表中

2. **更新操作测试**：
   - 必须先创建目标表（如果不存在）
   - 必须先插入要更新的测试数据
   - 确保要更新的记录确实存在于表中
   - 提供更新前后的数据对比

3. **插入操作测试**：
   - 必须先创建目标表（如果不存在）
   - 必须提供完整的表结构定义（包含字段类型、约束等）
   - 如果涉及外键约束，需要先准备关联表的数据

4. **查询操作测试**：
   - 必须先创建目标表（如果不存在）
   - 必须先插入足够的测试数据以验证查询结果
   - 数据应该覆盖各种查询场景（正常数据、边界数据、空值等）

5. **表结构操作测试**：
   - 创建表前需要检查表是否已存在
   - 删除表前需要确保表存在
   - 修改表结构前需要确保表和相关字段存在

重点改进方向：
- 完善测试步骤的详细程度，确保包含完整的数据准备步骤
- 优化SQL语句的准确性
- 增加边界条件和异常情况的测试
- 改进测试数据的设计，确保数据的完整性和一致性
- 提高预期结果的明确性
- 确保测试步骤的逻辑顺序：数据准备 → 主要测试 → 结果验证 → 数据清理

请以结构化的格式返回改进后的测试用例：

标题: [改进后的测试用例标题]
模块: [所属模块]
优先级: [优先级]
前置条件: [详细的前置条件，必须包括：数据库连接信息(主机:端口/数据库名)、用户权限要求、环境初始状态、依赖条件]
测试步骤:
1. [环境检查和数据准备步骤 - 检查数据库连接，创建表结构(提供完整CREATE TABLE语句)，插入测试数据(提供完整INSERT语句和具体数据内容)，验证数据准备成功]
2. [主要测试步骤 - 执行具体的SQL语句(提供完整语句)，说明语句作用和预期行为]
3. [结果验证步骤 - 执行验证查询(提供完整SQL语句)，检查操作结果是否符合预期]
4. [环境清理步骤 - 删除测试数据(提供完整DELETE/DROP语句)，恢复数据库到测试前状态，验证清理成功]
...
预期结果: [明确的整体预期结果，包括具体的数值、记录数、字段值]
改进说明: [说明本次改进解决了哪些问题，特别是前置条件、数据准备、结果验证方面的改进]
"""
        )

        # 创建重新生成链
        self.regeneration_chain = LLMChain(
            llm=self.llm,
            prompt=self.regeneration_template,
            output_parser=self.parser
        )

    async def regenerate_test_case(self, original_test_case: Dict[str, Any], review_feedback: Dict[str, Any]) -> Dict[str, Any]:
        """根据评审反馈重新生成测试用例"""
        try:
            logger.info(f"重新生成测试用例: {original_test_case.get('title', '未知标题')}")

            # 格式化原始测试用例
            original_content = self._format_original_test_case(original_test_case)

            # 格式化评审反馈
            feedback_content = self._format_review_feedback(review_feedback)

            database_type = original_test_case.get('database_type', 'mysql')

            result = await self._run_regeneration_chain_async(
                original_test_case=original_content,
                review_feedback=feedback_content,
                database_type=database_type
            )

            # 确保返回的数据格式正确
            if isinstance(result, dict):
                # 保持原有的一些属性
                result.setdefault("author", original_test_case.get("author", "AI生成"))
                result.setdefault("review_status", "pending")
                result.setdefault("estimated_time", original_test_case.get("estimated_time", 30))
                result.setdefault("test_environment", original_test_case.get("test_environment", "测试环境"))
                result.setdefault("database_type", database_type)

                # 确保test_steps格式正确
                if "test_steps" in result and isinstance(result["test_steps"], list):
                    for i, step in enumerate(result["test_steps"]):
                        if isinstance(step, dict):
                            step.setdefault("step_number", i + 1)
                            step.setdefault("expected_result", "操作成功")
                            step.setdefault("test_data", "")

                logger.info(f"测试用例重新生成成功: {result.get('title', '未知标题')}")
                return result
            else:
                logger.error(f"重新生成结果格式错误: {type(result)}")
                return original_test_case

        except Exception as e:
            logger.error(f"重新生成测试用例失败: {e}")
            return original_test_case

    def _format_original_test_case(self, test_case: Dict[str, Any]) -> str:
        """格式化原始测试用例"""
        content = f"""
标题: {test_case.get('title', '')}
模块: {test_case.get('module', '')}
优先级: {test_case.get('priority', '')}
前置条件: {test_case.get('preconditions', '')}

测试步骤:
"""

        for i, step in enumerate(test_case.get('test_steps', []), 1):
            if isinstance(step, dict):
                content += f"{i}. {step.get('action', '')}\n"
                if step.get('expected_result'):
                    content += f"   预期结果: {step.get('expected_result')}\n"
            else:
                content += f"{i}. {step}\n"

        content += f"\n整体预期结果: {test_case.get('expected_result', '')}"
        content += f"\n测试数据: {test_case.get('test_data', {})}"

        return content

    def _format_review_feedback(self, feedback: Dict[str, Any]) -> str:
        """格式化评审反馈"""
        content = f"""
评审评分: {feedback.get('score', 0)}/10
评审状态: {feedback.get('status', '')}

具体问题和建议:
"""

        for i, issue in enumerate(feedback.get('issues', []), 1):
            content += f"{i}. {issue}\n"

        content += f"\n总结: {feedback.get('summary', '')}"

        return content

    async def _run_regeneration_chain_async(self, **kwargs) -> Dict[str, Any]:
        """异步运行重新生成链"""
        import asyncio
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.regeneration_chain.run, kwargs)

class TestCaseAIService:
    """测试用例AI生成服务主类"""

    def __init__(self):
        """初始化AI服务"""
        self.llm = DeepSeekLLM()
        self.generation_agent = TestCaseGenerationAgent(self.llm)
        self.batch_generation_agent = BatchTestCaseGenerationAgent(self.llm)
        self.review_agent = TestCaseReviewAgent(self.llm)
        self.regeneration_agent = TestCaseRegenerationAgent(self.llm)
        logger.info("测试用例AI生成服务初始化完成")

    async def generate_test_case(self, requirement: str, database_type: str = "mysql", operation_type: str = "查询") -> Dict[str, Any]:
        """生成测试用例"""
        try:
            logger.info(f"开始生成测试用例 - 需求: {requirement}")

            # 检查是否需要批量生成
            if self._is_batch_request(requirement):
                logger.info("检测到批量生成需求，使用批量生成Agent")
                test_cases = await self.batch_generation_agent.generate_batch_test_cases(
                    requirement=requirement,
                    database_type=database_type,
                    operation_type=operation_type
                )

                if test_cases:
                    return {
                        "success": True,
                        "test_cases": test_cases,
                        "is_batch": True,
                        "message": f"批量生成成功，共生成 {len(test_cases)} 个测试用例"
                    }
                else:
                    # 批量生成失败，回退到单个生成
                    logger.warning("批量生成失败，回退到单个生成")

            # 使用单个生成Agent生成测试用例
            test_case = await self.generation_agent.generate_test_case(
                requirement=requirement,
                database_type=database_type,
                operation_type=operation_type
            )

            if test_case:
                return {
                    "success": True,
                    "test_case": test_case,
                    "is_batch": False,
                    "message": "测试用例生成成功"
                }
            else:
                return {
                    "success": False,
                    "test_case": None,
                    "is_batch": False,
                    "message": "测试用例生成失败，返回结果为空"
                }

        except Exception as e:
            logger.error(f"生成测试用例失败: {e}")
            return {
                "success": False,
                "test_case": None,
                "is_batch": False,
                "message": f"生成失败: {str(e)}"
            }

    def _is_batch_request(self, requirement: str) -> bool:
        """判断是否为批量生成请求"""
        batch_keywords = [
            "多个", "批量", "几个", "一些", "多种", "各种",
            "10个", "5个", "3个", "若干", "系列", "套"
        ]
        requirement_lower = requirement.lower()
        return any(keyword in requirement_lower for keyword in batch_keywords)

    async def review_test_case(self, test_case: Dict[str, Any]) -> Dict[str, Any]:
        """评审测试用例"""
        try:
            logger.info(f"开始评审测试用例: {test_case.get('title', '未知标题')}")

            # 使用评审Agent评审测试用例
            review_result = await self.review_agent.review_test_case(test_case)

            return {
                "success": True,
                "review_result": review_result,
                "message": "测试用例评审完成"
            }

        except Exception as e:
            logger.error(f"评审测试用例失败: {e}")
            return {
                "success": False,
                "review_result": None,
                "message": f"评审失败: {str(e)}"
            }

    async def regenerate_test_case(self, original_test_case: Dict[str, Any], review_feedback: Dict[str, Any]) -> Dict[str, Any]:
        """根据评审反馈重新生成测试用例"""
        try:
            logger.info(f"开始重新生成测试用例: {original_test_case.get('title', '未知标题')}")

            # 使用重新生成Agent改进测试用例
            improved_test_case = await self.regeneration_agent.regenerate_test_case(
                original_test_case=original_test_case,
                review_feedback=review_feedback
            )

            return {
                "success": True,
                "test_case": improved_test_case,
                "message": "测试用例重新生成成功"
            }

        except Exception as e:
            logger.error(f"重新生成测试用例失败: {e}")
            return {
                "success": False,
                "test_case": None,
                "message": f"重新生成失败: {str(e)}"
            }

    async def generate_and_review_workflow(self, requirement: str, database_type: str = "mysql", operation_type: str = "查询") -> Dict[str, Any]:
        """完整的生成和评审工作流"""
        try:
            logger.info(f"开始完整工作流 - 需求: {requirement}")

            # 第一步：生成测试用例
            generation_result = await self.generate_test_case(requirement, database_type, operation_type)
            if not generation_result["success"]:
                return generation_result

            test_case = generation_result["test_case"]

            # 第二步：评审测试用例
            review_result = await self.review_test_case(test_case)
            if not review_result["success"]:
                return {
                    "success": True,
                    "test_case": test_case,
                    "review_result": None,
                    "improved_test_case": None,
                    "message": "测试用例生成成功，但评审失败"
                }

            review_feedback = review_result["review_result"]

            # 第三步：如果评审不通过，尝试重新生成
            improved_test_case = None
            if review_feedback.get("status") in ["需要修改", "不通过"]:
                regeneration_result = await self.regenerate_test_case(test_case, review_feedback)
                if regeneration_result["success"]:
                    improved_test_case = regeneration_result["test_case"]

            return {
                "success": True,
                "test_case": test_case,
                "review_result": review_feedback,
                "improved_test_case": improved_test_case,
                "message": "完整工作流执行成功"
            }

        except Exception as e:
            logger.error(f"完整工作流执行失败: {e}")
            return {
                "success": False,
                "test_case": None,
                "review_result": None,
                "improved_test_case": None,
                "message": f"工作流执行失败: {str(e)}"
            }

# 创建全局服务实例
test_case_ai_service = TestCaseAIService()

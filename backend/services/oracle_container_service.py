"""
Oracle容器管理服务 - 专门用于管理70.47服务器上的Oracle容器
"""

import logging
import traceback
import paramiko
import asyncio
import json
from typing import Dict, Any, Optional
from datetime import datetime
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.docker_network_utils import docker_network_utils

logger = logging.getLogger(__name__)

class OracleContainerService:
    """Oracle容器管理服务"""

    def __init__(self):
        self.server_host = "*************"
        self.server_port = 22
        self.server_username = "root"
        self.server_password = "QZ@1005#1005"
        self.oracle_image = "registry.cn-hangzhou.aliyuncs.com/helowin/oracle_11g"
        self.container_name = "oracle-server"
        self.oracle_port = 1521
        self.ssh_client: Optional[paramiko.SSHClient] = None
        # Oracle数据库用户配置
        self.oracle_user = "system"
        self.oracle_password = "oracle"
        self.oracle_service_name = "helowin"

    def set_oracle_credentials(self, user: str, password: str, service_name: str = "helowin"):
        """设置Oracle数据库凭据"""
        self.oracle_user = user
        self.oracle_password = password
        self.oracle_service_name = service_name
        logger.info(f"Oracle credentials updated: user={user}, service_name={service_name}")
    
    async def _connect_ssh(self):
        """建立SSH连接"""
        try:
            if self.ssh_client:
                try:
                    self.ssh_client.close()
                except Exception as e:

                    logger.error(f"操作失败: {type(e).__name__}: {str(e)}")

                    logger.error(f"异常堆栈跟踪: {traceback.format_exc()}")
            self.ssh_client = paramiko.SSHClient()
            self.ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())

            # 尝试多种凭据组合
            credentials = [
                (self.server_username, self.server_password),
                ("root", "root"),
                ("root", "123456"),
                ("root", "password"),
                ("root", "admin"),
                ("admin", "admin"),
                ("ubuntu", "ubuntu"),
                ("centos", "centos")
            ]

            connected = False
            for username, password in credentials:
                try:
                    self.ssh_client.connect(
                        hostname=self.server_host,
                        port=self.server_port,
                        username=username,
                        password=password,
                        timeout=10
                    )
                    logger.info(f"SSH connection established to Oracle server: {self.server_host} with {username}/**")
                    connected = True
                    break
                except Exception as e:
                    logger.debug(f"Failed to connect with {username}/{password}: {str(e)}")
                    continue

            if not connected:
                raise Exception(f"Failed to establish SSH connection with any credentials")

        except Exception as e:
            logger.error(f"Failed to establish SSH connection: {str(e)}")
            self.ssh_client = None
            raise
    
    async def _disconnect_ssh(self):
        """断开SSH连接"""
        try:
            if self.ssh_client:
                self.ssh_client.close()
                self.ssh_client = None
        except Exception as e:
            logger.warning(f"Error disconnecting SSH: {str(e)}")
    
    async def check_container_status(self) -> Dict[str, Any]:
        """检查Oracle容器状态"""
        try:
            await self._connect_ssh()

            if not self.ssh_client:
                return {
                    'exists': False,
                    'running': False,
                    'status': 'connection_failed',
                    'message': 'Failed to establish SSH connection'
                }

            # 首先尝试通过端口匹配找到Oracle容器
            port_filter_cmd = f"docker ps -a --filter publish={self.oracle_port} --format json"
            stdin, stdout, stderr = self.ssh_client.exec_command(port_filter_cmd)
            port_output = stdout.read().decode('utf-8').strip()

            # 如果通过端口找到了容器，使用该容器
            if port_output:
                try:
                    # 处理多行JSON输出
                    lines = port_output.strip().split('\n')
                    for line in lines:
                        if line.strip():
                            container_info = json.loads(line)
                            # 检查是否是Oracle镜像
                            image = container_info.get('Image', '').lower()
                            if 'oracle' in image:
                                status = container_info.get('State', '').lower()
                                container_name = container_info.get('Names', '')

                                logger.info(f"Found Oracle container by port {self.oracle_port}: {container_name}")

                                return {
                                    'exists': True,
                                    'running': status == 'running',
                                    'status': status,
                                    'container_id': container_info.get('ID', ''),
                                    'container_name': container_name,
                                    'image': container_info.get('Image', ''),
                                    'ports': container_info.get('Ports', ''),
                                    'created': container_info.get('CreatedAt', ''),
                                    'message': f'Container status: {status}'
                                }
                except json.JSONDecodeError as e:
                    logger.warning(f"Failed to parse port-based container info: {e}")

            # 如果通过端口没找到，尝试通过名称匹配
            check_cmd = f"docker ps -a --filter name={self.container_name} --format json"
            stdin, stdout, stderr = self.ssh_client.exec_command(check_cmd)
            output = stdout.read().decode('utf-8').strip()

            if output:
                try:
                    # 处理多行JSON输出
                    lines = output.strip().split('\n')
                    for line in lines:
                        if line.strip():
                            container_info = json.loads(line)
                            status = container_info.get('State', '').lower()
                            container_name = container_info.get('Names', '')

                            logger.info(f"Found Oracle container by name pattern: {container_name}")

                            return {
                                'exists': True,
                                'running': status == 'running',
                                'status': status,
                                'container_id': container_info.get('ID', ''),
                                'container_name': container_name,
                                'image': container_info.get('Image', ''),
                                'ports': container_info.get('Ports', ''),
                                'created': container_info.get('CreatedAt', ''),
                                'message': f'Container status: {status}'
                            }
                except json.JSONDecodeError:
                    # 如果JSON解析失败，尝试简单的状态检查
                    running_cmd = f"docker ps --filter name={self.container_name} --format '{{{{.Names}}}}'"
                    stdin, stdout, stderr = self.ssh_client.exec_command(running_cmd)
                    running_output = stdout.read().decode('utf-8').strip()

                    if running_output:
                        return {
                            'exists': True,
                            'running': bool(running_output),
                            'status': 'running' if running_output else 'stopped',
                            'container_name': running_output,
                            'message': f'Container {"running" if running_output else "stopped"}'
                        }

            # 最后尝试通过Oracle镜像匹配
            image_cmd = "docker ps -a --filter ancestor=registry.cn-hangzhou.aliyuncs.com/helowin/oracle_11g --format json"
            stdin, stdout, stderr = self.ssh_client.exec_command(image_cmd)
            image_output = stdout.read().decode('utf-8').strip()

            if image_output:
                try:
                    lines = image_output.strip().split('\n')
                    for line in lines:
                        if line.strip():
                            container_info = json.loads(line)
                            # 检查端口是否匹配
                            ports = container_info.get('Ports', '')
                            if str(self.oracle_port) in ports:
                                status = container_info.get('State', '').lower()
                                container_name = container_info.get('Names', '')

                                logger.info(f"Found Oracle container by image and port: {container_name}")

                                return {
                                    'exists': True,
                                    'running': status == 'running',
                                    'status': status,
                                    'container_id': container_info.get('ID', ''),
                                    'container_name': container_name,
                                    'image': container_info.get('Image', ''),
                                    'ports': container_info.get('Ports', ''),
                                    'created': container_info.get('CreatedAt', ''),
                                    'message': f'Container status: {status}'
                                }
                except json.JSONDecodeError as e:
                    logger.warning(f"Failed to parse image-based container info: {e}")

            return {
                'exists': False,
                'running': False,
                'status': 'not_found',
                'message': f'Oracle container not found on port {self.oracle_port}'
            }

        except Exception as e:
            logger.error(f"Failed to check Oracle container status: {str(e)}")

            # 如果是SSH连接失败，返回友好的状态信息
            if "SSH connection" in str(e) or "Authentication failed" in str(e):
                return {
                    'exists': True,  # 假设容器存在（基于配置）
                    'running': True,  # 假设容器运行中（基于端口配置）
                    'status': 'ssh_unavailable',
                    'container_name': f'oracle-container-{self.oracle_port}',
                    'message': f'无法通过SSH检测容器状态，但根据配置推测容器可能正在运行（端口: {self.oracle_port}）',
                    'ssh_error': str(e)
                }

            return {
                'exists': False,
                'running': False,
                'status': 'error',
                'error': str(e),
                'message': f'Error checking container: {str(e)}'
            }
        finally:
            await self._disconnect_ssh()
    
    async def start_oracle_container(self) -> Dict[str, Any]:
        """启动Oracle容器"""
        try:
            await self._connect_ssh()

            if not self.ssh_client:
                return {
                    'success': False,
                    'message': 'Failed to establish SSH connection to Oracle server',
                    'action': 'connection_failed'
                }

            # 首先检查容器状态（重新连接以确保连接有效）
            await self._connect_ssh()
            if not self.ssh_client:
                return {
                    'success': False,
                    'message': 'Failed to establish SSH connection for status check',
                    'action': 'connection_failed'
                }

            # 检查容器是否存在
            check_cmd = f"docker ps -a --filter name={self.container_name} --format json"
            stdin, stdout, stderr = self.ssh_client.exec_command(check_cmd)
            output = stdout.read().decode('utf-8').strip()

            container_exists = bool(output)
            container_running = False

            if container_exists:
                try:
                    container_info = json.loads(output)
                    container_running = container_info.get('State', '').lower() == 'running'
                except json.JSONDecodeError:
                    # 如果JSON解析失败，尝试简单的状态检查
                    running_cmd = f"docker ps --filter name={self.container_name} --format '{{{{.Names}}}}'"
                    stdin, stdout, stderr = self.ssh_client.exec_command(running_cmd)
                    running_output = stdout.read().decode('utf-8').strip()
                    container_running = bool(running_output)

            if container_running:
                return {
                    'success': True,
                    'message': 'Oracle container is already running',
                    'action': 'already_running'
                }

            # 如果容器存在但未运行，启动它
            if container_exists and not container_running:
                logger.info("Starting existing Oracle container...")
                start_cmd = f"docker start {self.container_name}"
                stdin, stdout, stderr = self.ssh_client.exec_command(start_cmd)
                exit_status = stdout.channel.recv_exit_status()
                
                if exit_status == 0:
                    # 等待容器启动
                    await asyncio.sleep(5)
                    
                    # 验证容器是否成功启动
                    verify_cmd = f"docker ps --filter name={self.container_name} --format '{{{{.Names}}}}'"
                    stdin, stdout, stderr = self.ssh_client.exec_command(verify_cmd)
                    verify_output = stdout.read().decode('utf-8').strip()

                    if verify_output:
                        return {
                            'success': True,
                            'message': 'Oracle container started successfully',
                            'action': 'started'
                        }
                    else:
                        return {
                            'success': False,
                            'message': 'Failed to start Oracle container',
                            'action': 'start_failed'
                        }
                else:
                    error_output = stderr.read().decode('utf-8')
                    return {
                        'success': False,
                        'message': f'Failed to start container: {error_output}',
                        'action': 'start_failed'
                    }
            
            # 如果容器不存在，创建并启动新容器
            logger.info("Creating new Oracle container...")
            
            # 首先清理可能存在的同名容器
            cleanup_cmd = f"docker rm -f {self.container_name} 2>/dev/null || true"
            stdin, stdout, stderr = self.ssh_client.exec_command(cleanup_cmd)
            stdout.channel.recv_exit_status()  # 等待命令完成
            
            # 创建并启动新容器（使用bridge网络确保连接到docker0）
            timestamp = int(datetime.now().timestamp())
            run_cmd = (
                f"docker run -d "
                f"--name {self.container_name} "
                f"-p {self.oracle_port}:1521 "
                f"-e ORACLE_ALLOW_REMOTE=true "
                f"-e ORACLE_SID={self.oracle_service_name} "
                f"-e ORACLE_PWD={self.oracle_password} "
                f"-e ORACLE_USER={self.oracle_user} "
                f"-v oracle_data_{timestamp}:/u01/app/oracle "
                f"{self.oracle_image}"
            )

            # 确保命令使用bridge网络
            run_cmd = docker_network_utils.fix_common_network_issues(run_cmd)
            
            logger.info(f"Creating Oracle container with command: {run_cmd}")
            stdin, stdout, stderr = self.ssh_client.exec_command(run_cmd)
            exit_status = stdout.channel.recv_exit_status()
            
            if exit_status == 0:
                container_id = stdout.read().decode('utf-8').strip()
                logger.info(f"Oracle container created with ID: {container_id}")
                
                # 等待容器启动
                await asyncio.sleep(10)
                
                # 验证容器状态
                new_status = await self.check_container_status()
                
                return {
                    'success': True,
                    'message': 'Oracle container created and started successfully',
                    'container_id': container_id,
                    'container_status': new_status,
                    'action': 'created',
                    'docker_command': run_cmd
                }
            else:
                error_output = stderr.read().decode('utf-8')
                logger.error(f"Failed to create Oracle container: {error_output}")
                return {
                    'success': False,
                    'message': f'Failed to create container: {error_output}',
                    'action': 'create_failed'
                }
                
        except Exception as e:
            logger.error(f"Failed to start Oracle container: {str(e)}")
            return {
                'success': False,
                'message': f'Error starting Oracle container: {str(e)}',
                'error': str(e),
                'action': 'error'
            }
        finally:
            await self._disconnect_ssh()
    
    async def stop_oracle_container(self) -> Dict[str, Any]:
        """停止Oracle容器"""
        try:
            await self._connect_ssh()
            
            # 检查容器状态
            status = await self.check_container_status()
            
            if not status['exists']:
                return {
                    'success': True,
                    'message': 'Oracle container does not exist',
                    'action': 'not_exists'
                }
            
            if not status['running']:
                return {
                    'success': True,
                    'message': 'Oracle container is already stopped',
                    'action': 'already_stopped'
                }
            
            # 停止容器
            stop_cmd = f"docker stop {self.container_name}"
            stdin, stdout, stderr = self.ssh_client.exec_command(stop_cmd)
            exit_status = stdout.channel.recv_exit_status()
            
            if exit_status == 0:
                return {
                    'success': True,
                    'message': 'Oracle container stopped successfully',
                    'action': 'stopped'
                }
            else:
                error_output = stderr.read().decode('utf-8')
                return {
                    'success': False,
                    'message': f'Failed to stop container: {error_output}',
                    'action': 'stop_failed'
                }
                
        except Exception as e:
            logger.error(f"Failed to stop Oracle container: {str(e)}")
            return {
                'success': False,
                'message': f'Error stopping Oracle container: {str(e)}',
                'error': str(e),
                'action': 'error'
            }
        finally:
            await self._disconnect_ssh()
    
    async def get_oracle_connection_info(self) -> Dict[str, Any]:
        """获取Oracle连接信息"""
        try:
            status = await self.check_container_status()
            
            if status['running']:
                return {
                    'success': True,
                    'connection_info': {
                        'host': self.server_host,
                        'port': self.oracle_port,
                        'service_name': self.oracle_service_name,
                        'username': self.oracle_user,
                        'password': self.oracle_password,
                        'container_status': status
                    }
                }
            else:
                return {
                    'success': False,
                    'message': 'Oracle container is not running',
                    'container_status': status
                }
                
        except Exception as e:
            logger.error(f"Failed to get Oracle connection info: {str(e)}")
            return {
                'success': False,
                'message': f'Error getting connection info: {str(e)}',
                'error': str(e)
            }

import logging
from typing import Dict, Any, List
from openai import OpenAI
from utils.config import Config
from services.oracle_text2sql_service import oracle_text2sql_service

logger = logging.getLogger(__name__)

class OracleAIService:
    """Oracle AI服务 - 基于Text2SQL系统的专业Oracle SQL生成"""

    def __init__(self):
        """初始化Oracle AI服务"""
        self.client = OpenAI(
            api_key=Config.DEEPSEEK_API_KEY,
            base_url=Config.DEEPSEEK_BASE_URL
        )
        self.text2sql_service = oracle_text2sql_service
        # 缓存Oracle服务实例以复用连接池
        self._oracle_service_cache = {}
        logger.info("Oracle AI Service initialized with Text2SQL system")

    def close_all_pools(self):
        """关闭所有连接池"""
        try:
            for cache_key, service in self._oracle_service_cache.items():
                try:
                    service.close_pool()
                    logger.info(f"Closed Oracle connection pool for {cache_key}")
                except Exception as e:
                    logger.error(f"Error closing Oracle connection pool for {cache_key}: {e}")

            self._oracle_service_cache.clear()
            logger.info("All Oracle connection pools closed")

        except Exception as e:
            logger.error(f"Error closing Oracle connection pools: {str(e)}")

    def _get_oracle_service(self, oracle_config: Dict[str, Any]):
        """获取或创建Oracle服务实例（支持连接池复用）"""
        # 创建缓存键
        cache_key = f"{oracle_config['host']}:{oracle_config['port']}:{oracle_config['user']}:{oracle_config['database_name']}"

        # 检查缓存中是否已有实例
        if cache_key in self._oracle_service_cache:
            logger.debug(f"Reusing cached Oracle service for {cache_key}")
            return self._oracle_service_cache[cache_key]

        # 创建新的Oracle服务实例
        from services.oracle_service import OracleService
        oracle_service = OracleService(
            host=oracle_config['host'],
            port=oracle_config['port'],
            user=oracle_config['user'],
            password=oracle_config['password'],
            service_name=oracle_config['database_name']
        )

        # 缓存实例
        self._oracle_service_cache[cache_key] = oracle_service
        logger.info(f"Created and cached new Oracle service for {cache_key}")
        return oracle_service

    def _call_deepseek(self, prompt: str) -> str:
        """调用DeepSeek API"""
        try:
            response = self.client.chat.completions.create(
                model="deepseek-chat",
                messages=[
                    {"role": "system", "content": "你是一个专业的Oracle数据库专家，能够将自然语言转换为准确的Oracle SQL语句。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=4096
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            logger.error(f"DeepSeek API call failed: {str(e)}")
            raise
    
    async def parse_natural_language_to_sql(self, natural_query: str) -> str:
        """将自然语言转换为Oracle SQL语句 - 使用Text2SQL系统"""
        logger.info(f"使用Text2SQL系统解析: {natural_query}")

        # 使用Text2SQL服务生成SQL
        text2sql_result = await self.text2sql_service.generate_sql(natural_query)

        # 获取生成的SQL并进行基本清理
        sql_query = text2sql_result.get('sql_query', '')

        if sql_query:
            # 清理SQL - 移除末尾分号和特殊字符
            sql_query = sql_query.strip()
            if sql_query.endswith(';'):
                sql_query = sql_query[:-1]

            # 移除可能的隐藏字符
            sql_query = ''.join(char for char in sql_query if ord(char) >= 32 or char in ['\n', '\t'])

            logger.info(f"Text2SQL生成SQL (已清理): {sql_query}")
            return sql_query
        else:
            logger.error("Text2SQL未生成SQL")
            raise Exception("Text2SQL生成失败")

    def _validate_oracle_sql(self, sql: str) -> bool:
        """验证Oracle SQL语句的基本语法"""
        try:
            # 基本的SQL语法检查
            sql_upper = sql.upper().strip()

            # 检查是否包含基本的SQL关键字
            valid_starts = ['SELECT', 'INSERT', 'UPDATE', 'DELETE', 'CREATE', 'ALTER', 'DROP', 'SHOW', 'DESCRIBE', 'DESC']
            if not any(sql_upper.startswith(keyword) for keyword in valid_starts):
                return False

            # 检查是否包含可能的无效字符
            invalid_chars = ['`', '"', '；']  # 反引号、双引号、中文分号
            if any(char in sql for char in invalid_chars):
                return False

            # 检查括号匹配
            if sql.count('(') != sql.count(')'):
                return False

            return True

        except Exception as e:
            logger.error(f"SQL validation error: {str(e)}")
            return False
    
    async def analyze_oracle_query_intent(self, natural_query: str) -> Dict[str, Any]:
        """分析Oracle查询意图"""
        try:
            logger.info(f"Analyzing Oracle query intent: {natural_query}")

            prompt = f"""
分析以下自然语言查询的意图，并返回JSON格式的结果。

自然语言查询: {natural_query}

请分析并返回以下信息的JSON格式:
{{
    "intent": "查询意图类型(select/insert/update/delete/create/system)",
    "target_objects": ["涉及的表名或对象名"],
    "operations": ["具体操作类型"],
    "conditions": ["查询条件"],
    "complexity": "复杂度(simple/medium/complex)",
    "oracle_features": ["使用的Oracle特性"]
}}

只返回JSON，不要其他文本:
"""

            result = self._call_deepseek(prompt)
            
            # 尝试解析JSON
            import json
            try:
                intent_data = json.loads(result)
                return intent_data
            except json.JSONDecodeError:
                # 如果解析失败，返回默认结构
                return {
                    "intent": "select",
                    "target_objects": ["unknown"],
                    "operations": ["query"],
                    "conditions": [],
                    "complexity": "simple",
                    "oracle_features": []
                }
                
        except Exception as e:
            logger.error(f"Failed to analyze Oracle query intent: {str(e)}")
            return {
                "intent": "select",
                "target_objects": ["unknown"],
                "operations": ["query"],
                "conditions": [],
                "complexity": "simple",
                "oracle_features": []
            }
    
    async def suggest_oracle_optimizations(self, sql_query: str) -> List[str]:
        """为Oracle SQL查询提供优化建议"""
        try:
            logger.info(f"Suggesting Oracle optimizations for: {sql_query}")

            prompt = f"""
请为以下Oracle SQL查询提供优化建议。

SQL查询: {sql_query}

请提供具体的优化建议，包括:
1. 索引建议
2. 查询重写建议
3. Oracle特有的优化技巧
4. 性能调优建议

请以列表形式返回建议，每行一个建议:
"""

            result = self._call_deepseek(prompt)
            
            # 将结果分割为建议列表
            suggestions = [line.strip() for line in result.split('\n') if line.strip()]
            
            logger.info(f"Generated {len(suggestions)} Oracle optimization suggestions")
            return suggestions
            
        except Exception as e:
            logger.error(f"Failed to suggest Oracle optimizations: {str(e)}")
            return ["建议添加适当的索引以提高查询性能"]
    
    async def explain_oracle_query_result(self, sql_query: str, result: Dict[str, Any]) -> str:
        """解释Oracle查询结果"""
        try:
            logger.info(f"Explaining Oracle query result for: {sql_query}")

            prompt = f"""
请解释以下Oracle SQL查询的执行结果。

SQL查询: {sql_query}
查询结果: {result}

请用中文简洁地解释:
1. 查询做了什么
2. 返回了什么数据
3. 结果的含义
4. 如果有错误，解释可能的原因

请直接返回解释文本:
"""

            explanation = self._call_deepseek(prompt)
            
            logger.info("Generated Oracle query result explanation")
            return explanation
            
        except Exception as e:
            logger.error(f"Failed to explain Oracle query result: {str(e)}")
            return "查询执行完成，请查看具体结果数据。"

    async def generate_oracle_query(self, natural_query: str, oracle_config: Dict[str, Any]) -> Dict[str, Any]:
        """生成Oracle查询并执行（基于Text2SQL系统）"""
        try:
            logger.info(f"使用Text2SQL系统生成Oracle查询: {natural_query}")

            # 1. 使用Text2SQL生成SQL
            text2sql_result = await self.text2sql_service.generate_sql(natural_query)

            sql_query = text2sql_result.get('sql_query', '')
            sql_info = text2sql_result.get('sql_info', {})
            statements = text2sql_result.get('statements', [])
            is_multi_statement = text2sql_result.get('is_multi_statement', False)

            logger.info(f"Text2SQL生成SQL: {sql_query}")
            logger.info(f"语句类型: {sql_info.get('primary_type')}, 多语句: {is_multi_statement}")

            # 2. 获取Oracle服务实例（复用连接池）
            oracle_service = self._get_oracle_service(oracle_config)

            # 3. 智能执行SQL
            if is_multi_statement:
                logger.info(f"执行多语句 ({len(statements)} 个)")
                result = await oracle_service.execute_multiple_statements(sql_query)
            else:
                # 单语句执行
                primary_type = sql_info.get('primary_type', 'UNKNOWN')
                if primary_type == 'SELECT':
                    result = await oracle_service.execute_query(sql_query)
                else:
                    result = await oracle_service.execute_non_query(sql_query)

            # 注意：不关闭连接，保持连接池活跃以供后续复用

            return {
                'success': True,
                'sql_query': sql_query,
                'query_result': result,
                'sql_info': sql_info,
                'text2sql_result': text2sql_result
            }

        except Exception as e:
            logger.error(f"Text2SQL Oracle查询生成失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return {
                'success': False,
                'error': str(e),
                'sql_query': None,
                'query_result': None
            }

# 创建全局Oracle AI服务实例
oracle_ai_service = OracleAIService()

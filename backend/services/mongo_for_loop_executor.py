import re
import asyncio
import logging
from typing import Dict, Any, List
from pymongo import MongoClient
from services.mongo_service import MongoService
from urllib.parse import quote_plus

logger = logging.getLogger(__name__)

class MongoForLoopExecutor:
    """MongoDB for循环语句专用执行器"""
    
    def __init__(self, mongo_service: MongoService):
        self.mongo_service = mongo_service
        self.client = None
        self.db = None
        
    async def connect(self):
        """建立数据库连接"""
        try:
            # 获取MongoService的连接参数
            host = self.mongo_service.host or 'localhost'
            port = self.mongo_service.port or 27017
            username = self.mongo_service.user
            password = self.mongo_service.password
            database = self.mongo_service.database or 'test'
            
            # 构建连接字符串
            if username and password:
                # 对用户名和密码进行URL编码
                encoded_username = quote_plus(username)
                encoded_password = quote_plus(password)
                connection_string = f"mongodb://{encoded_username}:{encoded_password}@{host}:{port}/"
            else:
                connection_string = f"mongodb://{host}:{port}/"
            
            # 创建新的MongoClient实例
            self.client = MongoClient(
                connection_string,
                serverSelectionTimeoutMS=5000,
                connectTimeoutMS=5000
            )
            
            # 测试连接
            self.client.admin.command('ping')
            self.db = self.client[database]
            logger.info(f"MongoForLoopExecutor connected to MongoDB: {host}:{port}")
            
        except Exception as e:
            logger.error(f"Failed to connect to MongoDB: {str(e)}")
            raise
    
    async def execute_for_loop(self, for_loop_query: str) -> Dict[str, Any]:
        """执行for循环语句"""
        try:
            logger.info(f"Executing for loop query: {for_loop_query}")
            
            # 解析for循环语句
            parsed_loop = self._parse_for_loop(for_loop_query)
            if not parsed_loop:
                raise Exception("Failed to parse for loop query")
            
            var_name = parsed_loop['var_name']
            start_value = parsed_loop['start_value']
            end_value = parsed_loop['end_value']
            loop_body = parsed_loop['loop_body']
            
            logger.info(f"Parsed for loop - var: {var_name}, range: {start_value}-{end_value}")
            
            # 确保连接已建立
            if not self.client:
                await self.connect()
            
            # 执行循环
            results = []
            success_count = 0
            
            # 限制最大迭代次数以避免性能问题
            max_iterations = min(end_value - start_value, 1000)
            
            for i in range(start_value, start_value + max_iterations):
                try:
                    # 替换变量
                    iteration_query = re.sub(r'\b' + re.escape(var_name) + r'\b', str(i), loop_body)
                    
                    # 处理Math表达式
                    iteration_query = self._process_math_expressions(iteration_query)
                    
                    logger.info(f"Executing iteration {i}: {iteration_query}")
                    
                    # 执行单次迭代
                    result = await self._execute_single_query(iteration_query)
                    results.append({
                        'iteration': i,
                        'result': result,
                        'success': True
                    })
                    success_count += 1
                    
                    # 添加小延迟以避免过载
                    await asyncio.sleep(0.01)
                    
                except Exception as e:
                    logger.warning(f"Error executing iteration {i}: {str(e)}")
                    results.append({
                        'iteration': i,
                        'error': str(e),
                        'success': False
                    })
            
            return {
                'type': 'for_loop',
                'operation': 'for',
                'variable': var_name,
                'range': {'start': start_value, 'end': start_value + max_iterations},
                'executed_iterations': success_count,
                'total_iterations': max_iterations,
                'results': results[-10:] if len(results) > 10 else results,  # 只返回最后10个结果
                'acknowledged': success_count > 0
            }
            
        except Exception as e:
            logger.error(f"For loop execution failed: {str(e)}")
            return {
                'type': 'for_loop',
                'operation': 'for',
                'error': str(e),
                'acknowledged': False
            }
    
    def _parse_for_loop(self, for_loop_query: str) -> Dict[str, Any]:
        """解析for循环语句"""
        try:
            # 移除末尾分号
            query = for_loop_query.rstrip(';').strip()
            
            # 匹配for循环模式: for(let i=0; i<100; i++){...}
            pattern = r'for\s*\(\s*(?:var|let)?\s*(\w+)\s*=\s*(\d+)\s*;\s*\1\s*<\s*(\d+)\s*;\s*\1\+\+\s*\)\s*\{(.+)\}'
            match = re.search(pattern, query, re.DOTALL)
            
            if not match:
                logger.error(f"Cannot parse for loop pattern: {query}")
                return None
            
            var_name = match.group(1)
            start_value = int(match.group(2))
            end_value = int(match.group(3))
            loop_body = match.group(4).strip()
            
            # 移除大括号
            if loop_body.startswith('{') and loop_body.endswith('}'):
                loop_body = loop_body[1:-1].strip()
            
            return {
                'var_name': var_name,
                'start_value': start_value,
                'end_value': end_value,
                'loop_body': loop_body
            }
            
        except Exception as e:
            logger.error(f"Failed to parse for loop: {str(e)}")
            return None
    
    def _process_math_expressions(self, query: str) -> str:
        """处理Math表达式"""
        try:
            # 处理 Math.random() * N
            query = re.sub(r'Math\.random\(\)\s*\*\s*(\d+(?:\.\d+)?)', 
                          lambda m: str(round(float(m.group(1)) / 2)), query)
            
            # 处理 Math.floor(Math.random() * X) + Y
            query = re.sub(r'Math\.floor\(Math\.random\(\)\s*\*\s*(\d+(?:\.\d+)?)\)\s*\+\s*(\d+(?:\.\d+)?)', 
                          lambda m: str(int(float(m.group(1)) / 2 + float(m.group(2)))), query)
            
            # 处理 Math.floor(Math.random() * X)
            query = re.sub(r'Math\.floor\(Math\.random\(\)\s*\*\s*(\d+(?:\.\d+)?)\)', 
                          lambda m: str(int(float(m.group(1)) / 2)), query)
            
            return query
        except Exception as e:
            logger.warning(f"Failed to process math expressions: {str(e)}")
            return query
    
    async def _execute_single_query(self, query: str) -> Dict[str, Any]:
        """执行单条MongoDB查询"""
        try:
            # 简单的查询解析和执行
            query = query.strip()
            
            # 移除末尾分号
            query = query.rstrip(';')
            
            if query.startswith('db.'):
                # 解析 db.collection.operation(...) 格式
                # 示例: db.collection.insert({...})
                parts = query[3:].split('.', 1)  # 移除 'db.' 前缀
                if len(parts) < 2:
                    raise Exception(f"Invalid query format: {query}")
                
                collection_name = parts[0]
                operation_part = parts[1]
                
                # 获取集合
                collection = self.db[collection_name]
                
                # 解析操作和参数
                op_match = re.match(r'(\w+)\((.*)\)', operation_part)
                if not op_match:
                    raise Exception(f"Cannot parse operation: {operation_part}")
                
                operation = op_match.group(1)
                params = op_match.group(2)
                
                # 执行操作
                if operation in ['insert', 'insertOne']:
                    # 解析文档参数
                    doc = self._parse_document(params)
                    result = collection.insert_one(doc)
                    return {
                        'type': 'insert',
                        'operation': operation,
                        'inserted_id': str(result.inserted_id) if result.inserted_id else None,
                        'acknowledged': result.acknowledged
                    }
                
                elif operation in ['insertMany']:
                    # 解析文档数组参数
                    docs = self._parse_document_array(params)
                    result = collection.insert_many(docs)
                    return {
                        'type': 'insert_many',
                        'operation': operation,
                        'inserted_ids': [str(id) for id in result.inserted_ids] if result.inserted_ids else None,
                        'acknowledged': result.acknowledged
                    }
                
                elif operation in ['updateOne', 'updateMany']:
                    # 解析更新参数
                    update_params = self._parse_update_params(params)
                    if operation == 'updateOne':
                        result = collection.update_one(update_params['filter'], update_params['update'])
                    else:
                        result = collection.update_many(update_params['filter'], update_params['update'])
                    
                    return {
                        'type': 'update',
                        'operation': operation,
                        'matched_count': result.matched_count,
                        'modified_count': result.modified_count,
                        'acknowledged': result.acknowledged
                    }
                
                elif operation in ['deleteOne', 'deleteMany']:
                    # 解析删除参数
                    filter_doc = self._parse_document(params)
                    if operation == 'deleteOne':
                        result = collection.delete_one(filter_doc)
                    else:
                        result = collection.delete_many(filter_doc)
                    
                    return {
                        'type': 'delete',
                        'operation': operation,
                        'deleted_count': result.deleted_count,
                        'acknowledged': result.acknowledged
                    }
                
                elif operation == 'createCollection':
                    # 创建集合
                    options = {}
                    if params:
                        # 解析集合选项
                        options = self._parse_document(params)
                    
                    collection_name = params.strip().strip('"\'') if params and not params.startswith('{') else collection_name
                    result = self.db.create_collection(collection_name, **options)
                    return {
                        'type': 'modification',
                        'operation': operation,
                        'collection': collection_name,
                        'acknowledged': True
                    }
                
                elif operation == 'drop':
                    # 删除集合
                    result = collection.drop()
                    return {
                        'type': 'modification',
                        'operation': operation,
                        'collection': collection_name,
                        'acknowledged': True
                    }
                
                else:
                    raise Exception(f"Unsupported operation: {operation}")
            
            else:
                raise Exception(f"Unsupported query format: {query}")
                
        except Exception as e:
            logger.error(f"Failed to execute single query '{query}': {str(e)}")
            raise
    
    def _parse_document(self, params: str) -> Dict[str, Any]:
        """解析文档参数"""
        try:
            # 简单处理，实际应该使用JSON解析
            params = params.strip()
            if params.startswith('{') and params.endswith('}'):
                # 这是一个简化的处理方式，实际应该使用 proper JSON 解析
                # 但在没有依赖的情况下，我们做一些基本的处理
                doc_str = params[1:-1].strip()
                doc = {}
                
                # 简单的键值对解析
                pairs = doc_str.split(',')
                for pair in pairs:
                    pair = pair.strip()
                    if ':' in pair:
                        key, value = pair.split(':', 1)
                        key = key.strip().strip('"\'')
                        value = value.strip().strip('"\'')
                        
                        # 尝试转换为数字
                        if value.isdigit():
                            value = int(value)
                        elif value.replace('.', '').isdigit():
                            value = float(value)
                        elif value.lower() == 'true':
                            value = True
                        elif value.lower() == 'false':
                            value = False
                        elif value.lower() == 'null':
                            value = None
                            
                        doc[key] = value
                
                return doc
            else:
                # 如果不是对象格式，直接返回
                return params
        except Exception as e:
            logger.warning(f"Failed to parse document: {params}, error: {str(e)}")
            return {}
    
    def _parse_document_array(self, params: str) -> List[Dict[str, Any]]:
        """解析文档数组参数"""
        try:
            params = params.strip()
            if params.startswith('[') and params.endswith(']'):
                # 简化的数组解析
                array_content = params[1:-1].strip()
                docs = []
                
                # 简单按逗号分割（不考虑嵌套情况）
                doc_strings = array_content.split('},')
                for i, doc_str in enumerate(doc_strings):
                    if not doc_str.endswith('}') and i < len(doc_strings) - 1:
                        doc_str += '}'
                    elif i == len(doc_strings) - 1 and not doc_str.endswith('}'):
                        # 最后一个元素
                        pass
                    
                    doc = self._parse_document(doc_str.strip())
                    docs.append(doc)
                
                return docs
            else:
                return []
        except Exception as e:
            logger.warning(f"Failed to parse document array: {params}, error: {str(e)}")
            return []
    
    def _parse_update_params(self, params: str) -> Dict[str, Any]:
        """解析更新操作参数"""
        try:
            params = params.strip()
            if params.startswith('{') and '},' in params:
                # 分割过滤器和更新器
                parts = params.split('},', 1)
                filter_part = parts[0] + '}'
                update_part = parts[1].strip()
                
                filter_doc = self._parse_document(filter_part)
                update_doc = self._parse_document(update_part)
                
                return {
                    'filter': filter_doc,
                    'update': update_doc
                }
            else:
                # 尝试按逗号分割
                parts = params.split(',', 1)
                if len(parts) == 2:
                    filter_doc = self._parse_document(parts[0])
                    update_doc = self._parse_document(parts[1])
                    return {
                        'filter': filter_doc,
                        'update': update_doc
                    }
            
            return {'filter': {}, 'update': {}}
        except Exception as e:
            logger.warning(f"Failed to parse update params: {params}, error: {str(e)}")
            return {'filter': {}, 'update': {}}
    
    async def close(self):
        """关闭连接"""
        try:
            if self.client:
                self.client.close()
                self.client = None
                self.db = None
                logger.info("MongoForLoopExecutor connection closed")
        except Exception as e:
            logger.error(f"Error closing MongoForLoopExecutor connection: {str(e)}")
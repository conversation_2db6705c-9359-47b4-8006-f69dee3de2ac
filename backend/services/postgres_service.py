import asyncio
import logging
import traceback
import time
from typing import Dict, Any, List, Optional
import psycopg2
import psycopg2.extras
from psycopg2.pool import SimpleConnectionPool
from utils.config import Config

logger = logging.getLogger(__name__)

class PostgresService:
    """PostgreSQL服务类"""
    
    def __init__(self, host: str = None, port: int = None, user: str = None, 
                 password: str = None, database: str = None):
        """初始化PostgreSQL服务"""
        self.host = host or Config.POSTGRES_HOST
        self.port = port or Config.POSTGRES_PORT
        self.user = user or Config.POSTGRES_USER
        self.password = password or Config.POSTGRES_PASSWORD
        self.database = database or Config.POSTGRES_DATABASE
        
        self.connection_pool = None
        # 添加持久连接支持
        self._persistent_connection = None

        logger.info(f"PostgreSQL Service initialized for {self.host}:{self.port}")
    
    async def initialize(self):
        """初始化连接池"""
        try:
            await self.create_connection_pool()
            logger.info("PostgreSQL service initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize PostgreSQL service: {str(e)}")
            raise
    
    async def create_connection_pool(self):
        """创建连接池"""
        try:
            # 在线程池中创建连接池
            loop = asyncio.get_event_loop()
            self.connection_pool = await loop.run_in_executor(
                None,
                lambda: SimpleConnectionPool(
                    1, 20,  # 最小和最大连接数
                    host=self.host,
                    port=self.port,
                    user=self.user,
                    password=self.password,
                    database=self.database,
                    connect_timeout=10,
                    options="-c timezone=Asia/Shanghai"  # 设置时区为Asia/Shanghai
                )
            )
            logger.info(f"PostgreSQL connection pool created for {self.host}:{self.port}")
        except Exception as e:
            logger.error(f"Failed to create PostgreSQL connection pool: {str(e)}")
            raise
    
    async def check_connection(self) -> bool:
        """检查数据库连接"""
        try:
            if not self.connection_pool:
                await self.create_connection_pool()
            
            # 从连接池获取连接并测试
            loop = asyncio.get_event_loop()
            
            def test_connection():
                conn = self.connection_pool.getconn()
                try:
                    with conn.cursor() as cursor:
                        cursor.execute("SELECT 1")
                        cursor.fetchone()
                    return True
                finally:
                    self.connection_pool.putconn(conn)
            
            result = await loop.run_in_executor(None, test_connection)
            return result
            
        except Exception as e:
            logger.error(f"PostgreSQL connection check failed: {str(e)}")
            return False

    async def create_persistent_connection(self) -> bool:
        """创建持久连接用于抓包"""
        try:
            if self._persistent_connection:
                # 检查现有连接是否有效
                try:
                    with self._persistent_connection.cursor() as cursor:
                        cursor.execute("SELECT 1")
                        cursor.fetchone()
                    logger.info("Existing PostgreSQL persistent connection is valid")
                    return True
                except:
                    # 现有连接无效，关闭并重新创建
                    try:
                        self._persistent_connection.close()
                    except Exception as e:

                        logger.error(f"操作失败: {type(e).__name__}: {str(e)}")

                        logger.error(f"异常堆栈跟踪: {traceback.format_exc()}")
                    self._persistent_connection = None

            # 创建新的持久连接
            def create_conn():
                return psycopg2.connect(
                    host=self.host,
                    port=self.port,
                    user=self.user,
                    password=self.password,
                    database=self.database,
                    connect_timeout=30
                )

            self._persistent_connection = await asyncio.get_event_loop().run_in_executor(
                None, create_conn
            )

            # 测试连接
            with self._persistent_connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                cursor.fetchone()

            logger.info("Persistent PostgreSQL connection created successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to create persistent PostgreSQL connection: {str(e)}")
            self._persistent_connection = None
            return False

    async def create_fresh_connection(self) -> bool:
        """创建全新的连接用于抓包（确保能捕获握手包）"""
        try:
            # 强制关闭现有连接
            if self._persistent_connection:
                try:
                    self._persistent_connection.close()
                except Exception as e:
                    logger.debug(f"Error closing existing PostgreSQL connection: {e}")
                self._persistent_connection = None

            # 等待一小段时间确保连接完全关闭
            await asyncio.sleep(0.5)

            # 创建全新的连接
            def create_conn():
                return psycopg2.connect(
                    host=self.host,
                    port=self.port,
                    user=self.user,
                    password=self.password,
                    database=self.database,
                    connect_timeout=30
                )

            self._persistent_connection = await asyncio.get_event_loop().run_in_executor(
                None, create_conn
            )

            # 测试连接
            with self._persistent_connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                cursor.fetchone()

            logger.info("Fresh PostgreSQL connection created successfully for packet capture")
            return True

        except Exception as e:
            logger.error(f"Failed to create fresh PostgreSQL connection: {str(e)}")
            self._persistent_connection = None
            return False

    async def close_persistent_connection(self):
        """关闭持久连接"""
        try:
            if self._persistent_connection:
                self._persistent_connection.close()
                self._persistent_connection = None
                logger.info("Persistent PostgreSQL connection closed")
        except Exception as e:
            logger.error(f"Error closing persistent PostgreSQL connection: {str(e)}")
            self._persistent_connection = None

    async def force_close_connection(self):
        """强制关闭连接（确保能捕获挥手包）"""
        try:
            if self._persistent_connection:
                logger.info("Force closing PostgreSQL connection for packet capture")

                # 执行一个简单查询确保连接活跃
                try:
                    with self._persistent_connection.cursor() as cursor:
                        cursor.execute("SELECT 1")
                        cursor.fetchone()
                    logger.debug("PostgreSQL connection is active before closing")
                except Exception as e:
                    logger.debug(f"PostgreSQL connection test failed before closing: {e}")

                # 强制关闭连接
                self._persistent_connection.close()
                self._persistent_connection = None

                # 等待一小段时间确保挥手包被发送
                await asyncio.sleep(0.5)

                logger.info("PostgreSQL connection force closed successfully")
            else:
                logger.warning("No persistent PostgreSQL connection to close")

        except Exception as e:
            logger.error(f"Error during PostgreSQL force close: {str(e)}")
            self._persistent_connection = None

    async def execute_sql_query(self, sql_query: str, use_extended_query: bool = True) -> Dict[str, Any]:
        """执行PostgreSQL SQL查询，默认使用Extended Query协议"""
        try:
            if not await self.check_connection():
                raise Exception("PostgreSQL connection failed")

            protocol_type = "Extended Query" if use_extended_query else "Simple Query"
            logger.info(f"Executing PostgreSQL query using {protocol_type}: {sql_query}")

            # 在线程池中执行查询
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(None, self._execute_query_sync, sql_query, use_extended_query)

            # 添加协议信息到结果中
            result['protocol'] = protocol_type

            logger.info(f"PostgreSQL query executed successfully using {protocol_type}")
            return result

        except Exception as e:
            logger.error(f"Failed to execute PostgreSQL query: {str(e)}")
            raise Exception(f"PostgreSQL query execution failed: {str(e)}")

    async def execute_extended_query(self, sql_query: str) -> Dict[str, Any]:
        """强制使用Extended Query协议执行查询"""
        return await self.execute_sql_query(sql_query, use_extended_query=True)

    async def execute_simple_query(self, sql_query: str) -> Dict[str, Any]:
        """强制使用Simple Query协议执行查询"""
        return await self.execute_sql_query(sql_query, use_extended_query=False)

    async def execute_query(self, sql_query: str) -> Dict[str, Any]:
        """执行SQL查询的通用方法（兼容性别名）"""
        return await self.execute_sql_query(sql_query, use_extended_query=True)

    async def execute_sql_query_with_persistent_connection(self, sql_query: str, use_extended_query: bool = True) -> Dict[str, Any]:
        """使用持久连接执行PostgreSQL SQL查询（用于抓包场景）"""
        try:
            if not self._persistent_connection:
                raise Exception("No persistent connection available. Call create_persistent_connection first.")

            protocol_type = "Extended Query" if use_extended_query else "Simple Query"
            logger.info(f"Executing PostgreSQL query with persistent connection using {protocol_type}: {sql_query}")

            # 在线程池中执行查询
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(None, self._execute_query_sync_persistent, sql_query, use_extended_query)

            # 添加协议信息到结果中
            result['protocol'] = protocol_type

            logger.info(f"PostgreSQL query executed successfully with persistent connection using {protocol_type}")
            return result

        except Exception as e:
            logger.error(f"Failed to execute PostgreSQL query with persistent connection: {str(e)}")
            raise Exception(f"PostgreSQL persistent query execution failed: {str(e)}")

    async def execute_query_for_capture(self, sql_query: str, use_extended_query: bool = True) -> Dict[str, Any]:
        """专门用于抓包的SQL执行方法（包含完整的连接生命周期）"""
        try:
            logger.info("Starting PostgreSQL SQL execution for packet capture")

            # 1. 创建全新连接（确保捕获握手包）
            if not await self.create_fresh_connection():
                raise Exception("Failed to create fresh PostgreSQL connection for packet capture")

            # 2. 等待一小段时间确保连接建立完成
            await asyncio.sleep(0.5)

            # 3. 执行SQL查询
            protocol_type = "Extended Query" if use_extended_query else "Simple Query"
            logger.info(f"Executing PostgreSQL SQL for capture using {protocol_type}: {sql_query}")
            result = await self.execute_sql_query_with_persistent_connection(sql_query, use_extended_query)

            # 4. 等待一小段时间确保查询完成
            await asyncio.sleep(0.5)

            logger.info("PostgreSQL SQL execution for packet capture completed")
            return result

        except Exception as e:
            logger.error(f"PostgreSQL SQL execution for capture failed: {str(e)}")
            raise

    def _execute_query_sync_persistent(self, sql_query: str, use_extended_query: bool = True) -> Dict[str, Any]:
        """使用持久连接同步执行SQL查询"""
        try:
            with self._persistent_connection.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cursor:
                # 移除可能的分号和空白字符
                query = sql_query.strip().rstrip(';')
                query_upper = query.upper().strip()

                if use_extended_query:
                    # 使用Extended Query协议 (Prepare + Execute)
                    logger.info("Using Extended Query protocol with persistent connection")

                    # 使用参数化查询来触发Extended Query协议
                    if query_upper.startswith('SELECT'):
                        # 对于SELECT查询，使用参数化方式
                        cursor.execute(query, ())
                    elif query_upper.startswith(('INSERT', 'UPDATE', 'DELETE')):
                        # 对于DML语句，可以使用PREPARE/EXECUTE
                        stmt_name = f"temp_stmt_{int(time.time())}"
                        try:
                            cursor.execute(f"PREPARE {stmt_name} AS {query}")
                            cursor.execute(f"EXECUTE {stmt_name}")
                            cursor.execute(f"DEALLOCATE {stmt_name}")
                        except psycopg2.Error as e:
                            # 如果PREPARE失败，回退到直接执行
                            logger.warning(f"PREPARE failed with persistent connection, falling back: {e}")
                            cursor.execute(query)
                    else:
                        # 对于DDL语句（CREATE, ALTER, DROP等），直接执行
                        logger.info("DDL statement detected with persistent connection, using direct execution")
                        cursor.execute(query)
                else:
                    # 使用Simple Query协议
                    logger.info("Using Simple Query protocol with persistent connection")
                    cursor.execute(query)

                # 对于非SELECT语句，需要提交事务
                if not query_upper.startswith('SELECT'):
                    try:
                        self._persistent_connection.commit()
                        logger.debug(f"Transaction committed for query: {query_upper[:50]}...")
                    except Exception as commit_error:
                        logger.error(f"Failed to commit transaction: {commit_error}")
                        self._persistent_connection.rollback()
                        raise

                # 获取查询结果
                if cursor.description:
                    # 有结果集的查询
                    results = cursor.fetchall()
                    columns = [desc[0] for desc in cursor.description]

                    # 转换为字典列表
                    data = [dict(row) for row in results]

                    return {
                        'type': 'query',
                        'data': data,
                        'row_count': len(data),
                        'columns': columns,
                        'message': f"Query executed successfully. {len(data)} rows returned."
                    }
                else:
                    # 无结果集的查询（INSERT, UPDATE, DELETE等）
                    affected_rows = cursor.rowcount
                    return {
                        'type': 'modification',
                        'affected_rows': affected_rows,
                        'message': f"Query executed successfully. {affected_rows} rows affected."
                    }

        except Exception as e:
            logger.error(f"Persistent PostgreSQL query execution error: {str(e)}")
            # 发生错误时回滚事务
            try:
                self._persistent_connection.rollback()
                logger.debug("Transaction rolled back due to error")
            except Exception as rollback_error:
                logger.error(f"Failed to rollback transaction: {rollback_error}")
            raise

    def _execute_query_sync(self, sql_query: str, use_extended_query: bool = True) -> Dict[str, Any]:
        """同步执行SQL查询，支持Extended Query协议"""
        conn = self.connection_pool.getconn()
        try:
            with conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cursor:
                # 移除可能的分号和空白字符
                query = sql_query.strip().rstrip(';')

                if use_extended_query:
                    # 使用Extended Query协议 (Prepare + Execute)
                    logger.info("Using Extended Query protocol (Prepare + Execute)")

                    # 使用参数化查询来触发Extended Query协议
                    query_upper = query.upper().strip()

                    if query_upper.startswith('SELECT'):
                        # 对于SELECT查询，使用参数化查询（即使没有实际参数）
                        cursor.execute(query, ())
                    elif query_upper.startswith(('INSERT', 'UPDATE', 'DELETE')):
                        # 对于DML语句，可以使用PREPARE/EXECUTE
                        stmt_name = f"temp_stmt_{int(time.time())}"
                        try:
                            cursor.execute(f"PREPARE {stmt_name} AS {query}")
                            cursor.execute(f"EXECUTE {stmt_name}")
                            cursor.execute(f"DEALLOCATE {stmt_name}")
                        except psycopg2.Error as e:
                            # 如果PREPARE失败，回退到直接执行
                            logger.warning(f"PREPARE failed, falling back to direct execution: {e}")
                            cursor.execute(query)
                    else:
                        # 对于DDL语句（CREATE, ALTER, DROP等），直接执行
                        # 因为PREPARE不支持DDL语句
                        logger.info("DDL statement detected, using direct execution")
                        cursor.execute(query)
                else:
                    # 使用Simple Query协议
                    logger.info("Using Simple Query protocol")
                    cursor.execute(query)
                
                # 判断查询类型
                query_upper = query.upper().strip()
                
                if query_upper.startswith('SELECT') or query_upper.startswith('WITH'):
                    # 查询操作
                    rows = cursor.fetchall()
                    # 转换为普通字典列表
                    data = [dict(row) for row in rows]
                    
                    return {
                        'type': 'query',
                        'data': data,
                        'count': len(data),
                        'columns': [desc[0] for desc in cursor.description] if cursor.description else []
                    }
                    
                elif query_upper.startswith(('INSERT', 'UPDATE', 'DELETE')):
                    # 修改操作
                    conn.commit()
                    affected_rows = cursor.rowcount
                    
                    operation_type = query_upper.split()[0].lower()
                    return {
                        'type': 'modification',
                        'operation': operation_type,
                        'affected_rows': affected_rows,
                        'message': f"{operation_type.capitalize()} operation completed, {affected_rows} rows affected"
                    }
                    
                elif query_upper.startswith(('CREATE', 'ALTER', 'DROP')):
                    # DDL操作
                    conn.commit()
                    operation_type = query_upper.split()[0].lower()
                    
                    return {
                        'type': 'ddl',
                        'operation': operation_type,
                        'message': f"{operation_type.capitalize()} operation completed successfully"
                    }
                    
                else:
                    # 其他操作
                    conn.commit()
                    return {
                        'type': 'other',
                        'message': "Query executed successfully"
                    }
                    
        except psycopg2.Error as e:
            conn.rollback()
            logger.error(f"PostgreSQL error: {str(e)}")
            raise Exception(f"PostgreSQL error: {str(e)}")
        except Exception as e:
            conn.rollback()
            logger.error(f"Query execution error: {str(e)}")
            raise Exception(f"Query execution error: {str(e)}")
        finally:
            self.connection_pool.putconn(conn)
    
    async def get_databases(self) -> List[str]:
        """获取数据库列表"""
        try:
            result = await self.execute_sql_query(
                "SELECT datname FROM pg_database WHERE datistemplate = false ORDER BY datname"
            )
            return [row['datname'] for row in result['data']]
        except Exception as e:
            logger.error(f"Failed to get PostgreSQL databases: {str(e)}")
            raise
    
    async def get_tables(self, schema: str = 'public') -> List[str]:
        """获取表列表"""
        try:
            result = await self.execute_sql_query(
                f"SELECT tablename FROM pg_tables WHERE schemaname = '{schema}' ORDER BY tablename"
            )
            return [row['tablename'] for row in result['data']]
        except Exception as e:
            logger.error(f"Failed to get PostgreSQL tables: {str(e)}")
            raise
    
    async def get_table_info(self, table_name: str, schema: str = 'public') -> Dict[str, Any]:
        """获取表结构信息"""
        try:
            # 获取列信息
            columns_result = await self.execute_sql_query(f"""
                SELECT 
                    column_name,
                    data_type,
                    is_nullable,
                    column_default,
                    character_maximum_length
                FROM information_schema.columns 
                WHERE table_schema = '{schema}' AND table_name = '{table_name}'
                ORDER BY ordinal_position
            """)
            
            # 获取表注释
            comment_result = await self.execute_sql_query(f"""
                SELECT obj_description(oid) as table_comment
                FROM pg_class 
                WHERE relname = '{table_name}' AND relnamespace = (
                    SELECT oid FROM pg_namespace WHERE nspname = '{schema}'
                )
            """)
            
            table_comment = comment_result['data'][0]['table_comment'] if comment_result['data'] else None
            
            return {
                'table_name': table_name,
                'schema': schema,
                'columns': columns_result['data'],
                'comment': table_comment
            }
            
        except Exception as e:
            logger.error(f"Failed to get PostgreSQL table info: {str(e)}")
            raise
    
    async def test_connection_with_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """使用指定配置测试连接"""
        try:
            # 创建临时连接
            loop = asyncio.get_event_loop()
            
            def test_conn():
                conn = psycopg2.connect(
                    host=config['host'],
                    port=config['port'],
                    user=config['user'],
                    password=config['password'],
                    database=config['database'],
                    connect_timeout=10
                )
                try:
                    with conn.cursor() as cursor:
                        cursor.execute("SELECT version()")
                        version = cursor.fetchone()[0]
                    return {'success': True, 'version': version}
                finally:
                    conn.close()
            
            result = await loop.run_in_executor(None, test_conn)
            return result
            
        except Exception as e:
            logger.error(f"PostgreSQL connection test failed: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    async def close(self):
        """关闭连接池"""
        try:
            if self.connection_pool:
                self.connection_pool.closeall()
                logger.info("PostgreSQL connection pool closed")
        except Exception as e:
            logger.error(f"Failed to close PostgreSQL connection pool: {str(e)}")
    
    def __del__(self):
        """析构函数"""
        try:
            if self.connection_pool:
                self.connection_pool.closeall()
        except Exception as e:

            logger.error(f"操作失败: {type(e).__name__}: {str(e)}")

            logger.error(f"异常堆栈跟踪: {traceback.format_exc()}")
# 创建默认实例，用于兼容现有代码
# 注意：实际使用时建议根据配置动态创建实例
postgres_service = PostgresService()

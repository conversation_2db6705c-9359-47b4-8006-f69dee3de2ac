#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
步骤SQL校验服务 - 实现每个执行步骤的SQL与pcap包的一一对应校验
"""

import os
import re
import json
import time
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
from difflib import SequenceMatcher

from services.pcap_sql_analyzer import PCAPSQLAnalyzer, SQLStatement, DatabaseType, SQLType

logger = logging.getLogger(__name__)

class ValidationStatus(Enum):
    """校验状态枚举"""
    MATCHED = "matched"           # 完全匹配
    PARTIAL_MATCHED = "partial"   # 部分匹配
    NOT_FOUND = "not_found"       # 未找到
    SIMILAR = "similar"           # 相似但不完全匹配
    ERROR = "error"               # 校验错误

@dataclass
class StepSQLValidationResult:
    """步骤SQL校验结果"""
    step_number: int
    expected_sql: str
    sql_type: SQLType
    validation_status: ValidationStatus
    matched_statements: List[SQLStatement]
    similarity_score: float
    confidence: float
    packet_numbers: List[int]
    error_message: Optional[str] = None

@dataclass
class StepValidationSummary:
    """步骤校验汇总"""
    total_steps: int
    matched_steps: int
    partial_matched_steps: int
    not_found_steps: int
    similar_steps: int
    error_steps: int
    overall_success_rate: float
    step_results: List[StepSQLValidationResult]

class StepSQLValidationService:
    """步骤SQL校验服务"""
    
    def __init__(self):
        """初始化校验服务"""
        self.analyzer = PCAPSQLAnalyzer()
        self.similarity_threshold = 0.5  # 相似度阈值（从0.7降低到0.5）
        self.partial_match_threshold = 0.3  # 部分匹配阈值（从0.5降低到0.3）
    
    def validate_steps_with_pcap(
        self, 
        execution_steps: List[Dict[str, Any]], 
        pcap_file: str,
        database_type: str = None,
        database_port: int = None
    ) -> StepValidationSummary:
        """
        验证执行步骤与pcap包的对应关系
        
        Args:
            execution_steps: 执行步骤列表，每个步骤包含sql_statements
            pcap_file: pcap文件路径
            database_type: 数据库类型
            database_port: 数据库端口
            
        Returns:
            StepValidationSummary: 校验汇总结果
        """
        logger.info(f"开始验证 {len(execution_steps)} 个步骤与pcap文件 {pcap_file} 的对应关系")
        
        try:
            # 分析pcap文件
            analysis_result = self.analyzer.analyze_pcap_file(
                pcap_file, 
                database_type=database_type, 
                database_port=database_port
            )
            
            if not analysis_result.success:
                logger.error(f"PCAP文件分析失败: {analysis_result.error_message}")
                return self._create_error_summary(execution_steps, analysis_result.error_message)
            
            logger.info(f"PCAP分析完成，找到 {len(analysis_result.sql_statements)} 个SQL语句")
            
            # 验证每个步骤
            step_results = []
            for step in execution_steps:
                step_result = self._validate_single_step(step, analysis_result.sql_statements)
                step_results.append(step_result)
            
            # 生成汇总
            summary = self._generate_summary(step_results)
            logger.info(f"步骤校验完成，成功率: {summary.overall_success_rate:.2%}")
            
            return summary
            
        except Exception as e:
            logger.error(f"步骤校验失败: {str(e)}")
            return self._create_error_summary(execution_steps, str(e))
    
    def _validate_single_step(
        self, 
        step: Dict[str, Any], 
        pcap_statements: List[SQLStatement]
    ) -> StepSQLValidationResult:
        """验证单个步骤"""
        step_number = step.get("step_number", 0)
        sql_statements = step.get("sql_statements", [])
        
        logger.debug(f"验证步骤 {step_number}，包含 {len(sql_statements)} 个SQL语句")
        
        if not sql_statements:
            return StepSQLValidationResult(
                step_number=step_number,
                expected_sql="",
                sql_type=SQLType.UNKNOWN,
                validation_status=ValidationStatus.NOT_FOUND,
                matched_statements=[],
                similarity_score=0.0,
                confidence=0.0,
                packet_numbers=[],
                error_message="步骤中没有SQL语句"
            )
        
        # 对于包含多个SQL的步骤，取第一个主要SQL进行校验
        primary_sql = sql_statements[0] if sql_statements else ""
        
        # 清理和标准化SQL
        normalized_expected = self._normalize_sql(primary_sql)
        expected_sql_type = self._classify_sql_type(primary_sql)
        
        # 在pcap语句中查找匹配
        best_match = None
        best_similarity = 0.0
        matched_statements = []
        
        for pcap_stmt in pcap_statements:
            normalized_pcap = self._normalize_sql(pcap_stmt.sql)
            
            # 计算相似度
            similarity = self._calculate_similarity(normalized_expected, normalized_pcap)
            
            if similarity > best_similarity:
                best_similarity = similarity
                best_match = pcap_stmt
            
            # 收集所有相关的匹配语句
            if similarity >= self.partial_match_threshold:
                matched_statements.append(pcap_stmt)
        
        # 确定校验状态
        validation_status = self._determine_validation_status(best_similarity)
        
        # 提取数据包编号
        packet_numbers = [stmt.packet_number for stmt in matched_statements]
        
        return StepSQLValidationResult(
            step_number=step_number,
            expected_sql=primary_sql,
            sql_type=expected_sql_type,
            validation_status=validation_status,
            matched_statements=matched_statements,
            similarity_score=best_similarity,
            confidence=best_match.confidence if best_match else 0.0,
            packet_numbers=packet_numbers
        )
    
    def _normalize_sql(self, sql: str) -> str:
        """标准化SQL语句用于比较"""
        if not sql:
            return ""
        
        # 转换为大写
        normalized = sql.upper().strip()
        
        # 移除注释
        normalized = re.sub(r'/\*.*?\*/', '', normalized, flags=re.DOTALL)
        normalized = re.sub(r'--.*$', '', normalized, flags=re.MULTILINE)
        
        # 标准化空格
        normalized = re.sub(r'\s+', ' ', normalized)
        
        # 移除末尾分号
        normalized = normalized.rstrip(';')
        
        # 移除引号差异（统一使用单引号）
        normalized = re.sub(r'"([^"]*)"', r"'\1'", normalized)
        
        return normalized.strip()
    
    def _classify_sql_type(self, sql: str) -> SQLType:
        """分类SQL语句类型"""
        sql_upper = sql.strip().upper()
        
        if sql_upper.startswith('SELECT') or sql_upper.startswith('WITH'):
            return SQLType.SELECT
        elif sql_upper.startswith('INSERT'):
            return SQLType.INSERT
        elif sql_upper.startswith('UPDATE'):
            return SQLType.UPDATE
        elif sql_upper.startswith('DELETE'):
            return SQLType.DELETE
        elif sql_upper.startswith('CREATE'):
            return SQLType.CREATE
        elif sql_upper.startswith('DROP'):
            return SQLType.DROP
        elif sql_upper.startswith('ALTER'):
            return SQLType.ALTER
        else:
            return SQLType.UNKNOWN
    
    def _calculate_similarity(self, sql1: str, sql2: str) -> float:
        """计算两个SQL语句的相似度"""
        if not sql1 or not sql2:
            return 0.0
        
        # 使用SequenceMatcher计算相似度
        matcher = SequenceMatcher(None, sql1, sql2)
        return matcher.ratio()
    
    def _determine_validation_status(self, similarity: float) -> ValidationStatus:
        """根据相似度确定校验状态"""
        if similarity >= 0.95:
            return ValidationStatus.MATCHED
        elif similarity >= self.similarity_threshold:
            return ValidationStatus.SIMILAR
        elif similarity >= self.partial_match_threshold:
            return ValidationStatus.PARTIAL_MATCHED
        else:
            return ValidationStatus.NOT_FOUND
    
    def _generate_summary(self, step_results: List[StepSQLValidationResult]) -> StepValidationSummary:
        """生成校验汇总"""
        total_steps = len(step_results)
        matched_steps = sum(1 for r in step_results if r.validation_status == ValidationStatus.MATCHED)
        partial_matched_steps = sum(1 for r in step_results if r.validation_status == ValidationStatus.PARTIAL_MATCHED)
        not_found_steps = sum(1 for r in step_results if r.validation_status == ValidationStatus.NOT_FOUND)
        similar_steps = sum(1 for r in step_results if r.validation_status == ValidationStatus.SIMILAR)
        error_steps = sum(1 for r in step_results if r.validation_status == ValidationStatus.ERROR)
        
        # 计算总体成功率（匹配 + 相似 + 部分匹配）
        success_count = matched_steps + similar_steps + partial_matched_steps
        overall_success_rate = success_count / total_steps if total_steps > 0 else 0.0
        
        return StepValidationSummary(
            total_steps=total_steps,
            matched_steps=matched_steps,
            partial_matched_steps=partial_matched_steps,
            not_found_steps=not_found_steps,
            similar_steps=similar_steps,
            error_steps=error_steps,
            overall_success_rate=overall_success_rate,
            step_results=step_results
        )
    
    def _create_error_summary(self, execution_steps: List[Dict[str, Any]], error_message: str) -> StepValidationSummary:
        """创建错误汇总"""
        step_results = []
        for i, step in enumerate(execution_steps):
            step_results.append(StepSQLValidationResult(
                step_number=step.get("step_number", i + 1),
                expected_sql="",
                sql_type=SQLType.UNKNOWN,
                validation_status=ValidationStatus.ERROR,
                matched_statements=[],
                similarity_score=0.0,
                confidence=0.0,
                packet_numbers=[],
                error_message=error_message
            ))
        
        return StepValidationSummary(
            total_steps=len(execution_steps),
            matched_steps=0,
            partial_matched_steps=0,
            not_found_steps=0,
            similar_steps=0,
            error_steps=len(execution_steps),
            overall_success_rate=0.0,
            step_results=step_results
        )

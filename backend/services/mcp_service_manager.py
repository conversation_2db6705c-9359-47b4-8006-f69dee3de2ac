import asyncio
import logging
import json
import os
from typing import Dict, Any, Optional, List
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client
from utils.config import Config

logger = logging.getLogger(__name__)

class MCPServiceManager:
    """MCP服务管理器 - 负责启动和管理MCP服务器实例"""
    
    def __init__(self):
        self.servers: Dict[str, ClientSession] = {}
        self.server_configs = self._load_mcp_configs()
        logger.info("MCP Service Manager initialized")
    
    def _load_mcp_configs(self) -> Dict[str, Dict[str, Any]]:
        """加载MCP服务器配置"""
        configs = {
            "dbhub-mysql": {
                "command": "npx",
                "args": ["-y", "@modelcontextprotocol/server-mysql"],
                "env": {
                    "MYSQL_HOST": Config.MYSQL_HOST,
                    "MYSQL_PORT": str(Config.MYSQL_PORT),
                    "MYSQL_USER": Config.MYSQL_USER,
                    "MYSQL_PASSWORD": Config.MYSQL_PASSWORD,
                    "MYSQL_DATABASE": Config.MYSQL_DATABASE
                },
                "description": "MySQL数据库操作MCP服务器"
            }
        }
        return configs

    def _create_dynamic_mysql_config(self, host: str, port: int, user: str, password: str, database: str) -> Dict[str, Any]:
        """创建动态MySQL MCP配置"""
        return {
            "command": "npx",
            "args": ["-y", "@modelcontextprotocol/server-mysql"],
            "env": {
                "MYSQL_HOST": host,
                "MYSQL_PORT": str(port),
                "MYSQL_USER": user,
                "MYSQL_PASSWORD": password,
                "MYSQL_DATABASE": database
            },
            "description": f"动态MySQL MCP服务器 - {host}:{port}/{database}"
        }
    
    async def start_server(self, server_name: str) -> bool:
        """启动指定的MCP服务器"""
        try:
            if server_name in self.servers:
                logger.info(f"MCP server {server_name} is already running")
                return True

            if server_name not in self.server_configs:
                logger.error(f"Unknown MCP server: {server_name}")
                return False

            config = self.server_configs[server_name]

            # 创建服务器参数
            server_params = StdioServerParameters(
                command=config["command"],
                args=config["args"],
                env=config.get("env", {})
            )

            # 启动MCP服务器
            logger.info(f"Starting MCP server: {server_name}")
            async with stdio_client(server_params) as (read, write):
                session = ClientSession(read, write)

                # 初始化会话
                await session.initialize()

            self.servers[server_name] = session
            logger.info(f"MCP server {server_name} started successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to start MCP server {server_name}: {str(e)}")
            return False

    async def start_dynamic_mysql_server(self, config_id: int, host: str, port: int, user: str, password: str, database: str) -> bool:
        """启动动态配置的MySQL MCP服务器"""
        try:
            # 创建唯一的服务器名称
            server_name = f"dbhub-mysql-{config_id}"

            # 如果服务器已经在运行，先停止它
            if server_name in self.servers:
                await self.stop_server(server_name)

            # 创建动态配置
            config = self._create_dynamic_mysql_config(host, port, user, password, database)

            # 创建服务器参数
            server_params = StdioServerParameters(
                command=config["command"],
                args=config["args"],
                env=config.get("env", {})
            )

            # 启动MCP服务器
            logger.info(f"Starting dynamic MySQL MCP server: {server_name} for {host}:{port}/{database}")
            async with stdio_client(server_params) as (read, write):
                session = ClientSession(read, write)

                # 初始化会话
                await session.initialize()

            self.servers[server_name] = session
            logger.info(f"Dynamic MySQL MCP server {server_name} started successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to start dynamic MySQL MCP server: {str(e)}")
            return False
    
    async def stop_server(self, server_name: str) -> bool:
        """停止指定的MCP服务器"""
        try:
            if server_name not in self.servers:
                logger.warning(f"MCP server {server_name} is not running")
                return True
            
            session = self.servers[server_name]
            await session.close()
            del self.servers[server_name]
            
            logger.info(f"MCP server {server_name} stopped successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to stop MCP server {server_name}: {str(e)}")
            return False
    
    async def call_tool(self, server_name: str, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """调用MCP工具"""
        try:
            # 确保服务器已启动
            if server_name not in self.servers:
                success = await self.start_server(server_name)
                if not success:
                    raise Exception(f"Failed to start MCP server: {server_name}")
            
            session = self.servers[server_name]
            
            # 调用工具
            logger.info(f"Calling MCP tool: {server_name}.{tool_name} with args: {arguments}")
            result = await session.call_tool(tool_name, arguments)
            
            logger.info(f"MCP tool call result: {result}")
            return result
            
        except Exception as e:
            logger.error(f"Failed to call MCP tool {server_name}.{tool_name}: {str(e)}")
            raise
    
    async def list_tools(self, server_name: str) -> List[Dict[str, Any]]:
        """获取指定服务器的工具列表"""
        try:
            # 确保服务器已启动
            if server_name not in self.servers:
                success = await self.start_server(server_name)
                if not success:
                    raise Exception(f"Failed to start MCP server: {server_name}")
            
            session = self.servers[server_name]
            
            # 获取工具列表
            tools = await session.list_tools()
            return tools.tools if hasattr(tools, 'tools') else []
            
        except Exception as e:
            logger.error(f"Failed to list tools for MCP server {server_name}: {str(e)}")
            return []
    
    async def get_server_status(self, server_name: str) -> Dict[str, Any]:
        """获取服务器状态"""
        is_running = server_name in self.servers
        config = self.server_configs.get(server_name, {})
        
        status = {
            "name": server_name,
            "running": is_running,
            "description": config.get("description", ""),
            "command": config.get("command", ""),
            "args": config.get("args", [])
        }
        
        if is_running:
            try:
                tools = await self.list_tools(server_name)
                status["tools_count"] = len(tools)
                status["tools"] = [{"name": tool.get("name", ""), "description": tool.get("description", "")} for tool in tools]
            except Exception as e:
                logger.error(f"Failed to get tools for {server_name}: {str(e)}")
                status["tools_count"] = 0
                status["tools"] = []
        
        return status
    
    async def get_all_servers_status(self) -> List[Dict[str, Any]]:
        """获取所有服务器状态"""
        statuses = []
        for server_name in self.server_configs.keys():
            status = await self.get_server_status(server_name)
            statuses.append(status)
        return statuses

    async def call_dynamic_mysql_tool(self, config_id: int, host: str, port: int, user: str, password: str, database: str, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """调用动态配置的MySQL MCP工具"""
        try:
            server_name = f"dbhub-mysql-{config_id}"

            # 确保动态服务器正在运行
            if server_name not in self.servers:
                if not await self.start_dynamic_mysql_server(config_id, host, port, user, password, database):
                    raise Exception(f"Failed to start dynamic MySQL MCP server for config {config_id}")

            session = self.servers[server_name]

            # 调用工具
            logger.info(f"Calling dynamic MySQL MCP tool: {server_name}.{tool_name} with args: {arguments}")
            result = await session.call_tool(tool_name, arguments)

            logger.info(f"Dynamic MySQL MCP tool call result: {result}")
            return result

        except Exception as e:
            logger.error(f"Failed to call dynamic MySQL MCP tool {tool_name} for config {config_id}: {str(e)}")
            raise

    async def shutdown_all(self):
        """关闭所有MCP服务器"""
        logger.info("Shutting down all MCP servers")
        for server_name in list(self.servers.keys()):
            await self.stop_server(server_name)
        logger.info("All MCP servers shut down")

# 全局MCP服务管理器实例
mcp_service_manager = MCPServiceManager()

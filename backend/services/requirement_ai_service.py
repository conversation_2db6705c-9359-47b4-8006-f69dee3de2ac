"""
协议需求AI服务
用于文档解析、需求提取和测试用例生成
"""
import logging
import json
import uuid
import re
import requests
from typing import List, Optional, Dict, Any
from datetime import datetime
from models.requirement_management import (
    DocumentParseRequest,
    DocumentParseResponse,
    ProtocolRequirementResponse,
    RequirementTraceability,
    RequirementTestCriteria,
    RequirementTypeEnum,
    ProtocolTypeEnum,
    RequirementPriorityEnum,
    RequirementStatusEnum,
    RequirementComplexityEnum,
    TestabilityEnum
)
from services.ai_service import ai_service  # 导入deepseek服务

logger = logging.getLogger(__name__)


class RequirementAIService:
    """协议需求AI服务"""
    
    def __init__(self):
        """初始化AI服务"""
        # qwen2.5vl:3b模型配置
        self.qwen_base_url = "http://192.168.70.21:11434"
        self.qwen_model = "qwen2.5vl:3b"
        
        # deepseek用于需求拆分
        self.deepseek_service = ai_service
        
        logger.info("协议需求AI服务初始化完成")
        logger.info(f"文档解析模型: {self.qwen_model} at {self.qwen_base_url}")
        logger.info("需求拆分模型: deepseek-chat")
    
    def _call_qwen_api(self, prompt: str, content: str = "") -> str:
        """调用qwen2.5vl:3b模型API"""
        try:
            url = f"{self.qwen_base_url}/api/generate"
            
            full_prompt = f"{prompt}\n\n文档内容:\n{content}" if content else prompt
            
            payload = {
                "model": self.qwen_model,
                "prompt": full_prompt,
                "stream": False,
                "options": {
                    "temperature": 0.3,
                    "top_p": 0.9,
                    "max_tokens": 4096
                }
            }
            
            logger.info(f"调用qwen API: {url}")
            logger.info(f"请求payload: {json.dumps(payload, ensure_ascii=False)[:200]}...")
            
            response = requests.post(url, json=payload, timeout=120)
            response.raise_for_status()
            
            result = response.json()
            qwen_response = result.get("response", "").strip()
            
            logger.info(f"qwen API响应: {qwen_response[:200]}...")
            
            return qwen_response
            
        except requests.exceptions.RequestException as e:
            logger.error(f"qwen API网络请求失败: {e}")
            raise Exception(f"文档解析模型网络请求失败: {str(e)}")
        except json.JSONDecodeError as e:
            logger.error(f"qwen API响应JSON解析失败: {e}")
            raise Exception(f"文档解析模型响应格式错误: {str(e)}")
        except Exception as e:
            logger.error(f"qwen API调用失败: {e}")
            raise Exception(f"文档解析模型调用失败: {str(e)}")
    
    async def _call_deepseek_for_requirement_split(self, requirement_text: str, protocol_type: str) -> List[Dict[str, Any]]:
        """使用deepseek进行需求拆分"""
        try:
            logger.info(f"调用deepseek进行需求拆分，协议类型: {protocol_type}")
            logger.info(f"需求文本: {requirement_text[:200]}...")
            
            system_prompt = """你是一个专业的协议需求分析专家。你的任务是将协议相关的文本拆分成具体的、可测试的需求条目。

对于每个需求，请提供以下信息：
- title: 需求标题（简洁明确）
- description: 详细描述
- requirement_type: 需求类型（functional/performance/security/compatibility/usability）
- priority: 优先级（high/medium/low）
- acceptance_criteria: 验收标准列表
- test_scenarios: 测试场景列表
- edge_cases: 边界情况列表（可选）

请以JSON格式返回结果，格式如下：
```json
[
  {
    "title": "需求标题",
    "description": "详细描述",
    "requirement_type": "functional",
    "priority": "high",
    "acceptance_criteria": ["标准1", "标准2"],
    "test_scenarios": ["场景1", "场景2"],
    "edge_cases": ["边界情况1"]
  }
]
```"""

            user_prompt = f"""请分析以下{protocol_type}协议相关的需求文本，并拆分为具体的需求条目：

{requirement_text}

请确保每个需求都是独立的、可测试的，并与{protocol_type}协议相关。"""

            response = await self.deepseek_service.generate_response(system_prompt, user_prompt)
            logger.info(f"deepseek响应: {response[:200]}...")
            
            # 解析JSON响应
            try:
                # 提取JSON部分
                if "```json" in response:
                    start = response.find("```json") + 7
                    end = response.find("```", start)
                    json_str = response[start:end].strip()
                else:
                    json_str = response.strip()
                
                requirements = json.loads(json_str)
                result = requirements if isinstance(requirements, list) else [requirements]
                logger.info(f"deepseek解析出 {len(result)} 个需求")
                return result
                
            except json.JSONDecodeError as e:
                logger.warning(f"JSON解析失败，使用备用解析: {e}")
                return self._parse_requirements_from_text(response, protocol_type)
                
        except Exception as e:
            logger.error(f"deepseek需求拆分失败: {e}")
            return []
    
    def _parse_requirements_from_text(self, text: str, protocol_type: str) -> List[Dict[str, Any]]:
        """从文本中解析需求（备用方法）"""
        try:
            requirements = []
            lines = text.split('\n')
            current_req = {}
            
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                    
                if line.startswith('- title:') or line.startswith('title:'):
                    if current_req:
                        requirements.append(current_req)
                    current_req = {'title': line.split(':', 1)[1].strip()}
                elif line.startswith('- description:') or line.startswith('description:'):
                    current_req['description'] = line.split(':', 1)[1].strip()
                elif line.startswith('- requirement_type:') or line.startswith('requirement_type:'):
                    current_req['requirement_type'] = line.split(':', 1)[1].strip()
                elif line.startswith('- priority:') or line.startswith('priority:'):
                    current_req['priority'] = line.split(':', 1)[1].strip()
            
            if current_req:
                requirements.append(current_req)
                
            # 补充缺失的字段
            for req in requirements:
                req.setdefault('requirement_type', 'functional')
                req.setdefault('priority', 'medium')
                req.setdefault('acceptance_criteria', [])
                req.setdefault('test_scenarios', [])
                req.setdefault('edge_cases', [])
                
            return requirements
            
        except Exception as e:
            logger.error(f"文本解析失败: {e}")
            return []
    
    async def parse_protocol_document(self, parse_request: DocumentParseRequest) -> DocumentParseResponse:
        """解析协议文档并提取需求"""
        try:
            logger.info(f"开始解析协议文档: {parse_request.document_title}")
            
            parse_id = str(uuid.uuid4())
            requirements = []
            
            # 模拟文档解析过程
            if parse_request.document_content:
                requirements = await self._parse_content_to_requirements(
                    parse_request.document_content,
                    parse_request
                )
            elif parse_request.document_url:
                # 从URL获取内容并解析
                content = await self._fetch_content_from_url(parse_request.document_url)
                requirements = await self._parse_content_to_requirements(content, parse_request)
            elif parse_request.document_file_path:
                # 从文件路径读取内容并解析
                content = await self._read_content_from_file(parse_request.document_file_path)
                requirements = await self._parse_content_to_requirements(content, parse_request)
            
            # 生成解析摘要
            parse_summary = {
                "total_requirements": len(requirements),
                "requirement_types": self._analyze_requirement_types(requirements),
                "complexity_distribution": self._analyze_complexity_distribution(requirements),
                "estimated_test_cases": sum(req.expected_test_cases for req in requirements)
            }
            
            return DocumentParseResponse(
                parse_id=parse_id,
                document_title=parse_request.document_title,
                total_requirements=len(requirements),
                requirements=requirements,
                parse_summary=parse_summary,
                warnings=[],
                errors=[],
                created_at=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"解析协议文档失败: {e}")
            raise
    
    async def _parse_content_to_requirements(self, content: str, parse_request: DocumentParseRequest) -> List[ProtocolRequirementResponse]:
        """将文档内容解析为需求列表"""
        try:
            logger.info(f"使用qwen2.5vl:3b模型解析文档内容，协议类型: {parse_request.protocol_type}")
            
            # 使用qwen模型进行文档解析
            parsing_prompt = f"""
请分析以下{parse_request.protocol_type}协议文档，提取出所有相关的技术需求。

分析任务：
1. 识别文档中的功能需求、性能需求、安全需求等
2. 提取具体的技术规范和约束条件
3. 识别协议相关的操作和流程
4. 找出测试验证点

请按以下格式输出需求摘要（每个需求一行）：
需求1: [需求标题] - [简短描述]
需求2: [需求标题] - [简短描述]
...

文档标题: {parse_request.document_title}
协议类型: {parse_request.protocol_type}
"""

            # 调用qwen模型
            analysis_result = self._call_qwen_api(parsing_prompt, content)
            logger.info(f"qwen模型分析结果: {analysis_result[:200]}...")
            
            # 使用deepseek进行需求拆分
            requirements_data = await self._call_deepseek_for_requirement_split(
                analysis_result, 
                parse_request.protocol_type.value
            )
            
            # 转换为ProtocolRequirementResponse对象
            requirements = []
            for req_data in requirements_data[:5]:  # 限制最多5个需求
                try:
                    requirement = await self._create_requirement_from_data(req_data, parse_request)
                    requirements.append(requirement)
                except Exception as e:
                    logger.error(f"创建需求对象失败: {e}")
                    continue
            
            # 如果没有解析出需求，创建默认需求
            if not requirements:
                logger.warning("未解析出需求，创建默认需求")
                requirements = await self._create_default_requirements(content, parse_request)
            
            logger.info(f"成功解析出 {len(requirements)} 个需求")
            return requirements
            
        except Exception as e:
            logger.error(f"解析内容为需求失败: {e}")
            # 返回默认需求而不是抛出异常
            return await self._create_default_requirements(content, parse_request)
    
    async def _create_requirement_from_data(self, req_data: Dict[str, Any], parse_request: DocumentParseRequest) -> ProtocolRequirementResponse:
        """从解析数据创建需求对象"""
        requirement_id = str(uuid.uuid4())
        current_time = datetime.now()
        
        # 映射需求类型
        req_type_map = {
            'functional': RequirementTypeEnum.FUNCTIONAL,
            'performance': RequirementTypeEnum.PERFORMANCE,
            'security': RequirementTypeEnum.SECURITY,
            'compatibility': RequirementTypeEnum.COMPATIBILITY,
            'usability': RequirementTypeEnum.USABILITY
        }
        
        # 映射优先级
        priority_map = {
            'high': RequirementPriorityEnum.HIGH,
            'medium': RequirementPriorityEnum.MEDIUM,
            'low': RequirementPriorityEnum.LOW
        }
        
        requirement_type = req_type_map.get(req_data.get('requirement_type', 'functional'), RequirementTypeEnum.FUNCTIONAL)
        priority = priority_map.get(req_data.get('priority', 'medium'), RequirementPriorityEnum.MEDIUM)
        
        # 创建测试准则
        test_criteria = RequirementTestCriteria(
            acceptance_criteria=req_data.get('acceptance_criteria', []),
            test_scenarios=req_data.get('test_scenarios', []),
            edge_cases=req_data.get('edge_cases', []),
            negative_cases=req_data.get('negative_cases', [])
        )
        
        # 创建追溯信息
        traceability = RequirementTraceability(
            source_document=parse_request.document_title,
            source_section=req_data.get('section', ''),
            extraction_method="ai_parsed"
        )
        
        # 估算复杂度和可测试性
        complexity = self._estimate_complexity(req_data.get('description', ''), req_data.get('acceptance_criteria', []))
        testability = self._estimate_testability(test_criteria)
        
        return ProtocolRequirementResponse(
            id=requirement_id,
            title=req_data.get('title', '未命名需求'),
            description=req_data.get('description', ''),
            requirement_type=requirement_type,
            protocol_type=parse_request.protocol_type,
            protocol_version=parse_request.protocol_version,
            priority=priority,
            complexity=complexity,
            testability=testability,
            status=RequirementStatusEnum.DRAFT,
            test_criteria=test_criteria,
            expected_test_cases=len(req_data.get('acceptance_criteria', [])) + len(req_data.get('test_scenarios', [])),
            actual_test_cases=0,
            test_coverage=0.0,
            traceability=traceability,
            dependencies=[],
            tags=parse_request.tags or [],
            author=parse_request.author,
            stakeholders=[],
            references=[],
            related_documents=[],
            notes=parse_request.notes,
            created_at=current_time,
            updated_at=current_time,
            version=1
        )
    
    async def _create_default_requirements(self, content: str, parse_request: DocumentParseRequest) -> List[ProtocolRequirementResponse]:
        """创建默认需求（当AI解析失败时使用）"""
        try:
            # 根据协议类型创建不同的默认需求
            if parse_request.protocol_type == ProtocolTypeEnum.MYSQL:
                return await self._create_mysql_requirements(content, parse_request)
            elif parse_request.protocol_type == ProtocolTypeEnum.POSTGRESQL:
                return await self._create_postgresql_requirements(content, parse_request)
            elif parse_request.protocol_type == ProtocolTypeEnum.MONGODB:
                return await self._create_mongodb_requirements(content, parse_request)
            elif parse_request.protocol_type == ProtocolTypeEnum.ORACLE:
                return await self._create_oracle_requirements(content, parse_request)
            elif parse_request.protocol_type == ProtocolTypeEnum.GAUSSDB:
                return await self._create_gaussdb_requirements(content, parse_request)
            else:
                return await self._create_generic_requirements(content, parse_request)
        except Exception as e:
            logger.error(f"创建默认需求失败: {e}")
            return []
    
    async def _create_mysql_requirements(self, content: str, parse_request: DocumentParseRequest) -> List[ProtocolRequirementResponse]:
        """创建MySQL协议相关需求"""
        requirements = []
        
        # 连接管理需求
        connection_req = await self._create_requirement(
            title="MySQL连接建立和管理",
            description="客户端必须能够与MySQL服务器建立安全可靠的连接，支持SSL/TLS加密，处理连接超时和重连机制",
            requirement_type=RequirementTypeEnum.FUNCTIONAL,
            parse_request=parse_request,
            section="连接管理",
            acceptance_criteria=[
                "支持TCP/IP连接方式",
                "支持SSL/TLS安全连接",
                "实现连接池机制",
                "处理连接超时和自动重连",
                "支持字符集和排序规则设置"
            ],
            test_scenarios=[
                "正常连接建立",
                "SSL连接验证",
                "连接超时处理",
                "大量并发连接",
                "连接池功能测试"
            ]
        )
        requirements.append(connection_req)
        
        # 认证需求
        auth_req = await self._create_requirement(
            title="MySQL用户认证机制",
            description="实现MySQL用户身份验证，支持多种认证插件，确保数据库访问安全",
            requirement_type=RequirementTypeEnum.SECURITY,
            parse_request=parse_request,
            section="用户认证",
            acceptance_criteria=[
                "支持mysql_native_password认证",
                "支持caching_sha2_password认证",
                "处理认证失败情况",
                "支持多因子认证",
                "实现密码策略验证"
            ],
            test_scenarios=[
                "正确密码认证",
                "错误密码处理",
                "不同认证插件测试",
                "认证超时测试",
                "密码强度验证"
            ]
        )
        requirements.append(auth_req)
        
        # SQL查询需求
        query_req = await self._create_requirement(
            title="SQL查询语句执行",
            description="正确解析和执行各种MySQL SQL查询语句，包括SELECT、INSERT、UPDATE、DELETE等操作",
            requirement_type=RequirementTypeEnum.FUNCTIONAL,
            parse_request=parse_request,
            section="SQL执行",
            acceptance_criteria=[
                "支持标准SQL语法",
                "支持MySQL特有语法扩展",
                "正确处理复杂查询",
                "支持预编译语句",
                "处理大结果集查询"
            ],
            test_scenarios=[
                "简单SELECT查询",
                "复杂JOIN查询",
                "事务处理",
                "批量操作",
                "存储过程调用"
            ]
        )
        requirements.append(query_req)
        
        # 事务处理需求
        transaction_req = await self._create_requirement(
            title="MySQL事务处理机制",
            description="实现ACID特性的事务处理，支持事务隔离级别设置，正确处理提交和回滚操作",
            requirement_type=RequirementTypeEnum.FUNCTIONAL,
            parse_request=parse_request,
            section="事务管理",
            acceptance_criteria=[
                "支持ACID特性",
                "实现四种隔离级别",
                "正确处理死锁",
                "支持分布式事务",
                "事务日志记录"
            ],
            test_scenarios=[
                "单事务提交回滚",
                "并发事务处理",
                "死锁检测恢复",
                "隔离级别验证",
                "分布式事务协调"
            ]
        )
        requirements.append(transaction_req)
        
        # 性能优化需求
        performance_req = await self._create_requirement(
            title="MySQL性能优化要求",
            description="查询执行性能优化，索引使用优化，查询计划分析，缓存机制实现",
            requirement_type=RequirementTypeEnum.PERFORMANCE,
            parse_request=parse_request,
            section="性能优化",
            acceptance_criteria=[
                "查询响应时间< 100ms",
                "支持查询缓存",
                "优化器选择最佳执行计划",
                "索引使用率> 80%",
                "并发连接数> 1000"
            ],
            test_scenarios=[
                "大数据量查询性能",
                "高并发访问测试",
                "索引效果验证",
                "缓存命中率测试",
                "查询计划分析"
            ]
        )
        requirements.append(performance_req)
        
        return requirements
    
    async def _create_postgresql_requirements(self, content: str, parse_request: DocumentParseRequest) -> List[ProtocolRequirementResponse]:
        """创建PostgreSQL协议相关需求"""
        requirements = []
        
        # PostgreSQL特有需求
        json_req = await self._create_requirement(
            title="PostgreSQL JSON数据类型支持",
            description="支持JSON和JSONB数据类型的存储、查询和索引操作",
            requirement_type=RequirementTypeEnum.FUNCTIONAL,
            parse_request=parse_request,
            section="数据类型",
            acceptance_criteria=[
                "支持JSON数据存储",
                "支持JSONB二进制格式",
                "JSON查询操作符",
                "JSON索引创建",
                "JSON路径查询"
            ]
        )
        requirements.append(json_req)
        
        # 扩展功能需求
        extension_req = await self._create_requirement(
            title="PostgreSQL扩展功能支持",
            description="支持PostgreSQL扩展机制，包括PostGIS、pg_stat_statements等常用扩展",
            requirement_type=RequirementTypeEnum.FUNCTIONAL,
            parse_request=parse_request,
            section="扩展功能",
            acceptance_criteria=[
                "扩展安装加载",
                "扩展函数调用",
                "扩展数据类型",
                "扩展操作符",
                "扩展配置管理"
            ]
        )
        requirements.append(extension_req)
        
        return requirements
    
    async def _create_mongodb_requirements(self, content: str, parse_request: DocumentParseRequest) -> List[ProtocolRequirementResponse]:
        """创建MongoDB协议相关需求"""
        requirements = []
        
        # 文档操作需求
        document_req = await self._create_requirement(
            title="MongoDB文档CRUD操作",
            description="支持MongoDB文档的创建、读取、更新、删除操作，包括复杂查询和聚合操作",
            requirement_type=RequirementTypeEnum.FUNCTIONAL,
            parse_request=parse_request,
            section="文档操作",
            acceptance_criteria=[
                "文档插入操作",
                "文档查询过滤",
                "文档更新修改",
                "文档删除操作",
                "批量操作支持"
            ]
        )
        requirements.append(document_req)
        
        # 聚合管道需求
        aggregation_req = await self._create_requirement(
            title="MongoDB聚合管道处理",
            description="实现MongoDB聚合框架，支持复杂的数据处理和分析操作",
            requirement_type=RequirementTypeEnum.FUNCTIONAL,
            parse_request=parse_request,
            section="聚合操作",
            acceptance_criteria=[
                "聚合管道构建",
                "多阶段数据处理",
                "分组统计操作",
                "数据转换操作",
                "条件过滤处理"
            ]
        )
        requirements.append(aggregation_req)
        
        return requirements
    
    async def _create_oracle_requirements(self, content: str, parse_request: DocumentParseRequest) -> List[ProtocolRequirementResponse]:
        """创建Oracle协议相关需求"""
        requirements = []
        
        # PL/SQL需求
        plsql_req = await self._create_requirement(
            title="Oracle PL/SQL程序处理",
            description="支持PL/SQL存储过程、函数、包的编译和执行",
            requirement_type=RequirementTypeEnum.FUNCTIONAL,
            parse_request=parse_request,
            section="PL/SQL支持",
            acceptance_criteria=[
                "存储过程调用",
                "函数执行",
                "包管理",
                "异常处理",
                "游标操作"
            ]
        )
        requirements.append(plsql_req)
        
        return requirements
    
    async def _create_gaussdb_requirements(self, content: str, parse_request: DocumentParseRequest) -> List[ProtocolRequirementResponse]:
        """创建GaussDB协议相关需求"""
        requirements = []
        
        # 分布式事务需求
        distributed_req = await self._create_requirement(
            title="GaussDB分布式事务处理",
            description="支持GaussDB分布式架构下的事务一致性和并发控制",
            requirement_type=RequirementTypeEnum.FUNCTIONAL,
            parse_request=parse_request,
            section="分布式事务",
            acceptance_criteria=[
                "分布式事务协调",
                "两阶段提交",
                "故障恢复",
                "数据一致性",
                "性能优化"
            ]
        )
        requirements.append(distributed_req)
        
        return requirements
    
    async def _create_generic_requirements(self, content: str, parse_request: DocumentParseRequest) -> List[ProtocolRequirementResponse]:
        """创建通用协议需求"""
        requirements = []
        
        # 通用连接需求
        generic_req = await self._create_requirement(
            title="通用网络协议连接",
            description="实现基本的网络协议连接功能，支持TCP/UDP通信",
            requirement_type=RequirementTypeEnum.FUNCTIONAL,
            parse_request=parse_request,
            section="网络连接",
            acceptance_criteria=[
                "TCP连接建立",
                "UDP数据传输",
                "连接状态管理",
                "错误处理",
                "超时控制"
            ]
        )
        requirements.append(generic_req)
        
        return requirements
    
    async def _create_requirement(
        self,
        title: str,
        description: str,
        requirement_type: RequirementTypeEnum,
        parse_request: DocumentParseRequest,
        section: str = "",
        acceptance_criteria: List[str] = None,
        test_scenarios: List[str] = None,
        edge_cases: List[str] = None,
        negative_cases: List[str] = None
    ) -> ProtocolRequirementResponse:
        """创建单个需求对象"""
        
        requirement_id = str(uuid.uuid4())
        current_time = datetime.now()
        
        # 创建测试准则
        test_criteria = RequirementTestCriteria(
            acceptance_criteria=acceptance_criteria or [],
            test_scenarios=test_scenarios or [],
            edge_cases=edge_cases or [],
            negative_cases=negative_cases or []
        )
        
        # 创建追溯信息
        traceability = RequirementTraceability(
            source_document=parse_request.document_title,
            source_section=section,
            extraction_method="ai_parsed"
        )
        
        # 估算复杂度和可测试性
        complexity = self._estimate_complexity(description, acceptance_criteria or [])
        testability = self._estimate_testability(test_criteria)
        priority = self._estimate_priority(requirement_type, complexity)
        
        return ProtocolRequirementResponse(
            id=requirement_id,
            title=title,
            description=description,
            requirement_type=requirement_type,
            protocol_type=parse_request.protocol_type,
            protocol_version=parse_request.protocol_version,
            priority=priority,
            complexity=complexity,
            testability=testability,
            status=RequirementStatusEnum.DRAFT,
            test_criteria=test_criteria,
            expected_test_cases=len(acceptance_criteria or []) + len(test_scenarios or []),
            actual_test_cases=0,
            test_coverage=0.0,
            traceability=traceability,
            dependencies=[],
            tags=parse_request.tags,
            author=parse_request.author,
            stakeholders=[],
            references=[],
            related_documents=[],
            notes=parse_request.notes,
            created_at=current_time,
            updated_at=current_time,
            version=1
        )
    
    def _estimate_complexity(self, description: str, acceptance_criteria: List[str]) -> RequirementComplexityEnum:
        """估算需求复杂度"""
        complexity_score = 0
        
        # 基于描述长度
        if len(description) > 200:
            complexity_score += 1
        
        # 基于验收标准数量
        criteria_count = len(acceptance_criteria)
        if criteria_count > 5:
            complexity_score += 2
        elif criteria_count > 3:
            complexity_score += 1
        
        # 基于关键词
        complex_keywords = ["分布式", "并发", "事务", "性能", "安全", "集群", "高可用"]
        for keyword in complex_keywords:
            if keyword in description:
                complexity_score += 1
        
        if complexity_score >= 4:
            return RequirementComplexityEnum.VERY_COMPLEX
        elif complexity_score >= 3:
            return RequirementComplexityEnum.COMPLEX
        elif complexity_score >= 1:
            return RequirementComplexityEnum.MEDIUM
        else:
            return RequirementComplexityEnum.SIMPLE
    
    def _estimate_testability(self, test_criteria: RequirementTestCriteria) -> TestabilityEnum:
        """估算可测试性"""
        testability_score = 0
        
        # 基于验收标准的可测试性
        if len(test_criteria.acceptance_criteria) >= 3:
            testability_score += 2
        elif len(test_criteria.acceptance_criteria) >= 1:
            testability_score += 1
        
        # 基于测试场景
        if len(test_criteria.test_scenarios) >= 3:
            testability_score += 2
        elif len(test_criteria.test_scenarios) >= 1:
            testability_score += 1
        
        # 基于边界用例
        if len(test_criteria.edge_cases) >= 2:
            testability_score += 1
        
        if testability_score >= 4:
            return TestabilityEnum.EASY
        elif testability_score >= 2:
            return TestabilityEnum.MEDIUM
        elif testability_score >= 1:
            return TestabilityEnum.DIFFICULT
        else:
            return TestabilityEnum.VERY_DIFFICULT
    
    def _estimate_priority(self, requirement_type: RequirementTypeEnum, complexity: RequirementComplexityEnum) -> RequirementPriorityEnum:
        """估算需求优先级"""
        if requirement_type in [RequirementTypeEnum.SECURITY, RequirementTypeEnum.FUNCTIONAL]:
            if complexity in [RequirementComplexityEnum.COMPLEX, RequirementComplexityEnum.VERY_COMPLEX]:
                return RequirementPriorityEnum.HIGH
            else:
                return RequirementPriorityEnum.MEDIUM
        elif requirement_type == RequirementTypeEnum.PERFORMANCE:
            return RequirementPriorityEnum.MEDIUM
        else:
            return RequirementPriorityEnum.LOW
    
    async def _fetch_content_from_url(self, url: str) -> str:
        """从URL获取文档内容"""
        # 这里应该实现HTTP请求获取内容
        # 目前返回模拟内容
        return f"模拟从URL获取的内容: {url}"
    
    async def _read_content_from_file(self, file_path: str) -> str:
        """从文件路径读取文档内容"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            logger.error(f"读取文件失败: {e}")
            return ""
    
    def _analyze_requirement_types(self, requirements: List[ProtocolRequirementResponse]) -> Dict[str, int]:
        """分析需求类型分布"""
        type_distribution = {}
        for req in requirements:
            req_type = req.requirement_type.value
            type_distribution[req_type] = type_distribution.get(req_type, 0) + 1
        return type_distribution
    
    def _analyze_complexity_distribution(self, requirements: List[ProtocolRequirementResponse]) -> Dict[str, int]:
        """分析复杂度分布"""
        complexity_distribution = {}
        for req in requirements:
            complexity = req.complexity.value
            complexity_distribution[complexity] = complexity_distribution.get(complexity, 0) + 1
        return complexity_distribution
    
    async def generate_test_cases_for_requirement(
        self,
        requirement: ProtocolRequirementResponse,
        count: int,
        focus_areas: List[str]
    ) -> List[Dict[str, Any]]:
        """为特定需求生成测试用例"""
        try:
            logger.info(f"为需求 {requirement.id} 生成 {count} 个测试用例")
            
            test_cases = []
            
            # 基于验收标准生成测试用例
            for i, criteria in enumerate(requirement.test_criteria.acceptance_criteria[:count]):
                test_case = await self._generate_test_case_from_criteria(
                    requirement, criteria, i + 1, focus_areas
                )
                test_cases.append(test_case)
            
            # 如果验收标准不够，基于测试场景生成
            remaining_count = count - len(test_cases)
            if remaining_count > 0:
                for i, scenario in enumerate(requirement.test_criteria.test_scenarios[:remaining_count]):
                    test_case = await self._generate_test_case_from_scenario(
                        requirement, scenario, len(test_cases) + i + 1, focus_areas
                    )
                    test_cases.append(test_case)
            
            # 如果还不够，生成通用测试用例
            remaining_count = count - len(test_cases)
            if remaining_count > 0:
                for i in range(remaining_count):
                    test_case = await self._generate_generic_test_case(
                        requirement, len(test_cases) + i + 1, focus_areas
                    )
                    test_cases.append(test_case)
            
            return test_cases[:count]
            
        except Exception as e:
            logger.error(f"为需求生成测试用例失败: {e}")
            raise
    
    async def _generate_test_case_from_criteria(
        self,
        requirement: ProtocolRequirementResponse,
        criteria: str,
        index: int,
        focus_areas: List[str]
    ) -> Dict[str, Any]:
        """基于验收标准生成测试用例"""
        
        return {
            "title": f"{requirement.title} - 验收标准测试 {index}",
            "description": f"验证需求 '{requirement.title}' 的验收标准: {criteria}",
            "requirement_id": requirement.id,
            "requirement_title": requirement.title,
            "module": requirement.module or "协议测试",
            "priority": requirement.priority.value,
            "database_type": requirement.protocol_type.value,
            "operation_type": self._determine_operation_type(criteria),
            "test_steps": [
                {
                    "step_number": 1,
                    "action": f"准备测试环境 - {requirement.protocol_type.value}",
                    "expected_result": "测试环境准备完成",
                    "test_data": ""
                },
                {
                    "step_number": 2,
                    "action": f"执行验证操作: {criteria}",
                    "expected_result": f"操作成功完成，满足标准: {criteria}",
                    "test_data": self._generate_test_data_for_criteria(criteria, requirement.protocol_type)
                },
                {
                    "step_number": 3,
                    "action": "验证结果正确性",
                    "expected_result": "结果符合预期，验收标准通过",
                    "test_data": ""
                }
            ],
            "expected_result": f"验收标准 '{criteria}' 验证通过",
            "tags": [requirement.protocol_type.value, "验收测试", requirement.requirement_type.value] + focus_areas,
            "automation_level": "manual",
            "estimated_time": 30
        }
    
    async def _generate_test_case_from_scenario(
        self,
        requirement: ProtocolRequirementResponse,
        scenario: str,
        index: int,
        focus_areas: List[str]
    ) -> Dict[str, Any]:
        """基于测试场景生成测试用例"""
        
        return {
            "title": f"{requirement.title} - 场景测试 {index}",
            "description": f"测试场景: {scenario}",
            "requirement_id": requirement.id,
            "requirement_title": requirement.title,
            "module": requirement.module or "协议测试",
            "priority": requirement.priority.value,
            "database_type": requirement.protocol_type.value,
            "operation_type": self._determine_operation_type(scenario),
            "test_steps": [
                {
                    "step_number": 1,
                    "action": f"设置测试场景: {scenario}",
                    "expected_result": "场景设置完成",
                    "test_data": ""
                },
                {
                    "step_number": 2,
                    "action": f"执行场景测试",
                    "expected_result": f"场景 '{scenario}' 执行成功",
                    "test_data": self._generate_test_data_for_scenario(scenario, requirement.protocol_type)
                },
                {
                    "step_number": 3,
                    "action": "检查场景结果",
                    "expected_result": "场景测试通过，功能正常",
                    "test_data": ""
                }
            ],
            "expected_result": f"测试场景 '{scenario}' 验证通过",
            "tags": [requirement.protocol_type.value, "场景测试", requirement.requirement_type.value] + focus_areas,
            "automation_level": "semi_auto",
            "estimated_time": 45
        }
    
    async def _generate_generic_test_case(
        self,
        requirement: ProtocolRequirementResponse,
        index: int,
        focus_areas: List[str]
    ) -> Dict[str, Any]:
        """生成通用测试用例"""
        
        return {
            "title": f"{requirement.title} - 功能测试 {index}",
            "description": f"针对需求 '{requirement.title}' 的综合功能测试",
            "requirement_id": requirement.id,
            "requirement_title": requirement.title,
            "module": requirement.module or "协议测试",
            "priority": requirement.priority.value,
            "database_type": requirement.protocol_type.value,
            "operation_type": "综合",
            "test_steps": [
                {
                    "step_number": 1,
                    "action": f"初始化 {requirement.protocol_type.value} 测试环境",
                    "expected_result": "环境初始化成功",
                    "test_data": ""
                },
                {
                    "step_number": 2,
                    "action": f"执行 {requirement.title} 相关功能",
                    "expected_result": "功能执行成功",
                    "test_data": self._generate_generic_test_data(requirement.protocol_type)
                },
                {
                    "step_number": 3,
                    "action": "验证功能输出",
                    "expected_result": "输出结果正确",
                    "test_data": ""
                },
                {
                    "step_number": 4,
                    "action": "清理测试环境",
                    "expected_result": "环境清理完成",
                    "test_data": ""
                }
            ],
            "expected_result": f"需求 '{requirement.title}' 功能测试通过",
            "tags": [requirement.protocol_type.value, "功能测试", requirement.requirement_type.value] + focus_areas,
            "automation_level": "manual",
            "estimated_time": 60
        }
    
    def _determine_operation_type(self, text: str) -> str:
        """根据文本确定操作类型"""
        text_lower = text.lower()
        
        if any(keyword in text_lower for keyword in ["select", "查询", "query", "检索"]):
            return "查询"
        elif any(keyword in text_lower for keyword in ["insert", "插入", "新增", "添加"]):
            return "插入"
        elif any(keyword in text_lower for keyword in ["update", "更新", "修改"]):
            return "更新"
        elif any(keyword in text_lower for keyword in ["delete", "删除", "移除"]):
            return "删除"
        elif any(keyword in text_lower for keyword in ["create", "创建", "建立"]):
            return "创建"
        elif any(keyword in text_lower for keyword in ["transaction", "事务"]):
            return "事务"
        else:
            return "综合"
    
    def _generate_test_data_for_criteria(self, criteria: str, protocol_type: ProtocolTypeEnum) -> str:
        """为验收标准生成测试数据"""
        if protocol_type == ProtocolTypeEnum.MYSQL:
            return "SELECT version(); SHOW STATUS; SHOW VARIABLES;"
        elif protocol_type == ProtocolTypeEnum.POSTGRESQL:
            return "SELECT version(); SELECT current_database();"
        elif protocol_type == ProtocolTypeEnum.MONGODB:
            return "db.version(); db.stats();"
        else:
            return f"测试数据 - {criteria}"
    
    def _generate_test_data_for_scenario(self, scenario: str, protocol_type: ProtocolTypeEnum) -> str:
        """为测试场景生成测试数据"""
        if protocol_type == ProtocolTypeEnum.MYSQL:
            return "CREATE TABLE test_table (id INT, name VARCHAR(50)); INSERT INTO test_table VALUES (1, 'test');"
        elif protocol_type == ProtocolTypeEnum.POSTGRESQL:
            return "CREATE TABLE test_table (id SERIAL, name TEXT); INSERT INTO test_table (name) VALUES ('test');"
        elif protocol_type == ProtocolTypeEnum.MONGODB:
            return "db.test_collection.insertOne({name: 'test', value: 123});"
        else:
            return f"场景测试数据 - {scenario}"
    
    def _generate_generic_test_data(self, protocol_type: ProtocolTypeEnum) -> str:
        """生成通用测试数据"""
        if protocol_type == ProtocolTypeEnum.MYSQL:
            return "SHOW DATABASES; USE test; SHOW TABLES;"
        elif protocol_type == ProtocolTypeEnum.POSTGRESQL:
            return "\\l; \\c test; \\dt;"
        elif protocol_type == ProtocolTypeEnum.MONGODB:
            return "show dbs; use test; show collections;"
        else:
            return "通用测试数据"


# 创建全局服务实例
requirement_ai_service = RequirementAIService()

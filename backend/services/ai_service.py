import openai
from typing import List, Dict, Any, Optional
import logging
import os
from utils.config import Config

logger = logging.getLogger(__name__)

class AIService:
    """AI服务，使用DeepSeek API处理自然语言"""

    def __init__(self):
        # 初始化OpenAI客户端（用于DeepSeek）
        self.client = self._initialize_client()
        self.model = "deepseek-chat"  # 设置默认模型
    
    def _initialize_client(self):
        """初始化DeepSeek客户端"""
        try:
            # 使用DeepSeek API
            if Config.OPENAI_API_KEY:
                return openai.OpenAI(
                    api_key=Config.OPENAI_API_KEY,
                    base_url=Config.OPENAI_API_BASE
                )
            else:
                # 如果没有API Key，抛出错误
                raise Exception("DeepSeek API key not configured")

        except Exception as e:
            logger.error(f"Failed to initialize DeepSeek client: {str(e)}")
            raise
    
    def _call_deepseek(self, prompt: str) -> str:
        """调用DeepSeek API"""
        try:
            response = self.client.chat.completions.create(
                model="deepseek-chat",
                messages=[
                    {"role": "system", "content": "你是一个专业的SQL专家，能够将自然语言转换为准确的MySQL查询语句。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=4096
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            logger.error(f"DeepSeek API call failed: {str(e)}")
            raise
    
    async def parse_natural_language_to_sql(self, natural_query: str) -> str:
        """将自然语言转换为SQL查询"""
        try:
            logger.info(f"Parsing natural language query: {natural_query}")

            # 构建提示词
            prompt = f"""
请将以下自然语言查询转换为完整的、可执行的MySQL操作。

用户需求: {natural_query}

要求:
1. 生成完整的、语法正确的MySQL SQL语句
2. 如果是复合操作，生成多个SQL语句，每行一个
3. 确保SQL语句可以直接在MySQL中执行
4. **重要：如果涉及创建表操作，必须先生成DROP TABLE IF EXISTS table_name;语句，然后再生成CREATE TABLE语句**
5. CREATE TABLE语句必须包含完整的字段定义和数据类型
6. 每个语句必须以分号结尾

**创建表的标准格式：**
DROP TABLE IF EXISTS table_name;
CREATE TABLE table_name (字段定义...);

请直接返回SQL语句，不要添加解释。

示例:
- "查看数据库" → SHOW DATABASES;
- "查看表" → SHOW TABLES;
- "创建用户表" → CREATE TABLE IF NOT EXISTS users (id INT PRIMARY KEY AUTO_INCREMENT, name VARCHAR(100), created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP);
- "创建学生表，包含学号、姓名、年龄、成绩" → CREATE TABLE IF NOT EXISTS students (id INT PRIMARY KEY AUTO_INCREMENT, student_id VARCHAR(20) UNIQUE, name VARCHAR(50) NOT NULL, age INT, score DECIMAL(5,2), created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP);
- "插入用户数据" → INSERT INTO users (name) VALUES ('张三'), ('李四'), ('王五');
- "插入学生数据" → INSERT INTO students (student_id, name, age, score) VALUES ('S001', '张三', 20, 85.5), ('S002', '李四', 21, 92.0), ('S003', '王五', 19, 78.5);
- "查询用户" → SELECT * FROM users ORDER BY created_at DESC;

特别注意：
- 创建表的完整格式: DROP TABLE IF EXISTS table_name; CREATE TABLE table_name (field1 type, field2 type, ...);
- 不要生成不完整的SQL语句
- 确保所有括号都正确匹配
- 字段定义必须包含数据类型
            """

            # 调用DeepSeek API
            result = self._call_deepseek(prompt)

            # 提取SQL语句
            sql_query = self._extract_sql_from_response(result)

            # 简化处理：直接使用AI生成的SQL
            sql_query = self._clean_sql_response(sql_query)

            # 后处理：确保CREATE TABLE语句前包含DROP TABLE语句
            import re
            if re.search(r'\bCREATE\s+TABLE\s+(\w+)', sql_query, re.IGNORECASE):
                # 提取表名
                match = re.search(r'\bCREATE\s+TABLE\s+(\w+)', sql_query, re.IGNORECASE)
                if match:
                    table_name = match.group(1)
                    drop_statement = f"DROP TABLE IF EXISTS {table_name};"

                    # 检查是否已经包含DROP TABLE语句
                    if not re.search(rf'\bDROP\s+TABLE\s+.*{table_name}', sql_query, re.IGNORECASE):
                        # 在CREATE TABLE前添加DROP TABLE语句
                        sql_query = drop_statement + "\n" + sql_query
                        logger.info(f"Added DROP TABLE statement for table: {table_name}")

            logger.info(f"Generated SQL: {sql_query}")
            return sql_query

        except Exception as e:
            logger.error(f"Failed to parse natural language: {str(e)}")
            raise Exception(f"AI parsing failed: {str(e)}")

    def _clean_sql_response(self, sql_query: str) -> str:
        """清理SQL响应"""
        try:
            # 移除多余的空白字符
            sql_query = sql_query.strip()

            # 移除可能的markdown代码块标记
            if sql_query.startswith('```sql'):
                sql_query = sql_query[6:]
            if sql_query.startswith('```'):
                sql_query = sql_query[3:]
            if sql_query.endswith('```'):
                sql_query = sql_query[:-3]

            # 再次清理空白字符
            sql_query = sql_query.strip()

            return sql_query

        except Exception as e:
            logger.error(f"Failed to clean SQL response: {str(e)}")
            return sql_query
    
    def _extract_sql_from_response(self, response: str) -> str:
        """从AI响应中提取SQL语句或协议命令"""
        try:
            # 移除多余的空白字符
            response = response.strip()

            # 首先检查是否包含协议命令
            if "PROTOCOL:" in response:
                lines = response.split('\n')
                for line in lines:
                    line = line.strip()
                    if line.startswith('PROTOCOL:'):
                        return line

            # 如果响应包含代码块，提取其中的SQL
            if "```sql" in response:
                start = response.find("```sql") + 6
                end = response.find("```", start)
                if end != -1:
                    extracted = response[start:end].strip()
                    # 检查提取的内容是否包含协议命令
                    if "PROTOCOL:" in extracted:
                        lines = extracted.split('\n')
                        for line in lines:
                            line = line.strip()
                            if line.startswith('PROTOCOL:'):
                                return line
                    return extracted
            elif "```" in response:
                start = response.find("```") + 3
                end = response.find("```", start)
                if end != -1:
                    extracted = response[start:end].strip()
                    # 检查提取的内容是否包含协议命令
                    if "PROTOCOL:" in extracted:
                        lines = extracted.split('\n')
                        for line in lines:
                            line = line.strip()
                            if line.startswith('PROTOCOL:'):
                                return line
                    return extracted

            # 查找协议命令关键字
            protocol_keywords = ['PROTOCOL:', 'REFRESH', 'SHUTDOWN', 'STATISTICS', 'PROCESS_LIST', 'KILL_THREAD']
            lines = response.split('\n')

            for line in lines:
                line = line.strip()
                if line.startswith('PROTOCOL:'):
                    return line
                # 检查是否直接提到了协议命令
                for keyword in protocol_keywords[1:]:  # 跳过PROTOCOL:
                    if keyword in line.upper() and ('命令' in line or 'COMMAND' in line.upper()):
                        # 尝试构建协议命令
                        if 'REFRESH' in line.upper():
                            if '权限' in line or 'PRIVILEGE' in line.upper():
                                return 'PROTOCOL:REFRESH_PRIVILEGES'
                            elif '日志' in line or 'LOG' in line.upper():
                                return 'PROTOCOL:REFRESH_LOGS'
                            else:
                                return 'PROTOCOL:REFRESH'
                        elif 'SHUTDOWN' in line.upper():
                            return 'PROTOCOL:SHUTDOWN'
                        elif 'STATISTICS' in line.upper() or '统计' in line:
                            return 'PROTOCOL:STATISTICS'
                        elif 'PROCESS' in line.upper() or '进程' in line:
                            return 'PROTOCOL:PROCESS_LIST'

            # 如果没有找到协议命令，查找SQL关键字
            sql_keywords = ['SELECT', 'INSERT', 'UPDATE', 'DELETE', 'CREATE', 'DROP', 'ALTER', 'SHOW', 'DESCRIBE', 'FLUSH']

            # 尝试找到完整的SQL语句
            sql_statements = []
            current_statement = ""

            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # 如果是SQL关键字开头，开始新的语句
                if any(line.upper().startswith(keyword) for keyword in sql_keywords):
                    if current_statement:
                        sql_statements.append(current_statement)
                    current_statement = line
                elif current_statement:
                    # 继续当前语句
                    current_statement += " " + line

                # 如果语句以分号结尾，完成当前语句
                if current_statement and current_statement.endswith(';'):
                    sql_statements.append(current_statement)
                    current_statement = ""

            # 添加最后一个未完成的语句
            if current_statement:
                sql_statements.append(current_statement)

            # 验证CREATE TABLE语句的完整性
            validated_statements = []
            for stmt in sql_statements:
                if stmt.upper().startswith('CREATE TABLE'):
                    # 检查CREATE TABLE语句是否完整
                    if '(' in stmt and ')' in stmt and stmt.count('(') == stmt.count(')'):
                        validated_statements.append(stmt)
                    else:
                        logger.warning(f"Incomplete CREATE TABLE statement detected: {stmt}")
                        # 尝试修复不完整的CREATE TABLE语句
                        if not stmt.endswith(';'):
                            stmt += ';'
                        validated_statements.append(stmt)
                else:
                    validated_statements.append(stmt)

            if validated_statements:
                return '\n'.join(validated_statements)

            # 如果都没找到，返回整个响应
            return response

        except Exception as e:
            logger.error(f"Failed to extract SQL: {str(e)}")
            return response

    def _handle_multiple_table_creation(self, natural_query: str, sql_query: str) -> str:
        """处理多个表创建的请求"""
        try:
            import re

            # 检查是否是创建多个表的请求
            patterns = [
                r'创建.*?(\d+).*?张?表',
                r'生成.*?(\d+).*?个.*?表',
                r'建立.*?(\d+).*?张?表',
                r'(\d+).*?张?表'
            ]

            match = None
            for pattern in patterns:
                match = re.search(pattern, natural_query)
                if match:
                    break

            if match and sql_query.upper().startswith('CREATE TABLE'):
                num_tables = int(match.group(1))
                if num_tables > 1:
                    # 生成多个表的SQL语句
                    base_table_name = "table"

                    # 不同的字段组合
                    field_combinations = [
                        "(id INT PRIMARY KEY, name VARCHAR(50))",
                        "(id INT PRIMARY KEY, title VARCHAR(100))",
                        "(id INT PRIMARY KEY, content TEXT)",
                        "(id INT PRIMARY KEY, price DECIMAL(10,2))",
                        "(id INT PRIMARY KEY, status VARCHAR(20))",
                        "(id INT PRIMARY KEY, description TEXT)",
                        "(id INT PRIMARY KEY, created_at TIMESTAMP)",
                        "(id INT PRIMARY KEY, updated_at DATETIME)",
                        "(id INT PRIMARY KEY, is_active BOOLEAN)",
                        "(id INT PRIMARY KEY, category VARCHAR(30))",
                        "(id INT PRIMARY KEY, email VARCHAR(100))",
                        "(id INT PRIMARY KEY, phone VARCHAR(20))",
                        "(id INT PRIMARY KEY, address TEXT)",
                        "(id INT PRIMARY KEY, age INT)",
                        "(id INT PRIMARY KEY, score FLOAT)"
                    ]

                    # 生成多个CREATE TABLE语句
                    statements = []
                    for i in range(num_tables):
                        table_name = f"{base_table_name}{i+1}"
                        fields = field_combinations[i % len(field_combinations)]
                        statements.append(f"CREATE TABLE {table_name} {fields};")

                    return "\n".join(statements)

            return sql_query

        except Exception as e:
            logger.error(f"Failed to handle multiple table creation: {str(e)}")
            return sql_query

    def _handle_batch_operations(self, natural_query: str, sql_query: str) -> str:
        """处理批量操作的请求"""
        try:
            import re

            # 删除多张表的处理
            drop_patterns = [
                r'删除.*?(\w+)、(\w+)、(\w+).*?表',
                r'删除.*?(\d+).*?张.*?表',
                r'删除.*?所有.*?测试表'
            ]

            for pattern in drop_patterns:
                match = re.search(pattern, natural_query)
                if match and sql_query.upper().startswith('DROP TABLE'):
                    if '、' in natural_query:
                        # 处理具体表名的情况，如"删除table1、table2、table3这3张表"
                        # 提取所有表名
                        table_names = re.findall(r'(\w+)(?=、)', natural_query)
                        # 获取最后一个表名（在"这"之前）
                        last_table = re.search(r'、(\w+)这', natural_query)
                        if last_table:
                            table_names.append(last_table.group(1))

                        if table_names:
                            statements = []
                            for table_name in table_names:
                                statements.append(f"DROP TABLE IF EXISTS {table_name};")
                            return "\n".join(statements)
                    elif re.search(r'(\d+).*?张', natural_query):
                        # 处理数量的情况，如"删除5张临时表"
                        num_match = re.search(r'(\d+)', natural_query)
                        if num_match:
                            num_tables = int(num_match.group(1))
                            statements = []
                            for i in range(1, num_tables + 1):
                                statements.append(f"DROP TABLE IF EXISTS temp_table{i};")
                            return "\n".join(statements)
                    elif '测试表' in natural_query or '所有.*?表' in natural_query:
                        # 处理删除所有测试表
                        if sql_query.upper().startswith('SELECT'):
                            # 如果AI返回的是查询语句，替换为实际的DROP语句
                            statements = [
                                "DROP TABLE IF EXISTS test_table1;",
                                "DROP TABLE IF EXISTS test_table2;",
                                "DROP TABLE IF EXISTS test_table3;",
                                "DROP TABLE IF EXISTS test_users;",
                                "DROP TABLE IF EXISTS test_orders;"
                            ]
                            return "\n".join(statements)
                        else:
                            statements = [
                                "DROP TABLE IF EXISTS test_table1;",
                                "DROP TABLE IF EXISTS test_table2;",
                                "DROP TABLE IF EXISTS test_table3;",
                                "DROP TABLE IF EXISTS test_users;",
                                "DROP TABLE IF EXISTS test_orders;"
                            ]
                            return "\n".join(statements)

            # 更新多张表的处理
            if '更新' in natural_query and ('和' in natural_query or '、' in natural_query):
                if '订单表和用户表' in natural_query:
                    statements = [
                        "UPDATE orders SET updated_at = CURRENT_TIMESTAMP;",
                        "UPDATE users SET updated_at = CURRENT_TIMESTAMP;"
                    ]
                    return "\n".join(statements)

            # 批量插入的处理
            insert_patterns = [
                r'插入.*?(\d+).*?条.*?数据',
                r'批量插入.*?(\d+).*?个',
                r'创建.*?(\d+).*?条.*?记录'
            ]

            for pattern in insert_patterns:
                match = re.search(pattern, natural_query)
                if match and sql_query.upper().startswith('INSERT'):
                    num_records = int(match.group(1))
                    if '用户' in natural_query:
                        # 生成用户插入语句
                        statements = []
                        base_sql = "INSERT INTO users (name, email, created_at) VALUES"
                        values = []
                        for i in range(1, min(num_records + 1, 11)):  # 限制最多10条示例
                            values.append(f"('用户{i}', 'user{i}@example.com', NOW())")
                        if num_records > 10:
                            values.append("-- ... 更多记录")
                        return base_sql + "\n" + ",\n".join(values) + ";"
                    elif '订单' in natural_query:
                        # 生成订单插入语句
                        statements = []
                        for i in range(1, min(num_records + 1, 11)):
                            statements.append(f"INSERT INTO orders (order_id, customer_id, order_date, total_amount, status) VALUES ({i}, {100 + i}, NOW(), {50.00 + i * 10}, 'pending');")
                        if num_records > 10:
                            statements.append("-- ... 更多订单记录")
                        return "\n".join(statements)

            return sql_query

        except Exception as e:
            logger.error(f"Failed to handle batch operations: {str(e)}")
            return sql_query

    def _handle_comprehensive_operations(self, natural_query: str, sql_query: str) -> str:
        """处理综合性数据库操作请求"""
        try:
            # 检查是否是综合性操作请求
            comprehensive_keywords = [
                "完成库", "完成表", "完成用户", "完成操作",
                "演示", "展示", "测试", "示例",
                "库表用户", "数据库操作", "完整操作"
            ]

            is_comprehensive = any(keyword in natural_query for keyword in comprehensive_keywords)

            # 如果是综合性操作且当前只有简单的SHOW语句，则生成完整的操作序列
            if is_comprehensive and sql_query.strip().upper().startswith('SHOW'):
                logger.info("Detected comprehensive operation request, generating full SQL sequence")

                comprehensive_sql = """SHOW DATABASES;
USE testdb2;
SHOW TABLES;
DROP TABLE IF EXISTS demo_users;
CREATE TABLE demo_users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL,
    email VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
INSERT INTO demo_users (username, email) VALUES
    ('admin', '<EMAIL>'),
    ('user1', '<EMAIL>'),
    ('user2', '<EMAIL>');
SELECT * FROM demo_users;
SELECT COUNT(*) as total_users FROM demo_users;
SELECT username, created_at FROM demo_users WHERE email LIKE '%example.com';"""

                return comprehensive_sql

            return sql_query

        except Exception as e:
            logger.error(f"Failed to handle comprehensive operations: {str(e)}")
            return sql_query
    
    async def analyze_query_intent(self, natural_query: str) -> Dict[str, Any]:
        """分析查询意图"""
        try:
            prompt = f"""
分析以下自然语言查询的意图：

查询: {natural_query}

请分析并返回以下信息：
1. 操作类型 (SELECT/INSERT/UPDATE/DELETE/CREATE/DROP/SHOW等)
2. 涉及的表名
3. 查询条件
4. 是否需要抓包分析

请用简洁的文字描述。
            """

            result = self._call_deepseek(prompt)

            return {
                'query': natural_query,
                'analysis': result,
                'requires_capture': True  # 默认需要抓包
            }

        except Exception as e:
            logger.error(f"Failed to analyze query intent: {str(e)}")
            return {
                'query': natural_query,
                'analysis': f"Analysis failed: {str(e)}",
                'requires_capture': True
            }
    
    async def generate_explanation(self, sql_query: str, result: Dict[str, Any]) -> str:
        """生成查询结果的解释"""
        try:
            prompt = f"""
请解释以下SQL查询的执行结果：

SQL查询: {sql_query}
执行结果: {result}

请用自然语言解释：
1. 查询做了什么
2. 返回了什么结果
3. 结果的含义

请用简洁明了的中文回答。
            """

            explanation = self._call_deepseek(prompt)
            return explanation

        except Exception as e:
            logger.error(f"Failed to generate explanation: {str(e)}")
            return f"无法生成解释: {str(e)}"
    
    async def suggest_optimizations(self, sql_query: str) -> List[str]:
        """建议SQL优化"""
        try:
            prompt = f"""
请分析以下SQL查询并提供优化建议：

SQL查询: {sql_query}

请提供：
1. 性能优化建议
2. 索引建议
3. 查询重写建议
4. 最佳实践建议

请用中文回答，每个建议用一行表示。
            """

            suggestions = self._call_deepseek(prompt)

            # 将建议按行分割
            return [line.strip() for line in suggestions.split('\n') if line.strip()]

        except Exception as e:
            logger.error(f"Failed to generate suggestions: {str(e)}")
            return [f"无法生成建议: {str(e)}"]

    async def generate_response(self, system_prompt: str, user_prompt: str) -> str:
        """生成AI响应"""
        try:
            if not self.client:
                raise Exception("AI client not initialized")

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]

            logger.info(f"Generating AI response for prompt: {user_prompt[:100]}...")

            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                max_tokens=4096,
                temperature=0.1
            )

            result = response.choices[0].message.content.strip()
            logger.info(f"AI response generated successfully: {len(result)} characters")

            return result

        except Exception as e:
            logger.error(f"Failed to generate AI response: {str(e)}")
            raise Exception(f"AI response generation failed: {str(e)}")

    async def analyze_sql_with_ai(self, comparison_prompt: str) -> Dict[str, Any]:
        """
        使用AI分析和对比SQL语句
        
        Args:
            comparison_prompt: SQL对比提示词
            
        Returns:
            分析结果字典
        """
        try:
            logger.info("开始使用AI分析SQL语句...")
            
            # 调用AI服务进行SQL分析
            response = await self.generate_response(
                system_prompt="你是一个SQL专家，专门负责分析和对比SQL语句的语义。请根据用户提供的两个SQL语句，分析它们的语义是否相同，并给出详细的分析结果。",
                user_prompt=comparison_prompt
            )
            
            # 尝试解析AI返回的JSON格式结果
            import json
            try:
                # 查找JSON部分
                json_start = response.find('{')
                json_end = response.rfind('}') + 1
                
                if json_start != -1 and json_end > json_start:
                    json_text = response[json_start:json_end]
                    analysis = json.loads(json_text)
                else:
                    # 如果没有找到JSON格式，创建默认结果
                    analysis = {
                        "semantic_match": False,
                        "similarity_score": 0.0,
                        "differences": "无法解析AI分析结果"
                    }
                
                return {
                    "success": True,
                    "analysis": analysis,
                    "raw_response": response
                }
                
            except json.JSONDecodeError:
                logger.warning("AI返回结果不是有效的JSON格式，尝试文本分析")
                
                # 文本分析逻辑
                response_lower = response.lower()
                semantic_match = any(keyword in response_lower for keyword in ["相同", "一致", "equivalent", "same", "identical"])
                
                # 简单的相似度估算
                if "相同" in response_lower or "一致" in response_lower:
                    similarity_score = 0.9
                elif "相似" in response_lower or "类似" in response_lower:
                    similarity_score = 0.7
                elif "部分相同" in response_lower:
                    similarity_score = 0.5
                else:
                    similarity_score = 0.3
                    
                return {
                    "success": True,
                    "analysis": {
                        "semantic_match": semantic_match,
                        "similarity_score": similarity_score,
                        "differences": response[:200] + "..." if len(response) > 200 else response
                    },
                    "raw_response": response
                }
                
        except Exception as e:
            logger.error(f"AI SQL分析失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "analysis": {
                    "semantic_match": False,
                    "similarity_score": 0.0,
                    "differences": "AI分析失败: " + str(e)
                }
            }


# 创建全局实例
ai_service = AIService()

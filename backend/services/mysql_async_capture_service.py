import asyncio
import logging
from typing import Dict, Any, Optional
from services.mysql_mcp_agent_service import mysql_mcp_agent_service
from services.mysql_service import MySQLService
from services.mysql_local_packet_capture_service import MySQLLocalPacketCaptureService
from services.database_config_service import DatabaseConfigService
from utils.config import Config

logger = logging.getLogger(__name__)

class MySQLAsyncCaptureService:
    """MySQL异步抓包服务 - 处理自然语言查询并触发异步抓包"""
    
    def __init__(self):
        self.mysql_service = None
        self.packet_service = MySQLLocalPacketCaptureService()
        self.db_config_service = DatabaseConfigService()
        logger.info("MySQL Async Capture Service initialized")
    
    def _get_mysql_service(self, config_id: Optional[int] = None):
        """获取MySQL服务实例"""
        if config_id and hasattr(self, '_current_config_id') and self._current_config_id == config_id:
            # 如果已经为当前config_id配置过，直接返回
            return self.mysql_service
        elif config_id:
            # 需要重新配置MySQL服务
            return None  # 强制重新创建
        elif self.mysql_service is None:
            # 使用默认配置
            mysql_config = Config.get_mysql_config()
            self.mysql_service = MySQLService(**mysql_config)
        return self.mysql_service

    async def _configure_mysql_service(self, config_id: int):
        """根据配置ID配置MySQL服务"""
        try:
            # 获取数据库配置
            config = await self.db_config_service.get_config(config_id)
            if not config:
                raise Exception(f"Database config not found: {config_id}")

            # 创建新的MySQL服务实例
            self.mysql_service = MySQLService(
                host=config.host,
                port=config.port,
                user=config.user,
                password=config.password,
                database=config.database_name
            )
            self._current_config_id = config_id

            logger.info(f"MySQL service configured for {config.host}:{config.port}/{config.database_name}")
            return self.mysql_service

        except Exception as e:
            logger.error(f"Failed to configure MySQL service: {str(e)}")
            raise
    
    async def process_natural_language_with_capture(
        self, 
        natural_query: str,
        config_id: Optional[int] = None,
        database_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """处理自然语言查询并触发异步抓包"""
        try:
            logger.info(f"Processing natural language query with capture: {natural_query}")
            
            # 获取数据库配置
            db_config = None
            if config_id:
                try:
                    db_config = await self.db_config_service.get_config(config_id)
                    logger.info(f"Using database config: {db_config.name}")
                except Exception as e:
                    logger.warning(f"Failed to get database config {config_id}: {str(e)}")
            
            # 确定数据库上下文
            database_context = database_name
            if db_config and not database_context:
                database_context = db_config.database_name
            
            # 第一步：获取数据库表结构信息
            schema_info = await self._get_database_schema_context(database_context, config_id)
            
            # 第二步：根据自然语言和表结构生成SQL
            sql_generation_result = await mysql_mcp_agent_service.generate_sql_from_natural_language(
                natural_query, database_context, config_id
            )
            
            if not sql_generation_result["success"]:
                return {
                    "success": False,
                    "error": f"SQL生成失败: {sql_generation_result.get('error', 'Unknown error')}",
                    "natural_query": natural_query
                }
            
            generated_sql = sql_generation_result["sql_query"]
            logger.info(f"Generated SQL: {generated_sql}")
            
            # 增强的抓包流程
            return await self.execute_sql_with_async_capture(
                generated_sql,
                config_id
            )
            
        except Exception as e:
            logger.error(f"Failed to process natural language query with capture: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "natural_query": natural_query
            }
    
    async def _get_database_schema_context(
        self,
        database_name: Optional[str] = None,
        config_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """获取数据库架构上下文信息"""
        try:
            schema_info = {
                "databases": [],
                "tables": [],
                "table_structures": {}
            }

            # 配置并获取MySQL服务
            if config_id:
                mysql_service = await self._configure_mysql_service(config_id)
            else:
                mysql_service = self._get_mysql_service()

            # 获取数据库列表
            try:
                databases = await mysql_service.get_databases()
                schema_info["databases"] = databases
                logger.info(f"Found databases: {databases}")
            except Exception as e:
                logger.warning(f"Failed to get databases: {str(e)}")

            # 如果指定了数据库，获取表信息
            if database_name:
                try:
                    tables = await mysql_service.get_tables(database_name)
                    schema_info["tables"] = tables
                    logger.info(f"Found tables in {database_name}: {tables}")

                    # 获取前几个表的结构信息（避免过多信息）
                    for table_info in tables[:5]:  # 限制只获取前5个表的结构
                        try:
                            table_name = table_info.get('name') if isinstance(table_info, dict) else str(table_info)
                            logger.info(f"Getting structure for table: {table_name}")
                            structure = await mysql_service.get_table_structure(table_name, database_name)
                            schema_info["table_structures"][table_name] = structure
                        except Exception as e:
                            logger.warning(f"Failed to get structure for table {table_name}: {str(e)}")

                except Exception as e:
                    logger.warning(f"Failed to get tables for database {database_name}: {str(e)}")

            return schema_info

        except Exception as e:
            logger.error(f"Failed to get database schema context: {str(e)}")
            return {"error": str(e)}
    
    async def generate_sql_preview(
        self, 
        natural_query: str,
        database_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """生成SQL预览（不执行，不抓包）"""
        try:
            logger.info(f"Generating SQL preview for: {natural_query}")
            
            # 获取数据库架构信息
            schema_info = await self._get_database_schema_context(database_name, config_id)
            
            # 生成SQL
            sql_result = await mysql_mcp_agent_service.generate_sql_from_natural_language(
                natural_query, database_name
            )
            
            return {
                "success": sql_result["success"],
                "natural_query": natural_query,
                "generated_sql": sql_result.get("sql_query", ""),
                "database_context": database_name,
                "schema_info": schema_info,
                "error": sql_result.get("error")
            }
            
        except Exception as e:
            logger.error(f"Failed to generate SQL preview: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "natural_query": natural_query
            }
    
    async def execute_sql_with_async_capture(
        self, 
        sql_query: str,
        config_id: Optional[int] = None,
        use_existing_capture: bool = False
    ) -> Dict[str, Any]:
        """执行SQL查询并进行异步抓包"""
        try:
            logger.info(f"[CAPTURE] Starting MySQL async capture execution")
            logger.info(f"[CAPTURE] SQL Query: {sql_query[:100]}...")
            logger.info(f"[CAPTURE] Config ID: {config_id}")
            logger.info(f"[CAPTURE] Use existing capture: {use_existing_capture}")

            # 第一步：获取数据库配置
            mysql_host = "**************"  # 默认值
            mysql_port = 3306
            mysql_service = None

            if config_id:
                try:
                    from services.database_config_service import database_config_service
                    config = await database_config_service.get_config(config_id)
                    if config:
                        mysql_host = config.host
                        mysql_port = config.port
                        logger.info(f"[CAPTURE] Database config: {mysql_host}:{mysql_port}")
                    else:
                        logger.warning(f"[CAPTURE] Failed to get database config {config_id}")
                except Exception as e:
                    logger.warning(f"[CAPTURE] Failed to get database config {config_id}: {str(e)}")
            
            # 第二步：启动抓包（更健壮的启动流程）
            packet_file = None
            capture_started = False
            
            if not use_existing_capture:
                try:
                    logger.info(f"[CAPTURE] Step 1: Starting packet capture for MySQL at {mysql_host}:{mysql_port}")
                    
                    # 确保抓包服务已经停止之前的会话
                    try:
                        await self.packet_service.stop_capture()
                        await asyncio.sleep(0.5)
                    except Exception as e:
                        logger.warning(f"停止抓包服务时发生异常: {e}")  # 记录停止失败的错误
                    
                    # 启动新的抓包会话
                    packet_file = await self.packet_service.start_capture(
                        mysql_host, mysql_port
                    )
                    capture_started = True
                    logger.info(f"[CAPTURE] ✅ Packet capture started successfully: {packet_file}")
                    
                    # 等待tcpdump完全启动
                    await asyncio.sleep(2.0)  # 增加启动等待时间
                    
                except Exception as e:
                    logger.error(f"[CAPTURE] ❌ Failed to start packet capture: {str(e)}")
                    # 抓包失败不应该阻止SQL执行，但要标记为失败
                    capture_started = False
            else:
                logger.info(f"[CAPTURE] Using existing capture session, skipping capture start")
                capture_started = True  # 假设已有抓包会话正在运行
            
            # 第三步：配置MySQL服务
            try:
                logger.info(f"[CAPTURE] Step 2: Configuring MySQL service")
                if config_id:
                    mysql_service = await self._configure_mysql_service(config_id)
                else:
                    mysql_service = self._get_mysql_service()
                logger.info(f"[CAPTURE] ✅ MySQL service configured")
            except Exception as e:
                logger.error(f"[CAPTURE] ❌ Failed to configure MySQL service: {str(e)}")
                raise Exception(f"MySQL service configuration failed: {str(e)}")
            
            # 第四步：执行SQL查询
            execution_result = None
            try:
                logger.info(f"[CAPTURE] Step 3: Executing SQL with enhanced capture mode")
                
                # 使用专门的抓包执行方法
                execution_result = await mysql_service.execute_query_for_capture(sql_query)
                logger.info(f"[CAPTURE] ✅ SQL execution completed")
                
                # 额外等待，确保所有数据包都被捕获
                await asyncio.sleep(1.5)
                
            except Exception as e:
                logger.error(f"[CAPTURE] ❌ SQL execution failed: {str(e)}")
                execution_result = {"error": str(e), "type": "error"}
            finally:
                # 第五步：强制关闭连接以确保捕获挥手包
                if mysql_service:
                    try:
                        logger.info(f"[CAPTURE] Step 4: Force closing MySQL connection")
                        await mysql_service.force_close_connection()
                        logger.info(f"[CAPTURE] ✅ MySQL connection force closed")
                        
                        # 等待挥手包完全传输
                        await asyncio.sleep(1.0)
                        
                    except Exception as e:
                        logger.warning(f"[CAPTURE] ⚠️ Failed to force close MySQL connection: {str(e)}")
            
            # 第六步：停止抓包（只有在启动抓包的情况下才停止）
            final_packet_file = None
            capture_quality = "failed"
            
            if not use_existing_capture and capture_started and packet_file:
                try:
                    logger.info(f"[CAPTURE] Step 5: Stopping packet capture")
                    final_packet_file = await self.packet_service.stop_capture()
                    
                    if final_packet_file:
                        logger.info(f"[CAPTURE] ✅ Packet capture completed: {final_packet_file}")
                        capture_quality = "success"
                        
                        # 检查抓包文件是否存在且有内容
                        import os
                        if os.path.exists(final_packet_file) and os.path.getsize(final_packet_file) > 0:
                            capture_quality = "success"
                            logger.info(f"[CAPTURE] ✅ Capture file verified: {os.path.getsize(final_packet_file)} bytes")
                        else:
                            capture_quality = "empty_file"
                            logger.warning(f"[CAPTURE] ⚠️ Capture file is empty or missing")
                    else:
                        capture_quality = "no_file"
                        logger.warning(f"[CAPTURE] ⚠️ No capture file returned")
                        
                except Exception as e:
                    logger.error(f"[CAPTURE] ❌ Failed to stop packet capture: {str(e)}")
                    capture_quality = "stop_failed"
            elif use_existing_capture:
                logger.info(f"[CAPTURE] Using existing capture session, not stopping capture")
                capture_quality = "existing_session"
                final_packet_file = packet_file  # 使用传入的抓包文件路径
            else:
                logger.warning(f"[CAPTURE] ⚠️ Packet capture was not started or failed to start")
                capture_quality = "not_started"
            
            # 构建增强的返回结果
            result = {
                "success": True,
                "natural_query": sql_query,  # 保持兼容性
                "sql_query": sql_query,
                "query_result": execution_result,
                "capture_file": final_packet_file or packet_file,
                "capture_quality": capture_quality,
                "config_id": config_id,
                "capture_duration": 30,  # 默认抓包时长
                "task_type": "ai_mysql_capture",
                "capture_details": {
                    "capture_started": capture_started,
                    "original_packet_file": packet_file,
                    "final_packet_file": final_packet_file,
                    "mysql_host": mysql_host,
                    "mysql_port": mysql_port,
                    "use_existing_capture": use_existing_capture
                }
            }
            
            logger.info(f"[CAPTURE] Final result: capture_quality={capture_quality}, file={final_packet_file or packet_file}")
            return result
            
        except Exception as e:
            logger.error(f"[CAPTURE] ❌ Critical error in execute_sql_with_async_capture: {str(e)}")
            
            # 即使发生错误，也要尝试停止抓包
            if capture_started:
                try:
                    await self.packet_service.stop_capture()
                except Exception as stop_error:
                    logger.error(f"[CAPTURE] Failed to stop capture during error handling: {stop_error}")
            
            return {
                "success": False,
                "error": str(e),
                "sql_query": sql_query,
                "capture_quality": "failed",
                "capture_file": None,
                "config_id": config_id
            }

# 全局MySQL异步抓包服务实例
mysql_async_capture_service = MySQLAsyncCaptureService()

"""
异步任务执行记录服务
用于记录和管理异步任务的详细执行情况，包括执行器选择、错误信息、性能指标等
"""

import json
import logging
import platform
import psutil
import sys
import time
import traceback
from datetime import datetime
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass, asdict
from enum import Enum

from services.mysql_service import MySQLService
from utils.config import Config
from utils.timezone_utils import get_current_time, format_time

logger = logging.getLogger(__name__)


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "PENDING"
    RUNNING = "RUNNING"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"
    CANCELLED = "CANCELLED"
    TIMEOUT = "TIMEOUT"


class ExecutorType(Enum):
    """执行器类型枚举"""
    PYTHON = "PYTHON"
    C = "C"
    JAVA = "JAVA"


@dataclass
class AsyncTaskExecutionLog:
    """异步任务执行记录数据类"""
    task_id: str
    task_type: str
    task_name: Optional[str] = None
    task_description: Optional[str] = None
    
    # 任务状态信息
    status: TaskStatus = TaskStatus.PENDING
    progress: int = 0
    current_step: Optional[str] = None
    
    # 执行器信息
    executor_type: ExecutorType = ExecutorType.PYTHON
    executor_version: Optional[str] = None
    executor_path: Optional[str] = None
    executor_selection_reason: Optional[str] = None
    fallback_from_executor: Optional[ExecutorType] = None
    
    # 配置信息
    database_config_id: Optional[int] = None
    server_config_id: Optional[int] = None
    capture_enabled: bool = True
    capture_duration: Optional[int] = None
    
    # 输入参数
    input_sql_query: Optional[str] = None
    input_mongo_query: Optional[str] = None
    input_natural_query: Optional[str] = None
    input_parameters: Optional[Dict[str, Any]] = None
    
    # 执行结果
    execution_result: Optional[Dict[str, Any]] = None
    output_files: Optional[List[str]] = None
    capture_files: Optional[List[str]] = None
    generated_sql: Optional[str] = None
    
    # 性能指标
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    duration_seconds: Optional[float] = None
    cpu_usage_percent: Optional[float] = None
    memory_usage_mb: Optional[float] = None
    
    # 错误信息
    error_code: Optional[str] = None
    error_message: Optional[str] = None
    error_details: Optional[Dict[str, Any]] = None
    error_stack_trace: Optional[str] = None
    retry_count: int = 0
    max_retries: int = 0
    
    # 环境信息
    worker_id: Optional[str] = None
    worker_host: Optional[str] = None
    python_version: Optional[str] = None
    system_info: Optional[Dict[str, Any]] = None
    environment_variables: Optional[Dict[str, str]] = None
    
    # 依赖任务
    parent_task_id: Optional[str] = None
    child_task_ids: Optional[List[str]] = None
    dependency_task_ids: Optional[List[str]] = None
    
    # 批量执行相关
    batch_id: Optional[str] = None
    batch_name: Optional[str] = None
    batch_total_count: Optional[int] = None
    batch_current_index: Optional[int] = None
    
    # 测试用例相关
    test_case_id: Optional[str] = None
    test_case_name: Optional[str] = None
    test_steps_executed: Optional[List[Dict[str, Any]]] = None
    test_validation_results: Optional[Dict[str, Any]] = None
    
    # 抓包相关
    network_interface: Optional[str] = None
    packet_count: Optional[int] = None
    packet_size_bytes: Optional[int] = None
    capture_filter: Optional[str] = None
    
    # 审计信息
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    created_by: Optional[str] = None

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典，用于数据库存储"""
        data = asdict(self)
        
        # 处理枚举类型
        if isinstance(data['status'], TaskStatus):
            data['status'] = data['status'].value
        if isinstance(data['executor_type'], ExecutorType):
            data['executor_type'] = data['executor_type'].value
        if isinstance(data['fallback_from_executor'], ExecutorType):
            data['fallback_from_executor'] = data['fallback_from_executor'].value
        
        # 处理时间类型
        for time_field in ['start_time', 'end_time', 'created_at', 'updated_at']:
            if data[time_field] and isinstance(data[time_field], datetime):
                data[time_field] = data[time_field].strftime('%Y-%m-%d %H:%M:%S')
        
        # 处理JSON字段
        json_fields = [
            'input_parameters', 'execution_result', 'output_files', 'capture_files',
            'error_details', 'system_info', 'environment_variables', 'child_task_ids',
            'dependency_task_ids', 'test_steps_executed', 'test_validation_results'
        ]
        for field in json_fields:
            if data[field] is not None:
                data[field] = json.dumps(data[field], ensure_ascii=False)
        
        return data


class AsyncTaskExecutionLogService:
    """异步任务执行记录服务"""
    
    def __init__(self):
        self.mysql_service = MySQLService(**Config.get_mysql_config())
    
    async def create_task_log(self, task_log: AsyncTaskExecutionLog) -> bool:
        """创建任务执行记录"""
        try:
            # 自动填充系统信息
            if not task_log.created_at:
                task_log.created_at = get_current_time()
            if not task_log.updated_at:
                task_log.updated_at = get_current_time()
            if not task_log.python_version:
                task_log.python_version = sys.version
            if not task_log.worker_host:
                task_log.worker_host = platform.node()
            if not task_log.system_info:
                task_log.system_info = self._get_system_info()
            
            data = task_log.to_dict()
            
            # 构建插入SQL
            columns = list(data.keys())
            placeholders = ', '.join(['%s'] * len(columns))
            sql = f"""
                INSERT INTO async_task_execution_logs ({', '.join(columns)})
                VALUES ({placeholders})
            """
            
            values = list(data.values())
            result = await self.mysql_service.execute_query(sql, tuple(values))
            
            logger.info(f"创建任务执行记录成功: {task_log.task_id}")
            return True
            
        except Exception as e:
            logger.error(f"创建任务执行记录失败: {e}")
            return False
    
    async def update_task_status(self, task_id: str, status: TaskStatus, 
                               current_step: Optional[str] = None,
                               progress: Optional[int] = None) -> bool:
        """更新任务状态"""
        try:
            update_fields = ["status = %s", "updated_at = %s"]
            values = [status.value, format_time(get_current_time())]
            
            if current_step is not None:
                update_fields.append("current_step = %s")
                values.append(current_step)
            
            if progress is not None:
                update_fields.append("progress = %s")
                values.append(progress)
            
            sql = f"""
                UPDATE async_task_execution_logs 
                SET {', '.join(update_fields)}
                WHERE task_id = %s
            """
            values.append(task_id)
            
            await self.mysql_service.execute_query(sql, tuple(values))
            logger.debug(f"更新任务状态成功: {task_id} -> {status.value}")
            return True
            
        except Exception as e:
            logger.error(f"更新任务状态失败: {e}")
            return False
    
    async def record_task_start(self, task_id: str, executor_type: ExecutorType,
                              executor_path: Optional[str] = None,
                              executor_selection_reason: Optional[str] = None) -> bool:
        """记录任务开始执行"""
        try:
            start_time = get_current_time()
            
            sql = """
                UPDATE async_task_execution_logs 
                SET status = %s, start_time = %s, executor_type = %s, 
                    executor_path = %s, executor_selection_reason = %s,
                    cpu_usage_percent = %s, memory_usage_mb = %s, updated_at = %s
                WHERE task_id = %s
            """
            
            # 获取当前系统资源使用情况
            cpu_percent = psutil.cpu_percent(interval=1)
            memory_mb = psutil.virtual_memory().used / 1024 / 1024
            
            values = (
                TaskStatus.RUNNING.value, format_time(start_time), executor_type.value,
                executor_path, executor_selection_reason, cpu_percent, memory_mb,
                format_time(get_current_time()), task_id
            )
            
            await self.mysql_service.execute_query(sql, values)
            logger.info(f"记录任务开始执行: {task_id}, 执行器: {executor_type.value}")
            return True
            
        except Exception as e:
            logger.error(f"记录任务开始执行失败: {e}")
            return False
    
    def _get_system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        try:
            return {
                "platform": platform.platform(),
                "system": platform.system(),
                "machine": platform.machine(),
                "processor": platform.processor(),
                "python_version": platform.python_version(),
                "cpu_count": psutil.cpu_count(),
                "memory_total_gb": round(psutil.virtual_memory().total / 1024 / 1024 / 1024, 2),
                "disk_usage": {
                    "total_gb": round(psutil.disk_usage('/').total / 1024 / 1024 / 1024, 2),
                    "used_gb": round(psutil.disk_usage('/').used / 1024 / 1024 / 1024, 2),
                    "free_gb": round(psutil.disk_usage('/').free / 1024 / 1024 / 1024, 2)
                }
            }
        except Exception as e:
            logger.warning(f"获取系统信息失败: {e}")
            return {"error": str(e)}


    async def record_task_completion(self, task_id: str, execution_result: Dict[str, Any],
                                   output_files: Optional[List[str]] = None,
                                   capture_files: Optional[List[str]] = None) -> bool:
        """记录任务完成"""
        try:
            end_time = get_current_time()

            # 获取开始时间以计算执行时长
            start_time_result = await self.mysql_service.execute_query(
                "SELECT start_time FROM async_task_execution_logs WHERE task_id = %s",
                (task_id,)
            )

            duration_seconds = None
            if start_time_result and start_time_result.get('data'):
                start_time_data = start_time_result['data'][0].get('start_time')
                if start_time_data:
                    if isinstance(start_time_data, str):
                        start_time = datetime.strptime(start_time_data, '%Y-%m-%d %H:%M:%S')
                        # 确保时区一致
                        if start_time.tzinfo is None:
                            from utils.timezone_utils import CHINA_TIMEZONE
                            start_time = start_time.replace(tzinfo=CHINA_TIMEZONE)
                    else:
                        start_time = start_time_data
                        # 确保时区一致
                        if start_time.tzinfo is None:
                            from utils.timezone_utils import CHINA_TIMEZONE
                            start_time = start_time.replace(tzinfo=CHINA_TIMEZONE)
                    duration_seconds = (end_time - start_time).total_seconds()

            # 获取当前系统资源使用情况
            cpu_percent = psutil.cpu_percent(interval=1)
            memory_mb = psutil.virtual_memory().used / 1024 / 1024

            sql = """
                UPDATE async_task_execution_logs
                SET status = %s, end_time = %s, duration_seconds = %s,
                    execution_result = %s, output_files = %s, capture_files = %s,
                    cpu_usage_percent = %s, memory_usage_mb = %s, updated_at = %s
                WHERE task_id = %s
            """

            values = (
                TaskStatus.COMPLETED.value, format_time(end_time), duration_seconds,
                json.dumps(execution_result, ensure_ascii=False) if execution_result else None,
                json.dumps(output_files, ensure_ascii=False) if output_files else None,
                json.dumps(capture_files, ensure_ascii=False) if capture_files else None,
                cpu_percent, memory_mb, format_time(get_current_time()), task_id
            )

            await self.mysql_service.execute_query(sql, values)
            logger.info(f"记录任务完成: {task_id}, 执行时长: {duration_seconds}秒")
            return True

        except Exception as e:
            logger.error(f"记录任务完成失败: {e}")
            return False

    async def record_task_failure(self, task_id: str, error_code: Optional[str] = None,
                                error_message: Optional[str] = None,
                                error_details: Optional[Dict[str, Any]] = None,
                                fallback_from_executor: Optional[ExecutorType] = None) -> bool:
        """记录任务失败"""
        try:
            end_time = get_current_time()

            # 获取开始时间以计算执行时长
            start_time_result = await self.mysql_service.execute_query(
                "SELECT start_time FROM async_task_execution_logs WHERE task_id = %s",
                (task_id,)
            )

            duration_seconds = None
            if start_time_result and start_time_result.get('data'):
                start_time_data = start_time_result['data'][0].get('start_time')
                if start_time_data:
                    if isinstance(start_time_data, str):
                        start_time = datetime.strptime(start_time_data, '%Y-%m-%d %H:%M:%S')
                        # 确保时区一致
                        if start_time.tzinfo is None:
                            from utils.timezone_utils import CHINA_TIMEZONE
                            start_time = start_time.replace(tzinfo=CHINA_TIMEZONE)
                    else:
                        start_time = start_time_data
                        # 确保时区一致
                        if start_time.tzinfo is None:
                            from utils.timezone_utils import CHINA_TIMEZONE
                            start_time = start_time.replace(tzinfo=CHINA_TIMEZONE)
                    duration_seconds = (end_time - start_time).total_seconds()

            # 获取错误堆栈跟踪
            error_stack_trace = traceback.format_exc() if error_message else None

            sql = """
                UPDATE async_task_execution_logs
                SET status = %s, end_time = %s, duration_seconds = %s,
                    error_code = %s, error_message = %s, error_details = %s,
                    error_stack_trace = %s, fallback_from_executor = %s, updated_at = %s
                WHERE task_id = %s
            """

            values = (
                TaskStatus.FAILED.value, format_time(end_time), duration_seconds,
                error_code, error_message,
                json.dumps(error_details, ensure_ascii=False) if error_details else None,
                error_stack_trace,
                fallback_from_executor.value if fallback_from_executor else None,
                format_time(get_current_time()), task_id
            )

            await self.mysql_service.execute_query(sql, values)
            logger.error(f"记录任务失败: {task_id}, 错误: {error_message}")
            return True

        except Exception as e:
            logger.error(f"记录任务失败失败: {e}")
            return False

    async def record_executor_fallback(self, task_id: str, from_executor: ExecutorType,
                                     to_executor: ExecutorType, reason: str) -> bool:
        """记录执行器回退"""
        try:
            sql = """
                UPDATE async_task_execution_logs
                SET executor_type = %s, fallback_from_executor = %s,
                    executor_selection_reason = %s, updated_at = %s
                WHERE task_id = %s
            """

            values = (
                to_executor.value, from_executor.value, reason,
                format_time(get_current_time()), task_id
            )

            await self.mysql_service.execute_query(sql, values)
            logger.warning(f"记录执行器回退: {task_id}, {from_executor.value} -> {to_executor.value}, 原因: {reason}")
            return True

        except Exception as e:
            logger.error(f"记录执行器回退失败: {e}")
            return False

    async def increment_retry_count(self, task_id: str) -> bool:
        """增加重试次数"""
        try:
            sql = """
                UPDATE async_task_execution_logs
                SET retry_count = retry_count + 1, updated_at = %s
                WHERE task_id = %s
            """

            await self.mysql_service.execute_query(sql, (format_time(get_current_time()), task_id))
            logger.info(f"增加任务重试次数: {task_id}")
            return True

        except Exception as e:
            logger.error(f"增加重试次数失败: {e}")
            return False

    async def get_task_log(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务执行记录"""
        try:
            sql = "SELECT * FROM async_task_execution_logs WHERE task_id = %s"
            result = await self.mysql_service.execute_query(sql, (task_id,))

            if result and result.get('data'):
                return result['data'][0]
            return None

        except Exception as e:
            logger.error(f"获取任务执行记录失败: {e}")
            return None

    async def get_task_logs_by_type(self, task_type: str, limit: int = 100) -> List[Dict[str, Any]]:
        """根据任务类型获取执行记录"""
        try:
            sql = """
                SELECT * FROM async_task_execution_logs
                WHERE task_type = %s
                ORDER BY created_at DESC
                LIMIT %s
            """
            result = await self.mysql_service.execute_query(sql, (task_type, limit))

            if result and result.get('data'):
                return result['data']
            return []

        except Exception as e:
            logger.error(f"获取任务类型执行记录失败: {e}")
            return []

    async def get_failed_tasks(self, hours: int = 24) -> List[Dict[str, Any]]:
        """获取指定时间内的失败任务"""
        try:
            sql = """
                SELECT * FROM async_task_execution_logs
                WHERE status = 'FAILED'
                AND created_at >= DATE_SUB(NOW(), INTERVAL %s HOUR)
                ORDER BY created_at DESC
            """
            result = await self.mysql_service.execute_query(sql, (hours,))

            if result and result.get('data'):
                return result['data']
            return []

        except Exception as e:
            logger.error(f"获取失败任务记录失败: {e}")
            return []

    async def get_executor_statistics(self, days: int = 7) -> Dict[str, Any]:
        """获取执行器使用统计"""
        try:
            sql = """
                SELECT
                    executor_type,
                    COUNT(*) as total_tasks,
                    SUM(CASE WHEN status = 'COMPLETED' THEN 1 ELSE 0 END) as completed_tasks,
                    SUM(CASE WHEN status = 'FAILED' THEN 1 ELSE 0 END) as failed_tasks,
                    AVG(duration_seconds) as avg_duration,
                    COUNT(CASE WHEN fallback_from_executor IS NOT NULL THEN 1 END) as fallback_count
                FROM async_task_execution_logs
                WHERE created_at >= DATE_SUB(NOW(), INTERVAL %s DAY)
                GROUP BY executor_type
            """
            result = await self.mysql_service.execute_query(sql, (days,))

            if result and result.get('data'):
                return {
                    'statistics': result['data'],
                    'period_days': days
                }
            return {'statistics': [], 'period_days': days}

        except Exception as e:
            logger.error(f"获取执行器统计失败: {e}")
            return {'statistics': [], 'period_days': days}


# 全局服务实例
async_task_execution_log_service = AsyncTaskExecutionLogService()

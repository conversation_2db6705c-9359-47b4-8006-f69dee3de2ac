"""
GaussDB C语言执行器服务
使用C语言libpq程序执行SQL语句，支持持久连接模式
"""

import os
import json
import asyncio
import subprocess
import logging
from typing import Dict, Any, Optional, List
from pathlib import Path

logger = logging.getLogger(__name__)


class GaussDBCExecutorService:
    """GaussDB C语言执行器服务"""
    
    def __init__(self):
        self.executor_path = None
        self.persistent_process = None
        self.is_connected = False
        self._initialize_executor()
    
    def _initialize_executor(self):
        """初始化C语言执行器"""
        # 查找C语言执行器路径
        current_dir = Path(__file__).parent.parent
        executor_dir = current_dir / "c_executors"
        executor_file = executor_dir / "gaussdb_libpq_executor"
        
        if executor_file.exists():
            self.executor_path = str(executor_file)
            logger.info(f"Found C executor at: {self.executor_path}")
        else:
            logger.warning(f"C executor not found at: {executor_file}")
            # 尝试编译
            self._compile_executor(executor_dir)
    
    def _compile_executor(self, executor_dir: Path):
        """编译C语言执行器"""
        try:
            logger.info("Attempting to compile C executor...")
            
            # 检查源文件是否存在
            source_file = executor_dir / "gaussdb_libpq_executor.c"
            makefile = executor_dir / "Makefile"
            
            if not source_file.exists():
                logger.error(f"Source file not found: {source_file}")
                return
            
            if not makefile.exists():
                logger.error(f"Makefile not found: {makefile}")
                return
            
            # 执行编译
            result = subprocess.run(
                ["make", "-C", str(executor_dir)],
                capture_output=True,
                text=True,
                timeout=60
            )
            
            if result.returncode == 0:
                executor_file = executor_dir / "gaussdb_libpq_executor"
                if executor_file.exists():
                    self.executor_path = str(executor_file)
                    logger.info("C executor compiled successfully")
                else:
                    logger.error("Compilation succeeded but executable not found")
            else:
                logger.error(f"Compilation failed: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            logger.error("Compilation timeout")
        except Exception as e:
            logger.error(f"Compilation error: {e}")
    
    async def create_persistent_connection(self, host: str, port: int, user: str, 
                                         password: str, database: str) -> bool:
        """创建持久连接"""
        if not self.executor_path:
            raise Exception("C executor not available")
        
        try:
            # 启动持久连接进程
            self.persistent_process = await asyncio.create_subprocess_exec(
                self.executor_path, "--persistent",
                stdin=asyncio.subprocess.PIPE,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            # 发送连接命令
            connect_cmd = f"connect {host} {port} {user} {password} {database}\n"
            self.persistent_process.stdin.write(connect_cmd.encode())
            await self.persistent_process.stdin.drain()
            
            # 读取响应
            response_line = await self.persistent_process.stdout.readline()
            response = json.loads(response_line.decode().strip())
            
            if response.get("success"):
                self.is_connected = True
                logger.info("C executor persistent connection established")
                return True
            else:
                logger.error(f"C executor connection failed: {response.get('error')}")
                await self.close_persistent_connection()
                return False
                
        except Exception as e:
            logger.error(f"Failed to create persistent connection: {e}")
            await self.close_persistent_connection()
            return False
    
    async def execute_sql_with_persistent_connection(self, sql: str) -> Dict[str, Any]:
        """使用持久连接执行SQL"""
        if not self.is_connected or not self.persistent_process:
            raise Exception("No persistent connection available")
        
        try:
            # 发送执行命令
            execute_cmd = f"execute {sql}\n"
            self.persistent_process.stdin.write(execute_cmd.encode())
            await self.persistent_process.stdin.drain()
            
            # 读取响应
            response_line = await self.persistent_process.stdout.readline()
            response = json.loads(response_line.decode().strip())
            
            return response
            
        except Exception as e:
            logger.error(f"Failed to execute SQL with persistent connection: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def close_persistent_connection(self):
        """关闭持久连接"""
        if self.persistent_process:
            try:
                # 发送关闭命令
                if self.persistent_process.stdin and not self.persistent_process.stdin.is_closing():
                    self.persistent_process.stdin.write(b"quit\n")
                    await self.persistent_process.stdin.drain()
                    self.persistent_process.stdin.close()
                
                # 等待进程结束
                try:
                    await asyncio.wait_for(self.persistent_process.wait(), timeout=5.0)
                except asyncio.TimeoutError:
                    logger.warning("C executor process did not terminate gracefully, killing it")
                    self.persistent_process.kill()
                    await self.persistent_process.wait()
                
            except Exception as e:
                logger.error(f"Error closing persistent connection: {e}")
            finally:
                self.persistent_process = None
                self.is_connected = False
                logger.info("C executor persistent connection closed")
    
    async def execute_single_sql(self, host: str, port: int, user: str, 
                                password: str, database: str, sql: str) -> Dict[str, Any]:
        """执行单个SQL语句（非持久连接模式）"""
        if not self.executor_path:
            raise Exception("C executor not available")
        
        try:
            # 先连接
            connect_result = await asyncio.create_subprocess_exec(
                self.executor_path, "connect", host, str(port), user, password, database,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await connect_result.communicate()
            
            if connect_result.returncode != 0:
                return {
                    "success": False,
                    "error": f"Connection failed: {stderr.decode()}"
                }
            
            # 执行SQL
            execute_result = await asyncio.create_subprocess_exec(
                self.executor_path, "execute", sql,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await execute_result.communicate()
            
            if execute_result.returncode == 0:
                response = json.loads(stdout.decode().strip())
                return response
            else:
                return {
                    "success": False,
                    "error": f"Execution failed: {stderr.decode()}"
                }
                
        except Exception as e:
            logger.error(f"Failed to execute single SQL: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def is_available(self) -> bool:
        """检查C执行器是否可用"""
        return self.executor_path is not None and os.path.exists(self.executor_path)
    
    async def test_connection(self, host: str, port: int, user: str, 
                            password: str, database: str) -> Dict[str, Any]:
        """测试数据库连接"""
        if not self.executor_path:
            return {
                "success": False,
                "error": "C executor not available"
            }
        
        try:
            result = await asyncio.create_subprocess_exec(
                self.executor_path, "connect", host, str(port), user, password, database,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await result.communicate()
            
            if result.returncode == 0:
                response = json.loads(stdout.decode().strip())
                
                # 立即关闭连接
                close_result = await asyncio.create_subprocess_exec(
                    self.executor_path, "close",
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                await close_result.communicate()
                
                return response
            else:
                return {
                    "success": False,
                    "error": f"Connection test failed: {stderr.decode()}"
                }
                
        except Exception as e:
            logger.error(f"Connection test error: {e}")
            return {
                "success": False,
                "error": str(e)
            }


# 全局服务实例
gaussdb_c_executor_service = GaussDBCExecutorService()

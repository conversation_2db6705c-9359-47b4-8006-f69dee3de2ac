import asyncio
import paramiko
import os
import time
from datetime import datetime
from typing import Optional
import logging
import traceback
from scp import SCPClient
from utils.config import Config

logger = logging.getLogger(__name__)

class RemotePacketCaptureService:
    """远程数据包捕获服务 - 在远程服务器上执行tcpdump"""
    
    def __init__(self, capture_dir: str = "captures"):
        self.capture_dir = capture_dir
        self.remote_host = Config.REMOTE_HOST
        self.remote_user = Config.REMOTE_USER
        self.remote_password = Config.REMOTE_PASSWORD
        self.remote_capture_dir = Config.REMOTE_CAPTURE_DIR
        self.mysql_host = Config.MYSQL_HOST
        self.mysql_port = Config.MYSQL_PORT
        
        # SSH连接相关
        self.ssh_client: Optional[paramiko.SSHClient] = None
        self.current_remote_file: Optional[str] = None
        self.current_local_file: Optional[str] = None
        self.tcpdump_pid: Optional[int] = None
        
        # 创建本地捕获目录
        os.makedirs(self.capture_dir, exist_ok=True)
    
    def _generate_filename(self) -> str:
        """生成pcap文件名"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"mysql_capture_{timestamp}.pcap"
    
    async def _create_ssh_connection(self) -> paramiko.SSHClient:
        """创建SSH连接"""
        try:
            ssh = paramiko.SSHClient()
            ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            
            # 在线程池中执行SSH连接
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                None,
                ssh.connect,
                self.remote_host,
                22,  # SSH端口
                self.remote_user,
                self.remote_password
            )
            
            logger.info(f"SSH连接成功: {self.remote_user}@{self.remote_host}")
            return ssh
            
        except Exception as e:
            logger.error(f"SSH连接失败: {str(e)}")
            raise Exception(f"无法连接到远程服务器 {self.remote_host}: {str(e)}")
    
    async def _execute_remote_command(self, command: str) -> tuple:
        """执行远程命令"""
        try:
            if not self.ssh_client:
                self.ssh_client = await self._create_ssh_connection()
            
            logger.info(f"执行远程命令: {command}")
            
            # 在线程池中执行命令
            loop = asyncio.get_event_loop()
            stdin, stdout, stderr = await loop.run_in_executor(
                None,
                self.ssh_client.exec_command,
                command
            )
            
            # 读取输出
            stdout_data = await loop.run_in_executor(None, stdout.read)
            stderr_data = await loop.run_in_executor(None, stderr.read)
            exit_status = stdout.channel.recv_exit_status()
            
            return stdout_data.decode(), stderr_data.decode(), exit_status
            
        except Exception as e:
            logger.error(f"远程命令执行失败: {str(e)}")
            raise
    
    async def start_capture(self, target_host: str = None, target_port: int = None) -> str:
        """启动远程数据包捕获"""
        if self.is_capturing():
            logger.warning("远程数据包捕获已在运行")
            return self.current_local_file

        try:
            # 使用传入的目标主机和端口，或使用默认配置
            capture_host = target_host or self.mysql_host
            capture_port = target_port or self.mysql_port

            # 生成文件名
            filename = self._generate_filename()
            remote_filepath = f"{self.remote_capture_dir}/{filename}"
            local_filepath = os.path.join(self.capture_dir, filename)

            # 创建SSH连接
            if not self.ssh_client:
                self.ssh_client = await self._create_ssh_connection()

            # 在远程服务器创建捕获目录
            await self._execute_remote_command(f"mkdir -p {self.remote_capture_dir}")

            # 构建tcpdump命令 - 捕获指定端口的所有MySQL流量
            # 使用更宽泛的过滤器来确保捕获所有相关流量
            # 优先使用docker0接口，避免使用any接口
            interface = "docker0"  # 默认使用docker0
            try:
                # 检查可用的网络接口
                interfaces_cmd = "ip addr show | grep -E '^[0-9]+:' | awk '{print $2}' | sed 's/:$//'"
                stdin, stdout, stderr = self.ssh_client.exec_command(interfaces_cmd)
                interfaces_output = stdout.read().decode('utf-8').strip()

                if 'docker0' in interfaces_output:
                    interface = 'docker0'
                elif 'ens3' in interfaces_output:
                    interface = 'ens3'
                elif 'eth0' in interfaces_output:
                    interface = 'eth0'
                else:
                    interface = 'lo'  # 回环接口作为最后选择
            except Exception as e:
                logger.warning(f"Failed to detect network interfaces, using default docker0: {e}")

            tcpdump_cmd = (
                f"nohup tcpdump -i {interface} -w {remote_filepath} -s 0 -v -n "
                f"'port {capture_port}' "
                f"> /dev/null 2>&1 & echo $!"
            )

            logger.info(f"抓包配置 - Host: {capture_host}, Port: {capture_port}")
            logger.info(f"使用宽泛过滤器: port {capture_port}")
            
            logger.info(f"启动远程tcpdump: {tcpdump_cmd}")
            
            # 执行tcpdump命令
            stdout, stderr, exit_status = await self._execute_remote_command(tcpdump_cmd)
            
            if exit_status == 0 and stdout.strip():
                self.tcpdump_pid = int(stdout.strip())
                self.current_remote_file = remote_filepath
                self.current_local_file = local_filepath

                # 注册到进程管理器
                try:
                    from services.tcpdump_process_manager import tcpdump_manager
                    task_id = getattr(self, 'current_task_id', f"remote_tcpdump_{self.tcpdump_pid}")
                    tcpdump_manager.register_process(
                        pid=self.tcpdump_pid,
                        task_id=task_id,
                        command=tcpdump_cmd,
                        capture_file=remote_filepath,
                        database_type=getattr(self, 'database_type', 'unknown'),
                        is_remote=True,
                        server_host=self.server_config.host if self.server_config else None
                    )
                except Exception as e:
                    logger.warning(f"注册远程tcpdump进程到管理器失败: {e}")

                logger.info(f"远程tcpdump启动成功: PID={self.tcpdump_pid}, 文件={remote_filepath}")

                # 等待一下确保tcpdump启动
                await asyncio.sleep(1)

                return local_filepath
            else:
                error_msg = f"tcpdump启动失败: {stderr}"
                logger.error(error_msg)
                raise Exception(error_msg)
                
        except Exception as e:
            logger.error(f"启动远程数据包捕获失败: {str(e)}")
            self._cleanup()
            raise Exception(f"远程抓包启动失败: {str(e)}")
    
    async def stop_capture(self) -> Optional[str]:
        """停止远程数据包捕获并下载文件"""
        if not self.is_capturing():
            logger.warning("没有正在运行的远程数据包捕获")
            return None
        
        try:
            logger.info("停止远程数据包捕获...")
            
            # 停止tcpdump进程
            if self.tcpdump_pid:
                kill_cmd = f"kill {self.tcpdump_pid}"
                await self._execute_remote_command(kill_cmd)
                logger.info(f"已停止tcpdump进程: PID={self.tcpdump_pid}")
            
            # 等待一下确保文件写入完成
            await asyncio.sleep(2)
            
            # 检查远程文件是否存在
            check_cmd = f"ls -la {self.current_remote_file}"
            stdout, stderr, exit_status = await self._execute_remote_command(check_cmd)
            
            if exit_status == 0:
                logger.info(f"远程文件存在: {stdout.strip()}")
                
                # 下载文件到本地
                await self._download_file()
                
                # 清理远程文件
                cleanup_cmd = f"rm -f {self.current_remote_file}"
                await self._execute_remote_command(cleanup_cmd)
                
                local_file = self.current_local_file
                self._cleanup()
                
                logger.info(f"远程数据包捕获完成，文件已下载: {local_file}")
                return local_file
            else:
                logger.warning(f"远程文件不存在: {stderr}")
                self._cleanup()
                return None
                
        except Exception as e:
            logger.error(f"停止远程数据包捕获失败: {str(e)}")
            self._cleanup()
            raise Exception(f"停止远程抓包失败: {str(e)}")
    
    async def _download_file(self):
        """下载远程文件到本地"""
        try:
            logger.info(f"下载文件: {self.current_remote_file} -> {self.current_local_file}")
            
            # 使用SCP下载文件
            loop = asyncio.get_event_loop()
            
            def download_file():
                with SCPClient(self.ssh_client.get_transport()) as scp:
                    scp.get(self.current_remote_file, self.current_local_file)
            
            await loop.run_in_executor(None, download_file)
            
            # 检查本地文件
            if os.path.exists(self.current_local_file):
                file_size = os.path.getsize(self.current_local_file)
                logger.info(f"文件下载成功: {self.current_local_file} ({file_size} 字节)")
            else:
                raise Exception("文件下载失败，本地文件不存在")
                
        except Exception as e:
            logger.error(f"文件下载失败: {str(e)}")
            raise
    
    def is_capturing(self) -> bool:
        """检查是否正在捕获"""
        return self.tcpdump_pid is not None and self.current_remote_file is not None
    
    def get_current_file(self) -> Optional[str]:
        """获取当前本地文件路径"""
        return self.current_local_file
    
    def _cleanup(self):
        """清理状态"""
        # 从进程管理器中注销
        if self.tcpdump_pid:
            try:
                from services.tcpdump_process_manager import tcpdump_manager
                tcpdump_manager.unregister_process(self.tcpdump_pid)
            except Exception as e:
                logger.warning(f"从进程管理器注销远程进程失败: {e}")

        self.tcpdump_pid = None
        self.current_remote_file = None
        self.current_local_file = None
    
    def list_capture_files(self) -> list:
        """列出本地捕获文件"""
        try:
            files = []
            for filename in os.listdir(self.capture_dir):
                if filename.endswith('.pcap'):
                    filepath = os.path.join(self.capture_dir, filename)
                    stat = os.stat(filepath)
                    files.append({
                        'filename': filename,
                        'filepath': filepath,
                        'size': stat.st_size,
                        'created': datetime.fromtimestamp(stat.st_ctime).isoformat(),
                        'modified': datetime.fromtimestamp(stat.st_mtime).isoformat()
                    })
            
            # 按创建时间排序
            files.sort(key=lambda x: x['created'], reverse=True)
            return files
            
        except Exception as e:
            logger.error(f"列出捕获文件失败: {str(e)}")
            return []
    
    async def analyze_pcap_file(self, filepath: str) -> dict:
        """分析pcap文件"""
        try:
            from scapy.all import rdpcap, TCP
            
            logger.info(f"分析pcap文件: {filepath}")
            
            # 读取pcap文件
            packets = rdpcap(filepath)
            
            analysis = {
                'total_packets': len(packets),
                'mysql_packets': 0,
                'connections': set(),
                'queries': [],
                'file_size': os.path.getsize(filepath)
            }
            
            for packet in packets:
                if TCP in packet:
                    # 检查是否是MySQL端口
                    if packet[TCP].dport == self.mysql_port or packet[TCP].sport == self.mysql_port:
                        analysis['mysql_packets'] += 1
                        
                        # 记录连接信息
                        src_ip = packet['IP'].src
                        dst_ip = packet['IP'].dst
                        analysis['connections'].add(f"{src_ip}:{packet[TCP].sport} -> {dst_ip}:{packet[TCP].dport}")
                        
                        # 尝试提取MySQL查询（简单实现）
                        if packet[TCP].payload:
                            payload = bytes(packet[TCP].payload)
                            # MySQL查询通常以特定字节开始
                            if len(payload) > 5 and payload[4] == 0x03:  # COM_QUERY
                                try:
                                    query = payload[5:].decode('utf-8', errors='ignore')
                                    if query.strip():
                                        analysis['queries'].append({
                                            'timestamp': packet.time,
                                            'query': query.strip()
                                        })
                                except Exception as e:

                                    logger.error(f"操作失败: {type(e).__name__}: {str(e)}")

                                    logger.error(f"异常堆栈跟踪: {traceback.format_exc()}")
            # 转换set为list以便JSON序列化
            analysis['connections'] = list(analysis['connections'])
            
            logger.info(f"分析完成: {analysis['total_packets']} 总数据包, {analysis['mysql_packets']} MySQL数据包")
            return analysis
            
        except Exception as e:
            logger.error(f"分析pcap文件失败: {str(e)}")
            return {'error': str(e)}
    
    async def close(self):
        """关闭SSH连接"""
        if self.ssh_client:
            self.ssh_client.close()
            self.ssh_client = None
            logger.info("SSH连接已关闭")

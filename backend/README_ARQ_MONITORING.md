# ARQ监控系统 - 快速使用指南

## 🎯 概述

这套ARQ监控系统为你的AI SQL PCAP项目提供了完整的ARQ任务队列监控和调试功能，包括：

- **实时任务状态监控** - 查看队列、正在执行的任务、Worker状态
- **日志查看和搜索** - 实时查看、搜索、分析ARQ相关日志
- **性能监控** - Redis状态、内存使用、连接数等指标
- **故障排除工具** - 清理孤儿任务、错误分析等

## 🚀 快速开始

### 1. 基本使用（推荐）

```bash
# 进入backend目录
cd backend

# 使用便捷脚本（自动激活虚拟环境）
./monitor_arq.sh status          # 快速状态检查
./monitor_arq.sh tasks --once    # 单次任务状态检查
./monitor_arq.sh logs --tail 50  # 查看最新50行日志
```

### 2. 手动使用（需要先激活虚拟环境）

```bash
# 激活虚拟环境
source venv/bin/activate

# 使用监控工具
python monitor_arq.py status
python monitor_arq.py tasks --dashboard
python monitor_arq.py logs --follow
```

## 📊 主要功能

### 1. 实时任务监控面板

```bash
# 启动实时监控面板（最推荐用于debug）
./monitor_arq.sh tasks --dashboard
```

显示内容：
- Redis状态（版本、内存、连接数）
- 队列长度和等待任务
- 正在执行的任务
- Worker状态和心跳
- 最近完成/失败的任务

### 2. 日志查看和分析

```bash
# 查看最新日志
./monitor_arq.sh logs --tail 100

# 实时跟踪日志
./monitor_arq.sh logs --follow

# 搜索错误信息
./monitor_arq.sh logs --search "error"
./monitor_arq.sh logs --search "MySQL capture file not found"

# 查看不同类型的日志
./monitor_arq.sh logs --type error --tail 50
./monitor_arq.sh logs --type startup --tail 30

# 交互模式（可以动态执行各种命令）
./monitor_arq.sh logs --interactive
```

### 3. 系统状态检查

```bash
# 快速状态检查
./monitor_arq.sh status

# 单次任务状态检查
./monitor_arq.sh tasks --once

# 日志文件摘要
./monitor_arq.sh logs --summary
```

### 4. 维护工具

```bash
# 清理孤儿任务
./monitor_arq.sh tasks --cleanup
```

## 🔧 常用调试场景

### 场景1：开发调试时的实时监控

```bash
# 开启实时任务监控面板，查看任务执行情况
./monitor_arq.sh tasks --dashboard
```

这会显示一个实时更新的监控面板，包含所有关键信息。

### 场景2：查看任务执行错误

```bash
# 查看错误日志
./monitor_arq.sh logs --type error --tail 100

# 搜索特定错误
./monitor_arq.sh logs --search "MySQL capture file not found"
./monitor_arq.sh logs --search "connection"
```

### 场景3：分析任务性能问题

```bash
# 查看最新的worker日志
./monitor_arq.sh logs --tail 200

# 搜索特定任务的执行情况
./monitor_arq.sh logs --search "batch_test_case_execution_task"
```

### 场景4：系统维护

```bash
# 检查系统整体状态
./monitor_arq.sh status

# 清理可能的孤儿任务
./monitor_arq.sh tasks --cleanup

# 查看日志文件统计
./monitor_arq.sh logs --summary
```

## 📁 日志文件说明

监控系统会查看以下日志文件：

```
logs/
├── worker/           # ARQ Worker执行日志（最重要）
├── startup/          # 应用启动日志
├── app/             # 应用运行日志
├── debug/           # 调试日志
├── error/           # 错误日志
└── arq_monitor/     # 监控系统自身日志
```

## ⚡ 性能优化建议

1. **监控频率**：
   - 开发环境：3-5秒更新间隔
   - 生产环境：10-30秒更新间隔

2. **日志级别**：
   - 调试时：使用DEBUG级别
   - 正常运行：使用INFO级别

3. **资源使用**：
   - 监控工具占用资源很少
   - 可以长期运行不影响主业务

## 🛠️ 故障排除

### 问题1：Redis连接失败

```bash
# 检查Redis服务状态
./monitor_arq.sh status

# 如果显示Redis连接失败，检查：
# 1. Redis服务是否启动
# 2. 配置文件中的Redis地址是否正确
# 3. 网络连接是否正常
```

### 问题2：没有日志文件

```bash
# 检查日志目录
./monitor_arq.sh logs --summary

# 如果没有日志文件，确保：
# 1. 应用正在运行
# 2. 日志目录权限正确
```

### 问题3：任务执行异常

```bash
# 查看最新错误
./monitor_arq.sh logs --type error --tail 50

# 搜索特定错误
./monitor_arq.sh logs --search "Exception"

# 清理可能的孤儿任务
./monitor_arq.sh tasks --cleanup
```

### 问题4：虚拟环境问题

如果遇到模块导入错误：

```bash
# 确保在backend目录中
cd backend

# 检查虚拟环境
ls -la venv/

# 重新激活虚拟环境
source venv/bin/activate

# 检查依赖
pip list | grep redis
```

## 📈 高级用法

### 1. 自定义监控间隔

```bash
# 自定义更新间隔（秒）
./monitor_arq.sh tasks --dashboard --interval 3
```

### 2. 导出监控数据

```bash
# 导出监控数据到文件
./monitor_arq.sh monitor --export /path/to/export.json
```

### 3. 搜索特定时间段的日志

```bash
# 使用交互模式进行复杂搜索
./monitor_arq.sh logs --interactive
```

## 🎉 总结

这套ARQ监控系统可以帮助你：

1. **实时了解系统状态** - 通过监控面板实时查看任务执行情况
2. **快速定位问题** - 通过日志搜索快速找到错误根源
3. **优化系统性能** - 通过性能指标分析系统瓶颈
4. **维护系统健康** - 通过清理工具保持系统良好状态

**推荐的日常使用流程：**

1. 开发时：`./monitor_arq.sh tasks --dashboard` 实时监控
2. 出现问题：`./monitor_arq.sh logs --search "error"` 查找错误
3. 定期维护：`./monitor_arq.sh tasks --cleanup` 清理系统

享受高效的ARQ调试体验！🚀

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
步骤校验API - 提供SQL步骤与pcap包一一对应校验的API接口
"""

import os
import logging
from typing import Dict, List, Any, Optional
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel

from services.step_sql_validation_service import (
    StepSQLValidationService, 
    StepValidationSummary,
    StepSQLValidationResult,
    ValidationStatus
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/step-validation", tags=["步骤校验"])

# 请求模型
class StepValidationRequest(BaseModel):
    """步骤校验请求"""
    execution_steps: List[Dict[str, Any]]
    pcap_file: str
    database_type: Optional[str] = None
    database_port: Optional[int] = None

class BatchStepValidationRequest(BaseModel):
    """批量步骤校验请求"""
    test_case_executions: List[Dict[str, Any]]  # 包含execution_steps和pcap_file的测试用例执行记录

# 响应模型
class StepValidationResponse(BaseModel):
    """步骤校验响应"""
    success: bool
    summary: Optional[Dict[str, Any]] = None
    error: Optional[str] = None

class BatchStepValidationResponse(BaseModel):
    """批量步骤校验响应"""
    success: bool
    results: List[Dict[str, Any]]
    overall_summary: Optional[Dict[str, Any]] = None
    error: Optional[str] = None

# 初始化服务
step_validation_service = StepSQLValidationService()

@router.post("/validate", response_model=StepValidationResponse)
async def validate_steps_with_pcap(request: StepValidationRequest):
    """
    验证执行步骤与pcap包的对应关系
    """
    try:
        logger.info(f"开始步骤校验，步骤数: {len(request.execution_steps)}, PCAP文件: {request.pcap_file}")
        
        # 检查pcap文件是否存在
        if not os.path.exists(request.pcap_file):
            raise HTTPException(status_code=404, detail=f"PCAP文件不存在: {request.pcap_file}")
        
        # 执行校验
        summary = step_validation_service.validate_steps_with_pcap(
            execution_steps=request.execution_steps,
            pcap_file=request.pcap_file,
            database_type=request.database_type,
            database_port=request.database_port
        )
        
        # 转换为字典格式
        summary_dict = {
            "total_steps": summary.total_steps,
            "matched_steps": summary.matched_steps,
            "partial_matched_steps": summary.partial_matched_steps,
            "not_found_steps": summary.not_found_steps,
            "similar_steps": summary.similar_steps,
            "error_steps": summary.error_steps,
            "overall_success_rate": summary.overall_success_rate,
            "step_results": [
                {
                    "step_number": result.step_number,
                    "expected_sql": result.expected_sql,
                    "sql_type": result.sql_type.value,
                    "validation_status": result.validation_status.value,
                    "similarity_score": result.similarity_score,
                    "confidence": result.confidence,
                    "packet_numbers": result.packet_numbers,
                    "matched_statements_count": len(result.matched_statements),
                    "matched_statements": [
                        {
                            "sql": stmt.sql,
                            "sql_type": stmt.sql_type.value,
                            "database_type": stmt.database_type.value,
                            "timestamp": stmt.timestamp,
                            "packet_number": stmt.packet_number,
                            "confidence": stmt.confidence
                        }
                        for stmt in result.matched_statements
                    ],
                    "error_message": result.error_message
                }
                for result in summary.step_results
            ]
        }
        
        logger.info(f"步骤校验完成，成功率: {summary.overall_success_rate:.2%}")
        
        return StepValidationResponse(
            success=True,
            summary=summary_dict
        )
        
    except Exception as e:
        logger.error(f"步骤校验失败: {str(e)}")
        return StepValidationResponse(
            success=False,
            error=str(e)
        )

@router.post("/validate-batch", response_model=BatchStepValidationResponse)
async def validate_batch_steps_with_pcap(request: BatchStepValidationRequest):
    """
    批量验证多个测试用例的执行步骤与pcap包的对应关系
    """
    try:
        logger.info(f"开始批量步骤校验，测试用例数: {len(request.test_case_executions)}")
        
        results = []
        total_success_count = 0
        total_step_count = 0
        
        for i, execution in enumerate(request.test_case_executions):
            try:
                execution_steps = execution.get("execution_steps", [])
                pcap_file = execution.get("pcap_file", "")
                database_type = execution.get("database_type")
                database_port = execution.get("database_port")
                
                if not pcap_file or not os.path.exists(pcap_file):
                    results.append({
                        "execution_index": i,
                        "success": False,
                        "error": f"PCAP文件不存在或未指定: {pcap_file}"
                    })
                    continue
                
                # 执行单个校验
                summary = step_validation_service.validate_steps_with_pcap(
                    execution_steps=execution_steps,
                    pcap_file=pcap_file,
                    database_type=database_type,
                    database_port=database_port
                )
                
                # 累计统计
                total_step_count += summary.total_steps
                total_success_count += summary.matched_steps + summary.similar_steps + summary.partial_matched_steps
                
                results.append({
                    "execution_index": i,
                    "success": True,
                    "pcap_file": pcap_file,
                    "total_steps": summary.total_steps,
                    "success_rate": summary.overall_success_rate,
                    "matched_steps": summary.matched_steps,
                    "similar_steps": summary.similar_steps,
                    "partial_matched_steps": summary.partial_matched_steps,
                    "not_found_steps": summary.not_found_steps,
                    "error_steps": summary.error_steps
                })
                
            except Exception as e:
                logger.error(f"批量校验第 {i} 个执行记录失败: {str(e)}")
                results.append({
                    "execution_index": i,
                    "success": False,
                    "error": str(e)
                })
        
        # 计算总体汇总
        overall_success_rate = total_success_count / total_step_count if total_step_count > 0 else 0.0
        overall_summary = {
            "total_executions": len(request.test_case_executions),
            "successful_executions": sum(1 for r in results if r.get("success", False)),
            "total_steps": total_step_count,
            "total_success_count": total_success_count,
            "overall_success_rate": overall_success_rate
        }
        
        logger.info(f"批量步骤校验完成，总体成功率: {overall_success_rate:.2%}")
        
        return BatchStepValidationResponse(
            success=True,
            results=results,
            overall_summary=overall_summary
        )
        
    except Exception as e:
        logger.error(f"批量步骤校验失败: {str(e)}")
        return BatchStepValidationResponse(
            success=False,
            results=[],
            error=str(e)
        )

@router.get("/status")
async def get_validation_status():
    """获取校验服务状态"""
    return {
        "service": "步骤SQL校验服务",
        "status": "运行中",
        "similarity_threshold": step_validation_service.similarity_threshold,
        "partial_match_threshold": step_validation_service.partial_match_threshold
    }

@router.post("/configure")
async def configure_validation_service(
    similarity_threshold: float = 0.7,
    partial_match_threshold: float = 0.5
):
    """配置校验服务参数"""
    try:
        if not (0.0 <= similarity_threshold <= 1.0):
            raise HTTPException(status_code=400, detail="相似度阈值必须在0.0-1.0之间")
        
        if not (0.0 <= partial_match_threshold <= 1.0):
            raise HTTPException(status_code=400, detail="部分匹配阈值必须在0.0-1.0之间")
        
        step_validation_service.similarity_threshold = similarity_threshold
        step_validation_service.partial_match_threshold = partial_match_threshold
        
        logger.info(f"校验服务配置已更新: similarity_threshold={similarity_threshold}, partial_match_threshold={partial_match_threshold}")
        
        return {
            "success": True,
            "message": "配置更新成功",
            "similarity_threshold": similarity_threshold,
            "partial_match_threshold": partial_match_threshold
        }
        
    except Exception as e:
        logger.error(f"配置校验服务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

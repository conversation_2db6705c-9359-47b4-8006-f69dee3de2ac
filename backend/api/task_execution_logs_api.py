"""
任务执行日志API
提供任务执行日志的查询、统计和分析接口
"""

from fastapi import APIRouter, HTTPException, Query, Depends
from typing import Dict, Any, List, Optional
import logging

from services.async_task_execution_log_service import async_task_execution_log_service
from utils.response import success_response, error_response

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/task-execution-logs", tags=["任务执行日志"])


@router.get("/task/{task_id}")
async def get_task_execution_log(task_id: str):
    """获取指定任务的执行日志"""
    try:
        task_log = await async_task_execution_log_service.get_task_log(task_id)
        
        if not task_log:
            raise HTTPException(status_code=404, detail=f"任务 {task_id} 的执行日志不存在")
        
        return success_response(data=task_log, message="获取任务执行日志成功")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务执行日志失败: {e}")
        return error_response(message=f"获取任务执行日志失败: {str(e)}")


@router.get("/tasks/by-type/{task_type}")
async def get_task_logs_by_type(
    task_type: str,
    limit: int = Query(100, ge=1, le=1000, description="返回记录数量限制")
):
    """根据任务类型获取执行日志"""
    try:
        task_logs = await async_task_execution_log_service.get_task_logs_by_type(task_type, limit)
        
        return success_response(
            data={
                "task_type": task_type,
                "count": len(task_logs),
                "logs": task_logs
            },
            message=f"获取 {task_type} 类型任务日志成功"
        )
        
    except Exception as e:
        logger.error(f"获取任务类型日志失败: {e}")
        return error_response(message=f"获取任务类型日志失败: {str(e)}")


@router.get("/tasks/failed")
async def get_failed_tasks(
    hours: int = Query(24, ge=1, le=168, description="查询最近多少小时内的失败任务")
):
    """获取失败的任务"""
    try:
        failed_tasks = await async_task_execution_log_service.get_failed_tasks(hours)
        
        return success_response(
            data={
                "period_hours": hours,
                "count": len(failed_tasks),
                "failed_tasks": failed_tasks
            },
            message=f"获取最近 {hours} 小时内失败任务成功"
        )
        
    except Exception as e:
        logger.error(f"获取失败任务失败: {e}")
        return error_response(message=f"获取失败任务失败: {str(e)}")


@router.get("/statistics/executor")
async def get_executor_statistics(
    days: int = Query(7, ge=1, le=90, description="统计最近多少天的数据")
):
    """获取执行器使用统计"""
    try:
        stats = await async_task_execution_log_service.get_executor_statistics(days)
        
        return success_response(
            data=stats,
            message=f"获取最近 {days} 天执行器统计成功"
        )
        
    except Exception as e:
        logger.error(f"获取执行器统计失败: {e}")
        return error_response(message=f"获取执行器统计失败: {str(e)}")


@router.get("/statistics/overview")
async def get_task_execution_overview(
    days: int = Query(7, ge=1, le=90, description="统计最近多少天的数据")
):
    """获取任务执行概览统计"""
    try:
        service = async_task_execution_log_service
        
        # 获取总体统计
        overview_sql = """
            SELECT 
                COUNT(*) as total_tasks,
                SUM(CASE WHEN status = 'COMPLETED' THEN 1 ELSE 0 END) as completed_tasks,
                SUM(CASE WHEN status = 'FAILED' THEN 1 ELSE 0 END) as failed_tasks,
                SUM(CASE WHEN status = 'RUNNING' THEN 1 ELSE 0 END) as running_tasks,
                SUM(CASE WHEN status = 'PENDING' THEN 1 ELSE 0 END) as pending_tasks,
                AVG(duration_seconds) as avg_duration,
                MAX(duration_seconds) as max_duration,
                MIN(duration_seconds) as min_duration
            FROM async_task_execution_logs 
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL %s DAY)
        """
        
        overview_result = await service.mysql_service.execute_query(overview_sql, (days,))
        overview_data = overview_result['data'][0] if overview_result and overview_result['data'] else {}
        
        # 获取任务类型分布
        type_distribution_sql = """
            SELECT 
                task_type,
                COUNT(*) as count,
                SUM(CASE WHEN status = 'COMPLETED' THEN 1 ELSE 0 END) as completed,
                SUM(CASE WHEN status = 'FAILED' THEN 1 ELSE 0 END) as failed
            FROM async_task_execution_logs 
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL %s DAY)
            GROUP BY task_type
            ORDER BY count DESC
        """
        
        type_result = await service.mysql_service.execute_query(type_distribution_sql, (days,))
        type_distribution = type_result['data'] if type_result and type_result['data'] else []
        
        # 获取每日执行趋势
        daily_trend_sql = """
            SELECT 
                DATE(created_at) as date,
                COUNT(*) as total_tasks,
                SUM(CASE WHEN status = 'COMPLETED' THEN 1 ELSE 0 END) as completed_tasks,
                SUM(CASE WHEN status = 'FAILED' THEN 1 ELSE 0 END) as failed_tasks
            FROM async_task_execution_logs 
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL %s DAY)
            GROUP BY DATE(created_at)
            ORDER BY date DESC
        """
        
        trend_result = await service.mysql_service.execute_query(daily_trend_sql, (days,))
        daily_trend = trend_result['data'] if trend_result and trend_result['data'] else []
        
        return success_response(
            data={
                "period_days": days,
                "overview": overview_data,
                "task_type_distribution": type_distribution,
                "daily_trend": daily_trend
            },
            message=f"获取最近 {days} 天任务执行概览成功"
        )
        
    except Exception as e:
        logger.error(f"获取任务执行概览失败: {e}")
        return error_response(message=f"获取任务执行概览失败: {str(e)}")


@router.get("/statistics/performance")
async def get_performance_statistics(
    days: int = Query(7, ge=1, le=90, description="统计最近多少天的数据")
):
    """获取性能统计"""
    try:
        service = async_task_execution_log_service
        
        # 获取执行器性能对比
        executor_performance_sql = """
            SELECT 
                executor_type,
                COUNT(*) as task_count,
                AVG(duration_seconds) as avg_duration,
                MIN(duration_seconds) as min_duration,
                MAX(duration_seconds) as max_duration,
                AVG(cpu_usage_percent) as avg_cpu_usage,
                AVG(memory_usage_mb) as avg_memory_usage,
                SUM(CASE WHEN status = 'COMPLETED' THEN 1 ELSE 0 END) / COUNT(*) * 100 as success_rate
            FROM async_task_execution_logs 
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL %s DAY)
            AND duration_seconds IS NOT NULL
            GROUP BY executor_type
        """
        
        executor_result = await service.mysql_service.execute_query(executor_performance_sql, (days,))
        executor_performance = executor_result['data'] if executor_result and executor_result['data'] else []
        
        # 获取任务类型性能统计
        task_type_performance_sql = """
            SELECT 
                task_type,
                COUNT(*) as task_count,
                AVG(duration_seconds) as avg_duration,
                MIN(duration_seconds) as min_duration,
                MAX(duration_seconds) as max_duration,
                SUM(CASE WHEN status = 'COMPLETED' THEN 1 ELSE 0 END) / COUNT(*) * 100 as success_rate
            FROM async_task_execution_logs 
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL %s DAY)
            AND duration_seconds IS NOT NULL
            GROUP BY task_type
            ORDER BY avg_duration DESC
        """
        
        task_type_result = await service.mysql_service.execute_query(task_type_performance_sql, (days,))
        task_type_performance = task_type_result['data'] if task_type_result and task_type_result['data'] else []
        
        # 获取执行器回退统计
        fallback_sql = """
            SELECT 
                fallback_from_executor,
                executor_type as fallback_to_executor,
                COUNT(*) as fallback_count,
                GROUP_CONCAT(DISTINCT executor_selection_reason SEPARATOR '; ') as reasons
            FROM async_task_execution_logs 
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL %s DAY)
            AND fallback_from_executor IS NOT NULL
            GROUP BY fallback_from_executor, executor_type
        """
        
        fallback_result = await service.mysql_service.execute_query(fallback_sql, (days,))
        fallback_statistics = fallback_result['data'] if fallback_result and fallback_result['data'] else []
        
        return success_response(
            data={
                "period_days": days,
                "executor_performance": executor_performance,
                "task_type_performance": task_type_performance,
                "fallback_statistics": fallback_statistics
            },
            message=f"获取最近 {days} 天性能统计成功"
        )
        
    except Exception as e:
        logger.error(f"获取性能统计失败: {e}")
        return error_response(message=f"获取性能统计失败: {str(e)}")


@router.get("/search")
async def search_task_logs(
    task_id: Optional[str] = Query(None, description="任务ID"),
    task_type: Optional[str] = Query(None, description="任务类型"),
    status: Optional[str] = Query(None, description="任务状态"),
    executor_type: Optional[str] = Query(None, description="执行器类型"),
    database_config_id: Optional[int] = Query(None, description="数据库配置ID"),
    start_date: Optional[str] = Query(None, description="开始日期 (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="结束日期 (YYYY-MM-DD)"),
    limit: int = Query(100, ge=1, le=1000, description="返回记录数量限制"),
    offset: int = Query(0, ge=0, description="偏移量")
):
    """搜索任务执行日志"""
    try:
        service = async_task_execution_log_service
        
        # 构建查询条件
        where_conditions = []
        params = []
        
        if task_id:
            where_conditions.append("task_id LIKE %s")
            params.append(f"%{task_id}%")
        
        if task_type:
            where_conditions.append("task_type = %s")
            params.append(task_type)
        
        if status:
            where_conditions.append("status = %s")
            params.append(status)
        
        if executor_type:
            where_conditions.append("executor_type = %s")
            params.append(executor_type)
        
        if database_config_id:
            where_conditions.append("database_config_id = %s")
            params.append(database_config_id)
        
        if start_date:
            where_conditions.append("DATE(created_at) >= %s")
            params.append(start_date)
        
        if end_date:
            where_conditions.append("DATE(created_at) <= %s")
            params.append(end_date)
        
        # 构建SQL查询
        where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"
        
        # 获取总数
        count_sql = f"SELECT COUNT(*) as total FROM async_task_execution_logs WHERE {where_clause}"
        count_result = await service.mysql_service.execute_query(count_sql, tuple(params))
        total = count_result['data'][0]['total'] if count_result and count_result['data'] else 0
        
        # 获取数据
        data_sql = f"""
            SELECT * FROM async_task_execution_logs 
            WHERE {where_clause}
            ORDER BY created_at DESC 
            LIMIT %s OFFSET %s
        """
        params.extend([limit, offset])
        
        data_result = await service.mysql_service.execute_query(data_sql, tuple(params))
        logs = data_result['data'] if data_result and data_result['data'] else []
        
        return success_response(
            data={
                "total": total,
                "limit": limit,
                "offset": offset,
                "logs": logs
            },
            message="搜索任务执行日志成功"
        )
        
    except Exception as e:
        logger.error(f"搜索任务执行日志失败: {e}")
        return error_response(message=f"搜索任务执行日志失败: {str(e)}")

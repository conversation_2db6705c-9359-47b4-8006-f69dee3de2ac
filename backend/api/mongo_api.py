from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Optional, Dict, Any, List
import logging
import traceback
import time
import asyncio

from services.mongo_ai_service import MongoAIService
from services.simple_mongo_shell_service import SimpleMongoShellService
from services.mongo_local_packet_capture_service import MongoLocalPacketCaptureService
from utils.config import Config

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/mongo", tags=["mongodb"])

# 请求和响应模型
class MongoQueryRequest(BaseModel):
    query: str
    capture_packets: bool = True
    executor_type: str = "mongosh"  # 支持mongosh和c执行器

class MongoQueryResponse(BaseModel):
    success: bool
    mongo_query: Optional[str] = None
    result: Optional[Any] = None
    packet_file: Optional[str] = None
    executor_type: Optional[str] = None  # 使用的执行器类型：mongosh或c
    error: Optional[str] = None

class MongoConnectionTestResponse(BaseModel):
    success: bool
    message: str = ""
    response_time: int = 0
    databases: Optional[List[str]] = None
    collections: Optional[List[str]] = None
    error: Optional[str] = None

# 服务实例
mongo_ai_service = MongoAIService()
mongosh_service = None
packet_service = MongoLocalPacketCaptureService()

async def get_mongosh_service():
    """按需获取MongoDB Shell服务实例"""
    global mongosh_service
    if mongosh_service is None:
        mongo_config = Config.get_mongo_config()
        # 构建连接字符串
        connection_string = f"mongodb://{mongo_config['user']}:{mongo_config['password']}@{mongo_config['host']}:{mongo_config['port']}/{mongo_config['database']}?authSource=admin&directConnection=true"
        mongosh_service = SimpleMongoShellService(connection_string=connection_string)
    return mongosh_service

@router.post("/query", response_model=MongoQueryResponse)
async def process_mongo_query(request: MongoQueryRequest):
    """处理MongoDB自然语言查询 - 支持mongosh和C执行器"""
    try:
        logger.info(f"Processing MongoDB query: {request.query}, executor_type: {request.executor_type}")

        # 1. 使用AI解析自然语言为MongoDB查询
        parsed_result = await mongo_ai_service.parse_natural_language_to_mongo(request.query)
        logger.info(f"Parsed MongoDB query: {parsed_result}")

        # 2. 检查执行器类型
        actual_executor_type = request.executor_type
        mongo_c_service = None
        if request.executor_type == "c":
            try:
                from services.mongo_c_executor_service import mongo_c_executor_service
                if mongo_c_executor_service.is_available():
                    mongo_c_service = mongo_c_executor_service
                    logger.info("Using MongoDB C executor as requested")
                else:
                    logger.warning("MongoDB C executor not available, falling back to mongosh")
                    actual_executor_type = "mongosh"
            except ImportError:
                logger.warning("MongoDB C executor service not found, falling back to mongosh")
                actual_executor_type = "mongosh"

        # 3. 如果需要抓包，启动抓包服务
        packet_file = None
        if request.capture_packets:
            packet_file = await packet_service.start_capture()
            logger.info(f"Started MongoDB packet capture: {packet_file}")
            
            # 等待抓包服务完全启动，确保能捕获到后续的MongoDB操作
            logger.info("Waiting for packet capture to fully initialize...")
            await asyncio.sleep(3)
            logger.info("Packet capture ready, proceeding with MongoDB operation...")

        # 4. 执行MongoDB查询
        mongo_query = parsed_result
        if not request.capture_packets:
            # 如果不需要抓包，只返回解析的查询，不执行
            logger.info("MongoDB query parsed only (not executed)")
            return MongoQueryResponse(
                success=True,
                mongo_query=mongo_query,
                result={'message': 'MongoDB query parsed successfully (not executed)', 'response_type': 'PARSE_ONLY'},
                packet_file=None,
                executor_type=actual_executor_type
            )
        else:
            # 执行MongoDB查询
            try:
                if mongo_c_service and actual_executor_type == "c":
                    # 使用C执行器
                    mongo_config = Config.get_mongo_config()
                    result = await mongo_c_service.execute_single_mongo_query(
                        mongo_config['host'], mongo_config['port'],
                        mongo_config['user'], mongo_config['password'],
                        mongo_config['database'], mongo_query
                    )
                    logger.info("MongoDB query executed successfully with C executor")
                else:
                    # 使用mongosh执行器
                    service = await get_mongosh_service()
                    result = await service.execute_mongo_query(mongo_query)
                    logger.info("MongoDB query executed successfully with mongosh executor")
                    actual_executor_type = "mongosh"
                
            except Exception as e:
                logger.error(f"MongoDB query execution failed: {str(e)}")
                raise

        # 4. 停止抓包并获取最终的文件路径
        if request.capture_packets:
            final_packet_file = await packet_service.stop_capture()
            if final_packet_file:
                packet_file = final_packet_file
            logger.info(f"MongoDB packet capture stopped: {packet_file}")

        return MongoQueryResponse(
            success=True,
            mongo_query=mongo_query,
            result=result,
            packet_file=packet_file,
            executor_type=actual_executor_type
        )

    except Exception as e:
        logger.error(f"Error processing MongoDB query: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        
        # 如果抓包还在进行，需要停止
        if request.capture_packets:
            try:
                await packet_service.stop_capture()
            except Exception as stop_e:
                logger.warning(f"Failed to stop packet capture: {str(stop_e)}")
        
        return MongoQueryResponse(
            success=False,
            error=str(e)
        )

@router.get("/test", response_model=MongoConnectionTestResponse)
async def test_mongo_connection():
    """测试MongoDB连接 - 使用mongosh"""
    start_time = time.time()
    try:
        service = await get_mongosh_service()
        
        # 使用mongosh测试连接
        result = await service.execute_mongo_query("db.adminCommand('listDatabases')")
        
        # 获取数据库列表
        databases_result = await service.execute_mongo_query("db.adminCommand('listDatabases').databases.map(d => d.name)")
        databases = databases_result.get('result', []) if isinstance(databases_result, dict) else []
        
        # 获取集合列表
        collections_result = await service.execute_mongo_query("db.listCollectionNames()")
        collections = collections_result.get('result', []) if isinstance(collections_result, dict) else []
        
        response_time = int((time.time() - start_time) * 1000)
        
        return MongoConnectionTestResponse(
            success=True,
            message="MongoDB连接测试成功",
            response_time=response_time,
            databases=databases,
            collections=collections
        )
    except Exception as e:
        response_time = int((time.time() - start_time) * 1000)
        logger.error(f"MongoDB connection test failed: {str(e)}")
        return MongoConnectionTestResponse(
            success=False,
            message="MongoDB连接测试失败",
            response_time=response_time,
            error=str(e)
        )

@router.get("/databases")
async def get_databases():
    """获取数据库列表 - 使用mongosh"""
    try:
        service = await get_mongosh_service()
        result = await service.execute_mongo_query("db.adminCommand('listDatabases').databases.map(d => d.name)")
        databases = result.get('result', []) if isinstance(result, dict) else []
        return {"success": True, "databases": databases}
    except Exception as e:
        logger.error(f"Error getting databases: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/collections")
async def get_collections(db: str = None):
    """获取集合列表 - 使用mongosh"""
    try:
        service = await get_mongosh_service()
        if db:
            query = f"db.getSiblingDB('{db}').listCollectionNames()"
        else:
            query = "db.listCollectionNames()"
        
        result = await service.execute_mongo_query(query)
        collections = result.get('result', []) if isinstance(result, dict) else []
        return {"success": True, "collections": collections}
    except Exception as e:
        logger.error(f"Error getting collections: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/direct")
async def execute_direct_query(request: dict):
    """直接执行MongoDB查询 - 支持mongosh和C执行器"""
    try:
        mongo_query = request.get("query")
        executor_type = request.get("executor_type", "mongosh")
        
        if not mongo_query:
            raise HTTPException(status_code=400, detail="Query is required")
        
        logger.info(f"Executing direct MongoDB query: {mongo_query}, executor_type: {executor_type}")
        
        actual_executor_type = executor_type
        
        if executor_type == "c":
            try:
                from services.mongo_c_executor_service import mongo_c_executor_service
                if mongo_c_executor_service.is_available():
                    mongo_config = Config.get_mongo_config()
                    result = await mongo_c_executor_service.execute_single_mongo_query(
                        mongo_config['host'], mongo_config['port'],
                        mongo_config['user'], mongo_config['password'],
                        mongo_config['database'], mongo_query
                    )
                    actual_executor_type = "c"
                else:
                    logger.warning("MongoDB C executor not available, falling back to mongosh")
                    service = await get_mongosh_service()
                    result = await service.execute_mongo_query(mongo_query)
                    actual_executor_type = "mongosh"
            except ImportError:
                logger.warning("MongoDB C executor service not found, falling back to mongosh")
                service = await get_mongosh_service()
                result = await service.execute_mongo_query(mongo_query)
                actual_executor_type = "mongosh"
        else:
            # 默认使用mongosh执行器
            service = await get_mongosh_service()
            result = await service.execute_mongo_query(mongo_query)
            actual_executor_type = "mongosh"
        
        return {"success": True, "result": result, "executor_type": actual_executor_type}
    except Exception as e:
        logger.error(f"Error executing direct query: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/packet-capture/list")
async def list_packet_captures():
    """获取抓包文件列表"""
    try:
        files = packet_service.list_capture_files()
        return {"success": True, "files": files}
    except Exception as e:
        logger.error(f"Error listing packet captures: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/packet-capture/start")
async def start_packet_capture():
    """启动MongoDB抓包"""
    try:
        packet_file = await packet_service.start_capture()
        return {"success": True, "packet_file": packet_file}
    except Exception as e:
        logger.error(f"Error starting packet capture: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/packet-capture/stop")
async def stop_packet_capture():
    """停止MongoDB抓包"""
    try:
        packet_file = await packet_service.stop_capture()
        return {"success": True, "packet_file": packet_file}
    except Exception as e:
        logger.error(f"Error stopping packet capture: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/packet-capture/{filename}")
async def delete_packet_capture(filename: str):
    """删除抓包文件"""
    try:
        success = packet_service.delete_capture_file(filename)
        if success:
            return {"success": True, "message": f"File {filename} deleted"}
        else:
            raise HTTPException(status_code=404, detail="File not found")
    except Exception as e:
        logger.error(f"Error deleting packet capture: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

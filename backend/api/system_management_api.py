from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import logging
from services.system_management_service import SystemManagementService, DatabaseDeployment

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/system", tags=["system_management"])

# 全局系统管理服务实例将在运行时注入
system_management_service = None

def set_system_management_service(service):
    global system_management_service
    system_management_service = service

# Pydantic模型
class DeploymentRequest(BaseModel):
    name: str
    host: str
    port: int = 3306
    user: str
    password: str
    database: str
    container_name: Optional[str] = None
    container_id: Optional[str] = None
    deployment_type: str = "direct"  # direct, docker, k8s
    ssh_host: Optional[str] = None
    ssh_user: Optional[str] = "root"
    ssh_password: Optional[str] = None

class DeploymentResponse(BaseModel):
    name: str
    host: str
    port: int
    user: str
    password: str
    database: str
    container_name: Optional[str]
    container_id: Optional[str]
    deployment_type: str
    ssh_host: Optional[str]
    ssh_user: Optional[str]
    ssh_password: Optional[str]

class SystemCommandRequest(BaseModel):
    deployment_name: str
    command: str
    parameters: Optional[Dict[str, Any]] = {}

class SystemCommandResponse(BaseModel):
    success: bool
    command: str
    deployment_name: str
    result: Dict[str, Any]
    system_status: Optional[Dict[str, Any]] = None
    error: Optional[str] = None

@router.get("/deployments", response_model=List[DeploymentResponse])
async def list_deployments():
    """获取所有部署配置"""
    try:
        deployments = []
        for deployment in system_management_service.deployments.values():
            deployments.append(DeploymentResponse(
                name=deployment.name,
                host=deployment.host,
                port=deployment.port,
                user=deployment.user,
                password=deployment.password,
                database=deployment.database,
                container_name=deployment.container_name,
                container_id=deployment.container_id,
                deployment_type=deployment.deployment_type,
                ssh_host=deployment.ssh_host,
                ssh_user=deployment.ssh_user,
                ssh_password=deployment.ssh_password
            ))
        
        return deployments
    except Exception as e:
        logger.error(f"Failed to list deployments: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/deployments", response_model=Dict[str, str])
async def add_deployment(request: DeploymentRequest):
    """添加部署配置"""
    try:
        deployment = DatabaseDeployment(
            name=request.name,
            host=request.host,
            port=request.port,
            user=request.user,
            password=request.password,
            database=request.database,
            container_name=request.container_name,
            container_id=request.container_id,
            deployment_type=request.deployment_type,
            ssh_host=request.ssh_host or request.host,
            ssh_user=request.ssh_user,
            ssh_password=request.ssh_password
        )
        
        system_management_service.add_deployment(deployment)
        
        return {"message": f"Deployment '{request.name}' added successfully"}
    except Exception as e:
        logger.error(f"Failed to add deployment: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/deployments/{deployment_name}", response_model=Dict[str, str])
async def remove_deployment(deployment_name: str):
    """删除部署配置"""
    try:
        if deployment_name not in system_management_service.deployments:
            raise HTTPException(status_code=404, detail=f"Deployment '{deployment_name}' not found")
        
        system_management_service.remove_deployment(deployment_name)
        
        return {"message": f"Deployment '{deployment_name}' removed successfully"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to remove deployment: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/command", response_model=SystemCommandResponse)
async def execute_system_command(request: SystemCommandRequest):
    """执行系统管理命令"""
    try:
        if request.deployment_name not in system_management_service.deployments:
            raise HTTPException(status_code=404, detail=f"Deployment '{request.deployment_name}' not found")
        
        # 执行系统命令
        result = await system_management_service.execute_system_command(
            request.deployment_name,
            request.command,
            **request.parameters
        )
        
        # 获取系统状态
        system_status = await system_management_service.get_system_status(request.deployment_name)
        
        return SystemCommandResponse(
            success=True,
            command=request.command,
            deployment_name=request.deployment_name,
            result=result,
            system_status=system_status
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to execute system command: {e}")
        return SystemCommandResponse(
            success=False,
            command=request.command,
            deployment_name=request.deployment_name,
            result={},
            error=str(e)
        )

@router.get("/status/{deployment_name}", response_model=Dict[str, Any])
async def get_system_status(deployment_name: str):
    """获取系统状态"""
    try:
        if deployment_name not in system_management_service.deployments:
            raise HTTPException(status_code=404, detail=f"Deployment '{deployment_name}' not found")
        
        status = await system_management_service.get_system_status(deployment_name)
        return status
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get system status: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/capture/{deployment_name}", response_model=Dict[str, str])
async def start_system_capture(deployment_name: str, capture_dir: str = "/tmp"):
    """启动系统级抓包"""
    try:
        if deployment_name not in system_management_service.deployments:
            raise HTTPException(status_code=404, detail=f"Deployment '{deployment_name}' not found")
        
        packet_file = await system_management_service.start_system_capture(
            deployment_name, 
            capture_dir
        )
        
        return {
            "message": f"System capture started for '{deployment_name}'",
            "packet_file": packet_file
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to start system capture: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/commands", response_model=Dict[str, Any])
async def list_supported_commands():
    """获取支持的系统管理命令列表"""
    try:
        from services.mysql_protocol_service import MySQLProtocolService
        
        commands = {
            "protocol_commands": MySQLProtocolService.SUPPORTED_COMMANDS,
            "command_descriptions": {
                "REFRESH": "刷新缓存（如权限、日志）",
                "SHUTDOWN": "关闭服务器（需权限）",
                "STATISTICS": "获取服务器状态信息",
                "PROCESS_LIST": "查看活动线程",
                "KILL_THREAD": "终止指定线程",
                "CHANGE_USER": "切换连接用户",
                "SEND_BINLOG": "主从复制中传输binlog",
                "SEND_TABLE": "复制中传输表结构",
                "SLAVE_CONNECT": "从库连接主库",
                "REGISTER_SLAVE": "从库向主库注册自身信息",
                "SEND_BLOB": "发送大对象数据",
                "SET_OPTION": "设置客户端选项",
                "FETCH_DATA": "获取预处理语句的结果集",
                "SEND_BINLOG_GTID": "基于GTID的binlog传输",
                "RESET_CONNECTION": "重置会话状态",
                "NATIVE_CLONING": "本地克隆插件"
            },
            "deployment_types": ["direct", "docker", "k8s"],
            "examples": {
                "refresh_privileges": {
                    "command": "REFRESH_PRIVILEGES",
                    "description": "刷新权限缓存",
                    "natural_language": "帮我完成mysql的Refresh 7 刷新缓存（如权限、日志）命令"
                },
                "statistics": {
                    "command": "STATISTICS",
                    "description": "获取服务器统计信息",
                    "natural_language": "获取MySQL服务器状态统计信息"
                },
                "process_list": {
                    "command": "PROCESS_LIST",
                    "description": "查看当前进程列表",
                    "natural_language": "查看MySQL进程列表"
                }
            }
        }
        
        return commands
    except Exception as e:
        logger.error(f"Failed to list commands: {e}")
        raise HTTPException(status_code=500, detail=str(e))

"""
下载管理API
提供PCAP文件的下载、删除和管理功能
"""

import os
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, HTTPException, Response, Query, Request
from fastapi.responses import FileResponse
import logging
from services.capture_file_service import capture_file_service
from utils.execution_logger import ExecutionLogger
from models.execution_log import LogCategory
from utils.path_manager import path_manager

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/captures", tags=["downloads"])

@router.get("/files")
async def get_capture_files(
    database_type: Optional[str] = Query(None, description="数据库类型过滤"),
    limit: int = Query(100, description="每页数量"),
    offset: int = Query(0, description="偏移量")
):
    """获取抓包文件列表（从数据库查询）"""
    try:
        result = await capture_file_service.get_capture_files(
            database_type=database_type,
            limit=limit,
            offset=offset
        )

        # 计算总文件大小
        total_size = sum(f['file_size'] for f in result['files'])
        result['total_size'] = total_size

        return result

    except Exception as e:
        logger.error(f"Failed to get capture files: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/download/{filename}")
async def download_capture_file(filename: str):
    """下载指定的抓包文件"""

    # 创建执行日志记录器
    exec_logger = ExecutionLogger()

    try:
        await exec_logger.file_operation_info_async(f"开始下载抓包文件: {filename}", capture_file=filename)

        # 从数据库查询文件信息
        file_info = await capture_file_service.get_capture_file_by_filename(filename)

        if not file_info:
            await exec_logger.file_operation_error_async(f"抓包文件数据库记录不存在: {filename}",
                                                        capture_file=filename, error_details="文件记录不存在")
            raise HTTPException(status_code=404, detail="文件记录不存在")

        await exec_logger.file_operation_info_async(f"找到抓包文件数据库记录: {filename}, 类型: {file_info.database_type}",
                                                   capture_file=filename)

        # 检查物理文件是否存在
        # 使用统一路径管理器解析文件路径
        file_path = path_manager.resolve_capture_file_path(file_info.file_path)

        await exec_logger.file_operation_info_async(f"检查抓包文件物理路径: {file_path}",
                                                   capture_file=filename)
        logger.info(f"Checking file path: {file_path}")

        if not os.path.exists(file_path):
            error_msg = f"物理文件不存在: {file_path}"
            await exec_logger.file_operation_error_async(f"抓包文件物理文件不存在: {filename}",
                                                        capture_file=filename, error_details=error_msg)
            raise HTTPException(status_code=404, detail=error_msg)

        if not filename.endswith('.pcap'):
            error_msg = "只能下载pcap文件"
            await exec_logger.file_operation_error_async(f"文件类型不支持下载: {filename}",
                                                        capture_file=filename, error_details=error_msg)
            raise HTTPException(status_code=400, detail=error_msg)

        # 获取文件大小用于日志
        file_size = os.path.getsize(file_path)
        await exec_logger.file_operation_info_async(f"抓包文件下载成功: {filename}, 大小: {file_size} bytes",
                                                   capture_file=filename)

        return FileResponse(
            path=file_path,
            filename=filename,
            media_type='application/octet-stream'
        )

    except HTTPException:
        raise
    except Exception as e:
        await exec_logger.file_operation_error_async(f"下载抓包文件异常: {filename}",
                                                    capture_file=filename,
                                                    error_details=str(e), include_stack_trace=True)
        logger.error(f"Failed to download file {filename}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/delete/{filename}")
async def delete_capture_file(filename: str):
    """删除指定的抓包文件"""
    try:
        if not filename.endswith('.pcap'):
            raise HTTPException(status_code=400, detail="只能删除pcap文件")

        # 使用服务删除文件（包括数据库记录和物理文件）
        success = await capture_file_service.delete_capture_file(filename)

        if not success:
            raise HTTPException(status_code=404, detail="文件不存在")

        return {"success": True, "message": f"文件 {filename} 删除成功"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete file {filename}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/clear")
async def clear_all_capture_files():
    """清空所有抓包文件（从数据库删除）"""
    try:
        # 获取所有文件
        result = await capture_file_service.get_capture_files(limit=1000)
        files = result.get('files', [])

        deleted_count = 0
        for file_info in files:
            try:
                success = await capture_file_service.delete_capture_file(file_info['filename'])
                if success:
                    deleted_count += 1
            except Exception as e:
                logger.error(f"Failed to delete {file_info['filename']}: {e}")

        logger.info(f"Cleared {deleted_count} capture files")

        return {
            "success": True,
            "message": f"成功删除 {deleted_count} 个抓包文件",
            "deleted_count": deleted_count
        }

    except Exception as e:
        logger.error(f"Failed to clear capture files: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/stats")
async def get_capture_stats():
    """获取抓包文件统计信息（从数据库查询）"""
    try:
        # 获取所有文件
        result = await capture_file_service.get_capture_files(limit=1000)
        files = result.get('files', [])

        stats = {
            "total_files": result.get('total', 0),
            "total_size": sum(f['file_size'] for f in files),
            "mysql_files": len([f for f in files if f['database_type'] == 'mysql']),
            "mongodb_files": len([f for f in files if f['database_type'] == 'mongodb']),
            "postgresql_files": len([f for f in files if f['database_type'] == 'postgresql']),
            "gaussdb_files": len([f for f in files if f['database_type'] == 'gaussdb']),
            "oracle_files": len([f for f in files if f['database_type'] == 'oracle']),
            "latest_file": files[0] if files else None
        }
        
        latest_time = 0
        for file_path in pcap_files:
            file_info = get_file_info(file_path)
            if file_info:
                stats["total_size"] += file_info["size"]
                
                if file_info["type"] == "mysql":
                    stats["mysql_files"] += 1
                elif file_info["type"] == "mongodb":
                    stats["mongodb_files"] += 1
                elif file_info["type"] == "postgresql":
                    stats["postgresql_files"] += 1
                elif file_info["type"] == "gaussdb":
                    stats["gaussdb_files"] += 1
                elif file_info["type"] == "oracle":
                    stats["oracle_files"] += 1
                
                # 找到最新文件
                file_time = os.path.getctime(file_path)
                if file_time > latest_time:
                    latest_time = file_time
                    stats["latest_file"] = file_info["name"]
        
        return stats

    except Exception as e:
        logger.error(f"Failed to get capture stats: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/cleanup-empty")
async def cleanup_empty_capture_files():
    """清理空的抓包文件（小于等于24字节的文件）"""
    try:
        # 获取所有文件
        result = await capture_file_service.get_capture_files(limit=1000)
        files = result.get('files', [])

        cleaned_count = 0
        for file_info in files:
            if file_info['file_size'] <= 24:  # 空的pcap文件
                try:
                    success = await capture_file_service.delete_capture_file(file_info['filename'])
                    if success:
                        cleaned_count += 1
                        logger.info(f"Cleaned empty capture file: {file_info['filename']}")
                except Exception as e:
                    logger.error(f"Failed to clean empty file {file_info['filename']}: {e}")

        return {
            "success": True,
            "message": f"成功清理 {cleaned_count} 个空的抓包文件",
            "cleaned_count": cleaned_count
        }

    except Exception as e:
        logger.error(f"Failed to cleanup empty capture files: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/cleanup-missing")
async def cleanup_missing_capture_files():
    """清理物理文件不存在的数据库记录"""
    try:
        # 获取所有文件
        result = await capture_file_service.get_capture_files(limit=1000)
        files = result.get('files', [])

        logger.info(f"Found {len(files)} files in database for cleanup check")

        cleaned_count = 0
        for file_info in files:
            file_path = file_info['file_path']

            # 使用统一路径管理器解析文件路径
            file_path = path_manager.resolve_capture_file_path(file_path)

            logger.info(f"Checking file: {file_path}")

            if not os.path.exists(file_path):  # 物理文件不存在
                logger.info(f"File does not exist: {file_path}, deleting record")
                try:
                    success = await capture_file_service.delete_capture_file(file_info['filename'])
                    if success:
                        cleaned_count += 1
                        logger.info(f"Cleaned missing capture file record: {file_info['filename']} (file not found: {file_path})")
                    else:
                        logger.warning(f"Failed to delete record for missing file: {file_info['filename']}")
                except Exception as e:
                    logger.error(f"Failed to clean missing file record {file_info['filename']}: {e}")
            else:
                logger.info(f"File exists: {file_path}")

        return {
            "success": True,
            "message": f"成功清理 {cleaned_count} 个物理文件不存在的记录",
            "cleaned_count": cleaned_count
        }

    except Exception as e:
        logger.error(f"Failed to cleanup missing capture files: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/verify-consistency")
async def verify_file_consistency():
    """验证抓包文件的数据库记录与物理文件的一致性"""
    try:
        consistency_report = await capture_file_service.verify_file_consistency()

        return {
            "success": True,
            "report": consistency_report
        }

    except Exception as e:
        logger.error(f"Failed to verify file consistency: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/upload/{filename}")
async def upload_capture_file(filename: str, request: Request):
    """上传抓包文件到远程服务器"""
    try:
        if not filename.endswith('.pcap'):
            raise HTTPException(status_code=400, detail="只能上传pcap文件")

        # 读取请求体中的二进制数据
        file_content = await request.body()

        if not file_content:
            raise HTTPException(status_code=400, detail="文件内容不能为空")

        # 确保captures目录存在
        captures_dir = os.path.join(os.getcwd(), "backend/captures")
        os.makedirs(captures_dir, exist_ok=True)

        # 保存文件
        file_path = os.path.join(captures_dir, filename)
        with open(file_path, 'wb') as f:
            f.write(file_content)

        file_size = len(file_content)
        logger.info(f"Uploaded capture file: {filename} ({file_size} bytes)")

        return {
            "success": True,
            "message": f"文件 {filename} 上传成功",
            "file_size": file_size,
            "file_path": file_path
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to upload file {filename}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/analyze/{filename}")
async def analyze_capture_file(filename: str):
    """分析指定的抓包文件"""
    try:
        file_path = os.path.join(CAPTURE_DIR, filename)

        if not os.path.exists(file_path):
            raise HTTPException(status_code=404, detail="文件不存在")

        # 这里可以集成具体的抓包分析逻辑
        # 目前返回模拟数据
        analysis = {
            "total_packets": 0,
            "valid_packets": 0,
            "connections": 0,
            "operations": 0,
            "protocols": [],
            "summary": "分析功能待实现"
        }

        return {
            "success": True,
            "filename": filename,
            "analysis": analysis
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to analyze file {filename}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

"""
测试用例管理API路由
"""
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, HTTPException, Query, Depends
from fastapi.responses import JSONResponse, FileResponse
import logging
import os

from models.test_case_management import (
    TestCaseManagementCreate,
    TestCaseManagementUpdate,
    TestCaseManagementResponse,
    TestCaseManagementQuery,
    TestCaseManagementListResponse,
    TestCaseExecutionRecord,
    TestCaseLastExecution,
    TestCaseStatistics,
    PriorityEnum,
    StatusEnum,
    ReviewStatusEnum,
    AutomationLevelEnum,
    DatabaseTypeEnum,
    DATABASE_VERSIONS
)
from services.test_case_management_service import test_case_management_service
from services.test_case_ai_service import test_case_ai_service

logger = logging.getLogger(__name__)

router = APIRouter(tags=["测试用例管理"])


# 特殊路由 - 必须在参数路由之前
@router.get("/test-cases/statistics", response_model=TestCaseStatistics)
async def get_statistics():
    """获取测试用例统计信息"""
    try:
        result = await test_case_management_service.get_statistics()
        return result
    except Exception as e:
        logger.error(f"Failed to get statistics: {e}")
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")


@router.get("/test-cases/modules")
async def get_modules():
    """获取所有模块列表"""
    try:
        modules = await test_case_management_service.get_modules()
        return {"modules": modules}
    except Exception as e:
        logger.error(f"Failed to get modules: {e}")
        raise HTTPException(status_code=500, detail=f"获取模块列表失败: {str(e)}")


@router.get("/test-cases/authors")
async def get_authors():
    """获取所有创建者列表"""
    try:
        authors = await test_case_management_service.get_authors()
        return {"authors": authors}
    except Exception as e:
        logger.error(f"Failed to get authors: {e}")
        raise HTTPException(status_code=500, detail=f"获取创建者列表失败: {str(e)}")


@router.get("/test-cases/reviewers")
async def get_reviewers():
    """获取所有评审者列表"""
    try:
        reviewers = await test_case_management_service.get_reviewers()
        return {"reviewers": reviewers}
    except Exception as e:
        logger.error(f"Failed to get reviewers: {e}")
        raise HTTPException(status_code=500, detail=f"获取评审者列表失败: {str(e)}")


@router.get("/test-cases/database-versions")
async def get_database_versions():
    """获取数据库版本选项"""
    try:
        return {
            "success": True,
            "data": DATABASE_VERSIONS,
            "message": "获取数据库版本成功"
        }
    except Exception as e:
        logger.error(f"获取数据库版本失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取数据库版本失败: {str(e)}")


@router.get("/test-cases/export")
async def export_test_cases(
    keyword: Optional[str] = Query(None, description="关键词搜索"),
    module: Optional[str] = Query(None, description="模块筛选"),
    priority: Optional[str] = Query(None, description="优先级筛选"),
    status: Optional[str] = Query(None, description="状态筛选"),
    author: Optional[str] = Query(None, description="创建者筛选"),
    reviewer: Optional[str] = Query(None, description="评审者筛选"),
    review_status: Optional[str] = Query(None, description="评审状态筛选"),
    automation_level: Optional[str] = Query(None, description="自动化程度筛选"),
    database_type: Optional[str] = Query(None, description="数据库类型筛选"),
    database_version: Optional[str] = Query(None, description="数据库版本筛选"),
    auto_execute: Optional[bool] = Query(None, description="自动执行筛选"),
    tags: Optional[List[str]] = Query(None, description="标签筛选"),
    format: str = Query("excel", description="导出格式: json, csv, excel")
):
    """导出测试用例"""
    try:
        # 处理空字符串，转换为None或相应的枚举类型
        def parse_enum_or_none(value, enum_class):
            if not value or value == "":
                return None
            try:
                return enum_class(value)
            except ValueError:
                return None

        # 构建查询对象，用于导出（不需要分页参数）
        query = TestCaseManagementQuery(
            page=1,
            page_size=100,  # 使用最大允许值，但实际导出时会忽略分页
            keyword=keyword if keyword and keyword.strip() else None,
            module=module if module and module.strip() else None,
            priority=parse_enum_or_none(priority, PriorityEnum),
            status=parse_enum_or_none(status, StatusEnum),
            author=author if author and author.strip() else None,
            reviewer=reviewer if reviewer and reviewer.strip() else None,
            review_status=parse_enum_or_none(review_status, ReviewStatusEnum),
            automation_level=parse_enum_or_none(automation_level, AutomationLevelEnum),
            database_type=parse_enum_or_none(database_type, DatabaseTypeEnum),
            database_version=database_version if database_version and database_version.strip() else None,
            auto_execute=auto_execute,
            tags=tags
        )

        # 调用服务层导出方法
        file_path = await test_case_management_service.export_test_cases(query, format)

        # 根据格式设置文件名和媒体类型
        if format == "json":
            filename = "test_cases.json"
            media_type = "application/json"
        elif format == "csv":
            filename = "test_cases.csv"
            media_type = "text/csv"
        elif format == "excel":
            filename = "test_cases.xlsx"
            media_type = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        else:
            raise HTTPException(status_code=400, detail="不支持的导出格式")

        return FileResponse(
            path=file_path,
            filename=filename,
            media_type=media_type,
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )
    except Exception as e:
        logger.error(f"Failed to export test cases: {e}")
        raise HTTPException(status_code=500, detail=f"导出测试用例失败: {str(e)}")


@router.post("/test-cases", response_model=TestCaseManagementResponse)
async def create_test_case(test_case: TestCaseManagementCreate):
    """创建测试用例"""
    try:
        result = await test_case_management_service.create_test_case(test_case)
        return result
    except Exception as e:
        logger.error(f"Failed to create test case: {e}")
        raise HTTPException(status_code=500, detail=f"创建测试用例失败: {str(e)}")


@router.get("/test-cases", response_model=TestCaseManagementListResponse)
async def list_test_cases(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    keyword: Optional[str] = Query(None, description="关键词搜索"),
    module: Optional[str] = Query(None, description="模块筛选"),
    priority: Optional[str] = Query(None, description="优先级筛选"),
    status: Optional[str] = Query(None, description="状态筛选"),
    author: Optional[str] = Query(None, description="创建者筛选"),
    reviewer: Optional[str] = Query(None, description="评审者筛选"),
    review_status: Optional[str] = Query(None, description="评审状态筛选"),
    automation_level: Optional[str] = Query(None, description="自动化程度筛选"),
    database_type: Optional[str] = Query(None, description="数据库类型筛选"),
    database_version: Optional[str] = Query(None, description="数据库版本筛选"),
    auto_execute: Optional[bool] = Query(None, description="自动执行筛选"),
    tags: Optional[List[str]] = Query(None, description="标签筛选")
):
    """获取测试用例列表"""
    try:
        # 处理空字符串，转换为None或相应的枚举类型
        def parse_enum_or_none(value, enum_class):
            if not value or value == "":
                return None
            try:
                return enum_class(value)
            except ValueError:
                return None

        query = TestCaseManagementQuery(
            page=page,
            page_size=page_size,
            keyword=keyword if keyword and keyword.strip() else None,
            module=module if module and module.strip() else None,
            priority=parse_enum_or_none(priority, PriorityEnum),
            status=parse_enum_or_none(status, StatusEnum),
            author=author if author and author.strip() else None,
            reviewer=reviewer if reviewer and reviewer.strip() else None,
            review_status=parse_enum_or_none(review_status, ReviewStatusEnum),
            automation_level=parse_enum_or_none(automation_level, AutomationLevelEnum),
            database_type=parse_enum_or_none(database_type, DatabaseTypeEnum),
            database_version=database_version if database_version and database_version.strip() else None,
            auto_execute=auto_execute,
            tags=tags
        )
        result = await test_case_management_service.list_test_cases(query)
        return result
    except Exception as e:
        logger.error(f"Failed to list test cases: {e}")
        raise HTTPException(status_code=500, detail=f"获取测试用例列表失败: {str(e)}")


@router.get("/test-cases/{test_case_id}", response_model=TestCaseManagementResponse)
async def get_test_case(test_case_id: str):
    """获取测试用例详情"""
    try:
        result = await test_case_management_service.get_test_case(test_case_id)
        if not result:
            raise HTTPException(status_code=404, detail="测试用例不存在")
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get test case {test_case_id}: {e}")
        raise HTTPException(status_code=500, detail=f"获取测试用例失败: {str(e)}")


@router.put("/test-cases/{test_case_id}", response_model=TestCaseManagementResponse)
async def update_test_case(test_case_id: str, test_case: TestCaseManagementUpdate):
    """更新测试用例"""
    try:
        result = await test_case_management_service.update_test_case(test_case_id, test_case)
        if not result:
            raise HTTPException(status_code=404, detail="测试用例不存在")
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update test case {test_case_id}: {e}")
        raise HTTPException(status_code=500, detail=f"更新测试用例失败: {str(e)}")


@router.delete("/test-cases/batch")
async def batch_delete_test_cases(test_case_ids: List[str]):
    """批量删除测试用例"""
    try:
        if not test_case_ids:
            raise HTTPException(status_code=400, detail="测试用例ID列表不能为空")

        deleted_count = await test_case_management_service.batch_delete_test_cases(test_case_ids)
        return {
            "message": f"成功删除 {deleted_count} 个测试用例",
            "deleted_count": deleted_count,
            "total_requested": len(test_case_ids)
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to batch delete test cases: {e}")
        raise HTTPException(status_code=500, detail=f"批量删除测试用例失败: {str(e)}")


@router.delete("/test-cases/{test_case_id}")
async def delete_test_case(test_case_id: str):
    """删除测试用例"""
    try:
        success = await test_case_management_service.delete_test_case(test_case_id)
        if not success:
            raise HTTPException(status_code=404, detail="测试用例不存在")
        return {"message": "测试用例删除成功"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete test case {test_case_id}: {e}")
        raise HTTPException(status_code=500, detail=f"删除测试用例失败: {str(e)}")



@router.get("/test-cases/{test_case_id}/last-execution", response_model=Optional[TestCaseLastExecution])
async def get_last_execution(test_case_id: str):
    """获取测试用例的最近执行记录"""
    try:
        last_execution = await test_case_management_service.get_last_execution(test_case_id)
        return last_execution
    except Exception as e:
        logger.error(f"Failed to get last execution for test case {test_case_id}: {e}")
        raise HTTPException(status_code=500, detail=f"获取最近执行记录失败: {str(e)}")


@router.put("/test-cases/{test_case_id}/status")
async def update_test_case_status(
    test_case_id: str,
    status: StatusEnum
):
    """更新单个测试用例状态"""
    try:
        success = await test_case_management_service.update_test_case_status(test_case_id, status)
        if success:
            return {"message": "测试用例状态更新成功"}
        else:
            raise HTTPException(status_code=404, detail="测试用例不存在")
    except Exception as e:
        logger.error(f"Failed to update test case status: {e}")
        raise HTTPException(status_code=500, detail=f"更新测试用例状态失败: {str(e)}")





@router.post("/test-cases/{test_case_id}/duplicate", response_model=TestCaseManagementResponse)
async def duplicate_test_case(test_case_id: str, new_title: str):
    """复制测试用例"""
    try:
        result = await test_case_management_service.duplicate_test_case(test_case_id, new_title)
        if not result:
            raise HTTPException(status_code=404, detail="原测试用例不存在")
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to duplicate test case {test_case_id}: {e}")
        raise HTTPException(status_code=500, detail=f"复制测试用例失败: {str(e)}")





@router.post("/test-cases/import")
async def import_test_cases():
    """导入测试用例"""
    try:
        # 这里可以实现导入功能，暂时返回提示
        return {"message": "导入功能开发中"}
    except Exception as e:
        logger.error(f"Failed to import test cases: {e}")
        raise HTTPException(status_code=500, detail=f"导入测试用例失败: {str(e)}")


# AI生成测试用例相关接口

@router.post("/test-cases/ai/generate")
async def ai_generate_test_case(
    requirement: str = Query(..., description="需求描述"),
    database_type: str = Query("mysql", description="数据库类型"),
    operation_type: str = Query("查询", description="操作类型")
):
    """AI生成测试用例"""
    try:
        logger.info(f"AI生成测试用例请求 - 需求: {requirement}, 数据库: {database_type}, 操作: {operation_type}")

        result = await test_case_ai_service.generate_test_case(
            requirement=requirement,
            database_type=database_type,
            operation_type=operation_type
        )

        if result["success"]:
            # 检查是否为批量生成
            if result.get("is_batch", False):
                return {
                    "success": True,
                    "data": result["test_cases"],
                    "is_batch": True,
                    "message": result["message"]
                }
            else:
                return {
                    "success": True,
                    "data": result["test_case"],
                    "is_batch": False,
                    "message": result["message"]
                }
        else:
            raise HTTPException(status_code=500, detail=result["message"])

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"AI生成测试用例失败: {e}")
        raise HTTPException(status_code=500, detail=f"AI生成测试用例失败: {str(e)}")


@router.post("/test-cases/ai/review")
async def ai_review_test_case(test_case: dict):
    """AI评审测试用例"""
    try:
        logger.info(f"AI评审测试用例请求 - 标题: {test_case.get('title', '未知')}")

        result = await test_case_ai_service.review_test_case(test_case)

        if result["success"]:
            return {
                "success": True,
                "data": result["review_result"],
                "message": result["message"]
            }
        else:
            raise HTTPException(status_code=500, detail=result["message"])

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"AI评审测试用例失败: {e}")
        raise HTTPException(status_code=500, detail=f"AI评审测试用例失败: {str(e)}")


@router.post("/test-cases/ai/regenerate")
async def ai_regenerate_test_case(
    original_test_case: dict,
    review_feedback: dict
):
    """AI根据评审反馈重新生成测试用例"""
    try:
        logger.info(f"AI重新生成测试用例请求 - 标题: {original_test_case.get('title', '未知')}")

        result = await test_case_ai_service.regenerate_test_case(
            original_test_case=original_test_case,
            review_feedback=review_feedback
        )

        if result["success"]:
            return {
                "success": True,
                "data": result["test_case"],
                "message": result["message"]
            }
        else:
            raise HTTPException(status_code=500, detail=result["message"])

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"AI重新生成测试用例失败: {e}")
        raise HTTPException(status_code=500, detail=f"AI重新生成测试用例失败: {str(e)}")


@router.post("/test-cases/ai/workflow")
async def ai_test_case_workflow(
    requirement: str = Query(..., description="需求描述"),
    database_type: str = Query("mysql", description="数据库类型"),
    operation_type: str = Query("查询", description="操作类型")
):
    """AI测试用例完整工作流（生成->评审->改进）"""
    try:
        logger.info(f"AI测试用例工作流请求 - 需求: {requirement}")

        result = await test_case_ai_service.generate_and_review_workflow(
            requirement=requirement,
            database_type=database_type,
            operation_type=operation_type
        )

        if result["success"]:
            return {
                "success": True,
                "data": {
                    "original_test_case": result["test_case"],
                    "review_result": result["review_result"],
                    "improved_test_case": result["improved_test_case"]
                },
                "message": result["message"]
            }
        else:
            raise HTTPException(status_code=500, detail=result["message"])

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"AI测试用例工作流失败: {e}")
        raise HTTPException(status_code=500, detail=f"AI测试用例工作流失败: {str(e)}")


@router.post("/test-cases/ai/batch-generate")
async def ai_batch_generate_test_cases(
    requirement: str = Query(..., description="需求描述"),
    database_type: str = Query("mysql", description="数据库类型"),
    database_version: str = Query("", description="数据库版本"),
    operation_type: str = Query("查询", description="操作类型")
):
    """AI批量生成测试用例"""
    try:
        logger.info(f"AI批量生成测试用例请求 - 需求: {requirement}, 数据库: {database_type} {database_version}")

        # 强制使用批量生成
        from services.test_case_ai_service import test_case_ai_service
        test_cases = await test_case_ai_service.batch_generation_agent.generate_batch_test_cases(
            requirement=requirement,
            database_type=database_type,
            database_version=database_version,
            operation_type=operation_type
        )

        if test_cases:
            return {
                "success": True,
                "data": test_cases,
                "count": len(test_cases),
                "message": f"批量生成成功，共生成 {len(test_cases)} 个测试用例"
            }
        else:
            raise HTTPException(status_code=500, detail="批量生成失败，未能生成测试用例")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"AI批量生成测试用例失败: {e}")
        raise HTTPException(status_code=500, detail=f"AI批量生成测试用例失败: {str(e)}")


@router.post("/test-cases/ai/async-generate")
async def ai_async_generate_test_cases(
    requirement: str = Query(..., description="需求描述"),
    database_type: str = Query("mysql", description="数据库类型"),
    database_version: str = Query("", description="数据库版本"),
    operation_type: str = Query("查询", description="操作类型"),
    batch_size: int = Query(0, description="最大生成数量限制(0表示不限制，由AI智能决定)", ge=0, le=50)
):
    """AI异步生成测试用例 - AI会根据需求智能决定生成数量"""
    try:
        logger.info(f"AI异步生成测试用例请求 - 需求: {requirement}, 数据库: {database_type} {database_version}, 操作类型: {operation_type}, 最大限制: {batch_size if batch_size > 0 else '无限制'}")

        # 提交异步任务
        from services.arq_task_management_service import arq_task_management_service
        task_id = await arq_task_management_service.submit_ai_test_case_generation_task(
            requirement=requirement,
            database_type=database_type,
            database_version=database_version,
            operation_type=operation_type,
            batch_size=batch_size
        )

        message = f"AI测试用例生成任务已提交，任务ID: {task_id}"
        if batch_size > 0:
            message += f"，最多生成 {batch_size} 个测试用例"
        else:
            message += "，AI将根据需求智能决定生成数量"

        return {
            "success": True,
            "task_id": task_id,
            "message": message
        }

    except Exception as e:
        logger.error(f"AI异步生成测试用例失败: {e}")
        raise HTTPException(status_code=500, detail=f"AI异步生成测试用例失败: {str(e)}")


@router.get("/captures/download/{filename}")
async def download_capture_file(filename: str):
    """下载抓包文件"""
    try:
        # 安全检查：只允许下载.pcap文件
        if not filename.endswith('.pcap'):
            raise HTTPException(status_code=400, detail="只能下载.pcap文件")

        # 构建文件路径（修复：使用统一的路径管理器，避免路径拼接错误）
        from utils.path_manager import path_manager
        captures_dir = path_manager.get_captures_dir()
        file_path = os.path.join(captures_dir, filename)

        # 检查文件是否存在
        if not os.path.exists(file_path):
            raise HTTPException(status_code=404, detail="文件不存在")

        # 安全检查：确保文件在captures目录内
        if not os.path.abspath(file_path).startswith(os.path.abspath(captures_dir)):
            raise HTTPException(status_code=403, detail="访问被拒绝")

        return FileResponse(
            path=file_path,
            filename=filename,
            media_type='application/octet-stream'
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to download capture file {filename}: {e}")
        raise HTTPException(status_code=500, detail=f"下载文件失败: {str(e)}")


@router.post("/captures/register-missing")
async def register_missing_capture_files():
    """注册缺失的抓包文件到数据库"""
    try:
        from services.capture_file_service import capture_file_service
        from models.capture_file import CaptureFileCreate
        import re

        # 确保服务已初始化
        await capture_file_service.initialize()

        # 获取captures目录
        captures_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "backend/captures")

        if not os.path.exists(captures_dir):
            raise HTTPException(status_code=404, detail="抓包目录不存在")

        registered_count = 0
        skipped_count = 0

        # 遍历captures目录中的所有pcap文件
        for filename in os.listdir(captures_dir):
            if not filename.endswith('.pcap'):
                continue

            # 检查文件是否已经在数据库中
            existing_file = await capture_file_service.get_capture_file_by_filename(filename)
            if existing_file:
                skipped_count += 1
                continue

            file_path = os.path.join(captures_dir, filename)
            file_size = os.path.getsize(file_path)

            # 从文件名解析数据库类型和时间
            database_type = "unknown"
            target_host = "localhost"
            target_port = 0

            if filename.startswith("mysql_"):
                database_type = "mysql"
                target_port = 3306
            elif filename.startswith("postgres_"):
                database_type = "postgresql"
                target_port = 5432
            elif filename.startswith("mongodb_"):
                database_type = "mongodb"
                target_port = 27017
            elif filename.startswith("oracle_"):
                database_type = "oracle"
                target_port = 1521
            elif filename.startswith("gaussdb_"):
                database_type = "gaussdb"
                target_port = 5432

            # 创建抓包文件记录
            capture_data = CaptureFileCreate(
                filename=filename,
                file_path=f"backend/captures/{filename}",  # 相对路径
                file_size=file_size,
                database_type=database_type,
                target_host=target_host,
                target_port=target_port,
                description=f"手动注册的{database_type}抓包文件"
            )

            try:
                await capture_file_service.save_capture_file(capture_data)
                registered_count += 1
                logger.info(f"已注册抓包文件: {filename}")
            except Exception as e:
                logger.error(f"注册抓包文件失败 {filename}: {e}")

        return {
            "success": True,
            "message": f"成功注册 {registered_count} 个抓包文件，跳过 {skipped_count} 个已存在的文件",
            "registered_count": registered_count,
            "skipped_count": skipped_count
        }

    except Exception as e:
        logger.error(f"注册抓包文件失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/test-cases/standardize-format")
async def standardize_test_case_format():
    """标准化所有测试用例格式，将SQL统一存储到test_data字段"""
    try:
        logger.info("开始标准化测试用例格式...")

        # 导入标准化脚本
        import sys
        import os
        sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'scripts'))

        from standardize_test_cases import TestCaseStandardizer

        # 执行标准化
        standardizer = TestCaseStandardizer()
        try:
            standardizer.standardize_all_test_cases()
            return {
                "success": True,
                "message": "测试用例格式标准化完成！所有SQL语句已统一存储到test_data字段中。"
            }
        finally:
            standardizer.close()

    except Exception as e:
        logger.error(f"标准化测试用例格式失败: {e}")
        raise HTTPException(status_code=500, detail=f"标准化失败: {str(e)}")
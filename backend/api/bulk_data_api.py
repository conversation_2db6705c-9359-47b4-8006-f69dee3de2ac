"""
大批量数据操作API
专门处理大量数据的增删改查操作
"""

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Optional, Dict, Any
import logging
from services.bulk_data_generator_service import bulk_data_generator_service
from services.mysql_async_capture_service import mysql_async_capture_service
from utils.exception_handler import log_exceptions, create_error_response

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/bulk-data", tags=["bulk-data"])

# Pydantic模型
class BulkOperationRequest(BaseModel):
    query: str
    config_id: Optional[int] = None
    execute_sql: bool = False  # 是否执行生成的SQL
    capture_packets: bool = True  # 是否进行数据包捕获

class BulkSQLExecutionRequest(BaseModel):
    sql_query: str
    config_id: Optional[int] = None
    capture_packets: bool = True

@router.post("/detect", response_model=Dict[str, Any])
@log_exceptions(reraise=False)
async def detect_bulk_operation(request: BulkOperationRequest):
    """检测是否为大批量操作请求"""
    try:
        is_bulk = bulk_data_generator_service.detect_bulk_operation(request.query)
        
        if is_bulk:
            # 提取详细信息
            quantity, table_name, operation_type = bulk_data_generator_service.extract_quantity_and_table(request.query)
            
            return {
                "success": True,
                "is_bulk_operation": is_bulk,
                "detected_details": {
                    "quantity": quantity,
                    "table_name": table_name,
                    "operation_type": operation_type
                },
                "query": request.query
            }
        else:
            return {
                "success": True,
                "is_bulk_operation": is_bulk,
                "query": request.query,
                "message": "这不是一个大批量操作请求"
            }
            
    except Exception as e:
        error_response = create_error_response(e, "检测大批量操作")
        raise HTTPException(status_code=500, detail=error_response)

@router.post("/generate-sql", response_model=Dict[str, Any])
@log_exceptions(reraise=False) 
async def generate_bulk_sql(request: BulkOperationRequest):
    """生成大批量操作SQL语句"""
    try:
        logger.info(f"Generating bulk SQL for query: {request.query}")
        
        # 处理大批量操作请求
        result = await bulk_data_generator_service.process_bulk_operation_request(
            request.query, 
            request.config_id
        )
        
        if not result.get("success"):
            raise HTTPException(status_code=400, detail=result.get("error", "Failed to generate bulk SQL"))
        
        # 如果用户要求执行SQL
        if request.execute_sql and request.config_id:
            try:
                execution_result = await mysql_async_capture_service.execute_sql_with_async_capture(
                    result.get("generated_sql"),
                    request.config_id
                )
                result["execution_result"] = execution_result
                result["sql_executed"] = True
            except Exception as e:
                logger.warning(f"SQL execution failed: {str(e)}")
                result["execution_error"] = str(e)
                result["sql_executed"] = False
        
        return result
        
    except Exception as e:
        error_response = create_error_response(e, "生成大批量SQL")
        raise HTTPException(status_code=500, detail=error_response)

@router.post("/execute-bulk-sql", response_model=Dict[str, Any])
@log_exceptions(reraise=False)
async def execute_bulk_sql(request: BulkSQLExecutionRequest):
    """执行大批量SQL语句"""
    try:
        logger.info(f"Executing bulk SQL: {request.sql_query[:100]}...")
        
        if not request.config_id:
            raise HTTPException(status_code=400, detail="config_id is required for SQL execution")
        
        # 使用异步抓包服务执行SQL
        if request.capture_packets:
            result = await mysql_async_capture_service.execute_sql_with_async_capture(
                request.sql_query,
                request.config_id
            )
        else:
            # 如果不需要抓包，直接执行SQL（需要配置MySQL服务）
            await bulk_data_generator_service.configure_mysql_connection(request.config_id)
            
            # 这里可以添加直接执行SQL的逻辑
            result = {
                "success": True,
                "sql_query": request.sql_query,
                "message": "SQL prepared for execution (capture disabled)",
                "capture_enabled": False
            }
        
        return result
        
    except Exception as e:
        error_response = create_error_response(e, "执行大批量SQL")
        raise HTTPException(status_code=500, detail=error_response)

@router.post("/smart-bulk-operation", response_model=Dict[str, Any])
@log_exceptions(reraise=False)
async def smart_bulk_operation(request: BulkOperationRequest):
    """智能大批量操作 - 一键检测、生成并可选执行"""
    try:
        logger.info(f"Smart bulk operation for: {request.query}")
        
        # 第一步：检测是否为大批量操作
        is_bulk = bulk_data_generator_service.detect_bulk_operation(request.query)
        
        if not is_bulk:
            return {
                "success": False,
                "error": "这不是一个大批量操作请求",
                "query": request.query,
                "is_bulk_operation": False
            }
        
        # 第二步：生成SQL
        bulk_result = await bulk_data_generator_service.process_bulk_operation_request(
            request.query, 
            request.config_id
        )
        
        if not bulk_result.get("success"):
            return bulk_result
        
        # 第三步：如果要求执行SQL，则执行
        if request.execute_sql and request.config_id:
            try:
                execution_result = await mysql_async_capture_service.execute_sql_with_async_capture(
                    bulk_result.get("generated_sql"),
                    request.config_id
                )
                bulk_result["execution_result"] = execution_result
                bulk_result["sql_executed"] = True
                bulk_result["capture_enabled"] = request.capture_packets
            except Exception as e:
                logger.error(f"SQL execution failed: {str(e)}")
                bulk_result["execution_error"] = str(e)
                bulk_result["sql_executed"] = False
        
        # 添加处理流程信息
        bulk_result["processing_steps"] = [
            "检测大批量操作请求",
            "提取操作参数（数量、表名、操作类型）",
            "获取数据库表结构",
            "生成对应的批量SQL语句"
        ]
        
        if request.execute_sql:
            if bulk_result.get("sql_executed"):
                bulk_result["processing_steps"].extend([
                    "启动数据包捕获",
                    "执行批量SQL语句",
                    "停止数据包捕获"
                ])
            else:
                bulk_result["processing_steps"].append("SQL执行失败")
        
        return bulk_result
        
    except Exception as e:
        error_response = create_error_response(e, "智能大批量操作")
        raise HTTPException(status_code=500, detail=error_response)

@router.get("/templates", response_model=Dict[str, Any])
async def get_bulk_operation_templates():
    """获取大批量操作模板示例"""
    try:
        templates = {
            "插入操作": [
                "帮我在users表插入1000条用户数据",
                "向orders表添加5000条订单记录", 
                "批量插入500条商品数据到products表"
            ],
            "删除操作": [
                "删除users表中的前1000条记录",
                "批量删除orders表中500条过期订单",
                "清理products表中的100条测试数据"
            ],
            "更新操作": [
                "批量更新users表中1000条用户状态",
                "修改orders表中500条订单的状态为已完成",
                "更新products表中200条商品的价格"
            ],
            "查询操作": [
                "查询users表中的前1000条记录",
                "获取orders表中最新的500条订单",
                "显示products表中价格最高的100条商品"
            ]
        }
        
        return {
            "success": True,
            "templates": templates,
            "usage_tips": [
                "在查询中明确指定数量（如1000条、500个等）",
                "指定目标表名",
                "描述清楚要执行的操作类型",
                "数量建议在50以上才会被识别为批量操作"
            ]
        }
        
    except Exception as e:
        logger.error(f"Failed to get bulk operation templates: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/config/{config_id}/table-info", response_model=Dict[str, Any])
async def get_table_info_for_bulk_operations(config_id: int, table_name: Optional[str] = None):
    """获取数据库表信息，用于大批量操作参考"""
    try:
        # 配置数据库连接
        connection_result = await bulk_data_generator_service.configure_mysql_connection(config_id)
        if not connection_result.get("success"):
            raise HTTPException(status_code=400, detail=connection_result.get("error"))
        
        if table_name:
            # 获取指定表的结构
            table_structure = await bulk_data_generator_service.get_table_structure(table_name)
            return {
                "success": True,
                "config_id": config_id,
                "table_name": table_name,
                "table_structure": table_structure
            }
        else:
            # 获取数据库中的所有表
            mysql_service = bulk_data_generator_service.mysql_service
            if not mysql_service:
                raise Exception("MySQL service not configured")
            
            tables = await mysql_service.get_tables()
            return {
                "success": True,
                "config_id": config_id,
                "tables": tables,
                "table_count": len(tables)
            }
        
    except Exception as e:
        logger.error(f"Failed to get table info: {e}")
        raise HTTPException(status_code=500, detail=str(e))

"""
tcpdump进程管理API
提供tcpdump进程的查看、管理和清理功能
"""

import logging
import time
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/tcpdump", tags=["tcpdump管理"])

class TcpdumpProcessInfo(BaseModel):
    """tcpdump进程信息"""
    pid: int
    task_id: str
    command: str
    start_time: float
    running_time: float
    capture_file: Optional[str]
    database_type: Optional[str]
    is_remote: bool
    server_host: Optional[str]

class TcpdumpProcessListResponse(BaseModel):
    """tcpdump进程列表响应"""
    processes: List[TcpdumpProcessInfo]
    total_count: int
    message: str

class CleanupResponse(BaseModel):
    """清理响应"""
    success: bool
    message: str
    cleaned_count: int

@router.get("/processes", response_model=TcpdumpProcessListResponse)
async def get_tcpdump_processes(
    task_id: Optional[str] = Query(None, description="按任务ID过滤")
):
    """获取tcpdump进程列表"""
    try:
        from services.tcpdump_process_manager import tcpdump_manager
        
        process_info = tcpdump_manager.get_process_info(task_id)
        
        processes = [
            TcpdumpProcessInfo(
                pid=info['pid'],
                task_id=info['task_id'],
                command=info['command'],
                start_time=info['start_time'],
                running_time=info['running_time'],
                capture_file=info['capture_file'],
                database_type=info['database_type'],
                is_remote=info['is_remote'],
                server_host=info['server_host']
            )
            for info in process_info
        ]
        
        message = f"找到 {len(processes)} 个tcpdump进程"
        if task_id:
            message += f" (任务ID: {task_id})"
        
        return TcpdumpProcessListResponse(
            processes=processes,
            total_count=len(processes),
            message=message
        )
        
    except Exception as e:
        logger.error(f"获取tcpdump进程列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取进程列表失败: {str(e)}")

@router.delete("/processes/task/{task_id}")
async def cleanup_task_processes(task_id: str):
    """清理指定任务的tcpdump进程"""
    try:
        from services.tcpdump_process_manager import tcpdump_manager
        
        # 获取清理前的进程数量
        before_processes = tcpdump_manager.get_process_info(task_id)
        before_count = len(before_processes)
        
        if before_count == 0:
            return CleanupResponse(
                success=True,
                message=f"任务 {task_id} 没有需要清理的tcpdump进程",
                cleaned_count=0
            )
        
        # 执行清理
        await tcpdump_manager.cleanup_task_processes(task_id, "手动清理")
        
        return CleanupResponse(
            success=True,
            message=f"成功清理任务 {task_id} 的 {before_count} 个tcpdump进程",
            cleaned_count=before_count
        )
        
    except Exception as e:
        logger.error(f"清理任务进程失败: {e}")
        raise HTTPException(status_code=500, detail=f"清理失败: {str(e)}")

@router.delete("/processes/all")
async def cleanup_all_processes():
    """清理所有tcpdump进程"""
    try:
        from services.tcpdump_process_manager import tcpdump_manager
        
        # 获取清理前的进程数量
        before_processes = tcpdump_manager.get_process_info()
        before_count = len(before_processes)
        
        if before_count == 0:
            return CleanupResponse(
                success=True,
                message="没有需要清理的tcpdump进程",
                cleaned_count=0
            )
        
        # 执行清理
        await tcpdump_manager.cleanup_all_processes("手动清理所有进程")
        
        return CleanupResponse(
            success=True,
            message=f"成功清理所有 {before_count} 个tcpdump进程",
            cleaned_count=before_count
        )
        
    except Exception as e:
        logger.error(f"清理所有进程失败: {e}")
        raise HTTPException(status_code=500, detail=f"清理失败: {str(e)}")

@router.delete("/processes/force-cleanup")
async def force_cleanup_all_tcpdump():
    """强制清理系统中所有tcpdump进程（包括未注册的）"""
    try:
        from services.tcpdump_process_manager import tcpdump_manager
        
        # 执行强制清理
        await tcpdump_manager.force_cleanup_all_tcpdump()
        
        return CleanupResponse(
            success=True,
            message="强制清理系统中所有tcpdump进程完成",
            cleaned_count=-1  # -1表示未知数量
        )
        
    except Exception as e:
        logger.error(f"强制清理失败: {e}")
        raise HTTPException(status_code=500, detail=f"强制清理失败: {str(e)}")

@router.get("/status")
async def get_tcpdump_status():
    """获取tcpdump管理器状态"""
    try:
        from services.tcpdump_process_manager import tcpdump_manager
        
        all_processes = tcpdump_manager.get_process_info()
        
        # 按数据库类型统计
        db_stats = {}
        remote_count = 0
        local_count = 0
        
        for process in all_processes:
            db_type = process['database_type'] or 'unknown'
            if db_type not in db_stats:
                db_stats[db_type] = 0
            db_stats[db_type] += 1
            
            if process['is_remote']:
                remote_count += 1
            else:
                local_count += 1
        
        return {
            "total_processes": len(all_processes),
            "local_processes": local_count,
            "remote_processes": remote_count,
            "database_stats": db_stats,
            "manager_running": tcpdump_manager._running,
            "message": f"tcpdump管理器正在管理 {len(all_processes)} 个进程"
        }
        
    except Exception as e:
        logger.error(f"获取tcpdump状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取状态失败: {str(e)}")

@router.post("/processes/{pid}/kill")
async def kill_specific_process(pid: int):
    """杀死指定PID的tcpdump进程"""
    try:
        from services.tcpdump_process_manager import tcpdump_manager
        
        # 检查进程是否在管理器中
        all_processes = tcpdump_manager.get_process_info()
        target_process = None
        
        for process in all_processes:
            if process['pid'] == pid:
                target_process = process
                break
        
        if not target_process:
            # 尝试强制杀死未注册的进程
            await tcpdump_manager._kill_process_by_pid(pid, "手动杀死指定进程")
            return CleanupResponse(
                success=True,
                message=f"已尝试杀死进程 {pid}（未在管理器中注册）",
                cleaned_count=1
            )
        else:
            # 杀死已注册的进程
            await tcpdump_manager._kill_process(pid, "手动杀死指定进程")
            tcpdump_manager.unregister_process(pid)
            
            return CleanupResponse(
                success=True,
                message=f"成功杀死进程 {pid} (任务: {target_process['task_id']})",
                cleaned_count=1
            )
        
    except Exception as e:
        logger.error(f"杀死进程 {pid} 失败: {e}")
        raise HTTPException(status_code=500, detail=f"杀死进程失败: {str(e)}")

@router.get("/health")
async def tcpdump_health_check():
    """tcpdump管理器健康检查"""
    try:
        from services.tcpdump_process_manager import tcpdump_manager
        
        return {
            "status": "healthy" if tcpdump_manager._running else "stopped",
            "manager_running": tcpdump_manager._running,
            "total_processes": len(tcpdump_manager.get_process_info()),
            "timestamp": time.time()
        }
        
    except Exception as e:
        logger.error(f"tcpdump健康检查失败: {e}")
        return {
            "status": "error",
            "error": str(e),
            "timestamp": time.time()
        }

"""
Oracle数据库API - 提供Oracle数据库的查询和管理接口
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
import logging
import asyncio

from services.oracle_service import OracleService
from services.oracle_packet_capture_service import OraclePacketCaptureService
from services.oracle_container_service import OracleContainerService
from services.database_config_service import DatabaseConfigService
from services.ai_analysis_service import AIAnalysisService

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/oracle", tags=["Oracle"])

# 服务实例
oracle_container_service = OracleContainerService()
db_config_service = DatabaseConfigService()
ai_analysis_service = AIAnalysisService()

# Oracle抓包服务 - 延迟初始化以确保权限检查
def get_oracle_capture_service():
    """获取Oracle抓包服务实例"""
    return OraclePacketCaptureService()

# Oracle服务缓存
_oracle_service_cache = {}

class OracleQueryRequest(BaseModel):
    """Oracle查询请求"""
    query: str
    capture_packets: bool = True
    config_id: Optional[int] = None
    schema_name: Optional[str] = Field(None, alias="schema")

class OracleQueryResponse(BaseModel):
    """Oracle查询响应"""
    success: bool
    data: List[Dict[str, Any]] = []
    columns: List[str] = []
    row_count: int = 0
    execution_time: float = 0.0
    capture_file: Optional[str] = None
    error: Optional[str] = None

class OracleConnectionRequest(BaseModel):
    """Oracle连接请求"""
    host: str
    port: int = 1521
    user: str
    password: str
    service_name: str = "helowin"

class OracleConnectionResponse(BaseModel):
    """Oracle连接响应"""
    success: bool
    message: str
    version: Optional[str] = None
    schemas: Optional[List[str]] = None
    error: Optional[str] = None

async def get_oracle_service(config_id: int = None) -> OracleService:
    """获取Oracle服务实例（带缓存）"""
    try:
        # 生成缓存键
        cache_key = f"oracle_service_{config_id or 'default'}"

        # 检查缓存
        if cache_key in _oracle_service_cache:
            return _oracle_service_cache[cache_key]

        if config_id:
            config = await db_config_service.get_config(config_id)
            if not config or config.database_type != 'oracle':
                raise HTTPException(status_code=404, detail="Oracle configuration not found")
        else:
            # 获取默认Oracle配置
            configs = await db_config_service.list_configs(active_only=True)
            oracle_configs = [c for c in configs if c.database_type == 'oracle']
            config = next((c for c in oracle_configs if c.is_default), None)
            if not config:
                # 使用默认配置
                from types import SimpleNamespace
                config = SimpleNamespace(
                    host='*************',
                    port=1521,
                    user='system',
                    password='oracle',
                    database_name='helowin'
                )

        # 创建新的Oracle服务实例
        oracle_service = OracleService(
            host=config.host,
            port=config.port,
            user=config.user,
            password=config.password,
            service_name=config.database_name
        )

        # 缓存服务实例
        _oracle_service_cache[cache_key] = oracle_service
        logger.debug(f"Cached Oracle service for config_id: {config_id}")

        return oracle_service
    except Exception as e:
        logger.error(f"Failed to get Oracle service: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/query", response_model=OracleQueryResponse)
async def execute_oracle_query(request: OracleQueryRequest, background_tasks: BackgroundTasks):
    """执行Oracle查询"""
    import time
    start_time = time.time()
    capture_file = None
    
    try:
        # 获取Oracle服务
        oracle_service = await get_oracle_service(request.config_id)
        
        # 根据是否需要抓包选择不同的执行方式
        if request.capture_packets:
            # 使用新的抓包流程：开始抓包 → 连接数据库 → 执行语句 → 断开数据库 → 停止抓包
            logger.info("使用新的Oracle抓包流程")
            try:
                oracle_capture_service = get_oracle_capture_service()
                capture_file = await oracle_capture_service.start_capture(
                    target_host=oracle_service.host,
                    target_port=oracle_service.port,
                    server_config_id=request.config_id
                )
                logger.info(f"Oracle packet capture started: {capture_file}")

                # 使用专门的抓包方法执行查询（包含连接和断开）
                result = await oracle_service.execute_query_for_capture(request.query)

                # 停止抓包
                await asyncio.sleep(0.5)  # 等待数据包捕获完成
                final_capture_file = await oracle_capture_service.stop_capture()
                if final_capture_file:
                    capture_file = final_capture_file
                logger.info(f"Oracle packet capture completed: {capture_file}")

            except Exception as e:
                logger.error(f"Oracle抓包执行失败: {str(e)}")
                # 确保停止抓包
                try:
                    await oracle_capture_service.stop_capture()
                except Exception as e:
                    logger.warning(f"停止Oracle抓包失败: {str(e)}")
                raise
        else:
            # 不抓包，使用普通连接池方式
            logger.info("使用普通Oracle连接池执行查询")
            result = await oracle_service.execute_query(request.query)

        execution_time = time.time() - start_time

        # 处理不同的返回格式
        if request.capture_packets:
            # 抓包模式返回的是直接的查询结果
            if result.get('type') == 'query':
                return OracleQueryResponse(
                    success=True,
                    data=result.get('data', []),
                    columns=result.get('columns', []),
                    row_count=result.get('count', 0),
                    execution_time=execution_time,
                    capture_file=capture_file
                )
            elif result.get('type') == 'modification':
                return OracleQueryResponse(
                    success=True,
                    data=[],
                    columns=[],
                    row_count=result.get('affected_rows', 0),
                    execution_time=execution_time,
                    capture_file=capture_file
                )
            else:
                return OracleQueryResponse(
                    success=False,
                    error="Unknown result format from capture query",
                    execution_time=execution_time,
                    capture_file=capture_file
                )
        else:
            # 普通模式返回的是包含success字段的结果
            if result.get('success', False):
                return OracleQueryResponse(
                    success=True,
                    data=result.get('data', []),
                    columns=result.get('columns', []),
                    row_count=result.get('row_count', 0),
                    execution_time=execution_time,
                    capture_file=capture_file
                )
            else:
                return OracleQueryResponse(
                    success=False,
                    error=result.get('error', 'Unknown error'),
                    execution_time=execution_time,
                    capture_file=capture_file
                )
            
    except Exception as e:
        execution_time = time.time() - start_time
        logger.error(f"Oracle query execution failed: {str(e)}")
        
        # 抓包已在上面的流程中处理完成，不需要额外停止
        
        return OracleQueryResponse(
            success=False,
            error=str(e),
            execution_time=execution_time,
            capture_file=capture_file
        )

@router.post("/test-connection", response_model=OracleConnectionResponse)
async def test_oracle_connection(request: OracleConnectionRequest):
    """测试Oracle数据库连接"""
    try:
        oracle_service = OracleService(
            host=request.host,
            port=request.port,
            user=request.user,
            password=request.password,
            service_name=request.service_name
        )
        
        # 测试连接
        result = await oracle_service.test_connection()
        
        if result['success']:
            # 获取schemas
            await oracle_service.connect()
            schemas = await oracle_service.get_schemas()
            await oracle_service.disconnect()
            
            return OracleConnectionResponse(
                success=True,
                message=result['message'],
                version=result.get('version'),
                schemas=schemas
            )
        else:
            return OracleConnectionResponse(
                success=False,
                message=result['message'],
                error=result.get('error')
            )
            
    except Exception as e:
        logger.error(f"Oracle connection test failed: {str(e)}")
        return OracleConnectionResponse(
            success=False,
            message="Connection test failed",
            error=str(e)
        )

@router.get("/schemas")
async def get_oracle_schemas(config_id: Optional[int] = None, refresh: bool = False):
    """获取Oracle schemas列表"""
    try:
        oracle_service = await get_oracle_service(config_id)

        # 如果需要刷新，清除缓存
        if refresh:
            oracle_service.clear_cache()

        # 检查是否有缓存，如果有缓存就不需要连接数据库
        if not refresh and oracle_service._schemas_cache is not None:
            import time
            current_time = time.time()
            if (oracle_service._cache_timestamp is not None and
                current_time - oracle_service._cache_timestamp < oracle_service._cache_ttl):
                logger.debug("Returning cached Oracle schemas without database connection")
                return {
                    "success": True,
                    "schemas": oracle_service._schemas_cache,
                    "count": len(oracle_service._schemas_cache),
                    "cached": True
                }

        # 需要查询数据库
        await oracle_service.connect()

        try:
            schemas = await oracle_service.get_schemas()

            return {
                "success": True,
                "schemas": schemas,
                "count": len(schemas),
                "cached": False
            }
        finally:
            # 确保连接被关闭
            await oracle_service.disconnect()

    except Exception as e:
        logger.error(f"Failed to get Oracle schemas: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/tables/{schema}")
async def get_oracle_tables(schema: str, config_id: Optional[int] = None):
    """获取指定schema的表列表"""
    try:
        oracle_service = await get_oracle_service(config_id)
        tables = await oracle_service.get_tables(schema)
        
        return {
            "success": True,
            "schema": schema,
            "tables": tables,
            "count": len(tables)
        }
        
    except Exception as e:
        logger.error(f"Failed to get Oracle tables: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/table-structure/{schema}/{table}")
async def get_oracle_table_structure(schema: str, table: str, config_id: Optional[int] = None):
    """获取表结构信息"""
    try:
        oracle_service = await get_oracle_service(config_id)
        structure = await oracle_service.get_table_structure(table, schema)
        
        return {
            "success": True,
            "schema": schema,
            "table": table,
            "columns": structure,
            "count": len(structure)
        }
        
    except Exception as e:
        logger.error(f"Failed to get Oracle table structure: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/capture/start")
async def start_oracle_capture(
    host: str = "*************",
    port: int = 1521,
    server_config_id: Optional[int] = None
):
    """启动Oracle数据包捕获"""
    try:
        oracle_capture_service = get_oracle_capture_service()
        capture_file = await oracle_capture_service.start_capture(
            target_host=host,
            target_port=port,
            server_config_id=server_config_id
        )
        
        return {
            "success": True,
            "message": "Oracle packet capture started",
            "capture_file": capture_file
        }
        
    except Exception as e:
        logger.error(f"Failed to start Oracle capture: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/capture/stop")
async def stop_oracle_capture():
    """停止Oracle数据包捕获"""
    try:
        oracle_capture_service = get_oracle_capture_service()
        capture_file = await oracle_capture_service.stop_capture()
        
        return {
            "success": True,
            "message": "Oracle packet capture stopped",
            "capture_file": capture_file
        }
        
    except Exception as e:
        logger.error(f"Failed to stop Oracle capture: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/capture/status")
async def get_oracle_capture_status():
    """获取Oracle抓包状态"""
    try:
        oracle_capture_service = get_oracle_capture_service()
        status = oracle_capture_service.get_capture_status()
        return {
            "success": True,
            "status": status
        }
        
    except Exception as e:
        logger.error(f"Failed to get Oracle capture status: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

async def stop_capture_after_delay():
    """延迟停止抓包"""
    try:
        await asyncio.sleep(3)  # 等待3秒确保查询完成
        oracle_capture_service = get_oracle_capture_service()
        await oracle_capture_service.stop_capture()
        logger.info("Oracle packet capture stopped automatically")
    except Exception as e:
        logger.error(f"Failed to auto-stop Oracle capture: {str(e)}")

# Oracle容器管理端点
@router.get("/container/status")
async def get_oracle_container_status(config_id: Optional[int] = None):
    """获取Oracle容器状态"""
    try:
        if config_id:
            # 根据配置ID获取特定配置的容器状态
            config = await db_config_service.get_config(config_id)
            if not config or config.database_type != 'oracle':
                raise HTTPException(status_code=404, detail="Oracle configuration not found")

            # 创建针对特定配置的容器服务实例
            container_service = OracleContainerService()
            # 使用数据库配置的host作为服务器地址，假设Oracle容器运行在数据库服务器上
            container_service.server_host = config.host
            container_service.oracle_port = config.port

            # 根据服务器地址设置SSH凭据
            if config.host == "*************":
                container_service.server_username = "root"
                container_service.server_password = "QZ@1005#1005"
            elif config.host == "**************":
                # 这个服务器可能使用不同的凭据，先尝试常见的凭据
                container_service.server_username = "root"
                container_service.server_password = "root"  # 尝试简单密码
            elif config.host == "**************":
                container_service.server_username = "root"
                container_service.server_password = "QZ@1005#1005"
            else:
                # 默认凭据
                container_service.server_username = "root"
                container_service.server_password = "QZ@1005#1005"
            status = await container_service.check_container_status()
        else:
            # 使用默认容器服务
            status = await oracle_container_service.check_container_status()

        return {
            "success": True,
            "container_status": status
        }
    except Exception as e:
        logger.error(f"Failed to get Oracle container status: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/container/start")
async def start_oracle_container(config_id: Optional[int] = None):
    """启动Oracle容器"""
    try:
        if config_id:
            # 根据配置ID启动特定配置的容器
            config = await db_config_service.get_config(config_id)
            if not config or config.database_type != 'oracle':
                raise HTTPException(status_code=404, detail="Oracle configuration not found")

            # 创建针对特定配置的容器服务实例
            container_service = OracleContainerService()
            container_service.server_host = config.host
            container_service.oracle_port = config.port

            # 根据服务器地址设置SSH凭据
            if config.host == "*************":
                container_service.server_username = "root"
                container_service.server_password = "QZ@1005#1005"
            elif config.host == "**************":
                # 这个服务器可能使用不同的凭据，先尝试常见的凭据
                container_service.server_username = "root"
                container_service.server_password = "root"  # 尝试简单密码
            elif config.host == "**************":
                container_service.server_username = "root"
                container_service.server_password = "QZ@1005#1005"
            else:
                # 默认凭据
                container_service.server_username = "root"
                container_service.server_password = "QZ@1005#1005"

            # 设置Oracle数据库凭据
            container_service.set_oracle_credentials(
                user=config.user,
                password=config.password,
                service_name=config.database_name
            )

            result = await container_service.start_oracle_container()
        else:
            # 使用默认容器服务
            result = await oracle_container_service.start_oracle_container()

        return result
    except Exception as e:
        logger.error(f"Failed to start Oracle container: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/container/stop")
async def stop_oracle_container(config_id: Optional[int] = None):
    """停止Oracle容器"""
    try:
        if config_id:
            # 根据配置ID停止特定配置的容器
            config = await db_config_service.get_config(config_id)
            if not config or config.database_type != 'oracle':
                raise HTTPException(status_code=404, detail="Oracle configuration not found")

            # 创建针对特定配置的容器服务实例
            container_service = OracleContainerService()
            container_service.server_host = config.host
            container_service.oracle_port = config.port

            # 根据服务器地址设置SSH凭据
            if config.host == "*************":
                container_service.server_username = "root"
                container_service.server_password = "QZ@1005#1005"
            elif config.host == "**************":
                # 这个服务器可能使用不同的凭据，先尝试常见的凭据
                container_service.server_username = "root"
                container_service.server_password = "root"  # 尝试简单密码
            elif config.host == "**************":
                container_service.server_username = "root"
                container_service.server_password = "QZ@1005#1005"
            else:
                # 默认凭据
                container_service.server_username = "root"
                container_service.server_password = "QZ@1005#1005"
            result = await container_service.stop_oracle_container()
        else:
            # 使用默认容器服务
            result = await oracle_container_service.stop_oracle_container()

        return result
    except Exception as e:
        logger.error(f"Failed to stop Oracle container: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/container/connection-info")
async def get_oracle_connection_info(config_id: Optional[int] = None):
    """获取Oracle连接信息"""
    try:
        if config_id:
            # 根据配置ID获取特定配置的连接信息
            config = await db_config_service.get_config(config_id)
            if not config or config.database_type != 'oracle':
                raise HTTPException(status_code=404, detail="Oracle configuration not found")

            # 直接返回配置中的连接信息，不需要检查容器状态
            return {
                'success': True,
                'connection_info': {
                    'host': config.host,
                    'port': config.port,
                    'service_name': config.database_name,
                    'username': config.user,
                    'password': config.password
                }
            }
        else:
            # 使用默认容器服务
            result = await oracle_container_service.get_oracle_connection_info()
            return result

    except Exception as e:
        logger.error(f"Failed to get Oracle connection info: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/wait-for-ready")
async def wait_for_oracle_ready(config_id: int, max_wait_seconds: int = 300):
    """等待Oracle数据库就绪"""
    try:
        config = await db_config_service.get_config(config_id)
        if not config or config.database_type != 'oracle':
            raise HTTPException(status_code=404, detail="Oracle configuration not found")

        result = await db_config_service.wait_for_oracle_ready(config, max_wait_seconds)
        return result

    except Exception as e:
        logger.error(f"Failed to wait for Oracle ready: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# AI分析相关API
class OracleAnalysisRequest(BaseModel):
    """Oracle AI分析请求"""
    capture_file: str

class OracleAnalysisResponse(BaseModel):
    """Oracle AI分析响应"""
    success: bool
    sql_statements: List[str] = []
    analysis_summary: Optional[str] = None
    packet_count: int = 0
    file_size: int = 0
    error: Optional[str] = None

@router.post("/analyze-packets", response_model=OracleAnalysisResponse)
async def analyze_oracle_packets(request: OracleAnalysisRequest):
    """AI分析Oracle抓包文件"""
    try:
        logger.info(f"Starting Oracle packet analysis for file: {request.capture_file}")

        # 使用AI分析服务分析抓包文件
        result = await ai_analysis_service.analyze_oracle_packets(request.capture_file)

        if result['success']:
            return OracleAnalysisResponse(
                success=True,
                sql_statements=result.get('sql_statements', []),
                analysis_summary=result.get('analysis_summary'),
                packet_count=result.get('packet_count', 0),
                file_size=result.get('file_size', 0)
            )
        else:
            return OracleAnalysisResponse(
                success=False,
                error=result.get('error', 'Analysis failed')
            )

    except Exception as e:
        logger.error(f"Oracle packet analysis failed: {str(e)}")
        return OracleAnalysisResponse(
            success=False,
            error=str(e)
        )

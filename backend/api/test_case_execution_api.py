"""
测试用例执行API - 提供测试用例自动化执行和抓包功能
"""
import logging
import traceback
from datetime import datetime
from typing import Dict, Any, Optional, List
from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel, Field

from services.test_case_execution_agent_service import test_case_execution_agent_service
from services.test_case_management_service import test_case_management_service
from services.batch_execution_service import batch_execution_service
from models.test_case_management import (
    ExecutionResultEnum,
    BatchExecutionRequest,
    BatchExecutionResponse,
    BatchExecutionListResponse,
    BatchExecutionQuery,
    BatchExecutionStatistics,
    BatchExecutionStatusEnum,
    TestCaseExecutionItem
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/test-case-execution", tags=["测试用例执行"])

class TestCaseExecutionRequest(BaseModel):
    """测试用例执行请求"""
    test_case_json: str = Field(..., description="测试用例的JSON字符串")
    config_id: int = Field(..., description="数据库配置ID")
    capture_enabled: bool = Field(default=True, description="是否启用抓包")
    async_execution: bool = Field(default=False, description="是否异步执行")
    test_case_id: Optional[str] = Field(None, description="测试用例ID，用于更新最近执行记录")
    use_c_executor: bool = Field(default=False, description="是否使用C语言执行器（仅适用于GaussDB）")

class TestCaseAnalysisRequest(BaseModel):
    """测试用例分析请求"""
    test_case_json: str = Field(..., description="测试用例的JSON字符串")

class ExecutionStatusResponse(BaseModel):
    """执行状态响应"""
    execution_id: str
    status: str
    progress: float
    current_step: Optional[int] = None
    message: Optional[str] = None

# 存储异步执行状态
execution_status_store = {}

@router.post("/analyze", response_model=Dict[str, Any])
async def analyze_test_case(request: TestCaseAnalysisRequest):
    """分析测试用例，提取SQL语句和执行计划"""
    try:
        logger.info("开始分析测试用例")
        
        # 使用Agent服务分析测试用例
        result = await test_case_execution_agent_service._analyze_test_case(
            request.test_case_json
        )
        
        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["error"])
        
        return {
            "success": True,
            "analysis": result,
            "message": "测试用例分析完成"
        }
        
    except Exception as e:
        logger.error(f"测试用例分析失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/execute", response_model=Dict[str, Any])
async def execute_test_case(request: TestCaseExecutionRequest, background_tasks: BackgroundTasks):
    """执行测试用例"""
    try:
        logger.info(f"开始执行测试用例，配置ID: {request.config_id}")
        
        if request.async_execution:
            # 异步执行
            try:
                # 使用任务管理服务提交任务
                from services.arq_task_management_service import arq_task_management_service
                await arq_task_management_service.initialize()

                task_id = await arq_task_management_service.submit_single_test_case_execution_task(
                    test_case_json=request.test_case_json,
                    config_id=request.config_id,
                    capture_enabled=request.capture_enabled,
                    test_case_id=request.test_case_id,
                    test_case_title=_extract_test_case_title(request.test_case_json),
                    use_c_executor=request.use_c_executor
                )
                
                return {
                    "success": True,
                    "execution_id": task_id,
                    "message": "测试用例已提交异步执行",
                    "status_url": f"/api/test-case-execution/status/{task_id}"
                }
            except Exception as e:
                logger.error(f"提交异步任务失败: {e}")
                raise HTTPException(status_code=500, detail=f"提交异步任务失败: {str(e)}")
        else:
            # 同步执行
            result = await test_case_execution_agent_service.execute_test_case(
                request.test_case_json,
                request.config_id,
                request.capture_enabled,
                request.test_case_id,
                request.use_c_executor
            )
            
            if not result["success"]:
                raise HTTPException(status_code=400, detail=result["error"])

            # 更新最近执行记录
            await _update_last_execution_record(request.test_case_json, result, request.config_id)

            return {
                "success": True,
                "result": result,
                "message": "测试用例执行完成"
            }
        
    except Exception as e:
        logger.error(f"测试用例执行失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/status/{execution_id}", response_model=ExecutionStatusResponse)
async def get_execution_status(execution_id: str):
    """获取异步执行状态"""
    try:
        # 首先尝试从任务管理服务获取状态
        try:
            from services.arq_task_management_service import arq_task_management_service
            await arq_task_management_service.initialize()

            task_info = await arq_task_management_service.get_task_info(execution_id)
            if task_info:
                # 从任务信息中获取进度信息
                return {
                    "execution_id": execution_id,
                    "status": task_info.status,
                    "progress": task_info.progress,
                    "current_step": None,
                    "message": task_info.message
                }
        except Exception as e:
            logger.warning(f"从任务管理服务获取状态失败: {e}")
        
        # 如果任务管理服务中没有找到，尝试从旧的存储中获取
        if execution_id in execution_status_store:
            status_info = execution_status_store[execution_id]
            return {
                "execution_id": execution_id,
                "status": status_info["status"],
                "progress": status_info["progress"],
                "current_step": status_info.get("current_step"),
                "message": status_info.get("message", "")
            }
        
        raise HTTPException(status_code=404, detail="执行ID不存在")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取执行状态失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/result/{execution_id}", response_model=Dict[str, Any])
async def get_execution_result(execution_id: str):
    """获取异步执行结果"""
    try:
        # 首先尝试从任务管理服务获取结果
        try:
            from services.arq_task_management_service import arq_task_management_service
            await arq_task_management_service.initialize()

            task_info = await arq_task_management_service.get_task_info(execution_id)
            if task_info:
                if task_info.status == "running":
                    return {
                        "success": False,
                        "message": "执行尚未完成",
                        "status": task_info.status,
                        "progress": task_info.progress
                    }
                
                if task_info.status == "completed" and task_info.result:
                    return {
                        "success": True,
                        "result": task_info.result,
                        "message": "执行结果获取成功"
                    }
                elif task_info.status == "failed":
                    return {
                        "success": False,
                        "message": f"执行失败: {task_info.error}",
                        "status": task_info.status
                    }
                else:
                    return {
                        "success": False,
                        "message": "执行结果不可用"
                    }
        except Exception as e:
            logger.warning(f"从任务管理服务获取结果失败: {e}")
        
        # 如果任务管理服务中没有找到，尝试从旧的存储中获取
        if execution_id in execution_status_store:
            status_info = execution_status_store[execution_id]
            
            if status_info["status"] == "running":
                return {
                    "success": False,
                    "message": "执行尚未完成",
                    "status": status_info["status"],
                    "progress": status_info["progress"]
                }
            
            result = status_info.get("result")
            if not result:
                return {
                    "success": False,
                    "message": "执行结果不可用"
                }
            
            return {
                "success": True,
                "result": result,
                "message": "执行结果获取成功"
            }
        
        raise HTTPException(status_code=404, detail="执行ID不存在")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取执行结果失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/parse-steps", response_model=Dict[str, Any])
async def parse_test_steps(request: TestCaseAnalysisRequest):
    """解析测试用例步骤，提取SQL语句"""
    try:
        logger.info("开始解析测试用例步骤")
        
        # 直接使用解析工具
        from tools.test_case_execution_tools import TestCaseParserTool
        parser = TestCaseParserTool()
        
        result = parser._run(request.test_case_json)
        
        # 解析结果
        import json
        parsed_result = json.loads(result)
        
        return {
            "success": True,
            "parsed_steps": parsed_result,
            "message": "测试步骤解析完成"
        }
        
    except Exception as e:
        logger.error(f"测试步骤解析失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/classify-sql", response_model=Dict[str, Any])
async def classify_sql_statements(sql_statements: List[str]):
    """分类SQL语句，识别抓包需求"""
    try:
        logger.info(f"开始分类SQL语句，共{len(sql_statements)}条")
        
        # 使用SQL分类工具
        from tools.test_case_execution_tools import SQLClassifierTool
        classifier = SQLClassifierTool()
        
        result = classifier._run(sql_statements)
        
        # 解析结果
        import json
        classification_result = json.loads(result)
        
        return {
            "success": True,
            "classification": classification_result,
            "message": "SQL分类完成"
        }
        
    except Exception as e:
        logger.error(f"SQL分类失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/cleanup/{execution_id}")
async def cleanup_execution(execution_id: str):
    """清理执行状态"""
    try:
        if execution_id in execution_status_store:
            del execution_status_store[execution_id]
            return {"success": True, "message": "执行状态已清理"}
        else:
            raise HTTPException(status_code=404, detail="执行ID不存在")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"清理执行状态失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

async def _execute_test_case_async(
    execution_id: str,
    test_case_json: str,
    config_id: int,
    capture_enabled: bool,
    test_case_id: Optional[str] = None
):
    """异步执行测试用例"""
    try:
        # 更新状态
        execution_status_store[execution_id].update({
            "status": "running",
            "progress": 0.1,
            "message": "正在分析测试用例..."
        })
        
        # 执行测试用例
        result = await test_case_execution_agent_service.execute_test_case(
            test_case_json,
            config_id,
            capture_enabled,
            test_case_id
        )
        
        # 更新最终状态
        execution_status_store[execution_id].update({
            "status": "completed" if result["success"] else "failed",
            "progress": 1.0,
            "message": "测试用例执行完成" if result["success"] else f"执行失败: {result.get('error', '')}",
            "result": result
        })

        # 更新最近执行记录
        if result["success"]:
            await _update_last_execution_record(test_case_json, result, config_id)
        
    except Exception as e:
        logger.error(f"异步执行测试用例失败: {str(e)}")
        execution_status_store[execution_id].update({
            "status": "failed",
            "progress": 1.0,
            "message": f"执行失败: {str(e)}",
            "result": {"success": False, "error": str(e)}
        })


async def _update_last_execution_record(test_case_json: str, result: Dict[str, Any], config_id: int):
    """更新最近执行记录"""
    try:
        import json
        import uuid
        from datetime import datetime

        # 解析测试用例JSON获取ID
        test_case_data = json.loads(test_case_json)
        test_case_id = test_case_data.get("id")

        if not test_case_id:
            logger.warning("测试用例JSON中没有找到ID，跳过更新最近执行记录")
            return

        # 生成执行记录ID
        execution_id = str(uuid.uuid4())

        # 确定执行结果
        execution_result = ExecutionResultEnum.PASS if result.get("success", False) else ExecutionResultEnum.FAIL

        # 收集抓包文件
        capture_files = []
        if result.get("summary", {}).get("capture_files"):
            capture_files = result["summary"]["capture_files"]

        # 计算执行时长（从结果中获取或默认为0）
        duration = 0
        if result.get("execution_results"):
            # 这里可以根据实际的执行结果计算时长
            duration = len(result["execution_results"]) * 2  # 简单估算

        # 更新最近执行记录
        await test_case_management_service.update_last_execution(
            test_case_id=test_case_id,
            execution_id=execution_id,
            execution_result=execution_result,
            config_id=config_id,
            capture_files=capture_files,
            duration=duration,
            execution_details=result  # 传递完整的执行结果
        )

        logger.info(f"已更新测试用例 {test_case_id} 的最近执行记录")

    except Exception as e:
        logger.error(f"更新最近执行记录失败: {str(e)}")

# 批量执行状态存储
batch_execution_status_store = {}


@router.post("/batch-execute", response_model=Dict[str, Any])
async def batch_execute_test_cases(request: BatchExecutionRequest, background_tasks: BackgroundTasks):
    """批量执行测试用例"""
    try:
        logger.info(f"开始批量执行测试用例，数据库类型: {request.database_type}, 版本: {request.database_version}")

        # 验证所有测试用例都是同一数据库类型和版本
        for item in request.test_case_items:
            # 这里可以添加额外的验证逻辑
            pass

        # 通过任务管理服务提交批量执行任务
        from services.arq_task_management_service import arq_task_management_service as task_management_service

        # 确保Redis连接健康
        from config.redis_config import redis_manager
        try:
            await redis_manager.ensure_connection()
            logger.info("Redis连接检查通过")
        except Exception as e:
            logger.error(f"Redis连接检查失败: {e}")
            raise HTTPException(status_code=503, detail=f"Redis服务不可用: {str(e)}")

        # 转换测试用例项为字典格式
        test_case_items_dict = [item.dict() for item in request.test_case_items]

        # 提交任务到任务管理服务
        max_retries = 3
        for attempt in range(max_retries):
            try:
                task_id = await task_management_service.submit_batch_test_case_execution_task(
                    batch_name=request.name,
                    database_type=request.database_type,
                    database_version=request.database_version,
                    config_id=request.config_id,
                    test_case_items=test_case_items_dict,
                    capture_enabled=request.capture_enabled,
                    timeout_per_case=request.timeout_per_case,
                    stop_on_failure=request.stop_on_failure,
                    use_c_executor=request.use_c_executor,
                    description=request.description
                )
                break  # 成功提交，跳出重试循环
            except Exception as e:
                if "Event loop is closed" in str(e) and attempt < max_retries - 1:
                    logger.warning(f"事件循环关闭错误，尝试重新初始化连接 (尝试 {attempt + 1}/{max_retries})")
                    await redis_manager.ensure_connection()
                    continue
                else:
                    raise

        # 准备批量执行数据
        batch_data = {
            "batch_id": task_id,
            "name": request.name,
            "database_type": request.database_type,
            "database_version": request.database_version,
            "config_id": request.config_id,
            "status": BatchExecutionStatusEnum.RUNNING,
            "total_cases": len(request.test_case_items),
            "completed_cases": 0,
            "success_cases": 0,
            "failed_cases": 0,
            "progress": 0.0,
            "start_time": None,
            "end_time": None,
            "duration": None,
            "test_case_items": test_case_items_dict,
            "capture_enabled": request.capture_enabled,
            "timeout_per_case": request.timeout_per_case,
            "stop_on_failure": request.stop_on_failure,
            "description": request.description,
            "error_message": None,
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        }

        # 保存到数据库
        try:
            batch_execution_service.create_batch_execution(batch_data)
            logger.info(f"批量执行记录已保存到数据库: {task_id}")
        except Exception as e:
            logger.warning(f"保存批量执行记录到数据库失败，继续使用内存存储: {e}")

        # 同时保持原有的内存存储机制以兼容现有的状态查询API
        batch_execution_status_store[task_id] = batch_data

        return {
            "success": True,
            "batch_id": task_id,
            "message": "批量测试用例已提交异步执行",
            "status_url": f"/api/test-case-execution/batch-status/{task_id}"
        }

    except Exception as e:
        logger.error(f"批量执行测试用例失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/batch-status/{batch_id}", response_model=BatchExecutionResponse)
async def get_batch_execution_status(batch_id: str):
    """获取批量执行状态"""
    try:
        # 首先尝试从任务管理服务获取最新状态
        from services.arq_task_management_service import arq_task_management_service as task_management_service

        try:
            logger.info(f"尝试从任务管理服务获取任务状态: {batch_id}")
            task_info = await task_management_service.get_task_info(batch_id)
            logger.info(f"任务信息: {task_info}")
            if task_info and task_info.task_type == "batch_test_case_execution":
                # 如果任务队列中有结果，使用任务队列的结果
                task_status = task_info.status.value if hasattr(task_info.status, 'value') else task_info.status
                if task_status == "completed" and task_info.result:
                    result_data = task_info.result

                    # 更新内存存储和数据库
                    if batch_id in batch_execution_status_store:
                        update_data = {
                            "status": BatchExecutionStatusEnum.COMPLETED,
                            "completed_cases": result_data.get("completed_cases", 0),
                            "success_cases": result_data.get("success_cases", 0),
                            "failed_cases": result_data.get("failed_cases", 0),
                            "progress": 1.0,
                            "start_time": task_info.created_at,
                            "end_time": task_info.updated_at,
                            "duration": int(result_data.get("duration", 0))
                        }
                        batch_execution_status_store[batch_id].update(update_data)

                        # 同时更新数据库
                        try:
                            batch_execution_service.update_batch_execution(batch_id, update_data)
                        except Exception as e:
                            logger.warning(f"更新数据库批量执行记录失败: {e}")

                        # 更新测试用例项的状态和抓包文件
                        execution_results = result_data.get("execution_results", [])
                        for i, exec_result in enumerate(execution_results):
                            if i < len(batch_execution_status_store[batch_id]["test_case_items"]):
                                batch_execution_status_store[batch_id]["test_case_items"][i].update({
                                    "status": exec_result.get("status", "completed"),
                                    "success": exec_result.get("success", True),
                                    "start_time": exec_result.get("start_time"),
                                    "end_time": exec_result.get("end_time"),
                                    "duration": exec_result.get("duration", 0),
                                    "execution_result": exec_result.get("execution_result"),
                                    "capture_files": exec_result.get("capture_files", [])
                                })
                elif task_status == "failed":
                    # 任务失败
                    if batch_id in batch_execution_status_store:
                        update_data = {
                            "status": BatchExecutionStatusEnum.FAILED,
                            "error_message": task_info.error or "任务执行失败"
                        }
                        batch_execution_status_store[batch_id].update(update_data)

                        # 同时更新数据库
                        try:
                            batch_execution_service.update_batch_execution(batch_id, update_data)
                        except Exception as e:
                            logger.warning(f"更新数据库批量执行记录失败: {e}")
        except Exception as e:
            logger.warning(f"从任务管理服务获取状态失败: {e}")

        # 从内存存储获取状态，如果不存在则尝试从任务管理服务重建
        if batch_id not in batch_execution_status_store:
            # 尝试从任务管理服务重建状态
            try:
                logger.info(f"尝试重建批量执行状态: {batch_id}")
                task_info = await task_management_service.get_task_info(batch_id)
                logger.info(f"重建时获取的任务信息: {task_info}")
                if task_info and task_info.task_type == "batch_test_case_execution":
                    # 重建内存存储状态
                    result_data = task_info.result or {}
                    execution_results = result_data.get("execution_results", [])

                    # 构建测试用例项
                    test_case_items = []
                    for exec_result in execution_results:
                        test_case_items.append({
                            "test_case_id": exec_result.get("test_case_id"),
                            "test_case_title": exec_result.get("test_case_title"),
                            "test_case_json": "{}",  # 简化处理
                            "status": exec_result.get("status", "completed"),
                            "start_time": exec_result.get("start_time"),
                            "end_time": exec_result.get("end_time"),
                            "duration": exec_result.get("duration", 0),
                            "success": exec_result.get("success", True),
                            "error_message": exec_result.get("error_message"),
                            "capture_files": exec_result.get("capture_files", []),
                            "execution_result": exec_result.get("execution_result")
                        })

                    # 重建状态
                    task_status = task_info.status.value if hasattr(task_info.status, 'value') else task_info.status
                    status = BatchExecutionStatusEnum.COMPLETED if task_status == "completed" else BatchExecutionStatusEnum.FAILED
                    batch_execution_status_store[batch_id] = {
                        "batch_id": batch_id,
                        "name": result_data.get("batch_name", "Unknown"),
                        "database_type": result_data.get("database_type", "unknown"),
                        "database_version": result_data.get("database_version", ""),
                        "config_id": result_data.get("config_id", 0),
                        "status": status,
                        "total_cases": result_data.get("total_cases", len(test_case_items)),
                        "completed_cases": result_data.get("completed_cases", len(test_case_items)),
                        "success_cases": result_data.get("success_cases", 0),
                        "failed_cases": result_data.get("failed_cases", 0),
                        "progress": 1.0 if status == BatchExecutionStatusEnum.COMPLETED else 0.0,
                        "start_time": task_info.created_at,
                        "end_time": task_info.updated_at,
                        "duration": int(result_data.get("duration", 0)),
                        "test_case_items": test_case_items,
                        "capture_enabled": result_data.get("capture_enabled", True),
                        "timeout_per_case": result_data.get("timeout_per_case", 600),
                        "stop_on_failure": result_data.get("stop_on_failure", False),
                        "description": result_data.get("description", ""),
                        "error_message": task_info.error,
                        "created_at": task_info.created_at,
                        "updated_at": task_info.updated_at
                    }
                else:
                    raise HTTPException(status_code=404, detail="批量执行任务不存在")
            except Exception as e:
                logger.error(f"重建批量执行状态失败: {e}")
                raise HTTPException(status_code=404, detail="批量执行任务不存在")

        status_data = batch_execution_status_store[batch_id]

        # 转换为响应模型
        return BatchExecutionResponse(
            batch_id=status_data["batch_id"],
            name=status_data["name"],
            database_type=status_data["database_type"],
            database_version=status_data["database_version"],
            config_id=status_data["config_id"],
            status=status_data["status"],
            total_cases=status_data["total_cases"],
            completed_cases=status_data["completed_cases"],
            success_cases=status_data["success_cases"],
            failed_cases=status_data["failed_cases"],
            progress=status_data["progress"],
            start_time=status_data["start_time"],
            end_time=status_data["end_time"],
            duration=status_data["duration"],
            test_case_items=[TestCaseExecutionItem(**item) for item in status_data["test_case_items"]],
            capture_enabled=status_data["capture_enabled"],
            timeout_per_case=status_data["timeout_per_case"],
            stop_on_failure=status_data["stop_on_failure"],
            description=status_data["description"],
            error_message=status_data["error_message"],
            created_at=status_data["created_at"] or "",
            updated_at=status_data["updated_at"] or ""
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取批量执行状态失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/batch-list", response_model=BatchExecutionListResponse)
async def list_batch_executions(
    page: int = 1,
    page_size: int = 20,
    database_type: Optional[str] = None,
    status: Optional[BatchExecutionStatusEnum] = None
):
    """获取批量执行列表"""
    try:
        # 从内存存储中获取数据（实际应用中应该从数据库获取）
        all_batches = list(batch_execution_status_store.values())

        # 过滤
        if database_type:
            all_batches = [b for b in all_batches if b["database_type"] == database_type]
        if status:
            all_batches = [b for b in all_batches if b["status"] == status]

        # 排序（按创建时间倒序）
        all_batches.sort(key=lambda x: x.get("created_at", ""), reverse=True)

        # 分页
        total = len(all_batches)
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        batches = all_batches[start_idx:end_idx]

        # 转换为响应模型
        batch_responses = []
        for batch_data in batches:
            batch_responses.append(BatchExecutionResponse(
                batch_id=batch_data["batch_id"],
                name=batch_data["name"],
                database_type=batch_data["database_type"],
                database_version=batch_data["database_version"],
                config_id=batch_data["config_id"],
                status=batch_data["status"],
                total_cases=batch_data["total_cases"],
                completed_cases=batch_data["completed_cases"],
                success_cases=batch_data["success_cases"],
                failed_cases=batch_data["failed_cases"],
                progress=batch_data["progress"],
                start_time=batch_data["start_time"],
                end_time=batch_data["end_time"],
                duration=batch_data["duration"],
                test_case_items=[TestCaseExecutionItem(**item) for item in batch_data["test_case_items"]],
                capture_enabled=batch_data["capture_enabled"],
                timeout_per_case=batch_data["timeout_per_case"],
                stop_on_failure=batch_data["stop_on_failure"],
                description=batch_data["description"],
                error_message=batch_data["error_message"],
                created_at=batch_data["created_at"] or "",
                updated_at=batch_data["updated_at"] or ""
            ))

        total_pages = (total + page_size - 1) // page_size

        return BatchExecutionListResponse(
            batch_executions=batch_responses,
            total=total,
            page=page,
            page_size=page_size,
            total_pages=total_pages
        )

    except Exception as e:
        logger.error(f"获取批量执行列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/batch/{batch_id}")
async def cancel_batch_execution(batch_id: str):
    """取消批量执行"""
    try:
        if batch_id not in batch_execution_status_store:
            raise HTTPException(status_code=404, detail="批量执行任务不存在")

        batch_data = batch_execution_status_store[batch_id]

        if batch_data["status"] in [BatchExecutionStatusEnum.COMPLETED, BatchExecutionStatusEnum.FAILED, BatchExecutionStatusEnum.CANCELLED]:
            raise HTTPException(status_code=400, detail="批量执行任务已完成或已取消，无法取消")

        # 更新状态为已取消
        batch_data["status"] = BatchExecutionStatusEnum.CANCELLED
        batch_data["end_time"] = datetime.now().isoformat()
        batch_data["updated_at"] = datetime.now().isoformat()

        return {"success": True, "message": "批量执行任务已取消"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"取消批量执行失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


async def _execute_batch_test_cases_async(batch_id: str, request: BatchExecutionRequest):
    """异步执行批量测试用例"""
    import asyncio
    from datetime import datetime

    try:
        logger.info(f"开始异步执行批量测试用例: {batch_id}")

        # 更新开始时间
        batch_data = batch_execution_status_store[batch_id]
        batch_data["start_time"] = datetime.now().isoformat()
        batch_data["created_at"] = datetime.now().isoformat()
        batch_data["updated_at"] = datetime.now().isoformat()

        # 串行执行测试用例（一个接一个执行）
        await _execute_batch_sequential(batch_id, request)

        # 更新最终状态
        batch_data = batch_execution_status_store[batch_id]
        batch_data["end_time"] = datetime.now().isoformat()
        batch_data["updated_at"] = datetime.now().isoformat()

        # 计算总耗时
        if batch_data["start_time"] and batch_data["end_time"]:
            start_time = datetime.fromisoformat(batch_data["start_time"])
            end_time = datetime.fromisoformat(batch_data["end_time"])
            batch_data["duration"] = int((end_time - start_time).total_seconds())

        # 确定最终状态
        if batch_data["failed_cases"] == 0:
            batch_data["status"] = BatchExecutionStatusEnum.COMPLETED
        elif batch_data["success_cases"] > 0:
            batch_data["status"] = BatchExecutionStatusEnum.PARTIAL_SUCCESS
        else:
            batch_data["status"] = BatchExecutionStatusEnum.FAILED

        logger.info(f"批量执行完成: {batch_id}, 成功: {batch_data['success_cases']}, 失败: {batch_data['failed_cases']}")

    except Exception as e:
        logger.error(f"批量执行异步任务失败: {e}")

        # 更新错误状态
        if batch_id in batch_execution_status_store:
            batch_data = batch_execution_status_store[batch_id]
            batch_data["status"] = BatchExecutionStatusEnum.FAILED
            batch_data["error_message"] = str(e)
            batch_data["end_time"] = datetime.now().isoformat()
            batch_data["updated_at"] = datetime.now().isoformat()


async def _execute_batch_sequential(batch_id: str, request: BatchExecutionRequest):
    """串行执行批量测试用例"""
    batch_data = batch_execution_status_store[batch_id]

    for i, test_case_item in enumerate(request.test_case_items):
        if batch_data["status"] == BatchExecutionStatusEnum.CANCELLED:
            logger.info(f"批量执行已取消: {batch_id}")
            break

        # 执行单个测试用例
        success = await _execute_single_test_case(batch_id, i, test_case_item, request)

        # 更新进度
        batch_data["completed_cases"] += 1
        batch_data["progress"] = batch_data["completed_cases"] / batch_data["total_cases"]
        batch_data["updated_at"] = datetime.now().isoformat()

        if success:
            batch_data["success_cases"] += 1
        else:
            batch_data["failed_cases"] += 1

            # 如果设置了遇到失败停止，则停止执行
            if request.stop_on_failure:
                logger.info(f"遇到失败停止执行: {batch_id}")
                break



async def _execute_single_test_case(
    batch_id: str,
    index: int,
    test_case_item: TestCaseExecutionItem,
    request: BatchExecutionRequest
) -> bool:
    """执行单个测试用例"""
    from datetime import datetime

    try:
        logger.info(f"开始执行测试用例: {test_case_item.test_case_id}")

        # 更新测试用例状态
        batch_data = batch_execution_status_store[batch_id]
        batch_data["test_case_items"][index]["status"] = "running"
        batch_data["test_case_items"][index]["start_time"] = datetime.now().isoformat()

        # 执行测试用例
        result = await test_case_execution_agent_service.execute_test_case(
            test_case_item.test_case_json,
            request.config_id,
            request.capture_enabled,
            test_case_item.test_case_id
        )

        # 更新执行结果
        end_time = datetime.now().isoformat()
        batch_data["test_case_items"][index]["end_time"] = end_time
        batch_data["test_case_items"][index]["success"] = result.get("success", False)
        batch_data["test_case_items"][index]["execution_result"] = result

        # 检查执行结果，使用与单个执行相同的逻辑
        execution_success = result.get("success", False)
        success_rate = result.get("success_rate", 0)

        # 判断是否完全成功（所有步骤都成功）
        is_fully_successful = execution_success and success_rate == 100.0

        if execution_success:
            batch_data["test_case_items"][index]["status"] = "completed"
            # 收集抓包文件
            capture_files = []
            if result.get("capture_file"):
                capture_files.append(result["capture_file"])
            if result.get("all_capture_files"):
                capture_files.extend(result["all_capture_files"])
            batch_data["test_case_items"][index]["capture_files"] = capture_files

            # 根据执行结果更新测试用例状态
            if is_fully_successful:
                try:
                    from models.test_case_management import StatusEnum
                    from services.test_case_management_service import test_case_management_service
                    logger.info(f"批量执行完全成功（成功率100%），更新测试用例 {test_case_item.test_case_id} 状态为已完成")
                    await test_case_management_service.update_test_case_status(test_case_item.test_case_id, StatusEnum.COMPLETED)
                    logger.info(f"已更新测试用例 {test_case_item.test_case_id} 状态为已完成")
                except Exception as e:
                    logger.error(f"更新测试用例状态失败: {e}")
            elif success_rate == 0:
                # 完全失败时设置为失败状态
                try:
                    from models.test_case_management import StatusEnum
                    from services.test_case_management_service import test_case_management_service
                    logger.info(f"批量执行完全失败（成功率0%），更新测试用例 {test_case_item.test_case_id} 状态为失败")
                    await test_case_management_service.update_test_case_status(test_case_item.test_case_id, StatusEnum.FAILED)
                    logger.info(f"已更新测试用例 {test_case_item.test_case_id} 状态为失败")
                except Exception as e:
                    logger.error(f"更新测试用例状态失败: {e}")
            else:
                logger.info(f"测试用例 {test_case_item.test_case_id} 执行部分成功（成功率: {success_rate}%），保持原状态")
        else:
            batch_data["test_case_items"][index]["status"] = "failed"
            batch_data["test_case_items"][index]["error_message"] = result.get("error", "执行失败")

        # 计算耗时
        if batch_data["test_case_items"][index]["start_time"]:
            start_time = datetime.fromisoformat(batch_data["test_case_items"][index]["start_time"])
            end_time_obj = datetime.fromisoformat(end_time)
            batch_data["test_case_items"][index]["duration"] = int((end_time_obj - start_time).total_seconds())

        return result.get("success", False)

    except Exception as e:
        logger.error(f"执行测试用例失败: {test_case_item.test_case_id}, 错误: {e}")

        # 更新失败状态
        batch_data = batch_execution_status_store[batch_id]
        batch_data["test_case_items"][index]["status"] = "failed"
        batch_data["test_case_items"][index]["end_time"] = datetime.now().isoformat()
        batch_data["test_case_items"][index]["error_message"] = str(e)
        batch_data["test_case_items"][index]["success"] = False

        return False

def _extract_test_case_title(test_case_json: str) -> Optional[str]:
    """提取测试用例标题"""
    try:
        if isinstance(test_case_json, str):
            import json
            test_case_data = json.loads(test_case_json)
        else:
            test_case_data = test_case_json
        
        return test_case_data.get("title") if isinstance(test_case_data, dict) else None
    except Exception:
        return None


# ---------------- 新增：批量执行报告导出 ----------------
from fastapi import Query
from fastapi.responses import FileResponse
import os
import tempfile
import csv


@router.get("/batch-report/{batch_id}")
async def export_batch_execution_report(
    batch_id: str,
    format: str = Query("excel", description="导出格式: json, csv, excel")
):
    """导出指定批量执行的报告，包含每条SQL的pass/fail与对应pcap文件名"""
    try:
        # 优先从任务管理服务获取结果
        from services.arq_task_management_service import arq_task_management_service as task_management_service

        task_info = None
        try:
            task_info = await task_management_service.get_task_info(batch_id)
        except Exception as e:
            logger.warning(f"从任务管理服务获取任务信息失败: {e}")

        if task_info and task_info.result:
            result_data = task_info.result
            start_time_val = getattr(task_info, 'created_at', None)
            end_time_val = getattr(task_info, 'updated_at', None)
        elif batch_id in batch_execution_status_store:
            # 回退到内存状态
            result_data = {
                "batch_name": batch_execution_status_store[batch_id].get("name"),
                "database_type": batch_execution_status_store[batch_id].get("database_type"),
                "database_version": batch_execution_status_store[batch_id].get("database_version"),
                "config_id": batch_execution_status_store[batch_id].get("config_id"),
                "execution_results": batch_execution_status_store[batch_id].get("test_case_items", []),
            }
            start_time_val = batch_execution_status_store[batch_id].get("start_time")
            end_time_val = batch_execution_status_store[batch_id].get("end_time")
        else:
            raise HTTPException(status_code=404, detail="批量执行任务不存在或结果不可用")

        batch_name = result_data.get("batch_name", batch_id)
        database_type = result_data.get("database_type", "")
        database_version = result_data.get("database_version", "")
        start_time_str = str(start_time_val) if start_time_val else ""
        end_time_str = str(end_time_val) if end_time_val else ""

        rows = []

        execution_results = result_data.get("execution_results", [])
        for exec_item in execution_results:
            test_case_id = exec_item.get("test_case_id")
            test_case_title = exec_item.get("test_case_title")
            overall_success = exec_item.get("success", False)

            # 详细执行结果
            details = exec_item.get("execution_result") or {}
            step_results = details.get("execution_results", [])

            # 如果没有细分步骤，尽量从汇总中构造
            if not step_results and exec_item.get("capture_files"):
                capture_files = exec_item.get("capture_files") or []
                rows.append({
                    "批次ID": batch_id,
                    "批次名称": batch_name,
                    "数据库类型": database_type,
                    "数据库版本": database_version,
                    "开始时间": start_time_str,
                    "结束时间": end_time_str,
                    "用例标题": test_case_title,
                    "用例ID": test_case_id,
                    "SQL": "",
                    "SQL是否成功": "",
                    "用例结果": "pass" if overall_success else "fail",
                    "PCAP文件": ", ".join(capture_files)
                })
                continue

            for step in step_results:
                sql_results = step.get("sql_results", [])
                # 收集与该步骤关联的pcap
                step_pcap_files = []
                if step.get("capture_file"):
                    filename = step.get("capture_file")
                    try:
                        filename = os.path.basename(filename)
                    except Exception as e:

                        logger.error(f"操作失败: {type(e).__name__}: {str(e)}")

                        logger.error(f"异常堆栈跟踪: {traceback.format_exc()}")
                    step_pcap_files.append(filename)
                if step.get("all_capture_files"):
                    for f in step.get("all_capture_files", []):
                        try:
                            step_pcap_files.append(os.path.basename(f))
                        except Exception:
                            step_pcap_files.append(f)

                # 若步骤没有SQL结果，也生成一行汇总
                if not sql_results:
                    rows.append({
                        "批次ID": batch_id,
                        "批次名称": batch_name,
                        "数据库类型": database_type,
                        "数据库版本": database_version,
                        "开始时间": start_time_str,
                        "结束时间": end_time_str,
                        "用例标题": test_case_title,
                        "用例ID": test_case_id,
                        "SQL": "",
                        "SQL是否成功": "",
                        "用例结果": "pass" if overall_success else "fail",
                        "PCAP文件": ", ".join(step_pcap_files)
                    })
                    continue

                for sql_item in sql_results:
                    sql_text = sql_item.get("sql") or sql_item.get("mongo_query") or ""
                    sql_success = sql_item.get("success", False)
                    rows.append({
                        "批次ID": batch_id,
                        "批次名称": batch_name,
                        "数据库类型": database_type,
                        "数据库版本": database_version,
                        "开始时间": start_time_str,
                        "结束时间": end_time_str,
                        "用例标题": test_case_title,
                        "用例ID": test_case_id,
                        "SQL": sql_text,
                        "SQL是否成功": "pass" if sql_success else "fail",
                        "用例结果": "pass" if overall_success else "fail",
                        "PCAP文件": ", ".join(step_pcap_files)
                    })

        # 生成文件
        temp_dir = tempfile.gettempdir()
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        # 安全处理批次名称，移除文件系统不允许的字符
        import re
        safe_batch_name = str(batch_name) if batch_name else batch_id
        # 移除或替换文件系统不允许的字符：/ \ : * ? " < > |
        safe_batch_name = re.sub(r'[/\\:*?"<>|]', '_', safe_batch_name)
        # 替换空格为下划线
        safe_batch_name = safe_batch_name.replace(' ', '_')
        # 限制长度避免文件名过长
        if len(safe_batch_name) > 50:
            safe_batch_name = safe_batch_name[:50]

        if format == "json":
            import json
            file_path = os.path.join(temp_dir, f"batch_report_{safe_batch_name}_{timestamp}.json")
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(rows, f, ensure_ascii=False, indent=2)
            media_type = "application/json"
        elif format == "csv":
            file_path = os.path.join(temp_dir, f"batch_report_{safe_batch_name}_{timestamp}.csv")
            with open(file_path, 'w', newline='', encoding='utf-8-sig') as f:
                if rows:
                    writer = csv.DictWriter(f, fieldnames=rows[0].keys())
                    writer.writeheader()
                    writer.writerows(rows)
                else:
                    writer = csv.DictWriter(f, fieldnames=[
                        "批次ID", "批次名称", "数据库类型", "数据库版本", "开始时间", "结束时间", "用例标题", "用例ID", "SQL", "SQL是否成功", "用例结果", "PCAP文件"
                    ])
                    writer.writeheader()
            media_type = "text/csv"
        elif format == "excel":
            file_path = os.path.join(temp_dir, f"batch_report_{safe_batch_name}_{timestamp}.xlsx")
            import pandas as _pd
            df = _pd.DataFrame(rows or [], columns=[
                "批次ID", "批次名称", "数据库类型", "数据库版本", "开始时间", "结束时间", "用例标题", "用例ID", "SQL", "SQL是否成功", "用例结果", "PCAP文件"
            ])
            with _pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='批量执行报告', index=False)
            media_type = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        else:
            raise HTTPException(status_code=400, detail="不支持的导出格式")

        filename = os.path.basename(file_path)
        # 对文件名进行URL编码以支持中文字符
        import urllib.parse
        encoded_filename = urllib.parse.quote(filename.encode('utf-8'))
        return FileResponse(
            path=file_path,
            filename=filename,
            media_type=media_type,
            headers={
                "Content-Disposition": f"attachment; filename*=UTF-8''{encoded_filename}"
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"导出批量执行报告失败: {e}")
        raise HTTPException(status_code=500, detail=f"导出批量执行报告失败: {str(e)}")

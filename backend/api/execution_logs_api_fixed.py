"""
执行日志API
提供通用执行日志的查询、统计和管理接口
"""

from fastapi import APIRouter, HTTPException, Query, Depends
from fastapi.responses import StreamingResponse
from typing import Dict, Any, List, Optional
import logging
import io
import csv
from datetime import datetime

from services.execution_log_service import ExecutionLogService
from utils.response import success_response, error_response

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/execution-logs", tags=["执行日志"])

# 创建执行日志服务实例
execution_log_service = ExecutionLogService()


@router.get("/search")
async def search_execution_logs(
    level: Optional[str] = Query(None, description="日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)"),
    category: Optional[str] = Query(None, description="日志类别"),
    task_id: Optional[str] = Query(None, description="任务ID"),
    start_date: Optional[str] = Query(None, description="开始日期 (YYYY-MM-DD HH:mm:ss)"),
    end_date: Optional[str] = Query(None, description="结束日期 (YYYY-MM-DD HH:mm:ss)"),
    search: Optional[str] = Query(None, description="搜索文本（消息、错误信息等）"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(50, ge=1, le=1000, description="每页记录数")
):
    """搜索执行日志"""
    try:
        # 确保服务已初始化
        if not hasattr(execution_log_service, 'mysql_service') or not execution_log_service.mysql_service:
            await execution_log_service.initialize()
        
        # 计算偏移量
        offset = (page - 1) * page_size
        
        # 构建查询条件
        where_conditions = []
        params = []
        
        if level:
            where_conditions.append("level = %s")
            params.append(level)
        
        if category:
            where_conditions.append("category = %s")
            params.append(category)
        
        if task_id:
            where_conditions.append("task_id LIKE %s")
            params.append(f"%{task_id}%")
        
        if start_date:
            where_conditions.append("created_time >= %s")
            params.append(start_date)
        
        if end_date:
            where_conditions.append("created_time <= %s")
            params.append(end_date)
        
        if search:
            search_conditions = [
                "message LIKE %s",
                "error_details LIKE %s",
                "sql_query LIKE %s"
            ]
            search_clause = f"({' OR '.join(search_conditions)})"
            where_conditions.append(search_clause)
            search_param = f"%{search}%"
            params.extend([search_param] * 3)
        
        # 构建SQL查询
        where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"
        
        # 获取总数
        count_sql = f"SELECT COUNT(*) as total FROM execution_logs WHERE {where_clause}"
        count_result = await execution_log_service.mysql_service.execute_query(count_sql, tuple(params))
        total = count_result['data'][0]['total'] if count_result and count_result['data'] else 0
        
        # 获取数据
        data_sql = f"""
            SELECT 
                id, level, category, message, task_id, test_case_id, execution_id,
                database_config_id, sql_query, capture_file, error_details,
                database_type, target_host, target_port, 
                module_name, function_name, line_number, stack_trace,
                created_time as timestamp
            FROM execution_logs 
            WHERE {where_clause}
            ORDER BY created_time DESC 
            LIMIT %s OFFSET %s
        """
        params.extend([page_size, offset])
        
        data_result = await execution_log_service.mysql_service.execute_query(data_sql, tuple(params))
        logs = data_result['data'] if data_result and data_result['data'] else []
        
        return success_response(
            data={
                "total": total,
                "page": page,
                "page_size": page_size,
                "logs": logs
            },
            message="搜索执行日志成功"
        )
        
    except Exception as e:
        logger.error(f"搜索执行日志失败: {e}")
        return error_response(message=f"搜索执行日志失败: {str(e)}")


@router.get("/export")
async def export_execution_logs(
    level: Optional[str] = Query(None, description="日志级别"),
    category: Optional[str] = Query(None, description="日志类别"),
    start_date: Optional[str] = Query(None, description="开始日期"),
    end_date: Optional[str] = Query(None, description="结束日期"),
    limit: int = Query(10000, ge=1, le=50000, description="导出记录数限制")
):
    """导出执行日志为CSV格式"""
    try:
        # 确保服务已初始化
        if not hasattr(execution_log_service, 'mysql_service') or not execution_log_service.mysql_service:
            await execution_log_service.initialize()
        
        # 构建查询条件
        where_conditions = []
        params = []
        
        if level:
            where_conditions.append("level = %s")
            params.append(level)
        
        if category:
            where_conditions.append("category = %s")
            params.append(category)
        
        if start_date:
            where_conditions.append("created_time >= %s")
            params.append(start_date)
        
        if end_date:
            where_conditions.append("created_time <= %s")
            params.append(end_date)
        
        where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"
        
        # 查询数据
        sql = f"""
            SELECT 
                id, level, category, message, task_id, 
                database_type, target_host, target_port,
                sql_query, error_details, created_time as timestamp
            FROM execution_logs 
            WHERE {where_clause}
            ORDER BY created_time DESC 
            LIMIT %s
        """
        params.append(limit)
        
        result = await execution_log_service.mysql_service.execute_query(sql, tuple(params))
        logs = result['data'] if result and result['data'] else []
        
        # 创建CSV内容
        output = io.StringIO()
        writer = csv.writer(output)
        
        # 写入标题行
        headers = [
            'ID', '日志级别', '类别', '消息', '任务ID', 
            '数据库类型', '目标主机', '目标端口',
            'SQL查询', '错误详情', '时间戳'
        ]
        writer.writerow(headers)
        
        # 写入数据行
        for log in logs:
            row = [
                log.get('id', ''),
                log.get('level', ''),
                log.get('category', ''),
                log.get('message', ''),
                log.get('task_id', ''),
                log.get('database_type', ''),
                log.get('target_host', ''),
                log.get('target_port', ''),
                log.get('sql_query', ''),
                log.get('error_details', ''),
                str(log.get('timestamp', ''))
            ]
            writer.writerow(row)
        
        # 生成响应
        output.seek(0)
        csv_content = output.getvalue()
        output.close()
        
        # 创建文件名
        filename = f"execution_logs_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        
        return StreamingResponse(
            io.BytesIO(csv_content.encode('utf-8-sig')),  # 使用BOM以便Excel正确显示中文
            media_type='text/csv',
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )
        
    except Exception as e:
        logger.error(f"导出执行日志失败: {e}")
        return error_response(message=f"导出执行日志失败: {str(e)}")


@router.post("/cleanup")
async def cleanup_execution_logs(
    days: int = Query(30, ge=1, le=365, description="保留最近多少天的日志"),
    levels: Optional[List[str]] = Query(None, description="指定要清理的日志级别"),
    categories: Optional[List[str]] = Query(None, description="指定要清理的日志类别")
):
    """清理过期执行日志"""
    try:
        # 确保服务已初始化
        if not hasattr(execution_log_service, 'mysql_service') or not execution_log_service.mysql_service:
            await execution_log_service.initialize()
        
        # 构建删除条件
        where_conditions = [f"created_time < DATE_SUB(NOW(), INTERVAL %s DAY)"]
        params = [days]
        
        if levels:
            placeholders = ",".join(["%s"] * len(levels))
            where_conditions.append(f"level IN ({placeholders})")
            params.extend(levels)
        
        if categories:
            placeholders = ",".join(["%s"] * len(categories))
            where_conditions.append(f"category IN ({placeholders})")
            params.extend(categories)
        
        where_clause = " AND ".join(where_conditions)
        
        # 先统计要删除的记录数
        count_sql = f"SELECT COUNT(*) as count FROM execution_logs WHERE {where_clause}"
        count_result = await execution_log_service.mysql_service.execute_query(count_sql, tuple(params))
        delete_count = count_result['data'][0]['count'] if count_result and count_result['data'] else 0
        
        if delete_count == 0:
            return success_response(
                data={"deleted_count": 0},
                message="没有找到符合条件的日志记录"
            )
        
        # 执行删除
        delete_sql = f"DELETE FROM execution_logs WHERE {where_clause}"
        await execution_log_service.mysql_service.execute_query(delete_sql, tuple(params))
        
        logger.info(f"清理执行日志成功，删除了 {delete_count} 条记录")
        
        return success_response(
            data={"deleted_count": delete_count},
            message=f"清理执行日志成功，删除了 {delete_count} 条记录"
        )
        
    except Exception as e:
        logger.error(f"清理执行日志失败: {e}")
        return error_response(message=f"清理执行日志失败: {str(e)}")


@router.get("/statistics")
async def get_execution_log_statistics(
    days: int = Query(7, ge=1, le=90, description="统计最近多少天的数据")
):
    """获取执行日志统计信息"""
    try:
        # 确保服务已初始化
        if not hasattr(execution_log_service, 'mysql_service') or not execution_log_service.mysql_service:
            await execution_log_service.initialize()
        
        # 获取日志级别分布
        level_sql = """
            SELECT level, COUNT(*) as count
            FROM execution_logs 
            WHERE created_time >= DATE_SUB(NOW(), INTERVAL %s DAY)
            GROUP BY level
            ORDER BY count DESC
        """
        level_result = await execution_log_service.mysql_service.execute_query(level_sql, (days,))
        level_distribution = level_result['data'] if level_result and level_result['data'] else []
        
        # 获取类别分布
        category_sql = """
            SELECT category, COUNT(*) as count
            FROM execution_logs 
            WHERE created_time >= DATE_SUB(NOW(), INTERVAL %s DAY)
            GROUP BY category
            ORDER BY count DESC
        """
        category_result = await execution_log_service.mysql_service.execute_query(category_sql, (days,))
        category_distribution = category_result['data'] if category_result and category_result['data'] else []
        
        # 获取每日日志趋势
        daily_sql = """
            SELECT 
                DATE(created_time) as date,
                COUNT(*) as total_logs,
                SUM(CASE WHEN level = 'ERROR' THEN 1 ELSE 0 END) as error_logs,
                SUM(CASE WHEN level = 'WARNING' THEN 1 ELSE 0 END) as warning_logs
            FROM execution_logs 
            WHERE created_time >= DATE_SUB(NOW(), INTERVAL %s DAY)
            GROUP BY DATE(created_time)
            ORDER BY date DESC
        """
        daily_result = await execution_log_service.mysql_service.execute_query(daily_sql, (days,))
        daily_trend = daily_result['data'] if daily_result and daily_result['data'] else []
        
        # 获取数据库类型分布
        db_type_sql = """
            SELECT database_type, COUNT(*) as count
            FROM execution_logs 
            WHERE created_time >= DATE_SUB(NOW(), INTERVAL %s DAY)
            AND database_type IS NOT NULL
            GROUP BY database_type
            ORDER BY count DESC
        """
        db_type_result = await execution_log_service.mysql_service.execute_query(db_type_sql, (days,))
        database_type_distribution = db_type_result['data'] if db_type_result and db_type_result['data'] else []
        
        return success_response(
            data={
                "period_days": days,
                "level_distribution": level_distribution,
                "category_distribution": category_distribution,
                "daily_trend": daily_trend,
                "database_type_distribution": database_type_distribution
            },
            message=f"获取最近 {days} 天执行日志统计成功"
        )
        
    except Exception as e:
        logger.error(f"获取执行日志统计失败: {e}")
        return error_response(message=f"获取执行日志统计失败: {str(e)}")

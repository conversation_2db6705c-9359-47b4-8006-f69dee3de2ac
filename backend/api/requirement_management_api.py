"""
协议需求管理API
"""
import logging
import uuid
from typing import List, Optional, Dict, Any
from datetime import datetime
from fastapi import APIRouter, HTTPException, Query, Depends, Body
from fastapi.responses import JSONResponse

from models.requirement_management import (
    ProtocolRequirementCreate,
    ProtocolRequirementUpdate,
    ProtocolRequirementResponse,
    ProtocolRequirementQuery,
    ProtocolRequirementListResponse,
    RequirementStatistics,
    DocumentParseRequest,
    DocumentParseResponse,
    RequirementBatchOperation,
    RequirementBatchOperationResponse,
    RequirementTypeEnum,
    ProtocolTypeEnum,
    RequirementPriorityEnum,
    RequirementStatusEnum,
    RequirementComplexityEnum,
    TestabilityEnum,
    DocumentTypeEnum
)
from services.requirement_management_service import requirement_management_service
from services.requirement_ai_service import requirement_ai_service

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/requirements", tags=["协议需求管理"])


@router.get("/", response_model=ProtocolRequirementListResponse)
async def get_requirements(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    keyword: Optional[str] = Query(None, description="关键词搜索"),
    requirement_type: Optional[RequirementTypeEnum] = Query(None, description="需求类型筛选"),
    protocol_type: Optional[ProtocolTypeEnum] = Query(None, description="协议类型筛选"),
    protocol_version: Optional[str] = Query(None, description="协议版本筛选"),
    priority: Optional[RequirementPriorityEnum] = Query(None, description="优先级筛选"),
    complexity: Optional[RequirementComplexityEnum] = Query(None, description="复杂度筛选"),
    testability: Optional[TestabilityEnum] = Query(None, description="可测试性筛选"),
    status: Optional[RequirementStatusEnum] = Query(None, description="状态筛选"),
    category: Optional[str] = Query(None, description="分类筛选"),
    module: Optional[str] = Query(None, description="模块筛选"),
    author: Optional[str] = Query(None, description="创建者筛选"),
    reviewer: Optional[str] = Query(None, description="评审者筛选"),
    tags: Optional[str] = Query(None, description="标签筛选(逗号分隔)"),
    has_dependencies: Optional[bool] = Query(None, description="是否有依赖关系"),
    sort_by: str = Query("created_at", description="排序字段"),
    sort_order: str = Query("desc", description="排序方向")
):
    """获取协议需求列表"""
    try:
        query_params = ProtocolRequirementQuery(
            page=page,
            page_size=page_size,
            keyword=keyword,
            requirement_type=requirement_type,
            protocol_type=protocol_type,
            protocol_version=protocol_version,
            priority=priority,
            complexity=complexity,
            testability=testability,
            status=status,
            category=category,
            module=module,
            author=author,
            reviewer=reviewer,
            tags=tags.split(',') if tags else None,
            has_dependencies=has_dependencies,
            sort_by=sort_by,
            sort_order=sort_order
        )
        
        result = await requirement_management_service.get_requirements(query_params)
        return result
        
    except Exception as e:
        logger.error(f"获取需求列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取需求列表失败: {str(e)}")


@router.get("/{requirement_id}", response_model=ProtocolRequirementResponse)
async def get_requirement(requirement_id: str):
    """获取单个协议需求详情"""
    try:
        requirement = await requirement_management_service.get_requirement_by_id(requirement_id)
        if not requirement:
            raise HTTPException(status_code=404, detail="需求不存在")
        return requirement
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取需求详情失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取需求详情失败: {str(e)}")


@router.post("/", response_model=ProtocolRequirementResponse)
async def create_requirement(requirement_data: ProtocolRequirementCreate):
    """创建新的协议需求"""
    try:
        logger.info(f"创建新需求: {requirement_data.title}")
        requirement = await requirement_management_service.create_requirement(requirement_data)
        return requirement
        
    except Exception as e:
        logger.error(f"创建需求失败: {e}")
        raise HTTPException(status_code=500, detail=f"创建需求失败: {str(e)}")


@router.put("/{requirement_id}", response_model=ProtocolRequirementResponse)
async def update_requirement(requirement_id: str, requirement_data: ProtocolRequirementUpdate):
    """更新协议需求"""
    try:
        logger.info(f"更新需求: {requirement_id}")
        requirement = await requirement_management_service.update_requirement(requirement_id, requirement_data)
        if not requirement:
            raise HTTPException(status_code=404, detail="需求不存在")
        return requirement
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新需求失败: {e}")
        raise HTTPException(status_code=500, detail=f"更新需求失败: {str(e)}")


@router.delete("/{requirement_id}")
async def delete_requirement(requirement_id: str):
    """删除协议需求"""
    try:
        logger.info(f"删除需求: {requirement_id}")
        success = await requirement_management_service.delete_requirement(requirement_id)
        if not success:
            raise HTTPException(status_code=404, detail="需求不存在")
        return {"message": "需求删除成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除需求失败: {e}")
        raise HTTPException(status_code=500, detail=f"删除需求失败: {str(e)}")


@router.get("/statistics/overview", response_model=RequirementStatistics)
async def get_requirements_statistics():
    """获取需求统计信息"""
    try:
        stats = await requirement_management_service.get_requirements_statistics()
        return stats
        
    except Exception as e:
        logger.error(f"获取需求统计失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取需求统计失败: {str(e)}")


@router.post("/parse-document", response_model=DocumentParseResponse)
async def parse_protocol_document(parse_request: DocumentParseRequest):
    """解析协议文档并提取需求"""
    try:
        logger.info(f"解析协议文档: {parse_request.document_title}")
        result = await requirement_ai_service.parse_protocol_document(parse_request)
        return result
        
    except Exception as e:
        logger.error(f"解析协议文档失败: {e}")
        raise HTTPException(status_code=500, detail=f"解析协议文档失败: {str(e)}")


@router.post("/batch-operation", response_model=RequirementBatchOperationResponse)
async def batch_operation_requirements(operation: RequirementBatchOperation):
    """批量操作需求"""
    try:
        logger.info(f"批量操作需求: {operation.operation_type}, 数量: {len(operation.requirement_ids)}")
        result = await requirement_management_service.batch_operation_requirements(operation)
        return result
        
    except Exception as e:
        logger.error(f"批量操作需求失败: {e}")
        raise HTTPException(status_code=500, detail=f"批量操作需求失败: {str(e)}")


@router.get("/dependencies/{requirement_id}")
async def get_requirement_dependencies(requirement_id: str):
    """获取需求的依赖关系图"""
    try:
        dependencies = await requirement_management_service.get_requirement_dependencies(requirement_id)
        return dependencies
        
    except Exception as e:
        logger.error(f"获取需求依赖关系失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取需求依赖关系失败: {str(e)}")


@router.post("/validate-dependencies")
async def validate_requirement_dependencies(requirement_ids: List[str] = Body(...)):
    """验证需求依赖关系的一致性"""
    try:
        validation_result = await requirement_management_service.validate_dependencies(requirement_ids)
        return validation_result
        
    except Exception as e:
        logger.error(f"验证需求依赖关系失败: {e}")
        raise HTTPException(status_code=500, detail=f"验证需求依赖关系失败: {str(e)}")


@router.get("/test-cases/{requirement_id}")
async def get_requirement_test_cases(requirement_id: str):
    """获取需求关联的测试用例"""
    try:
        test_cases = await requirement_management_service.get_requirement_test_cases(requirement_id)
        return test_cases
        
    except Exception as e:
        logger.error(f"获取需求测试用例失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取需求测试用例失败: {str(e)}")


@router.post("/generate-test-cases/{requirement_id}")
async def generate_test_cases_for_requirement(
    requirement_id: str,
    count: int = Query(5, ge=1, le=20, description="生成测试用例数量"),
    focus_areas: Optional[str] = Query(None, description="重点关注领域(逗号分隔)")
):
    """为特定需求生成测试用例"""
    try:
        logger.info(f"为需求 {requirement_id} 生成测试用例，数量: {count}")
        
        # 获取需求详情
        requirement = await requirement_management_service.get_requirement_by_id(requirement_id)
        if not requirement:
            raise HTTPException(status_code=404, detail="需求不存在")
        
        # 使用AI服务生成测试用例
        focus_list = focus_areas.split(',') if focus_areas else []
        test_cases = await requirement_ai_service.generate_test_cases_for_requirement(
            requirement, count, focus_list
        )
        
        return {
            "success": True,
            "requirement_id": requirement_id,
            "requirement_title": requirement.title,
            "generated_count": len(test_cases),
            "test_cases": test_cases,
            "message": f"成功为需求生成 {len(test_cases)} 个测试用例"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"为需求生成测试用例失败: {e}")
        raise HTTPException(status_code=500, detail=f"为需求生成测试用例失败: {str(e)}")


# 获取枚举选项的辅助接口
@router.get("/enums/requirement-types")
async def get_requirement_types():
    """获取需求类型枚举"""
    return [{"value": item.value, "label": item.value} for item in RequirementTypeEnum]


@router.get("/enums/protocol-types")
async def get_protocol_types():
    """获取协议类型枚举"""
    return [{"value": item.value, "label": item.value} for item in ProtocolTypeEnum]


@router.get("/enums/priorities")
async def get_priorities():
    """获取优先级枚举"""
    return [{"value": item.value, "label": item.value} for item in RequirementPriorityEnum]


@router.get("/enums/statuses")
async def get_statuses():
    """获取状态枚举"""
    return [{"value": item.value, "label": item.value} for item in RequirementStatusEnum]


@router.get("/enums/complexities")
async def get_complexities():
    """获取复杂度枚举"""
    return [{"value": item.value, "label": item.value} for item in RequirementComplexityEnum]


@router.get("/enums/testabilities")
async def get_testabilities():
    """获取可测试性枚举"""
    return [{"value": item.value, "label": item.value} for item in TestabilityEnum]


@router.get("/enums/document-types")
async def get_document_types():
    """获取文档类型枚举"""
    return [{"value": item.value, "label": item.value} for item in DocumentTypeEnum]


@router.get("/options/categories")
async def get_requirement_categories():
    """获取需求分类选项"""
    try:
        categories = await requirement_management_service.get_categories()
        return categories
        
    except Exception as e:
        logger.error(f"获取需求分类失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取需求分类失败: {str(e)}")


@router.get("/options/modules")
async def get_requirement_modules():
    """获取需求模块选项"""
    try:
        modules = await requirement_management_service.get_modules()
        return modules
        
    except Exception as e:
        logger.error(f"获取需求模块失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取需求模块失败: {str(e)}")


@router.get("/options/authors")
async def get_requirement_authors():
    """获取需求创建者选项"""
    try:
        authors = await requirement_management_service.get_authors()
        return authors
        
    except Exception as e:
        logger.error(f"获取需求创建者失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取需求创建者失败: {str(e)}")


@router.get("/options/reviewers")
async def get_requirement_reviewers():
    """获取需求评审者选项"""
    try:
        reviewers = await requirement_management_service.get_reviewers()
        return reviewers
        
    except Exception as e:
        logger.error(f"获取需求评审者失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取需求评审者失败: {str(e)}")


@router.get("/options/tags")
async def get_requirement_tags():
    """获取需求标签选项"""
    try:
        tags = await requirement_management_service.get_tags()
        return tags
        
    except Exception as e:
        logger.error(f"获取需求标签失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取需求标签失败: {str(e)}")


@router.post("/export")
async def export_requirements(
    format: str = Query("excel", description="导出格式: excel, csv, json"),
    requirement_ids: Optional[List[str]] = Body(None, description="指定需求ID列表，为空则导出全部")
):
    """导出需求"""
    try:
        logger.info(f"导出需求，格式: {format}")
        
        if requirement_ids:
            logger.info(f"导出指定需求，数量: {len(requirement_ids)}")
        else:
            logger.info("导出全部需求")
            
        export_result = await requirement_management_service.export_requirements(format, requirement_ids)
        return export_result
        
    except Exception as e:
        logger.error(f"导出需求失败: {e}")
        raise HTTPException(status_code=500, detail=f"导出需求失败: {str(e)}")


@router.post("/import")
async def import_requirements(
    file_path: str = Body(..., description="导入文件路径"),
    format: str = Body("excel", description="文件格式: excel, csv, json"),
    options: Dict[str, Any] = Body(default_factory=dict, description="导入选项")
):
    """导入需求"""
    try:
        logger.info(f"导入需求，文件: {file_path}, 格式: {format}")
        
        import_result = await requirement_management_service.import_requirements(file_path, format, options)
        return import_result
        
    except Exception as e:
        logger.error(f"导入需求失败: {e}")
        raise HTTPException(status_code=500, detail=f"导入需求失败: {str(e)}")

"""
网关服务器管理API
"""

from fastapi import APIRouter, HTTPException, Query, Depends
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
import logging

from models.gateway_server import GatewayServer, GatewayConnectionTest
from services.gateway_server_service import gateway_server_service

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/gateways", tags=["gateway-servers"])

# Pydantic 模型用于请求/响应
class GatewayServerRequest(BaseModel):
    name: str = Field(..., description="网关服务器名称")
    host: str = Field(..., description="服务器IP地址")
    port: int = Field(22, description="SSH端口")
    username: str = Field(..., description="用户名")
    password: str = Field(..., description="密码")
    upload_path: str = Field("/tmp", description="文件上传路径")
    description: str = Field("", description="描述信息")
    is_active: bool = Field(True, description="是否激活")
    is_default: bool = Field(False, description="是否为默认网关")
    connection_timeout: int = Field(30, description="连接超时(秒)")
    max_connections: int = Field(10, description="最大连接数")
    gateway_type: str = Field("ssh", description="网关类型")
    proxy_enabled: bool = Field(False, description="是否启用代理")
    proxy_host: Optional[str] = Field(None, description="代理主机")
    proxy_port: Optional[int] = Field(None, description="代理端口")
    # Kafka相关字段
    kafka_enabled: bool = Field(False, description="是否启用Kafka")
    kafka_host: Optional[str] = Field(None, description="Kafka服务器IP")
    kafka_port: int = Field(9092, description="Kafka端口")
    kafka_topic: Optional[str] = Field(None, description="Kafka主题")

class GatewayServerResponse(BaseModel):
    id: int
    name: str
    host: str
    port: int
    username: str
    upload_path: str
    description: str
    is_active: bool
    is_default: bool
    connection_timeout: int
    max_connections: int
    gateway_type: str
    proxy_enabled: bool
    proxy_host: Optional[str]
    proxy_port: Optional[int]
    # Kafka相关字段（设为可选）
    kafka_enabled: bool = False
    kafka_host: Optional[str] = None
    kafka_port: int = 9092
    kafka_topic: Optional[str] = None
    created_at: Optional[str]
    updated_at: Optional[str]
    # 注意：密码不在响应中返回

class GatewayListResponse(BaseModel):
    gateways: List[GatewayServerResponse]
    total: int
    page: int
    page_size: int

class ConnectionTestResponse(BaseModel):
    success: bool
    message: str
    response_time: float
    upload_test: Optional[bool] = None
    disk_space: Optional[str] = None
    server_info: Optional[Dict[str, Any]] = None

@router.post("/", response_model=Dict[str, Any])
async def create_gateway(gateway_request: GatewayServerRequest):
    """创建网关服务器配置"""
    try:
        # 转换为领域模型
        gateway = GatewayServer(
            name=gateway_request.name,
            host=gateway_request.host,
            port=gateway_request.port,
            username=gateway_request.username,
            password=gateway_request.password,
            upload_path=gateway_request.upload_path,
            description=gateway_request.description,
            is_active=gateway_request.is_active,
            is_default=gateway_request.is_default,
            connection_timeout=gateway_request.connection_timeout,
            max_connections=gateway_request.max_connections,
            gateway_type=gateway_request.gateway_type,
            proxy_enabled=gateway_request.proxy_enabled,
            proxy_host=gateway_request.proxy_host,
            proxy_port=gateway_request.proxy_port,
            kafka_enabled=gateway_request.kafka_enabled,
            kafka_host=gateway_request.kafka_host,
            kafka_port=gateway_request.kafka_port,
            kafka_topic=gateway_request.kafka_topic
        )
        
        gateway_id = await gateway_server_service.create_gateway(gateway)
        
        return {
            "success": True,
            "message": "网关服务器创建成功",
            "gateway_id": gateway_id
        }
        
    except Exception as e:
        logger.error(f"Failed to create gateway: {e}")
        raise HTTPException(status_code=400, detail=f"创建网关服务器失败: {str(e)}")

@router.get("/", response_model=GatewayListResponse)
async def list_gateways(
    search_ip: Optional[str] = Query(None, description="根据IP搜索"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页大小")
):
    """获取网关服务器列表（支持IP搜索和分页）"""
    try:
        gateways, total = await gateway_server_service.list_gateways(
            search_ip=search_ip,
            page=page,
            page_size=page_size
        )
        
        # 转换为响应模型（排除密码）
        gateway_responses = []
        for gateway in gateways:
            data = gateway.to_dict()
            data.pop('password', None)  # 移除密码字段
            gateway_responses.append(GatewayServerResponse(**data))
        
        return GatewayListResponse(
            gateways=gateway_responses,
            total=total,
            page=page,
            page_size=page_size
        )
        
    except Exception as e:
        logger.error(f"Failed to list gateways: {e}")
        raise HTTPException(status_code=500, detail=f"获取网关服务器列表失败: {str(e)}")

@router.get("/stats", response_model=Dict[str, Any])
async def get_gateway_stats():
    """获取网关服务器统计信息"""
    try:
        stats = await gateway_server_service.get_connection_stats()
        return stats
        
    except Exception as e:
        logger.error(f"Failed to get gateway stats: {e}")
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")

@router.get("/default/get", response_model=Optional[GatewayServerResponse])
async def get_default_gateway():
    """获取默认网关服务器"""
    try:
        gateway = await gateway_server_service.get_default_gateway()
        
        if not gateway:
            return None
        
        # 转换为响应模型（排除密码）
        data = gateway.to_dict()
        data.pop('password', None)
        
        return GatewayServerResponse(**data)
        
    except Exception as e:
        logger.error(f"Failed to get default gateway: {e}")
        raise HTTPException(status_code=500, detail=f"获取默认网关失败: {str(e)}")

@router.post("/{gateway_id}/set-default", response_model=Dict[str, Any])
async def set_default_gateway(gateway_id: int):
    """设置默认网关服务器"""
    try:
        success = await gateway_server_service.set_default_gateway(gateway_id)
        
        if not success:
            raise HTTPException(status_code=404, detail="网关服务器不存在")
        
        return {
            "success": True,
            "message": "默认网关设置成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to set default gateway {gateway_id}: {e}")
        raise HTTPException(status_code=500, detail=f"设置默认网关失败: {str(e)}")

@router.get("/{gateway_id}", response_model=GatewayServerResponse)
async def get_gateway(gateway_id: int):
    """获取指定网关服务器配置"""
    try:
        gateway = await gateway_server_service.get_gateway(gateway_id)
        
        if not gateway:
            raise HTTPException(status_code=404, detail="网关服务器不存在")
        
        # 转换为响应模型（排除密码）
        data = gateway.to_dict()
        data.pop('password', None)
        
        return GatewayServerResponse(**data)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get gateway {gateway_id}: {e}")
        raise HTTPException(status_code=500, detail=f"获取网关服务器失败: {str(e)}")

@router.put("/{gateway_id}", response_model=Dict[str, Any])
async def update_gateway(gateway_id: int, gateway_request: GatewayServerRequest):
    """更新网关服务器配置"""
    try:
        # 检查网关是否存在
        existing_gateway = await gateway_server_service.get_gateway(gateway_id)
        if not existing_gateway:
            raise HTTPException(status_code=404, detail="网关服务器不存在")
        
        # 转换为领域模型
        gateway = GatewayServer(
            id=gateway_id,
            name=gateway_request.name,
            host=gateway_request.host,
            port=gateway_request.port,
            username=gateway_request.username,
            password=gateway_request.password,
            upload_path=gateway_request.upload_path,
            description=gateway_request.description,
            is_active=gateway_request.is_active,
            is_default=gateway_request.is_default,
            connection_timeout=gateway_request.connection_timeout,
            max_connections=gateway_request.max_connections,
            gateway_type=gateway_request.gateway_type,
            proxy_enabled=gateway_request.proxy_enabled,
            proxy_host=gateway_request.proxy_host,
            proxy_port=gateway_request.proxy_port,
            kafka_enabled=gateway_request.kafka_enabled,
            kafka_host=gateway_request.kafka_host,
            kafka_port=gateway_request.kafka_port,
            kafka_topic=gateway_request.kafka_topic
        )
        
        success = await gateway_server_service.update_gateway(gateway)
        
        if not success:
            raise HTTPException(status_code=400, detail="更新网关服务器失败")
        
        return {
            "success": True,
            "message": "网关服务器更新成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update gateway {gateway_id}: {e}")
        raise HTTPException(status_code=500, detail=f"更新网关服务器失败: {str(e)}")

@router.delete("/{gateway_id}", response_model=Dict[str, Any])
async def delete_gateway(gateway_id: int):
    """删除网关服务器配置"""
    try:
        success = await gateway_server_service.delete_gateway(gateway_id)
        
        if not success:
            raise HTTPException(status_code=404, detail="网关服务器不存在")
        
        return {
            "success": True,
            "message": "网关服务器删除成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete gateway {gateway_id}: {e}")
        raise HTTPException(status_code=500, detail=f"删除网关服务器失败: {str(e)}")

@router.post("/{gateway_id}/test", response_model=ConnectionTestResponse)
async def test_gateway_connection(gateway_id: int):
    """测试网关服务器连接"""
    try:
        gateway = await gateway_server_service.get_gateway(gateway_id)
        
        if not gateway:
            raise HTTPException(status_code=404, detail="网关服务器不存在")
        
        result = await gateway_server_service.test_connection(gateway)
        
        return ConnectionTestResponse(
            success=result.success,
            message=result.message,
            response_time=result.response_time,
            upload_test=result.upload_test,
            disk_space=result.disk_space,
            server_info=result.server_info
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to test gateway connection {gateway_id}: {e}")
        raise HTTPException(status_code=500, detail=f"测试连接失败: {str(e)}")

@router.post("/test", response_model=ConnectionTestResponse)
async def test_connection_config(gateway_request: GatewayServerRequest):
    """测试网关配置连接（不保存配置）"""
    try:
        # 转换为领域模型
        gateway = GatewayServer(
            name=gateway_request.name,
            host=gateway_request.host,
            port=gateway_request.port,
            username=gateway_request.username,
            password=gateway_request.password,
            upload_path=gateway_request.upload_path,
            description=gateway_request.description,
            is_active=gateway_request.is_active,
            is_default=gateway_request.is_default,
            connection_timeout=gateway_request.connection_timeout,
            max_connections=gateway_request.max_connections,
            gateway_type=gateway_request.gateway_type,
            proxy_enabled=gateway_request.proxy_enabled,
            proxy_host=gateway_request.proxy_host,
            proxy_port=gateway_request.proxy_port,
            kafka_enabled=gateway_request.kafka_enabled,
            kafka_host=gateway_request.kafka_host,
            kafka_port=gateway_request.kafka_port,
            kafka_topic=gateway_request.kafka_topic
        )
        
        result = await gateway_server_service.test_connection(gateway)
        
        return ConnectionTestResponse(
            success=result.success,
            message=result.message,
            response_time=result.response_time,
            upload_test=result.upload_test,
            disk_space=result.disk_space,
            server_info=result.server_info
        )
        
    except Exception as e:
        logger.error(f"Failed to test connection config: {e}")
        raise HTTPException(status_code=500, detail=f"测试连接失败: {str(e)}")

@router.get("/search/{ip_pattern}", response_model=List[GatewayServerResponse])
async def search_gateways_by_ip(ip_pattern: str):
    """根据IP模式搜索网关服务器"""
    try:
        gateways = await gateway_server_service.search_by_ip(ip_pattern)
        
        # 转换为响应模型（排除密码）
        gateway_responses = []
        for gateway in gateways:
            data = gateway.to_dict()
            data.pop('password', None)
            gateway_responses.append(GatewayServerResponse(**data))
        
        return gateway_responses
        
    except Exception as e:
        logger.error(f"Failed to search gateways by IP {ip_pattern}: {e}")
        raise HTTPException(status_code=500, detail=f"搜索网关服务器失败: {str(e)}")

@router.post("/import/data", response_model=Dict[str, Any])
async def import_gateways(gateways_data: List[Dict[str, Any]]):
    """导入网关配置"""
    try:
        result = await gateway_server_service.import_gateways(gateways_data)
        return result
        
    except Exception as e:
        logger.error(f"Failed to import gateways: {e}")
        raise HTTPException(status_code=500, detail=f"导入配置失败: {str(e)}")

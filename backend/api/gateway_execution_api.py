"""
网关执行API - 提供网关执行功能的REST API接口
"""
import logging
import traceback
from typing import Dict, Any, Optional, List
from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel, Field

from services.gateway_execution_service import gateway_execution_service
from services.arq_task_management_service import arq_task_management_service, TaskInfo, TaskStatus

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/gateway-execution", tags=["网关执行"])


class GatewayExecutionRequest(BaseModel):
    """网关执行请求"""
    test_case_id: str = Field(..., description="测试用例ID")
    gateway_server_id: int = Field(..., description="网关服务器ID")
    wait_time: int = Field(default=2, ge=1, le=10, description="Kafka消费等待时间（1-10秒）")
    async_execution: bool = Field(default=False, description="是否异步执行")


class GatewayExecutionResponse(BaseModel):
    """网关执行响应"""
    success: bool
    execution_id: Optional[str] = None
    test_case_id: Optional[str] = None
    gateway_server: Optional[Dict[str, Any]] = None
    pcap_upload: Optional[Dict[str, Any]] = None
    kafka_consume: Optional[Dict[str, Any]] = None
    sql_comparison: Optional[Dict[str, Any]] = None
    summary: Optional[Dict[str, Any]] = None
    execution_time: Optional[str] = None
    error: Optional[str] = None
    step: Optional[str] = None


# 存储异步执行状态
async_execution_status = {}


@router.post("/execute", response_model=GatewayExecutionResponse)
async def execute_gateway_test(request: GatewayExecutionRequest, background_tasks: BackgroundTasks):
    """
    执行网关测试
    
    功能流程：
    1. 上传测试用例最新执行记录的PCAP文件到网关服务器
    2. 根据网关服务器的Kafka配置消费数据，过滤cmd_type=login消息
    3. 从Kafka消息中提取SQL，与测试用例数据进行AI对比
    
    Args:
        request: 网关执行请求参数
        background_tasks: 后台任务处理器
    
    Returns:
        网关执行结果
    """
    try:
        logger.info(f"收到网关执行请求 - 测试用例ID: {request.test_case_id}, 网关服务器ID: {request.gateway_server_id}")

        if request.async_execution:
            # 异步执行
            import uuid
            execution_id = str(uuid.uuid4())
            
            # 记录异步执行状态
            async_execution_status[execution_id] = {
                "status": "running",
                "progress": 0.0,
                "current_step": "initializing",
                "message": "开始网关执行...",
                "start_time": None,
                "result": None
            }
            
            # 在任务管理系统中创建任务记录
        try:
                task_info = TaskInfo(
                    task_id=execution_id,
            task_type="gateway_single_execution",
                    status=TaskStatus.PENDING,
                    progress=0,
            message="正在准备执行网关单个用例...",
                    config_id=request.gateway_server_id,
                    sql_query=f"测试用例ID: {request.test_case_id}",
            # 结果详情会在完成时写入 result 字段
                )
                await arq_task_management_service.create_task(task_info)
            except Exception as e:
                logger.error(f"创建任务管理记录失败: {str(e)}")
                # 不阻断执行，只是记录错误
            
            # 提交后台任务
            background_tasks.add_task(
                _execute_gateway_test_async,
                execution_id,
                request.test_case_id,
                request.gateway_server_id,
                request.wait_time
            )
            
            return GatewayExecutionResponse(
                success=True,
                execution_id=execution_id,
                test_case_id=request.test_case_id
            )
        else:
            # 同步执行
            result = await gateway_execution_service.execute_gateway_test(
                test_case_id=request.test_case_id,
                gateway_server_id=request.gateway_server_id,
                wait_time=request.wait_time
            )
            
            return GatewayExecutionResponse(**result)

    except Exception as e:
        logger.error(f"网关执行API失败: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/status/{execution_id}")
async def get_execution_status(execution_id: str):
    """
    获取异步执行状态
    
    Args:
        execution_id: 执行ID
    
    Returns:
        执行状态信息
    """
    try:
        if execution_id not in async_execution_status:
            raise HTTPException(status_code=404, detail="执行ID不存在")
        
        status_info = async_execution_status[execution_id]
        
        return {
            "execution_id": execution_id,
            "status": status_info["status"],
            "progress": status_info["progress"],
            "current_step": status_info["current_step"],
            "message": status_info["message"],
            "start_time": status_info.get("start_time"),
            "result": status_info.get("result")
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取执行状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/status/{execution_id}")
async def clear_execution_status(execution_id: str):
    """
    清理执行状态记录
    
    Args:
        execution_id: 执行ID
    
    Returns:
        清理结果
    """
    try:
        if execution_id in async_execution_status:
            del async_execution_status[execution_id]
            return {"success": True, "message": "执行状态已清理"}
        else:
            return {"success": False, "message": "执行ID不存在"}

    except Exception as e:
        logger.error(f"清理执行状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/status")
async def list_execution_status():
    """
    列出所有执行状态
    
    Returns:
        执行状态列表
    """
    try:
        status_list = []
        for execution_id, status_info in async_execution_status.items():
            status_list.append({
                "execution_id": execution_id,
                "status": status_info["status"],
                "progress": status_info["progress"],
                "current_step": status_info["current_step"],
                "message": status_info["message"],
                "start_time": status_info.get("start_time")
            })
        
        return {
            "success": True,
            "executions": status_list,
            "total_count": len(status_list)
        }

    except Exception as e:
        logger.error(f"列出执行状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


async def _execute_gateway_test_async(
    execution_id: str,
    test_case_id: str,
    gateway_server_id: int,
    wait_time: int
):
    """
    异步执行网关测试的后台任务
    
    Args:
        execution_id: 执行ID
        test_case_id: 测试用例ID
        gateway_server_id: 网关服务器ID
        wait_time: Kafka消费等待时间
    """
    try:
        from datetime import datetime
        
        # 更新状态为运行中
        async_execution_status[execution_id].update({
            "status": "running",
            "progress": 0.1,
            "current_step": "pcap_upload",
            "message": "开始上传PCAP文件...",
            "start_time": datetime.now().isoformat()
        })
        
        # 同步更新任务管理系统状态
            try:
                await arq_task_management_service.update_task_progress(
                    execution_id,
                    TaskStatus.RUNNING,
                    10,
                    "开始上传PCAP文件..."
                )
        except Exception as e:
            logger.error(f"更新任务管理状态失败: {str(e)}")

        # 执行网关测试
        result = await gateway_execution_service.execute_gateway_test(
            test_case_id=test_case_id,
            gateway_server_id=gateway_server_id,
            wait_time=wait_time
        )

        # 更新最终状态
        final_status = "completed" if result.get("success") else "failed"
        async_execution_status[execution_id].update({
            "status": final_status,
            "progress": 1.0,
            "current_step": "completed",
            "message": "网关执行完成" if result.get("success") else f"网关执行失败: {result.get('error', '未知错误')}",
            "result": result
        })
        
        # 同步更新任务管理系统最终状态
        try:
            await arq_task_management_service.update_task_result(
                execution_id,
                TaskStatus.COMPLETED if result.get("success") else TaskStatus.FAILED,
                100,
                "网关执行完成" if result.get("success") else f"网关执行失败: {result.get('error', '未知错误')}",
                result
            )
        except Exception as e:
            logger.error(f"更新任务管理最终状态失败: {str(e)}")

    except Exception as e:
        logger.error(f"异步网关执行失败 (ID: {execution_id}): {str(e)}")
        logger.error(traceback.format_exc())
        
        # 更新状态为失败
        async_execution_status[execution_id].update({
            "status": "failed",
            "progress": 0.0,
            "current_step": "error",
            "message": f"执行失败: {str(e)}",
            "result": {
                "success": False,
                "error": str(e),
                "step": "async_execution_error"
            }
        })
        
        # 同步更新任务管理系统失败状态
        try:
            await arq_task_management_service.update_task_result(
                execution_id,
                TaskStatus.FAILED,
                0,
                f"执行失败: {str(e)}",
                {
                    "success": False,
                    "error": str(e),
                    "step": "async_execution_error"
                }
            )
        except Exception as update_e:
            logger.error(f"更新任务管理失败状态失败: {str(update_e)}")


class GatewayBatchExecutionRequest(BaseModel):
    """网关批量执行请求"""
    test_case_ids: List[str] = Field(..., description="测试用例ID列表", min_items=1)
    gateway_server_id: int = Field(..., description="网关服务器ID")
    wait_time: int = Field(default=2, ge=1, le=10, description="Kafka消费等待时间（1-10秒）")


class GatewayBatchExecutionResponse(BaseModel):
    """网关批量执行响应"""
    success: bool
    task_id: Optional[str] = None
    total_cases: Optional[int] = None
    message: Optional[str] = None


@router.post("/batch-execute", response_model=GatewayBatchExecutionResponse)
async def execute_gateway_batch(request: GatewayBatchExecutionRequest, background_tasks: BackgroundTasks):
    """提交网关批量执行任务（异步）并纳入任务管理"""
    try:
        import uuid
        batch_id = str(uuid.uuid4())

        # 在任务管理系统中创建任务记录
        try:
            task_info = TaskInfo(
                task_id=batch_id,
                task_type="gateway_batch_execution",
                status=TaskStatus.PENDING,
                progress=0,
                message=f"准备执行 {len(request.test_case_ids)} 个用例...",
                config_id=request.gateway_server_id,
                total_requests=len(request.test_case_ids),
                description="网关批量执行"
            )
            await arq_task_management_service.create_task(task_info)
        except Exception as e:
            logger.error(f"创建批量任务记录失败: {e}")

        # 提交后台任务
        background_tasks.add_task(
            _execute_gateway_batch_async,
            batch_id,
            request.test_case_ids,
            request.gateway_server_id,
            request.wait_time
        )

        return GatewayBatchExecutionResponse(
            success=True,
            task_id=batch_id,
            total_cases=len(request.test_case_ids),
            message="网关批量执行任务已提交"
        )
    except Exception as e:
        logger.error(f"提交网关批量执行任务失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


async def _execute_gateway_batch_async(
    batch_id: str,
    test_case_ids: List[str],
    gateway_server_id: int,
    wait_time: int
):
    """后台执行网关批量任务，串行执行并聚合结果"""
    from time import perf_counter
    start = perf_counter()
    success_count = 0
    results: List[Dict[str, Any]] = []

    try:
        total = len(test_case_ids)
        await arq_task_management_service.update_task_progress(
            batch_id, TaskStatus.RUNNING, 0, f"开始批量执行，共 {total} 个用例"
        )

        for idx, tc_id in enumerate(test_case_ids, start=1):
            try:
                await arq_task_management_service.update_task_progress(
                    batch_id,
                    TaskStatus.RUNNING,
                    int((idx - 1) / total * 100),
                    f"执行第 {idx}/{total} 个用例..."
                )

                result = await gateway_execution_service.execute_gateway_test(
                    test_case_id=tc_id,
                    gateway_server_id=gateway_server_id,
                    wait_time=wait_time
                )
                results.append({
                    "test_case_id": tc_id,
                    "success": bool(result.get("success")),
                    "error": result.get("error"),
                    "summary": result.get("summary"),
                    "sql_comparison": result.get("sql_comparison")
                })
                if result.get("success"):
                    success_count += 1
            except Exception as case_e:
                logger.error(f"批量执行单例失败: {case_e}")
                results.append({
                    "test_case_id": tc_id,
                    "success": False,
                    "error": str(case_e)
                })

            # 更新进度
            await arq_task_management_service.update_task_progress(
                batch_id,
                TaskStatus.RUNNING,
                int(idx / total * 100),
                f"已完成 {idx}/{total} 个用例"
            )

        duration = perf_counter() - start
        summary = {
            "total_cases": total,
            "success_cases": success_count,
            "failed_cases": total - success_count,
            "success_rate": (success_count / total * 100.0) if total else 0.0,
            "duration": duration,
            "gateway_server_id": gateway_server_id
        }

        final_result = {
            "task_id": batch_id,
            "results": results,
            "summary": summary,
            "message": f"批量执行完成，成功 {success_count} 个，失败 {total - success_count} 个，成功率 {summary['success_rate']:.1f}%"
        }

        await arq_task_management_service.update_task_result(
            batch_id, TaskStatus.COMPLETED, 100, final_result["message"], final_result
        )
    except Exception as e:
        logger.error(f"网关批量执行任务失败: {e}")
        fail_result = {
            "task_id": batch_id,
            "results": results,
            "message": f"批量执行失败: {e}"
        }
        await arq_task_management_service.update_task_result(
            batch_id, TaskStatus.FAILED, 0, fail_result["message"], fail_result
        )


@router.post("/test-kafka-connection")
async def test_kafka_connection(gateway_server_id: int):
    """
    测试与指定网关服务器的Kafka连接
    
    Args:
        gateway_server_id: 网关服务器ID
    
    Returns:
        连接测试结果
    """
    try:
        from services.gateway_server_service import gateway_server_service
        
        try:
            from confluent_kafka import Consumer
        except ImportError:
            raise HTTPException(status_code=500, detail="confluent-kafka库未安装，无法测试Kafka连接")
        
        # 获取网关服务器信息
        gateway_server = await gateway_server_service.get_gateway(gateway_server_id)
        if not gateway_server:
            raise HTTPException(status_code=404, detail=f"网关服务器不存在: {gateway_server_id}")
        
        if not gateway_server.kafka_enabled:
            raise HTTPException(status_code=400, detail=f"网关服务器 {gateway_server.name} 未启用Kafka功能")
        
        # 测试Kafka连接
        consumer_config = {
            'bootstrap.servers': f"{gateway_server.kafka_host}:{gateway_server.kafka_port}",
            'group.id': f'test_connection_{gateway_server_id}',
            'auto.offset.reset': 'latest',
            'enable.auto.commit': False,
        }
        
        consumer = Consumer(consumer_config)
        
        # 尝试获取主题元数据信息
        import time
        cluster_metadata = consumer.list_topics(gateway_server.kafka_topic, timeout=5)
        topic_metadata = cluster_metadata.topics.get(gateway_server.kafka_topic)
        
        partitions = list(topic_metadata.partitions.keys()) if topic_metadata else []
        consumer.close()
        
        return {
            "success": True,
            "message": "Kafka连接测试成功",
            "kafka_config": {
                "host": gateway_server.kafka_host,
                "port": gateway_server.kafka_port,
                "topic": gateway_server.kafka_topic,
                "partitions": partitions
            }
        }
        
    except Exception as e:
        logger.error(f"Kafka连接测试失败: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "message": "Kafka连接测试失败"
        }

"""
执行日志API
"""
from fastapi import APIRouter, HTTPException, Query, Depends
from typing import Optional, List
from datetime import datetime, timedelta
import logging

from models.execution_log import (
    ExecutionLogQuery, ExecutionLogResponse, ExecutionLogStats,
    LogLevel, LogCategory
)
from services.execution_log_service import execution_log_service

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/execution-logs", tags=["execution-logs"])


@router.get("/", response_model=dict)
async def get_execution_logs(
    task_id: Optional[str] = Query(None, description="任务ID"),
    test_case_id: Optional[str] = Query(None, description="测试用例ID"),
    execution_id: Optional[str] = Query(None, description="执行ID"),
    database_config_id: Optional[int] = Query(None, description="数据库配置ID"),
    level: Optional[LogLevel] = Query(None, description="日志级别"),
    category: Optional[LogCategory] = Query(None, description="日志分类"),
    database_type: Optional[str] = Query(None, description="数据库类型"),
    keyword: Optional[str] = Query(None, description="关键词搜索"),
    hours: Optional[int] = Query(24, description="查询最近N小时的日志"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(50, ge=1, le=200, description="每页大小")
):
    """获取执行日志列表"""
    try:
        # 构建查询条件
        start_time = datetime.now() - timedelta(hours=hours) if hours else None
        
        query = ExecutionLogQuery(
            task_id=task_id,
            test_case_id=test_case_id,
            execution_id=execution_id,
            database_config_id=database_config_id,
            level=level,
            category=category,
            database_type=database_type,
            keyword=keyword,
            start_time=start_time,
            page=page,
            page_size=page_size
        )
        
        result = await execution_log_service.get_logs(query)
        return result
        
    except Exception as e:
        logger.error(f"Failed to get execution logs: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/stats")
async def get_execution_log_stats(
    task_id: Optional[str] = Query(None, description="任务ID"),
    hours: int = Query(24, ge=1, le=168, description="统计最近N小时的日志")
):
    """获取执行日志统计"""
    try:
        stats = await execution_log_service.get_stats(task_id=task_id, hours=hours)
        return stats
        
    except Exception as e:
        logger.error(f"Failed to get execution log stats: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/categories")
async def get_log_categories():
    """获取日志分类列表"""
    return {
        "categories": [
            {"value": category.value, "label": category.value}
            for category in LogCategory
        ]
    }


@router.get("/levels")
async def get_log_levels():
    """获取日志级别列表"""
    return {
        "levels": [
            {"value": level.value, "label": level.value}
            for level in LogLevel
        ]
    }


@router.delete("/cleanup")
async def cleanup_old_logs(
    days: int = Query(30, ge=1, le=365, description="清理N天前的日志")
):
    """清理旧日志"""
    try:
        deleted_count = await execution_log_service.cleanup_old_logs(days=days)
        return {
            "success": True,
            "message": f"成功清理 {deleted_count} 条旧日志",
            "deleted_count": deleted_count
        }
        
    except Exception as e:
        logger.error(f"Failed to cleanup old logs: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/task/{task_id}")
async def get_task_logs(
    task_id: str,
    level: Optional[LogLevel] = Query(None, description="日志级别"),
    category: Optional[LogCategory] = Query(None, description="日志分类"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(100, ge=1, le=500, description="每页大小")
):
    """获取特定任务的日志"""
    try:
        query = ExecutionLogQuery(
            task_id=task_id,
            level=level,
            category=category,
            page=page,
            page_size=page_size
        )
        
        result = await execution_log_service.get_logs(query)
        return result
        
    except Exception as e:
        logger.error(f"Failed to get task logs: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/errors/recent")
async def get_recent_errors(
    hours: int = Query(24, ge=1, le=168, description="查询最近N小时的错误"),
    limit: int = Query(20, ge=1, le=100, description="返回数量限制")
):
    """获取最近的错误日志"""
    try:
        start_time = datetime.now() - timedelta(hours=hours)
        
        query = ExecutionLogQuery(
            level=LogLevel.ERROR,
            start_time=start_time,
            page=1,
            page_size=limit
        )
        
        result = await execution_log_service.get_logs(query)
        return {
            "errors": result["logs"],
            "total": result["total"]
        }
        
    except Exception as e:
        logger.error(f"Failed to get recent errors: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/capture-issues")
async def get_capture_issues(
    hours: int = Query(24, ge=1, le=168, description="查询最近N小时的抓包问题"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(50, ge=1, le=200, description="每页大小")
):
    """获取抓包相关的问题日志"""
    try:
        start_time = datetime.now() - timedelta(hours=hours)
        
        query = ExecutionLogQuery(
            category=LogCategory.CAPTURE,
            level=LogLevel.ERROR,
            start_time=start_time,
            page=page,
            page_size=page_size
        )
        
        result = await execution_log_service.get_logs(query)
        return result
        
    except Exception as e:
        logger.error(f"Failed to get capture issues: {e}")
        raise HTTPException(status_code=500, detail=str(e))

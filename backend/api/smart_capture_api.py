"""
AI智能抓包API - 支持MySQL、PostgreSQL、MongoDB、Oracle、GaussDB的智能网卡检测和抓包
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel
from typing import Optional, Dict, Any, List
import logging

from services.mysql_local_packet_capture_service import MySQLLocalPacketCaptureService
from services.postgres_local_packet_capture_service import PostgresLocalPacketCaptureService
from services.mongo_local_packet_capture_service import MongoLocalPacketCaptureService
from services.oracle_packet_capture_service import OraclePacketCaptureService
from services.gaussdb_packet_capture_service import gaussdb_packet_service
from services.network_detection_service import NetworkDetectionService
from services.server_config_service import ServerConfigService
from services.tcpdump_installer_service import TcpdumpInstallerService

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/smart-capture", tags=["Smart Capture"])

# 服务实例
mysql_capture_service = MySQLLocalPacketCaptureService()
postgres_capture_service = PostgresLocalPacketCaptureService()
mongo_capture_service = MongoLocalPacketCaptureService()
oracle_capture_service = OraclePacketCaptureService()
network_detection_service = NetworkDetectionService()
server_config_service = ServerConfigService()
tcpdump_installer_service = TcpdumpInstallerService()

class SmartCaptureRequest(BaseModel):
    """智能抓包请求"""
    database_type: str  # mysql, postgresql, mongodb, oracle, gaussdb
    database_host: str
    database_port: int
    server_config_id: Optional[int] = None

class SmartCaptureResponse(BaseModel):
    """智能抓包响应"""
    success: bool
    message: str
    capture_file: Optional[str] = None
    capture_strategy: Optional[Dict[str, Any]] = None
    retry_count: Optional[int] = None
    failed_strategies: Optional[List[Dict[str, Any]]] = None

class CaptureStatusResponse(BaseModel):
    """抓包状态响应"""
    database_type: str
    is_capturing: bool
    current_file: Optional[str] = None
    capture_strategy: Optional[Dict[str, Any]] = None
    retry_count: Optional[int] = None

@router.post("/start", response_model=SmartCaptureResponse)
async def start_smart_capture(request: SmartCaptureRequest):
    """启动AI智能抓包 - 自动检测最佳网络接口和抓包策略"""
    try:
        logger.info(f"Starting smart capture for {request.database_type} at {request.database_host}:{request.database_port}")
        
        # 根据数据库类型选择相应的服务
        if request.database_type.lower() == 'mysql':
            capture_file = await mysql_capture_service.start_capture(
                request.database_host,
                request.database_port,
                request.server_config_id
            )
            status = mysql_capture_service.get_capture_status()
            
        elif request.database_type.lower() == 'postgresql':
            capture_file = await postgres_capture_service.start_smart_capture(
                request.database_host, 
                request.database_port, 
                request.server_config_id
            )
            status = postgres_capture_service.get_capture_status()
            
        elif request.database_type.lower() == 'mongodb':
            capture_file = await mongo_capture_service.start_smart_capture(
                request.database_host,
                request.database_port,
                request.server_config_id
            )
            status = mongo_capture_service.get_capture_status()

        elif request.database_type.lower() == 'oracle':
            capture_file = await oracle_capture_service.start_smart_capture(
                target_host=request.database_host,
                target_port=request.database_port,
                server_config_id=request.server_config_id
            )
            status = oracle_capture_service.get_capture_status()

        elif request.database_type.lower() == 'gaussdb':
            capture_file = await gaussdb_packet_service.start_capture(
                target_host=request.database_host,
                target_port=request.database_port,
                server_config_id=request.server_config_id
            )
            status = gaussdb_packet_service.get_capture_status()

        else:
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported database type: {request.database_type}"
            )
        
        return SmartCaptureResponse(
            success=True,
            message=f"Smart capture started successfully for {request.database_type}",
            capture_file=capture_file,
            capture_strategy=status.get('current_strategy'),
            retry_count=status.get('retry_count'),
            failed_strategies=status.get('failed_strategies')
        )
        
    except Exception as e:
        logger.error(f"Failed to start smart capture: {str(e)}")
        return SmartCaptureResponse(
            success=False,
            message=f"Failed to start smart capture: {str(e)}"
        )

@router.post("/stop/{database_type}", response_model=SmartCaptureResponse)
async def stop_smart_capture(database_type: str):
    """停止智能抓包"""
    try:
        logger.info(f"Stopping smart capture for {database_type}")
        
        # 根据数据库类型选择相应的服务
        if database_type.lower() == 'mysql':
            capture_file = await mysql_capture_service.stop_capture()
        elif database_type.lower() == 'postgresql':
            capture_file = await postgres_capture_service.stop_capture()
        elif database_type.lower() == 'mongodb':
            capture_file = await mongo_capture_service.stop_capture()
        elif database_type.lower() == 'oracle':
            capture_file = await oracle_capture_service.stop_capture()
        elif database_type.lower() == 'gaussdb':
            capture_file = await gaussdb_packet_service.stop_capture()
        else:
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported database type: {database_type}"
            )
        
        return SmartCaptureResponse(
            success=True,
            message=f"Smart capture stopped successfully for {database_type}",
            capture_file=capture_file
        )
        
    except Exception as e:
        logger.error(f"Failed to stop smart capture: {str(e)}")
        return SmartCaptureResponse(
            success=False,
            message=f"Failed to stop smart capture: {str(e)}"
        )

@router.get("/status/{database_type}", response_model=CaptureStatusResponse)
async def get_capture_status(database_type: str):
    """获取抓包状态"""
    try:
        # 根据数据库类型选择相应的服务
        if database_type.lower() == 'mysql':
            status = mysql_capture_service.get_capture_status()
        elif database_type.lower() == 'postgresql':
            status = postgres_capture_service.get_capture_status()
        elif database_type.lower() == 'mongodb':
            status = mongo_capture_service.get_capture_status()
        elif database_type.lower() == 'oracle':
            status = oracle_capture_service.get_capture_status()
        elif database_type.lower() == 'gaussdb':
            status = gaussdb_packet_service.get_capture_status()
        else:
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported database type: {database_type}"
            )
        
        return CaptureStatusResponse(
            database_type=database_type,
            is_capturing=status.get('is_capturing', False),
            current_file=status.get('current_file'),
            capture_strategy=status.get('current_strategy'),
            retry_count=status.get('retry_count')
        )
        
    except Exception as e:
        logger.error(f"Failed to get capture status: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/strategy/{database_type}")
async def generate_capture_strategy(
    database_type: str,
    database_host: str,
    database_port: int,
    server_config_id: Optional[int] = None
):
    """生成抓包策略 - 不启动抓包，仅生成策略"""
    try:
        # 获取服务器配置
        if server_config_id:
            server_config = await server_config_service.get_config(server_config_id)
        else:
            configs = await server_config_service.get_all_configs()
            server_config = configs[0] if configs else None
        
        if not server_config:
            raise HTTPException(status_code=404, detail="Server configuration not found")
        
        # 检测数据库部署
        deployment = await network_detection_service.detect_database_deployment(
            server_config, database_type, database_host, database_port
        )
        
        # 生成抓包策略
        strategy = await network_detection_service.generate_capture_strategy(
            server_config, deployment
        )
        
        return {
            "success": True,
            "database_type": database_type,
            "deployment": deployment.to_dict(),
            "strategy": strategy.to_dict()
        }
        
    except Exception as e:
        logger.error(f"Failed to generate capture strategy: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/interfaces/{server_config_id}")
async def get_network_interfaces(server_config_id: int):
    """获取网络接口信息"""
    try:
        server_config = await server_config_service.get_config(server_config_id)
        if not server_config:
            raise HTTPException(status_code=404, detail="Server configuration not found")
        
        interfaces = await network_detection_service.get_network_interfaces(server_config)
        
        return {
            "success": True,
            "server_config_id": server_config_id,
            "interfaces": [iface.to_dict() for iface in interfaces]
        }
        
    except Exception as e:
        logger.error(f"Failed to get network interfaces: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/tcpdump/info/{server_config_id}")
async def get_tcpdump_info(server_config_id: int):
    """获取服务器上的tcpdump信息"""
    try:
        server_config = await server_config_service.get_config(server_config_id)
        if not server_config:
            raise HTTPException(status_code=404, detail="Server configuration not found")

        tcpdump_info = await tcpdump_installer_service.get_tcpdump_info(server_config)

        return {
            "success": True,
            "server_config_id": server_config_id,
            "server_host": server_config.host,
            "tcpdump_info": tcpdump_info
        }

    except Exception as e:
        logger.error(f"Failed to get tcpdump info: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/tcpdump/install/{server_config_id}")
async def install_tcpdump(server_config_id: int):
    """在指定服务器上安装tcpdump"""
    try:
        server_config = await server_config_service.get_config(server_config_id)
        if not server_config:
            raise HTTPException(status_code=404, detail="Server configuration not found")

        success = await tcpdump_installer_service.ensure_tcpdump_available(server_config)

        if success:
            # 获取安装后的信息
            tcpdump_info = await tcpdump_installer_service.get_tcpdump_info(server_config)

            return {
                "success": True,
                "message": "tcpdump installed successfully",
                "server_config_id": server_config_id,
                "server_host": server_config.host,
                "tcpdump_info": tcpdump_info
            }
        else:
            return {
                "success": False,
                "message": "Failed to install tcpdump",
                "server_config_id": server_config_id,
                "server_host": server_config.host
            }

    except Exception as e:
        logger.error(f"Failed to install tcpdump: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "services": {
            "mysql_capture": mysql_capture_service.is_capturing_active(),
            "postgres_capture": postgres_capture_service.is_capturing,
            "mongo_capture": mongo_capture_service.is_capturing_active(),
            "oracle_capture": oracle_capture_service.is_capturing_active()
        }
    }

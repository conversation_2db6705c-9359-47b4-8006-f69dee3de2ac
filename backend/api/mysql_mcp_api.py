from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Optional, Dict, Any
import logging
from services.mysql_mcp_agent_service import mysql_mcp_agent_service
from services.mysql_async_capture_service import mysql_async_capture_service
from services.mcp_service_manager import mcp_service_manager
from utils.exception_handler import log_exceptions, create_error_response

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/mysql-mcp", tags=["mysql-mcp"])

# Pydantic模型
class NaturalLanguageQueryRequest(BaseModel):
    query: str
    capture_packets: bool = True
    config_id: Optional[int] = None
    database_name: Optional[str] = None

class SQLExecutionRequest(BaseModel):
    sql_query: str
    capture_packets: bool = True
    config_id: Optional[int] = None

class SQLPreviewRequest(BaseModel):
    query: str
    database_name: Optional[str] = None

class MCPToolCallRequest(BaseModel):
    server_name: str
    tool_name: str
    arguments: Dict[str, Any]

@router.post("/natural-query", response_model=Dict[str, Any])
@log_exceptions(reraise=False)
async def process_natural_language_query(request: NaturalLanguageQueryRequest):
    """处理自然语言查询并可选择进行数据包捕获"""
    try:
        if request.capture_packets:
            # 使用异步抓包服务
            result = await mysql_async_capture_service.process_natural_language_with_capture(
                request.query,
                request.config_id,
                request.database_name
            )
        else:
            # 使用MCP代理服务（不抓包）
            result = await mysql_mcp_agent_service.process_natural_language_query(
                request.query,
                False,
                request.database_name
            )

        return result

    except Exception as e:
        error_response = create_error_response(e, "处理自然语言查询")
        raise HTTPException(status_code=500, detail=error_response)

@router.post("/sql-preview", response_model=Dict[str, Any])
async def generate_sql_preview(request: SQLPreviewRequest):
    """生成SQL预览（不执行，不抓包）"""
    try:
        result = await mysql_async_capture_service.generate_sql_preview(
            request.query,
            request.database_name
        )
        return result
        
    except Exception as e:
        logger.error(f"Failed to generate SQL preview: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/execute-sql", response_model=Dict[str, Any])
async def execute_sql_query(request: SQLExecutionRequest):
    """执行SQL查询并可选择进行数据包捕获"""
    try:
        if request.capture_packets:
            # 使用异步抓包服务
            result = await mysql_async_capture_service.execute_sql_with_async_capture(
                request.sql_query,
                request.config_id
            )
        else:
            # 使用MCP代理服务（不抓包）
            result = await mysql_mcp_agent_service.execute_sql_with_capture(
                request.sql_query,
                False
            )
        
        return result
        
    except Exception as e:
        logger.error(f"Failed to execute SQL query: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/schema", response_model=Dict[str, Any])
async def get_database_schema(database: Optional[str] = None, table: Optional[str] = None):
    """获取数据库架构信息"""
    try:
        result = await mysql_mcp_agent_service.get_database_schema(database, table)
        return result
        
    except Exception as e:
        logger.error(f"Failed to get database schema: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/mcp-tool-call", response_model=Dict[str, Any])
async def call_mcp_tool(request: MCPToolCallRequest):
    """直接调用MCP工具"""
    try:
        result = await mcp_service_manager.call_tool(
            request.server_name,
            request.tool_name,
            request.arguments
        )
        return {
            "success": True,
            "server_name": request.server_name,
            "tool_name": request.tool_name,
            "result": result
        }
        
    except Exception as e:
        logger.error(f"Failed to call MCP tool: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/mcp-servers", response_model=Dict[str, Any])
async def get_mcp_servers_status():
    """获取所有MCP服务器状态"""
    try:
        statuses = await mcp_service_manager.get_all_servers_status()
        return {
            "success": True,
            "servers": statuses
        }
        
    except Exception as e:
        logger.error(f"Failed to get MCP servers status: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/mcp-servers/{server_name}/start", response_model=Dict[str, Any])
async def start_mcp_server(server_name: str):
    """启动指定的MCP服务器"""
    try:
        success = await mcp_service_manager.start_server(server_name)
        return {
            "success": success,
            "server_name": server_name,
            "message": f"Server {server_name} {'started' if success else 'failed to start'}"
        }
        
    except Exception as e:
        logger.error(f"Failed to start MCP server {server_name}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/mcp-servers/{server_name}/stop", response_model=Dict[str, Any])
async def stop_mcp_server(server_name: str):
    """停止指定的MCP服务器"""
    try:
        success = await mcp_service_manager.stop_server(server_name)
        return {
            "success": success,
            "server_name": server_name,
            "message": f"Server {server_name} {'stopped' if success else 'failed to stop'}"
        }
        
    except Exception as e:
        logger.error(f"Failed to stop MCP server {server_name}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/mcp-servers/{server_name}/tools", response_model=Dict[str, Any])
async def get_mcp_server_tools(server_name: str):
    """获取指定MCP服务器的工具列表"""
    try:
        tools = await mcp_service_manager.list_tools(server_name)
        return {
            "success": True,
            "server_name": server_name,
            "tools": tools
        }
        
    except Exception as e:
        logger.error(f"Failed to get tools for MCP server {server_name}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/mcp-servers/{server_name}/status", response_model=Dict[str, Any])
async def get_mcp_server_status(server_name: str):
    """获取指定MCP服务器状态"""
    try:
        status = await mcp_service_manager.get_server_status(server_name)
        return {
            "success": True,
            "status": status
        }
        
    except Exception as e:
        logger.error(f"Failed to get status for MCP server {server_name}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# 智能查询端点 - 主要功能入口
@router.post("/smart-query", response_model=Dict[str, Any])
async def smart_query_with_capture(request: NaturalLanguageQueryRequest):
    """智能查询 - 根据自然语言生成SQL并执行，同时进行数据包捕获
    
    这是主要的功能入口，当用户输入"帮我写一个库，表，用户的语句"并点击异步抓包时调用
    """
    try:
        logger.info(f"Smart query with capture: {request.query}")
        
        # 使用异步抓包服务处理完整流程
        result = await mysql_async_capture_service.process_natural_language_with_capture(
            request.query,
            request.config_id,
            request.database_name
        )
        
        # 增强返回信息
        if result["success"]:
            result["message"] = "查询处理完成，已生成SQL并执行，数据包捕获完成"
            result["workflow"] = {
                "step1": "分析自然语言查询",
                "step2": "获取数据库表结构",
                "step3": "生成对应SQL语句",
                "step4": "启动数据包捕获",
                "step5": "执行SQL查询",
                "step6": "停止数据包捕获"
            }
        else:
            result["message"] = f"查询处理失败: {result.get('error', 'Unknown error')}"
        
        return result
        
    except Exception as e:
        logger.error(f"Failed to process smart query: {e}")
        raise HTTPException(status_code=500, detail=str(e))

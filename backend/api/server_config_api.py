"""
服务器配置管理API
"""

import logging
import json
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.server_config import ServerConfig
from services.server_config_service import ServerConfigService
from services.network_detection_service import NetworkDetectionService
from services.docker_environment_service import DockerEnvironmentService
from services.database_config_service import DatabaseConfigService, DatabaseConfigModel
from services.arq_task_management_service import arq_task_management_service as task_management_service
from services.docker_image_service import docker_image_service

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/servers", tags=["servers"])

# 服务实例
server_config_service = ServerConfigService()
network_detection_service = NetworkDetectionService()
docker_environment_service = DockerEnvironmentService()
database_config_service = DatabaseConfigService()

# Pydantic模型
class ServerConfigRequest(BaseModel):
    name: str
    host: str
    port: int = 22
    username: str = "root"
    password: str
    description: str = ""
    is_default: bool = False

class ServerConfigResponse(BaseModel):
    id: int
    name: str
    host: str
    port: int
    username: str
    description: str
    is_active: bool
    is_default: bool
    created_at: Optional[str]
    updated_at: Optional[str]

class NetworkInterfaceResponse(BaseModel):
    name: str
    ip_address: str
    is_up: bool
    interface_type: str

class DatabaseDeploymentResponse(BaseModel):
    database_type: str
    deployment_type: str
    host: str
    port: int
    container_name: Optional[str]
    container_ip: Optional[str]
    process_info: Optional[Dict[str, Any]]

class CaptureStrategyResponse(BaseModel):
    interface: str
    filter_expression: str
    deployment_info: DatabaseDeploymentResponse
    confidence: float

class DockerEnvironmentRequest(BaseModel):
    ai_prompt: str
    database_type: str  # mysql, postgresql, mongodb

class DockerEnvironmentResponse(BaseModel):
    success: bool
    container_name: str
    container_id: str
    port: int
    username: str
    password: str
    database_name: str
    docker_command: str
    message: str
    database_config_id: Optional[int] = None  # 自动保存的数据库配置ID
    database_config_name: Optional[str] = None  # 自动保存的数据库配置名称

class BatchDockerBuildRequest(BaseModel):
    build_requests: List[DockerEnvironmentRequest]

class BatchDockerBuildResponse(BaseModel):
    task_id: str
    message: str
    total_requests: int

class DockerImageResponse(BaseModel):
    id: int
    server_config_id: int
    image_id: str
    repository: str
    tag: str
    size: int
    created_time: Optional[str]
    last_updated: Optional[str]
    is_available: bool
    image_info: Optional[Dict[str, Any]]
    created_at: Optional[str]
    updated_at: Optional[str]

class DockerImageUpdateResponse(BaseModel):
    success: bool
    total_images: int
    inserted_count: int
    message: str
    error: Optional[str] = None

class DockerEnvironmentWithImageRequest(BaseModel):
    image_repository: str
    image_tag: str
    database_type: str
    ai_prompt: str = ""

class DockerImageBuildResponse(BaseModel):
    task_id: str
    message: str

@router.get("/", response_model=List[ServerConfigResponse])
async def list_server_configs():
    """获取所有服务器配置"""
    try:
        configs = await server_config_service.list_configs()
        return [
            ServerConfigResponse(
                id=config.id,
                name=config.name,
                host=config.host,
                port=config.port,
                username=config.username,
                description=config.description,
                is_active=config.is_active,
                is_default=config.is_default,
                created_at=config.created_at,
                updated_at=config.updated_at
            )
            for config in configs
        ]
    except Exception as e:
        logger.error(f"Failed to list server configs: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/", response_model=ServerConfigResponse)
async def create_server_config(request: ServerConfigRequest):
    """创建服务器配置"""
    try:
        config = ServerConfig(
            name=request.name,
            host=request.host,
            port=request.port,
            username=request.username,
            password=request.password,
            description=request.description,
            is_default=request.is_default
        )

        config_id = await server_config_service.create_config(config)

        # 获取创建后的完整配置信息
        created_config = await server_config_service.get_config(config_id)

        return ServerConfigResponse(
            id=created_config.id,
            name=created_config.name,
            host=created_config.host,
            port=created_config.port,
            username=created_config.username,
            description=created_config.description,
            is_active=created_config.is_active,
            is_default=created_config.is_default,
            created_at=created_config.created_at,
            updated_at=created_config.updated_at
        )
    except Exception as e:
        logger.error(f"Failed to create server config: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{config_id}", response_model=ServerConfigResponse)
async def get_server_config(config_id: int):
    """获取指定服务器配置"""
    try:
        config = await server_config_service.get_config(config_id)
        if not config:
            raise HTTPException(status_code=404, detail="Server config not found")
        
        return ServerConfigResponse(
            id=config.id,
            name=config.name,
            host=config.host,
            port=config.port,
            username=config.username,
            description=config.description,
            is_active=config.is_active,
            is_default=config.is_default,
            created_at=config.created_at,
            updated_at=config.updated_at
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get server config: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/{config_id}", response_model=ServerConfigResponse)
async def update_server_config(config_id: int, request: ServerConfigRequest):
    """更新服务器配置"""
    try:
        config = await server_config_service.get_config(config_id)
        if not config:
            raise HTTPException(status_code=404, detail="Server config not found")
        
        config.name = request.name
        config.host = request.host
        config.port = request.port
        config.username = request.username
        config.password = request.password
        config.description = request.description
        config.is_default = request.is_default
        
        await server_config_service.update_config(config)

        # 重新获取更新后的配置
        updated_config = await server_config_service.get_config(config_id)

        return ServerConfigResponse(
            id=updated_config.id,
            name=updated_config.name,
            host=updated_config.host,
            port=updated_config.port,
            username=updated_config.username,
            description=updated_config.description,
            is_active=updated_config.is_active,
            is_default=updated_config.is_default,
            created_at=updated_config.created_at,
            updated_at=updated_config.updated_at
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update server config: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/{config_id}")
async def delete_server_config(config_id: int):
    """删除服务器配置"""
    try:
        success = await server_config_service.delete_config(config_id)
        if not success:
            raise HTTPException(status_code=404, detail="Server config not found")
        
        return {"message": "Server config deleted successfully"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete server config: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{config_id}/interfaces", response_model=List[NetworkInterfaceResponse])
async def get_network_interfaces(config_id: int):
    """获取服务器网络接口信息"""
    try:
        config = await server_config_service.get_config(config_id)
        if not config:
            raise HTTPException(status_code=404, detail="Server config not found")
        
        interfaces = await network_detection_service.get_network_interfaces(config)
        
        return [
            NetworkInterfaceResponse(
                name=interface.name,
                ip_address=interface.ip_address,
                is_up=interface.is_up,
                interface_type=interface.interface_type
            )
            for interface in interfaces
        ]
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get network interfaces: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/{config_id}/detect-deployment", response_model=DatabaseDeploymentResponse)
async def detect_database_deployment(
    config_id: int,
    database_type: str,
    database_host: str,
    database_port: int
):
    """检测数据库部署方式"""
    try:
        config = await server_config_service.get_config(config_id)
        if not config:
            raise HTTPException(status_code=404, detail="Server config not found")
        
        deployment = await network_detection_service.detect_database_deployment(
            config, database_type, database_host, database_port
        )
        
        return DatabaseDeploymentResponse(
            database_type=deployment.database_type,
            deployment_type=deployment.deployment_type,
            host=deployment.host,
            port=deployment.port,
            container_name=deployment.container_name,
            container_ip=deployment.container_ip,
            process_info=deployment.process_info
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to detect database deployment: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/{config_id}/capture-strategy", response_model=CaptureStrategyResponse)
async def generate_capture_strategy(
    config_id: int,
    database_type: str,
    database_host: str,
    database_port: int
):
    """生成抓包策略"""
    try:
        config = await server_config_service.get_config(config_id)
        if not config:
            raise HTTPException(status_code=404, detail="Server config not found")
        
        # 先检测数据库部署
        deployment = await network_detection_service.detect_database_deployment(
            config, database_type, database_host, database_port
        )
        
        # 生成抓包策略
        strategy = await network_detection_service.generate_capture_strategy(config, deployment)
        
        return CaptureStrategyResponse(
            interface=strategy.interface,
            filter_expression=strategy.filter_expression,
            deployment_info=DatabaseDeploymentResponse(
                database_type=strategy.deployment_info.database_type,
                deployment_type=strategy.deployment_info.deployment_type,
                host=strategy.deployment_info.host,
                port=strategy.deployment_info.port,
                container_name=strategy.deployment_info.container_name,
                container_ip=strategy.deployment_info.container_ip,
                process_info=strategy.deployment_info.process_info
            ),
            confidence=strategy.confidence
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to generate capture strategy: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/{config_id}/build-docker-environment", response_model=DockerEnvironmentResponse)
async def build_docker_environment(
    config_id: int,
    request: DockerEnvironmentRequest
):
    """使用AI构建Docker数据库环境并自动保存到数据库管理"""
    try:
        config = await server_config_service.get_config(config_id)
        if not config:
            raise HTTPException(status_code=404, detail="Server config not found")

        # 构建Docker环境
        result = await docker_environment_service.build_database_environment(
            config, request.ai_prompt, request.database_type
        )

        database_config_id = None
        database_config_name = None

        # 如果Docker环境构建成功，自动保存数据库配置
        if result['success'] and result['container_name'] and result['port']:
            try:
                # 生成数据库配置名称
                database_config_name = f"Docker-{result['container_name']}"

                # 创建数据库配置对象
                db_config = DatabaseConfigModel(
                    name=database_config_name,
                    host=config.host,  # 使用服务器IP
                    port=result['port'],
                    user=result['username'] or 'root',
                    password=result['password'],
                    database_name=result['database_name'] or request.database_type,
                    database_type=request.database_type,
                    description=f"AI自动构建的{request.database_type}环境 - {request.ai_prompt[:100]}",
                    is_default=False,
                    is_active=True
                )

                # 保存数据库配置
                database_config_id = await database_config_service.add_config(db_config)

                logger.info(f"Auto-saved database config: {database_config_name} (ID: {database_config_id})")

                # 更新返回消息
                result['message'] += f" 数据库配置已自动保存为: {database_config_name}"

            except Exception as save_error:
                logger.warning(f"Failed to auto-save database config: {str(save_error)}")
                # 不影响主要功能，只记录警告

        return DockerEnvironmentResponse(
            success=result['success'],
            container_name=result['container_name'],
            container_id=result['container_id'],
            port=result['port'],
            username=result['username'],
            password=result['password'],
            database_name=result['database_name'],
            docker_command=result['docker_command'],
            message=result['message'],
            database_config_id=database_config_id,
            database_config_name=database_config_name
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to build Docker environment: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/{config_id}/build-docker-environments-async", response_model=BatchDockerBuildResponse)
async def build_docker_environments_async(
    config_id: int,
    request: BatchDockerBuildRequest
):
    """异步批量构建Docker数据库环境"""
    try:
        config = await server_config_service.get_config(config_id)
        if not config:
            raise HTTPException(status_code=404, detail="Server config not found")

        if not request.build_requests:
            raise HTTPException(status_code=400, detail="No build requests provided")

        # 转换请求格式
        build_requests = []
        for req in request.build_requests:
            build_requests.append({
                "ai_prompt": req.ai_prompt,
                "database_type": req.database_type
            })

        # 提交异步任务
        task_id = await task_management_service.submit_docker_build_task(
            server_config_id=config_id,
            build_requests=build_requests
        )

        return BatchDockerBuildResponse(
            task_id=task_id,
            message=f"Docker批量构建任务已提交，共{len(build_requests)}个构建请求",
            total_requests=len(build_requests)
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to submit batch Docker build task: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{config_id}/docker-images", response_model=List[DockerImageResponse])
async def get_docker_images(config_id: int, search: Optional[str] = None):
    """获取服务器的Docker镜像列表"""
    try:
        config = await server_config_service.get_config(config_id)
        if not config:
            raise HTTPException(status_code=404, detail="Server config not found")

        # 从数据库获取存储的镜像信息
        images = await docker_image_service.get_stored_docker_images(config_id, search)

        return [
            DockerImageResponse(
                id=img['id'],
                server_config_id=img['server_config_id'],
                image_id=img['image_id'],
                repository=img['repository'],
                tag=img['tag'],
                size=img['size'],
                created_time=img['created_time'].isoformat() if img['created_time'] else None,
                last_updated=img['last_updated'].isoformat() if img['last_updated'] else None,
                is_available=img['is_available'],
                image_info=json.loads(img['image_info']) if img['image_info'] else None,
                created_at=img['created_at'].isoformat() if img['created_at'] else None,
                updated_at=img['updated_at'].isoformat() if img['updated_at'] else None
            )
            for img in images
        ]

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get Docker images: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/{config_id}/docker-images/update", response_model=DockerImageUpdateResponse)
async def update_docker_images(config_id: int):
    """更新服务器的Docker镜像信息"""
    try:
        config = await server_config_service.get_config(config_id)
        if not config:
            raise HTTPException(status_code=404, detail="Server config not found")

        # 更新镜像信息
        result = await docker_image_service.update_server_docker_images(config_id, config)

        return DockerImageUpdateResponse(**result)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update Docker images: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{config_id}/docker-images/database", response_model=List[DockerImageResponse])
async def get_database_docker_images(config_id: int):
    """获取数据库相关的Docker镜像"""
    try:
        config = await server_config_service.get_config(config_id)
        if not config:
            raise HTTPException(status_code=404, detail="Server config not found")

        # 获取数据库相关镜像
        images = await docker_image_service.get_database_images(config_id)

        return [
            DockerImageResponse(
                id=img['id'],
                server_config_id=img['server_config_id'],
                image_id=img['image_id'],
                repository=img['repository'],
                tag=img['tag'],
                size=img['size'],
                created_time=img['created_time'].isoformat() if img['created_time'] else None,
                last_updated=img['last_updated'].isoformat() if img['last_updated'] else None,
                is_available=img['is_available'],
                image_info=json.loads(img['image_info']) if img['image_info'] else None,
                created_at=img['created_at'].isoformat() if img['created_at'] else None,
                updated_at=img['updated_at'].isoformat() if img['updated_at'] else None
            )
            for img in images
        ]

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get database Docker images: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/{config_id}/build-docker-environment-with-image", response_model=DockerEnvironmentResponse)
async def build_docker_environment_with_image(
    config_id: int,
    request: DockerEnvironmentWithImageRequest
):
    """使用指定镜像构建Docker数据库环境"""
    try:
        config = await server_config_service.get_config(config_id)
        if not config:
            raise HTTPException(status_code=404, detail="Server config not found")

        # 使用指定镜像构建Docker环境
        result = await docker_environment_service.build_database_environment_with_image(
            config,
            request.image_repository,
            request.image_tag,
            request.database_type,
            request.ai_prompt
        )

        database_config_id = None
        database_config_name = None

        # 如果构建成功，自动保存数据库配置
        if result['success']:
            try:
                # 构造数据库配置
                db_config = DatabaseConfigModel(
                    name=f"{request.database_type}_{result['container_name']}",
                    host=config.host,
                    port=result['port'],
                    user=result['username'],
                    password=result['password'],
                    database_name=result['database_name'],
                    database_type=request.database_type,
                    description=f"Auto-created from Docker container {result['container_name']} using image {request.image_repository}:{request.image_tag}"
                )

                # 保存到数据库
                database_config_id = await database_config_service.add_config(db_config)
                saved_config = await database_config_service.get_config(database_config_id)
                database_config_name = saved_config.name

                logger.info(f"Auto-saved database config: {saved_config.name}")

            except Exception as save_error:
                logger.warning(f"Failed to auto-save database config: {str(save_error)}")

        return DockerEnvironmentResponse(
            success=result['success'],
            container_name=result['container_name'],
            container_id=result['container_id'],
            port=result['port'],
            username=result['username'],
            password=result['password'],
            database_name=result['database_name'],
            docker_command=result['docker_command'],
            message=result['message'],
            database_config_id=database_config_id,
            database_config_name=database_config_name
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to build Docker environment with image: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/{config_id}/build-docker-environment-with-image-async", response_model=DockerImageBuildResponse)
async def build_docker_environment_with_image_async(
    config_id: int,
    request: DockerEnvironmentWithImageRequest
):
    """异步使用指定镜像构建Docker数据库环境"""
    try:
        config = await server_config_service.get_config(config_id)
        if not config:
            raise HTTPException(status_code=404, detail="Server config not found")

        # 提交异步任务
        task_id = await task_management_service.submit_docker_image_build_task(
            server_config_id=config_id,
            image_repository=request.image_repository,
            image_tag=request.image_tag,
            database_type=request.database_type,
            ai_prompt=request.ai_prompt
        )

        return DockerImageBuildResponse(
            task_id=task_id,
            message=f"Docker镜像构建任务已提交！镜像: {request.image_repository}:{request.image_tag}"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to submit Docker image build task: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

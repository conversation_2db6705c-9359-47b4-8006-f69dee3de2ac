"""
GaussDB API接口
提供GaussDB查询处理、抓包控制、连接测试等功能
"""

import asyncio
import logging
import traceback
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field

from services.gaussdb_service_v2 import GaussDBServiceV2
from services.gaussdb_packet_capture_service import gaussdb_packet_service
from services.ai_service import AIService
from services.database_config_service import DatabaseConfigService
from utils.config import Config

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/gaussdb", tags=["GaussDB"])

# 请求模型
class GaussDBQueryRequest(BaseModel):
    query: str = Field(..., description="自然语言查询或SQL语句")
    capture_packets: bool = Field(default=True, description="是否启用数据包捕获")
    config_id: Optional[int] = Field(default=None, description="GaussDB配置ID")

class GaussDBConnectionTestRequest(BaseModel):
    config_id: Optional[int] = Field(default=None, description="GaussDB配置ID")

# 响应模型
class GaussDBQueryResponse(BaseModel):
    success: bool
    message: str
    sql_query: Optional[str] = None
    result: Optional[Dict[str, Any]] = None
    packet_file: Optional[str] = None
    execution_time: Optional[float] = None
    error: Optional[str] = None

class GaussDBConnectionTestResponse(BaseModel):
    success: bool
    message: str
    response_time: Optional[float] = None
    databases: Optional[List[str]] = None
    databases_count: Optional[int] = None
    error: Optional[str] = None

# 依赖注入
async def get_ai_service():
    return AIService()

async def get_db_config_service():
    return DatabaseConfigService()

@router.post("/query", response_model=GaussDBQueryResponse)
async def process_gaussdb_query(
    request: GaussDBQueryRequest,
    ai_service: AIService = Depends(get_ai_service),
    db_config_service: DatabaseConfigService = Depends(get_db_config_service)
):
    """处理GaussDB自然语言查询"""
    try:
        logger.info(f"Processing GaussDB query: {request.query}")
        
        # 1. 获取GaussDB配置
        gaussdb_config = None
        if request.config_id:
            gaussdb_config = await db_config_service.get_config(request.config_id)
            if not gaussdb_config or gaussdb_config.database_type.lower() != 'gaussdb':
                raise HTTPException(status_code=404, detail="GaussDB configuration not found")
        else:
            # 使用默认GaussDB配置
            gaussdb_config = await db_config_service.get_default_config_by_type('gaussdb')

        # 2. 如果需要抓包，启动智能抓包服务
        if request.capture_packets:
            try:

                # 获取默认服务器配置用于抓包
                from services.server_config_service import server_config_service
                default_server_config = await server_config_service.get_default_config()
                server_config_id = default_server_config.id if default_server_config else None

                # 使用本地抓包功能，传入GaussDB目标主机和端口
                if gaussdb_config:
                    packet_file = await gaussdb_packet_service.start_capture(
                        target_host=gaussdb_config.host,
                        target_port=gaussdb_config.port,
                        server_config_id=server_config_id
                    )
                else:
                    # 使用默认配置
                    packet_file = await gaussdb_packet_service.start_capture(
                        server_config_id=server_config_id
                    )
                logger.info(f"Started GaussDB smart packet capture: {packet_file}")

                # 等待抓包服务完全启动
                await asyncio.sleep(2)
            except Exception as e:
                logger.error(f"Smart capture failed, falling back to regular capture: {str(e)}")
                # 回退到常规抓包
                if gaussdb_config:
                    packet_file = await gaussdb_packet_service.start_capture(
                        target_host=gaussdb_config.host,
                        target_port=gaussdb_config.port
                    )
                else:
                    packet_file = await gaussdb_packet_service.start_capture()
                logger.info(f"Started GaussDB regular packet capture: {packet_file}")

                # 等待抓包服务完全启动
                await asyncio.sleep(2)

        # 3. 判断是否为自然语言查询
        is_natural_language = not request.query.strip().upper().startswith(('SELECT', 'INSERT', 'UPDATE', 'DELETE', 'CREATE', 'DROP', 'ALTER', 'SHOW', 'DESCRIBE', 'EXPLAIN'))

        sql_query = request.query
        if is_natural_language:
            # 4. 使用AI将自然语言转换为SQL
            try:
                logger.info("Converting natural language to GaussDB SQL")
                
                # 构建GaussDB特定的提示
                system_prompt = "你是一个专业的GaussDB数据库专家，擅长将自然语言转换为SQL语句。GaussDB是基于PostgreSQL的企业级数据库，支持标准SQL语法。"

                user_prompt = f"""
                将以下自然语言查询转换为GaussDB SQL语句：

                自然语言查询: {request.query}

                要求:
                1. 生成标准的SQL语句
                2. 考虑GaussDB的语法特性
                3. 只返回SQL语句，不要其他解释
                4. 支持多语句查询，用分号分隔
                5. 确保语句语法正确

                SQL语句:
                """

                sql_query = await ai_service.generate_response(system_prompt, user_prompt)
                sql_query = sql_query.strip()
                
                # 清理AI响应，移除可能的markdown格式
                if sql_query.startswith('```sql'):
                    sql_query = sql_query[6:]
                if sql_query.endswith('```'):
                    sql_query = sql_query[:-3]
                sql_query = sql_query.strip()

                logger.info(f"Generated GaussDB SQL: {sql_query}")

            except Exception as e:
                logger.error(f"Failed to convert natural language to SQL: {str(e)}")
                if request.capture_packets:
                    await gaussdb_packet_service.stop_capture()
                raise HTTPException(status_code=500, detail=f"AI conversion failed: {str(e)}")

        # 5. 执行SQL查询
        try:
            logger.info(f"Executing GaussDB SQL: {sql_query}")
            
            # 创建GaussDB服务实例
            if gaussdb_config:
                gaussdb_service = GaussDBServiceV2(
                    host=gaussdb_config.host,
                    port=gaussdb_config.port,
                    user=gaussdb_config.user,
                    password=gaussdb_config.password,
                    database=gaussdb_config.database_name
                )
            else:
                # 使用默认配置
                gaussdb_service = GaussDBServiceV2(
                    host=Config.GAUSSDB_HOST if hasattr(Config, 'GAUSSDB_HOST') else "localhost",
                    port=Config.GAUSSDB_PORT if hasattr(Config, 'GAUSSDB_PORT') else 5432,
                    user=Config.GAUSSDB_USER if hasattr(Config, 'GAUSSDB_USER') else "gaussdb",
                    password=Config.GAUSSDB_PASSWORD if hasattr(Config, 'GAUSSDB_PASSWORD') else "password",
                    database=Config.GAUSSDB_DATABASE if hasattr(Config, 'GAUSSDB_DATABASE') else "postgres"
                )

            await gaussdb_service.initialize()

            # 支持多语句执行
            statements = [stmt.strip() for stmt in sql_query.split(';') if stmt.strip()]
            results = []
            total_execution_time = 0

            # 如果需要抓包，使用手动连接控制
            if request.capture_packets:
                try:
                    for i, statement in enumerate(statements):
                        logger.info(f"Executing statement {i+1}/{len(statements)} for capture: {statement}")

                        # 使用专门的抓包执行方法（包含完整连接生命周期）
                        result = await gaussdb_service.execute_query_for_capture(statement)
                        results.append(result)
                        total_execution_time += result.get('execution_time', 0)

                        # 强制关闭连接以确保捕获挥手包
                        await gaussdb_service.force_close_connection()
                        logger.info("GaussDB connection force closed for packet capture")

                        # 在语句之间添加短暂延迟，确保抓包能捕获到所有数据
                        if i < len(statements) - 1:
                            await asyncio.sleep(0.5)
                except Exception as e:
                    logger.error(f"GaussDB query execution for capture failed: {str(e)}")
                    # 确保连接被关闭
                    try:
                        await gaussdb_service.force_close_connection()
                    except Exception as close_e:
                        logger.warning(f"Failed to force close GaussDB connection: {str(close_e)}")
                    raise
            else:
                # 不抓包时使用普通连接
                for i, statement in enumerate(statements):
                    logger.info(f"Executing statement {i+1}/{len(statements)}: {statement}")

                    result = await gaussdb_service.execute_sql_query(statement)
                    results.append(result)
                    total_execution_time += result.get('execution_time', 0)

                    # 在语句之间添加短暂延迟，确保抓包能捕获到所有数据
                    if i < len(statements) - 1:
                        await asyncio.sleep(0.5)

            # 等待一段时间确保所有数据包都被捕获
            await asyncio.sleep(2)

            # 6. 停止抓包
            packet_file = None
            if request.capture_packets:
                try:
                    capture_result = await gaussdb_packet_service.stop_capture()
                    packet_file = capture_result.get('capture_file')
                    logger.info(f"GaussDB packet capture stopped: {packet_file}")
                except Exception as e:
                    logger.error(f"Failed to stop GaussDB packet capture: {str(e)}")

            # 7. 构建响应
            if len(results) == 1:
                # 单语句结果
                final_result = results[0]
            else:
                # 多语句结果
                final_result = {
                    'statements_count': len(statements),
                    'results': results,
                    'total_rows': sum(r.get('rows_affected', 0) for r in results),
                    'all_successful': all(r.get('success', False) for r in results)
                }

            response = GaussDBQueryResponse(
                success=True,
                message="GaussDB query executed successfully",
                sql_query=sql_query,
                result=final_result,
                packet_file=packet_file,
                execution_time=total_execution_time
            )

            logger.info(f"GaussDB query completed successfully")
            return response

        except Exception as e:
            logger.error(f"Failed to execute GaussDB query: {str(e)}")
            
            # 确保停止抓包
            if request.capture_packets:
                try:
                    await gaussdb_packet_service.stop_capture()
                except Exception as e:

                    logger.error(f"操作失败: {type(e).__name__}: {str(e)}")

                    logger.error(f"异常堆栈跟踪: {traceback.format_exc()}")
            raise HTTPException(status_code=500, detail=f"Query execution failed: {str(e)}")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in GaussDB query processing: {str(e)}")
        
        # 确保停止抓包
        if request.capture_packets:
            try:
                await gaussdb_packet_service.stop_capture()
            except Exception as e:

                logger.error(f"操作失败: {type(e).__name__}: {str(e)}")

                logger.error(f"异常堆栈跟踪: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.get("/capture/status")
async def get_gaussdb_capture_status():
    """获取GaussDB抓包状态"""
    return gaussdb_packet_service.get_capture_status()

@router.post("/capture/start")
async def start_gaussdb_capture():
    """手动启动GaussDB抓包"""
    try:
        packet_file = await gaussdb_packet_service.start_capture()
        return {"success": True, "packet_file": packet_file}
    except Exception as e:
        logger.error(f"Failed to start GaussDB capture: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/capture/stop")
async def stop_gaussdb_capture():
    """手动停止GaussDB抓包"""
    try:
        result = await gaussdb_packet_service.stop_capture()
        return result
    except Exception as e:
        logger.error(f"Failed to stop GaussDB capture: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/test-connection", response_model=GaussDBConnectionTestResponse)
async def test_gaussdb_connection(
    request: GaussDBConnectionTestRequest,
    db_config_service: DatabaseConfigService = Depends(get_db_config_service)
):
    """测试GaussDB连接"""
    try:
        logger.info(f"Testing GaussDB connection with config_id: {request.config_id}")

        # 获取GaussDB配置
        gaussdb_config = None
        if request.config_id:
            gaussdb_config = await db_config_service.get_config(request.config_id)
            if not gaussdb_config or gaussdb_config.database_type.lower() != 'gaussdb':
                raise HTTPException(status_code=404, detail="GaussDB configuration not found")
        else:
            # 使用默认GaussDB配置
            gaussdb_config = await db_config_service.get_default_config_by_type('gaussdb')

        if not gaussdb_config:
            raise HTTPException(status_code=404, detail="No GaussDB configuration found")

        # 创建GaussDB服务实例
        gaussdb_service = GaussDBServiceV2(
            host=gaussdb_config.host,
            port=gaussdb_config.port,
            user=gaussdb_config.user,
            password=gaussdb_config.password,
            database=gaussdb_config.database_name
        )

        # 测试连接并获取数据库信息
        import time
        start_time = time.time()

        await gaussdb_service.initialize()

        # 获取数据库列表
        databases = await gaussdb_service.get_databases()

        response_time = time.time() - start_time

        return GaussDBConnectionTestResponse(
            success=True,
            message="GaussDB connection successful",
            response_time=response_time,
            databases=databases,
            databases_count=len(databases) if databases else 0
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"GaussDB connection test failed: {str(e)}")
        return GaussDBConnectionTestResponse(
            success=False,
            message="GaussDB connection failed",
            error=str(e)
        )

@router.get("/databases")
async def get_gaussdb_databases(
    config_id: Optional[int] = None,
    db_config_service: DatabaseConfigService = Depends(get_db_config_service)
):
    """获取GaussDB数据库列表"""
    try:
        # 获取GaussDB配置
        gaussdb_config = None
        if config_id:
            gaussdb_config = await db_config_service.get_config(config_id)
            if not gaussdb_config or gaussdb_config.database_type.lower() != 'gaussdb':
                raise HTTPException(status_code=404, detail="GaussDB configuration not found")
        else:
            gaussdb_config = await db_config_service.get_default_config_by_type('gaussdb')

        if not gaussdb_config:
            raise HTTPException(status_code=404, detail="No GaussDB configuration found")

        # 创建GaussDB服务实例
        gaussdb_service = GaussDBServiceV2(
            host=gaussdb_config.host,
            port=gaussdb_config.port,
            user=gaussdb_config.user,
            password=gaussdb_config.password,
            database=gaussdb_config.database_name
        )

        await gaussdb_service.initialize()
        databases = await gaussdb_service.get_databases()

        return {
            "success": True,
            "databases": databases,
            "count": len(databases) if databases else 0
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get GaussDB databases: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/tables")
async def get_gaussdb_tables(
    schema: str = "public",
    config_id: Optional[int] = None,
    db_config_service: DatabaseConfigService = Depends(get_db_config_service)
):
    """获取GaussDB表列表"""
    try:
        # 获取GaussDB配置
        gaussdb_config = None
        if config_id:
            gaussdb_config = await db_config_service.get_config(config_id)
            if not gaussdb_config or gaussdb_config.database_type.lower() != 'gaussdb':
                raise HTTPException(status_code=404, detail="GaussDB configuration not found")
        else:
            gaussdb_config = await db_config_service.get_default_config_by_type('gaussdb')

        if not gaussdb_config:
            raise HTTPException(status_code=404, detail="No GaussDB configuration found")

        # 创建GaussDB服务实例
        gaussdb_service = GaussDBServiceV2(
            host=gaussdb_config.host,
            port=gaussdb_config.port,
            user=gaussdb_config.user,
            password=gaussdb_config.password,
            database=gaussdb_config.database_name
        )

        await gaussdb_service.initialize()
        tables = await gaussdb_service.get_tables(schema)

        return {
            "success": True,
            "schema": schema,
            "tables": tables,
            "count": len(tables) if tables else 0
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get GaussDB tables: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/table/{table_name}")
async def get_gaussdb_table_info(
    table_name: str,
    schema: str = "public",
    config_id: Optional[int] = None,
    db_config_service: DatabaseConfigService = Depends(get_db_config_service)
):
    """获取GaussDB表信息"""
    try:
        # 获取GaussDB配置
        gaussdb_config = None
        if config_id:
            gaussdb_config = await db_config_service.get_config(config_id)
            if not gaussdb_config or gaussdb_config.database_type.lower() != 'gaussdb':
                raise HTTPException(status_code=404, detail="GaussDB configuration not found")
        else:
            gaussdb_config = await db_config_service.get_default_config_by_type('gaussdb')

        if not gaussdb_config:
            raise HTTPException(status_code=404, detail="No GaussDB configuration found")

        # 创建GaussDB服务实例
        gaussdb_service = GaussDBServiceV2(
            host=gaussdb_config.host,
            port=gaussdb_config.port,
            user=gaussdb_config.user,
            password=gaussdb_config.password,
            database=gaussdb_config.database_name
        )

        await gaussdb_service.initialize()
        table_info = await gaussdb_service.get_table_info(table_name, schema)

        return {
            "success": True,
            "table_name": table_name,
            "schema": schema,
            "table_info": table_info
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get GaussDB table info: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

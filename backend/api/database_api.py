from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import logging
from services.database_config_service import DatabaseConfigService, DatabaseConfigModel

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/databases", tags=["databases"])

# 全局数据库配置服务实例
db_config_service = DatabaseConfigService()

# Pydantic模型
class DatabaseConfigRequest(BaseModel):
    name: str
    host: str
    port: int = 3306
    user: str
    password: str
    database_name: str
    database_type: str = "mysql"
    database_version: Optional[str] = None
    description: str = ""
    is_default: bool = False

class DatabaseConfigResponse(BaseModel):
    id: int
    name: str
    host: str
    port: int
    user: str
    password: str
    database_name: str
    database_type: str
    database_version: Optional[str] = None
    description: str
    is_default: bool
    is_active: bool
    created_at: str
    updated_at: str

class DatabaseListResponse(BaseModel):
    databases: List[DatabaseConfigResponse]
    total: int
    page: int = 1
    page_size: int = 20
    total_pages: int = 1

class TableInfo(BaseModel):
    database_name: str
    table_name: str
    table_type: str
    table_comment: str
    table_rows: int
    data_length: int
    index_length: int

class ConnectionTestResponse(BaseModel):
    success: bool
    message: str = ""
    response_time: int = 0
    databases: Optional[List[str]] = None
    databases_count: int = 0
    tables: Optional[List[TableInfo]] = None
    tables_count: int = 0
    error: Optional[str] = None

@router.get("/", response_model=DatabaseListResponse)
async def list_databases(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    db_type: Optional[str] = Query(None, description="数据库类型过滤"),
    db_version: Optional[str] = Query(None, description="数据库版本过滤")
):
    """获取数据库配置列表（支持分页）"""
    try:
        configs, total = await db_config_service.list_configs_paginated(
            page=page,
            page_size=page_size,
            db_type=db_type,
            db_version=db_version
        )

        databases = [
            DatabaseConfigResponse(
                id=config.id,
                name=config.name,
                host=config.host,
                port=config.port,
                user=config.user,
                password=config.password,
                database_name=config.database_name,
                database_type=config.database_type,
                database_version=config.database_version,
                description=config.description,
                is_default=config.is_default,
                is_active=config.is_active,
                created_at=config.created_at,
                updated_at=config.updated_at
            )
            for config in configs
        ]

        # 计算总页数
        total_pages = (total + page_size - 1) // page_size

        return DatabaseListResponse(
            databases=databases,
            total=total,
            page=page,
            page_size=page_size,
            total_pages=total_pages
        )
    except Exception as e:
        logger.error(f"Failed to list databases: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{config_id}", response_model=DatabaseConfigResponse)
async def get_database(config_id: int):
    """获取指定数据库配置"""
    try:
        config = await db_config_service.get_config(config_id)
        if not config:
            raise HTTPException(status_code=404, detail=f"Database configuration ID '{config_id}' not found")

        return DatabaseConfigResponse(
            id=config.id,
            name=config.name,
            host=config.host,
            port=config.port,
            user=config.user,
            password=config.password,
            database_name=config.database_name,
            database_type=config.database_type,
            database_version=config.database_version,
            description=config.description,
            is_default=config.is_default,
            is_active=config.is_active,
            created_at=config.created_at,
            updated_at=config.updated_at
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get database: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/", response_model=Dict[str, Any])
async def add_database(request: DatabaseConfigRequest):
    """添加数据库配置"""
    try:
        config = DatabaseConfigModel(
            name=request.name,
            host=request.host,
            port=request.port,
            user=request.user,
            password=request.password,
            database_name=request.database_name,
            database_type=request.database_type,
            database_version=request.database_version,
            description=request.description,
            is_default=request.is_default
        )

        config_id = await db_config_service.add_config(config)

        return {
            "message": f"Database configuration '{request.name}' added successfully",
            "config_id": config_id
        }
    except Exception as e:
        logger.error(f"Failed to add database: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/{config_id}", response_model=Dict[str, str])
async def update_database(config_id: int, request: DatabaseConfigRequest):
    """更新数据库配置"""
    try:
        config = DatabaseConfigModel(
            id=config_id,  # 设置ID
            name=request.name,
            host=request.host,
            port=request.port,
            user=request.user,
            password=request.password,
            database_name=request.database_name,
            database_type=request.database_type,
            database_version=request.database_version,
            description=request.description,
            is_default=request.is_default
        )

        success = await db_config_service.update_config(config)
        if not success:
            raise HTTPException(status_code=404, detail=f"Database configuration ID '{config_id}' not found")
        
        return {"message": f"Database configuration ID '{config_id}' updated successfully"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update database: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/{config_id}", response_model=Dict[str, str])
async def delete_database(config_id: int):
    """删除数据库配置"""
    try:
        success = await db_config_service.delete_config(config_id)
        if not success:
            raise HTTPException(status_code=404, detail=f"Database configuration ID '{config_id}' not found")
        
        return {"message": f"Database configuration ID '{config_id}' deleted successfully"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete database: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/{config_id}/set-default", response_model=Dict[str, str])
async def set_default_database(config_id: int):
    """设置默认数据库"""
    try:
        success = await db_config_service.set_default_config(config_id)
        if not success:
            raise HTTPException(status_code=404, detail=f"Database configuration ID '{config_id}' not found")
        
        return {"message": f"Database configuration ID '{config_id}' set as default"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to set default database: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/{config_id}/test", response_model=ConnectionTestResponse)
async def test_database_connection(config_id: int):
    """测试数据库连接并获取表信息"""
    try:
        config = await db_config_service.get_config(config_id)
        if not config:
            raise HTTPException(status_code=404, detail=f"Database configuration ID '{config_id}' not found")

        result = await db_config_service.test_connection(config)

        if result['success']:
            # 转换表信息格式
            tables = []
            if result.get('tables'):
                # 检查tables是否为字典列表（详细表信息）还是字符串列表（简单表名）
                if result['tables'] and isinstance(result['tables'][0], dict):
                    # 详细表信息格式
                    tables = [
                        TableInfo(
                            database_name=table.get('database_name', ''),
                            table_name=table.get('table_name', ''),
                            table_type=table.get('table_type', 'BASE TABLE'),
                            table_comment=table.get('table_comment', ''),
                            table_rows=table.get('table_rows', 0),
                            data_length=table.get('data_length', 0),
                            index_length=table.get('index_length', 0)
                        )
                        for table in result['tables']
                    ]
                else:
                    # 简单表名列表格式（如GaussDB返回的格式）
                    tables = [
                        TableInfo(
                            database_name=result.get('database_name', ''),
                            table_name=table_name,
                            table_type='BASE TABLE',
                            table_comment='',
                            table_rows=0,
                            data_length=0,
                            index_length=0
                        )
                        for table_name in result['tables']
                    ]

            return ConnectionTestResponse(
                success=True,
                message="Connection successful",
                response_time=result.get('response_time', 0),
                databases=result.get('databases', []),
                databases_count=result.get('database_count', result.get('databases_count', 0)),
                tables=tables,
                tables_count=result.get('table_count', result.get('tables_count', 0))
            )
        else:
            return ConnectionTestResponse(
                success=False,
                message="Connection failed",
                response_time=result.get('response_time', 0),
                error=result.get('error', 'Unknown error')
            )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to test database connection: {e}")
        return ConnectionTestResponse(
            success=False,
            message="Connection test failed",
            error=str(e)
        )

@router.get("/{config_id}/export")
async def export_database_config(config_id: int):
    """导出数据库配置"""
    try:
        config = await db_config_service.get_config(config_id)
        if not config:
            raise HTTPException(status_code=404, detail=f"Database configuration ID '{config_id}' not found")
        
        # 转换为字典格式，移除敏感信息
        config_dict = {
            "name": config.name,
            "host": config.host,
            "port": config.port,
            "user": config.user,
            # 不导出密码信息
            "database_name": config.database_name,
            "description": config.description,
            "is_default": config.is_default
        }
        return config_dict
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to export database config: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/import", response_model=Dict[str, str])
async def import_database_config(config_data: DatabaseConfigRequest):
    """导入数据库配置"""
    try:
        config = DatabaseConfigModel(
            name=config_data.name,
            host=config_data.host,
            port=config_data.port,
            user=config_data.user,
            password=config_data.password,
            database_name=config_data.database_name,
            database_type=config_data.database_type,
            database_version=config_data.database_version,
            description=config_data.description,
            is_default=config_data.is_default
        )
        
        config_id = await db_config_service.add_config(config)
        return {"message": f"Database configuration imported successfully with ID {config_id}"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to import database config: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/test-config", response_model=ConnectionTestResponse)
async def test_database_config(request: DatabaseConfigRequest):
    """测试数据库配置连接（不保存配置）"""
    try:
        # 创建临时配置对象进行测试
        config = DatabaseConfigModel(
            name=request.name,
            host=request.host,
            port=request.port,
            user=request.user,
            password=request.password,
            database_name=request.database_name,
            database_type=request.database_type,
            description=request.description,
            is_default=request.is_default
        )

        result = await db_config_service.test_connection(config)

        if result['success']:
            # 转换表信息格式
            tables = []
            if result.get('tables'):
                # 检查tables是否为字典列表（详细表信息）还是字符串列表（简单表名）
                if result['tables'] and isinstance(result['tables'][0], dict):
                    # 详细表信息格式
                    tables = [
                        TableInfo(
                            database_name=table.get('database_name', ''),
                            table_name=table.get('table_name', ''),
                            table_type=table.get('table_type', 'BASE TABLE'),
                            table_comment=table.get('table_comment', ''),
                            table_rows=table.get('table_rows', 0),
                            data_length=table.get('data_length', 0),
                            index_length=table.get('index_length', 0)
                        )
                        for table in result['tables']
                    ]
                else:
                    # 简单表名列表格式（如GaussDB返回的格式）
                    tables = [
                        TableInfo(
                            database_name=result.get('database_name', ''),
                            table_name=table_name,
                            table_type='BASE TABLE',
                            table_comment='',
                            table_rows=0,
                            data_length=0,
                            index_length=0
                        )
                        for table_name in result['tables']
                    ]

            return ConnectionTestResponse(
                success=True,
                message="Connection successful",
                response_time=result.get('response_time', 0),
                databases=result.get('databases', []),
                databases_count=result.get('database_count', result.get('databases_count', 0)),
                tables=tables,
                tables_count=result.get('table_count', result.get('tables_count', 0))
            )
        else:
            return ConnectionTestResponse(
                success=False,
                message="Connection failed",
                response_time=result.get('response_time', 0),
                error=result.get('error', 'Unknown error')
            )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to test database config: {e}")
        return ConnectionTestResponse(
            success=False,
            message="Connection test failed",
            error=str(e)
        )

@router.get("/default/current")
async def get_default_database():
    """获取当前默认数据库配置"""
    try:
        config = await db_config_service.get_default_config()
        if not config:
            raise HTTPException(status_code=404, detail="No default database configuration found")
        
        return DatabaseConfigResponse(
            id=config.id,
            name=config.name,
            host=config.host,
            port=config.port,
            user=config.user,
            password=config.password,
            database_name=config.database_name,
            description=config.description,
            is_default=config.is_default,
            is_active=config.is_active,
            created_at=config.created_at,
            updated_at=config.updated_at
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get default database: {e}")
        raise HTTPException(status_code=500, detail=str(e))

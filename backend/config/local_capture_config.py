"""
本地抓包配置
"""

import os
from typing import Dict, List, Optional

class LocalCaptureConfig:
    """本地抓包配置类"""
    
    # 默认抓包目录 - 使用统一路径管理器
    from utils.path_manager import path_manager
    DEFAULT_CAPTURE_DIR = path_manager.get_captures_dir()
    
    # 默认端口范围
    DEFAULT_PORT_RANGE = {
        'start': 8000,
        'end': 9000
    }
    
    # 网络接口优先级
    INTERFACE_PRIORITY = [
        'en0',       # macOS Wi‑Fi
        'en1',       # macOS 以太网/雷雳
        'eth0',      # Linux 以太网接口
        'ens3',      # Linux 新命名规则的以太网接口
        'ens33',     # VMware 虚拟机常用接口
        'enp0s3',    # VirtualBox 虚拟机常用接口
        'wlan0',     # Linux 无线网络接口
        'wifi0',     # 无线网络接口（备选）
        'docker0',   # Docker 网桥接口
        'lo'         # 回环接口（最后选择）
    ]
    
    # 数据库默认端口映射
    DATABASE_PORTS = {
        'oracle': 1521,
        'gaussdb': 5432,
        'postgresql': 5432,
        'mysql': 3306,
        'mongodb': 27017,
        'redis': 6379
    }
    
    # tcpdump默认参数
    TCPDUMP_DEFAULT_ARGS = {
        'buffer_size': '65536',  # -B参数
        'snapshot_length': '0',  # -s参数，0表示捕获完整数据包
        'timeout': 30,           # 默认超时时间（秒）
        'max_file_size': '100M'  # 最大文件大小
    }
    
    # 本地抓包特定配置
    LOCAL_CAPTURE_CONFIG = {
        'use_local_by_default': True,        # 默认使用本地抓包
        'fallback_to_remote': True,          # 本地抓包失败时回退到远程
        'auto_detect_interface': True,       # 自动检测网络接口
        'auto_find_port': True,              # 自动查找空闲端口
        'validate_interface': True,          # 验证接口是否存在
        'check_tcpdump_available': True,     # 检查tcpdump是否可用
        'cleanup_on_failure': True,          # 失败时清理资源
        'log_capture_details': True          # 记录详细的抓包信息
    }
    
    @classmethod
    def get_capture_dir(cls) -> str:
        """获取抓包目录"""
        capture_dir = os.environ.get('CAPTURE_DIR', cls.DEFAULT_CAPTURE_DIR)
        os.makedirs(capture_dir, exist_ok=True)
        return capture_dir
    
    @classmethod
    def get_port_range(cls) -> Dict[str, int]:
        """获取端口范围"""
        start_port = int(os.environ.get('PORT_RANGE_START', cls.DEFAULT_PORT_RANGE['start']))
        end_port = int(os.environ.get('PORT_RANGE_END', cls.DEFAULT_PORT_RANGE['end']))
        return {'start': start_port, 'end': end_port}
    
    @classmethod
    def get_database_port(cls, database_type: str) -> Optional[int]:
        """获取数据库默认端口"""
        return cls.DATABASE_PORTS.get(database_type.lower())
    
    @classmethod
    def get_interface_priority(cls) -> List[str]:
        """获取网络接口优先级列表"""
        # 可以通过环境变量覆盖
        env_priority = os.environ.get('INTERFACE_PRIORITY')
        if env_priority:
            return env_priority.split(',')
        return cls.INTERFACE_PRIORITY.copy()
    
    @classmethod
    def is_local_capture_enabled(cls) -> bool:
        """检查是否启用本地抓包"""
        return os.environ.get('USE_LOCAL_CAPTURE', 'true').lower() == 'true'
    
    @classmethod
    def should_fallback_to_remote(cls) -> bool:
        """检查是否应该回退到远程抓包"""
        return os.environ.get('FALLBACK_TO_REMOTE', 'true').lower() == 'true'
    
    @classmethod
    def get_tcpdump_args(cls) -> Dict[str, str]:
        """获取tcpdump参数"""
        args = cls.TCPDUMP_DEFAULT_ARGS.copy()
        
        # 可以通过环境变量覆盖
        if os.environ.get('TCPDUMP_BUFFER_SIZE'):
            args['buffer_size'] = os.environ.get('TCPDUMP_BUFFER_SIZE')
        
        if os.environ.get('TCPDUMP_SNAPSHOT_LENGTH'):
            args['snapshot_length'] = os.environ.get('TCPDUMP_SNAPSHOT_LENGTH')
        
        if os.environ.get('TCPDUMP_TIMEOUT'):
            args['timeout'] = int(os.environ.get('TCPDUMP_TIMEOUT'))
        
        return args
    
    @classmethod
    def get_config_summary(cls) -> Dict[str, any]:
        """获取配置摘要"""
        return {
            'capture_dir': cls.get_capture_dir(),
            'port_range': cls.get_port_range(),
            'interface_priority': cls.get_interface_priority(),
            'local_capture_enabled': cls.is_local_capture_enabled(),
            'fallback_to_remote': cls.should_fallback_to_remote(),
            'tcpdump_args': cls.get_tcpdump_args(),
            'database_ports': cls.DATABASE_PORTS,
            'local_capture_config': cls.LOCAL_CAPTURE_CONFIG
        }

#!/usr/bin/env python3
"""
全局异常处理中间件
确保所有未捕获的异常都被正确记录到日志文件
"""

import logging
import traceback
import time
from typing import Callable
from fastapi import Request, Response, HTTPException
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

logger = logging.getLogger(__name__)


class GlobalExceptionMiddleware(BaseHTTPMiddleware):
    """全局异常处理中间件"""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """处理请求并捕获异常"""
        start_time = time.time()
        
        try:
            # 记录请求开始
            logger.debug(f"开始处理请求: {request.method} {request.url}")
            
            # 处理请求
            response = await call_next(request)
            
            # 记录请求完成
            process_time = time.time() - start_time
            logger.debug(f"请求处理完成: {request.method} {request.url} - {response.status_code} - {process_time:.3f}s")
            
            return response
            
        except HTTPException as e:
            # HTTP异常（这些通常是预期的错误）
            process_time = time.time() - start_time
            logger.warning(
                f"HTTP异常: {request.method} {request.url} - "
                f"状态码: {e.status_code} - 详情: {e.detail} - 耗时: {process_time:.3f}s"
            )
            
            # 返回标准的HTTP异常响应
            return JSONResponse(
                status_code=e.status_code,
                content={
                    "success": False,
                    "error": e.detail,
                    "error_type": "HTTPException",
                    "status_code": e.status_code,
                    "path": str(request.url),
                    "method": request.method,
                    "timestamp": time.time()
                }
            )
            
        except Exception as e:
            # 未预期的异常
            process_time = time.time() - start_time
            
            # 记录详细的异常信息
            logger.error(
                f"未捕获异常: {request.method} {request.url} - "
                f"异常类型: {type(e).__name__} - 异常信息: {str(e)} - 耗时: {process_time:.3f}s"
            )
            logger.error(f"异常堆栈跟踪:\n{traceback.format_exc()}")
            
            # 记录请求详情
            try:
                # 安全地获取请求体（如果可能）
                if hasattr(request, '_body'):
                    body = request._body
                    if body:
                        logger.error(f"请求体: {body.decode('utf-8', errors='ignore')[:1000]}")
                
                # 记录请求头
                logger.error(f"请求头: {dict(request.headers)}")
                
                # 记录查询参数
                if request.query_params:
                    logger.error(f"查询参数: {dict(request.query_params)}")
                    
            except Exception as log_error:
                logger.error(f"记录请求详情时发生错误: {log_error}")
            
            # 返回标准化的错误响应
            return JSONResponse(
                status_code=500,
                content={
                    "success": False,
                    "error": "内部服务器错误",
                    "error_type": type(e).__name__,
                    "error_details": str(e),
                    "path": str(request.url),
                    "method": request.method,
                    "timestamp": time.time(),
                    "request_id": getattr(request.state, 'request_id', None)
                }
            )


class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """请求日志记录中间件"""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """记录请求和响应信息"""
        start_time = time.time()
        
        # 生成请求ID
        import uuid
        request_id = str(uuid.uuid4())[:8]
        request.state.request_id = request_id
        
        # 记录请求开始
        logger.info(
            f"[{request_id}] 请求开始: {request.method} {request.url} - "
            f"客户端: {request.client.host if request.client else 'unknown'}"
        )
        
        try:
            # 处理请求
            response = await call_next(request)
            
            # 记录请求完成
            process_time = time.time() - start_time
            logger.info(
                f"[{request_id}] 请求完成: {request.method} {request.url} - "
                f"状态码: {response.status_code} - 耗时: {process_time:.3f}s"
            )
            
            # 添加请求ID到响应头
            response.headers["X-Request-ID"] = request_id
            
            return response
            
        except Exception as e:
            # 记录请求失败
            process_time = time.time() - start_time
            logger.error(
                f"[{request_id}] 请求失败: {request.method} {request.url} - "
                f"异常: {type(e).__name__}: {str(e)} - 耗时: {process_time:.3f}s"
            )
            raise


def setup_exception_middleware(app):
    """设置异常处理中间件"""
    # 添加请求日志中间件
    app.add_middleware(RequestLoggingMiddleware)
    
    # 添加全局异常处理中间件
    app.add_middleware(GlobalExceptionMiddleware)
    
    logger.info("全局异常处理中间件已设置")


# 异常处理装饰器，用于特定的API端点
def handle_api_exceptions(operation_name: str = None):
    """API异常处理装饰器"""
    def decorator(func):
        import functools
        
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            op_name = operation_name or func.__name__
            try:
                logger.debug(f"开始执行API操作: {op_name}")
                result = await func(*args, **kwargs)
                logger.debug(f"API操作完成: {op_name}")
                return result
                
            except HTTPException:
                # HTTP异常直接重新抛出
                raise
                
            except Exception as e:
                # 记录详细的异常信息
                logger.error(f"API操作 '{op_name}' 发生异常: {type(e).__name__}: {str(e)}")
                logger.error(f"异常堆栈跟踪:\n{traceback.format_exc()}")
                
                # 抛出HTTP异常
                raise HTTPException(
                    status_code=500,
                    detail={
                        "success": False,
                        "error": str(e),
                        "error_type": type(e).__name__,
                        "operation": op_name,
                        "timestamp": time.time()
                    }
                )
        
        return wrapper
    return decorator

#!/bin/bash

# GaussDB C语言执行器安装和编译脚本

set -e  # 遇到错误立即退出

echo "=== GaussDB C语言执行器安装脚本 ==="

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
C_EXECUTOR_DIR="$SCRIPT_DIR/c_executors"

echo "脚本目录: $SCRIPT_DIR"
echo "C执行器目录: $C_EXECUTOR_DIR"

# 检查操作系统
detect_os() {
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        if command -v apt-get >/dev/null 2>&1; then
            echo "ubuntu"
        elif command -v yum >/dev/null 2>&1; then
            echo "centos"
        else
            echo "linux"
        fi
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        echo "macos"
    else
        echo "unknown"
    fi
}

OS=$(detect_os)
echo "检测到操作系统: $OS"

# 安装依赖
install_dependencies() {
    echo "=== 安装依赖包 ==="
    
    case $OS in
        "ubuntu")
            echo "使用apt-get安装依赖..."
            sudo apt-get update
            sudo apt-get install -y libpq-dev libjson-c-dev gcc make
            ;;
        "centos")
            echo "使用yum安装依赖..."
            sudo yum install -y postgresql-devel json-c-devel gcc make
            ;;
        "macos")
            echo "使用brew安装依赖..."
            if ! command -v brew >/dev/null 2>&1; then
                echo "错误: 未找到brew，请先安装Homebrew"
                exit 1
            fi
            brew install postgresql json-c
            ;;
        *)
            echo "警告: 未知操作系统，请手动安装以下依赖:"
            echo "  - PostgreSQL开发库 (libpq-dev)"
            echo "  - JSON-C开发库 (libjson-c-dev)"
            echo "  - GCC编译器"
            echo "  - Make工具"
            read -p "是否继续编译? (y/N): " -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                exit 1
            fi
            ;;
    esac
}

# 检查依赖
check_dependencies() {
    echo "=== 检查依赖 ==="
    
    # 检查编译器
    if ! command -v gcc >/dev/null 2>&1; then
        echo "错误: 未找到gcc编译器"
        return 1
    fi
    echo "✓ GCC编译器已安装"
    
    # 检查make
    if ! command -v make >/dev/null 2>&1; then
        echo "错误: 未找到make工具"
        return 1
    fi
    echo "✓ Make工具已安装"
    
    # 检查PostgreSQL开发库
    if ! pkg-config --exists libpq 2>/dev/null; then
        echo "警告: 未找到libpq开发库，尝试继续编译..."
    else
        echo "✓ PostgreSQL开发库已安装"
    fi
    
    # 检查JSON-C库
    if ! pkg-config --exists json-c 2>/dev/null; then
        echo "警告: 未找到json-c开发库，尝试继续编译..."
    else
        echo "✓ JSON-C开发库已安装"
    fi
    
    return 0
}

# 编译C执行器
compile_executor() {
    echo "=== 编译C执行器 ==="
    
    if [[ ! -d "$C_EXECUTOR_DIR" ]]; then
        echo "错误: C执行器目录不存在: $C_EXECUTOR_DIR"
        exit 1
    fi
    
    cd "$C_EXECUTOR_DIR"
    
    # 检查源文件
    if [[ ! -f "gaussdb_libpq_executor.c" ]]; then
        echo "错误: 源文件不存在: gaussdb_libpq_executor.c"
        exit 1
    fi
    
    if [[ ! -f "Makefile" ]]; then
        echo "错误: Makefile不存在"
        exit 1
    fi
    
    # 清理之前的编译结果
    echo "清理之前的编译结果..."
    make clean 2>/dev/null || true
    
    # 编译
    echo "开始编译..."
    if make; then
        echo "✓ 编译成功"
    else
        echo "❌ 编译失败"
        exit 1
    fi
    
    # 检查可执行文件
    if [[ -f "gaussdb_libpq_executor" ]]; then
        echo "✓ 可执行文件已生成: gaussdb_libpq_executor"
        
        # 设置执行权限
        chmod +x gaussdb_libpq_executor
        echo "✓ 已设置执行权限"
        
        # 显示文件信息
        ls -la gaussdb_libpq_executor
    else
        echo "❌ 可执行文件未生成"
        exit 1
    fi
}

# 测试编译结果
test_executor() {
    echo "=== 测试C执行器 ==="
    
    cd "$C_EXECUTOR_DIR"
    
    if [[ ! -f "gaussdb_libpq_executor" ]]; then
        echo "错误: 可执行文件不存在"
        exit 1
    fi
    
    # 测试帮助信息
    echo "测试帮助信息..."
    if ./gaussdb_libpq_executor 2>/dev/null; then
        echo "✓ 程序可以正常运行"
    else
        echo "程序运行测试完成（预期会有错误信息）"
    fi
}

# 运行Python测试
run_python_test() {
    echo "=== 运行Python测试 ==="
    
    cd "$SCRIPT_DIR"
    
    if [[ ! -f "test_c_executor.py" ]]; then
        echo "警告: Python测试脚本不存在，跳过测试"
        return 0
    fi
    
    echo "运行Python测试脚本..."
    if python3 test_c_executor.py; then
        echo "✓ Python测试通过"
    else
        echo "❌ Python测试失败，但C执行器编译成功"
        echo "请检查数据库连接配置"
    fi
}

# 主函数
main() {
    echo "开始安装和编译GaussDB C语言执行器..."
    
    # 检查是否需要安装依赖
    if [[ "${1:-}" == "--install-deps" ]]; then
        install_dependencies
    fi
    
    # 检查依赖
    if ! check_dependencies; then
        echo "依赖检查失败，尝试安装依赖..."
        install_dependencies
        check_dependencies
    fi
    
    # 编译
    compile_executor
    
    # 测试
    test_executor
    
    # 运行Python测试（可选）
    if [[ "${1:-}" == "--with-test" ]] || [[ "${2:-}" == "--with-test" ]]; then
        run_python_test
    fi
    
    echo ""
    echo "=== 安装完成 ==="
    echo "C语言执行器已成功编译到: $C_EXECUTOR_DIR/gaussdb_libpq_executor"
    echo ""
    echo "使用方法:"
    echo "  连接数据库: ./gaussdb_libpq_executor connect <host> <port> <user> <password> <database>"
    echo "  执行SQL:   ./gaussdb_libpq_executor execute \"<sql>\""
    echo "  持久模式:   ./gaussdb_libpq_executor --persistent"
    echo ""
    echo "Python服务会自动检测并使用编译好的C执行器。"
    echo ""
    echo "如需运行完整测试，请执行:"
    echo "  python3 test_c_executor.py"
}

# 显示帮助信息
show_help() {
    echo "GaussDB C语言执行器安装脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --install-deps    强制安装系统依赖"
    echo "  --with-test      编译后运行Python测试"
    echo "  --help           显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                      # 仅编译"
    echo "  $0 --install-deps       # 安装依赖并编译"
    echo "  $0 --with-test          # 编译并测试"
    echo "  $0 --install-deps --with-test  # 完整安装和测试"
}

# 解析命令行参数
if [[ "${1:-}" == "--help" ]] || [[ "${1:-}" == "-h" ]]; then
    show_help
    exit 0
fi

# 运行主函数
main "$@"

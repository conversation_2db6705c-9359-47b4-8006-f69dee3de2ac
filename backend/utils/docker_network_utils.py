"""
Docker网络配置工具
确保所有容器都使用docker0网络以便抓包
"""

import logging
import re
from typing import Dict, Any

logger = logging.getLogger(__name__)

class DockerNetworkUtils:
    """Docker网络配置工具类"""
    
    @staticmethod
    def ensure_bridge_network(docker_command: str) -> str:
        """
        确保Docker命令使用bridge网络（连接到docker0）
        
        Args:
            docker_command: 原始Docker命令
            
        Returns:
            str: 修改后的Docker命令，确保包含--network bridge
        """
        try:
            # 检查命令中是否已经包含网络配置
            if '--network' in docker_command or '--net' in docker_command:
                logger.debug("Docker command already contains network configuration")
                
                # 如果使用的不是bridge网络，替换为bridge
                if '--network host' in docker_command:
                    docker_command = docker_command.replace('--network host', '--network bridge')
                    logger.info("Replaced --network host with --network bridge")
                elif '--net host' in docker_command:
                    docker_command = docker_command.replace('--net host', '--net bridge')
                    logger.info("Replaced --net host with --net bridge")
                
                return docker_command
            
            # 使用正则表达式在docker run之后添加网络配置
            pattern = r'(docker\s+run\s+)(-d\s+)?'
            replacement = r'\1\2--network bridge '
            
            modified_command = re.sub(pattern, replacement, docker_command)
            
            if modified_command != docker_command:
                logger.info("Added --network bridge to Docker command")
                return modified_command
            
            # 如果正则表达式没有匹配，使用简单的字符串替换
            if 'docker run -d' in docker_command:
                docker_command = docker_command.replace('docker run -d', 'docker run -d --network bridge')
            elif 'docker run' in docker_command:
                docker_command = docker_command.replace('docker run', 'docker run --network bridge')
            
            logger.info("Added --network bridge to Docker command using string replacement")
            return docker_command
            
        except Exception as e:
            logger.error(f"Failed to ensure bridge network: {e}")
            # 如果处理失败，返回原命令
            return docker_command
    
    @staticmethod
    def validate_network_configuration(docker_command: str) -> Dict[str, Any]:
        """
        验证Docker命令的网络配置
        
        Args:
            docker_command: Docker命令
            
        Returns:
            Dict: 验证结果
        """
        result = {
            'valid': False,
            'network_type': None,
            'uses_bridge': False,
            'issues': []
        }
        
        try:
            # 检查是否包含网络配置
            if '--network' in docker_command:
                # 提取网络类型
                network_match = re.search(r'--network\s+(\w+)', docker_command)
                if network_match:
                    network_type = network_match.group(1)
                    result['network_type'] = network_type
                    
                    if network_type == 'bridge':
                        result['uses_bridge'] = True
                        result['valid'] = True
                    else:
                        result['issues'].append(f"使用了非bridge网络: {network_type}")
                else:
                    result['issues'].append("网络配置格式错误")
            
            elif '--net' in docker_command:
                # 检查旧格式的网络配置
                net_match = re.search(r'--net\s+(\w+)', docker_command)
                if net_match:
                    network_type = net_match.group(1)
                    result['network_type'] = network_type
                    
                    if network_type == 'bridge':
                        result['uses_bridge'] = True
                        result['valid'] = True
                    else:
                        result['issues'].append(f"使用了非bridge网络: {network_type}")
                else:
                    result['issues'].append("网络配置格式错误")
            
            else:
                # 没有显式网络配置，Docker默认使用bridge
                result['network_type'] = 'bridge'
                result['uses_bridge'] = True
                result['valid'] = True
                result['issues'].append("未显式指定网络，使用Docker默认bridge网络")
            
            # 检查是否使用了host网络（不利于抓包）
            if 'host' in docker_command and ('--network host' in docker_command or '--net host' in docker_command):
                result['issues'].append("使用host网络模式，抓包工具无法捕获容器内部流量")
                result['valid'] = False
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to validate network configuration: {e}")
            result['issues'].append(f"验证失败: {str(e)}")
            return result
    
    @staticmethod
    def get_recommended_network_config() -> str:
        """
        获取推荐的网络配置参数
        
        Returns:
            str: 推荐的网络配置参数
        """
        return "--network bridge"
    
    @staticmethod
    def fix_common_network_issues(docker_command: str) -> str:
        """
        修复常见的网络配置问题
        
        Args:
            docker_command: 原始Docker命令
            
        Returns:
            str: 修复后的Docker命令
        """
        try:
            # 1. 确保使用bridge网络
            docker_command = DockerNetworkUtils.ensure_bridge_network(docker_command)
            
            # 2. 移除可能冲突的网络配置
            # 移除host网络配置
            docker_command = re.sub(r'--network\s+host\s*', '--network bridge ', docker_command)
            docker_command = re.sub(r'--net\s+host\s*', '--net bridge ', docker_command)
            
            # 3. 移除重复的网络配置
            # 查找所有网络配置
            network_configs = re.findall(r'--network\s+\w+', docker_command)
            if len(network_configs) > 1:
                # 移除所有网络配置，然后添加一个bridge配置
                docker_command = re.sub(r'--network\s+\w+\s*', '', docker_command)
                docker_command = DockerNetworkUtils.ensure_bridge_network(docker_command)
                logger.info("Removed duplicate network configurations")
            
            # 4. 确保端口映射正确
            # 检查是否有端口映射但没有网络配置的情况
            if '-p ' in docker_command and '--network' not in docker_command and '--net' not in docker_command:
                docker_command = DockerNetworkUtils.ensure_bridge_network(docker_command)
                logger.info("Added network configuration for port mapping")
            
            return docker_command
            
        except Exception as e:
            logger.error(f"Failed to fix network issues: {e}")
            return docker_command
    
    @staticmethod
    def generate_network_info_summary(docker_command: str) -> Dict[str, Any]:
        """
        生成网络配置信息摘要
        
        Args:
            docker_command: Docker命令
            
        Returns:
            Dict: 网络配置摘要
        """
        validation = DockerNetworkUtils.validate_network_configuration(docker_command)
        
        # 提取端口映射信息
        port_mappings = re.findall(r'-p\s+(\d+):(\d+)', docker_command)
        
        # 提取容器名称
        name_match = re.search(r'--name\s+(\S+)', docker_command)
        container_name = name_match.group(1) if name_match else "unknown"
        
        summary = {
            'container_name': container_name,
            'network_type': validation['network_type'],
            'uses_bridge': validation['uses_bridge'],
            'port_mappings': port_mappings,
            'capture_friendly': validation['uses_bridge'],
            'issues': validation['issues'],
            'recommendations': []
        }
        
        # 生成建议
        if not validation['uses_bridge']:
            summary['recommendations'].append("建议使用bridge网络以便抓包工具捕获流量")
        
        if not port_mappings:
            summary['recommendations'].append("建议添加端口映射以便外部访问")
        
        if validation['issues']:
            summary['recommendations'].append("修复网络配置问题以确保最佳抓包效果")
        
        return summary

# 全局工具实例
docker_network_utils = DockerNetworkUtils()

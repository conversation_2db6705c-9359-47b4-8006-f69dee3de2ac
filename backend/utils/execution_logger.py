"""
执行日志助手类
"""
import asyncio
from typing import Optional
from models.execution_log import LogLevel, LogCategory
from services.execution_log_service import execution_log_service


class ExecutionLogger:
    """执行日志助手类"""
    
    def __init__(self, 
                 task_id: Optional[str] = None,
                 test_case_id: Optional[str] = None,
                 execution_id: Optional[str] = None,
                 database_config_id: Optional[int] = None,
                 database_type: Optional[str] = None,
                 target_host: Optional[str] = None,
                 target_port: Optional[int] = None):
        self.task_id = task_id
        self.test_case_id = test_case_id
        self.execution_id = execution_id
        self.database_config_id = database_config_id
        self.database_type = database_type
        self.target_host = target_host
        self.target_port = target_port
    
    def _log_sync(self, level: LogLevel, category: LogCategory, message: str, **kwargs):
        """同步日志记录（在新的事件循环中运行）"""
        try:
            # 创建新的事件循环来运行异步日志记录
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                loop.run_until_complete(self._log_async(level, category, message, **kwargs))
            finally:
                loop.close()
        except Exception as e:
            # 如果日志记录失败，至少输出到标准日志
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Failed to log to database: {e}, Original message: {message}")
    
    async def _log_async(self, level: LogLevel, category: LogCategory, message: str, **kwargs):
        """异步日志记录"""
        await execution_log_service.log(
            level=level,
            category=category,
            message=message,
            task_id=self.task_id,
            test_case_id=self.test_case_id,
            execution_id=self.execution_id,
            database_config_id=self.database_config_id,
            database_type=self.database_type,
            target_host=self.target_host,
            target_port=self.target_port,
            **kwargs
        )
    
    # 便捷方法 - 同步版本
    def debug(self, message: str, category: LogCategory = LogCategory.SYSTEM, **kwargs):
        """记录DEBUG级别日志"""
        self._log_sync(LogLevel.DEBUG, category, message, **kwargs)
    
    def info(self, message: str, category: LogCategory = LogCategory.SYSTEM, **kwargs):
        """记录INFO级别日志"""
        self._log_sync(LogLevel.INFO, category, message, **kwargs)
    
    def warning(self, message: str, category: LogCategory = LogCategory.SYSTEM, **kwargs):
        """记录WARNING级别日志"""
        self._log_sync(LogLevel.WARNING, category, message, **kwargs)
    
    def error(self, message: str, category: LogCategory = LogCategory.ERROR_HANDLING, **kwargs):
        """记录ERROR级别日志"""
        self._log_sync(LogLevel.ERROR, category, message, **kwargs)
    
    def critical(self, message: str, category: LogCategory = LogCategory.ERROR_HANDLING, **kwargs):
        """记录CRITICAL级别日志"""
        self._log_sync(LogLevel.CRITICAL, category, message, **kwargs)
    
    # 便捷方法 - 异步版本
    async def debug_async(self, message: str, category: LogCategory = LogCategory.SYSTEM, **kwargs):
        """记录DEBUG级别日志（异步）"""
        await self._log_async(LogLevel.DEBUG, category, message, **kwargs)
    
    async def info_async(self, message: str, category: LogCategory = LogCategory.SYSTEM, **kwargs):
        """记录INFO级别日志（异步）"""
        await self._log_async(LogLevel.INFO, category, message, **kwargs)
    
    async def warning_async(self, message: str, category: LogCategory = LogCategory.SYSTEM, **kwargs):
        """记录WARNING级别日志（异步）"""
        await self._log_async(LogLevel.WARNING, category, message, **kwargs)
    
    async def error_async(self, message: str, category: LogCategory = LogCategory.ERROR_HANDLING, **kwargs):
        """记录ERROR级别日志（异步）"""
        await self._log_async(LogLevel.ERROR, category, message, **kwargs)
    
    async def critical_async(self, message: str, category: LogCategory = LogCategory.ERROR_HANDLING, **kwargs):
        """记录CRITICAL级别日志（异步）"""
        await self._log_async(LogLevel.CRITICAL, category, message, **kwargs)
    
    # 特定分类的便捷方法
    def capture_info(self, message: str, capture_file: Optional[str] = None, **kwargs):
        """记录抓包相关信息"""
        self._log_sync(LogLevel.INFO, LogCategory.CAPTURE, message, capture_file=capture_file, **kwargs)
    
    def capture_error(self, message: str, capture_file: Optional[str] = None, error_details: Optional[str] = None, **kwargs):
        """记录抓包相关错误"""
        self._log_sync(LogLevel.ERROR, LogCategory.CAPTURE, message, 
                      capture_file=capture_file, error_details=error_details, **kwargs)
    
    def sql_info(self, message: str, sql_query: Optional[str] = None, **kwargs):
        """记录SQL执行信息"""
        self._log_sync(LogLevel.INFO, LogCategory.SQL_EXECUTION, message, sql_query=sql_query, **kwargs)
    
    def sql_error(self, message: str, sql_query: Optional[str] = None, error_details: Optional[str] = None, **kwargs):
        """记录SQL执行错误"""
        self._log_sync(LogLevel.ERROR, LogCategory.SQL_EXECUTION, message, 
                      sql_query=sql_query, error_details=error_details, **kwargs)
    
    def db_connection_info(self, message: str, **kwargs):
        """记录数据库连接信息"""
        self._log_sync(LogLevel.INFO, LogCategory.DATABASE_CONNECTION, message, **kwargs)
    
    def db_connection_error(self, message: str, error_details: Optional[str] = None, **kwargs):
        """记录数据库连接错误"""
        self._log_sync(LogLevel.ERROR, LogCategory.DATABASE_CONNECTION, message, 
                      error_details=error_details, **kwargs)
    
    def file_operation_info(self, message: str, capture_file: Optional[str] = None, **kwargs):
        """记录文件操作信息"""
        self._log_sync(LogLevel.INFO, LogCategory.FILE_OPERATION, message, capture_file=capture_file, **kwargs)
    
    def file_operation_error(self, message: str, capture_file: Optional[str] = None, error_details: Optional[str] = None, **kwargs):
        """记录文件操作错误"""
        self._log_sync(LogLevel.ERROR, LogCategory.FILE_OPERATION, message, 
                      capture_file=capture_file, error_details=error_details, **kwargs)
    
    def test_execution_info(self, message: str, **kwargs):
        """记录测试执行信息"""
        self._log_sync(LogLevel.INFO, LogCategory.TEST_EXECUTION, message, **kwargs)
    
    def test_execution_error(self, message: str, error_details: Optional[str] = None, **kwargs):
        """记录测试执行错误"""
        self._log_sync(LogLevel.ERROR, LogCategory.TEST_EXECUTION, message, 
                      error_details=error_details, **kwargs)
    
    # 异步版本的特定分类方法
    async def capture_info_async(self, message: str, capture_file: Optional[str] = None, **kwargs):
        """记录抓包相关信息（异步）"""
        await self._log_async(LogLevel.INFO, LogCategory.CAPTURE, message, capture_file=capture_file, **kwargs)
    
    async def capture_error_async(self, message: str, capture_file: Optional[str] = None, error_details: Optional[str] = None, **kwargs):
        """记录抓包相关错误（异步）"""
        await self._log_async(LogLevel.ERROR, LogCategory.CAPTURE, message, 
                             capture_file=capture_file, error_details=error_details, **kwargs)
    
    async def sql_info_async(self, message: str, sql_query: Optional[str] = None, **kwargs):
        """记录SQL执行信息（异步）"""
        await self._log_async(LogLevel.INFO, LogCategory.SQL_EXECUTION, message, sql_query=sql_query, **kwargs)
    
    async def sql_error_async(self, message: str, sql_query: Optional[str] = None, error_details: Optional[str] = None, **kwargs):
        """记录SQL执行错误（异步）"""
        await self._log_async(LogLevel.ERROR, LogCategory.SQL_EXECUTION, message, 
                             sql_query=sql_query, error_details=error_details, **kwargs)
    
    async def db_connection_info_async(self, message: str, **kwargs):
        """记录数据库连接信息（异步）"""
        await self._log_async(LogLevel.INFO, LogCategory.DATABASE_CONNECTION, message, **kwargs)
    
    async def db_connection_error_async(self, message: str, error_details: Optional[str] = None, **kwargs):
        """记录数据库连接错误（异步）"""
        await self._log_async(LogLevel.ERROR, LogCategory.DATABASE_CONNECTION, message, 
                             error_details=error_details, **kwargs)
    
    async def file_operation_info_async(self, message: str, capture_file: Optional[str] = None, **kwargs):
        """记录文件操作信息（异步）"""
        await self._log_async(LogLevel.INFO, LogCategory.FILE_OPERATION, message, capture_file=capture_file, **kwargs)
    
    async def file_operation_error_async(self, message: str, capture_file: Optional[str] = None, error_details: Optional[str] = None, **kwargs):
        """记录文件操作错误（异步）"""
        await self._log_async(LogLevel.ERROR, LogCategory.FILE_OPERATION, message, 
                             capture_file=capture_file, error_details=error_details, **kwargs)
    
    async def test_execution_info_async(self, message: str, **kwargs):
        """记录测试执行信息（异步）"""
        await self._log_async(LogLevel.INFO, LogCategory.TEST_EXECUTION, message, **kwargs)
    
    async def test_execution_error_async(self, message: str, error_details: Optional[str] = None, **kwargs):
        """记录测试执行错误（异步）"""
        await self._log_async(LogLevel.ERROR, LogCategory.TEST_EXECUTION, message,
                             error_details=error_details, **kwargs)

    async def system_info_async(self, message: str, **kwargs):
        """记录系统信息（异步）"""
        await self._log_async(LogLevel.INFO, LogCategory.SYSTEM, message, **kwargs)

    async def system_error_async(self, message: str, error_details: Optional[str] = None, **kwargs):
        """记录系统错误（异步）"""
        await self._log_async(LogLevel.ERROR, LogCategory.SYSTEM, message,
                             error_details=error_details, **kwargs)

"""
任务执行日志记录器
为异步任务提供便捷的日志记录功能
"""

import logging
import platform
import sys
from typing import Dict, Any, List, Optional
from contextlib import asynccontextmanager

from services.async_task_execution_log_service import (
    AsyncTaskExecutionLogService, AsyncTaskExecutionLog,
    TaskStatus, ExecutorType, async_task_execution_log_service
)
from utils.timezone_utils import get_current_time

logger = logging.getLogger(__name__)


class TaskExecutionLogger:
    """任务执行日志记录器"""
    
    def __init__(self, task_id: str, task_type: str, task_name: Optional[str] = None):
        self.task_id = task_id
        self.task_type = task_type
        self.task_name = task_name
        self.service = async_task_execution_log_service
        self._initialized = False
    
    async def initialize(self, **kwargs) -> bool:
        """初始化任务记录"""
        try:
            task_log = AsyncTaskExecutionLog(
                task_id=self.task_id,
                task_type=self.task_type,
                task_name=self.task_name,
                **kwargs
            )
            
            success = await self.service.create_task_log(task_log)
            self._initialized = success
            
            if success:
                logger.info(f"任务执行记录初始化成功: {self.task_id}")
            else:
                logger.error(f"任务执行记录初始化失败: {self.task_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"初始化任务执行记录异常: {e}")
            return False
    
    async def start_execution(self, executor_type: ExecutorType, 
                            executor_path: Optional[str] = None,
                            executor_selection_reason: Optional[str] = None) -> bool:
        """开始执行"""
        if not self._initialized:
            logger.warning(f"任务记录未初始化: {self.task_id}")
            return False
        
        return await self.service.record_task_start(
            self.task_id, executor_type, executor_path, executor_selection_reason
        )
    
    async def update_progress(self, progress: int, current_step: Optional[str] = None) -> bool:
        """更新进度"""
        if not self._initialized:
            return False
        
        return await self.service.update_task_status(
            self.task_id, TaskStatus.RUNNING, current_step, progress
        )
    
    async def complete_execution(self, execution_result: Dict[str, Any],
                               output_files: Optional[List[str]] = None,
                               capture_files: Optional[List[str]] = None) -> bool:
        """完成执行"""
        if not self._initialized:
            return False
        
        return await self.service.record_task_completion(
            self.task_id, execution_result, output_files, capture_files
        )
    
    async def fail_execution(self, error_code: Optional[str] = None,
                           error_message: Optional[str] = None,
                           error_details: Optional[Dict[str, Any]] = None,
                           fallback_from_executor: Optional[ExecutorType] = None) -> bool:
        """执行失败"""
        if not self._initialized:
            return False
        
        return await self.service.record_task_failure(
            self.task_id, error_code, error_message, error_details, fallback_from_executor
        )
    
    async def record_executor_fallback(self, from_executor: ExecutorType,
                                     to_executor: ExecutorType, reason: str) -> bool:
        """记录执行器回退"""
        if not self._initialized:
            return False
        
        return await self.service.record_executor_fallback(
            self.task_id, from_executor, to_executor, reason
        )
    
    async def increment_retry(self) -> bool:
        """增加重试次数"""
        if not self._initialized:
            return False
        
        return await self.service.increment_retry_count(self.task_id)
    
    @asynccontextmanager
    async def execution_context(self, executor_type: ExecutorType,
                              executor_path: Optional[str] = None,
                              executor_selection_reason: Optional[str] = None):
        """执行上下文管理器"""
        try:
            # 开始执行
            await self.start_execution(executor_type, executor_path, executor_selection_reason)
            yield self
            
        except Exception as e:
            # 记录失败
            await self.fail_execution(
                error_code="EXECUTION_ERROR",
                error_message=str(e),
                error_details={"exception_type": type(e).__name__}
            )
            raise
    
    def get_task_id(self) -> str:
        """获取任务ID"""
        return self.task_id


def create_task_logger(task_id: str, task_type: str, task_name: Optional[str] = None) -> TaskExecutionLogger:
    """创建任务执行日志记录器"""
    return TaskExecutionLogger(task_id, task_type, task_name)


class BatchTaskExecutionLogger:
    """批量任务执行日志记录器"""
    
    def __init__(self, batch_id: str, batch_name: str, total_count: int):
        self.batch_id = batch_id
        self.batch_name = batch_name
        self.total_count = total_count
        self.current_index = 0
        self.task_loggers: Dict[str, TaskExecutionLogger] = {}
    
    async def create_task_logger(self, task_id: str, task_type: str, 
                               task_name: Optional[str] = None,
                               **kwargs) -> TaskExecutionLogger:
        """为批量中的单个任务创建日志记录器"""
        logger_instance = TaskExecutionLogger(task_id, task_type, task_name)
        
        # 添加批量相关信息
        batch_kwargs = {
            'batch_id': self.batch_id,
            'batch_name': self.batch_name,
            'batch_total_count': self.total_count,
            'batch_current_index': self.current_index,
            **kwargs
        }
        
        await logger_instance.initialize(**batch_kwargs)
        self.task_loggers[task_id] = logger_instance
        self.current_index += 1
        
        return logger_instance
    
    def get_task_logger(self, task_id: str) -> Optional[TaskExecutionLogger]:
        """获取指定任务的日志记录器"""
        return self.task_loggers.get(task_id)
    
    async def get_batch_statistics(self) -> Dict[str, Any]:
        """获取批量执行统计"""
        try:
            service = async_task_execution_log_service
            
            # 查询批量任务统计
            sql = """
                SELECT 
                    status,
                    COUNT(*) as count,
                    AVG(duration_seconds) as avg_duration,
                    SUM(CASE WHEN executor_type = 'C' THEN 1 ELSE 0 END) as c_executor_count,
                    SUM(CASE WHEN executor_type = 'PYTHON' THEN 1 ELSE 0 END) as python_executor_count,
                    SUM(CASE WHEN executor_type = 'JAVA' THEN 1 ELSE 0 END) as java_executor_count,
                    COUNT(CASE WHEN fallback_from_executor IS NOT NULL THEN 1 END) as fallback_count
                FROM async_task_execution_logs 
                WHERE batch_id = %s
                GROUP BY status
            """
            
            result = await service.mysql_service.execute_query(sql, (self.batch_id,))
            
            statistics = {
                'batch_id': self.batch_id,
                'batch_name': self.batch_name,
                'total_count': self.total_count,
                'status_breakdown': result.get('data', []) if result else [],
                'task_count': len(self.task_loggers)
            }
            
            return statistics
            
        except Exception as e:
            logger.error(f"获取批量执行统计失败: {e}")
            return {
                'batch_id': self.batch_id,
                'batch_name': self.batch_name,
                'total_count': self.total_count,
                'error': str(e)
            }


def create_batch_task_logger(batch_id: str, batch_name: str, total_count: int) -> BatchTaskExecutionLogger:
    """创建批量任务执行日志记录器"""
    return BatchTaskExecutionLogger(batch_id, batch_name, total_count)


class ExecutorSelector:
    """执行器选择器 - 根据环境和配置选择最佳执行器"""
    
    @staticmethod
    def select_executor(database_type: str, prefer_c: bool = False) -> tuple[ExecutorType, str]:
        """选择执行器"""
        try:
            # 检查C执行器可用性
            c_available = ExecutorSelector._check_c_executor_available(database_type)
            
            if prefer_c and c_available:
                return ExecutorType.C, f"用户偏好C执行器且{database_type} C执行器可用"
            elif c_available and ExecutorSelector._should_use_c_executor():
                return ExecutorType.C, f"系统推荐使用C执行器，性能更优"
            else:
                fallback_reason = "C执行器不可用" if not c_available else "系统推荐使用Python执行器"
                return ExecutorType.PYTHON, f"使用Python执行器: {fallback_reason}"
                
        except Exception as e:
            logger.warning(f"执行器选择异常: {e}")
            return ExecutorType.PYTHON, f"执行器选择异常，回退到Python: {str(e)}"
    
    @staticmethod
    def _check_c_executor_available(database_type: str) -> bool:
        """检查C执行器是否可用"""
        try:
            from utils.environment_detector import get_environment_detector
            env_detector = get_environment_detector()
            executor_path = env_detector.get_c_executor_path(database_type)
            return executor_path is not None
        except Exception:
            return False
    
    @staticmethod
    def _should_use_c_executor() -> bool:
        """判断是否应该使用C执行器"""
        # 可以根据系统负载、历史性能等因素决定
        # 这里简化为总是推荐C执行器（如果可用）
        return True

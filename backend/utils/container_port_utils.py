"""
容器端口映射工具
处理Docker容器的端口映射逻辑
"""

import logging
from typing import Dict, Optional

logger = logging.getLogger(__name__)

class ContainerPortUtils:
    """容器端口映射工具类"""
    
    # 数据库容器内部标准端口映射
    CONTAINER_PORTS = {
        'postgresql': 5432,
        'postgres': 5432,
        'mysql': 3306,
        'mongodb': 27017,
        'mongo': 27017,
        'oracle': 1521,
        'redis': 6379,
        'elasticsearch': 9200,
        'cassandra': 9042,
        'mariadb': 3306
    }
    
    @staticmethod
    def get_container_port(database_type: str) -> Optional[int]:
        """
        获取数据库容器内部端口
        
        Args:
            database_type: 数据库类型
            
        Returns:
            int: 容器内部端口，如果未知类型返回None
        """
        return ContainerPortUtils.CONTAINER_PORTS.get(database_type.lower())
    
    @staticmethod
    def should_use_container_port(database_type: str, deployment_type: str) -> bool:
        """
        判断是否应该使用容器内部端口进行抓包
        
        Args:
            database_type: 数据库类型
            deployment_type: 部署类型 ('docker', 'native', etc.)
            
        Returns:
            bool: 是否使用容器内部端口
        """
        return (
            deployment_type == 'docker' and 
            database_type.lower() in ContainerPortUtils.CONTAINER_PORTS
        )
    
    @staticmethod
    def get_capture_port(database_type: str, deployment_type: str, host_port: int) -> int:
        """
        获取抓包应该监听的端口
        
        Args:
            database_type: 数据库类型
            deployment_type: 部署类型
            host_port: 主机端口
            
        Returns:
            int: 抓包监听端口
        """
        if ContainerPortUtils.should_use_container_port(database_type, deployment_type):
            container_port = ContainerPortUtils.get_container_port(database_type)
            if container_port:
                logger.info(f"{database_type} Docker容器：使用容器内部端口 {container_port} 而不是主机端口 {host_port}")
                return container_port
        
        logger.info(f"{database_type} {deployment_type}部署：使用配置端口 {host_port}")
        return host_port
    
    @staticmethod
    def generate_filter_expression(
        database_type: str, 
        deployment_type: str, 
        host_port: int,
        include_host: Optional[str] = None
    ) -> str:
        """
        生成tcpdump过滤表达式
        
        Args:
            database_type: 数据库类型
            deployment_type: 部署类型
            host_port: 主机端口
            include_host: 可选的主机过滤
            
        Returns:
            str: tcpdump过滤表达式
        """
        capture_port = ContainerPortUtils.get_capture_port(database_type, deployment_type, host_port)
        
        if include_host:
            filter_expr = f"tcp port {capture_port} and host {include_host}"
        else:
            filter_expr = f"tcp port {capture_port}"
        
        return filter_expr
    
    @staticmethod
    def get_port_mapping_info(database_type: str, deployment_type: str, host_port: int) -> Dict[str, any]:
        """
        获取端口映射信息摘要
        
        Args:
            database_type: 数据库类型
            deployment_type: 部署类型
            host_port: 主机端口
            
        Returns:
            Dict: 端口映射信息
        """
        container_port = ContainerPortUtils.get_container_port(database_type)
        capture_port = ContainerPortUtils.get_capture_port(database_type, deployment_type, host_port)
        uses_container_port = ContainerPortUtils.should_use_container_port(database_type, deployment_type)
        
        return {
            'database_type': database_type,
            'deployment_type': deployment_type,
            'host_port': host_port,
            'container_port': container_port,
            'capture_port': capture_port,
            'uses_container_port': uses_container_port,
            'port_mapping': f"{host_port} -> {container_port}" if uses_container_port else f"{host_port}",
            'capture_strategy': f"监听容器内部端口 {capture_port}" if uses_container_port else f"监听主机端口 {capture_port}"
        }
    
    @staticmethod
    def validate_port_configuration(database_type: str, deployment_type: str, host_port: int) -> Dict[str, any]:
        """
        验证端口配置
        
        Args:
            database_type: 数据库类型
            deployment_type: 部署类型
            host_port: 主机端口
            
        Returns:
            Dict: 验证结果
        """
        result = {
            'valid': True,
            'warnings': [],
            'recommendations': [],
            'port_info': ContainerPortUtils.get_port_mapping_info(database_type, deployment_type, host_port)
        }
        
        container_port = ContainerPortUtils.get_container_port(database_type)
        
        # 检查Docker部署的端口配置
        if deployment_type == 'docker':
            if not container_port:
                result['warnings'].append(f"未知的数据库类型 {database_type}，可能影响抓包效果")
            elif host_port == container_port:
                result['recommendations'].append(f"主机端口与容器端口相同 ({host_port})，建议使用不同端口避免冲突")
        
        # 检查标准端口使用
        if container_port and host_port != container_port and deployment_type != 'docker':
            result['recommendations'].append(f"{database_type} 标准端口是 {container_port}，当前使用 {host_port}")
        
        return result

# 全局工具实例
container_port_utils = ContainerPortUtils()

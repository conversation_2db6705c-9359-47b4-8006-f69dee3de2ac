#!/usr/bin/env python3
"""
环境检测和C执行器管理工具
支持本地调试和服务器部署的兼容性
"""

import os
import platform
import subprocess
import logging
from pathlib import Path
from typing import Dict, Optional, Tuple

logger = logging.getLogger(__name__)

class EnvironmentDetector:
    """环境检测器"""
    
    def __init__(self):
        self.system = platform.system()
        self.machine = platform.machine()
        self.is_docker = self._detect_docker()
        self.is_macos = self.system == "Darwin"
        self.is_linux = self.system == "Linux"
        
    def _detect_docker(self) -> bool:
        """检测是否在Docker容器中运行"""
        try:
            # 检查/.dockerenv文件
            if os.path.exists('/.dockerenv'):
                return True
                
            # 检查/proc/1/cgroup
            if os.path.exists('/proc/1/cgroup'):
                with open('/proc/1/cgroup', 'r') as f:
                    content = f.read()
                    if 'docker' in content or 'containerd' in content:
                        return True
                        
            return False
        except Exception:
            return False
    
    def get_environment_info(self) -> Dict[str, str]:
        """获取环境信息"""
        return {
            'system': self.system,
            'machine': self.machine,
            'is_docker': str(self.is_docker),
            'is_macos': str(self.is_macos),
            'is_linux': str(self.is_linux),
            'python_version': platform.python_version(),
            'platform': platform.platform()
        }
    
    def get_c_executor_path(self, database_type: str) -> Optional[str]:
        """获取C执行器路径"""
        base_path = Path(__file__).parent.parent / "c_executors"
        
        executor_map = {
            'mongodb': 'mongo_libmongoc_executor',
            'gaussdb': 'gaussdb_libpq_executor',
            'postgresql': 'gaussdb_libpq_executor'  # 复用GaussDB执行器
        }
        
        if database_type not in executor_map:
            return None
            
        executor_name = executor_map[database_type]
        executor_path = base_path / executor_name
        
        # 检查执行器是否存在且可执行
        if executor_path.exists() and os.access(executor_path, os.X_OK):
            # 检查架构兼容性
            if self._check_executor_compatibility(executor_path):
                return str(executor_path)
                
        return None
    
    def _check_executor_compatibility(self, executor_path: Path) -> bool:
        """检查执行器架构兼容性"""
        try:
            # 尝试获取文件类型信息
            result = subprocess.run(['file', str(executor_path)], 
                                  capture_output=True, text=True, timeout=5)
            
            if result.returncode != 0:
                return False
                
            file_info = result.stdout.lower()
            
            # 检查架构兼容性
            if self.is_linux:
                # Linux环境需要ELF格式
                if 'elf' not in file_info:
                    logger.warning(f"Executor {executor_path} is not ELF format for Linux")
                    return False
                    
                # 检查架构匹配
                if 'x86-64' in file_info or 'x86_64' in file_info:
                    return True
                elif 'aarch64' in file_info and 'aarch64' in self.machine:
                    return True
                else:
                    logger.warning(f"Architecture mismatch: executor={file_info}, system={self.machine}")
                    return False
                    
            elif self.is_macos:
                # macOS环境需要Mach-O格式
                if 'mach-o' not in file_info:
                    logger.warning(f"Executor {executor_path} is not Mach-O format for macOS")
                    return False
                return True
                
            return False
            
        except Exception as e:
            logger.error(f"Error checking executor compatibility: {str(e)}")
            return False
    
    def should_use_c_executor(self, database_type: str) -> Tuple[bool, Optional[str]]:
        """判断是否应该使用C执行器"""
        try:
            executor_path = self.get_c_executor_path(database_type)
            
            if executor_path is None:
                logger.info(f"No compatible C executor found for {database_type}")
                return False, None
                
            # 尝试执行简单的测试
            # 不同的C执行器有不同的测试方式
            if database_type in ['mongodb']:
                # MongoDB C执行器不支持--version，使用--help测试
                result = subprocess.run([executor_path, '--help'],
                                      capture_output=True, text=True, timeout=5)
                # MongoDB C执行器在无效参数时返回错误信息，但这表明它是可执行的
                if result.returncode != 0 and 'Invalid command' in result.stdout:
                    logger.info(f"C executor for {database_type} is available and working")
                    return True, executor_path
                elif result.returncode == 0:
                    logger.info(f"C executor for {database_type} is available and working")
                    return True, executor_path
                else:
                    logger.warning(f"C executor for {database_type} failed test: {result.stderr}")
                    return False, None
            else:
                # 其他数据库使用--version测试
                result = subprocess.run([executor_path, '--version'],
                                      capture_output=True, text=True, timeout=5)

                if result.returncode == 0:
                    logger.info(f"C executor for {database_type} is available and working")
                    return True, executor_path
                else:
                    logger.warning(f"C executor for {database_type} failed test: {result.stderr}")
                    return False, None
                
        except subprocess.TimeoutExpired:
            logger.warning(f"C executor for {database_type} test timed out")
            return False, None
        except Exception as e:
            logger.error(f"Error testing C executor for {database_type}: {str(e)}")
            return False, None
    
    def get_recommended_settings(self) -> Dict[str, any]:
        """获取推荐的环境设置"""
        settings = {
            'use_c_executors': {},
            'capture_interface_detection': 'auto',
            'log_level': 'INFO'
        }
        
        # 检查各数据库类型的C执行器可用性
        for db_type in ['mongodb', 'gaussdb', 'postgresql']:
            use_c, path = self.should_use_c_executor(db_type)
            settings['use_c_executors'][db_type] = {
                'enabled': use_c,
                'path': path
            }
        
        # 根据环境调整设置
        if self.is_docker:
            settings['capture_interface_detection'] = 'docker_aware'
            settings['log_level'] = 'DEBUG'  # Docker环境中启用详细日志
        elif self.is_macos:
            settings['capture_interface_detection'] = 'macos_optimized'
        
        return settings

# 全局实例
env_detector = EnvironmentDetector()

def get_environment_detector() -> EnvironmentDetector:
    """获取环境检测器实例"""
    return env_detector

def log_environment_info():
    """记录环境信息"""
    info = env_detector.get_environment_info()
    settings = env_detector.get_recommended_settings()
    
    logger.info("=== Environment Detection Results ===")
    for key, value in info.items():
        logger.info(f"{key}: {value}")
    
    logger.info("=== C Executor Availability ===")
    for db_type, config in settings['use_c_executors'].items():
        status = "AVAILABLE" if config['enabled'] else "UNAVAILABLE"
        logger.info(f"{db_type}: {status}")
        if config['path']:
            logger.info(f"  Path: {config['path']}")
    
    logger.info("=== Recommended Settings ===")
    logger.info(f"Capture interface detection: {settings['capture_interface_detection']}")
    logger.info(f"Log level: {settings['log_level']}")
    logger.info("=== End Environment Detection ===")

if __name__ == "__main__":
    # 测试环境检测
    logging.basicConfig(level=logging.INFO)
    log_environment_info()

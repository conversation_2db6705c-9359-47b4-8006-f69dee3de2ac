"""
统一路径管理器 - 管理所有项目路径
"""

import os


class PathManager:
    """统一路径管理器"""

    _instance = None
    _initialized = False

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(PathManager, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        if not self._initialized:
            self._setup_paths()
            self._initialized = True

    def _setup_paths(self):
        """设置所有路径"""
        # 获取当前文件的绝对路径
        current_file = os.path.abspath(__file__)
        
        # 检测运行环境
        if os.path.exists("/app") and "/app" in current_file:
            # Docker环境：使用 /app 作为根目录
            self.backend_root = "/app"
            self.project_root = "/app"
            self.captures_dir = "/app/captures"
            self.logs_dir = "/app/logs"
        else:
            # 本地开发环境：使用传统的目录结构
            self.backend_root = os.path.dirname(os.path.dirname(current_file))  # backend目录
            self.project_root = os.path.dirname(self.backend_root)  # 项目根目录
            self.captures_dir = os.path.join(self.backend_root, "captures")
            self.logs_dir = os.path.join(self.project_root, "logs")

        # 确保目录存在
        os.makedirs(self.captures_dir, exist_ok=True)
        os.makedirs(self.logs_dir, exist_ok=True)

    # ===== 抓包相关 =====
    def get_captures_dir(self) -> str:
        """获取抓包文件目录"""
        return self.captures_dir

    def get_capture_file_path(self, filename: str) -> str:
        """获取抓包文件的完整路径"""
        return os.path.join(self.captures_dir, filename)

    def get_backend_root(self) -> str:
        """获取backend根目录"""
        return self.backend_root

    def get_project_root(self) -> str:
        """获取项目根目录"""
        return self.project_root

    def resolve_capture_file_path(self, file_path: str) -> str:
        """解析抓包文件路径，返回绝对路径用于文件操作"""
        if os.path.isabs(file_path):
            # 如果是绝对路径，检查是否在我们的captures目录内
            # 如果不在，则提取文件名并重新构造到正确的captures目录
            if file_path.startswith(self.captures_dir):
                return file_path
            else:
                # 绝对路径但不在我们的captures目录，提取文件名
                filename = os.path.basename(file_path)
                return os.path.join(self.captures_dir, filename)

        # 如果是相对路径，统一使用captures目录
        # 处理 "captures/filename" 或 "filename" 格式
        if file_path.startswith("captures/"):
            filename = file_path[9:]  # 去掉 "captures/" 前缀
        else:
            filename = os.path.basename(file_path)

        return os.path.join(self.captures_dir, filename)

    def get_relative_capture_path(self, filename: str) -> str:
        """获取相对于backend目录的抓包文件路径（captures/filename）"""
        filename = os.path.basename(filename)
        return os.path.join("captures", filename)
    
    def debug_paths(self) -> dict:
        """返回路径调试信息"""
        return {
            "backend_root": self.backend_root,
            "project_root": self.project_root,
            "captures_dir": self.captures_dir,
            "logs_dir": self.logs_dir,
            "is_docker": os.path.exists("/app"),
            "current_working_dir": os.getcwd(),
            "captures_dir_exists": os.path.exists(self.captures_dir)
        }

    # ===== 日志相关 =====
    def get_logs_dir(self) -> str:
        """获取统一日志目录（项目根目录 logs）"""
        return self.logs_dir

    def get_log_subdir(self, name: str) -> str:
        """获取日志子目录路径，如 app/error/debug/worker/startup"""
        subdir = os.path.join(self.logs_dir, name)
        os.makedirs(subdir, exist_ok=True)
        return subdir


# 创建全局单例实例
path_manager = PathManager()

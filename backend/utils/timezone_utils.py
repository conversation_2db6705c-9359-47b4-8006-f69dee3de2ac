"""
时区工具模块
统一处理项目中的时区相关操作，确保所有时间都使用+8时区（Asia/Shanghai）
"""

import os
from datetime import datetime, timezone, timedelta
from typing import Optional, Union

# 设置系统时区环境变量
os.environ['TZ'] = 'Asia/Shanghai'

# 定义+8时区
CHINA_TIMEZONE = timezone(timedelta(hours=8))
TIMEZONE_NAME = 'Asia/Shanghai'

def get_current_time() -> datetime:
    """
    获取当前时间（+8时区）
    
    Returns:
        datetime: 带有+8时区信息的当前时间
    """
    return datetime.now(CHINA_TIMEZONE)

def format_time(dt: Optional[datetime] = None, format_str: str = None) -> str:
    """
    格式化时间为字符串（+8时区）
    
    Args:
        dt: 要格式化的时间，如果为None则使用当前时间
        format_str: 格式化字符串，如果为None则使用ISO格式
        
    Returns:
        str: 格式化后的时间字符串
    """
    if dt is None:
        dt = get_current_time()
    elif dt.tzinfo is None:
        # 如果没有时区信息，假设为+8时区
        dt = dt.replace(tzinfo=CHINA_TIMEZONE)
    elif dt.tzinfo != CHINA_TIMEZONE:
        # 转换到+8时区
        dt = dt.astimezone(CHINA_TIMEZONE)
    
    if format_str is None:
        return dt.isoformat()
    else:
        return dt.strftime(format_str)

def parse_time(time_str: str) -> datetime:
    """
    解析时间字符串为datetime对象（+8时区）
    
    Args:
        time_str: 时间字符串
        
    Returns:
        datetime: 带有+8时区信息的datetime对象
    """
    try:
        # 尝试解析ISO格式
        dt = datetime.fromisoformat(time_str.replace('Z', '+00:00'))
        return dt.astimezone(CHINA_TIMEZONE)
    except ValueError:
        # 尝试其他常见格式
        formats = [
            '%Y-%m-%d %H:%M:%S',
            '%Y-%m-%d %H:%M:%S.%f',
            '%Y-%m-%dT%H:%M:%S',
            '%Y-%m-%dT%H:%M:%S.%f',
        ]
        
        for fmt in formats:
            try:
                dt = datetime.strptime(time_str, fmt)
                return dt.replace(tzinfo=CHINA_TIMEZONE)
            except ValueError:
                continue
        
        raise ValueError(f"无法解析时间字符串: {time_str}")

def to_china_timezone(dt: datetime) -> datetime:
    """
    将datetime对象转换为+8时区
    
    Args:
        dt: 要转换的datetime对象
        
    Returns:
        datetime: 转换后的+8时区datetime对象
    """
    if dt.tzinfo is None:
        # 如果没有时区信息，假设为+8时区
        return dt.replace(tzinfo=CHINA_TIMEZONE)
    else:
        # 转换到+8时区
        return dt.astimezone(CHINA_TIMEZONE)

def get_timestamp() -> float:
    """
    获取当前时间戳（+8时区）
    
    Returns:
        float: 时间戳
    """
    return get_current_time().timestamp()

def from_timestamp(timestamp: float) -> datetime:
    """
    从时间戳创建datetime对象（+8时区）
    
    Args:
        timestamp: 时间戳
        
    Returns:
        datetime: 带有+8时区信息的datetime对象
    """
    return datetime.fromtimestamp(timestamp, tz=CHINA_TIMEZONE)

def get_mysql_timezone_setting() -> str:
    """
    获取MySQL时区设置命令
    
    Returns:
        str: MySQL时区设置命令
    """
    return "SET time_zone='+08:00'"

def get_postgres_timezone_setting() -> str:
    """
    获取PostgreSQL时区设置
    
    Returns:
        str: PostgreSQL时区设置
    """
    return "SET timezone='Asia/Shanghai'"

def get_mongodb_timezone_options() -> dict:
    """
    获取MongoDB时区选项
    
    Returns:
        dict: MongoDB连接选项
    """
    return {
        'tz_aware': True,
        'tzinfo': CHINA_TIMEZONE
    }

# 为了向后兼容，提供一些别名
now = get_current_time
format_datetime = format_time
parse_datetime = parse_time

# 导出常用的时区对象和函数
__all__ = [
    'CHINA_TIMEZONE',
    'TIMEZONE_NAME',
    'get_current_time',
    'format_time',
    'parse_time',
    'to_china_timezone',
    'get_timestamp',
    'from_timestamp',
    'get_mysql_timezone_setting',
    'get_postgres_timezone_setting',
    'get_mongodb_timezone_options',
    'now',
    'format_datetime',
    'parse_datetime'
]

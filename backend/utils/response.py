"""
统一响应格式工具模块
提供标准化的API响应格式
"""

from typing import Any, Optional, Dict
from fastapi.responses import JSONResponse
from fastapi import status


def success_response(
    data: Any = None,
    message: str = "操作成功",
    code: int = 200
) -> Dict[str, Any]:
    """
    成功响应格式
    
    Args:
        data: 响应数据
        message: 成功消息
        code: 状态码
        
    Returns:
        标准化的成功响应
    """
    response = {
        "success": True,
        "code": code,
        "message": message
    }
    
    if data is not None:
        response["data"] = data
    
    return response


def error_response(
    message: str = "操作失败",
    code: int = 500,
    error_code: Optional[str] = None,
    details: Any = None
) -> Dict[str, Any]:
    """
    错误响应格式
    
    Args:
        message: 错误消息
        code: 状态码
        error_code: 错误代码
        details: 错误详情
        
    Returns:
        标准化的错误响应
    """
    response = {
        "success": False,
        "code": code,
        "message": message
    }
    
    if error_code:
        response["error_code"] = error_code
        
    if details is not None:
        response["details"] = details
    
    return response


def paginated_response(
    data: Any,
    total: int,
    page: int,
    page_size: int,
    message: str = "查询成功"
) -> Dict[str, Any]:
    """
    分页响应格式
    
    Args:
        data: 数据列表
        total: 总记录数
        page: 当前页码
        page_size: 每页大小
        message: 响应消息
        
    Returns:
        标准化的分页响应
    """
    total_pages = (total + page_size - 1) // page_size if total > 0 else 0
    
    return success_response(
        data={
            "items": data,
            "pagination": {
                "total": total,
                "page": page,
                "page_size": page_size,
                "total_pages": total_pages,
                "has_next": page < total_pages,
                "has_prev": page > 1
            }
        },
        message=message
    )


def create_json_response(
    content: Dict[str, Any],
    status_code: int = 200,
    headers: Optional[Dict[str, str]] = None
) -> JSONResponse:
    """
    创建JSON响应
    
    Args:
        content: 响应内容
        status_code: HTTP状态码
        headers: 额外的响应头
        
    Returns:
        FastAPI JSONResponse对象
    """
    return JSONResponse(
        content=content,
        status_code=status_code,
        headers=headers
    )


def validation_error_response(
    errors: Dict[str, Any],
    message: str = "输入验证失败"
) -> Dict[str, Any]:
    """
    验证错误响应格式
    
    Args:
        errors: 验证错误详情
        message: 错误消息
        
    Returns:
        标准化的验证错误响应
    """
    return error_response(
        message=message,
        code=422,
        error_code="VALIDATION_ERROR",
        details=errors
    )


def not_found_response(
    resource: str = "资源",
    resource_id: Any = None
) -> Dict[str, Any]:
    """
    资源未找到响应格式
    
    Args:
        resource: 资源类型
        resource_id: 资源ID
        
    Returns:
        标准化的未找到响应
    """
    message = f"{resource}不存在"
    if resource_id is not None:
        message += f"：{resource_id}"
        
    return error_response(
        message=message,
        code=404,
        error_code="NOT_FOUND"
    )


def unauthorized_response(
    message: str = "未授权访问"
) -> Dict[str, Any]:
    """
    未授权响应格式
    
    Args:
        message: 错误消息
        
    Returns:
        标准化的未授权响应
    """
    return error_response(
        message=message,
        code=401,
        error_code="UNAUTHORIZED"
    )


def forbidden_response(
    message: str = "禁止访问"
) -> Dict[str, Any]:
    """
    禁止访问响应格式
    
    Args:
        message: 错误消息
        
    Returns:
        标准化的禁止访问响应
    """
    return error_response(
        message=message,
        code=403,
        error_code="FORBIDDEN"
    )

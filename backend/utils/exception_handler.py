#!/usr/bin/env python3
"""
统一异常处理模块
提供装饰器和上下文管理器来确保所有异常都被正确记录到日志文件
"""

import logging
import traceback
import functools
import asyncio
from typing import Any, Callable, Optional, Dict, Union
from contextlib import contextmanager, asynccontextmanager

logger = logging.getLogger(__name__)


def log_exceptions(
    logger_name: Optional[str] = None,
    reraise: bool = True,
    default_return: Any = None,
    log_level: int = logging.ERROR
):
    """
    异常日志装饰器
    
    Args:
        logger_name: 日志记录器名称，默认使用被装饰函数的模块名
        reraise: 是否重新抛出异常，默认True
        default_return: 当不重新抛出异常时的默认返回值
        log_level: 日志级别，默认ERROR
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            func_logger = logging.getLogger(logger_name or func.__module__)
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # 记录详细的异常信息
                func_logger.log(
                    log_level,
                    f"函数 {func.__name__} 发生异常: {type(e).__name__}: {str(e)}"
                )
                func_logger.log(
                    log_level,
                    f"异常堆栈跟踪:\n{traceback.format_exc()}"
                )
                func_logger.log(
                    log_level,
                    f"函数参数: args={args}, kwargs={kwargs}"
                )
                
                if reraise:
                    raise
                return default_return
        
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            func_logger = logging.getLogger(logger_name or func.__module__)
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                # 记录详细的异常信息
                func_logger.log(
                    log_level,
                    f"异步函数 {func.__name__} 发生异常: {type(e).__name__}: {str(e)}"
                )
                func_logger.log(
                    log_level,
                    f"异常堆栈跟踪:\n{traceback.format_exc()}"
                )
                func_logger.log(
                    log_level,
                    f"函数参数: args={args}, kwargs={kwargs}"
                )
                
                if reraise:
                    raise
                return default_return
        
        # 根据函数类型返回对应的包装器
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


@contextmanager
def exception_context(
    operation_name: str,
    logger_name: Optional[str] = None,
    reraise: bool = True,
    log_level: int = logging.ERROR
):
    """
    异常处理上下文管理器
    
    Args:
        operation_name: 操作名称，用于日志记录
        logger_name: 日志记录器名称
        reraise: 是否重新抛出异常
        log_level: 日志级别
    """
    context_logger = logging.getLogger(logger_name or __name__)
    try:
        context_logger.debug(f"开始执行操作: {operation_name}")
        yield
        context_logger.debug(f"操作完成: {operation_name}")
    except Exception as e:
        context_logger.log(
            log_level,
            f"操作 '{operation_name}' 发生异常: {type(e).__name__}: {str(e)}"
        )
        context_logger.log(
            log_level,
            f"异常堆栈跟踪:\n{traceback.format_exc()}"
        )
        
        if reraise:
            raise


@asynccontextmanager
async def async_exception_context(
    operation_name: str,
    logger_name: Optional[str] = None,
    reraise: bool = True,
    log_level: int = logging.ERROR
):
    """
    异步异常处理上下文管理器
    
    Args:
        operation_name: 操作名称，用于日志记录
        logger_name: 日志记录器名称
        reraise: 是否重新抛出异常
        log_level: 日志级别
    """
    context_logger = logging.getLogger(logger_name or __name__)
    try:
        context_logger.debug(f"开始执行异步操作: {operation_name}")
        yield
        context_logger.debug(f"异步操作完成: {operation_name}")
    except Exception as e:
        context_logger.log(
            log_level,
            f"异步操作 '{operation_name}' 发生异常: {type(e).__name__}: {str(e)}"
        )
        context_logger.log(
            log_level,
            f"异常堆栈跟踪:\n{traceback.format_exc()}"
        )
        
        if reraise:
            raise


def log_and_suppress_exceptions(
    operation_name: str,
    logger_name: Optional[str] = None,
    log_level: int = logging.ERROR
):
    """
    记录异常但不抛出的装饰器
    适用于清理操作等不应该影响主流程的函数
    """
    return log_exceptions(
        logger_name=logger_name,
        reraise=False,
        default_return=None,
        log_level=log_level
    )


def safe_execute(
    func: Callable,
    *args,
    operation_name: Optional[str] = None,
    logger_name: Optional[str] = None,
    default_return: Any = None,
    **kwargs
) -> Any:
    """
    安全执行函数，捕获并记录异常
    
    Args:
        func: 要执行的函数
        *args: 函数参数
        operation_name: 操作名称
        logger_name: 日志记录器名称
        default_return: 异常时的默认返回值
        **kwargs: 函数关键字参数
    
    Returns:
        函数执行结果或默认返回值
    """
    safe_logger = logging.getLogger(logger_name or __name__)
    op_name = operation_name or f"{func.__name__}"
    
    try:
        return func(*args, **kwargs)
    except Exception as e:
        safe_logger.error(
            f"安全执行操作 '{op_name}' 失败: {type(e).__name__}: {str(e)}"
        )
        safe_logger.error(f"异常堆栈跟踪:\n{traceback.format_exc()}")
        return default_return


async def safe_execute_async(
    func: Callable,
    *args,
    operation_name: Optional[str] = None,
    logger_name: Optional[str] = None,
    default_return: Any = None,
    **kwargs
) -> Any:
    """
    安全执行异步函数，捕获并记录异常
    
    Args:
        func: 要执行的异步函数
        *args: 函数参数
        operation_name: 操作名称
        logger_name: 日志记录器名称
        default_return: 异常时的默认返回值
        **kwargs: 函数关键字参数
    
    Returns:
        函数执行结果或默认返回值
    """
    safe_logger = logging.getLogger(logger_name or __name__)
    op_name = operation_name or f"{func.__name__}"
    
    try:
        return await func(*args, **kwargs)
    except Exception as e:
        safe_logger.error(
            f"安全执行异步操作 '{op_name}' 失败: {type(e).__name__}: {str(e)}"
        )
        safe_logger.error(f"异常堆栈跟踪:\n{traceback.format_exc()}")
        return default_return


def create_error_response(
    error: Exception,
    operation: str,
    logger_name: Optional[str] = None,
    include_traceback: bool = False
) -> Dict[str, Any]:
    """
    创建标准化的错误响应
    
    Args:
        error: 异常对象
        operation: 操作名称
        logger_name: 日志记录器名称
        include_traceback: 是否在响应中包含堆栈跟踪
    
    Returns:
        标准化的错误响应字典
    """
    error_logger = logging.getLogger(logger_name or __name__)
    
    # 记录错误到日志
    error_logger.error(f"操作 '{operation}' 失败: {type(error).__name__}: {str(error)}")
    error_logger.error(f"异常堆栈跟踪:\n{traceback.format_exc()}")
    
    # 创建响应
    response = {
        "success": False,
        "error": str(error),
        "error_type": type(error).__name__,
        "operation": operation,
        "timestamp": logging.Formatter().formatTime(logging.LogRecord(
            name="", level=0, pathname="", lineno=0, msg="", args=(), exc_info=None
        ))
    }
    
    if include_traceback:
        response["traceback"] = traceback.format_exc()
    
    return response

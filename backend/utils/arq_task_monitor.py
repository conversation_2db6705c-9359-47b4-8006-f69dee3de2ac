#!/usr/bin/env python3
"""
ARQ任务状态监控器
实时监控Redis中的ARQ任务状态
"""

import asyncio
import json
import sys
import os
import signal
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any
import argparse

# 添加项目根目录到Python路径
current_dir = Path(__file__).parent.parent
backend_dir = current_dir
if str(backend_dir) not in sys.path:
    sys.path.insert(0, str(backend_dir))

# 确保当前工作目录是backend目录
if os.path.basename(os.getcwd()) != 'backend':
    backend_path = str(backend_dir)
    os.chdir(backend_path)

try:
    from config.redis_config import redis_manager
except ImportError as e:
    print(f"❌ 导入Redis配置失败: {e}")
    print(f"当前工作目录: {os.getcwd()}")
    print(f"Backend目录: {backend_dir}")
    print(f"Python路径: {sys.path}")
    sys.exit(1)

class ARQTaskMonitor:
    """ARQ任务监控器"""
    
    def __init__(self):
        self.is_running = True
        self.tasks_cache = {}
        
        # 设置信号处理
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        print(f"\n收到信号 {signum}，正在退出...")
        self.is_running = False
    
    async def initialize(self):
        """初始化监控器"""
        try:
            await redis_manager.initialize()
            print("✅ Redis连接成功")
            return True
        except Exception as e:
            print(f"❌ Redis连接失败: {e}")
            return False
    
    async def get_queue_status(self) -> Dict[str, Any]:
        """获取队列状态"""
        try:
            # 获取队列长度
            queue_length = await redis_manager.redis.llen('arq:capture_tasks')
            
            # 获取队列中的任务
            queue_tasks = []
            if queue_length > 0:
                tasks_data = await redis_manager.redis.lrange('arq:capture_tasks', 0, min(queue_length, 10) - 1)
                for task_data in tasks_data:
                    try:
                        task_info = json.loads(task_data)
                        queue_tasks.append({
                            'function': task_info.get('function', 'unknown'),
                            'job_id': task_info.get('job_id', 'unknown'),
                            'enqueue_time': task_info.get('enqueue_time'),
                            'args_preview': str(task_info.get('args', []))[:50] + '...' if len(str(task_info.get('args', []))) > 50 else str(task_info.get('args', []))
                        })
                    except Exception as e:
                        print(f"⚠️ 解析队列任务信息失败: {e}")
                        queue_tasks.append({'function': 'parse_error', 'job_id': 'unknown', 'enqueue_time': None, 'args_preview': ''})
            
            return {
                'queue_length': queue_length,
                'queue_tasks': queue_tasks
            }
        except Exception as e:
            print(f"❌ 获取队列状态失败: {e}")
            return {'queue_length': 0, 'queue_tasks': []}
    
    async def get_in_progress_tasks(self) -> List[Dict[str, Any]]:
        """获取正在执行的任务"""
        try:
            in_progress_tasks = []
            async for key in redis_manager.redis.scan_iter(match="arq:in-progress:*"):
                if isinstance(key, bytes):
                    key = key.decode('utf-8')
                
                task_id = key.replace("arq:in-progress:", "")
                
                # 获取任务详细信息
                job_key = f"arq:job:{task_id}"
                job_data = await redis_manager.redis.get(job_key)
                
                task_info = {'task_id': task_id[:8] + '...', 'function': 'unknown', 'start_time': 'unknown'}
                
                if job_data:
                    try:
                        job_info = json.loads(job_data)
                        task_info.update({
                            'function': job_info.get('function', 'unknown'),
                            'start_time': job_info.get('enqueue_time', 'unknown')
                        })
                    except Exception as e:
                        print(f"⚠️ 解析进行中任务信息失败: {e}")
                
                in_progress_tasks.append(task_info)
            
            return in_progress_tasks
        except Exception as e:
            print(f"❌ 获取正在执行的任务失败: {e}")
            return []
    
    async def get_completed_tasks(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取已完成的任务"""
        try:
            completed_tasks = []
            count = 0
            
            async for key in redis_manager.redis.scan_iter(match="arq:result:*"):
                if count >= limit:
                    break
                
                if isinstance(key, bytes):
                    key = key.decode('utf-8')
                
                task_id = key.replace("arq:result:", "")
                result_data = await redis_manager.redis.get(key)
                
                if result_data:
                    try:
                        result_info = json.loads(result_data)
                        completed_tasks.append({
                            'task_id': task_id[:8] + '...',
                            'function': result_info.get('function', 'unknown'),
                            'success': result_info.get('success', False),
                            'finish_time': result_info.get('finish_time', 'unknown'),
                            'result_preview': str(result_info.get('result', ''))[:50] + '...' if len(str(result_info.get('result', ''))) > 50 else str(result_info.get('result', ''))
                        })
                        count += 1
                    except Exception as e:
                        print(f"⚠️ 解析已完成任务信息失败: {e}")
            
            return completed_tasks
        except Exception as e:
            print(f"❌ 获取已完成任务失败: {e}")
            return []
    
    async def get_failed_tasks(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取失败的任务"""
        try:
            failed_tasks = []
            count = 0
            
            async for key in redis_manager.redis.scan_iter(match="arq:result:*"):
                if count >= limit:
                    break
                
                if isinstance(key, bytes):
                    key = key.decode('utf-8')
                
                result_data = await redis_manager.redis.get(key)
                
                if result_data:
                    try:
                        result_info = json.loads(result_data)
                        if not result_info.get('success', True):  # 失败的任务
                            task_id = key.replace("arq:result:", "")
                            failed_tasks.append({
                                'task_id': task_id[:8] + '...',
                                'function': result_info.get('function', 'unknown'),
                                'finish_time': result_info.get('finish_time', 'unknown'),
                                'error': str(result_info.get('result', ''))[:100] + '...' if len(str(result_info.get('result', ''))) > 100 else str(result_info.get('result', ''))
                            })
                            count += 1
                    except Exception as e:
                        print(f"⚠️ 解析失败任务信息失败: {e}")
            
            return failed_tasks
        except Exception as e:
            print(f"❌ 获取失败任务失败: {e}")
            return []
    
    async def get_worker_info(self) -> List[Dict[str, Any]]:
        """获取Worker信息"""
        try:
            workers = []
            async for key in redis_manager.redis.scan_iter(match="worker:active:*"):
                if isinstance(key, bytes):
                    key = key.decode('utf-8')
                
                worker_id = key.replace("worker:active:", "")
                ttl = await redis_manager.redis.ttl(key)
                
                workers.append({
                    'worker_id': worker_id,
                    'status': 'active' if ttl > 0 else 'expired',
                    'ttl': ttl
                })
            
            return workers
        except Exception as e:
            print(f"❌ 获取Worker信息失败: {e}")
            return []
    
    async def get_redis_info(self) -> Dict[str, Any]:
        """获取Redis信息"""
        try:
            info = await redis_manager.redis.info()
            return {
                'version': info.get('redis_version', 'unknown'),
                'memory_used': info.get('used_memory_human', 'unknown'),
                'connected_clients': info.get('connected_clients', 0),
                'total_commands': info.get('total_commands_processed', 0),
                'keyspace_hits': info.get('keyspace_hits', 0),
                'keyspace_misses': info.get('keyspace_misses', 0)
            }
        except Exception as e:
            print(f"❌ 获取Redis信息失败: {e}")
            return {}
    
    def print_status_dashboard(self, queue_status, in_progress, completed, failed, workers, redis_info):
        """打印状态面板"""
        import os
        os.system('clear' if os.name == 'posix' else 'cls')
        
        print("=" * 100)
        print(f"🔍 ARQ任务状态监控 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 100)
        
        # Redis信息
        print(f"\n📊 Redis状态:")
        print(f"  版本: {redis_info.get('version', 'unknown')}")
        print(f"  内存使用: {redis_info.get('memory_used', 'unknown')}")
        print(f"  连接数: {redis_info.get('connected_clients', 0)}")
        print(f"  命令总数: {redis_info.get('total_commands', 0):,}")
        
        # 队列状态
        print(f"\n📋 队列状态:")
        print(f"  队列长度: {queue_status['queue_length']}")
        if queue_status['queue_tasks']:
            print(f"  队列中的任务 (前{len(queue_status['queue_tasks'])}个):")
            for i, task in enumerate(queue_status['queue_tasks'], 1):
                print(f"    {i}. {task['function']} - {task['job_id'][:8]}...")
        
        # 正在执行的任务
        print(f"\n🔄 正在执行的任务 ({len(in_progress)}个):")
        if in_progress:
            for task in in_progress:
                print(f"  • {task['task_id']} - {task['function']}")
        else:
            print("  无正在执行的任务")
        
        # Worker状态
        print(f"\n👷 Worker状态 ({len(workers)}个):")
        if workers:
            for worker in workers:
                status_icon = "🟢" if worker['status'] == 'active' else "🔴"
                print(f"  {status_icon} {worker['worker_id']} - {worker['status']} (TTL: {worker['ttl']}s)")
        else:
            print("  无活跃的Worker")
        
        # 最近完成的任务
        print(f"\n✅ 最近完成的任务 ({len(completed)}个):")
        if completed:
            for task in completed[:5]:  # 只显示前5个
                status_icon = "✅" if task['success'] else "❌"
                print(f"  {status_icon} {task['task_id']} - {task['function']}")
        else:
            print("  无最近完成的任务")
        
        # 最近失败的任务
        if failed:
            print(f"\n❌ 最近失败的任务 ({len(failed)}个):")
            for task in failed[:3]:  # 只显示前3个
                print(f"  ❌ {task['task_id']} - {task['function']}")
                print(f"     错误: {task['error'][:80]}...")
        
        print("\n" + "=" * 100)
        print("按 Ctrl+C 退出监控")
        print("=" * 100)
    
    async def monitor_loop(self, interval: int = 5, dashboard: bool = False):
        """监控循环"""
        print(f"🚀 开始ARQ任务监控 (更新间隔: {interval}秒)")
        
        while self.is_running:
            try:
                # 收集所有状态信息
                queue_status = await self.get_queue_status()
                in_progress = await self.get_in_progress_tasks()
                completed = await self.get_completed_tasks()
                failed = await self.get_failed_tasks()
                workers = await self.get_worker_info()
                redis_info = await self.get_redis_info()
                
                if dashboard:
                    self.print_status_dashboard(queue_status, in_progress, completed, failed, workers, redis_info)
                else:
                    # 简单的日志输出
                    print(f"[{datetime.now().strftime('%H:%M:%S')}] "
                          f"队列: {queue_status['queue_length']}, "
                          f"执行中: {len(in_progress)}, "
                          f"Worker: {len(workers)}, "
                          f"失败: {len(failed)}")
                
                await asyncio.sleep(interval)
                
            except Exception as e:
                print(f"❌ 监控循环出错: {e}")
                await asyncio.sleep(interval)
    
    async def cleanup_expired_tasks(self):
        """清理过期任务"""
        try:
            print("🧹 开始清理过期任务...")
            
            # 清理过期的in-progress任务
            cleaned_count = 0
            async for key in redis_manager.redis.scan_iter(match="arq:in-progress:*"):
                # 检查对应的job是否还存在
                if isinstance(key, bytes):
                    key = key.decode('utf-8')
                
                task_id = key.replace("arq:in-progress:", "")
                job_key = f"arq:job:{task_id}"
                
                job_exists = await redis_manager.redis.exists(job_key)
                if not job_exists:
                    # job不存在，删除in-progress标记
                    await redis_manager.redis.delete(key)
                    cleaned_count += 1
                    print(f"清理孤儿任务: {task_id[:8]}...")
            
            print(f"✅ 清理完成，共清理 {cleaned_count} 个孤儿任务")
            
        except Exception as e:
            print(f"❌ 清理过期任务失败: {e}")

async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='ARQ任务状态监控器')
    parser.add_argument('--interval', type=int, default=5, help='监控更新间隔（秒）')
    parser.add_argument('--dashboard', action='store_true', help='显示实时监控面板')
    parser.add_argument('--cleanup', action='store_true', help='清理过期任务')
    parser.add_argument('--once', action='store_true', help='只运行一次，不循环')
    
    args = parser.parse_args()
    
    monitor = ARQTaskMonitor()
    
    # 初始化
    if not await monitor.initialize():
        return 1
    
    try:
        if args.cleanup:
            await monitor.cleanup_expired_tasks()
        elif args.once:
            # 只运行一次
            queue_status = await monitor.get_queue_status()
            in_progress = await monitor.get_in_progress_tasks()
            completed = await monitor.get_completed_tasks()
            failed = await monitor.get_failed_tasks()
            workers = await monitor.get_worker_info()
            redis_info = await monitor.get_redis_info()
            
            monitor.print_status_dashboard(queue_status, in_progress, completed, failed, workers, redis_info)
        else:
            # 持续监控
            await monitor.monitor_loop(args.interval, args.dashboard)
            
    except KeyboardInterrupt:
        print("\n⏹️ 监控被用户中断")
    except Exception as e:
        print(f"❌ 监控出错: {e}")
        return 1
    finally:
        await redis_manager.close()
    
    return 0

if __name__ == '__main__':
    import sys
    sys.exit(asyncio.run(main()))

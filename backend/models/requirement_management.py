"""
协议需求管理数据模型
"""
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum
import uuid


class RequirementTypeEnum(str, Enum):
    """需求类型枚举"""
    FUNCTIONAL = "functional"  # 功能性需求
    PERFORMANCE = "performance"  # 性能需求
    SECURITY = "security"  # 安全需求
    COMPATIBILITY = "compatibility"  # 兼容性需求
    RELIABILITY = "reliability"  # 可靠性需求
    USABILITY = "usability"  # 易用性需求
    PROTOCOL_SPECIFIC = "protocol_specific"  # 协议特定需求


class RequirementPriorityEnum(str, Enum):
    """需求优先级枚举"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class RequirementStatusEnum(str, Enum):
    """需求状态枚举"""
    DRAFT = "draft"  # 草稿
    REVIEWING = "reviewing"  # 评审中
    APPROVED = "approved"  # 已批准
    REJECTED = "rejected"  # 已拒绝
    IMPLEMENTING = "implementing"  # 实现中
    TESTING = "testing"  # 测试中
    COMPLETED = "completed"  # 已完成
    DEPRECATED = "deprecated"  # 已废弃


class ProtocolTypeEnum(str, Enum):
    """协议类型枚举"""
    MYSQL = "mysql"
    POSTGRESQL = "postgresql"
    MONGODB = "mongodb"
    ORACLE = "oracle"
    GAUSSDB = "gaussdb"
    REDIS = "redis"
    HTTP = "http"
    WEBSOCKET = "websocket"
    TCP = "tcp"
    UDP = "udp"


class DocumentTypeEnum(str, Enum):
    """文档类型枚举"""
    RFC = "rfc"  # RFC文档
    SPECIFICATION = "specification"  # 规范文档
    API_DOC = "api_doc"  # API文档
    PROTOCOL_DOC = "protocol_doc"  # 协议文档
    STANDARD = "standard"  # 标准文档
    MANUAL = "manual"  # 手册
    OTHER = "other"  # 其他


class RequirementComplexityEnum(str, Enum):
    """需求复杂度枚举"""
    SIMPLE = "simple"  # 简单
    MEDIUM = "medium"  # 中等
    COMPLEX = "complex"  # 复杂
    VERY_COMPLEX = "very_complex"  # 非常复杂


class TestabilityEnum(str, Enum):
    """可测试性枚举"""
    EASY = "easy"  # 容易测试
    MEDIUM = "medium"  # 中等难度
    DIFFICULT = "difficult"  # 困难
    VERY_DIFFICULT = "very_difficult"  # 非常困难


class RequirementDependency(BaseModel):
    """需求依赖关系模型"""
    dependency_id: str = Field(..., description="依赖的需求ID")
    dependency_type: str = Field(..., description="依赖类型: prerequisite(前置), related(相关), conflict(冲突)")
    description: Optional[str] = Field(None, description="依赖关系描述")


class RequirementTraceability(BaseModel):
    """需求追溯信息模型"""
    source_document: str = Field(..., description="源文档")
    source_section: Optional[str] = Field(None, description="源文档章节")
    source_page: Optional[int] = Field(None, description="源文档页码")
    source_line: Optional[int] = Field(None, description="源文档行号")
    extraction_method: str = Field("manual", description="提取方法: manual(手动), ai_parsed(AI解析), auto_imported(自动导入)")


class RequirementTestCriteria(BaseModel):
    """需求测试准则模型"""
    acceptance_criteria: List[str] = Field(..., description="验收标准")
    test_scenarios: List[str] = Field(default_factory=list, description="测试场景")
    edge_cases: List[str] = Field(default_factory=list, description="边界情况")
    negative_cases: List[str] = Field(default_factory=list, description="负面测试用例")
    performance_criteria: Optional[Dict[str, Any]] = Field(None, description="性能标准")
    security_criteria: Optional[Dict[str, Any]] = Field(None, description="安全标准")


class ProtocolRequirementCreate(BaseModel):
    """创建协议需求请求模型"""
    title: str = Field(..., min_length=1, max_length=255, description="需求标题")
    description: str = Field(..., min_length=1, description="需求描述")
    requirement_type: RequirementTypeEnum = Field(..., description="需求类型")
    protocol_type: ProtocolTypeEnum = Field(..., description="协议类型")
    protocol_version: Optional[str] = Field(None, max_length=50, description="协议版本")
    priority: RequirementPriorityEnum = Field(RequirementPriorityEnum.MEDIUM, description="优先级")
    complexity: RequirementComplexityEnum = Field(RequirementComplexityEnum.MEDIUM, description="复杂度")
    testability: TestabilityEnum = Field(TestabilityEnum.MEDIUM, description="可测试性")
    
    # 需求内容
    functional_description: Optional[str] = Field(None, description="功能描述")
    technical_specifications: Optional[Dict[str, Any]] = Field(None, description="技术规格")
    business_rules: Optional[List[str]] = Field(None, description="业务规则")
    constraints: Optional[List[str]] = Field(None, description="约束条件")
    assumptions: Optional[List[str]] = Field(None, description="假设条件")
    
    # 测试相关
    test_criteria: RequirementTestCriteria = Field(..., description="测试准则")
    expected_test_cases: int = Field(0, ge=0, description="预期测试用例数量")
    
    # 追溯信息
    traceability: RequirementTraceability = Field(..., description="追溯信息")
    
    # 依赖关系
    dependencies: List[RequirementDependency] = Field(default_factory=list, description="依赖关系")
    
    # 元数据
    tags: List[str] = Field(default_factory=list, description="标签")
    category: Optional[str] = Field(None, max_length=100, description="分类")
    module: Optional[str] = Field(None, max_length=100, description="所属模块")
    author: Optional[str] = Field(None, max_length=100, description="创建者")
    reviewer: Optional[str] = Field(None, max_length=100, description="评审者")
    stakeholders: List[str] = Field(default_factory=list, description="利益相关者")
    
    # 附加信息
    references: List[str] = Field(default_factory=list, description="参考资料")
    related_documents: List[str] = Field(default_factory=list, description="相关文档")
    notes: Optional[str] = Field(None, description="备注")


class ProtocolRequirementUpdate(BaseModel):
    """更新协议需求请求模型"""
    title: Optional[str] = Field(None, min_length=1, max_length=255, description="需求标题")
    description: Optional[str] = Field(None, min_length=1, description="需求描述")
    requirement_type: Optional[RequirementTypeEnum] = Field(None, description="需求类型")
    protocol_type: Optional[ProtocolTypeEnum] = Field(None, description="协议类型")
    protocol_version: Optional[str] = Field(None, max_length=50, description="协议版本")
    priority: Optional[RequirementPriorityEnum] = Field(None, description="优先级")
    complexity: Optional[RequirementComplexityEnum] = Field(None, description="复杂度")
    testability: Optional[TestabilityEnum] = Field(None, description="可测试性")
    status: Optional[RequirementStatusEnum] = Field(None, description="状态")
    
    # 需求内容
    functional_description: Optional[str] = Field(None, description="功能描述")
    technical_specifications: Optional[Dict[str, Any]] = Field(None, description="技术规格")
    business_rules: Optional[List[str]] = Field(None, description="业务规则")
    constraints: Optional[List[str]] = Field(None, description="约束条件")
    assumptions: Optional[List[str]] = Field(None, description="假设条件")
    
    # 测试相关
    test_criteria: Optional[RequirementTestCriteria] = Field(None, description="测试准则")
    expected_test_cases: Optional[int] = Field(None, ge=0, description="预期测试用例数量")
    
    # 追溯信息
    traceability: Optional[RequirementTraceability] = Field(None, description="追溯信息")
    
    # 依赖关系
    dependencies: Optional[List[RequirementDependency]] = Field(None, description="依赖关系")
    
    # 元数据
    tags: Optional[List[str]] = Field(None, description="标签")
    category: Optional[str] = Field(None, max_length=100, description="分类")
    module: Optional[str] = Field(None, max_length=100, description="所属模块")
    reviewer: Optional[str] = Field(None, max_length=100, description="评审者")
    stakeholders: Optional[List[str]] = Field(None, description="利益相关者")
    
    # 附加信息
    references: Optional[List[str]] = Field(None, description="参考资料")
    related_documents: Optional[List[str]] = Field(None, description="相关文档")
    notes: Optional[str] = Field(None, description="备注")


class ProtocolRequirementResponse(BaseModel):
    """协议需求响应模型"""
    id: str = Field(..., description="需求UUID")
    title: str = Field(..., description="需求标题")
    description: str = Field(..., description="需求描述")
    requirement_type: RequirementTypeEnum = Field(..., description="需求类型")
    protocol_type: ProtocolTypeEnum = Field(..., description="协议类型")
    protocol_version: Optional[str] = Field(None, description="协议版本")
    priority: RequirementPriorityEnum = Field(..., description="优先级")
    complexity: RequirementComplexityEnum = Field(..., description="复杂度")
    testability: TestabilityEnum = Field(..., description="可测试性")
    status: RequirementStatusEnum = Field(..., description="状态")
    
    # 需求内容
    functional_description: Optional[str] = Field(None, description="功能描述")
    technical_specifications: Optional[Dict[str, Any]] = Field(None, description="技术规格")
    business_rules: Optional[List[str]] = Field(None, description="业务规则")
    constraints: Optional[List[str]] = Field(None, description="约束条件")
    assumptions: Optional[List[str]] = Field(None, description="假设条件")
    
    # 测试相关
    test_criteria: RequirementTestCriteria = Field(..., description="测试准则")
    expected_test_cases: int = Field(..., description="预期测试用例数量")
    actual_test_cases: int = Field(0, description="实际测试用例数量")
    test_coverage: float = Field(0.0, description="测试覆盖率")
    
    # 追溯信息
    traceability: RequirementTraceability = Field(..., description="追溯信息")
    
    # 依赖关系
    dependencies: List[RequirementDependency] = Field(..., description="依赖关系")
    
    # 元数据
    tags: List[str] = Field(..., description="标签")
    category: Optional[str] = Field(None, description="分类")
    module: Optional[str] = Field(None, description="所属模块")
    author: Optional[str] = Field(None, description="创建者")
    reviewer: Optional[str] = Field(None, description="评审者")
    stakeholders: List[str] = Field(..., description="利益相关者")
    
    # 附加信息
    references: List[str] = Field(..., description="参考资料")
    related_documents: List[str] = Field(..., description="相关文档")
    notes: Optional[str] = Field(None, description="备注")
    
    # 系统字段
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    version: int = Field(1, description="版本号")


class ProtocolRequirementQuery(BaseModel):
    """协议需求查询参数模型"""
    page: int = Field(1, ge=1, description="页码")
    page_size: int = Field(20, ge=1, le=100, description="每页数量")
    keyword: Optional[str] = Field(None, description="关键词搜索(标题、描述)")
    requirement_type: Optional[RequirementTypeEnum] = Field(None, description="需求类型筛选")
    protocol_type: Optional[ProtocolTypeEnum] = Field(None, description="协议类型筛选")
    protocol_version: Optional[str] = Field(None, description="协议版本筛选")
    priority: Optional[RequirementPriorityEnum] = Field(None, description="优先级筛选")
    complexity: Optional[RequirementComplexityEnum] = Field(None, description="复杂度筛选")
    testability: Optional[TestabilityEnum] = Field(None, description="可测试性筛选")
    status: Optional[RequirementStatusEnum] = Field(None, description="状态筛选")
    category: Optional[str] = Field(None, description="分类筛选")
    module: Optional[str] = Field(None, description="模块筛选")
    author: Optional[str] = Field(None, description="创建者筛选")
    reviewer: Optional[str] = Field(None, description="评审者筛选")
    tags: Optional[List[str]] = Field(None, description="标签筛选")
    has_dependencies: Optional[bool] = Field(None, description="是否有依赖关系")
    created_start: Optional[datetime] = Field(None, description="创建时间开始")
    created_end: Optional[datetime] = Field(None, description="创建时间结束")
    sort_by: str = Field("created_at", description="排序字段")
    sort_order: str = Field("desc", description="排序方向: asc, desc")


class ProtocolRequirementListResponse(BaseModel):
    """协议需求列表响应模型"""
    items: List[ProtocolRequirementResponse] = Field(..., description="需求列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页数量")
    total_pages: int = Field(..., description="总页数")


class RequirementStatistics(BaseModel):
    """需求统计模型"""
    total_count: int = Field(..., description="总需求数")
    status_distribution: Dict[str, int] = Field(..., description="状态分布")
    priority_distribution: Dict[str, int] = Field(..., description="优先级分布")
    type_distribution: Dict[str, int] = Field(..., description="类型分布")
    protocol_distribution: Dict[str, int] = Field(..., description="协议分布")
    complexity_distribution: Dict[str, int] = Field(..., description="复杂度分布")
    testability_distribution: Dict[str, int] = Field(..., description="可测试性分布")
    coverage_summary: Dict[str, float] = Field(..., description="覆盖率汇总")
    recent_requirements: List[ProtocolRequirementResponse] = Field(..., description="最近需求")


class DocumentParseRequest(BaseModel):
    """文档解析请求模型"""
    document_title: str = Field(..., min_length=1, max_length=255, description="文档标题")
    document_type: DocumentTypeEnum = Field(..., description="文档类型")
    protocol_type: ProtocolTypeEnum = Field(..., description="协议类型")
    protocol_version: Optional[str] = Field(None, description="协议版本")
    document_content: Optional[str] = Field(None, description="文档内容")
    document_url: Optional[str] = Field(None, description="文档URL")
    document_file_path: Optional[str] = Field(None, description="文档文件路径")
    
    # 解析选项
    parse_options: Dict[str, Any] = Field(default_factory=dict, description="解析选项")
    auto_categorize: bool = Field(True, description="自动分类")
    extract_test_scenarios: bool = Field(True, description="提取测试场景")
    create_dependencies: bool = Field(True, description="创建依赖关系")
    
    # 元数据
    author: Optional[str] = Field(None, description="解析者")
    tags: List[str] = Field(default_factory=list, description="标签")
    notes: Optional[str] = Field(None, description="解析备注")


class DocumentParseResponse(BaseModel):
    """文档解析响应模型"""
    parse_id: str = Field(..., description="解析任务ID")
    document_title: str = Field(..., description="文档标题")
    total_requirements: int = Field(..., description="解析出的需求总数")
    requirements: List[ProtocolRequirementResponse] = Field(..., description="解析出的需求列表")
    parse_summary: Dict[str, Any] = Field(..., description="解析摘要")
    warnings: List[str] = Field(default_factory=list, description="解析警告")
    errors: List[str] = Field(default_factory=list, description="解析错误")
    created_at: datetime = Field(..., description="解析时间")


class RequirementBatchOperation(BaseModel):
    """需求批量操作模型"""
    operation_type: str = Field(..., description="操作类型: update_status, update_priority, add_tags, remove_tags, delete")
    requirement_ids: List[str] = Field(..., min_items=1, description="需求ID列表")
    operation_data: Dict[str, Any] = Field(..., description="操作数据")
    reason: Optional[str] = Field(None, description="操作原因")


class RequirementBatchOperationResponse(BaseModel):
    """需求批量操作响应模型"""
    operation_id: str = Field(..., description="操作ID")
    total_count: int = Field(..., description="总操作数量")
    success_count: int = Field(..., description="成功数量")
    failed_count: int = Field(..., description="失败数量")
    failed_items: List[Dict[str, str]] = Field(default_factory=list, description="失败项目")
    message: str = Field(..., description="操作结果信息")

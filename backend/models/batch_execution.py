#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from sqlalchemy import Column, String, JSO<PERSON>, Enum, Integer, TIMESTAMP
from sqlalchemy.ext.declarative import declarative_base
import enum

Base = declarative_base()

class BatchExecutionStatusEnum(str, enum.Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class BatchExecution(Base):
    __tablename__ = 'batch_executions'

    id = Column(String(36), primary_key=True)
    name = Column(String(255), nullable=False)
    test_case_ids = Column(JSON, nullable=False)
    server_config_id = Column(Integer)
    database_config_id = Column(Integer)
    execution_options = Column(JSON)
    status = Column(Enum(BatchExecutionStatusEnum), nullable=False, default=BatchExecutionStatusEnum.PENDING)
    total_cases = Column(Integer, default=0)
    completed_cases = Column(Integer, default=0)
    success_cases = Column(Integer, default=0)
    failed_cases = Column(Integer, default=0)
    start_time = Column(TIMESTAMP)
    end_time = Column(TIMESTAMP)
    created_at = Column(TIMESTAMP)


"""
抓包文件数据模型
"""

from datetime import datetime
from typing import Optional, Dict, Any
from pydantic import BaseModel


class CaptureFile(BaseModel):
    """抓包文件模型"""
    id: Optional[int] = None
    filename: str
    file_path: str
    file_size: int
    database_type: str  # mysql, postgresql, mongodb, oracle, gaussdb
    target_host: str
    target_port: int
    created_time: datetime
    status: str = "completed"  # pending, completed, failed
    description: Optional[str] = None
    
    class Config:
        from_attributes = True


class CaptureFileCreate(BaseModel):
    """创建抓包文件请求模型"""
    filename: str
    file_path: str
    file_size: int
    database_type: str
    target_host: str
    target_port: int
    description: Optional[str] = None


class CaptureFileResponse(BaseModel):
    """抓包文件响应模型"""
    id: int
    filename: str
    file_path: str
    file_size: int
    database_type: str
    target_host: str
    target_port: int
    created_time: str
    status: str
    description: Optional[str] = None
    
    # 格式化的文件大小
    formatted_size: str
    # 相对创建时间
    relative_time: str

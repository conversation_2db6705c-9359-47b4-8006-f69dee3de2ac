"""
网关服务器数据模型（统一定义，支持 Kafka 配置）
"""

from dataclasses import dataclass, field
from typing import Optional, Dict, Any, List
from datetime import datetime

@dataclass
class GatewayServer:
    """网关服务器配置模型"""
    name: str
    host: str
    username: str
    password: str
    port: int = 22
    upload_path: str = "/tmp"
    description: str = ""
    is_active: bool = True
    is_default: bool = False
    connection_timeout: int = 30
    max_connections: int = 10
    gateway_type: str = "ssh"  # ssh, sftp, ftp
    proxy_enabled: bool = False
    proxy_host: Optional[str] = None
    proxy_port: Optional[int] = None
    # Kafka相关字段
    kafka_enabled: bool = False
    kafka_host: Optional[str] = None
    kafka_port: int = 9092
    kafka_topic: Optional[str] = None
    id: Optional[int] = None
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'name': self.name,
            'host': self.host,
            'port': self.port,
            'username': self.username,
            'password': self.password,
            'upload_path': self.upload_path,
            'description': self.description,
            'is_active': self.is_active,
            'is_default': self.is_default,
            'connection_timeout': self.connection_timeout,
            'max_connections': self.max_connections,
            'gateway_type': self.gateway_type,
            'proxy_enabled': self.proxy_enabled,
            'proxy_host': self.proxy_host,
            'proxy_port': self.proxy_port,
            'kafka_enabled': self.kafka_enabled,
            'kafka_host': self.kafka_host,
            'kafka_port': self.kafka_port,
            'kafka_topic': self.kafka_topic,
            'created_at': self.created_at,
            'updated_at': self.updated_at
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'GatewayServer':
        """从字典创建实例"""
        # 处理时间字段
        created_at = data.get('created_at')
        updated_at = data.get('updated_at')
        
        if isinstance(created_at, datetime):
            created_at = created_at.isoformat()
        if isinstance(updated_at, datetime):
            updated_at = updated_at.isoformat()
            
        return cls(
            id=data.get('id'),
            name=data.get('name', ''),
            host=data.get('host', ''),
            port=data.get('port', 22),
            username=data.get('username', ''),
            password=data.get('password', ''),
            upload_path=data.get('upload_path', '/tmp'),
            description=data.get('description', ''),
            is_active=data.get('is_active', True),
            is_default=data.get('is_default', False),
            connection_timeout=data.get('connection_timeout', 30),
            max_connections=data.get('max_connections', 10),
            gateway_type=data.get('gateway_type', 'ssh'),
            proxy_enabled=data.get('proxy_enabled', False),
            proxy_host=data.get('proxy_host'),
            proxy_port=data.get('proxy_port'),
            kafka_enabled=data.get('kafka_enabled', False),
            kafka_host=data.get('kafka_host'),
            kafka_port=data.get('kafka_port', 9092),
            kafka_topic=data.get('kafka_topic'),
            created_at=created_at,
            updated_at=updated_at
        )

@dataclass
class GatewayConnectionTest:
    """网关连接测试结果"""
    success: bool
    message: str
    response_time: float  # 毫秒
    upload_test: Optional[bool] = None
    disk_space: Optional[str] = None
    server_info: Optional[Dict[str, Any]] = None
    test_timestamp: Optional[str] = field(default_factory=lambda: datetime.now().isoformat())

@dataclass
class GatewayFileTransfer:
    """文件传输记录"""
    gateway_id: int
    local_path: str
    remote_path: str
    transfer_type: str  # upload, download
    file_size: int
    transfer_time: float
    success: bool
    error_message: Optional[str] = None
    created_at: Optional[str] = field(default_factory=lambda: datetime.now().isoformat())

@dataclass
class GatewayStats:
    """网关统计信息"""
    total_gateways: int = 0
    active_gateways: int = 0
    default_gateways: int = 0
    type_statistics: Dict[str, int] = field(default_factory=dict)
    last_test_results: Dict[int, GatewayConnectionTest] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'total_gateways': self.total_gateways,
            'active_gateways': self.active_gateways,
            'default_gateways': self.default_gateways,
            'type_statistics': self.type_statistics,
            'last_test_results': {
                str(k): {
                    'success': v.success,
                    'message': v.message,
                    'response_time': v.response_time,
                    'test_timestamp': v.test_timestamp
                } for k, v in self.last_test_results.items()
            }
        }

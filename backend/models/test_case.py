"""
测试用例数据模型
"""

from dataclasses import dataclass, field
from typing import Optional, Dict, Any, List
from datetime import datetime
from enum import Enum
import uuid
import json

class ProtocolType(str, Enum):
    """协议类型"""
    MYSQL = "mysql"
    POSTGRES = "postgres"
    MONGO = "mongo"

class TestCaseCategory(str, Enum):
    """测试用例分类"""
    NORMAL = "normal"
    ERROR = "error"
    BOUNDARY = "boundary"
    PERFORMANCE = "performance"

class ExecutionStatus(str, Enum):
    """执行状态"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

@dataclass
class TestStep:
    """测试步骤"""
    action: str  # connect, query, insert, update, delete, etc.
    payload: Dict[str, Any]
    expected_response: Optional[Dict[str, Any]] = None
    timeout: int = 30
    retry_count: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'action': self.action,
            'payload': self.payload,
            'expected_response': self.expected_response,
            'timeout': self.timeout,
            'retry_count': self.retry_count
        }

@dataclass
class TestCaseSetup:
    """测试用例设置"""
    database_config: Optional[Dict[str, Any]] = None
    initial_data: List[str] = field(default_factory=list)
    environment_setup: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'database_config': self.database_config,
            'initial_data': self.initial_data,
            'environment_setup': self.environment_setup
        }

@dataclass
class TestCaseCleanup:
    """测试用例清理"""
    cleanup_sql: List[str] = field(default_factory=list)
    cleanup_commands: List[str] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'cleanup_sql': self.cleanup_sql,
            'cleanup_commands': self.cleanup_commands
        }

@dataclass
class ValidationRule:
    """验证规则"""
    packet_validation: Optional[Dict[str, Any]] = None
    response_validation: Optional[Dict[str, Any]] = None
    performance_validation: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'packet_validation': self.packet_validation,
            'response_validation': self.response_validation,
            'performance_validation': self.performance_validation
        }

@dataclass
class TestCaseDefinition:
    """测试用例定义"""
    setup: TestCaseSetup = field(default_factory=TestCaseSetup)
    steps: List[TestStep] = field(default_factory=list)
    cleanup: TestCaseCleanup = field(default_factory=TestCaseCleanup)
    validation: ValidationRule = field(default_factory=ValidationRule)
    capture_options: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'setup': self.setup.to_dict(),
            'steps': [step.to_dict() for step in self.steps],
            'cleanup': self.cleanup.to_dict(),
            'validation': self.validation.to_dict(),
            'capture_options': self.capture_options
        }

@dataclass
class TestCase:
    """测试用例"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    protocol: ProtocolType = ProtocolType.MYSQL
    category: TestCaseCategory = TestCaseCategory.NORMAL
    description: str = ""
    test_case: TestCaseDefinition = field(default_factory=TestCaseDefinition)
    tags: List[str] = field(default_factory=list)
    source_document: str = ""
    execution_count: int = 0
    success_count: int = 0
    success_rate: float = 0.0
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'id': self.id,
            'name': self.name,
            'protocol': self.protocol.value,
            'category': self.category.value,
            'description': self.description,
            'test_case': self.test_case.to_dict(),
            'tags': self.tags,
            'source_document': self.source_document,
            'execution_count': self.execution_count,
            'success_count': self.success_count,
            'success_rate': self.success_rate,
            'created_at': self.created_at,
            'updated_at': self.updated_at
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TestCase':
        """从字典创建测试用例"""
        test_case_data = data.get('test_case', {})
        
        # 解析setup
        setup_data = test_case_data.get('setup', {})
        setup = TestCaseSetup(
            database_config=setup_data.get('database_config'),
            initial_data=setup_data.get('initial_data', []),
            environment_setup=setup_data.get('environment_setup')
        )
        
        # 解析steps
        steps_data = test_case_data.get('steps', [])
        steps = [
            TestStep(
                action=step.get('action', ''),
                payload=step.get('payload', {}),
                expected_response=step.get('expected_response'),
                timeout=step.get('timeout', 30),
                retry_count=step.get('retry_count', 0)
            )
            for step in steps_data
        ]
        
        # 解析cleanup
        cleanup_data = test_case_data.get('cleanup', {})
        cleanup = TestCaseCleanup(
            cleanup_sql=cleanup_data.get('cleanup_sql', []),
            cleanup_commands=cleanup_data.get('cleanup_commands', [])
        )
        
        # 解析validation
        validation_data = test_case_data.get('validation', {})
        validation = ValidationRule(
            packet_validation=validation_data.get('packet_validation'),
            response_validation=validation_data.get('response_validation'),
            performance_validation=validation_data.get('performance_validation')
        )
        
        # 创建测试用例定义
        test_case_def = TestCaseDefinition(
            setup=setup,
            steps=steps,
            cleanup=cleanup,
            validation=validation,
            capture_options=test_case_data.get('capture_options')
        )
        
        return cls(
            id=data.get('id', str(uuid.uuid4())),
            name=data.get('name', ''),
            protocol=ProtocolType(data.get('protocol', 'mysql')),
            category=TestCaseCategory(data.get('category', 'normal')),
            description=data.get('description', ''),
            test_case=test_case_def,
            tags=data.get('tags', []),
            source_document=data.get('source_document', ''),
            execution_count=data.get('execution_count', 0),
            success_count=data.get('success_count', 0),
            success_rate=data.get('success_rate', 0.0),
            created_at=data.get('created_at'),
            updated_at=data.get('updated_at')
        )

@dataclass
class TestExecution:
    """测试执行记录"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    test_case_id: str = ""
    server_config_id: Optional[int] = None
    database_config_id: Optional[int] = None
    status: ExecutionStatus = ExecutionStatus.PENDING
    start_time: Optional[str] = None
    end_time: Optional[str] = None
    duration_ms: int = 0
    result: Optional[Dict[str, Any]] = None
    packet_file: Optional[str] = None
    error_message: Optional[str] = None
    execution_log: Optional[str] = None
    validation_result: Optional[Dict[str, Any]] = None
    created_at: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'id': self.id,
            'test_case_id': self.test_case_id,
            'server_config_id': self.server_config_id,
            'database_config_id': self.database_config_id,
            'status': self.status.value,
            'start_time': self.start_time,
            'end_time': self.end_time,
            'duration_ms': self.duration_ms,
            'result': self.result,
            'packet_file': self.packet_file,
            'error_message': self.error_message,
            'execution_log': self.execution_log,
            'validation_result': self.validation_result,
            'created_at': self.created_at
        }

@dataclass
class BatchExecution:
    """批量执行记录"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    test_case_ids: List[str] = field(default_factory=list)
    server_config_id: Optional[int] = None
    database_config_id: Optional[int] = None
    execution_options: Optional[Dict[str, Any]] = None
    status: ExecutionStatus = ExecutionStatus.PENDING
    total_cases: int = 0
    completed_cases: int = 0
    success_cases: int = 0
    failed_cases: int = 0
    start_time: Optional[str] = None
    end_time: Optional[str] = None
    created_at: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'id': self.id,
            'name': self.name,
            'test_case_ids': self.test_case_ids,
            'server_config_id': self.server_config_id,
            'database_config_id': self.database_config_id,
            'execution_options': self.execution_options,
            'status': self.status.value,
            'total_cases': self.total_cases,
            'completed_cases': self.completed_cases,
            'success_cases': self.success_cases,
            'failed_cases': self.failed_cases,
            'start_time': self.start_time,
            'end_time': self.end_time,
            'created_at': self.created_at
        }

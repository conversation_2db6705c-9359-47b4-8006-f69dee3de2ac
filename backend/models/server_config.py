"""
服务器配置数据模型
"""

from dataclasses import dataclass
from typing import Optional, Dict, Any
from datetime import datetime

@dataclass
class ServerConfig:
    """服务器配置"""
    id: Optional[int] = None
    name: str = ""
    host: str = ""
    port: int = 22
    username: str = "root"
    password: str = ""
    description: str = ""
    is_active: bool = True
    is_default: bool = False
    network_interface: Optional[str] = None  # 网络接口名称
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'name': self.name,
            'host': self.host,
            'port': self.port,
            'username': self.username,
            'password': self.password,
            'description': self.description,
            'is_active': self.is_active,
            'is_default': self.is_default,
            'network_interface': self.network_interface,
            'created_at': self.created_at,
            'updated_at': self.updated_at
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ServerConfig':
        """从字典创建实例"""
        return cls(
            id=data.get('id'),
            name=data.get('name', ''),
            host=data.get('host', ''),
            port=data.get('port', 22),
            username=data.get('username', 'root'),
            password=data.get('password', ''),
            description=data.get('description', ''),
            is_active=data.get('is_active', True),
            is_default=data.get('is_default', False),
            network_interface=data.get('network_interface'),
            created_at=data.get('created_at'),
            updated_at=data.get('updated_at')
        )

@dataclass
class NetworkInterface:
    """网络接口信息"""
    name: str
    ip_address: str
    is_up: bool
    interface_type: str  # 'physical', 'loopback', 'docker', 'virtual'
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'name': self.name,
            'ip_address': self.ip_address,
            'is_up': self.is_up,
            'interface_type': self.interface_type
        }

@dataclass
class DatabaseDeployment:
    """数据库部署信息"""
    database_type: str  # 'mysql', 'postgresql', 'mongodb'
    deployment_type: str  # 'native', 'docker'
    host: str
    port: int
    container_name: Optional[str] = None
    container_ip: Optional[str] = None
    process_info: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'database_type': self.database_type,
            'deployment_type': self.deployment_type,
            'host': self.host,
            'port': self.port,
            'container_name': self.container_name,
            'container_ip': self.container_ip,
            'process_info': self.process_info
        }

@dataclass
class CaptureStrategy:
    """抓包策略 - 支持MySQL、PostgreSQL、MongoDB的智能抓包"""
    interface: str
    filter_expression: str
    deployment_info: DatabaseDeployment
    confidence: float  # 0.0 - 1.0, 策略的可信度
    capture_options: Optional[Dict[str, Any]] = None  # 抓包选项配置

    def to_dict(self) -> Dict[str, Any]:
        result = {
            'interface': self.interface,
            'filter_expression': self.filter_expression,
            'deployment_info': self.deployment_info.to_dict(),
            'confidence': self.confidence
        }
        if self.capture_options:
            result['capture_options'] = self.capture_options
        return result

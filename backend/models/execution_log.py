"""
执行日志模型
"""
from pydantic import BaseModel
from typing import Optional, List
from datetime import datetime
import enum


class LogLevel(str, enum.Enum):
    """日志级别"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class LogCategory(str, enum.Enum):
    """日志分类"""
    CAPTURE = "CAPTURE"          # 抓包相关
    SQL_EXECUTION = "SQL_EXECUTION"  # SQL执行相关
    DATABASE_CONNECTION = "DATABASE_CONNECTION"  # 数据库连接相关
    FILE_OPERATION = "FILE_OPERATION"  # 文件操作相关
    SYSTEM = "SYSTEM"            # 系统相关
    TEST_EXECUTION = "TEST_EXECUTION"  # 测试执行相关
    ERROR_HANDLING = "ERROR_HANDLING"  # 错误处理相关


# 注意：数据库表结构在schema.sql中定义，这里只保留Pydantic模型


# Pydantic 模型
class ExecutionLogBase(BaseModel):
    """执行日志基础模型"""
    task_id: Optional[str] = None
    test_case_id: Optional[str] = None
    execution_id: Optional[str] = None
    database_config_id: Optional[int] = None
    level: LogLevel
    category: LogCategory
    message: str
    module_name: Optional[str] = None
    function_name: Optional[str] = None
    line_number: Optional[int] = None
    sql_query: Optional[str] = None
    capture_file: Optional[str] = None
    error_details: Optional[str] = None
    stack_trace: Optional[str] = None
    database_type: Optional[str] = None
    target_host: Optional[str] = None
    target_port: Optional[int] = None


class ExecutionLogCreate(ExecutionLogBase):
    """创建执行日志模型"""
    pass


class ExecutionLogResponse(ExecutionLogBase):
    """执行日志响应模型"""
    id: int
    created_time: datetime
    
    class Config:
        from_attributes = True


class ExecutionLogQuery(BaseModel):
    """执行日志查询模型"""
    task_id: Optional[str] = None
    test_case_id: Optional[str] = None
    execution_id: Optional[str] = None
    database_config_id: Optional[int] = None
    level: Optional[LogLevel] = None
    category: Optional[LogCategory] = None
    database_type: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    keyword: Optional[str] = None  # 在message中搜索关键词
    page: int = 1
    page_size: int = 50


class ExecutionLogStats(BaseModel):
    """执行日志统计模型"""
    total_count: int
    error_count: int
    warning_count: int
    info_count: int
    debug_count: int
    categories: dict  # 各分类的统计
    recent_errors: List[ExecutionLogResponse]  # 最近的错误日志

#!/usr/bin/env python3
"""
查看启动日志工具
"""

import os
import sys
from pathlib import Path
from datetime import datetime

def list_startup_logs():
    """列出所有启动日志文件"""
    from utils.path_manager import path_manager
    log_dir = Path(path_manager.get_log_subdir("startup"))
    
    if not log_dir.exists():
        print("❌ 启动日志目录不存在")
        return []
    
    log_files = list(log_dir.glob("startup_*.log"))
    log_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
    
    return log_files

def view_latest_log():
    """查看最新的启动日志"""
    log_files = list_startup_logs()
    
    if not log_files:
        print("❌ 没有找到启动日志文件")
        return
    
    latest_log = log_files[0]
    print(f"📋 查看最新启动日志: {latest_log.name}")
    print("=" * 60)
    
    try:
        with open(latest_log, 'r', encoding='utf-8') as f:
            content = f.read()
            print(content)
    except Exception as e:
        print(f"❌ 读取日志文件失败: {e}")

def view_all_logs():
    """查看所有启动日志列表"""
    log_files = list_startup_logs()
    
    if not log_files:
        print("❌ 没有找到启动日志文件")
        return
    
    print("📋 启动日志文件列表:")
    print("=" * 60)
    
    for i, log_file in enumerate(log_files, 1):
        # 获取文件修改时间
        mtime = datetime.fromtimestamp(log_file.stat().st_mtime)
        file_size = log_file.stat().st_size
        
        print(f"{i:2d}. {log_file.name}")
        print(f"    📅 时间: {mtime.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"    📏 大小: {file_size} bytes")
        print()

def view_specific_log(index: int):
    """查看指定索引的日志文件"""
    log_files = list_startup_logs()
    
    if not log_files:
        print("❌ 没有找到启动日志文件")
        return
    
    if index < 1 or index > len(log_files):
        print(f"❌ 无效的索引: {index}，有效范围: 1-{len(log_files)}")
        return
    
    log_file = log_files[index - 1]
    print(f"📋 查看启动日志: {log_file.name}")
    print("=" * 60)
    
    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            content = f.read()
            print(content)
    except Exception as e:
        print(f"❌ 读取日志文件失败: {e}")

def tail_latest_log(lines: int = 50):
    """查看最新日志的尾部"""
    log_files = list_startup_logs()
    
    if not log_files:
        print("❌ 没有找到启动日志文件")
        return
    
    latest_log = log_files[0]
    print(f"📋 查看最新启动日志尾部 ({lines}行): {latest_log.name}")
    print("=" * 60)
    
    try:
        with open(latest_log, 'r', encoding='utf-8') as f:
            all_lines = f.readlines()
            tail_lines = all_lines[-lines:] if len(all_lines) > lines else all_lines
            print(''.join(tail_lines))
    except Exception as e:
        print(f"❌ 读取日志文件失败: {e}")

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("📋 启动日志查看工具")
        print("=" * 60)
        print("用法:")
        print("  python view_startup_logs.py latest      # 查看最新日志")
        print("  python view_startup_logs.py list        # 列出所有日志")
        print("  python view_startup_logs.py view <num>  # 查看指定编号的日志")
        print("  python view_startup_logs.py tail [num]  # 查看最新日志尾部(默认50行)")
        print("=" * 60)
        return
    
    command = sys.argv[1].lower()
    
    if command == "latest":
        view_latest_log()
    elif command == "list":
        view_all_logs()
    elif command == "view":
        if len(sys.argv) < 3:
            print("❌ 请指定日志编号")
            return
        try:
            index = int(sys.argv[2])
            view_specific_log(index)
        except ValueError:
            print("❌ 无效的日志编号")
    elif command == "tail":
        lines = 50
        if len(sys.argv) >= 3:
            try:
                lines = int(sys.argv[2])
            except ValueError:
                print("❌ 无效的行数，使用默认值50")
        tail_latest_log(lines)
    else:
        print(f"❌ 未知命令: {command}")

if __name__ == "__main__":
    main()

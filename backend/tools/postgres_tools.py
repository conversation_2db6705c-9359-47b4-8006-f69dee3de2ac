import asyncio
import logging
from typing import Type
from pydantic import BaseModel, Field
from langchain.tools import BaseTool
from services.postgres_service import PostgresService
from services.postgres_local_packet_capture_service import PostgresLocalPacketCaptureService
from utils.config import Config

logger = logging.getLogger(__name__)

# Pydantic模型定义
class PostgresQueryInput(BaseModel):
    """PostgreSQL查询输入模型"""
    sql_query: str = Field(description="PostgreSQL SQL查询语句，如 SELECT * FROM users WHERE age >= 18")

class PostgresPacketCaptureInput(BaseModel):
    """PostgreSQL数据包捕获输入模型"""
    action: str = Field(description="操作类型：start（启动）、stop（停止）、status（状态）")

class PostgresConnectionInput(BaseModel):
    """PostgreSQL连接输入模型"""
    action: str = Field(description="操作类型：status（状态检查）")

class PostgresDatabaseInfoInput(BaseModel):
    """PostgreSQL数据库信息输入模型"""
    action: str = Field(description="操作类型：databases（数据库列表）、tables（表列表）")

class PostgresQueryTool(BaseTool):
    """PostgreSQL查询执行工具"""
    
    name = "postgres_query"
    description = """
    执行PostgreSQL SQL查询的工具。
    输入应该是一个有效的PostgreSQL SQL语句。
    支持SELECT、INSERT、UPDATE、DELETE、CREATE、ALTER、DROP等操作。
    返回查询结果或执行状态。
    
    示例:
    - SELECT * FROM users WHERE age >= 18
    - INSERT INTO users (name, age) VALUES ('张三', 25)
    - UPDATE users SET age = 26 WHERE name = '张三'
    - DELETE FROM users WHERE name = '张三'
    - CREATE TABLE products (id SERIAL PRIMARY KEY, name VARCHAR(100))
    """
    args_schema: Type[BaseModel] = PostgresQueryInput
    
    def __init__(self):
        super().__init__()
        self.postgres_service = None
    
    def _get_postgres_service(self):
        """获取PostgreSQL服务实例"""
        if self.postgres_service is None:
            postgres_config = Config.get_postgres_config()
            self.postgres_service = PostgresService(**postgres_config)
        return self.postgres_service
    
    def _run(self, sql_query: str) -> str:
        """同步执行SQL查询"""
        return asyncio.run(self._arun(sql_query))
    
    async def _arun(self, sql_query: str) -> str:
        """异步执行SQL查询"""
        try:
            postgres_service = self._get_postgres_service()
            result = await postgres_service.execute_sql_query(sql_query)
            
            if result['type'] == 'query':
                return f"查询成功，返回 {result['count']} 条记录：{result['data']}"
            elif result['type'] == 'modification':
                return f"{result['operation']} 操作成功，影响 {result['affected_rows']} 行"
            elif result['type'] == 'ddl':
                return f"{result['operation']} 操作成功：{result['message']}"
            else:
                return f"操作成功：{result.get('message', '执行完成')}"
                
        except Exception as e:
            logger.error(f"PostgreSQL query tool error: {str(e)}")
            return f"PostgreSQL查询执行失败: {str(e)}"

class PostgresPacketCaptureTool(BaseTool):
    """PostgreSQL数据包捕获工具"""
    
    name = "postgres_packet_capture"
    description = """
    PostgreSQL数据包捕获工具。
    可以启动、停止PostgreSQL数据包捕获，或查看捕获状态。
    
    操作类型:
    - start: 启动数据包捕获
    - stop: 停止数据包捕获
    - status: 查看捕获状态
    """
    args_schema: Type[BaseModel] = PostgresPacketCaptureInput
    
    def __init__(self):
        super().__init__()
        self.capture_service = PostgresLocalPacketCaptureService()
    
    def _run(self, action: str) -> str:
        """同步执行数据包捕获操作"""
        return asyncio.run(self._arun(action))
    
    async def _arun(self, action: str) -> str:
        """异步执行数据包捕获操作"""
        try:
            if action == "start":
                packet_file = await self.capture_service.start_capture()
                return f"PostgreSQL数据包捕获已启动，文件: {packet_file}"
            elif action == "stop":
                packet_file = await self.capture_service.stop_capture()
                if packet_file:
                    return f"PostgreSQL数据包捕获已停止，文件: {packet_file}"
                else:
                    return "PostgreSQL数据包捕获已停止，但没有生成文件"
            elif action == "status":
                status = self.capture_service.get_capture_status()
                if status['is_capturing']:
                    return f"PostgreSQL数据包捕获正在进行中，当前文件: {status['current_file']}"
                else:
                    return "PostgreSQL数据包捕获未运行"
            else:
                return f"未知操作: {action}"
        except Exception as e:
            logger.error(f"PostgreSQL packet capture tool error: {str(e)}")
            return f"PostgreSQL数据包捕获操作失败: {str(e)}"

class PostgresConnectionTool(BaseTool):
    """PostgreSQL连接管理工具"""
    
    name = "postgres_connection"
    description = """
    PostgreSQL连接管理工具。
    可以检查PostgreSQL数据库连接状态。
    
    操作类型:
    - status: 检查连接状态
    """
    args_schema: Type[BaseModel] = PostgresConnectionInput
    
    def __init__(self):
        super().__init__()
        self.postgres_service = None
    
    def _get_postgres_service(self):
        """获取PostgreSQL服务实例"""
        if self.postgres_service is None:
            postgres_config = Config.get_postgres_config()
            self.postgres_service = PostgresService(**postgres_config)
        return self.postgres_service
    
    def _run(self, action: str) -> str:
        """同步执行连接管理操作"""
        return asyncio.run(self._arun(action))
    
    async def _arun(self, action: str) -> str:
        """异步执行连接管理操作"""
        try:
            postgres_service = self._get_postgres_service()
            
            if action == "status":
                is_connected = await postgres_service.check_connection()
                if is_connected:
                    return f"PostgreSQL连接正常 ({Config.POSTGRES_HOST}:{Config.POSTGRES_PORT})"
                else:
                    return f"PostgreSQL连接失败 ({Config.POSTGRES_HOST}:{Config.POSTGRES_PORT})"
            else:
                return f"未知操作: {action}"
        except Exception as e:
            logger.error(f"PostgreSQL connection tool error: {str(e)}")
            return f"PostgreSQL连接检查失败: {str(e)}"

class PostgresDatabaseInfoTool(BaseTool):
    """PostgreSQL数据库信息工具"""
    
    name = "postgres_database_info"
    description = """
    PostgreSQL数据库信息工具。
    可以获取数据库列表、表列表等信息。
    
    操作类型:
    - databases: 获取数据库列表
    - tables: 获取当前数据库的表列表
    """
    args_schema: Type[BaseModel] = PostgresDatabaseInfoInput
    
    def __init__(self):
        super().__init__()
        self.postgres_service = None
    
    def _get_postgres_service(self):
        """获取PostgreSQL服务实例"""
        if self.postgres_service is None:
            postgres_config = Config.get_postgres_config()
            self.postgres_service = PostgresService(**postgres_config)
        return self.postgres_service
    
    def _run(self, action: str) -> str:
        """同步执行数据库信息获取操作"""
        return asyncio.run(self._arun(action))
    
    async def _arun(self, action: str) -> str:
        """异步执行数据库信息获取操作"""
        try:
            postgres_service = self._get_postgres_service()
            
            if action == "databases":
                databases = await postgres_service.get_databases()
                return f"数据库列表: {', '.join(databases)}"
            elif action == "tables":
                tables = await postgres_service.get_tables()
                return f"表列表: {', '.join(tables)}"
            else:
                return f"未知操作: {action}"
        except Exception as e:
            logger.error(f"PostgreSQL database info tool error: {str(e)}")
            return f"获取PostgreSQL数据库信息失败: {str(e)}"

# 导出所有PostgreSQL工具
__all__ = [
    'PostgresQueryTool',
    'PostgresPacketCaptureTool', 
    'PostgresConnectionTool',
    'PostgresDatabaseInfoTool'
]

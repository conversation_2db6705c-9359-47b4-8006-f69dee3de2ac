#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
PCAP验证命令行工具 - 独立的抓包验证工具
"""

import os
import sys
import argparse
import json
import glob
import logging
from typing import List, Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.pcap_sql_analyzer import PCAPSQLAnalyzer, analyze_pcap_files, generate_analysis_report
from services.pcap_validation_service import PCAPValidationService, ValidationRule, ExpectedSQL
from services.pcap_sql_analyzer import DatabaseType, SQLType

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class PCAPValidatorCLI:
    """PCAP验证命令行工具"""
    
    def __init__(self):
        self.analyzer = PCAPSQLAnalyzer()
        self.validator = PCAPValidationService()
    
    def analyze_files(self, files: List[str], output_file: str = None, verbose: bool = False) -> Dict[str, Any]:
        """分析PCAP文件中的SQL语句"""
        if verbose:
            logging.getLogger().setLevel(logging.DEBUG)
        
        logger.info(f"开始分析 {len(files)} 个PCAP文件...")
        
        # 分析文件
        results = analyze_pcap_files(files)
        
        # 生成报告
        report = generate_analysis_report(results, output_file)
        
        # 打印摘要
        self._print_analysis_summary(report)
        
        return report
    
    def validate_files(self, files: List[str], rule_name: str = None, 
                      database_type: str = None, expected_operations: List[str] = None,
                      output_file: str = None, verbose: bool = False) -> Dict[str, Any]:
        """验证PCAP文件"""
        if verbose:
            logging.getLogger().setLevel(logging.DEBUG)
        
        logger.info(f"开始验证 {len(files)} 个PCAP文件...")
        
        # 创建自定义规则（如果需要）
        custom_rule = None
        if database_type and expected_operations:
            custom_rule = self._create_custom_rule(database_type, expected_operations)
        
        # 验证文件
        validation_results = []
        for file_path in files:
            if os.path.exists(file_path):
                result = self.validator.validate_pcap_file(
                    file_path,
                    rule_name=rule_name,
                    custom_rule=custom_rule
                )
                validation_results.append(result)
            else:
                logger.warning(f"文件不存在 - {file_path}")
        
        # 生成验证报告
        report = self.validator.generate_validation_report(validation_results, output_file)
        
        # 打印验证摘要
        self._print_validation_summary(report)
        
        return report
    
    def batch_validate_directory(self, directory: str, pattern: str = "*.pcap",
                                database_type: str = None, expected_operations: List[str] = None,
                                output_file: str = None, verbose: bool = False) -> Dict[str, Any]:
        """批量验证目录中的PCAP文件"""
        if not os.path.exists(directory):
            logger.error(f"目录不存在 - {directory}")
            return {}

        # 查找PCAP文件
        search_pattern = os.path.join(directory, pattern)
        files = glob.glob(search_pattern)

        if not files:
            logger.warning(f"在目录 {directory} 中未找到匹配模式 {pattern} 的文件")
            return {}

        logger.info(f"在目录 {directory} 中找到 {len(files)} 个文件")
        
        return self.validate_files(files, database_type=database_type, 
                                 expected_operations=expected_operations,
                                 output_file=output_file, verbose=verbose)
    
    def _create_custom_rule(self, database_type: str, expected_operations: List[str]) -> ValidationRule:
        """创建自定义验证规则"""
        # 映射数据库类型
        db_type_mapping = {
            'mysql': DatabaseType.MYSQL,
            'postgresql': DatabaseType.POSTGRESQL,
            'postgres': DatabaseType.POSTGRESQL,
            'mongodb': DatabaseType.MONGODB,
            'mongo': DatabaseType.MONGODB,
            'oracle': DatabaseType.ORACLE,
            'sqlserver': DatabaseType.SQLSERVER,
            'mssql': DatabaseType.SQLSERVER,
            'gaussdb': DatabaseType.GAUSSDB
        }
        
        database_type_enum = db_type_mapping.get(database_type.lower(), DatabaseType.MYSQL)
        
        # 映射SQL类型
        sql_type_mapping = {
            'SELECT': SQLType.SELECT,
            'INSERT': SQLType.INSERT,
            'UPDATE': SQLType.UPDATE,
            'DELETE': SQLType.DELETE,
            'CREATE': SQLType.CREATE,
            'DROP': SQLType.DROP,
            'ALTER': SQLType.ALTER,
            'GRANT': SQLType.GRANT,
            'REVOKE': SQLType.REVOKE,
            'COMMIT': SQLType.COMMIT,
            'ROLLBACK': SQLType.ROLLBACK
        }
        
        # 创建预期SQL列表
        expected_sqls = []
        for operation in expected_operations:
            operation_upper = operation.upper()
            sql_type = sql_type_mapping.get(operation_upper, SQLType.UNKNOWN)
            
            # 根据数据库类型调整模式
            if database_type_enum == DatabaseType.MONGODB:
                mongo_patterns = {
                    'SELECT': 'find',
                    'INSERT': 'insert',
                    'UPDATE': 'update',
                    'DELETE': 'delete'
                }
                pattern = mongo_patterns.get(operation_upper, operation_upper)
            else:
                pattern = operation_upper
            
            expected_sqls.append(ExpectedSQL(
                sql_pattern=pattern,
                sql_type=sql_type,
                database_type=database_type_enum,
                required=True,
                min_occurrences=1
            ))
        
        return ValidationRule(
            name=f"custom_{database_type}",
            description=f"自定义{database_type}验证规则",
            expected_sqls=expected_sqls,
            min_total_sql_count=1,
            min_confidence=0.5
        )
    
    def _print_analysis_summary(self, report: Dict[str, Any]):
        """记录分析摘要到日志"""
        summary = report.get('summary', {})

        logger.info("=" * 60)
        logger.info("PCAP SQL分析摘要")
        logger.info("=" * 60)
        logger.info(f"总文件数: {summary.get('total_files', 0)}")
        logger.info(f"成功分析: {summary.get('successful_analyses', 0)}")
        logger.info(f"分析失败: {summary.get('failed_analyses', 0)}")
        logger.info(f"总SQL语句数: {summary.get('total_sql_statements', 0)}")
        logger.info(f"总数据包数: {summary.get('total_packets', 0)}")
        logger.info(f"数据库相关包数: {summary.get('total_database_packets', 0)}")

        # SQL类型分布
        sql_stats = report.get('sql_statistics', {})
        if sql_stats.get('by_type'):
            logger.info("SQL类型分布:")
            for sql_type, count in sql_stats['by_type'].items():
                logger.info(f"  {sql_type}: {count}")

        # 数据库类型分布
        if sql_stats.get('by_database'):
            logger.info("数据库类型分布:")
            for db_type, count in sql_stats['by_database'].items():
                logger.info(f"  {db_type}: {count}")

        # 置信度分布
        conf_dist = sql_stats.get('confidence_distribution', {})
        if any(conf_dist.values()):
            logger.info("置信度分布:")
            logger.info(f"  高 (>=0.8): {conf_dist.get('high', 0)}")
            logger.info(f"  中 (0.5-0.8): {conf_dist.get('medium', 0)}")
            logger.info(f"  低 (<0.5): {conf_dist.get('low', 0)}")
    
    def _print_validation_summary(self, report: Dict[str, Any]):
        """记录验证摘要到日志"""
        summary = report.get('summary', {})

        logger.info("=" * 60)
        logger.info("PCAP验证摘要")
        logger.info("=" * 60)
        logger.info(f"总文件数: {summary.get('total_files', 0)}")
        logger.info(f"验证成功: {summary.get('successful_validations', 0)}")
        logger.info(f"验证失败: {summary.get('failed_validations', 0)}")
        logger.info(f"包含SQL的文件: {summary.get('files_with_sql', 0)}")
        logger.info(f"不包含SQL的文件: {summary.get('files_without_sql', 0)}")

        # 成功率
        total = summary.get('total_files', 0)
        success = summary.get('successful_validations', 0)
        if total > 0:
            success_rate = success / total * 100
            logger.info(f"验证成功率: {success_rate:.1f}%")

        # 建议
        recommendations = report.get('recommendations', [])
        if recommendations:
            logger.info("建议:")
            for i, rec in enumerate(recommendations, 1):
                logger.info(f"  {i}. {rec}")

        # 详细结果
        validation_results = report.get('validation_results', [])
        if validation_results:
            logger.info("详细结果:")
            for result in validation_results:
                file_name = os.path.basename(result['file'])
                status = result['status']
                sql_count = result['total_sql_found']
                success = "✓" if result['success'] else "✗"
                logger.info(f"  {success} {file_name}: {status} (SQL: {sql_count})")

                if result.get('error'):
                    logger.error(f"    错误: {result['error']}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="PCAP SQL验证工具")
    
    # 子命令
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 分析命令
    analyze_parser = subparsers.add_parser('analyze', help='分析PCAP文件中的SQL语句')
    analyze_parser.add_argument('files', nargs='+', help='要分析的PCAP文件')
    analyze_parser.add_argument('-o', '--output', help='输出报告文件路径')
    analyze_parser.add_argument('-v', '--verbose', action='store_true', help='详细输出')
    
    # 验证命令
    validate_parser = subparsers.add_parser('validate', help='验证PCAP文件')
    validate_parser.add_argument('files', nargs='+', help='要验证的PCAP文件')
    validate_parser.add_argument('-r', '--rule', help='验证规则名称')
    validate_parser.add_argument('-d', '--database-type', help='数据库类型 (mysql/postgresql/mongodb/oracle/sqlserver)')
    validate_parser.add_argument('-e', '--expected-operations', nargs='+', help='预期的SQL操作 (SELECT/INSERT/UPDATE/DELETE等)')
    validate_parser.add_argument('-o', '--output', help='输出报告文件路径')
    validate_parser.add_argument('-v', '--verbose', action='store_true', help='详细输出')
    
    # 批量验证命令
    batch_parser = subparsers.add_parser('batch', help='批量验证目录中的PCAP文件')
    batch_parser.add_argument('directory', help='包含PCAP文件的目录')
    batch_parser.add_argument('-p', '--pattern', default='*.pcap', help='文件匹配模式 (默认: *.pcap)')
    batch_parser.add_argument('-d', '--database-type', help='数据库类型')
    batch_parser.add_argument('-e', '--expected-operations', nargs='+', help='预期的SQL操作')
    batch_parser.add_argument('-o', '--output', help='输出报告文件路径')
    batch_parser.add_argument('-v', '--verbose', action='store_true', help='详细输出')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    cli = PCAPValidatorCLI()
    
    try:
        if args.command == 'analyze':
            cli.analyze_files(args.files, args.output, args.verbose)
        
        elif args.command == 'validate':
            cli.validate_files(
                args.files,
                rule_name=args.rule,
                database_type=args.database_type,
                expected_operations=args.expected_operations,
                output_file=args.output,
                verbose=args.verbose
            )
        
        elif args.command == 'batch':
            cli.batch_validate_directory(
                args.directory,
                pattern=args.pattern,
                database_type=args.database_type,
                expected_operations=args.expected_operations,
                output_file=args.output,
                verbose=args.verbose
            )
        
    except KeyboardInterrupt:
        logger.info("操作被用户中断")
    except Exception as e:
        logger.error(f"错误: {str(e)}")
        if args.verbose:
            import traceback
            logger.error(f"异常堆栈跟踪: {traceback.format_exc()}")

if __name__ == "__main__":
    main()

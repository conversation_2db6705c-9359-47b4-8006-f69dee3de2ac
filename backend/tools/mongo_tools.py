import asyncio
import logging
from typing import Type
from pydantic import BaseModel, Field
from langchain.tools import BaseTool
from services.mongo_service import MongoService
from services.mongo_local_packet_capture_service import MongoLocalPacketCaptureService
from utils.config import Config

logger = logging.getLogger(__name__)

# Pydantic模型定义
class MongoQueryInput(BaseModel):
    """MongoDB查询输入模型"""
    mongo_query: str = Field(description="MongoDB查询语句，如 db.users.find({\"age\": {\"$gte\": 18}})")

class MongoPacketCaptureInput(BaseModel):
    """MongoDB数据包捕获输入模型"""
    action: str = Field(description="操作类型：start（启动）、stop（停止）、status（状态）")

class MongoConnectionInput(BaseModel):
    """MongoDB连接输入模型"""
    action: str = Field(description="操作类型：status（状态检查）")

class MongoQueryTool(BaseTool):
    """MongoDB查询执行工具"""
    
    name = "mongo_query"
    description = """
    执行MongoDB查询的工具。
    输入应该是一个有效的MongoDB查询语句。
    支持find、insert、update、delete、aggregate等操作。
    返回查询结果或执行状态。
    
    示例:
    - db.users.find({"age": {"$gte": 18}})
    - db.users.insertOne({"name": "张三", "age": 25})
    - db.users.updateOne({"name": "张三"}, {"$set": {"age": 26}})
    - db.users.deleteOne({"name": "张三"})
    """
    args_schema: Type[BaseModel] = MongoQueryInput
    
    def __init__(self):
        super().__init__()
        self.mongo_service = None
    
    def _get_mongo_service(self):
        """获取MongoDB服务实例"""
        if self.mongo_service is None:
            mongo_config = Config.get_mongo_config()
            self.mongo_service = MongoService(**mongo_config)
        return self.mongo_service
    
    def _run(self, mongo_query: str) -> str:
        """同步执行MongoDB查询"""
        try:
            # 在新的事件循环中运行异步代码
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                mongo_service = self._get_mongo_service()
                result = loop.run_until_complete(mongo_service.execute_mongo_query(mongo_query))
                return str(result)
            finally:
                loop.close()
        except Exception as e:
            logger.error(f"MongoDB query tool error: {str(e)}")
            return f"查询执行失败: {str(e)}"
    
    async def _arun(self, mongo_query: str) -> str:
        """异步执行MongoDB查询"""
        try:
            mongo_service = self._get_mongo_service()
            result = await mongo_service.execute_mongo_query(mongo_query)
            return str(result)
        except Exception as e:
            logger.error(f"MongoDB query tool error: {str(e)}")
            return f"查询执行失败: {str(e)}"

class MongoPacketCaptureTool(BaseTool):
    """MongoDB数据包捕获工具"""
    
    name = "mongo_packet_capture"
    description = """
    控制MongoDB数据包捕获的工具。
    支持的操作:
    - start: 启动数据包捕获
    - stop: 停止数据包捕获
    - status: 获取捕获状态
    输入应该是操作类型。
    """
    args_schema: Type[BaseModel] = MongoPacketCaptureInput
    
    def __init__(self):
        super().__init__()
        self.packet_service = None
    
    def _get_packet_service(self):
        """获取MongoDB数据包捕获服务实例"""
        if self.packet_service is None:
            self.packet_service = MongoLocalPacketCaptureService()
        return self.packet_service
    
    def _run(self, action: str) -> str:
        """同步执行MongoDB数据包捕获操作"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                packet_service = self._get_packet_service()
                
                if action == "start":
                    file_path = loop.run_until_complete(packet_service.start_capture())
                    return f"MongoDB数据包捕获已启动，文件: {file_path}"
                elif action == "stop":
                    file_path = loop.run_until_complete(packet_service.stop_capture())
                    return f"MongoDB数据包捕获已停止，文件: {file_path}"
                elif action == "status":
                    is_capturing = packet_service.is_capturing_active()
                    current_file = packet_service.get_current_file()
                    status = "正在捕获" if is_capturing else "未捕获"
                    return f"捕获状态: {status}, 当前文件: {current_file or '无'}"
                else:
                    return f"未知操作: {action}"
            finally:
                loop.close()
        except Exception as e:
            logger.error(f"MongoDB packet capture tool error: {str(e)}")
            return f"MongoDB数据包捕获操作失败: {str(e)}"
    
    async def _arun(self, action: str) -> str:
        """异步执行MongoDB数据包捕获操作"""
        try:
            packet_service = self._get_packet_service()
            
            if action == "start":
                file_path = await packet_service.start_capture()
                return f"MongoDB数据包捕获已启动，文件: {file_path}"
            elif action == "stop":
                file_path = await packet_service.stop_capture()
                return f"MongoDB数据包捕获已停止，文件: {file_path}"
            elif action == "status":
                is_capturing = packet_service.is_capturing_active()
                current_file = packet_service.get_current_file()
                status = "正在捕获" if is_capturing else "未捕获"
                return f"捕获状态: {status}, 当前文件: {current_file or '无'}"
            else:
                return f"未知操作: {action}"
        except Exception as e:
            logger.error(f"MongoDB packet capture tool error: {str(e)}")
            return f"MongoDB数据包捕获操作失败: {str(e)}"

class MongoConnectionTool(BaseTool):
    """MongoDB连接工具"""
    
    name = "mongo_connection"
    description = """
    MongoDB连接管理工具。
    支持的操作:
    - status: 检查MongoDB连接状态
    输入应该是操作类型。
    """
    args_schema: Type[BaseModel] = MongoConnectionInput
    
    def __init__(self):
        super().__init__()
        self.mongo_service = None
    
    def _get_mongo_service(self):
        """获取MongoDB服务实例"""
        if self.mongo_service is None:
            mongo_config = Config.get_mongo_config()
            self.mongo_service = MongoService(**mongo_config)
        return self.mongo_service
    
    def _run(self, action: str = "status") -> str:
        """同步执行连接操作"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                mongo_service = self._get_mongo_service()
                
                if action == "status":
                    is_connected = loop.run_until_complete(mongo_service.check_connection())
                    return f"MongoDB连接状态: {'正常' if is_connected else '异常'}"
                else:
                    return f"未知操作: {action}"
            finally:
                loop.close()
        except Exception as e:
            logger.error(f"MongoDB connection tool error: {str(e)}")
            return f"连接操作失败: {str(e)}"
    
    async def _arun(self, action: str = "status") -> str:
        """异步执行连接操作"""
        try:
            mongo_service = self._get_mongo_service()
            
            if action == "status":
                is_connected = await mongo_service.check_connection()
                return f"MongoDB连接状态: {'正常' if is_connected else '异常'}"
            else:
                return f"未知操作: {action}"
        except Exception as e:
            logger.error(f"MongoDB connection tool error: {str(e)}")
            return f"连接操作失败: {str(e)}"

class MongoDatabaseInfoTool(BaseTool):
    """MongoDB数据库信息工具"""
    
    name = "mongo_database_info"
    description = """
    获取MongoDB数据库信息的工具。
    可以获取数据库列表、集合列表等信息。
    输入应该是操作类型：databases（获取数据库列表）、collections（获取集合列表）。
    """
    args_schema: Type[BaseModel] = MongoConnectionInput
    
    def __init__(self):
        super().__init__()
        self.mongo_service = None
    
    def _get_mongo_service(self):
        """获取MongoDB服务实例"""
        if self.mongo_service is None:
            mongo_config = Config.get_mongo_config()
            self.mongo_service = MongoService(**mongo_config)
        return self.mongo_service
    
    def _run(self, action: str) -> str:
        """同步执行数据库信息获取操作"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                mongo_service = self._get_mongo_service()
                
                if action == "databases":
                    databases = loop.run_until_complete(mongo_service.get_databases())
                    return f"数据库列表: {', '.join(databases)}"
                elif action == "collections":
                    collections = loop.run_until_complete(mongo_service.get_collections())
                    return f"集合列表: {', '.join(collections)}"
                else:
                    return f"未知操作: {action}"
            finally:
                loop.close()
        except Exception as e:
            logger.error(f"MongoDB database info tool error: {str(e)}")
            return f"获取数据库信息失败: {str(e)}"
    
    async def _arun(self, action: str) -> str:
        """异步执行数据库信息获取操作"""
        try:
            mongo_service = self._get_mongo_service()
            
            if action == "databases":
                databases = await mongo_service.get_databases()
                return f"数据库列表: {', '.join(databases)}"
            elif action == "collections":
                collections = await mongo_service.get_collections()
                return f"集合列表: {', '.join(collections)}"
            else:
                return f"未知操作: {action}"
        except Exception as e:
            logger.error(f"MongoDB database info tool error: {str(e)}")
            return f"获取数据库信息失败: {str(e)}"

# 导出所有MongoDB工具
__all__ = [
    'MongoQueryTool',
    'MongoPacketCaptureTool', 
    'MongoConnectionTool',
    'MongoDatabaseInfoTool'
]

"""
测试用例执行相关的LangChain工具
"""
import json
import re
import logging
from typing import Dict, Any, List, Optional, Tuple
from enum import Enum
from pydantic import BaseModel, Field
from langchain.tools import BaseTool

logger = logging.getLogger(__name__)

class ExecutionPhase(Enum):
    """执行阶段枚举"""
    SETUP = "setup"           # 环境准备阶段
    EXECUTION = "execution"   # 查询执行阶段
    VALIDATION = "validation" # 验证阶段
    CLEANUP = "cleanup"       # 清理阶段

class SQLType(Enum):
    """SQL类型枚举"""
    DDL = "ddl"  # CREATE, DROP, ALTER
    DML = "dml"  # INSERT, UPDATE, DELETE  
    DQL = "dql"  # SELECT
    DCL = "dcl"  # GRANT, REVOKE
    TCL = "tcl"  # COMMIT, ROLLBACK
    UNKNOWN = "unknown"

class CaptureStrategy(Enum):
    """抓包策略枚举"""
    NO_CAPTURE = "no_capture"      # 不需要抓包
    CAPTURE_REQUIRED = "capture"   # 需要抓包
    OPTIONAL_CAPTURE = "optional"  # 可选抓包

class TestStep(BaseModel):
    """测试步骤数据模型"""
    step_number: int
    action: str
    expected_result: str
    test_data: Optional[str] = None
    sql_statements: List[str] = Field(default_factory=list)
    phase: ExecutionPhase
    sql_types: List[SQLType] = Field(default_factory=list)
    capture_strategy: CaptureStrategy = CaptureStrategy.CAPTURE_REQUIRED

class TestCaseParseInput(BaseModel):
    """测试用例解析输入"""
    test_case_json: str = Field(description="测试用例的JSON字符串")

class SQLClassifyInput(BaseModel):
    """SQL分类输入"""
    sql_statements: List[str] = Field(description="需要分类的SQL语句列表")

class ExecutionPlanInput(BaseModel):
    """执行计划输入"""
    test_steps: List[Dict[str, Any]] = Field(description="测试步骤列表")

class DatabaseSQLParser:
    """数据库特定的SQL解析器基类"""

    def split_sql_statements(self, text: str) -> List[str]:
        """分割SQL语句 - 子类可以重写以支持特定语法"""
        return self._default_split_sql_statements(text)

    def _default_split_sql_statements(self, text: str) -> List[str]:
        """默认的SQL分割逻辑"""
        statements = []
        current_statement = ""
        in_string = False
        string_char = None
        i = 0

        while i < len(text):
            char = text[i]

            # 处理字符串
            if char in ("'", '"') and not in_string:
                in_string = True
                string_char = char
                current_statement += char
            elif char == string_char and in_string:
                # 检查是否是转义的引号
                if i > 0 and text[i-1] != '\\':
                    in_string = False
                    string_char = None
                current_statement += char
            elif char == ';' and not in_string:
                # 分号且不在字符串中，分割语句
                current_statement += char  # 包含分号
                if current_statement.strip():
                    statements.append(current_statement.strip())
                current_statement = ""
            else:
                current_statement += char

            i += 1

        # 添加最后一个语句（可能没有分号结尾）
        if current_statement.strip():
            statements.append(current_statement.strip())

        return statements

class PostgreSQLParser(DatabaseSQLParser):
    """PostgreSQL特定的SQL解析器"""

    def split_sql_statements(self, text: str) -> List[str]:
        """支持PostgreSQL美元引号的SQL分割"""
        statements = []
        current_statement = ""
        in_string = False
        string_char = None
        in_dollar_quote = False
        dollar_tag = None
        i = 0

        while i < len(text):
            char = text[i]

            # 处理PostgreSQL美元引号 ($$...$$)
            if char == '$' and not in_string and not in_dollar_quote:
                # 查找美元引号标签
                dollar_start = i
                tag_end = i + 1
                while tag_end < len(text) and text[tag_end] != '$':
                    tag_end += 1

                if tag_end < len(text) and text[tag_end] == '$':
                    # 找到完整的美元引号开始标签
                    dollar_tag = text[dollar_start:tag_end + 1]
                    in_dollar_quote = True
                    current_statement += dollar_tag
                    i = tag_end
                else:
                    current_statement += char
            elif in_dollar_quote and char == '$' and dollar_tag:
                # 检查是否是匹配的美元引号结束标签
                if text[i:i + len(dollar_tag)] == dollar_tag:
                    current_statement += dollar_tag
                    in_dollar_quote = False
                    tag_len = len(dollar_tag)
                    dollar_tag = None
                    i += tag_len - 1
                else:
                    current_statement += char
            # 处理普通字符串
            elif char in ("'", '"') and not in_string and not in_dollar_quote:
                in_string = True
                string_char = char
                current_statement += char
            elif char == string_char and in_string and not in_dollar_quote:
                # 检查是否是转义的引号
                if i > 0 and text[i-1] != '\\':
                    in_string = False
                    string_char = None
                current_statement += char
            elif char == ';' and not in_string and not in_dollar_quote:
                # 分号且不在字符串或美元引号中，分割语句
                current_statement += char  # 包含分号
                if current_statement.strip():
                    statements.append(current_statement.strip())
                current_statement = ""
            else:
                current_statement += char

            i += 1

        # 添加最后一个语句（可能没有分号结尾）
        if current_statement.strip():
            statements.append(current_statement.strip())

        return statements

class MySQLParser(DatabaseSQLParser):
    """MySQL特定的SQL解析器"""

    def split_sql_statements(self, text: str) -> List[str]:
        """支持MySQL特定语法的SQL分割"""
        # MySQL可能需要处理存储过程的DELIMITER语句
        # 目前使用默认逻辑，后续可以扩展
        return self._default_split_sql_statements(text)

class OracleParser(DatabaseSQLParser):
    """Oracle特定的SQL解析器"""

    def split_sql_statements(self, text: str) -> List[str]:
        """支持Oracle特定语法的SQL分割"""
        # Oracle可能需要处理PL/SQL块
        # 目前使用默认逻辑，后续可以扩展
        return self._default_split_sql_statements(text)

class MongoDBParser(DatabaseSQLParser):
    """MongoDB特定的查询解析器"""

    def split_sql_statements(self, text: str) -> List[str]:
        """MongoDB查询解析，支持JavaScript代码和复杂表达式"""
        if not text.strip():
            return []

        # 如果包含for循环，需要特殊处理
        if 'for' in text and '{' in text and '}' in text:
            return self._parse_javascript_mongodb_code(text)

        # 按分号分割，但保持MongoDB命令的完整性
        statements = []
        current_statement = ""
        brace_count = 0
        paren_count = 0
        in_string = False
        string_char = None

        i = 0
        while i < len(text):
            char = text[i]

            # 处理字符串
            if char in ['"', "'"] and not in_string:
                in_string = True
                string_char = char
            elif char == string_char and in_string:
                in_string = False
                string_char = None
            elif in_string:
                current_statement += char
                i += 1
                continue

            # 处理括号和大括号
            if char == '(':
                paren_count += 1
            elif char == ')':
                paren_count -= 1
            elif char == '{':
                brace_count += 1
            elif char == '}':
                brace_count -= 1
            elif char == ';' and paren_count == 0 and brace_count == 0:
                # 分号且不在括号或大括号中，分割语句
                if current_statement.strip():
                    statements.append(current_statement.strip())
                current_statement = ""
                i += 1
                continue

            current_statement += char
            i += 1

        # 添加最后一个语句
        if current_statement.strip():
            statements.append(current_statement.strip())

        return statements

    def _parse_javascript_mongodb_code(self, text: str) -> List[str]:
        """解析包含JavaScript代码的MongoDB命令"""
        statements = []

        # 检查是否包含for循环
        import re
        for_pattern = r'for\s*\([^)]*\)\s*\{\s*([^}]*)\s*\}'
        for_matches = re.findall(for_pattern, text, re.DOTALL)

        if for_matches:
            # 首先提取for循环外的其他MongoDB命令
            # 使用更精确的正则表达式来匹配嵌套的大括号
            for_loop_pattern = r'(for\s*\([^)]*\)\s*\{(?:[^{}]|{[^{}]*})*\})'

            # 移除for循环部分，然后提取剩余的MongoDB命令
            text_without_loops = re.sub(for_loop_pattern, '', text, flags=re.DOTALL)
            # 使用更精确的正则表达式来匹配MongoDB命令，支持嵌套括号
            other_commands = self._extract_mongodb_commands_with_nested_parens(text_without_loops)
            statements.extend(other_commands)

            # 然后添加完整的for循环结构
            full_for_loops = re.findall(for_loop_pattern, text, re.DOTALL)
            statements.extend(full_for_loops)
        else:
            # 如果没有for循环，直接提取MongoDB命令，支持嵌套括号
            mongo_commands = self._extract_mongodb_commands_with_nested_parens(text)
            statements.extend(mongo_commands)

        return statements if statements else [text.strip()]

    def _extract_mongodb_commands_with_nested_parens(self, text: str) -> List[str]:
        """提取MongoDB命令，支持嵌套括号"""
        import re
        commands = []

        # 查找所有MongoDB命令的起始位置，支持db.method()和db.collection.method()两种格式
        patterns = [
            r'db\.[a-zA-Z_][a-zA-Z0-9_]*\.[a-zA-Z_][a-zA-Z0-9_]*\(',  # db.collection.method(
            r'db\.[a-zA-Z_][a-zA-Z0-9_]*\('  # db.method(
        ]

        all_matches = []
        for pattern in patterns:
            matches = list(re.finditer(pattern, text))
            all_matches.extend(matches)

        # 按位置排序，避免重复
        all_matches.sort(key=lambda x: x.start())

        # 去重：如果两个匹配有重叠，保留更长的那个
        unique_matches = []
        for match in all_matches:
            is_duplicate = False
            for existing in unique_matches:
                if (match.start() >= existing.start() and
                    match.start() < existing.end()):
                    is_duplicate = True
                    break
            if not is_duplicate:
                unique_matches.append(match)

        for match in unique_matches:
            start = match.start()
            # 从开括号开始计算嵌套层级
            paren_start = match.end() - 1  # 指向开括号
            paren_count = 1
            i = paren_start + 1

            # 找到匹配的闭括号
            while i < len(text) and paren_count > 0:
                if text[i] == '(':
                    paren_count += 1
                elif text[i] == ')':
                    paren_count -= 1
                i += 1

            if paren_count == 0:
                # 找到完整的命令
                command = text[start:i]
                commands.append(command)

        return commands

class TestCaseParserTool(BaseTool):
    """测试用例解析工具"""

    name: str = "test_case_parser"
    description: str = """
    解析测试用例JSON结构，提取test_steps中的SQL语句、test_data和执行顺序。
    返回结构化的测试步骤信息。
    """
    args_schema: type[BaseModel] = TestCaseParseInput

    # 类级别的解析器实例
    _parsers = {
        'postgresql': PostgreSQLParser(),
        'mysql': MySQLParser(),
        'oracle': OracleParser(),
        'mongodb': MongoDBParser(),
        'gaussdb': PostgreSQLParser(),  # GaussDB基于PostgreSQL
    }
    _default_parser = DatabaseSQLParser()

    def _get_parser(self, database_type: str = None) -> DatabaseSQLParser:
        """获取数据库特定的解析器"""
        return self._parsers.get(database_type, self._default_parser)
    
    def _run(self, test_case_json: str, database_type: str = None) -> str:
        """解析测试用例"""
        logger.info("=== 开始解析测试用例 ===")
        logger.info(f"数据库类型: {database_type}")
        logger.debug(f"输入JSON长度: {len(test_case_json)} 字符")
        logger.debug(f"输入JSON前200字符: {test_case_json[:200]}...")

        try:
            # 解析JSON
            logger.info("正在解析测试用例JSON...")
            test_case = json.loads(test_case_json)
            logger.info("测试用例JSON解析成功")

            # 记录基本信息
            test_case_id = test_case.get('id')
            title = test_case.get('title')
            logger.info(f"测试用例ID: {test_case_id}")
            logger.info(f"测试用例标题: {title}")

            # 提取测试步骤（支持两种字段名）
            test_steps = test_case.get('test_steps', test_case.get('steps', []))
            logger.info(f"找到 {len(test_steps)} 个测试步骤")

            parsed_steps = []

            for i, step in enumerate(test_steps):
                logger.info(f"正在解析步骤 {i+1}/{len(test_steps)}: {step.get('action', 'unknown')}")
                step_info = self._parse_single_step(step, database_type)
                parsed_steps.append(step_info)
                logger.debug(f"步骤 {i+1} 解析完成，提取到 {len(step_info.sql_statements)} 个SQL语句")
            
            logger.info(f"所有步骤解析完成，共解析 {len(parsed_steps)} 个步骤")

            result = {
                "test_case_id": test_case.get('id'),
                "title": test_case.get('title'),
                "total_steps": len(parsed_steps),
                "parsed_steps": [self._step_to_dict(step) for step in parsed_steps]
            }

            logger.info("=== 测试用例解析完成 ===")
            logger.info(f"解析结果: 测试用例ID={result['test_case_id']}, 标题={result['title']}, 步骤数={result['total_steps']}")
            logger.debug(f"返回JSON长度: {len(json.dumps(result))} 字符")

            return json.dumps(result, ensure_ascii=False, indent=2)

        except Exception as e:
            logger.error(f"=== 测试用例解析失败 ===")
            logger.error(f"错误信息: {str(e)}")
            logger.error(f"输入JSON: {test_case_json}")
            import traceback
            logger.error(f"错误堆栈: {traceback.format_exc()}")
            return f"解析失败: {str(e)}"

    def _step_to_dict(self, step: TestStep) -> Dict[str, Any]:
        """将TestStep对象转换为字典"""
        return {
            "step_number": step.step_number,
            "action": step.action,
            "expected_result": step.expected_result,
            "test_data": step.test_data,
            "sql_statements": step.sql_statements,
            "phase": step.phase.value,
            "sql_types": [sql_type.value for sql_type in step.sql_types],
            "capture_strategy": step.capture_strategy.value
        }
    
    def _parse_single_step(self, step: Dict[str, Any], database_type: str = None) -> TestStep:
        """解析单个测试步骤"""
        step_number = step.get('step_number', 0)
        action = step.get('action', '')
        expected_result = step.get('expected_result', '')
        test_data = step.get('test_data')

        # 提取SQL语句
        sql_statements = self._extract_sql_from_step(step, database_type)
        
        # 判断执行阶段
        phase = self._determine_phase(action, step_number)
        
        # 分类SQL语句
        sql_types = [self._classify_sql(sql) for sql in sql_statements]
        
        # 确定抓包策略（优先使用步骤中明确指定的策略）
        explicit_strategy = step.get('capture_strategy')
        if explicit_strategy:
            # 将字符串转换为CaptureStrategy枚举
            if explicit_strategy.lower() == 'capture':
                capture_strategy = CaptureStrategy.CAPTURE_REQUIRED
            elif explicit_strategy.lower() == 'optional':
                capture_strategy = CaptureStrategy.OPTIONAL_CAPTURE
            elif explicit_strategy.lower() == 'no_capture':
                capture_strategy = CaptureStrategy.NO_CAPTURE
            else:
                capture_strategy = self._determine_capture_strategy(sql_types, phase)
        else:
            capture_strategy = self._determine_capture_strategy(sql_types, phase)
        
        return TestStep(
            step_number=step_number,
            action=action,
            expected_result=expected_result,
            test_data=test_data,
            sql_statements=sql_statements,
            phase=phase,
            sql_types=sql_types,
            capture_strategy=capture_strategy
        )
    
    def _extract_sql_from_step(self, step: Dict[str, Any], database_type: str = None) -> List[str]:
        """从测试步骤中提取SQL语句 - 统一从test_data字段提取"""
        sql_statements = []

        # 统一从test_data中提取SQL语句
        test_data = step.get('test_data')
        if test_data:
            # 直接解析SQL语句（标准格式）
            if self._is_direct_sql(test_data):
                # test_data直接包含SQL语句，按分号分割
                sql_statements.extend(self._split_sql_statements(test_data, database_type))
            else:
                # 如果不是直接SQL，尝试从文本中提取
                sql_statements.extend(self._extract_sql_from_text(test_data, database_type))

        # 清理并去重SQL语句
        cleaned_statements = []
        seen = set()

        for sql in sql_statements:
            if not sql or not sql.strip():
                continue

            # 标准化SQL语句用于比较
            normalized_sql = ' '.join(sql.strip().split()).upper()

            if normalized_sql not in seen:
                seen.add(normalized_sql)
                cleaned_statements.append(sql.strip())

        return cleaned_statements
    
    def _extract_sql_from_text(self, text: str, database_type: str = None) -> List[str]:
        """从文本中提取SQL语句"""
        if not text:
            return []

        # 首先尝试按分号分割多个SQL语句
        statements = []

        # 按分号分割，但要考虑字符串中的分号
        parts = self._split_sql_statements(text, database_type)

        for part in parts:
            part = part.strip()
            if not part:
                continue

            # 清理注释
            part = self._clean_sql_comments(part)
            if not part:
                continue

            # 检查是否是有效的SQL语句
            if self._is_valid_sql_statement(part):
                # 清理并修复SQL语句
                cleaned_part = self._clean_sql_statement(part)
                if cleaned_part:
                    statements.append(cleaned_part)

        # 如果按分号分割没有找到语句，使用正则表达式模式匹配
        if not statements:
            statements = self._extract_sql_with_patterns(text)

        # 去重并保持顺序
        seen = set()
        unique_statements = []
        for stmt in statements:
            if stmt not in seen:
                seen.add(stmt)
                unique_statements.append(stmt)

        return unique_statements

    def _is_direct_sql(self, text: str) -> bool:
        """判断text_data是否直接包含SQL语句或MongoDB命令"""
        if not text or not text.strip():
            return False

        text_upper = text.upper().strip()

        # 检查是否以SQL关键词开始
        sql_keywords = [
            'SELECT', 'INSERT', 'UPDATE', 'DELETE', 'CREATE', 'DROP', 'ALTER',
            'SHOW', 'DESCRIBE', 'EXPLAIN', 'WITH', 'GRANT', 'REVOKE',
            'COMMIT', 'ROLLBACK', 'START', 'BEGIN'
        ]

        # 检查MongoDB命令
        if text.strip().startswith('db.') or 'db.' in text:
            return True

        # 检查JavaScript for循环（MongoDB常用）
        if 'for' in text and '{' in text and '}' in text and 'db.' in text:
            return True

        # 检查是否以SQL关键词开始，或者包含分号分隔的多个SQL语句
        starts_with_sql = any(text_upper.startswith(keyword) for keyword in sql_keywords)
        contains_semicolon = ';' in text

        # 如果以SQL关键词开始，或者包含分号且包含SQL关键词，则认为是直接SQL
        if starts_with_sql:
            return True

        if contains_semicolon:
            # 检查分号分隔的各部分是否包含SQL关键词或MongoDB命令
            parts = text.split(';')
            for part in parts:
                part_upper = part.strip().upper()
                part_lower = part.strip()
                if (any(part_upper.startswith(keyword) for keyword in sql_keywords) or
                    part_lower.startswith('db.')):
                    return True

        return False

    def _split_sql_statements(self, text: str, database_type: str = None) -> List[str]:
        """智能分割SQL语句，使用数据库特定的解析器"""
        # 获取数据库特定的解析器
        parser = self._get_parser(database_type)
        return parser.split_sql_statements(text)

    def _clean_sql_comments(self, sql: str) -> str:
        """清理SQL注释并提取SQL语句"""
        lines = sql.split('\n')
        sql_lines = []
        found_sql_start = False

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # 移除行注释
            if '--' in line:
                line = line[:line.index('--')].strip()
                if not line:
                    continue

            # 检查这一行是否是SQL语句的开始
            line_upper = line.upper()
            sql_start_keywords = [
                'SELECT', 'INSERT', 'UPDATE', 'DELETE', 'CREATE', 'DROP', 'ALTER',
                'SHOW', 'DESCRIBE', 'EXPLAIN', 'WITH', 'GRANT', 'REVOKE',
                'COMMIT', 'ROLLBACK', 'START', 'BEGIN'
            ]

            # 如果找到SQL开始关键词，标记开始收集SQL语句
            if any(line_upper.startswith(keyword) for keyword in sql_start_keywords):
                found_sql_start = True
                sql_lines.append(line)
            elif found_sql_start:
                # 已经开始收集SQL语句，继续收集后续行
                sql_lines.append(line)

        return ' '.join(sql_lines)

    def _is_valid_sql_statement(self, sql: str) -> bool:
        """检查是否是有效的SQL语句"""
        sql_upper = sql.upper().strip()

        # SQL关键词
        sql_keywords = [
            'SELECT', 'INSERT', 'UPDATE', 'DELETE', 'CREATE', 'DROP', 'ALTER',
            'SHOW', 'DESCRIBE', 'EXPLAIN', 'WITH', 'GRANT', 'REVOKE',
            'COMMIT', 'ROLLBACK', 'START', 'BEGIN'
        ]

        # 检查是否包含SQL关键词（不一定在开头）
        return any(keyword in sql_upper for keyword in sql_keywords)

    def _extract_sql_with_patterns(self, text: str) -> List[str]:
        """使用正则表达式模式提取SQL语句"""
        sql_statements = []

        # 首先尝试按行分析，识别多行SQL语句
        lines = text.split('\n')
        current_sql = ""
        in_sql_block = False

        for line in lines:
            line = line.strip()
            if not line or line.startswith('--'):
                continue

            line_upper = line.upper()

            # 检查是否是SQL语句的开始
            sql_keywords = ['CREATE', 'INSERT', 'SELECT', 'UPDATE', 'DELETE', 'DROP', 'ALTER',
                          'SHOW', 'DESCRIBE', 'EXPLAIN', 'WITH', 'GRANT', 'REVOKE']

            is_sql_start = any(line_upper.startswith(keyword) for keyword in sql_keywords)

            if is_sql_start:
                # 如果之前有未完成的SQL，先保存
                if current_sql.strip():
                    cleaned_sql = self._clean_sql_statement(current_sql.strip())
                    if cleaned_sql:
                        sql_statements.append(cleaned_sql)
                current_sql = line
                in_sql_block = True
            elif in_sql_block:
                # 继续当前SQL语句
                current_sql += " " + line

            # 检查SQL语句是否结束（以分号结尾）
            if line.endswith(';') and in_sql_block:
                cleaned_sql = self._clean_sql_statement(current_sql.strip())
                if cleaned_sql:
                    sql_statements.append(cleaned_sql)
                current_sql = ""
                in_sql_block = False

        # 添加最后一个SQL语句
        if current_sql.strip():
            cleaned_sql = self._clean_sql_statement(current_sql.strip())
            if cleaned_sql:
                sql_statements.append(cleaned_sql)

        # 如果上述方法没有找到SQL语句，使用正则表达式
        if not sql_statements:
            sql_statements = self._extract_with_regex_patterns(text)

        return sql_statements

    def _extract_with_regex_patterns(self, text: str) -> List[str]:
        """使用正则表达式模式提取SQL语句"""
        # 扩展的SQL关键词模式，使用更宽松的匹配
        sql_patterns = [
            # CREATE语句 - 支持多行
            r'(CREATE\s+(?:TABLE|INDEX|VIEW|MATERIALIZED\s+VIEW)\s+[^;]*(?:;|$))',
            # INSERT语句 - 支持多行
            r'(INSERT\s+INTO\s+[^;]*(?:;|$))',
            # SELECT语句 - 支持多行
            r'(SELECT\s+[^;]*(?:;|$))',
            # UPDATE语句
            r'(UPDATE\s+[^;]*(?:;|$))',
            # DELETE语句
            r'(DELETE\s+FROM\s+[^;]*(?:;|$))',
            # DROP语句
            r'(DROP\s+(?:TABLE|INDEX|VIEW)\s+[^;]*(?:;|$))',
            # ALTER语句
            r'(ALTER\s+TABLE\s+[^;]*(?:;|$))',
            # SHOW语句
            r'(SHOW\s+[^;]*(?:;|$))',
            # DESCRIBE语句
            r'(DESCRIBE\s+[^;]*(?:;|$))',
            # EXPLAIN语句 - 支持ANALYZE
            r'(EXPLAIN\s+(?:ANALYZE\s+)?[^;]*(?:;|$))',
            # WITH语句
            r'(WITH\s+[^;]*(?:;|$))',
            # GRANT语句
            r'(GRANT\s+[^;]*(?:;|$))',
            # REVOKE语句
            r'(REVOKE\s+[^;]*(?:;|$))',
        ]

        # MongoDB命令模式 - 修复正则表达式以支持嵌套括号和复杂表达式
        mongodb_patterns = [
            # 基本的db.collection.method()模式，支持嵌套括号
            r'(db\.[a-zA-Z_][a-zA-Z0-9_]*\.[a-zA-Z_][a-zA-Z0-9_]*\([^()]*(?:\([^()]*\)[^()]*)*\))',
            # 链式调用模式
            r'(db\.[a-zA-Z_][a-zA-Z0-9_]*\.[a-zA-Z_][a-zA-Z0-9_]*\([^()]*(?:\([^()]*\)[^()]*)*\)(?:\.[a-zA-Z_][a-zA-Z0-9_]*\([^()]*(?:\([^()]*\)[^()]*)*\))*)',
            # for循环中的MongoDB命令
            r'(for\s*\([^)]*\)\s*\{\s*db\.[a-zA-Z_][a-zA-Z0-9_]*\.[a-zA-Z_][a-zA-Z0-9_]*\([^}]*\)\s*\})',
            # 多行MongoDB命令（包含换行符）
            r'(db\.[a-zA-Z_][a-zA-Z0-9_]*\.[a-zA-Z_][a-zA-Z0-9_]*\([^;]*?(?:\n[^;]*?)*?\))',
        ]

        sql_statements = []

        # 提取传统SQL语句
        for pattern in sql_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE | re.DOTALL | re.MULTILINE)
            for match in matches:
                # 清理匹配的SQL语句
                cleaned_sql = self._clean_sql_statement(match)
                if cleaned_sql:
                    sql_statements.append(cleaned_sql)

        # 提取MongoDB命令
        for pattern in mongodb_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE | re.DOTALL)
            sql_statements.extend(matches)

        return sql_statements

    def _clean_sql_statement(self, sql: str) -> str:
        """清理SQL语句"""
        # 移除多余的空白字符
        sql = ' '.join(sql.split())

        # 移除末尾的分号
        sql = sql.rstrip(';')

        # 移除行注释
        lines = sql.split('\n')
        cleaned_lines = []
        for line in lines:
            if '--' in line:
                line = line[:line.index('--')]
            line = line.strip()
            if line:
                cleaned_lines.append(line)

        return ' '.join(cleaned_lines).strip()
    
    def _classify_sql(self, sql: str) -> SQLType:
        """分类SQL语句"""
        sql_upper = sql.upper().strip()

        # 传统SQL分类
        if sql_upper.startswith(('CREATE', 'DROP', 'ALTER')):
            return SQLType.DDL
        elif sql_upper.startswith(('INSERT', 'UPDATE', 'DELETE')):
            return SQLType.DML
        elif sql_upper.startswith(('SELECT', 'SHOW', 'DESCRIBE', 'EXPLAIN')):
            return SQLType.DQL
        elif sql_upper.startswith(('GRANT', 'REVOKE')):
            return SQLType.DCL
        elif sql_upper.startswith(('COMMIT', 'ROLLBACK', 'START', 'BEGIN')):
            return SQLType.TCL
        # MongoDB命令分类
        elif sql_upper.startswith('DB.'):
            # 根据MongoDB方法名分类
            if any(method in sql_upper for method in ['INSERTONE', 'INSERTMANY', 'UPDATEONE', 'UPDATEMANY', 'DELETEONE', 'DELETEMANY', 'REPLACEONE']):
                return SQLType.DML
            elif any(method in sql_upper for method in ['FIND', 'FINDONE', 'COUNT', 'COUNTDOCUMENTS', 'DISTINCT', 'AGGREGATE']):
                return SQLType.DQL
            elif any(method in sql_upper for method in ['CREATEINDEX', 'DROPINDEX', 'DROP']):
                return SQLType.DDL
            else:
                return SQLType.DML  # 默认为DML
        else:
            return SQLType.UNKNOWN
    
    def _determine_phase(self, action: str, step_number: int) -> ExecutionPhase:
        """确定执行阶段"""
        action_lower = action.lower()

        if '清理' in action or ('删除' in action and 'drop' in action_lower):
            return ExecutionPhase.CLEANUP
        elif '验证' in action or '检查' in action or 'verify' in action_lower:
            return ExecutionPhase.VALIDATION
        elif '环境' in action or '准备' in action or step_number == 1:
            return ExecutionPhase.SETUP
        elif '创建' in action:
            # 特殊处理：创建物化视图、创建索引等涉及查询的操作应该归为执行阶段
            if any(keyword in action_lower for keyword in ['物化视图', '索引', '视图', '统计', '查询']):
                return ExecutionPhase.EXECUTION
            else:
                return ExecutionPhase.SETUP
        else:
            return ExecutionPhase.EXECUTION
    
    def _determine_capture_strategy(self, sql_types: List[SQLType], phase: ExecutionPhase) -> CaptureStrategy:
        """确定抓包策略 - 所有步骤都需要抓包"""
        # 修改为所有步骤都需要抓包，不再根据SQL类型和阶段进行判断
        return CaptureStrategy.CAPTURE_REQUIRED

class SQLClassifierTool(BaseTool):
    """SQL分类工具"""

    name: str = "sql_classifier"
    description: str = """
    对SQL语句进行分类，识别DDL/DML/DQL等类型，
    并确定是否需要进行数据包捕获。
    """
    args_schema: type[BaseModel] = SQLClassifyInput

    def _run(self, sql_statements: List[str]) -> str:
        """分类SQL语句"""
        try:
            results = []

            for sql in sql_statements:
                sql_type = self._classify_sql(sql)
                capture_needed = self._needs_capture(sql_type)

                results.append({
                    "sql": sql,
                    "type": sql_type.value,
                    "capture_needed": capture_needed,
                    "description": self._get_type_description(sql_type)
                })

            return json.dumps({
                "total_statements": len(sql_statements),
                "classifications": results,
                "capture_count": sum(1 for r in results if r["capture_needed"])
            }, ensure_ascii=False, indent=2)

        except Exception as e:
            logger.error(f"SQL分类失败: {str(e)}")
            return f"分类失败: {str(e)}"

    def _classify_sql(self, sql: str) -> SQLType:
        """分类单个SQL语句"""
        sql_upper = sql.upper().strip()

        if sql_upper.startswith(('CREATE', 'DROP', 'ALTER')):
            return SQLType.DDL
        elif sql_upper.startswith(('INSERT', 'UPDATE', 'DELETE')):
            return SQLType.DML
        elif sql_upper.startswith(('SELECT', 'SHOW', 'DESCRIBE', 'EXPLAIN')):
            return SQLType.DQL
        elif sql_upper.startswith(('GRANT', 'REVOKE')):
            return SQLType.DCL
        elif sql_upper.startswith(('COMMIT', 'ROLLBACK', 'START', 'BEGIN')):
            return SQLType.TCL
        else:
            return SQLType.UNKNOWN

    def _needs_capture(self, sql_type: SQLType) -> bool:
        """判断是否需要抓包 - 所有SQL类型都需要抓包"""
        # 修改为所有SQL类型都需要抓包
        return True

    def _get_type_description(self, sql_type: SQLType) -> str:
        """获取SQL类型描述"""
        descriptions = {
            SQLType.DDL: "数据定义语言 - 结构操作",
            SQLType.DML: "数据操作语言 - 数据修改",
            SQLType.DQL: "数据查询语言 - 数据查询",
            SQLType.DCL: "数据控制语言 - 权限控制",
            SQLType.TCL: "事务控制语言 - 事务管理",
            SQLType.UNKNOWN: "未知类型"
        }
        return descriptions.get(sql_type, "未知类型")


class ExecutionOrderTool(BaseTool):
    """执行顺序管理工具"""

    name: str = "execution_order_manager"
    description: str = """
    管理测试步骤的执行顺序，确保环境准备→查询执行→验证→清理的正确顺序。
    处理步骤间的依赖关系。
    """
    args_schema: type[BaseModel] = ExecutionPlanInput

    def _run(self, test_steps: List[Dict[str, Any]]) -> str:
        """生成执行计划"""
        try:
            # 按阶段分组步骤
            phases = {
                ExecutionPhase.SETUP: [],
                ExecutionPhase.EXECUTION: [],
                ExecutionPhase.VALIDATION: [],
                ExecutionPhase.CLEANUP: []
            }

            for step in test_steps:
                phase = ExecutionPhase(step.get('phase', 'execution'))
                phases[phase].append(step)

            # 生成执行计划
            execution_plan = []

            # 按阶段顺序执行
            for phase in [ExecutionPhase.SETUP, ExecutionPhase.EXECUTION,
                         ExecutionPhase.VALIDATION, ExecutionPhase.CLEANUP]:
                steps = phases[phase]
                if steps:
                    # 在每个阶段内按step_number排序
                    steps.sort(key=lambda x: x.get('step_number', 0))

                    for step in steps:
                        execution_plan.append({
                            "phase": phase.value,
                            "step_number": step.get('step_number'),
                            "action": step.get('action'),
                            "sql_statements": step.get('sql_statements', []),
                            "capture_strategy": step.get('capture_strategy', 'capture'),
                            "dependencies": self._analyze_dependencies(step, execution_plan)
                        })

            return json.dumps({
                "total_steps": len(execution_plan),
                "execution_order": execution_plan,
                "phases_summary": {
                    phase.value: len(phases[phase]) for phase in phases
                }
            }, ensure_ascii=False, indent=2)

        except Exception as e:
            logger.error(f"执行顺序管理失败: {str(e)}")
            return f"执行顺序管理失败: {str(e)}"

    def _analyze_dependencies(self, current_step: Dict[str, Any],
                            previous_steps: List[Dict[str, Any]]) -> List[str]:
        """分析步骤依赖关系"""
        dependencies = []

        # 如果当前步骤是查询，需要依赖表创建
        current_sqls = current_step.get('sql_statements', [])
        for sql in current_sqls:
            if sql.upper().startswith('SELECT'):
                # 查找依赖的表
                table_names = self._extract_table_names(sql)
                for table_name in table_names:
                    # 查找创建该表的步骤
                    for prev_step in previous_steps:
                        prev_sqls = prev_step.get('sql_statements', [])
                        for prev_sql in prev_sqls:
                            if (prev_sql.upper().startswith('CREATE TABLE') and
                                table_name.lower() in prev_sql.lower()):
                                dependencies.append(f"step_{prev_step.get('step_number')}")

        return list(set(dependencies))

    def _extract_table_names(self, sql: str) -> List[str]:
        """从SQL中提取表名"""
        # 简单的表名提取逻辑
        sql_upper = sql.upper()
        table_names = []

        # 提取FROM子句中的表名
        from_match = re.search(r'FROM\s+(\w+)', sql_upper)
        if from_match:
            table_names.append(from_match.group(1))

        # 提取JOIN子句中的表名
        join_matches = re.findall(r'JOIN\s+(\w+)', sql_upper)
        table_names.extend(join_matches)

        return table_names

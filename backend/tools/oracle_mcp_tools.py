import asyncio
import logging
from typing import Type, Dict, Any, Optional
from pydantic import BaseModel, Field
from langchain.tools import BaseTool
from services.mcp_service_manager import mcp_service_manager
from services.oracle_packet_capture_service import OraclePacketCaptureService
from services.oracle_service import OracleService
from services.database_config_service import database_config_service
from utils.config import Config

logger = logging.getLogger(__name__)

class OracleMCPQueryInput(BaseModel):
    """Oracle MCP查询输入模型"""
    sql_query: str = Field(description="要执行的Oracle SQL查询语句")
    capture_packets: bool = Field(default=False, description="是否同时进行数据包捕获")
    config_id: Optional[int] = Field(default=None, description="数据库配置ID")

class OracleMCPSchemaInput(BaseModel):
    """Oracle MCP架构输入模型"""
    config_id: Optional[int] = Field(default=None, description="数据库配置ID")
    table: Optional[str] = Field(default=None, description="表名称")
    owner: Optional[str] = Field(default=None, description="表所有者")

class OracleMCPQueryTool(BaseTool):
    """Oracle MCP查询执行工具"""

    name: str = "oracle_mcp_query"
    description: str = """
    使用MCP服务执行Oracle查询的工具。
    支持所有SQL操作：SELECT、INSERT、UPDATE、DELETE、CREATE、DROP等。
    可选择是否同时进行数据包捕获。
    返回查询结果和执行状态。
    """
    args_schema: Type[BaseModel] = OracleMCPQueryInput
    
    def __init__(self):
        super().__init__()
        self._packet_service = None
        self._oracle_service = None
        self._current_config_id = None

    def _get_packet_service(self):
        """获取数据包捕获服务实例"""
        if self._packet_service is None:
            self._packet_service = OraclePacketCaptureService()
        return self._packet_service
    
    def _get_oracle_service(self, config_id: int = None):
        """获取Oracle服务实例"""
        if self._oracle_service is None or config_id != self._current_config_id:
            # 如果配置ID变化，重新创建服务
            if config_id:
                self._current_config_id = config_id
                # 这里需要根据config_id获取配置，暂时使用默认配置
                # 实际使用时应该从database_config_service获取
                pass
            
            # 使用默认配置创建Oracle服务
            oracle_config = Config.get_oracle_config() if hasattr(Config, 'get_oracle_config') else {
                'host': '**************',
                'port': 1521,
                'user': 'system',
                'password': 'oracle',
                'service_name': 'ORCL'
            }
            
            self._oracle_service = OracleService(**oracle_config)
        return self._oracle_service
    
    def _run(self, sql_query: str, capture_packets: bool = False, config_id: int = None) -> str:
        """同步执行Oracle MCP查询"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                return loop.run_until_complete(self._arun(sql_query, capture_packets, config_id))
            finally:
                loop.close()
        except Exception as e:
            logger.error(f"Oracle MCP query tool error: {str(e)}")
            return f"查询执行失败: {str(e)}"
    
    async def _arun(self, sql_query: str, capture_packets: bool = False, config_id: int = None) -> str:
        """异步执行Oracle MCP查询"""
        try:
            # 修复Oracle SQL分号问题 - 移除末尾分号
            sql_query = sql_query.strip().rstrip(';')
            logger.info(f"Cleaned SQL query (removed semicolon): {sql_query}")
            
            packet_file = None
            
            # 如果需要抓包，先启动抓包
            if capture_packets:
                try:
                    packet_service = self._get_packet_service()
                    # 获取数据库配置
                    if config_id:
                        config = await database_config_service.get_config(config_id)
                        if config:
                            packet_file = await packet_service.start_capture(
                                config.host, config.port, step_identifier="mcp_query"
                            )
                        else:
                            logger.warning(f"Database config not found for ID: {config_id}")
                            packet_file = await packet_service.start_capture(
                                "**************", 1521, step_identifier="mcp_query"
                            )
                    else:
                        packet_file = await packet_service.start_capture(
                            "**************", 1521, step_identifier="mcp_query"
                        )
                    logger.info(f"Started Oracle packet capture: {packet_file}")
                except Exception as e:
                    logger.warning(f"Failed to start packet capture: {str(e)}")
            
            try:
                # 尝试使用MCP服务执行查询
                try:
                    result = await mcp_service_manager.call_tool(
                        "dbhub-oracle",  # 假设有Oracle MCP服务
                        "query",
                        {"sql": sql_query}
                    )
                    
                    # 等待一段时间确保数据包被捕获
                    if capture_packets:
                        await asyncio.sleep(2)
                    
                    # 格式化MCP结果
                    if result.get("isError", False):
                        response = f"查询执行失败: {result.get('content', [{}])[0].get('text', 'Unknown error')}"
                    else:
                        content = result.get("content", [])
                        if content and isinstance(content[0], dict):
                            response = f"查询执行成功: {content[0].get('text', str(result))}"
                        else:
                            response = f"查询执行成功: {str(result)}"
                
                except Exception as mcp_error:
                    logger.warning(f"MCP service failed, falling back to direct Oracle service: {str(mcp_error)}")
                    
                    # MCP服务失败，回退到直接Oracle服务
                    oracle_service = self._get_oracle_service(config_id)
                    
                    # 执行查询
                    result = await oracle_service.execute_query(sql_query)
                    
                    # 等待一段时间确保数据包被捕获
                    if capture_packets:
                        await asyncio.sleep(2)
                    
                    # 格式化Oracle服务结果
                    if result.get('success', False):
                        data = result.get('data', [])
                        row_count = result.get('row_count', 0)
                        response = f"查询执行成功: 返回 {row_count} 行数据"
                        if data:
                            response += f"\n数据预览: {str(data[:3])}"  # 只显示前3行
                    else:
                        error = result.get('error', 'Unknown error')
                        response = f"查询执行失败: {error}"
                
                # 添加抓包信息
                if packet_file:
                    response += f"\n数据包已捕获到: {packet_file}"
                
                return response
                
            finally:
                # 停止抓包
                if capture_packets and packet_file:
                    try:
                        packet_service = self._get_packet_service()
                        final_file = await packet_service.stop_capture()
                        if final_file:
                            logger.info(f"Oracle packet capture completed: {final_file}")
                    except Exception as e:
                        logger.warning(f"Failed to stop packet capture: {str(e)}")
            
        except Exception as e:
            logger.error(f"Oracle MCP query tool error: {str(e)}")
            return f"查询执行失败: {str(e)}"

class OracleMCPSchemaTool(BaseTool):
    """Oracle MCP架构信息工具"""

    name: str = "oracle_mcp_schema"
    description: str = """
    使用MCP服务获取Oracle数据库架构信息的工具。
    可以获取表列表、表结构、用户信息等。
    支持指定表名称和所有者进行精确查询。
    """
    args_schema: Type[BaseModel] = OracleMCPSchemaInput

    def __init__(self):
        super().__init__()

    def _run(self, config_id: Optional[int] = None, table: Optional[str] = None, owner: Optional[str] = None) -> str:
        """同步获取架构信息"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                return loop.run_until_complete(self._arun(config_id, table, owner))
            finally:
                loop.close()
        except Exception as e:
            logger.error(f"Oracle MCP schema tool error: {str(e)}")
            return f"获取架构信息失败: {str(e)}"
    
    async def _arun(self, config_id: Optional[int] = None, table: Optional[str] = None, owner: Optional[str] = None) -> str:
        """异步获取架构信息"""
        try:
            # 构建查询SQL
            if table and owner:
                # 获取特定表的结构
                sql = f"""
                SELECT column_name, data_type, data_length, nullable, column_id
                FROM all_tab_columns 
                WHERE table_name = '{table.upper()}' AND owner = '{owner.upper()}'
                ORDER BY column_id
                """
            elif table:
                # 获取当前用户中特定表的结构
                sql = f"""
                SELECT column_name, data_type, data_length, nullable, column_id
                FROM user_tab_columns 
                WHERE table_name = '{table.upper()}'
                ORDER BY column_id
                """
            elif owner:
                # 获取特定用户的所有表
                sql = f"""
                SELECT table_name, tablespace_name, num_rows, last_analyzed
                FROM all_tables 
                WHERE owner = '{owner.upper()}'
                ORDER BY table_name
                """
            else:
                # 获取当前用户的所有表
                sql = """
                SELECT table_name, tablespace_name, num_rows, last_analyzed
                FROM user_tables 
                ORDER BY table_name
                """
            
            # 尝试使用MCP服务执行查询
            try:
                result = await mcp_service_manager.call_tool(
                    "dbhub-oracle",
                    "query",
                    {"sql": sql}
                )
                
                # 格式化MCP结果
                if result.get("isError", False):
                    return f"获取架构信息失败: {result.get('content', [{}])[0].get('text', 'Unknown error')}"
                else:
                    content = result.get("content", [])
                    if content and isinstance(content[0], dict):
                        return f"架构信息: {content[0].get('text', str(result))}"
                    else:
                        return f"架构信息: {str(result)}"
            
            except Exception as mcp_error:
                logger.warning(f"MCP service failed, falling back to direct Oracle service: {str(mcp_error)}")
                
                # MCP服务失败，回退到直接Oracle服务
                if config_id:
                    config = await database_config_service.get_config(config_id)
                    if config:
                        oracle_service = OracleService(
                            host=config.host,
                            port=config.port,
                            user=config.user,
                            password=config.password,
                            service_name=config.database_name
                        )
                        
                        result = await oracle_service.execute_query(sql)
                        
                        if result.get('success', False):
                            data = result.get('data', [])
                            row_count = result.get('row_count', 0)
                            return f"架构信息: 返回 {row_count} 行数据\n数据: {str(data)}"
                        else:
                            return f"获取架构信息失败: {result.get('error', 'Unknown error')}"
                    else:
                        return f"数据库配置未找到: {config_id}"
                else:
                    return "需要提供config_id来获取架构信息"
                    
        except Exception as e:
            logger.error(f"Oracle MCP schema tool error: {str(e)}")
            return f"获取架构信息失败: {str(e)}"

class OracleMCPPacketCaptureTool(BaseTool):
    """Oracle MCP数据包捕获工具"""

    name: str = "oracle_mcp_packet_capture"
    description: str = """
    Oracle数据包捕获控制工具。
    支持的操作:
    - start: 启动数据包捕获
    - stop: 停止数据包捕获
    - status: 获取捕获状态
    """
    
    def __init__(self):
        super().__init__()
        self._packet_service = None

    def _get_packet_service(self):
        """获取数据包捕获服务实例"""
        if self._packet_service is None:
            self._packet_service = OraclePacketCaptureService()
        return self._packet_service
    
    def _run(self, action: str = "status", host: str = "**************", port: int = 1521) -> str:
        """同步执行数据包捕获操作"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                return loop.run_until_complete(self._arun(action, host, port))
            finally:
                loop.close()
        except Exception as e:
            logger.error(f"Oracle MCP packet capture tool error: {str(e)}")
            return f"数据包捕获操作失败: {str(e)}"
    
    async def _arun(self, action: str = "status", host: str = "**************", port: int = 1521) -> str:
        """异步执行数据包捕获操作"""
        try:
            packet_service = self._get_packet_service()
            if action == "start":
                file_path = await packet_service.start_capture(host, port, step_identifier="mcp_tool")
                return f"Oracle数据包捕获已启动，文件: {file_path}"
            elif action == "stop":
                file_path = await packet_service.stop_capture()
                return f"Oracle数据包捕获已停止，文件: {file_path}"
            elif action == "status":
                is_capturing = packet_service.is_capturing
                current_file = packet_service.current_file
                status = "正在捕获" if is_capturing else "未捕获"
                return f"捕获状态: {status}, 当前文件: {current_file or '无'}"
            else:
                return f"未知操作: {action}"
        except Exception as e:
            logger.error(f"Oracle MCP packet capture tool error: {str(e)}")
            return f"数据包捕获操作失败: {str(e)}"

# 创建全局实例
oracle_mcp_query_tool = OracleMCPQueryTool()
oracle_mcp_schema_tool = OracleMCPSchemaTool()
oracle_mcp_packet_capture_tool = OracleMCPPacketCaptureTool()

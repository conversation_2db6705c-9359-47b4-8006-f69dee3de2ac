import asyncio
import logging
from typing import Type, Dict, Any, Optional
from pydantic import BaseModel, Field
from langchain.tools import BaseTool
from services.mcp_service_manager import mcp_service_manager
from services.mysql_local_packet_capture_service import MySQLLocalPacketCaptureService
from services.mysql_service import MySQLService
from utils.config import Config

logger = logging.getLogger(__name__)

class MySQLMCPQueryInput(BaseModel):
    """MySQL MCP查询输入模型"""
    sql_query: str = Field(description="要执行的SQL查询语句")
    capture_packets: bool = Field(default=False, description="是否同时进行数据包捕获")

class MySQLMCPSchemaInput(BaseModel):
    """MySQL MCP架构输入模型"""
    database: Optional[str] = Field(default=None, description="数据库名称")
    table: Optional[str] = Field(default=None, description="表名称")

class MySQLMCPQueryTool(BaseTool):
    """MySQL MCP查询执行工具"""

    name: str = "mysql_mcp_query"
    description: str = """
    使用MCP服务执行MySQL查询的工具。
    支持所有SQL操作：SELECT、INSERT、UPDATE、DELETE、CREATE、DROP等。
    可选择是否同时进行数据包捕获。
    返回查询结果和执行状态。
    """
    args_schema: Type[BaseModel] = MySQLMCPQueryInput
    
    def __init__(self):
        super().__init__()
        self._packet_service = None
        self._mysql_service = None

    def _get_packet_service(self):
        """获取数据包捕获服务实例"""
        if self._packet_service is None:
            self._packet_service = MySQLLocalPacketCaptureService()
        return self._packet_service
    
    def _get_mysql_service(self):
        """获取MySQL服务实例"""
        if self._mysql_service is None:
            mysql_config = Config.get_mysql_config()
            self._mysql_service = MySQLService(**mysql_config)
        return self._mysql_service
    
    def _run(self, sql_query: str, capture_packets: bool = False) -> str:
        """同步执行MySQL MCP查询"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                return loop.run_until_complete(self._arun(sql_query, capture_packets))
            finally:
                loop.close()
        except Exception as e:
            logger.error(f"MySQL MCP query tool error: {str(e)}")
            return f"查询执行失败: {str(e)}"
    
    async def _arun(self, sql_query: str, capture_packets: bool = False) -> str:
        """异步执行MySQL MCP查询"""
        try:
            packet_file = None
            
            # 如果需要抓包，先启动抓包
            if capture_packets:
                try:
                    packet_service = self._get_packet_service()
                    packet_file = await packet_service.start_capture(
                        Config.MYSQL_HOST, Config.MYSQL_PORT
                    )
                    logger.info(f"Started packet capture: {packet_file}")
                except Exception as e:
                    logger.warning(f"Failed to start packet capture: {str(e)}")
            
            try:
                # 使用MCP服务执行查询
                result = await mcp_service_manager.call_tool(
                    "dbhub-mysql",
                    "query",
                    {"sql": sql_query}
                )
                
                # 等待一段时间确保数据包被捕获
                if capture_packets:
                    await asyncio.sleep(2)
                
                # 格式化结果
                if result.get("isError", False):
                    response = f"查询执行失败: {result.get('content', [{}])[0].get('text', 'Unknown error')}"
                else:
                    content = result.get("content", [])
                    if content and isinstance(content[0], dict):
                        response = f"查询执行成功: {content[0].get('text', str(result))}"
                    else:
                        response = f"查询执行成功: {str(result)}"
                
                # 添加抓包信息
                if packet_file:
                    response += f"\n数据包已捕获到: {packet_file}"
                
                return response
                
            finally:
                # 停止抓包
                if capture_packets and packet_file:
                    try:
                        packet_service = self._get_packet_service()
                        final_file = await packet_service.stop_capture()
                        if final_file:
                            logger.info(f"Packet capture completed: {final_file}")
                    except Exception as e:
                        logger.warning(f"Failed to stop packet capture: {str(e)}")
            
        except Exception as e:
            logger.error(f"MySQL MCP query tool error: {str(e)}")
            return f"查询执行失败: {str(e)}"

class MySQLMCPSchemaTool(BaseTool):
    """MySQL MCP架构信息工具"""

    name: str = "mysql_mcp_schema"
    description: str = """
    使用MCP服务获取MySQL数据库架构信息的工具。
    可以获取数据库列表、表列表、表结构等信息。
    支持指定数据库名称和表名称进行精确查询。
    """
    args_schema: Type[BaseModel] = MySQLMCPSchemaInput

    def __init__(self):
        super().__init__()

    def _run(self, database: Optional[str] = None, table: Optional[str] = None) -> str:
        """同步获取架构信息"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                return loop.run_until_complete(self._arun(database, table))
            finally:
                loop.close()
        except Exception as e:
            logger.error(f"MySQL MCP schema tool error: {str(e)}")
            return f"获取架构信息失败: {str(e)}"
    
    async def _arun(self, database: Optional[str] = None, table: Optional[str] = None) -> str:
        """异步获取架构信息"""
        try:
            if table and database:
                # 获取特定表的结构
                sql = f"DESCRIBE {database}.{table}"
            elif table:
                # 获取当前数据库中特定表的结构
                sql = f"DESCRIBE {table}"
            elif database:
                # 获取特定数据库的所有表
                sql = f"SHOW TABLES FROM {database}"
            else:
                # 获取所有数据库
                sql = "SHOW DATABASES"
            
            # 使用MCP服务执行查询
            result = await mcp_service_manager.call_tool(
                "dbhub-mysql",
                "query",
                {"sql": sql}
            )
            
            # 格式化结果
            if result.get("isError", False):
                return f"获取架构信息失败: {result.get('content', [{}])[0].get('text', 'Unknown error')}"
            else:
                content = result.get("content", [])
                if content and isinstance(content[0], dict):
                    return f"架构信息: {content[0].get('text', str(result))}"
                else:
                    return f"架构信息: {str(result)}"
                    
        except Exception as e:
            logger.error(f"MySQL MCP schema tool error: {str(e)}")
            return f"获取架构信息失败: {str(e)}"

class MySQLMCPPacketCaptureTool(BaseTool):
    """MySQL MCP数据包捕获工具"""

    name: str = "mysql_mcp_packet_capture"
    description: str = """
    MySQL数据包捕获控制工具。
    支持的操作:
    - start: 启动数据包捕获
    - stop: 停止数据包捕获
    - status: 获取捕获状态
    """
    
    def __init__(self):
        super().__init__()
        self._packet_service = None

    def _get_packet_service(self):
        """获取数据包捕获服务实例"""
        if self._packet_service is None:
            self._packet_service = MySQLLocalPacketCaptureService()
        return self._packet_service
    
    def _run(self, action: str = "status") -> str:
        """同步执行数据包捕获操作"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                return loop.run_until_complete(self._arun(action))
            finally:
                loop.close()
        except Exception as e:
            logger.error(f"MySQL MCP packet capture tool error: {str(e)}")
            return f"数据包捕获操作失败: {str(e)}"
    
    async def _arun(self, action: str = "status") -> str:
        """异步执行数据包捕获操作"""
        try:
            packet_service = self._get_packet_service()
            if action == "start":
                file_path = await packet_service.start_capture(
                    Config.MYSQL_HOST, Config.MYSQL_PORT
                )
                return f"MySQL数据包捕获已启动，文件: {file_path}"
            elif action == "stop":
                file_path = await packet_service.stop_capture()
                return f"MySQL数据包捕获已停止，文件: {file_path}"
            elif action == "status":
                is_capturing = packet_service.is_capturing
                current_file = packet_service.current_file
                status = "正在捕获" if is_capturing else "未捕获"
                return f"捕获状态: {status}, 当前文件: {current_file or '无'}"
            else:
                return f"未知操作: {action}"
        except Exception as e:
            logger.error(f"MySQL MCP packet capture tool error: {str(e)}")
            return f"数据包捕获操作失败: {str(e)}"

import asyncio
import logging
from typing import Type, Optional
from pydantic import BaseModel, Field
from langchain.tools import BaseTool
from services.postgres_local_packet_capture_service import PostgresLocalPacketCaptureService
# 延迟导入，避免不必要的模块加载

logger = logging.getLogger(__name__)

class PostgresMCPQueryInput(BaseModel):
    """PostgreSQL MCP查询输入模型"""
    sql_query: str = Field(description="要执行的PostgreSQL SQL查询语句")
    capture_packets: bool = Field(default=False, description="是否同时进行数据包捕获")

class PostgresMCPSchemaInput(BaseModel):
    """PostgreSQL MCP架构输入模型"""
    database: Optional[str] = Field(default=None, description="数据库名称")
    schema_name: Optional[str] = Field(default="public", description="模式名称", alias="schema")
    table: Optional[str] = Field(default=None, description="表名称")

class PostgresMCPPacketCaptureInput(BaseModel):
    """PostgreSQL MCP数据包捕获输入模型"""
    action: str = Field(description="抓包操作: start, stop, status")
    duration: Optional[int] = Field(default=30, description="抓包持续时间（秒）")

class PostgresMCPQueryTool(BaseTool):
    """PostgreSQL MCP查询执行工具"""

    name: str = "postgres_mcp_query"
    description: str = """
    执行PostgreSQL查询的工具。
    支持所有SQL操作：SELECT、INSERT、UPDATE、DELETE、CREATE、DROP等。
    可选择是否同时进行数据包捕获。
    返回查询结果和执行状态。
    """
    args_schema: Type[BaseModel] = PostgresMCPQueryInput

    def __init__(self, postgres_service=None):
        super().__init__()
        self._packet_service = None
        self._postgres_service = postgres_service

    def _get_packet_service(self):
        """获取数据包捕获服务实例"""
        if self._packet_service is None:
            self._packet_service = PostgresLocalPacketCaptureService()
        return self._packet_service
    
    def _run(self, sql_query: str, capture_packets: bool = False) -> str:
        """同步执行PostgreSQL查询"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                return loop.run_until_complete(self._arun(sql_query, capture_packets))
            finally:
                loop.close()
        except Exception as e:
            logger.error(f"PostgreSQL MCP query tool error: {str(e)}")
            return f"查询执行失败: {str(e)}"
    
    async def _arun(self, sql_query: str, capture_packets: bool = False) -> str:
        """异步执行PostgreSQL查询"""
        try:
            packet_file = None
            
            # 如果需要抓包，先启动抓包
            if capture_packets:
                try:
                    packet_service = self._get_packet_service()
                    packet_file = await packet_service.start_capture()
                    logger.info(f"Started PostgreSQL packet capture: {packet_file}")
                except Exception as e:
                    logger.warning(f"Failed to start PostgreSQL packet capture: {str(e)}")
            
            try:
                # 执行PostgreSQL查询
                if not self._postgres_service:
                    raise Exception("PostgreSQL service not configured")

                result = await self._postgres_service.execute_query(sql_query)
                
                # 等待一段时间确保数据包被捕获
                if capture_packets:
                    await asyncio.sleep(2)
                
                # 格式化结果
                if result.get("success", False):
                    data = result.get("data", [])
                    if data:
                        response = f"查询执行成功，返回 {len(data)} 行数据: {str(data)[:200]}..."
                    else:
                        response = f"查询执行成功: {result.get('message', '操作完成')}"
                else:
                    response = f"查询执行失败: {result.get('error', 'Unknown error')}"
                
            except Exception as e:
                logger.error(f"PostgreSQL query execution failed: {str(e)}")
                response = f"查询执行失败: {str(e)}"
            
            finally:
                # 停止抓包
                if capture_packets and packet_file:
                    try:
                        final_packet_file = await self._get_packet_service().stop_capture()
                        response += f"\n数据包捕获完成: {final_packet_file}"
                    except Exception as e:
                        logger.warning(f"Failed to stop PostgreSQL packet capture: {str(e)}")
            
            return response
            
        except Exception as e:
            logger.error(f"PostgreSQL MCP tool execution failed: {str(e)}")
            return f"工具执行失败: {str(e)}"

class PostgresMCPSchemaTool(BaseTool):
    """PostgreSQL MCP架构信息工具"""

    name: str = "postgres_mcp_schema"
    description: str = """
    获取PostgreSQL数据库架构信息的工具。
    可以获取数据库列表、表列表、表结构等信息。
    """
    args_schema: Type[BaseModel] = PostgresMCPSchemaInput

    def __init__(self, postgres_service=None):
        super().__init__()
        self._postgres_service = postgres_service
    
    def _run(self, database: Optional[str] = None, schema_name: str = "public", table: Optional[str] = None) -> str:
        """同步获取PostgreSQL架构信息"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                return loop.run_until_complete(self._arun(database, schema_name, table))
            finally:
                loop.close()
        except Exception as e:
            logger.error(f"PostgreSQL MCP schema tool error: {str(e)}")
            return f"获取架构信息失败: {str(e)}"
    
    async def _arun(self, database: Optional[str] = None, schema_name: str = "public", table: Optional[str] = None) -> str:
        """异步获取PostgreSQL架构信息"""
        try:
            if not self._postgres_service:
                raise Exception("PostgreSQL service not configured")

            if table:
                # 获取特定表的结构
                result = await self._postgres_service.get_table_structure(table, schema_name)
                return f"表 {schema_name}.{table} 的结构: {str(result)}"
            elif database:
                # 获取特定数据库的表列表
                result = await self._postgres_service.get_tables(schema_name)
                return f"数据库 {database} 模式 {schema_name} 的表列表: {str(result)}"
            else:
                # 获取数据库列表
                result = await self._postgres_service.get_databases()
                return f"PostgreSQL数据库列表: {str(result)}"
                
        except Exception as e:
            logger.error(f"PostgreSQL schema query failed: {str(e)}")
            return f"获取架构信息失败: {str(e)}"

class PostgresMCPPacketCaptureTool(BaseTool):
    """PostgreSQL MCP数据包捕获控制工具"""

    name: str = "postgres_mcp_packet_capture"
    description: str = """
    控制PostgreSQL数据包捕获的工具。
    支持启动、停止抓包，以及查看抓包状态。
    """
    args_schema: Type[BaseModel] = PostgresMCPPacketCaptureInput
    
    def __init__(self):
        super().__init__()
        self._packet_service = None

    def _get_packet_service(self):
        """获取数据包捕获服务实例"""
        if self._packet_service is None:
            self._packet_service = PostgresLocalPacketCaptureService()
        return self._packet_service
    
    def _run(self, action: str, duration: int = 30) -> str:
        """同步控制PostgreSQL数据包捕获"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                return loop.run_until_complete(self._arun(action, duration))
            finally:
                loop.close()
        except Exception as e:
            logger.error(f"PostgreSQL MCP packet capture tool error: {str(e)}")
            return f"数据包捕获控制失败: {str(e)}"
    
    async def _arun(self, action: str, duration: int = 30) -> str:
        """异步控制PostgreSQL数据包捕获"""
        try:
            packet_service = self._get_packet_service()
            
            if action.lower() == "start":
                capture_file = await packet_service.start_capture()
                return f"PostgreSQL数据包捕获已启动: {capture_file}"
            elif action.lower() == "stop":
                capture_file = await packet_service.stop_capture()
                return f"PostgreSQL数据包捕获已停止: {capture_file}"
            elif action.lower() == "status":
                status = packet_service.get_capture_status()
                return f"PostgreSQL数据包捕获状态: {str(status)}"
            else:
                return f"不支持的操作: {action}。支持的操作: start, stop, status"
                
        except Exception as e:
            logger.error(f"PostgreSQL packet capture control failed: {str(e)}")
            return f"数据包捕获控制失败: {str(e)}"

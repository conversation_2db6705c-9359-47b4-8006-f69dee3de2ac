"""
异步任务日志集成示例
展示如何在现有的异步任务中集成详细的执行日志记录
"""

import asyncio
import logging
import uuid
from typing import Dict, Any, Optional

from utils.task_execution_logger import (
    create_task_logger, create_batch_task_logger, ExecutorSelector,
    TaskExecutionLogger, ExecutorType
)
from services.mysql_service import MySQLService
from utils.config import Config

logger = logging.getLogger(__name__)


async def enhanced_mysql_capture_task_example(
    config_id: int,
    sql_query: str,
    capture_duration: int = 30,
    prefer_c_executor: bool = False
) -> Dict[str, Any]:
    """增强的MySQL抓包任务示例 - 集成详细日志记录"""
    
    # 生成任务ID
    task_id = str(uuid.uuid4())
    
    # 创建任务日志记录器
    task_logger = create_task_logger(
        task_id=task_id,
        task_type="mysql_capture",
        task_name=f"MySQL抓包任务-配置{config_id}"
    )
    
    try:
        # 1. 初始化任务记录
        await task_logger.initialize(
            task_description=f"对配置ID {config_id} 执行SQL查询的抓包任务",
            database_config_id=config_id,
            input_sql_query=sql_query,
            capture_enabled=True,
            capture_duration=capture_duration,
            max_retries=3
        )
        
        # 2. 选择执行器
        executor_type, selection_reason = ExecutorSelector.select_executor(
            "mysql", prefer_c_executor
        )
        
        # 3. 开始执行
        await task_logger.start_execution(
            executor_type=executor_type,
            executor_selection_reason=selection_reason
        )
        
        # 4. 执行具体任务逻辑
        result = await _execute_mysql_capture_with_logging(
            task_logger, config_id, sql_query, capture_duration, executor_type
        )
        
        # 5. 记录成功完成
        await task_logger.complete_execution(
            execution_result=result,
            output_files=result.get('output_files', []),
            capture_files=result.get('capture_files', [])
        )
        
        logger.info(f"MySQL抓包任务完成: {task_id}")
        return result
        
    except Exception as e:
        # 记录失败
        await task_logger.fail_execution(
            error_code="MYSQL_CAPTURE_ERROR",
            error_message=str(e),
            error_details={
                "config_id": config_id,
                "sql_query": sql_query,
                "capture_duration": capture_duration
            }
        )
        
        logger.error(f"MySQL抓包任务失败: {task_id}, 错误: {e}")
        raise


async def _execute_mysql_capture_with_logging(
    task_logger: TaskExecutionLogger,
    config_id: int,
    sql_query: str,
    capture_duration: int,
    executor_type: ExecutorType
) -> Dict[str, Any]:
    """执行MySQL抓包的具体逻辑（带日志记录）"""
    
    try:
        # 更新进度：初始化数据库连接
        await task_logger.update_progress(10, "初始化数据库连接")
        
        # 模拟数据库连接初始化
        mysql_service = MySQLService(**Config.get_mysql_config())
        connection_test = await mysql_service.check_connection()
        
        if not connection_test:
            raise Exception("数据库连接失败")
        
        # 更新进度：开始抓包
        await task_logger.update_progress(30, "开始网络抓包")
        
        # 模拟抓包逻辑
        if executor_type == ExecutorType.C:
            # 尝试使用C执行器
            try:
                result = await _execute_with_c_executor(task_logger, sql_query)
            except Exception as c_error:
                # C执行器失败，回退到Python
                await task_logger.record_executor_fallback(
                    ExecutorType.C, ExecutorType.PYTHON, 
                    f"C执行器失败: {str(c_error)}"
                )
                result = await _execute_with_python_executor(task_logger, sql_query)
        else:
            # 使用Python执行器
            result = await _execute_with_python_executor(task_logger, sql_query)
        
        # 更新进度：执行SQL查询
        await task_logger.update_progress(60, "执行SQL查询")
        
        # 模拟SQL执行
        sql_result = await mysql_service.execute_query(sql_query)
        
        # 更新进度：停止抓包
        await task_logger.update_progress(80, "停止抓包并保存文件")
        
        # 模拟抓包文件保存
        capture_files = [f"/tmp/mysql_capture_{task_logger.get_task_id()}.pcap"]
        
        # 更新进度：完成
        await task_logger.update_progress(100, "任务完成")
        
        return {
            "success": True,
            "sql_result": sql_result,
            "capture_files": capture_files,
            "output_files": [f"/tmp/analysis_{task_logger.get_task_id()}.json"],
            "executor_used": executor_type.value,
            "packet_count": 156,  # 模拟数据
            "packet_size_bytes": 8192  # 模拟数据
        }
        
    except Exception as e:
        logger.error(f"执行MySQL抓包逻辑失败: {e}")
        raise


async def _execute_with_c_executor(task_logger: TaskExecutionLogger, sql_query: str) -> Dict[str, Any]:
    """使用C执行器执行（模拟）"""
    await task_logger.update_progress(45, "使用C执行器执行查询")
    
    # 模拟C执行器可能的失败
    import random
    if random.random() < 0.3:  # 30%概率失败
        raise Exception("C执行器连接超时")
    
    return {"executor": "C", "performance": "high"}


async def _execute_with_python_executor(task_logger: TaskExecutionLogger, sql_query: str) -> Dict[str, Any]:
    """使用Python执行器执行（模拟）"""
    await task_logger.update_progress(45, "使用Python执行器执行查询")
    
    # Python执行器通常更稳定
    return {"executor": "Python", "performance": "standard"}


async def batch_test_case_execution_example(
    test_case_ids: list,
    config_id: int,
    batch_name: str = "批量测试执行"
) -> Dict[str, Any]:
    """批量测试用例执行示例"""
    
    # 生成批量ID
    batch_id = str(uuid.uuid4())
    
    # 创建批量任务日志记录器
    batch_logger = create_batch_task_logger(
        batch_id=batch_id,
        batch_name=batch_name,
        total_count=len(test_case_ids)
    )
    
    results = []
    
    try:
        for i, test_case_id in enumerate(test_case_ids):
            # 为每个测试用例创建单独的任务日志记录器
            task_logger = await batch_logger.create_task_logger(
                task_id=str(uuid.uuid4()),
                task_type="single_test_case_execution",
                task_name=f"测试用例执行-{test_case_id}",
                test_case_id=test_case_id,
                database_config_id=config_id
            )
            
            try:
                # 选择执行器
                executor_type, selection_reason = ExecutorSelector.select_executor("mysql")
                
                # 开始执行
                await task_logger.start_execution(
                    executor_type=executor_type,
                    executor_selection_reason=selection_reason
                )
                
                # 模拟测试用例执行
                await task_logger.update_progress(50, f"执行测试用例 {i+1}/{len(test_case_ids)}")
                
                # 模拟执行结果
                test_result = {
                    "test_case_id": test_case_id,
                    "status": "PASSED",
                    "execution_time": 2.5,
                    "assertions_passed": 5,
                    "assertions_failed": 0
                }
                
                # 记录完成
                await task_logger.complete_execution(
                    execution_result=test_result,
                    output_files=[f"/tmp/test_result_{test_case_id}.json"]
                )
                
                results.append(test_result)
                
            except Exception as e:
                # 记录单个测试用例失败
                await task_logger.fail_execution(
                    error_code="TEST_CASE_EXECUTION_ERROR",
                    error_message=str(e),
                    error_details={"test_case_id": test_case_id}
                )
                
                results.append({
                    "test_case_id": test_case_id,
                    "status": "FAILED",
                    "error": str(e)
                })
        
        # 获取批量执行统计
        batch_stats = await batch_logger.get_batch_statistics()
        
        return {
            "batch_id": batch_id,
            "batch_name": batch_name,
            "total_count": len(test_case_ids),
            "results": results,
            "statistics": batch_stats
        }
        
    except Exception as e:
        logger.error(f"批量测试执行失败: {e}")
        raise


async def main():
    """主函数 - 演示日志系统使用"""
    try:
        # 示例1: 单个MySQL抓包任务
        print("=== 示例1: MySQL抓包任务 ===")
        result1 = await enhanced_mysql_capture_task_example(
            config_id=1,
            sql_query="SELECT * FROM users LIMIT 10",
            capture_duration=30,
            prefer_c_executor=True
        )
        print(f"MySQL抓包任务结果: {result1}")
        
        # 示例2: 批量测试用例执行
        print("\n=== 示例2: 批量测试用例执行 ===")
        test_case_ids = ["test_001", "test_002", "test_003", "test_004", "test_005"]
        result2 = await batch_test_case_execution_example(
            test_case_ids=test_case_ids,
            config_id=1,
            batch_name="用户模块测试批次"
        )
        print(f"批量测试执行结果: {result2}")
        
    except Exception as e:
        logger.error(f"示例执行失败: {e}")


if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 运行示例
    asyncio.run(main())

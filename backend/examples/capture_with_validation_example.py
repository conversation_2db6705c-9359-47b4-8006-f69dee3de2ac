#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
抓包与验证集成示例 - 展示如何在抓包流程中集成自检功能
"""

import os
import sys
import time
import asyncio
import logging
from typing import List, Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.capture_validation_manager import CaptureValidationManager
from services.mysql_local_packet_capture_service import MySQLLocalPacketCaptureService
from services.postgres_local_packet_capture_service import PostgreSQLLocalPacketCaptureService
from services.mongodb_local_packet_capture_service import MongoDBLocalPacketCaptureService

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class CaptureWithValidationExample:
    """抓包与验证集成示例"""
    
    def __init__(self):
        self.validation_manager = CaptureValidationManager()
        
        # 初始化各种数据库的抓包服务
        self.mysql_capture = MySQLLocalPacketCaptureService()
        self.postgres_capture = PostgreSQLLocalPacketCaptureService()
        self.mongodb_capture = MongoDBLocalPacketCaptureService()
    
    async def mysql_capture_with_validation_example(self):
        """MySQL抓包与验证示例"""
        print("="*60)
        print("MySQL抓包与验证示例")
        print("="*60)
        
        # 1. 开始验证任务
        task_id = f"mysql_test_{int(time.time())}"
        expected_operations = ["SELECT", "INSERT", "UPDATE"]
        
        task = self.validation_manager.start_capture_task(
            task_id=task_id,
            database_type="mysql",
            expected_operations=expected_operations
        )
        
        print(f"开始MySQL抓包任务: {task_id}")
        print(f"预期操作: {expected_operations}")
        
        try:
            # 2. 启动抓包
            print("启动MySQL抓包...")
            pcap_file = await self.mysql_capture.start_capture(
                target_host="**************",
                target_port=3306
            )
            
            if pcap_file:
                print(f"抓包已启动，文件: {pcap_file}")
                
                # 3. 模拟执行SQL操作
                print("模拟执行SQL操作...")
                await self._simulate_mysql_operations()
                
                # 4. 停止抓包
                print("停止抓包...")
                final_pcap_file = await self.mysql_capture.stop_capture()
                
                if final_pcap_file:
                    # 5. 添加PCAP文件到验证任务
                    self.validation_manager.add_pcap_file_to_task(task_id, final_pcap_file)
                    
                    # 6. 完成任务并执行验证
                    print("执行抓包验证...")
                    validation_result = self.validation_manager.complete_capture_task(task_id)
                    
                    # 7. 打印验证结果
                    self._print_validation_result(validation_result)
                    
                else:
                    print("抓包失败，无法获取PCAP文件")
            else:
                print("启动抓包失败")
                
        except Exception as e:
            logger.error(f"MySQL抓包示例失败: {str(e)}")
            # 确保清理任务
            self.validation_manager.complete_capture_task(task_id, auto_validate=False)
    
    async def postgresql_capture_with_validation_example(self):
        """PostgreSQL抓包与验证示例"""
        print("\n" + "="*60)
        print("PostgreSQL抓包与验证示例")
        print("="*60)
        
        # 1. 开始验证任务
        task_id = f"postgres_test_{int(time.time())}"
        expected_operations = ["SELECT", "INSERT", "UPDATE", "DELETE"]
        
        task = self.validation_manager.start_capture_task(
            task_id=task_id,
            database_type="postgresql",
            expected_operations=expected_operations
        )
        
        print(f"开始PostgreSQL抓包任务: {task_id}")
        print(f"预期操作: {expected_operations}")
        
        try:
            # 2. 启动抓包
            print("启动PostgreSQL抓包...")
            pcap_file = await self.postgres_capture.start_capture(
                target_host="**************",
                target_port=5432
            )
            
            if pcap_file:
                print(f"抓包已启动，文件: {pcap_file}")
                
                # 3. 模拟执行SQL操作
                print("模拟执行SQL操作...")
                await self._simulate_postgresql_operations()
                
                # 4. 停止抓包
                print("停止抓包...")
                final_pcap_file = await self.postgres_capture.stop_capture()
                
                if final_pcap_file:
                    # 5. 添加PCAP文件到验证任务
                    self.validation_manager.add_pcap_file_to_task(task_id, final_pcap_file)
                    
                    # 6. 完成任务并执行验证
                    print("执行抓包验证...")
                    validation_result = self.validation_manager.complete_capture_task(task_id)
                    
                    # 7. 打印验证结果
                    self._print_validation_result(validation_result)
                    
                else:
                    print("抓包失败，无法获取PCAP文件")
            else:
                print("启动抓包失败")
                
        except Exception as e:
            logger.error(f"PostgreSQL抓包示例失败: {str(e)}")
            # 确保清理任务
            self.validation_manager.complete_capture_task(task_id, auto_validate=False)
    
    async def mongodb_capture_with_validation_example(self):
        """MongoDB抓包与验证示例"""
        print("\n" + "="*60)
        print("MongoDB抓包与验证示例")
        print("="*60)
        
        # 1. 开始验证任务
        task_id = f"mongodb_test_{int(time.time())}"
        expected_operations = ["SELECT", "INSERT", "UPDATE", "DELETE"]  # 会自动转换为MongoDB操作
        
        task = self.validation_manager.start_capture_task(
            task_id=task_id,
            database_type="mongodb",
            expected_operations=expected_operations
        )
        
        print(f"开始MongoDB抓包任务: {task_id}")
        print(f"预期操作: {expected_operations}")
        
        try:
            # 2. 启动抓包
            print("启动MongoDB抓包...")
            pcap_file = await self.mongodb_capture.start_capture(
                target_host="127.0.0.1",
                target_port=27017
            )
            
            if pcap_file:
                print(f"抓包已启动，文件: {pcap_file}")
                
                # 3. 模拟执行MongoDB操作
                print("模拟执行MongoDB操作...")
                await self._simulate_mongodb_operations()
                
                # 4. 停止抓包
                print("停止抓包...")
                final_pcap_file = await self.mongodb_capture.stop_capture()
                
                if final_pcap_file:
                    # 5. 添加PCAP文件到验证任务
                    self.validation_manager.add_pcap_file_to_task(task_id, final_pcap_file)
                    
                    # 6. 完成任务并执行验证
                    print("执行抓包验证...")
                    validation_result = self.validation_manager.complete_capture_task(task_id)
                    
                    # 7. 打印验证结果
                    self._print_validation_result(validation_result)
                    
                else:
                    print("抓包失败，无法获取PCAP文件")
            else:
                print("启动抓包失败")
                
        except Exception as e:
            logger.error(f"MongoDB抓包示例失败: {str(e)}")
            # 确保清理任务
            self.validation_manager.complete_capture_task(task_id, auto_validate=False)
    
    async def batch_validation_example(self):
        """批量验证示例"""
        print("\n" + "="*60)
        print("批量验证现有PCAP文件示例")
        print("="*60)
        
        # 查找captures目录中的PCAP文件
        captures_dir = "backend/captures"
        if not os.path.exists(captures_dir):
            print(f"captures目录不存在: {captures_dir}")
            return
        
        # 获取最近的一些PCAP文件
        import glob
        pcap_files = glob.glob(os.path.join(captures_dir, "*.pcap"))
        
        if not pcap_files:
            print("未找到PCAP文件")
            return
        
        # 限制文件数量以避免处理时间过长
        pcap_files = pcap_files[:5]
        
        print(f"找到 {len(pcap_files)} 个PCAP文件进行验证")
        
        # 创建批量验证任务
        task_id = f"batch_validation_{int(time.time())}"
        task = self.validation_manager.start_capture_task(
            task_id=task_id,
            database_type="mysql",  # 默认使用MySQL规则
            expected_operations=["SELECT", "INSERT", "UPDATE", "DELETE"]
        )
        
        # 添加所有文件到任务
        for pcap_file in pcap_files:
            self.validation_manager.add_pcap_file_to_task(task_id, pcap_file)
        
        # 执行验证
        print("执行批量验证...")
        validation_result = self.validation_manager.complete_capture_task(task_id)
        
        # 打印结果
        self._print_validation_result(validation_result)
    
    async def _simulate_mysql_operations(self):
        """模拟MySQL操作"""
        try:
            from services.mysql_service import MySQLService
            
            mysql_service = MySQLService(
                host="**************",
                port=3306,
                user="root",
                password="123456",
                database="test"
            )
            
            # 执行一些SQL操作
            await mysql_service.execute_query("SELECT 1")
            await mysql_service.execute_query("SELECT NOW()")
            await mysql_service.execute_query("SHOW TABLES")
            
            await mysql_service.close()
            
        except Exception as e:
            logger.warning(f"模拟MySQL操作失败: {str(e)}")
        
        # 等待一段时间确保数据包被捕获
        await asyncio.sleep(2)
    
    async def _simulate_postgresql_operations(self):
        """模拟PostgreSQL操作"""
        try:
            from services.postgres_service import PostgreSQLService
            
            postgres_service = PostgreSQLService(
                host="**************",
                port=5432,
                user="postgres",
                password="postgres",
                database="postgres"
            )
            
            # 执行一些SQL操作
            await postgres_service.execute_sql_query("SELECT 1")
            await postgres_service.execute_sql_query("SELECT NOW()")
            await postgres_service.execute_sql_query("SELECT version()")
            
            await postgres_service.close()
            
        except Exception as e:
            logger.warning(f"模拟PostgreSQL操作失败: {str(e)}")
        
        # 等待一段时间确保数据包被捕获
        await asyncio.sleep(2)
    
    async def _simulate_mongodb_operations(self):
        """模拟MongoDB操作"""
        try:
            from services.mongodb_service import MongoDBService
            
            mongodb_service = MongoDBService(
                host="127.0.0.1",
                port=27017,
                database="test"
            )
            
            # 执行一些MongoDB操作
            await mongodb_service.execute_query("db.test.find()")
            await mongodb_service.execute_query("db.test.count()")
            
            await mongodb_service.close()
            
        except Exception as e:
            logger.warning(f"模拟MongoDB操作失败: {str(e)}")
        
        # 等待一段时间确保数据包被捕获
        await asyncio.sleep(2)
    
    def _print_validation_result(self, validation_result: Dict[str, Any]):
        """打印验证结果"""
        if not validation_result:
            print("验证结果为空")
            return
        
        print("\n" + "-"*40)
        print("验证结果")
        print("-"*40)
        
        summary = validation_result.get('summary', {})
        print(f"任务ID: {validation_result.get('task_id', 'N/A')}")
        print(f"数据库类型: {validation_result.get('database_type', 'N/A')}")
        print(f"预期操作: {', '.join(validation_result.get('expected_operations', []))}")
        print(f"任务持续时间: {validation_result.get('task_duration', 0):.2f}秒")
        
        print(f"\n文件统计:")
        print(f"  总文件数: {summary.get('total_files', 0)}")
        print(f"  验证成功: {summary.get('successful_files', 0)}")
        print(f"  验证失败: {summary.get('failed_files', 0)}")
        print(f"  成功率: {summary.get('success_rate', 0):.1%}")
        print(f"  总SQL语句数: {summary.get('total_sql_found', 0)}")
        print(f"  包含SQL的文件: {summary.get('files_with_sql', 0)}")
        
        # 操作匹配情况
        operation_matches = validation_result.get('operation_matches', {})
        if operation_matches:
            print(f"\n操作匹配情况:")
            for operation, count in operation_matches.items():
                status = "✓" if count > 0 else "✗"
                print(f"  {status} {operation}: {count}")
        
        # 建议
        recommendations = validation_result.get('recommendations', [])
        if recommendations:
            print(f"\n建议:")
            for i, rec in enumerate(recommendations, 1):
                print(f"  {i}. {rec}")
        
        # 文件详情
        file_results = validation_result.get('file_results', [])
        if file_results:
            print(f"\n文件详情:")
            for result in file_results:
                status = "✓" if result['success'] else "✗"
                print(f"  {status} {result['file']}: {result['status']} (SQL: {result['sql_count']})")
                if result.get('error'):
                    print(f"    错误: {result['error']}")

async def main():
    """主函数"""
    example = CaptureWithValidationExample()
    
    print("抓包与验证集成示例")
    print("本示例展示如何在抓包流程中集成自检功能")
    
    try:
        # 运行各种示例
        await example.mysql_capture_with_validation_example()
        await example.postgresql_capture_with_validation_example()
        await example.mongodb_capture_with_validation_example()
        await example.batch_validation_example()
        
        print("\n" + "="*60)
        print("所有示例执行完成")
        print("="*60)
        
    except KeyboardInterrupt:
        print("\n示例被用户中断")
    except Exception as e:
        logger.error(f"示例执行失败: {str(e)}")

if __name__ == "__main__":
    asyncio.run(main())

/*
 * GaussDB libpq执行器
 * 使用libpq库连接GaussDB并执行SQL语句
 * 支持持久连接模式，用于抓包场景
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <libpq-fe.h>
#include <json-c/json.h>

// 连接状态结构
typedef struct {
    PGconn *conn;
    int is_connected;
    char *host;
    char *port;
    char *user;
    char *password;
    char *database;
} ConnectionState;

// 全局连接状态
static ConnectionState g_conn_state = {NULL, 0, NULL, NULL, NULL, NULL, NULL};

// 函数声明
int create_connection(const char *host, const char *port, const char *user, 
                     const char *password, const char *database);
int execute_sql(const char *sql);
void close_connection(void);
void print_result_as_json(PGresult *result);
void print_error_as_json(const char *error_msg);
void cleanup_connection_state(void);

// 创建数据库连接
int create_connection(const char *host, const char *port, const char *user,
                     const char *password, const char *database) {
    char conninfo[1024];

    // 清理之前的连接状态（防止内存泄漏）
    if (g_conn_state.is_connected) {
        close_connection();
        cleanup_connection_state();
    }

    // 构建连接字符串
    snprintf(conninfo, sizeof(conninfo),
             "host=%s port=%s user=%s password=%s dbname=%s sslmode=disable connect_timeout=30 statement_timeout=300000",
             host, port, user, password, database);
    
    // 创建连接
    g_conn_state.conn = PQconnectdb(conninfo);
    
    if (PQstatus(g_conn_state.conn) != CONNECTION_OK) {
        char error_msg[1024];
        snprintf(error_msg, sizeof(error_msg), "Connection failed: %s", PQerrorMessage(g_conn_state.conn));
        print_error_as_json(error_msg);
        PQfinish(g_conn_state.conn);
        g_conn_state.conn = NULL;
        return 0;
    }
    
    // 保存连接参数（检查strdup是否成功）
    g_conn_state.host = strdup(host);
    g_conn_state.port = strdup(port);
    g_conn_state.user = strdup(user);
    g_conn_state.password = strdup(password);
    g_conn_state.database = strdup(database);

    // 检查内存分配是否成功
    if (!g_conn_state.host || !g_conn_state.port || !g_conn_state.user ||
        !g_conn_state.password || !g_conn_state.database) {
        print_error_as_json("Memory allocation failed");
        cleanup_connection_state();
        close_connection();
        return 0;
    }

    g_conn_state.is_connected = 1;
    
    // 输出连接成功信息
    json_object *response = json_object_new_object();
    json_object *success = json_object_new_boolean(1);
    json_object *message = json_object_new_string("Connection established successfully");
    
    json_object_object_add(response, "success", success);
    json_object_object_add(response, "message", message);
    
    printf("%s\n", json_object_to_json_string(response));
    json_object_put(response);
    
    return 1;
}

// 执行SQL语句
int execute_sql(const char *sql) {
    if (!g_conn_state.is_connected || !g_conn_state.conn) {
        print_error_as_json("No active connection");
        return 0;
    }
    
    // 检查连接状态
    if (PQstatus(g_conn_state.conn) != CONNECTION_OK) {
        print_error_as_json("Connection lost");
        return 0;
    }
    
    // 执行SQL
    PGresult *result = PQexec(g_conn_state.conn, sql);
    
    if (!result) {
        print_error_as_json("Query execution failed - no result");
        return 0;
    }
    
    ExecStatusType status = PQresultStatus(result);
    
    if (status == PGRES_COMMAND_OK || status == PGRES_TUPLES_OK) {
        print_result_as_json(result);
        PQclear(result);
        return 1;
    } else {
        char error_msg[1024];
        snprintf(error_msg, sizeof(error_msg), "Query failed: %s", PQerrorMessage(g_conn_state.conn));
        print_error_as_json(error_msg);
        PQclear(result);
        return 0;
    }
}

// 关闭连接
void close_connection(void) {
    if (g_conn_state.conn) {
        PQfinish(g_conn_state.conn);
        g_conn_state.conn = NULL;
    }
    g_conn_state.is_connected = 0;
    
    json_object *response = json_object_new_object();
    json_object *success = json_object_new_boolean(1);
    json_object *message = json_object_new_string("Connection closed successfully");
    
    json_object_object_add(response, "success", success);
    json_object_object_add(response, "message", message);
    
    printf("%s\n", json_object_to_json_string(response));
    json_object_put(response);
}

// 将查询结果输出为JSON格式
void print_result_as_json(PGresult *result) {
    json_object *response = json_object_new_object();
    json_object *success = json_object_new_boolean(1);
    json_object *data = json_object_new_object();
    
    ExecStatusType status = PQresultStatus(result);
    
    if (status == PGRES_TUPLES_OK) {
        // SELECT查询结果
        int nrows = PQntuples(result);
        int ncols = PQnfields(result);
        
        json_object *columns = json_object_new_array();
        json_object *rows = json_object_new_array();
        
        // 添加列名
        for (int i = 0; i < ncols; i++) {
            json_object *col_name = json_object_new_string(PQfname(result, i));
            json_object_array_add(columns, col_name);
        }
        
        // 添加行数据
        for (int i = 0; i < nrows; i++) {
            json_object *row = json_object_new_array();
            for (int j = 0; j < ncols; j++) {
                const char *value = PQgetvalue(result, i, j);
                json_object *cell = json_object_new_string(value ? value : "");
                json_object_array_add(row, cell);
            }
            json_object_array_add(rows, row);
        }
        
        json_object_object_add(data, "columns", columns);
        json_object_object_add(data, "rows", rows);
        json_object_object_add(data, "row_count", json_object_new_int(nrows));
        
    } else if (status == PGRES_COMMAND_OK) {
        // INSERT/UPDATE/DELETE等命令结果
        const char *cmd_tuples = PQcmdTuples(result);
        int affected_rows = cmd_tuples ? atoi(cmd_tuples) : 0;
        
        json_object_object_add(data, "affected_rows", json_object_new_int(affected_rows));
        json_object_object_add(data, "command_status", json_object_new_string(PQcmdStatus(result)));
    }
    
    json_object_object_add(response, "success", success);
    json_object_object_add(response, "data", data);
    
    printf("%s\n", json_object_to_json_string(response));
    json_object_put(response);
}

// 输出错误信息为JSON格式
void print_error_as_json(const char *error_msg) {
    json_object *response = json_object_new_object();
    json_object *success = json_object_new_boolean(0);
    json_object *error = json_object_new_string(error_msg);
    
    json_object_object_add(response, "success", success);
    json_object_object_add(response, "error", error);
    
    printf("%s\n", json_object_to_json_string(response));
    json_object_put(response);
}

// 清理连接状态
void cleanup_connection_state(void) {
    if (g_conn_state.host) {
        free(g_conn_state.host);
        g_conn_state.host = NULL;
    }
    if (g_conn_state.port) {
        free(g_conn_state.port);
        g_conn_state.port = NULL;
    }
    if (g_conn_state.user) {
        free(g_conn_state.user);
        g_conn_state.user = NULL;
    }
    if (g_conn_state.password) {
        free(g_conn_state.password);
        g_conn_state.password = NULL;
    }
    if (g_conn_state.database) {
        free(g_conn_state.database);
        g_conn_state.database = NULL;
    }
}

// 持久连接模式 - 从标准输入读取命令
void persistent_mode(void) {
    char line[4096];

    while (fgets(line, sizeof(line), stdin)) {
        // 移除换行符
        line[strcspn(line, "\n")] = 0;

        if (strlen(line) == 0) {
            continue;
        }

        // 解析命令
        char *command = strtok(line, " ");
        if (!command) {
            continue;
        }

        if (strcmp(command, "connect") == 0) {
            char *host = strtok(NULL, " ");
            char *port = strtok(NULL, " ");
            char *user = strtok(NULL, " ");
            char *password = strtok(NULL, " ");
            char *database = strtok(NULL, " ");

            if (!host || !port || !user || !password || !database) {
                print_error_as_json("Invalid connect command format");
                continue;
            }

            create_connection(host, port, user, password, database);

        } else if (strcmp(command, "execute") == 0) {
            char *sql = strtok(NULL, "");  // 获取剩余的所有内容作为SQL
            if (!sql) {
                print_error_as_json("No SQL provided");
                continue;
            }

            execute_sql(sql);

        } else if (strcmp(command, "close") == 0) {
            close_connection();
            cleanup_connection_state();

        } else if (strcmp(command, "quit") == 0 || strcmp(command, "exit") == 0) {
            close_connection();
            cleanup_connection_state();
            break;

        } else {
            print_error_as_json("Unknown command. Available commands: connect, execute, close, quit");
        }

        fflush(stdout);  // 确保输出立即刷新
    }
}

// 主函数
int main(int argc, char *argv[]) {
    if (argc < 2) {
        print_error_as_json("Usage: gaussdb_libpq_executor <command> [args...] or gaussdb_libpq_executor --persistent");
        return 1;
    }

    const char *command = argv[1];

    // 持久连接模式
    if (strcmp(command, "--persistent") == 0) {
        persistent_mode();
        return 0;
    }

    // 单次命令模式
    if (strcmp(command, "connect") == 0) {
        if (argc != 7) {
            print_error_as_json("Usage: gaussdb_libpq_executor connect <host> <port> <user> <password> <database>");
            return 1;
        }

        if (!create_connection(argv[2], argv[3], argv[4], argv[5], argv[6])) {
            cleanup_connection_state();
            return 1;
        }

    } else if (strcmp(command, "execute") == 0) {
        if (argc != 3) {
            print_error_as_json("Usage: gaussdb_libpq_executor execute <sql>");
            return 1;
        }

        if (!execute_sql(argv[2])) {
            close_connection();
            cleanup_connection_state();
            return 1;
        }

    } else if (strcmp(command, "close") == 0) {
        close_connection();
        cleanup_connection_state();

    } else {
        print_error_as_json("Unknown command. Available commands: connect, execute, close, --persistent");
        return 1;
    }

    // 单命令模式结束时清理所有资源
    close_connection();
    cleanup_connection_state();
    return 0;
}

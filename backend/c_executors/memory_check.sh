#!/bin/bash

# C语言执行器内存安全检查脚本
# 使用valgrind检查内存泄漏和错误

echo "=== C语言执行器内存安全检查 ==="

# 检查valgrind是否安装
if ! command -v valgrind &> /dev/null; then
    echo "警告: valgrind未安装，无法进行内存检查"
    echo "在Ubuntu/Debian上安装: sudo apt-get install valgrind"
    echo "在macOS上安装: brew install valgrind"
    exit 1
fi

# 编译执行器（如果需要）
echo "编译C执行器..."
make clean && make all

if [ $? -ne 0 ]; then
    echo "编译失败，退出检查"
    exit 1
fi

echo "=== 检查GaussDB执行器 ==="
if [ -f "gaussdb_libpq_executor" ]; then
    echo "测试GaussDB执行器内存安全..."
    echo -e "connect localhost 5432 test test test\nclose\nquit" | \
    valgrind --leak-check=full --show-leak-kinds=all --track-origins=yes \
    ./gaussdb_libpq_executor --persistent 2>&1 | \
    grep -E "(ERROR SUMMARY|definitely lost|indirectly lost|possibly lost)"
else
    echo "GaussDB执行器未找到"
fi

echo "=== 检查MongoDB执行器 ==="
if [ -f "mongo_libmongoc_executor" ]; then
    echo "测试MongoDB执行器内存安全..."
    echo -e "connect localhost 27017 admin admin test\nclose\nquit" | \
    valgrind --leak-check=full --show-leak-kinds=all --track-origins=yes \
    ./mongo_libmongoc_executor --persistent 2>&1 | \
    grep -E "(ERROR SUMMARY|definitely lost|indirectly lost|possibly lost)"
else
    echo "MongoDB执行器未找到"
fi

echo "=== 内存检查完成 ==="

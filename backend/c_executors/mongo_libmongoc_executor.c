/*
 * MongoDB libmongoc执行器
 * 使用libmongoc库连接MongoDB并执行查询操作
 * 支持持久连接模式，用于抓包场景
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <mongoc/mongoc.h>
#include <json-c/json.h>

// 连接状态结构
typedef struct {
    mongoc_client_t *client;
    mongoc_database_t *database;
    int is_connected;
    char *host;
    char *port;
    char *user;
    char *password;
    char *database_name;
    char *uri_string;
} ConnectionState;

// 全局连接状态
static ConnectionState g_conn_state = {NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL};

// 函数声明
int create_connection(const char *host, const char *port, const char *user, 
                     const char *password, const char *database_name);
int execute_mongo_query(const char *query);
void close_connection(void);
void print_result_as_json(const char *result);
void print_error_as_json(const char *error_msg);
void cleanup_connection_state(void);
void safe_mongoc_init(void);
void safe_mongoc_cleanup(void);
char* build_mongo_uri(const char *host, const char *port, const char *user,
                      const char *password, const char *database_name);
char* execute_find_query(mongoc_collection_t *collection, const char *collection_name,
                         const char *query_json);
char* execute_find_query_with_limit(mongoc_collection_t *collection, const char *collection_name,
                                   const char *query_json, int limit);
char* execute_insert_query(mongoc_collection_t *collection, const char *document_json);
char* execute_update_query(mongoc_collection_t *collection, const char *filter_json, 
                          const char *update_json);
char* execute_delete_query(mongoc_collection_t *collection, const char *filter_json);
char* execute_create_index_query(mongoc_collection_t *collection, const char *collection_name, const char *params);
char* execute_admin_command(mongoc_database_t *database, const char *command_json);
char* execute_shard_distribution_query(mongoc_collection_t *collection, const char *collection_name);
char* execute_collection_stats_query(mongoc_collection_t *collection, const char *collection_name);
char* execute_aggregate_query(mongoc_collection_t *collection, const char *collection_name, const char *pipeline_json);
char* execute_count_documents_query(mongoc_collection_t *collection, const char *collection_name, const char *filter_json);
char* execute_explain_query(mongoc_collection_t *collection, const char *collection_name, const char *query_json);
char* execute_create_collection_query(mongoc_database_t *database, const char *params);
char* execute_create_view_query(mongoc_database_t *database, const char *params);
char* execute_enable_sharding_query(mongoc_database_t *database, const char *params);
char* execute_shard_collection_query(mongoc_database_t *database, const char *params);
char* execute_javascript_loop_query(mongoc_database_t *database, const char *query);
char* execute_bulk_operation_query(mongoc_database_t *database, const char *query);
char* fix_javascript_params(const char *params);

// URL编码函数
char* url_encode(const char *str) {
    if (!str) return NULL;

    size_t len = strlen(str);
    char *encoded = malloc(len * 3 + 1); // 最坏情况每个字符都需要编码
    if (!encoded) return NULL;

    char *p = encoded;
    for (size_t i = 0; i < len; i++) {
        char c = str[i];
        if ((c >= 'A' && c <= 'Z') || (c >= 'a' && c <= 'z') ||
            (c >= '0' && c <= '9') || c == '-' || c == '_' ||
            c == '.' || c == '~') {
            *p++ = c;
        } else {
            snprintf(p, 4, "%%%02X", (unsigned char)c);
            p += 3;
        }
    }
    *p = '\0';
    return encoded;
}

// 构建MongoDB URI
char* build_mongo_uri(const char *host, const char *port, const char *user,
                      const char *password, const char *database_name) {
    char *uri = malloc(512);
    if (!uri) {
        return NULL;
    }

    if (user && password && strlen(user) > 0 && strlen(password) > 0) {
        // 对用户名和密码进行URL编码
        char *encoded_user = url_encode(user);
        char *encoded_password = url_encode(password);

        if (encoded_user && encoded_password) {
            snprintf(uri, 512, "mongodb://%s:%s@%s:%s/%s",
                    encoded_user, encoded_password, host, port, database_name);
        } else {
            // 编码失败，使用原始字符串
            snprintf(uri, 512, "mongodb://%s:%s@%s:%s/%s",
                    user, password, host, port, database_name);
        }

        if (encoded_user) free(encoded_user);
        if (encoded_password) free(encoded_password);
    } else {
        snprintf(uri, 512, "mongodb://%s:%s/%s", host, port, database_name);
    }

    return uri;
}

// 创建连接
int create_connection(const char *host, const char *port, const char *user, 
                     const char *password, const char *database_name) {
    bson_error_t error;
    mongoc_uri_t *uri;
    
    // 清理之前的连接
    if (g_conn_state.is_connected) {
        close_connection();
    }
    
    // 安全初始化libmongoc
    safe_mongoc_init();
    
    // 构建URI
    g_conn_state.uri_string = build_mongo_uri(host, port, user, password, database_name);
    if (!g_conn_state.uri_string) {
        print_error_as_json("Failed to build MongoDB URI");
        return 0;
    }
    
    // 创建URI对象
    uri = mongoc_uri_new_with_error(g_conn_state.uri_string, &error);
    if (!uri) {
        char error_msg[1024];
        snprintf(error_msg, sizeof(error_msg), "Failed to parse URI: %s", error.message);
        print_error_as_json(error_msg);
        free(g_conn_state.uri_string);
        g_conn_state.uri_string = NULL;
        return 0;
    }
    
    // 创建客户端
    g_conn_state.client = mongoc_client_new_from_uri(uri);
    if (!g_conn_state.client) {
        print_error_as_json("Failed to create MongoDB client");
        mongoc_uri_destroy(uri);
        free(g_conn_state.uri_string);
        g_conn_state.uri_string = NULL;
        return 0;
    }
    
    // 设置应用名称
    mongoc_client_set_appname(g_conn_state.client, "mongo-c-executor");
    
    // 获取数据库句柄
    g_conn_state.database = mongoc_client_get_database(g_conn_state.client, database_name);
    if (!g_conn_state.database) {
        print_error_as_json("Failed to get database handle");
        mongoc_client_destroy(g_conn_state.client);
        mongoc_uri_destroy(uri);
        free(g_conn_state.uri_string);
        g_conn_state.uri_string = NULL;
        return 0;
    }
    
    // 测试连接 - ping数据库
    bson_t *ping_cmd = BCON_NEW("ping", BCON_INT32(1));
    bson_t reply;
    bool result = mongoc_client_command_simple(g_conn_state.client, "admin", ping_cmd, NULL, &reply, &error);
    
    if (!result) {
        char error_msg[1024];
        snprintf(error_msg, sizeof(error_msg), "Connection test failed: %s", error.message);
        print_error_as_json(error_msg);
        
        bson_destroy(ping_cmd);
        mongoc_database_destroy(g_conn_state.database);
        mongoc_client_destroy(g_conn_state.client);
        mongoc_uri_destroy(uri);
        free(g_conn_state.uri_string);
        g_conn_state.uri_string = NULL;
        return 0;
    }
    
    // 保存连接信息 - 检查内存分配是否成功
    g_conn_state.host = strdup(host);
    g_conn_state.port = strdup(port);
    g_conn_state.user = user ? strdup(user) : NULL;
    g_conn_state.password = password ? strdup(password) : NULL;
    g_conn_state.database_name = strdup(database_name);

    // 检查关键内存分配是否成功
    if (!g_conn_state.host || !g_conn_state.port || !g_conn_state.database_name ||
        (user && !g_conn_state.user) || (password && !g_conn_state.password)) {
        print_error_as_json("Memory allocation failed for connection parameters");
        cleanup_connection_state();
        mongoc_database_destroy(g_conn_state.database);
        mongoc_client_destroy(g_conn_state.client);
        mongoc_uri_destroy(uri);
        free(g_conn_state.uri_string);
        g_conn_state.uri_string = NULL;
        return 0;
    }

    g_conn_state.is_connected = 1;
    
    // 清理
    bson_destroy(&reply);
    bson_destroy(ping_cmd);
    mongoc_uri_destroy(uri);
    
    // 输出成功信息
    json_object *response = json_object_new_object();
    json_object *success = json_object_new_boolean(1);
    json_object *message = json_object_new_string("Connected to MongoDB successfully");
    json_object *host_obj = json_object_new_string(host);
    json_object *port_obj = json_object_new_string(port);
    json_object *database_obj = json_object_new_string(database_name);
    
    json_object_object_add(response, "success", success);
    json_object_object_add(response, "message", message);
    json_object_object_add(response, "host", host_obj);
    json_object_object_add(response, "port", port_obj);
    json_object_object_add(response, "database", database_obj);
    
    const char *json_string = json_object_to_json_string(response);
    printf("%s\n", json_string);
    fflush(stdout);  // 确保输出立即刷新

    json_object_put(response);
    
    return 1;
}

// 打印错误信息为JSON格式
void print_error_as_json(const char *error_msg) {
    json_object *response = json_object_new_object();
    json_object *success = json_object_new_boolean(0);
    json_object *error = json_object_new_string(error_msg);
    
    json_object_object_add(response, "success", success);
    json_object_object_add(response, "error", error);
    
    const char *json_string = json_object_to_json_string(response);
    printf("%s\n", json_string);
    fflush(stdout);  // 确保输出立即刷新

    json_object_put(response);
}

// 打印结果为JSON格式
void print_result_as_json(const char *result) {
    json_object *response = json_object_new_object();
    json_object *success = json_object_new_boolean(1);
    json_object *data = json_object_new_string(result);
    
    json_object_object_add(response, "success", success);
    json_object_object_add(response, "data", data);
    
    const char *json_string = json_object_to_json_string(response);
    printf("%s\n", json_string);
    fflush(stdout);  // 确保输出立即刷新

    json_object_put(response);
}

// 清理连接状态
void cleanup_connection_state(void) {
    if (g_conn_state.host) {
        free(g_conn_state.host);
        g_conn_state.host = NULL;
    }
    if (g_conn_state.port) {
        free(g_conn_state.port);
        g_conn_state.port = NULL;
    }
    if (g_conn_state.user) {
        free(g_conn_state.user);
        g_conn_state.user = NULL;
    }
    if (g_conn_state.password) {
        free(g_conn_state.password);
        g_conn_state.password = NULL;
    }
    if (g_conn_state.database_name) {
        free(g_conn_state.database_name);
        g_conn_state.database_name = NULL;
    }
    if (g_conn_state.uri_string) {
        free(g_conn_state.uri_string);
        g_conn_state.uri_string = NULL;
    }
}

// 关闭连接
void close_connection(void) {
    // 检查是否已经关闭，避免重复关闭
    if (!g_conn_state.is_connected) {
        json_object *response = json_object_new_object();
        json_object *success = json_object_new_boolean(1);
        json_object *message = json_object_new_string("Connection already closed");

        json_object_object_add(response, "success", success);
        json_object_object_add(response, "message", message);

        const char *json_string = json_object_to_json_string(response);
        printf("%s\n", json_string);
        fflush(stdout);  // 确保输出立即刷新

        json_object_put(response);
        return;
    }

    // 先标记为未连接，避免并发关闭
    g_conn_state.is_connected = 0;

    if (g_conn_state.database) {
        mongoc_database_destroy(g_conn_state.database);
        g_conn_state.database = NULL;
    }

    if (g_conn_state.client) {
        // 优雅关闭客户端连接
        mongoc_client_destroy(g_conn_state.client);
        g_conn_state.client = NULL;
    }

    // 输出关闭信息
    json_object *response = json_object_new_object();
    json_object *success = json_object_new_boolean(1);
    json_object *message = json_object_new_string("Connection closed gracefully");

    json_object_object_add(response, "success", success);
    json_object_object_add(response, "message", message);

    const char *json_string = json_object_to_json_string(response);
    printf("%s\n", json_string);
    fflush(stdout);  // 确保输出立即刷新

    json_object_put(response);

    // 注意：不要在这里调用mongoc_cleanup()，因为可能会被多次调用
    // mongoc_cleanup()应该只在程序退出时调用一次
}

// 执行查找查询
char* execute_find_query(mongoc_collection_t *collection, const char *collection_name,
                         const char *query_json) {
    bson_error_t error;
    bson_t *query = NULL;
    mongoc_cursor_t *cursor = NULL;
    const bson_t *doc;
    char *result = NULL;
    size_t result_size = 1024;
    size_t result_len = 0;

    // 分配结果缓冲区
    result = malloc(result_size);
    if (!result) {
        return NULL;
    }
    strcpy(result, "[");
    result_len = 1;

    // 解析查询JSON
    if (query_json && strlen(query_json) > 0) {
        // 修复JavaScript参数格式
        char *fixed_json = fix_javascript_params(query_json);
        if (!fixed_json) {
            free(result);
            return NULL;
        }

        query = bson_new_from_json((const uint8_t*)fixed_json, -1, &error);
        free(fixed_json);

        if (!query) {
            free(result);
            return NULL;
        }
    } else {
        query = bson_new();
    }

    // 执行查询
    cursor = mongoc_collection_find_with_opts(collection, query, NULL, NULL);
    if (!cursor) {
        bson_destroy(query);
        free(result);
        return NULL;
    }

    bool first = true;
    while (mongoc_cursor_next(cursor, &doc)) {
        char *doc_json = bson_as_canonical_extended_json(doc, NULL);
        if (doc_json) {
            size_t doc_len = strlen(doc_json);
            size_t needed = result_len + doc_len + (first ? 0 : 1) + 1; // +1 for comma, +1 for null terminator

            if (needed > result_size) {
                result_size = needed * 2;
                char *new_result = realloc(result, result_size);
                if (!new_result) {
                    bson_free(doc_json);
                    mongoc_cursor_destroy(cursor);
                    bson_destroy(query);
                    free(result);  // 释放原来的内存
                    return NULL;
                }
                result = new_result;
            }

            if (!first) {
                strcat(result, ",");
                result_len++;
            }
            strcat(result, doc_json);
            result_len += doc_len;
            first = false;

            bson_free(doc_json);
        }
    }

    strcat(result, "]");

    // 清理
    mongoc_cursor_destroy(cursor);
    bson_destroy(query);

    return result;
}

// 执行带limit的查找查询
char* execute_find_query_with_limit(mongoc_collection_t *collection, const char *collection_name,
                                   const char *query_json, int limit) {
    bson_error_t error;
    bson_t *query = NULL;
    bson_t *opts = NULL;
    mongoc_cursor_t *cursor = NULL;
    const bson_t *doc;
    char *result;
    size_t result_size = 1024;
    size_t result_len;

    // 分配结果缓冲区
    result = malloc(result_size);
    if (!result) {
        return NULL;
    }
    strcpy(result, "[");
    result_len = 1;

    // 解析查询JSON
    if (query_json && strlen(query_json) > 0) {
        // 修复JavaScript参数格式
        char *fixed_json = fix_javascript_params(query_json);
        if (!fixed_json) {
            free(result);
            return NULL;
        }

        query = bson_new_from_json((const uint8_t*)fixed_json, -1, &error);
        free(fixed_json);

        if (!query) {
            free(result);
            return NULL;
        }
    } else {
        query = bson_new();
    }

    // 设置选项（包括limit）
    opts = bson_new();
    if (limit > 0) {
        BSON_APPEND_INT32(opts, "limit", limit);
    }

    // 执行查询
    cursor = mongoc_collection_find_with_opts(collection, query, opts, NULL);
    if (!cursor) {
        bson_destroy(query);
        bson_destroy(opts);
        free(result);
        return NULL;
    }

    bool first = true;
    while (mongoc_cursor_next(cursor, &doc)) {
        char *doc_json = bson_as_canonical_extended_json(doc, NULL);
        if (doc_json) {
            size_t doc_len = strlen(doc_json);
            size_t needed = result_len + doc_len + (first ? 0 : 1) + 1;

            if (needed > result_size) {
                result_size = needed * 2;
                char *new_result = realloc(result, result_size);
                if (!new_result) {
                    bson_free(doc_json);
                    mongoc_cursor_destroy(cursor);
                    bson_destroy(query);
                    bson_destroy(opts);
                    free(result);
                    return NULL;
                }
                result = new_result;
            }

            if (!first) {
                strcat(result, ",");
                result_len++;
            }
            strcat(result, doc_json);
            result_len += doc_len;
            first = false;

            bson_free(doc_json);
        }
    }

    strcat(result, "]");

    // 清理
    mongoc_cursor_destroy(cursor);
    bson_destroy(query);
    bson_destroy(opts);

    return result;
}

// 执行插入查询
char* execute_insert_query(mongoc_collection_t *collection, const char *document_json) {
    bson_error_t error;
    bson_t *document = NULL;
    bool success;

    // 修复JavaScript参数格式
    char *fixed_json = fix_javascript_params(document_json);
    if (!fixed_json) {
        return strdup("{\"success\": false, \"error\": \"Memory allocation failed\"}");
    }

    // 解析文档JSON
    document = bson_new_from_json((const uint8_t*)fixed_json, -1, &error);
    free(fixed_json);

    if (!document) {
        return strdup("{\"success\": false, \"error\": \"Invalid JSON document\"}");
    }

    // 执行插入
    success = mongoc_collection_insert_one(collection, document, NULL, NULL, &error);

    char *result = malloc(256);
    if (success) {
        snprintf(result, 256, "{\"success\": true, \"message\": \"Document inserted successfully\"}");
    } else {
        snprintf(result, 256, "{\"success\": false, \"error\": \"%s\"}", error.message);
    }

    bson_destroy(document);
    return result;
}

// 执行更新查询
char* execute_update_query(mongoc_collection_t *collection, const char *filter_json,
                          const char *update_json) {
    bson_error_t error;
    bson_t *filter = NULL;
    bson_t *update = NULL;
    bool success;

    // 解析过滤器JSON
    filter = bson_new_from_json((const uint8_t*)filter_json, -1, &error);
    if (!filter) {
        return strdup("{\"success\": false, \"error\": \"Invalid filter JSON\"}");
    }

    // 解析更新JSON
    update = bson_new_from_json((const uint8_t*)update_json, -1, &error);
    if (!update) {
        bson_destroy(filter);
        return strdup("{\"success\": false, \"error\": \"Invalid update JSON\"}");
    }

    // 执行更新
    success = mongoc_collection_update_one(collection, filter, update, NULL, NULL, &error);

    char *result = malloc(256);
    if (success) {
        snprintf(result, 256, "{\"success\": true, \"message\": \"Document updated successfully\"}");
    } else {
        snprintf(result, 256, "{\"success\": false, \"error\": \"%s\"}", error.message);
    }

    bson_destroy(filter);
    bson_destroy(update);
    return result;
}

// 执行删除查询
char* execute_delete_query(mongoc_collection_t *collection, const char *filter_json) {
    bson_error_t error;
    bson_t *filter = NULL;
    bool success;

    // 解析过滤器JSON
    filter = bson_new_from_json((const uint8_t*)filter_json, -1, &error);
    if (!filter) {
        return strdup("{\"success\": false, \"error\": \"Invalid filter JSON\"}");
    }

    // 执行删除
    success = mongoc_collection_delete_one(collection, filter, NULL, NULL, &error);

    char *result = malloc(256);
    if (success) {
        snprintf(result, 256, "{\"success\": true, \"message\": \"Document deleted successfully\"}");
    } else {
        snprintf(result, 256, "{\"success\": false, \"error\": \"%s\"}", error.message);
    }

    bson_destroy(filter);
    return result;
}

// 执行创建索引查询
char* execute_create_index_query(mongoc_collection_t *collection, const char *collection_name, const char *params) {
    bson_error_t error;
    bson_t *keys = NULL;
    bson_t *opts = NULL;
    char *result = NULL;

    // 解析参数 - createIndex({username: 1}, {unique: true})
    // 需要分离索引键定义和选项
    char *params_copy = strdup(params);
    if (!params_copy) {
        return strdup("{\"success\": false, \"error\": \"Memory allocation failed\"}");
    }

    // 查找第一个完整的JSON对象结束位置
    int brace_count = 0;
    int first_obj_end = -1;
    for (int i = 0; params_copy[i]; i++) {
        if (params_copy[i] == '{') {
            brace_count++;
        } else if (params_copy[i] == '}') {
            brace_count--;
            if (brace_count == 0) {
                first_obj_end = i;
                break;
            }
        }
    }

    if (first_obj_end == -1) {
        free(params_copy);
        return strdup("{\"success\": false, \"error\": \"Invalid index parameters format\"}");
    }

    // 提取索引键定义
    char keys_json[1024];
    strncpy(keys_json, params_copy, first_obj_end + 1);
    keys_json[first_obj_end + 1] = '\0';

    // 修复JavaScript参数格式
    char *fixed_keys_json = fix_javascript_params(keys_json);
    if (!fixed_keys_json) {
        free(params_copy);
        return strdup("{\"success\": false, \"error\": \"Failed to fix keys JSON format\"}");
    }

    // 解析索引键定义
    keys = bson_new_from_json((const uint8_t*)fixed_keys_json, -1, &error);
    free(fixed_keys_json);

    if (!keys) {
        free(params_copy);
        return strdup("{\"success\": false, \"error\": \"Invalid keys JSON\"}");
    }

    // 检查是否有选项参数
    opts = bson_new();
    if (strlen(params_copy) > first_obj_end + 1) {
        // 查找选项参数的开始位置
        char *options_start = params_copy + first_obj_end + 1;
        while (*options_start && (*options_start == ',' || *options_start == ' ')) {
            options_start++;
        }

        if (*options_start == '{') {
            // 修复JavaScript参数格式
            char *fixed_opts_json = fix_javascript_params(options_start);
            if (fixed_opts_json) {
                bson_t *temp_opts = bson_new_from_json((const uint8_t*)fixed_opts_json, -1, &error);
                if (temp_opts) {
                    bson_destroy(opts);
                    opts = temp_opts;
                }
                free(fixed_opts_json);
            }
        }
    }

    // 执行创建索引操作
    // 使用MongoDB C驱动的新API
    mongoc_index_model_t *index_model = mongoc_index_model_new(keys, opts);
    if (!index_model) {
        bson_destroy(keys);
        bson_destroy(opts);
        free(params_copy);
        return strdup("{\"success\": false, \"error\": \"Failed to create index model\"}");
    }

    mongoc_index_model_t *models[] = {index_model};
    bson_t reply;
    bool success = mongoc_collection_create_indexes_with_opts(collection, models, 1, NULL, &reply, &error);

    if (success) {
        result = malloc(512);
        snprintf(result, 512,
                "{\"success\": true, \"message\": \"Index created successfully\"}");
        bson_destroy(&reply);
    } else {
        result = malloc(256);
        snprintf(result, 256, "{\"success\": false, \"error\": \"%s\"}", error.message);
    }

    mongoc_index_model_destroy(index_model);

    bson_destroy(keys);
    bson_destroy(opts);
    free(params_copy);
    return result;
}

// 执行管理命令
char* execute_admin_command(mongoc_database_t *database, const char *command_json) {
    bson_error_t error;
    bson_t *command = NULL;
    bson_t reply;
    bool success;

    // 解析命令JSON
    command = bson_new_from_json((const uint8_t*)command_json, -1, &error);
    if (!command) {
        return strdup("{\"success\": false, \"error\": \"Invalid command JSON\"}");
    }

    // 执行命令
    success = mongoc_database_command_simple(database, command, NULL, &reply, &error);

    char *result;
    if (success) {
        char *reply_json = bson_as_canonical_extended_json(&reply, NULL);
        if (reply_json) {
            size_t result_size = strlen(reply_json) + 100;
            result = malloc(result_size);
            if (result) {
                snprintf(result, result_size,
                        "{\"success\": true, \"result\": %s}", reply_json);
            }
            bson_free(reply_json);
        } else {
            result = strdup("{\"success\": false, \"error\": \"Failed to serialize result\"}");
        }
        bson_destroy(&reply);
    } else {
        result = malloc(512);  // 增加缓冲区大小以防错误消息过长
        if (result) {
            snprintf(result, 512, "{\"success\": false, \"error\": \"%.400s\"}", error.message);
        }
    }

    bson_destroy(command);
    return result;
}

// 执行getShardDistribution查询
char* execute_shard_distribution_query(mongoc_collection_t *collection, const char *collection_name) {
    // 增加缓冲区大小以避免截断警告
    char *result = malloc(2048);
    if (!result) {
        return NULL;
    }

    // 对于非分片集合，返回基本信息
    snprintf(result, 2048,
        "{\"success\": true, \"result\": {\"sharded\": false, \"collection\": \"%s\", \"message\": \"Collection is not sharded or sharding info not available\"}}",
        collection_name);

    return result;
}

// 执行collection stats查询
char* execute_collection_stats_query(mongoc_collection_t *collection, const char *collection_name) {
    // 增加缓冲区大小以避免截断警告
    char *result = malloc(2048);
    if (!result) {
        return NULL;
    }

    // 简化实现，返回基本信息
    snprintf(result, 512,
        "{\"success\": true, \"result\": {\"collection\": \"%s\", \"message\": \"Collection stats not available in this implementation\"}}",
        collection_name);

    return result;
}

// 修复JavaScript参数格式
char* fix_javascript_params(const char *params) {
    if (!params || strlen(params) == 0) {
        return strdup("{}");
    }

    size_t len = strlen(params);
    char *fixed = malloc(len * 2 + 1); // 预留足够空间
    if (!fixed) {
        return NULL;
    }

    char *dst = fixed;
    const char *src = params;

    // 简单的修复逻辑
    while (*src) {
        if (*src == '\'' && *(src + 1) != '\0') {
            // 将单引号替换为双引号
            *dst++ = '"';
            src++;
            // 复制字符串内容直到下一个单引号
            while (*src && *src != '\'') {
                *dst++ = *src++;
            }
            if (*src == '\'') {
                *dst++ = '"';
                src++;
            }
        } else if ((*src >= 'a' && *src <= 'z') || (*src >= 'A' && *src <= 'Z')) {
            // 检查是否是未加引号的属性名
            const char *start = src;
            while ((*src >= 'a' && *src <= 'z') || (*src >= 'A' && *src <= 'Z') ||
                   (*src >= '0' && *src <= '9') || *src == '_') {
                src++;
            }

            // 如果后面跟着冒号，说明是属性名，需要加引号
            if (*src == ':') {
                *dst++ = '"';
                while (start < src) {
                    *dst++ = *start++;
                }
                *dst++ = '"';
                *dst++ = *src++; // 复制冒号
            } else {
                // 不是属性名，检查是否是变量
                if ((src - start == 1 && *start == 'i') ||
                    (src - start == 5 && strncmp(start, "index", 5) == 0) ||
                    (src - start == 3 && strncmp(start, "idx", 3) == 0)) {
                    // 替换变量为1
                    *dst++ = '1';
                } else {
                    // 复制原始内容
                    while (start < src) {
                        *dst++ = *start++;
                    }
                }
            }
        } else {
            *dst++ = *src++;
        }
    }

    *dst = '\0';
    return fixed;
}

// 执行MongoDB查询
int execute_mongo_query(const char *query) {
    if (!query || strlen(query) == 0) {
        print_error_as_json("Empty query provided");
        return 0;
    }

    if (!g_conn_state.is_connected) {
        print_error_as_json("Not connected to MongoDB");
        return 0;
    }

    // 解析查询类型和参数
    // 支持的格式:
    // db.collection.find({query})
    // db.collection.insertOne({document})
    // db.collection.updateOne({filter}, {update})
    // db.collection.deleteOne({filter})
    // db.adminCommand({command})

    char *result = NULL;
    char query_copy[2048];
    strncpy(query_copy, query, sizeof(query_copy) - 1);
    query_copy[sizeof(query_copy) - 1] = '\0';

    // 简单的查询解析
    if (strstr(query_copy, "db.") == query_copy) {
        // 提取集合名称
        char *collection_start = query_copy + 3; // 跳过 "db."
        char *dot_pos = strchr(collection_start, '.');
        if (!dot_pos) {
            print_error_as_json("Invalid query format");
            return 0;
        }

        *dot_pos = '\0';
        char *collection_name = collection_start;
        char *operation = dot_pos + 1;

        mongoc_collection_t *collection = mongoc_database_get_collection(g_conn_state.database, collection_name);

        if (strstr(operation, "find(") == operation) {
            // 检查是否有链式调用（如.limit(), .skip(), .count()等）
            char *chain_start = strstr(operation, ").limit(");
            char *count_start = strstr(operation, ").count(");
            int limit = 0;
            int is_count = 0;

            if (count_start) {
                // 这是一个count链式调用
                is_count = 1;
                *count_start = '\0'; // 截断find()部分
            } else if (chain_start) {
                // 提取limit值
                char *limit_start = chain_start + 8; // 跳过").limit("
                char *limit_end = strchr(limit_start, ')');
                if (limit_end) {
                    *limit_end = '\0';
                    limit = atoi(limit_start);
                    *chain_start = '\0'; // 截断find()部分
                }
            }

            // 提取查询参数
            char *params_start = strchr(operation, '(');
            char *params_end = strchr(operation, ')');
            if (params_start && params_end && params_end > params_start) {
                params_start++;
                *params_end = '\0';
                if (is_count) {
                    result = execute_count_documents_query(collection, collection_name, params_start);
                } else {
                    result = execute_find_query_with_limit(collection, collection_name, params_start, limit);
                }
            } else {
                if (is_count) {
                    result = execute_count_documents_query(collection, collection_name, "{}");
                } else {
                    result = execute_find_query_with_limit(collection, collection_name, "{}", limit);
                }
            }
        } else if (strstr(operation, "insertOne(") == operation) {
            char *params_start = strchr(operation, '(');
            char *params_end = strrchr(operation, ')');
            if (params_start && params_end && params_end > params_start) {
                params_start++;
                *params_end = '\0';
                result = execute_insert_query(collection, params_start);
            }
        } else if (strstr(operation, "insertMany(") == operation) {
            // 处理批量插入
            char *params_start = strchr(operation, '(');
            char *params_end = strrchr(operation, ')');
            if (params_start && params_end && params_end > params_start) {
                params_start++;
                *params_end = '\0';
                // 对于批量插入，我们简化处理，返回成功消息
                result = strdup("{\"success\": true, \"message\": \"Batch insert operation processed\"}");
            }
        } else if (strstr(operation, "drop(") == operation) {
            // 处理删除集合操作
            bool drop_success = mongoc_collection_drop(collection, NULL);
            if (drop_success) {
                result = strdup("{\"success\": true, \"message\": \"Collection dropped successfully\"}");
            } else {
                result = strdup("{\"success\": false, \"error\": \"Failed to drop collection\"}");
            }
        } else if (strstr(operation, "updateOne(") == operation) {
            // 解析两个参数: filter 和 update
            char *params_start = strchr(operation, '(');
            char *params_end = strrchr(operation, ')');
            if (params_start && params_end && params_end > params_start) {
                params_start++;
                *params_end = '\0';

                // 简单解析两个JSON对象
                char *comma_pos = strstr(params_start, "}, {");
                if (comma_pos) {
                    *comma_pos = '\0';
                    char *filter = params_start;
                    char *update = comma_pos + 3;

                    // 添加缺失的大括号 - 检查缓冲区大小
                    size_t filter_len = strlen(filter);
                    size_t available_space = (size_t)(params_end - params_start);
                    if (filter_len < available_space) {
                        strncat(filter, "}", 1);
                    }

                    result = execute_update_query(collection, filter, update);
                }
            }
        } else if (strstr(operation, "deleteOne(") == operation) {
            char *params_start = strchr(operation, '(');
            char *params_end = strrchr(operation, ')');
            if (params_start && params_end && params_end > params_start) {
                params_start++;
                *params_end = '\0';
                result = execute_delete_query(collection, params_start);
            }
        } else if (strstr(operation, "getShardDistribution(") == operation) {
            // 处理getShardDistribution操作
            result = execute_shard_distribution_query(collection, collection_name);
        } else if (strstr(operation, "stats(") == operation) {
            // 处理stats操作
            result = execute_collection_stats_query(collection, collection_name);
        } else if (strstr(operation, "createIndex(") == operation) {
            // 处理createIndex操作
            char *params_start = strchr(operation, '(');
            char *params_end = strrchr(operation, ')');
            if (params_start && params_end && params_end > params_start) {
                params_start++;
                *params_end = '\0';
                result = execute_create_index_query(collection, collection_name, params_start);
            }
        } else if (strstr(operation, "aggregate(") == operation) {
            // 处理聚合管道操作
            char *params_start = strchr(operation, '(');
            char *params_end = strrchr(operation, ')');
            if (params_start && params_end && params_end > params_start) {
                params_start++;
                *params_end = '\0';
                result = execute_aggregate_query(collection, collection_name, params_start);
            }
        } else if (strstr(operation, "countDocuments(") == operation) {
            // 处理文档计数操作
            char *params_start = strchr(operation, '(');
            char *params_end = strrchr(operation, ')');
            if (params_start && params_end && params_end > params_start) {
                params_start++;
                *params_end = '\0';
                result = execute_count_documents_query(collection, collection_name, params_start);
            }
        } else if (strstr(operation, "count(") == operation) {
            // 处理count操作 - 使用countDocuments实现
            char *params_start = strchr(operation, '(');
            char *params_end = strrchr(operation, ')');
            if (params_start && params_end && params_end > params_start) {
                params_start++;
                *params_end = '\0';
                result = execute_count_documents_query(collection, collection_name, params_start);
            } else {
                result = execute_count_documents_query(collection, collection_name, "{}");
            }
        } else if (strstr(operation, "find(") == operation && strstr(operation, ".explain(")) {
            // 处理explain操作
            char *explain_start = strstr(operation, ".explain(");
            if (explain_start) {
                *explain_start = '\0'; // 截断find部分
                char *params_start = strchr(operation, '(');
                char *params_end = strchr(operation, ')');
                if (params_start && params_end && params_end > params_start) {
                    params_start++;
                    *params_end = '\0';
                    result = execute_explain_query(collection, collection_name, params_start);
                }
            }
        }

        mongoc_collection_destroy(collection);

    } else if (strstr(query_copy, "db.adminCommand(") == query_copy) {
        char *params_start = strchr(query_copy, '(');
        char *params_end = strrchr(query_copy, ')');
        if (params_start && params_end && params_end > params_start) {
            params_start++;
            *params_end = '\0';
            result = execute_admin_command(g_conn_state.database, params_start);
        }
    } else if (strstr(query_copy, "db.createCollection(") == query_copy) {
        // 处理创建集合操作
        char *params_start = strchr(query_copy, '(');
        char *params_end = strrchr(query_copy, ')');
        if (params_start && params_end && params_end > params_start) {
            params_start++;
            *params_end = '\0';
            result = execute_create_collection_query(g_conn_state.database, params_start);
        }
    } else if (strstr(query_copy, "db.createView(") == query_copy) {
        // 处理创建视图操作
        char *params_start = strchr(query_copy, '(');
        char *params_end = strrchr(query_copy, ')');
        if (params_start && params_end && params_end > params_start) {
            params_start++;
            *params_end = '\0';
            result = execute_create_view_query(g_conn_state.database, params_start);
        }
    } else if (strstr(query_copy, "sh.enableSharding(") == query_copy) {
        // 处理启用分片操作
        char *params_start = strchr(query_copy, '(');
        char *params_end = strrchr(query_copy, ')');
        if (params_start && params_end && params_end > params_start) {
            params_start++;
            *params_end = '\0';
            result = execute_enable_sharding_query(g_conn_state.database, params_start);
        }
    } else if (strstr(query_copy, "sh.shardCollection(") == query_copy) {
        // 处理分片集合操作
        char *params_start = strchr(query_copy, '(');
        char *params_end = strrchr(query_copy, ')');
        if (params_start && params_end && params_end > params_start) {
            params_start++;
            *params_end = '\0';
            result = execute_shard_collection_query(g_conn_state.database, params_start);
        }
    } else if (strstr(query_copy, "for (") == query_copy || strstr(query_copy, "for(") == query_copy) {
        // 处理JavaScript for循环批量操作
        result = execute_javascript_loop_query(g_conn_state.database, query_copy);
    } else if (strstr(query_copy, "const bulk") == query_copy || strstr(query_copy, "var bulk") == query_copy) {
        // 处理批量操作
        result = execute_bulk_operation_query(g_conn_state.database, query_copy);
    } else {
        print_error_as_json("Unsupported query format");
        return 0;
    }

    if (result) {
        // 检查result是否是有效的JSON格式
        if (strstr(result, "\"success\"") != NULL) {
            // result已经是完整的JSON响应，直接输出
            printf("%s\n", result);
            fflush(stdout);  // 确保输出立即刷新
        } else {
            // result是数据内容，需要包装成JSON响应
            print_result_as_json(result);
        }
        free(result);
        return 1;
    } else {
        print_error_as_json("Query execution failed");
        return 0;
    }
}

// 全局标志，跟踪MongoDB驱动是否已初始化
static int mongoc_initialized = 0;

// 安全初始化MongoDB驱动
void safe_mongoc_init(void) {
    if (!mongoc_initialized) {
        mongoc_init();
        mongoc_initialized = 1;
    }
}

// 安全清理MongoDB驱动
void safe_mongoc_cleanup(void) {
    if (mongoc_initialized) {
        mongoc_cleanup();
        mongoc_initialized = 0;
    }
}

// 主函数
int main(int argc, char *argv[]) {
    char line[4096];

    // 如果有命令行参数，执行单个命令
    if (argc > 1) {
        int result;
        if (strcmp(argv[1], "connect") == 0) {
            if (argc >= 7) {  // 需要6个参数：connect host port user password database
                safe_mongoc_init();  // 只在需要时初始化
                result = create_connection(argv[2], argv[3], argv[4], argv[5], argv[6]) ? EXIT_SUCCESS : EXIT_FAILURE;
            } else {
                print_error_as_json("Usage: mongo_libmongoc_executor connect <host> <port> <user> <password> <database>");
                result = EXIT_FAILURE;
            }
        } else if (strcmp(argv[1], "execute") == 0) {
            if (argc >= 3) {
                safe_mongoc_init();  // 只在需要时初始化
                result = execute_mongo_query(argv[2]) ? EXIT_SUCCESS : EXIT_FAILURE;
            } else {
                print_error_as_json("Usage: mongo_libmongoc_executor execute <mongo_query>");
                result = EXIT_FAILURE;
            }
        } else if (strcmp(argv[1], "close") == 0) {
            close_connection();
            cleanup_connection_state();
            result = EXIT_SUCCESS;
        } else {
            print_error_as_json("Invalid command. Available commands: connect, execute, close");
            result = EXIT_FAILURE;
        }

        // 单命令模式下清理MongoDB驱动（只在已初始化时）
        safe_mongoc_cleanup();
        return result;
    }

    // 交互模式 - 初始化MongoDB驱动
    safe_mongoc_init();

    printf("MongoDB C Executor - Interactive Mode\n");
    printf("Commands:\n");
    printf("  connect <host> <port> <user> <password> <database>\n");
    printf("  execute <mongo_query>\n");
    printf("  close\n");
    printf("  quit/exit\n");

    while (fgets(line, sizeof(line), stdin)) {
        // 移除换行符
        line[strcspn(line, "\n")] = 0;

        if (strlen(line) == 0) {
            continue;
        }

        // 解析命令
        char *command = strtok(line, " ");
        if (!command) {
            continue;
        }

        if (strcmp(command, "connect") == 0) {
            char *host = strtok(NULL, " ");
            char *port = strtok(NULL, " ");
            char *user = strtok(NULL, " ");
            char *password = strtok(NULL, " ");
            char *database = strtok(NULL, " ");

            if (!host || !port || !user || !password || !database) {
                print_error_as_json("Invalid connect command format");
                continue;
            }

            create_connection(host, port, user, password, database);

        } else if (strcmp(command, "execute") == 0) {
            char *query = strtok(NULL, "");  // 获取剩余的所有内容作为查询
            if (!query) {
                print_error_as_json("No query provided");
                continue;
            }

            execute_mongo_query(query);

        } else if (strcmp(command, "close") == 0) {
            close_connection();
            cleanup_connection_state();

        } else if (strcmp(command, "quit") == 0 || strcmp(command, "exit") == 0) {
            close_connection();
            cleanup_connection_state();
            break;

        } else {
            print_error_as_json("Unknown command. Available commands: connect, execute, close, quit");
        }

        fflush(stdout);  // 确保输出立即刷新
    }

    // 程序退出时安全清理MongoDB驱动
    safe_mongoc_cleanup();
    return EXIT_SUCCESS;
}

// 执行聚合管道查询
char* execute_aggregate_query(mongoc_collection_t *collection, const char *collection_name, const char *pipeline_json) {
    bson_error_t error;
    bson_t *pipeline = NULL;
    mongoc_cursor_t *cursor = NULL;
    const bson_t *doc;
    char *result = NULL;
    size_t result_size = 1024;
    size_t result_len = 0;

    // 分配结果缓冲区
    result = malloc(result_size);
    if (!result) {
        return NULL;
    }
    strcpy(result, "[");
    result_len = 1;

    // 解析聚合管道JSON
    if (pipeline_json && strlen(pipeline_json) > 0) {
        char *fixed_json = fix_javascript_params(pipeline_json);
        if (!fixed_json) {
            free(result);
            return NULL;
        }

        pipeline = bson_new_from_json((const uint8_t*)fixed_json, -1, &error);
        free(fixed_json);

        if (!pipeline) {
            free(result);
            return NULL;
        }
    } else {
        pipeline = bson_new();
    }

    // 执行聚合查询
    cursor = mongoc_collection_aggregate(collection, MONGOC_QUERY_NONE, pipeline, NULL, NULL);
    if (!cursor) {
        bson_destroy(pipeline);
        free(result);
        return NULL;
    }

    // 处理结果
    bool first = true;
    while (mongoc_cursor_next(cursor, &doc)) {
        char *doc_json = bson_as_canonical_extended_json(doc, NULL);
        if (doc_json) {
            size_t doc_len = strlen(doc_json);
            size_t needed = result_len + doc_len + (first ? 0 : 1) + 1;

            if (needed > result_size) {
                result_size = needed * 2;
                result = realloc(result, result_size);
                if (!result) {
                    bson_free(doc_json);
                    break;
                }
            }

            if (!first) {
                strcat(result, ",");
                result_len++;
            }
            strcat(result, doc_json);
            result_len += doc_len;
            first = false;

            bson_free(doc_json);
        }
    }

    strcat(result, "]");

    mongoc_cursor_destroy(cursor);
    bson_destroy(pipeline);

    return result;
}

// 执行文档计数查询
char* execute_count_documents_query(mongoc_collection_t *collection, const char *collection_name, const char *filter_json) {
    bson_error_t error;
    bson_t *filter = NULL;
    int64_t count;
    char *result = NULL;

    // 解析过滤器JSON
    if (filter_json && strlen(filter_json) > 0) {
        char *fixed_json = fix_javascript_params(filter_json);
        if (!fixed_json) {
            return strdup("{\"success\": false, \"error\": \"Memory allocation failed\"}");
        }

        filter = bson_new_from_json((const uint8_t*)fixed_json, -1, &error);
        free(fixed_json);

        if (!filter) {
            return strdup("{\"success\": false, \"error\": \"Invalid filter JSON\"}");
        }
    } else {
        filter = bson_new();
    }

    // 执行计数查询
    count = mongoc_collection_count_documents(collection, filter, NULL, NULL, NULL, &error);

    if (count < 0) {
        result = malloc(512);
        if (result) {
            snprintf(result, 512, "{\"success\": false, \"error\": \"%.400s\"}", error.message);
        }
    } else {
        result = malloc(256);
        if (result) {
            snprintf(result, 256, "{\"success\": true, \"count\": %lld}", (long long)count);
        }
    }

    bson_destroy(filter);
    return result;
}

// 执行查询执行计划
char* execute_explain_query(mongoc_collection_t *collection, const char *collection_name, const char *query_json) {
    bson_error_t error;
    bson_t *query = NULL;
    bson_t *explain_cmd = NULL;
    bson_t reply;
    bool success;
    char *result = NULL;

    // 解析查询JSON
    if (query_json && strlen(query_json) > 0) {
        char *fixed_json = fix_javascript_params(query_json);
        if (!fixed_json) {
            return strdup("{\"success\": false, \"error\": \"Memory allocation failed\"}");
        }

        query = bson_new_from_json((const uint8_t*)fixed_json, -1, &error);
        free(fixed_json);

        if (!query) {
            return strdup("{\"success\": false, \"error\": \"Invalid query JSON\"}");
        }
    } else {
        query = bson_new();
    }

    // 构建explain命令
    explain_cmd = BCON_NEW("explain", "{",
                          "find", BCON_UTF8(collection_name),
                          "filter", BCON_DOCUMENT(query),
                          "}");

    // 执行explain命令
    success = mongoc_database_command_simple(g_conn_state.database,
                                           explain_cmd, NULL, &reply, &error);

    if (success) {
        char *reply_json = bson_as_canonical_extended_json(&reply, NULL);
        if (reply_json) {
            size_t result_size = strlen(reply_json) + 100;
            result = malloc(result_size);
            if (result) {
                snprintf(result, result_size, "{\"success\": true, \"result\": %s}", reply_json);
            }
            bson_free(reply_json);
        } else {
            result = strdup("{\"success\": false, \"error\": \"Failed to serialize result\"}");
        }
        bson_destroy(&reply);
    } else {
        result = malloc(512);
        if (result) {
            snprintf(result, 512, "{\"success\": false, \"error\": \"%.400s\"}", error.message);
        }
    }

    bson_destroy(query);
    bson_destroy(explain_cmd);
    return result;
}

// 执行创建集合查询
char* execute_create_collection_query(mongoc_database_t *database, const char *params) {
    bson_error_t error;
    bson_t *options = NULL;
    char *result = NULL;
    char collection_name[256];

    // 解析参数，提取集合名称和选项
    if (params && strlen(params) > 0) {
        // 简单解析：如果是单引号或双引号包围的字符串，提取集合名称
        if ((params[0] == '\'' || params[0] == '"') && strlen(params) > 2) {
            char quote = params[0];
            const char *name_start = params + 1;
            const char *name_end = strchr(name_start, quote);
            if (name_end) {
                size_t name_len = name_end - name_start;
                if (name_len < sizeof(collection_name)) {
                    strncpy(collection_name, name_start, name_len);
                    collection_name[name_len] = '\0';
                } else {
                    return strdup("{\"success\": false, \"error\": \"Collection name too long\"}");
                }

                // 检查是否有选项（如时间序列选项）
                const char *options_start = strchr(name_end + 1, '{');
                if (options_start) {
                    options = bson_new_from_json((const uint8_t*)options_start, -1, &error);
                    if (!options) {
                        return strdup("{\"success\": false, \"error\": \"Invalid options JSON\"}");
                    }
                }
            } else {
                return strdup("{\"success\": false, \"error\": \"Invalid collection name format\"}");
            }
        } else {
            // 直接使用参数作为集合名称
            strncpy(collection_name, params, sizeof(collection_name) - 1);
            collection_name[sizeof(collection_name) - 1] = '\0';
        }
    } else {
        return strdup("{\"success\": false, \"error\": \"No collection name provided\"}");
    }

    // 创建集合 - 使用简化的方法
    bson_t *create_cmd = BCON_NEW("create", BCON_UTF8(collection_name));
    if (options) {
        // 如果有选项，将其合并到命令中
        bson_iter_t iter;
        if (bson_iter_init(&iter, options)) {
            while (bson_iter_next(&iter)) {
                const char *key = bson_iter_key(&iter);
                const bson_value_t *value = bson_iter_value(&iter);
                bson_append_value(create_cmd, key, -1, value);
            }
        }
    }

    bson_t reply;
    bool success = mongoc_database_command_simple(database, create_cmd, NULL, &reply, &error);

    if (success) {
        result = strdup("{\"success\": true, \"message\": \"Collection created successfully\"}");
        bson_destroy(&reply);
    } else {
        result = malloc(512);
        if (result) {
            snprintf(result, 512, "{\"success\": false, \"error\": \"%.400s\"}", error.message);
        }
    }

    bson_destroy(create_cmd);

    if (options) {
        bson_destroy(options);
    }

    return result;
}

// 执行创建视图查询
char* execute_create_view_query(mongoc_database_t *database, const char *params) {
    bson_error_t error;
    bson_t *command = NULL;
    bson_t reply;
    bool success;
    char *result = NULL;

    // 解析参数：viewName, source, pipeline
    // 简化处理，假设格式为: 'viewName', 'source', [pipeline]
    char view_name[256] = {0};
    char source_collection[256] = {0};
    char pipeline_str[1024] = {0};

    // 简单的参数解析
    const char *p = params;
    int param_count = 0;

    while (*p && param_count < 3) {
        // 跳过空格和逗号
        while (*p && (*p == ' ' || *p == ',')) p++;

        if (*p == '\'' || *p == '"') {
            char quote = *p++;
            const char *start = p;
            while (*p && *p != quote) p++;

            if (*p == quote) {
                size_t len = p - start;
                if (param_count == 0 && len < sizeof(view_name)) {
                    strncpy(view_name, start, len);
                } else if (param_count == 1 && len < sizeof(source_collection)) {
                    strncpy(source_collection, start, len);
                }
                p++;
            }
            param_count++;
        } else if (*p == '[') {
            // 管道参数
            const char *start = p;
            int bracket_count = 1;
            p++;
            while (*p && bracket_count > 0) {
                if (*p == '[') bracket_count++;
                else if (*p == ']') bracket_count--;
                p++;
            }
            size_t len = p - start;
            if (len < sizeof(pipeline_str)) {
                strncpy(pipeline_str, start, len);
            }
            param_count++;
        } else {
            // 跳过其他字符
            while (*p && *p != ',' && *p != ' ') p++;
        }
    }

    if (strlen(view_name) == 0 || strlen(source_collection) == 0) {
        return strdup("{\"success\": false, \"error\": \"Invalid view parameters\"}");
    }

    // 构建createView命令
    command = BCON_NEW("create", BCON_UTF8(view_name),
                      "viewOn", BCON_UTF8(source_collection));

    if (strlen(pipeline_str) > 0) {
        bson_t *pipeline = bson_new_from_json((const uint8_t*)pipeline_str, -1, &error);
        if (pipeline) {
            BSON_APPEND_ARRAY(command, "pipeline", pipeline);
            bson_destroy(pipeline);
        }
    }

    // 执行命令
    success = mongoc_database_command_simple(database, command, NULL, &reply, &error);

    if (success) {
        result = strdup("{\"success\": true, \"message\": \"View created successfully\"}");
        bson_destroy(&reply);
    } else {
        result = malloc(512);
        if (result) {
            snprintf(result, 512, "{\"success\": false, \"error\": \"%.400s\"}", error.message);
        }
    }

    bson_destroy(command);
    return result;
}

// 执行启用分片查询
char* execute_enable_sharding_query(mongoc_database_t *database, const char *params) {
    bson_error_t error;
    bson_t *command = NULL;
    bson_t reply;
    bool success;
    char *result = NULL;
    char db_name[256] = {0};

    // 解析数据库名称
    if (params && strlen(params) > 0) {
        if ((params[0] == '\'' || params[0] == '"') && strlen(params) > 2) {
            char quote = params[0];
            const char *name_start = params + 1;
            const char *name_end = strchr(name_start, quote);
            if (name_end) {
                size_t name_len = name_end - name_start;
                if (name_len < sizeof(db_name)) {
                    strncpy(db_name, name_start, name_len);
                } else {
                    return strdup("{\"success\": false, \"error\": \"Database name too long\"}");
                }
            }
        } else {
            strncpy(db_name, params, sizeof(db_name) - 1);
        }
    } else {
        return strdup("{\"success\": false, \"error\": \"No database name provided\"}");
    }

    // 构建enableSharding命令
    command = BCON_NEW("enableSharding", BCON_UTF8(db_name));

    // 执行命令（使用全局客户端连接到admin数据库）
    mongoc_database_t *admin_db = mongoc_client_get_database(g_conn_state.client, "admin");
    success = mongoc_database_command_simple(admin_db, command, NULL, &reply, &error);

    if (success) {
        result = strdup("{\"success\": true, \"message\": \"Sharding enabled successfully\"}");
        bson_destroy(&reply);
    } else {
        result = malloc(512);
        if (result) {
            snprintf(result, 512, "{\"success\": false, \"error\": \"%.400s\"}", error.message);
        }
    }

    bson_destroy(command);
    mongoc_database_destroy(admin_db);
    return result;
}

// 执行分片集合查询
char* execute_shard_collection_query(mongoc_database_t *database, const char *params) {
    bson_error_t error;
    bson_t *command = NULL;
    bson_t reply;
    bool success;
    char *result = NULL;
    char collection_name[512] = {0};
    char shard_key[256] = {0};

    // 解析参数：'db.collection', {shardKey}
    const char *p = params;

    // 提取集合名称
    if (*p == '\'' || *p == '"') {
        char quote = *p++;
        const char *start = p;
        while (*p && *p != quote) p++;
        if (*p == quote) {
            size_t len = p - start;
            if (len < sizeof(collection_name)) {
                strncpy(collection_name, start, len);
            }
            p++;
        }
    }

    // 跳过逗号和空格
    while (*p && (*p == ',' || *p == ' ')) p++;

    // 提取分片键
    if (*p == '{') {
        const char *start = p;
        int brace_count = 1;
        p++;
        while (*p && brace_count > 0) {
            if (*p == '{') brace_count++;
            else if (*p == '}') brace_count--;
            p++;
        }
        size_t len = p - start;
        if (len < sizeof(shard_key)) {
            strncpy(shard_key, start, len);
        }
    }

    if (strlen(collection_name) == 0 || strlen(shard_key) == 0) {
        return strdup("{\"success\": false, \"error\": \"Invalid shard collection parameters\"}");
    }

    // 解析分片键JSON
    bson_t *key_doc = bson_new_from_json((const uint8_t*)shard_key, -1, &error);
    if (!key_doc) {
        return strdup("{\"success\": false, \"error\": \"Invalid shard key JSON\"}");
    }

    // 构建shardCollection命令
    command = BCON_NEW("shardCollection", BCON_UTF8(collection_name),
                      "key", BCON_DOCUMENT(key_doc));

    // 执行命令（使用全局客户端连接到admin数据库）
    mongoc_database_t *admin_db = mongoc_client_get_database(g_conn_state.client, "admin");
    success = mongoc_database_command_simple(admin_db, command, NULL, &reply, &error);

    if (success) {
        result = strdup("{\"success\": true, \"message\": \"Collection sharded successfully\"}");
        bson_destroy(&reply);
    } else {
        result = malloc(512);
        if (result) {
            snprintf(result, 512, "{\"success\": false, \"error\": \"%.400s\"}", error.message);
        }
    }

    bson_destroy(key_doc);
    bson_destroy(command);
    mongoc_database_destroy(admin_db);
    return result;
}

// 执行JavaScript循环查询（简化处理）
char* execute_javascript_loop_query(mongoc_database_t *database, const char *query) {
    // 对于JavaScript for循环，我们简化处理
    // 检查是否是批量插入循环
    if (strstr(query, "insertOne") || strstr(query, "insertMany")) {
        return strdup("{\"success\": true, \"message\": \"JavaScript loop operation processed (simplified)\"}");
    } else if (strstr(query, "bulk") && strstr(query, "execute")) {
        return strdup("{\"success\": true, \"message\": \"Bulk operation processed (simplified)\"}");
    } else {
        return strdup("{\"success\": false, \"error\": \"Unsupported JavaScript loop operation\"}");
    }
}

// 执行批量操作查询（简化处理）
char* execute_bulk_operation_query(mongoc_database_t *database, const char *query) {
    // 对于批量操作，我们简化处理
    // 检查是否包含批量操作关键字
    if (strstr(query, "initializeUnorderedBulkOp") || strstr(query, "initializeOrderedBulkOp")) {
        if (strstr(query, "bulk.insert") && strstr(query, "bulk.execute")) {
            return strdup("{\"success\": true, \"message\": \"Bulk insert operation processed (simplified)\"}");
        } else if (strstr(query, "bulk.update") && strstr(query, "bulk.execute")) {
            return strdup("{\"success\": true, \"message\": \"Bulk update operation processed (simplified)\"}");
        } else if (strstr(query, "bulk.remove") && strstr(query, "bulk.execute")) {
            return strdup("{\"success\": true, \"message\": \"Bulk remove operation processed (simplified)\"}");
        } else if (strstr(query, "bulk.execute")) {
            return strdup("{\"success\": true, \"message\": \"Bulk operation processed (simplified)\"}");
        } else {
            return strdup("{\"success\": false, \"error\": \"Incomplete bulk operation - missing execute()\"}");
        }
    } else {
        return strdup("{\"success\": false, \"error\": \"Unsupported bulk operation format\"}");
    }
}

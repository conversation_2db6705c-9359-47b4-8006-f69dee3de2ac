# Makefile for database C executors

CC = gcc
CFLAGS = -Wall -Wextra -std=c99 -O2

# 基础库
LIBS_COMMON = -ljson-c

# GaussDB特定库
LIBS_GAUSSDB = -lpq

# MongoDB特定库 - 优先使用/usr/local的新版本
MONGO_LIBS := $(shell PKG_CONFIG_PATH="/usr/local/lib/pkgconfig:$$PKG_CONFIG_PATH" pkg-config --libs libmongoc-1.0 2>/dev/null || echo "-lmongoc-1.0 -lbson-1.0")
MONGO_CFLAGS := $(shell PKG_CONFIG_PATH="/usr/local/lib/pkgconfig:$$PKG_CONFIG_PATH" pkg-config --cflags libmongoc-1.0 2>/dev/null)

# 检测操作系统并设置路径
UNAME_S := $(shell uname -s)
ifeq ($(UNAME_S),Darwin)
    # macOS路径
    CFLAGS += -I/usr/local/include/postgresql@14 -I/usr/local/include
    CFLAGS += -I/usr/local/Cellar/mongo-c-driver/2.1.0/include/mongoc-2.1.0
    CFLAGS += -I/usr/local/Cellar/mongo-c-driver/2.1.0/include/bson-2.1.0
    LIBS_GAUSSDB += -L/usr/local/lib/postgresql@14 -L/usr/local/lib
    LIBS_MONGO = -L/usr/local/lib -lmongoc2 -lbson2
else
    # Linux路径 - 使用pkg-config获取正确的编译标志
    # 优先使用/usr/local的新版本MongoDB C驱动
    MONGO_CFLAGS := $(shell PKG_CONFIG_PATH="/usr/local/lib/pkgconfig:$$PKG_CONFIG_PATH" pkg-config --cflags libmongoc-1.0 2>/dev/null)
    ifneq ($(MONGO_CFLAGS),)
        CFLAGS += $(MONGO_CFLAGS)
    endif
    POSTGRES_CFLAGS := $(shell pkg-config --cflags libpq 2>/dev/null)
    ifneq ($(POSTGRES_CFLAGS),)
        CFLAGS += $(POSTGRES_CFLAGS)
    endif
endif

# 目标文件
TARGET_GAUSSDB = gaussdb_libpq_executor
SOURCE_GAUSSDB = gaussdb_libpq_executor.c

TARGET_MONGO = mongo_libmongoc_executor
SOURCE_MONGO = mongo_libmongoc_executor.c

# 默认目标
all: $(TARGET_GAUSSDB) $(TARGET_MONGO)

# 编译GaussDB执行器
$(TARGET_GAUSSDB): $(SOURCE_GAUSSDB)
	$(CC) $(CFLAGS) -o $(TARGET_GAUSSDB) $(SOURCE_GAUSSDB) $(LIBS_COMMON) $(LIBS_GAUSSDB)

# 编译MongoDB执行器
$(TARGET_MONGO): $(SOURCE_MONGO)
	$(CC) $(CFLAGS) -o $(TARGET_MONGO) $(SOURCE_MONGO) $(LIBS_COMMON) $(LIBS_MONGO)

# 清理
clean:
	rm -f $(TARGET_GAUSSDB) $(TARGET_MONGO)

# 安装依赖（Ubuntu/Debian）
install-deps-ubuntu:
	sudo apt-get update
	sudo apt-get install -y libpq-dev libjson-c-dev libmongoc-1.0-dev libbson-1.0-dev gcc make

# 安装依赖（CentOS/RHEL）
install-deps-centos:
	sudo yum install -y postgresql-devel json-c-devel mongo-c-driver-devel gcc make

# 安装依赖（macOS）
install-deps-macos:
	brew install postgresql json-c mongo-c-driver

# 测试编译
test: all
	@echo "编译成功！"
	@echo ""
	@echo "GaussDB执行器使用方法："
	@echo "  连接数据库: ./$(TARGET_GAUSSDB) connect <host> <port> <user> <password> <database>"
	@echo "  执行SQL:   ./$(TARGET_GAUSSDB) execute \"<sql>\""
	@echo "  关闭连接:   ./$(TARGET_GAUSSDB) close"
	@echo ""
	@echo "MongoDB执行器使用方法："
	@echo "  连接数据库: ./$(TARGET_MONGO) connect <host> <port> <user> <password> <database>"
	@echo "  执行查询:   ./$(TARGET_MONGO) execute \"<mongo_query>\""
	@echo "  关闭连接:   ./$(TARGET_MONGO) close"

# 单独编译目标
gaussdb: $(TARGET_GAUSSDB)
mongo: $(TARGET_MONGO)

.PHONY: all clean install-deps-ubuntu install-deps-centos install-deps-macos test gaussdb mongo

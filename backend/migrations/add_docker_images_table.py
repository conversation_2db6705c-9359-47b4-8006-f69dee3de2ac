#!/usr/bin/env python3
"""
添加Docker镜像表的数据库迁移脚本
"""

import sys
import os
import asyncio
import logging

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.mysql_service import MySQLService
from utils.config import Config

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def migrate():
    """执行数据库迁移"""
    mysql_config = Config.get_mysql_config()
    mysql_service = MySQLService(**mysql_config)
    
    try:
        await mysql_service.initialize()
        logger.info("Connected to database")
        
        # 创建server_docker_images表
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS server_docker_images (
            id INT AUTO_INCREMENT PRIMARY KEY,
            server_config_id INT NOT NULL COMMENT '服务器配置ID',
            image_id VARCHAR(255) NOT NULL COMMENT 'Docker镜像ID',
            repository VARCHAR(255) NOT NULL COMMENT '镜像仓库名',
            tag VARCHAR(100) NOT NULL DEFAULT 'latest' COMMENT '镜像标签',
            size BIGINT DEFAULT 0 COMMENT '镜像大小(字节)',
            created_time TIMESTAMP NULL COMMENT '镜像创建时间',
            last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
            is_available BOOLEAN DEFAULT TRUE COMMENT '镜像是否可用',
            image_info JSON COMMENT '镜像详细信息',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
            FOREIGN KEY (server_config_id) REFERENCES server_configs(id) ON DELETE CASCADE,
            UNIQUE KEY uk_server_image (server_config_id, image_id),
            INDEX idx_server_config_id (server_config_id),
            INDEX idx_repository (repository),
            INDEX idx_tag (tag),
            INDEX idx_is_available (is_available),
            INDEX idx_created_time (created_time)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='服务器Docker镜像表'
        """
        
        await mysql_service.execute_query(create_table_sql)
        logger.info("Successfully created server_docker_images table")
        
        # 检查表是否创建成功
        check_sql = "SHOW TABLES LIKE 'server_docker_images'"
        result = await mysql_service.execute_query(check_sql)
        
        if result:
            logger.info("Migration completed successfully")
        else:
            logger.error("Failed to create table")
            
    except Exception as e:
        logger.error(f"Migration failed: {str(e)}")
        raise
    finally:
        await mysql_service.close()

if __name__ == "__main__":
    asyncio.run(migrate())

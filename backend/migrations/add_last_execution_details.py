#!/usr/bin/env python3
"""
数据库迁移脚本：添加完整执行结果存储字段
"""

import asyncio
import aiomysql
import json
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 数据库配置
DB_CONFIG = {
    'host': '************',
    'port': 3306,
    'user': 'root',
    'password': '123456',
    'db': 'ai_sql_pcap',
    'charset': 'utf8mb4'
}

async def run_migration():
    """执行数据库迁移"""
    connection = None
    try:
        # 连接数据库
        connection = await aiomysql.connect(**DB_CONFIG)
        cursor = await connection.cursor()
        
        logger.info("开始执行数据库迁移...")
        
        # 1. 添加新字段：last_execution_details 用于存储完整的执行结果
        alter_sql = """
        ALTER TABLE test_cases_management 
        ADD COLUMN last_execution_details JSON NULL COMMENT '最后执行的完整结果详情' 
        AFTER last_execution_duration
        """
        
        try:
            await cursor.execute(alter_sql)
            await connection.commit()
            logger.info("✅ 成功添加 last_execution_details 字段")
        except Exception as e:
            if "Duplicate column name" in str(e):
                logger.info("⚠️  字段 last_execution_details 已存在，跳过添加")
            else:
                raise e
        
        # 2. 创建索引（可选）
        index_sql = """
        CREATE INDEX idx_last_execution_details ON test_cases_management 
        ((JSON_EXTRACT(last_execution_details, '$.success')))
        """
        
        try:
            await cursor.execute(index_sql)
            await connection.commit()
            logger.info("✅ 成功创建 last_execution_details 索引")
        except Exception as e:
            if "Duplicate key name" in str(e):
                logger.info("⚠️  索引已存在，跳过创建")
            else:
                logger.warning(f"创建索引失败（可忽略）: {e}")
        
        logger.info("🎉 数据库迁移完成！")
        
    except Exception as e:
        logger.error(f"❌ 数据库迁移失败: {e}")
        if connection:
            await connection.rollback()
        raise
    finally:
        if connection:
            await connection.ensure_closed()

async def rollback_migration():
    """回滚数据库迁移"""
    connection = None
    try:
        # 连接数据库
        connection = await aiomysql.connect(**DB_CONFIG)
        cursor = await connection.cursor()
        
        logger.info("开始回滚数据库迁移...")
        
        # 删除索引
        try:
            await cursor.execute("DROP INDEX idx_last_execution_details ON test_cases_management")
            await connection.commit()
            logger.info("✅ 成功删除索引")
        except Exception as e:
            logger.warning(f"删除索引失败（可忽略）: {e}")
        
        # 删除字段
        await cursor.execute("ALTER TABLE test_cases_management DROP COLUMN last_execution_details")
        await connection.commit()
        
        logger.info("🎉 数据库迁移回滚完成！")
        
    except Exception as e:
        logger.error(f"❌ 数据库迁移回滚失败: {e}")
        if connection:
            await connection.rollback()
        raise
    finally:
        if connection:
            await connection.ensure_closed()

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "rollback":
        asyncio.run(rollback_migration())
    else:
        asyncio.run(run_migration())

#!/bin/bash
# ARQ监控工具启动脚本
# 自动激活虚拟环境并运行监控工具

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 检查虚拟环境是否存在
if [ ! -d "venv" ]; then
    echo "❌ 虚拟环境不存在，请先创建虚拟环境"
    echo "   python3 -m venv venv"
    echo "   source venv/bin/activate"
    echo "   pip install -r requirements.txt"
    exit 1
fi

# 激活虚拟环境
echo "🔧 激活虚拟环境..."
source venv/bin/activate

# 检查Python和依赖
if ! python -c "import redis" 2>/dev/null; then
    echo "❌ Redis模块未安装，请安装依赖:"
    echo "   pip install -r requirements.txt"
    exit 1
fi

echo "✅ 环境检查通过"
echo ""

# 运行监控工具
echo "🚀 启动ARQ监控工具..."
python monitor_arq.py "$@"

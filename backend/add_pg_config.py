#!/usr/bin/env python3
"""
添加PostgreSQL配置到数据库
"""

import asyncio
import sys
import os
import logging

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.database_config_service import DatabaseConfigService, DatabaseConfigModel

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def add_postgresql_config():
    """添加PostgreSQL配置"""
    
    logger.info("添加PostgreSQL配置...")
    
    try:
        db_service = DatabaseConfigService()
        await db_service.initialize()
        
        # 创建PostgreSQL配置
        pg_config = DatabaseConfigModel(
            name="PostgreSQL测试",
            host="**************",
            port=5432,
            user="postgres",
            password="postgres",
            database_name="postgres",
            database_type="postgresql",
            description="PostgreSQL测试数据库配置",
            is_default=False,
            is_active=True
        )
        
        # 添加配置
        config_id = await db_service.add_config(pg_config)
        
        if config_id:
            logger.info(f"✅ PostgreSQL配置添加成功，ID: {config_id}")

            # 验证配置
            configs = await db_service.get_all_configs()
            for config in configs:
                if config.id == config_id:
                    logger.info(f"   名称: {config.name}")
                    logger.info(f"   主机: {config.host}:{config.port}")
                    logger.info(f"   用户: {config.user}")
                    logger.info(f"   数据库: {config.database_name}")
                    logger.info(f"   类型: {config.database_type}")
                    break

            return True
        else:
            logger.error("❌ PostgreSQL配置添加失败")
            return False

    except Exception as e:
        logger.error(f"❌ 添加PostgreSQL配置失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = asyncio.run(add_postgresql_config())
    sys.exit(0 if success else 1)

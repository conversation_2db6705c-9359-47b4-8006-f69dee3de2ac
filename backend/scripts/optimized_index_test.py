#!/usr/bin/env python3
"""
优化的索引性能测试脚本
减少数据量，避免长时间执行导致卡住
"""

import asyncio
import sys
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from services.gaussdb_service_v2 import GaussDBServiceV2

# 优化的测试用例配置
OPTIMIZED_TEST_CASE = {
    "id": "7242e02e-5d4a-4b73-8439-cf300537e7a9-optimized",
    "title": "索引性能测试（优化版）",
    "module": "性能优化",
    "priority": "medium",
    "status": "pending",
    "preconditions": "数据库连接信息: 主机:**************:5432/postgres，用户:gaussdb，环境初始状态: 无测试表",
    "test_steps": [
        {
            "step_number": 1,
            "action": "创建小数据量表 - 创建orders表并插入1000条测试数据",
            "expected_result": "表创建成功，1000条记录插入成功",
            "test_data": "CREATE TABLE IF NOT EXISTS orders_test (id SERIAL PRIMARY KEY, customer_id INT, order_date DATE, amount DECIMAL(10,2), status VARCHAR(20)); INSERT INTO orders_test (customer_id, order_date, amount, status) SELECT generate_series(1,1000), CURRENT_DATE - (random()*365)::INT, (random()*1000)::DECIMAL(10,2), CASE WHEN random() < 0.7 THEN 'completed' ELSE 'pending' END FROM generate_series(1,1000)",
            "sql_statement": None,
            "expected_packets": None
        },
        {
            "step_number": 2,
            "action": "无索引查询性能测试 - 查询特定customer_id的订单",
            "expected_result": "查询执行成功，记录执行时间",
            "test_data": "EXPLAIN ANALYZE SELECT * FROM orders_test WHERE customer_id = 500",
            "sql_statement": None,
            "expected_packets": None
        },
        {
            "step_number": 3,
            "action": "创建索引 - 在customer_id字段上创建索引",
            "expected_result": "索引创建成功，无错误返回",
            "test_data": "CREATE INDEX IF NOT EXISTS idx_orders_test_customer_id ON orders_test(customer_id)",
            "sql_statement": None,
            "expected_packets": None
        },
        {
            "step_number": 4,
            "action": "有索引查询性能测试 - 再次查询相同customer_id的订单",
            "expected_result": "查询执行成功，记录执行时间，应比无索引时快",
            "test_data": "EXPLAIN ANALYZE SELECT * FROM orders_test WHERE customer_id = 500",
            "sql_statement": None,
            "expected_packets": None
        },
        {
            "step_number": 5,
            "action": "清理环境 - 删除测试表和索引",
            "expected_result": "表和索引删除成功，无错误返回",
            "test_data": "DROP TABLE IF EXISTS orders_test",
            "sql_statement": None,
            "expected_packets": None
        }
    ],
    "expected_result": "索引创建后查询性能显著提升",
    "test_data": None,
    "tags": ["索引", "性能", "优化版"],
    "author": "AI生成",
    "reviewer": None,
    "review_status": "pending",
    "review_comments": None,
    "execution_count": 0,
    "success_count": 0,
    "failure_count": 0,
    "last_execution_time": None,
    "last_execution_result": None,
    "last_execution_id": None,
    "last_execution_config_id": None,
    "last_execution_capture_files": [],
    "last_execution_duration": 0,
    "estimated_time": 10,  # 减少到10分钟
    "actual_time": 0,
    "automation_level": "manual",
    "test_environment": "测试环境",
    "database_type": "gaussdb",
    "target_database_config_id": None,
    "sql_statements": None,
    "expected_packet_patterns": None,
    "packet_validation_rules": None,
    "capture_duration": 30,
    "auto_execute": False,
    "related_requirements": None,
    "related_defects": None,
    "notes": "优化版索引性能测试 - 减少数据量到1000条，避免长时间执行",
    "database_version": "openGauss 6.0",
    "created_at": "2025-08-18T12:47:44",
    "updated_at": "2025-08-18T12:47:44"
}

async def test_gaussdb_connection():
    """测试GaussDB连接"""
    try:
        # GaussDB配置
        gaussdb_config = {
            'host': '**************',
            'port': 5432,
            'user': 'gaussdb',
            'password': 'Gauss@123456',
            'database': 'postgres'
        }
        
        print("正在测试GaussDB连接...")
        gaussdb_service = GaussDBServiceV2(**gaussdb_config)
        await gaussdb_service.initialize()
        
        # 测试连接
        is_connected = await gaussdb_service.check_connection()
        if is_connected:
            print("✅ GaussDB连接成功")
            
            # 执行简单查询测试
            result = await gaussdb_service.execute_sql_query("SELECT version()")
            print(f"数据库版本: {result['data'][0] if result['data'] else 'Unknown'}")
            
            return True
        else:
            print("❌ GaussDB连接失败")
            return False
            
    except Exception as e:
        print(f"❌ GaussDB连接测试失败: {e}")
        return False
    finally:
        if 'gaussdb_service' in locals():
            await gaussdb_service.close()

async def run_optimized_test():
    """运行优化的索引性能测试"""
    try:
        # GaussDB配置
        gaussdb_config = {
            'host': '**************',
            'port': 5432,
            'user': 'gaussdb',
            'password': 'Gauss@123456',
            'database': 'postgres'
        }
        
        print("开始执行优化的索引性能测试...")
        gaussdb_service = GaussDBServiceV2(**gaussdb_config)
        await gaussdb_service.initialize()
        
        # 执行每个测试步骤
        for step in OPTIMIZED_TEST_CASE['test_steps']:
            print(f"\n步骤 {step['step_number']}: {step['action']}")
            
            try:
                sql = step['test_data']
                print(f"执行SQL: {sql[:100]}...")
                
                result = await gaussdb_service.execute_sql_query(sql)
                
                if result['type'] == 'query':
                    print(f"✅ 查询成功，返回 {result['count']} 行数据")
                    if 'EXPLAIN' in sql.upper():
                        # 显示执行计划的关键信息
                        for row in result['data']:
                            if 'QUERY PLAN' in row:
                                plan = row['QUERY PLAN']
                                if 'cost=' in plan:
                                    print(f"   执行计划: {plan}")
                elif result['type'] == 'modification':
                    affected = result.get('affected_rows', 0)
                    print(f"✅ 修改成功，影响 {affected} 行")
                else:
                    print(f"✅ 执行成功: {result.get('message', 'OK')}")
                    
            except Exception as e:
                print(f"❌ 步骤 {step['step_number']} 执行失败: {e}")
                # 继续执行下一步，不中断测试
        
        print("\n✅ 优化的索引性能测试完成")
        
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")
    finally:
        if 'gaussdb_service' in locals():
            await gaussdb_service.close()

async def main():
    """主函数"""
    print("=== 优化的索引性能测试工具 ===")
    
    # 1. 测试连接
    if not await test_gaussdb_connection():
        print("数据库连接失败，退出测试")
        return
    
    # 2. 运行优化测试
    await run_optimized_test()
    
    # 3. 保存优化的测试用例到文件
    output_file = project_root / "optimized_index_test_case.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(OPTIMIZED_TEST_CASE, f, ensure_ascii=False, indent=2)
    print(f"\n优化的测试用例已保存到: {output_file}")

if __name__ == "__main__":
    asyncio.run(main())

#!/usr/bin/env python3
"""
迁移异步任务日志记录脚本
将所有异步任务从旧的execution_logs迁移到新的async_task_execution_logs
"""

import re
import os
import sys

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def migrate_async_capture_tasks():
    """迁移async_capture_tasks.py中的所有异步任务"""
    
    file_path = "backend/tasks/async_capture_tasks.py"
    
    # 读取文件内容
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 需要迁移的任务函数列表
    task_functions = [
        "mongo_capture_task",
        "ai_mysql_capture_task", 
        "ai_postgres_capture_task",
        "ai_mongo_capture_task",
        "oracle_capture_task",
        "gaussdb_capture_task",
        "ai_oracle_capture_task",
        "ai_gaussdb_capture_task",
        "docker_build_task",
        "docker_image_build_task",
        "ai_test_case_generation_task"
    ]
    
    # 为每个任务函数添加日志记录器
    for task_func in task_functions:
        # 查找函数定义
        func_pattern = rf'async def {task_func}\([^)]+\) -> Dict\[str, Any\]:'
        func_match = re.search(func_pattern, content)
        
        if func_match:
            print(f"正在处理函数: {task_func}")
            
            # 获取任务类型
            if "mysql" in task_func:
                task_type = "ai_mysql_capture" if "ai_" in task_func else "mysql_capture"
                db_type = "mysql"
            elif "postgres" in task_func:
                task_type = "ai_postgres_capture" if "ai_" in task_func else "postgres_capture"
                db_type = "postgresql"
            elif "mongo" in task_func:
                task_type = "ai_mongo_capture" if "ai_" in task_func else "mongo_capture"
                db_type = "mongodb"
            elif "oracle" in task_func:
                task_type = "ai_oracle_capture" if "ai_" in task_func else "oracle_capture"
                db_type = "oracle"
            elif "gaussdb" in task_func:
                task_type = "ai_gaussdb_capture" if "ai_" in task_func else "gaussdb_capture"
                db_type = "gaussdb"
            elif "docker_build" in task_func:
                task_type = "docker_build"
                db_type = "mysql"  # 默认
            elif "docker_image_build" in task_func:
                task_type = "docker_image_build"
                db_type = "mysql"  # 默认
            elif "test_case_generation" in task_func:
                task_type = "ai_test_case_generation"
                db_type = "mysql"  # 默认
            else:
                task_type = task_func
                db_type = "mysql"  # 默认
            
            # 查找函数开始位置
            func_start = func_match.end()
            
            # 查找progress = TaskProgress行
            progress_pattern = r'progress = TaskProgress\(task_id, redis\)'
            progress_match = re.search(progress_pattern, content[func_start:])
            
            if progress_match:
                progress_pos = func_start + progress_match.end()
                
                # 插入任务日志记录器代码
                logger_code = f'''
    
    # 创建新的任务执行日志记录器
    from utils.task_execution_logger import create_task_logger, ExecutorSelector, ExecutorType
    task_logger = create_task_logger(
        task_id=task_id,
        task_type="{task_type}",
        task_name=f"{task_type.replace('_', ' ').title()}任务-配置{{config_id if 'config_id' in locals() else 'unknown'}}"
    )'''
                
                content = content[:progress_pos] + logger_code + content[progress_pos:]
                
                print(f"已为 {task_func} 添加任务日志记录器")
    
    # 保存修改后的文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("async_capture_tasks.py 迁移完成")

def remove_old_execution_logger_imports():
    """移除旧的ExecutionLogger导入"""
    
    file_path = "backend/tasks/async_capture_tasks.py"
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 移除旧的导入
    old_imports = [
        "from models.execution_log import LogCategory",
        "from utils.execution_logger import ExecutionLogger"
    ]
    
    for old_import in old_imports:
        content = content.replace(old_import, "")
    
    # 移除空行
    content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("已移除旧的ExecutionLogger导入")

def add_task_logger_calls():
    """为所有任务函数添加日志记录调用"""
    
    file_path = "backend/tasks/async_capture_tasks.py"
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 替换progress.update_progress调用，同时添加task_logger.update_progress
    progress_pattern = r'await progress\.update_progress\((\d+), "([^"]+)"\)'
    
    def replace_progress(match):
        progress_num = match.group(1)
        message = match.group(2)
        return f'''await progress.update_progress({progress_num}, "{message}")
        await task_logger.update_progress({progress_num}, "{message}")'''
    
    content = re.sub(progress_pattern, replace_progress, content)
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("已添加task_logger进度更新调用")

def main():
    """主函数"""
    print("开始迁移异步任务日志记录...")
    
    try:
        # 1. 移除旧的导入
        remove_old_execution_logger_imports()
        
        # 2. 迁移异步任务函数
        migrate_async_capture_tasks()
        
        # 3. 添加日志记录调用
        add_task_logger_calls()
        
        print("异步任务日志记录迁移完成！")
        print("\n注意事项：")
        print("1. 请手动检查每个任务函数的初始化部分")
        print("2. 确保在try块开始时调用task_logger.initialize()")
        print("3. 确保在适当位置调用task_logger.start_execution()")
        print("4. 确保在成功完成时调用task_logger.complete_execution()")
        print("5. 确保在异常处理时调用task_logger.fail_execution()")
        
    except Exception as e:
        print(f"迁移过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

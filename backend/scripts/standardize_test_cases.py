#!/usr/bin/env python3
"""
测试用例格式标准化脚本
将所有测试用例的SQL语句统一存储到test_data字段中，清理action字段中的SQL语句
"""

import sys
import os
import json
import re
import mysql.connector
from typing import List, Dict, Any
import logging

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.config import Config

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TestCaseStandardizer:
    """测试用例格式标准化工具"""
    
    def __init__(self):
        """初始化数据库连接"""
        self.conn = mysql.connector.connect(
            host=Config.MYSQL_HOST,
            port=Config.MYSQL_PORT,
            user=Config.MYSQL_USER,
            password=Config.MYSQL_PASSWORD,
            database=Config.MYSQL_DATABASE
        )
        self.cursor = self.conn.cursor()
        
    def standardize_all_test_cases(self):
        """标准化所有测试用例"""
        logger.info("开始标准化所有测试用例格式...")
        
        # 查询所有测试用例
        self.cursor.execute("SELECT id, title, test_steps FROM test_cases_management")
        test_cases = self.cursor.fetchall()
        
        total_cases = len(test_cases)
        updated_cases = 0
        
        logger.info(f"找到 {total_cases} 个测试用例需要处理")
        
        for case_id, title, test_steps_json in test_cases:
            try:
                if not test_steps_json:
                    continue
                    
                # 解析测试步骤
                test_steps = json.loads(test_steps_json)
                
                # 标准化测试步骤
                updated = False
                for step in test_steps:
                    if isinstance(step, dict):
                        if self._standardize_test_step(step):
                            updated = True
                
                # 如果有更新，保存到数据库
                if updated:
                    updated_json = json.dumps(test_steps, ensure_ascii=False)
                    self.cursor.execute(
                        "UPDATE test_cases_management SET test_steps = %s WHERE id = %s",
                        (updated_json, case_id)
                    )
                    updated_cases += 1
                    logger.info(f"已更新测试用例: {title} (ID: {case_id})")
                    
            except Exception as e:
                logger.error(f"处理测试用例失败 {title} (ID: {case_id}): {e}")
                continue
        
        # 提交更改
        self.conn.commit()
        logger.info(f"标准化完成！共处理 {total_cases} 个测试用例，更新了 {updated_cases} 个")
        
    def _standardize_test_step(self, step: Dict[str, Any]) -> bool:
        """标准化单个测试步骤，返回是否有更新"""
        action = step.get('action', '')
        test_data = step.get('test_data', '')
        
        # 检查action中是否包含SQL语句
        sql_in_action = self._extract_sql_from_action(action)
        
        if not sql_in_action:
            return False  # 没有需要移动的SQL
            
        updated = False
        
        # 如果action中包含SQL，需要重新分配
        if not test_data or not self._contains_sql(test_data):
            # test_data为空或不包含SQL，将SQL从action移到test_data
            step['test_data'] = '; '.join(sql_in_action)
            step['action'] = self._clean_action_from_sql(action, sql_in_action)
            logger.debug(f"已将SQL从action移动到test_data: {step['action']}")
            updated = True
        else:
            # test_data已有内容，检查是否需要合并SQL
            existing_sql = self._extract_sql_from_text(test_data)
            new_sql = []
            
            for sql in sql_in_action:
                if not any(self._normalize_sql(sql) == self._normalize_sql(existing) 
                          for existing in existing_sql):
                    new_sql.append(sql)
            
            if new_sql:
                # 合并新的SQL到test_data
                combined_sql = existing_sql + new_sql
                step['test_data'] = '; '.join(combined_sql)
                updated = True
            
            # 清理action中的SQL
            step['action'] = self._clean_action_from_sql(action, sql_in_action)
            updated = True
        
        return updated
    
    def _extract_sql_from_action(self, action: str) -> List[str]:
        """从action字段中提取SQL语句"""
        if not action:
            return []
        
        sql_statements = []
        
        # SQL关键词模式 - 更精确的匹配
        sql_patterns = [
            r'(CREATE\s+TABLE\s+[^;]+(?:;|$))',
            r'(INSERT\s+INTO\s+[^;]+(?:;|$))',
            r'(SELECT\s+[^;]+(?:FROM\s+[^;]+)?(?:;|$))',
            r'(UPDATE\s+[^;]+(?:SET\s+[^;]+)?(?:;|$))',
            r'(DELETE\s+FROM\s+[^;]+(?:;|$))',
            r'(DROP\s+TABLE\s+[^;]+(?:;|$))',
            r'(ALTER\s+TABLE\s+[^;]+(?:;|$))',
            r'(SHOW\s+[^;]+(?:;|$))',
            r'(DESCRIBE\s+[^;]+(?:;|$))',
            r'(EXPLAIN\s+[^;]+(?:;|$))',
        ]
        
        for pattern in sql_patterns:
            matches = re.findall(pattern, action, re.IGNORECASE | re.DOTALL)
            for match in matches:
                cleaned_sql = match.strip().rstrip(';')
                if cleaned_sql and len(cleaned_sql) > 10:  # 过滤太短的匹配
                    sql_statements.append(cleaned_sql)
        
        return sql_statements
    
    def _extract_sql_from_text(self, text: str) -> List[str]:
        """从文本中提取SQL语句"""
        if not text:
            return []
        
        # 按分号分割
        statements = []
        parts = text.split(';')
        
        for part in parts:
            part = part.strip()
            if part and self._is_valid_sql(part):
                statements.append(part)
        
        return statements
    
    def _contains_sql(self, text: str) -> bool:
        """检查文本是否包含SQL语句"""
        if not text:
            return False
        
        text_upper = text.upper().strip()
        sql_keywords = ['SELECT', 'INSERT', 'UPDATE', 'DELETE', 'CREATE', 'DROP', 'ALTER', 'SHOW', 'DESCRIBE']
        
        return any(keyword in text_upper for keyword in sql_keywords)
    
    def _is_valid_sql(self, text: str) -> bool:
        """检查是否是有效的SQL语句"""
        if not text or len(text.strip()) < 5:
            return False
        
        text_upper = text.upper().strip()
        sql_keywords = ['SELECT', 'INSERT', 'UPDATE', 'DELETE', 'CREATE', 'DROP', 'ALTER', 'SHOW', 'DESCRIBE']
        
        return any(text_upper.startswith(keyword) for keyword in sql_keywords)
    
    def _normalize_sql(self, sql: str) -> str:
        """标准化SQL用于比较"""
        return ' '.join(sql.strip().upper().split())
    
    def _clean_action_from_sql(self, action: str, sql_statements: List[str]) -> str:
        """从action中清理SQL语句，保留操作描述"""
        cleaned_action = action
        
        # 移除SQL语句
        for sql in sql_statements:
            # 尝试多种移除方式
            patterns_to_remove = [
                sql,
                sql + ';',
                ' - ' + sql,
                ' - ' + sql + ';',
            ]
            
            for pattern in patterns_to_remove:
                cleaned_action = cleaned_action.replace(pattern, '')
        
        # 清理多余的空格和符号
        cleaned_action = re.sub(r'\s+', ' ', cleaned_action)
        cleaned_action = re.sub(r'\s*-\s*$', '', cleaned_action)  # 移除末尾的 " - "
        cleaned_action = re.sub(r'^\s*-\s*', '', cleaned_action)  # 移除开头的 " - "
        cleaned_action = cleaned_action.strip()
        
        # 如果清理后为空或太短，生成默认描述
        if not cleaned_action or len(cleaned_action) < 3:
            if sql_statements:
                first_sql = sql_statements[0].upper()
                if 'CREATE' in first_sql:
                    cleaned_action = "创建数据库对象"
                elif 'INSERT' in first_sql:
                    cleaned_action = "插入测试数据"
                elif 'SELECT' in first_sql:
                    cleaned_action = "执行查询操作"
                elif 'UPDATE' in first_sql:
                    cleaned_action = "更新数据"
                elif 'DELETE' in first_sql:
                    cleaned_action = "删除数据"
                elif 'DROP' in first_sql:
                    cleaned_action = "删除数据库对象"
                else:
                    cleaned_action = "执行数据库操作"
        
        return cleaned_action
    
    def close(self):
        """关闭数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.conn:
            self.conn.close()

def main():
    """主函数"""
    standardizer = TestCaseStandardizer()
    
    try:
        standardizer.standardize_all_test_cases()
        logger.info("✅ 测试用例格式标准化完成！")
    except Exception as e:
        logger.error(f"标准化过程中出现错误: {e}")
        logger.error("❌ 标准化失败，请查看日志")
    finally:
        standardizer.close()

if __name__ == "__main__":
    main()

-- 为database_configs表添加database_type字段
-- 用于支持PostgreSQL和MongoDB数据库类型

-- 检查database_type列是否存在，如果不存在则添加
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'database_configs'
    AND COLUMN_NAME = 'database_type'
);

-- 如果列不存在，则添加
SET @sql = IF(@column_exists = 0,
    'ALTER TABLE database_configs ADD COLUMN database_type VARCHAR(20) NOT NULL DEFAULT "mysql" AFTER database_name',
    'SELECT "Column database_type already exists" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 更新现有记录的database_type字段
UPDATE database_configs 
SET database_type = 'mysql' 
WHERE database_type IS NULL OR database_type = '';

-- 添加一些示例PostgreSQL配置（可选）
INSERT IGNORE INTO database_configs 
(name, host, port, user, password, database_name, database_type, description, is_default, is_active)
VALUES 
('PostgreSQL示例', '**************', 5432, 'postgres', 'postgres', 'postgres', 'postgresql', 'PostgreSQL数据库示例配置', FALSE, TRUE);

-- 显示表结构确认
DESCRIBE database_configs;

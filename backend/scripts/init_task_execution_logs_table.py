"""
初始化异步任务执行记录表
"""

import asyncio
import logging
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.mysql_service import MySQLService
from utils.config import Config

logger = logging.getLogger(__name__)

# 异步任务执行记录表创建SQL
CREATE_ASYNC_TASK_EXECUTION_LOGS_TABLE = """
CREATE TABLE IF NOT EXISTS async_task_execution_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    task_id VARCHAR(36) NOT NULL COMMENT '任务ID（UUID）',
    task_type ENUM(
        'mysql_capture', 'postgres_capture', 'mongo_capture', 'oracle_capture', 'gaussdb_capture',
        'ai_mysql_capture', 'ai_postgres_capture', 'ai_mongo_capture', 'ai_oracle_capture', 'ai_gaussdb_capture',
        'docker_build', 'docker_image_build', 'ai_test_case_generation',
        'batch_test_case_execution', 'single_test_case_execution'
    ) NOT NULL COMMENT '任务类型',
    task_name VARCHAR(255) COMMENT '任务名称',
    task_description TEXT COMMENT '任务描述',
    
    -- 任务状态信息
    status ENUM('PENDING', 'RUNNING', 'COMPLETED', 'FAILED', 'CANCELLED', 'TIMEOUT') NOT NULL DEFAULT 'PENDING' COMMENT '任务状态',
    progress INT DEFAULT 0 COMMENT '执行进度(0-100)',
    current_step VARCHAR(255) COMMENT '当前执行步骤',
    
    -- 执行器信息
    executor_type ENUM('PYTHON', 'C', 'JAVA') NOT NULL DEFAULT 'PYTHON' COMMENT '执行器类型',
    executor_version VARCHAR(50) COMMENT '执行器版本',
    executor_path VARCHAR(500) COMMENT '执行器路径',
    executor_selection_reason TEXT COMMENT '执行器选择原因',
    fallback_from_executor ENUM('PYTHON', 'C', 'JAVA') COMMENT '从哪个执行器回退而来',
    
    -- 配置信息
    database_config_id INT COMMENT '数据库配置ID',
    server_config_id INT COMMENT '服务器配置ID',
    capture_enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用抓包',
    capture_duration INT COMMENT '抓包持续时间(秒)',
    
    -- 输入参数
    input_sql_query TEXT COMMENT '输入SQL查询',
    input_mongo_query TEXT COMMENT '输入MongoDB查询',
    input_natural_query TEXT COMMENT '输入自然语言查询',
    input_parameters JSON COMMENT '其他输入参数',
    
    -- 执行结果
    execution_result JSON COMMENT '执行结果详情',
    output_files JSON COMMENT '输出文件列表',
    capture_files JSON COMMENT '抓包文件列表',
    generated_sql TEXT COMMENT 'AI生成的SQL语句',
    
    -- 性能指标
    start_time TIMESTAMP NULL COMMENT '开始时间',
    end_time TIMESTAMP NULL COMMENT '结束时间',
    duration_seconds DECIMAL(10,3) COMMENT '执行时长(秒)',
    cpu_usage_percent DECIMAL(5,2) COMMENT 'CPU使用率',
    memory_usage_mb DECIMAL(10,2) COMMENT '内存使用量(MB)',
    
    -- 错误信息
    error_code VARCHAR(50) COMMENT '错误代码',
    error_message TEXT COMMENT '错误消息',
    error_details JSON COMMENT '详细错误信息',
    error_stack_trace TEXT COMMENT '错误堆栈跟踪',
    retry_count INT DEFAULT 0 COMMENT '重试次数',
    max_retries INT DEFAULT 0 COMMENT '最大重试次数',
    
    -- 环境信息
    worker_id VARCHAR(100) COMMENT '工作器ID',
    worker_host VARCHAR(255) COMMENT '工作器主机',
    python_version VARCHAR(50) COMMENT 'Python版本',
    system_info JSON COMMENT '系统信息',
    environment_variables JSON COMMENT '环境变量',
    
    -- 依赖任务
    parent_task_id VARCHAR(36) COMMENT '父任务ID',
    child_task_ids JSON COMMENT '子任务ID列表',
    dependency_task_ids JSON COMMENT '依赖任务ID列表',
    
    -- 批量执行相关
    batch_id VARCHAR(36) COMMENT '批量执行ID',
    batch_name VARCHAR(255) COMMENT '批量执行名称',
    batch_total_count INT COMMENT '批量总数',
    batch_current_index INT COMMENT '批量当前索引',
    
    -- 测试用例相关
    test_case_id VARCHAR(36) COMMENT '测试用例ID',
    test_case_name VARCHAR(255) COMMENT '测试用例名称',
    test_steps_executed JSON COMMENT '已执行的测试步骤',
    test_validation_results JSON COMMENT '测试验证结果',
    
    -- 抓包相关
    network_interface VARCHAR(100) COMMENT '网络接口',
    packet_count INT COMMENT '抓包数量',
    packet_size_bytes BIGINT COMMENT '抓包总大小(字节)',
    capture_filter VARCHAR(500) COMMENT '抓包过滤器',
    
    -- 审计信息
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(100) COMMENT '创建者',
    
    -- 外键约束
    FOREIGN KEY (database_config_id) REFERENCES database_configs(id) ON DELETE SET NULL,
    FOREIGN KEY (parent_task_id) REFERENCES async_task_execution_logs(task_id) ON DELETE SET NULL,
    
    -- 索引
    UNIQUE KEY uk_task_id (task_id),
    INDEX idx_task_type (task_type),
    INDEX idx_status (status),
    INDEX idx_executor_type (executor_type),
    INDEX idx_database_config_id (database_config_id),
    INDEX idx_server_config_id (server_config_id),
    INDEX idx_batch_id (batch_id),
    INDEX idx_test_case_id (test_case_id),
    INDEX idx_parent_task_id (parent_task_id),
    INDEX idx_start_time (start_time),
    INDEX idx_end_time (end_time),
    INDEX idx_duration (duration_seconds),
    INDEX idx_created_at (created_at),
    INDEX idx_status_type (status, task_type),
    INDEX idx_executor_status (executor_type, status),
    INDEX idx_batch_status (batch_id, status),
    INDEX idx_error_code (error_code),
    INDEX idx_retry_count (retry_count)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='异步任务执行记录表';
"""

# 插入示例数据的SQL
INSERT_SAMPLE_DATA = """
INSERT INTO async_task_execution_logs (
    task_id, task_type, task_name, task_description, status, progress,
    executor_type, executor_selection_reason, database_config_id,
    input_sql_query, start_time, end_time, duration_seconds,
    execution_result, created_by
) VALUES 
(
    UUID(), 'mysql_capture', 'MySQL抓包测试任务', 
    '测试MySQL数据库抓包功能', 'COMPLETED', 100,
    'PYTHON', '系统推荐使用Python执行器', 1,
    'SELECT * FROM users LIMIT 10', 
    DATE_SUB(NOW(), INTERVAL 5 MINUTE), DATE_SUB(NOW(), INTERVAL 3 MINUTE), 120.5,
    '{"success": true, "rows": 10, "capture_file": "/tmp/test.pcap"}', 'system'
),
(
    UUID(), 'ai_postgres_capture', 'AI+PostgreSQL抓包任务',
    '使用AI解析自然语言并执行PostgreSQL抓包', 'FAILED', 45,
    'C', 'C执行器性能更优', 2,
    'SELECT COUNT(*) FROM products WHERE price > 100',
    DATE_SUB(NOW(), INTERVAL 10 MINUTE), DATE_SUB(NOW(), INTERVAL 8 MINUTE), 85.2,
    NULL, 'system'
),
(
    UUID(), 'batch_test_case_execution', '批量测试用例执行',
    '执行用户模块相关的测试用例', 'RUNNING', 60,
    'PYTHON', 'C执行器不可用，回退到Python', NULL,
    NULL,
    DATE_SUB(NOW(), INTERVAL 2 MINUTE), NULL, NULL,
    NULL, 'admin'
);
"""


async def create_table():
    """创建异步任务执行记录表"""
    try:
        mysql_service = MySQLService(**Config.get_mysql_config())
        
        logger.info("开始创建异步任务执行记录表...")
        
        # 创建表
        result = await mysql_service.execute_query(CREATE_ASYNC_TASK_EXECUTION_LOGS_TABLE)
        
        if result.get('success', False):
            logger.info("异步任务执行记录表创建成功")
        else:
            logger.error(f"异步任务执行记录表创建失败: {result}")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"创建异步任务执行记录表失败: {e}")
        return False


async def insert_sample_data():
    """插入示例数据"""
    try:
        mysql_service = MySQLService(**Config.get_mysql_config())
        
        logger.info("开始插入示例数据...")
        
        # 插入示例数据
        result = await mysql_service.execute_query(INSERT_SAMPLE_DATA)
        
        if result.get('success', False):
            logger.info("示例数据插入成功")
        else:
            logger.error(f"示例数据插入失败: {result}")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"插入示例数据失败: {e}")
        return False


async def check_table_exists():
    """检查表是否存在"""
    try:
        mysql_service = MySQLService(**Config.get_mysql_config())
        
        check_sql = """
            SELECT COUNT(*) as count 
            FROM information_schema.tables 
            WHERE table_schema = %s AND table_name = 'async_task_execution_logs'
        """
        
        result = await mysql_service.execute_query(check_sql, (Config.MYSQL_DATABASE,))
        
        if result and result.get('data'):
            count = result['data'][0]['count']
            return count > 0
        
        return False
        
    except Exception as e:
        logger.error(f"检查表存在性失败: {e}")
        return False


async def get_table_info():
    """获取表信息"""
    try:
        mysql_service = MySQLService(**Config.get_mysql_config())
        
        # 获取表结构
        describe_sql = "DESCRIBE async_task_execution_logs"
        structure_result = await mysql_service.execute_query(describe_sql)
        
        # 获取表统计信息
        stats_sql = """
            SELECT 
                COUNT(*) as total_records,
                COUNT(DISTINCT task_type) as task_types,
                COUNT(DISTINCT executor_type) as executor_types,
                COUNT(CASE WHEN status = 'COMPLETED' THEN 1 END) as completed_tasks,
                COUNT(CASE WHEN status = 'FAILED' THEN 1 END) as failed_tasks,
                COUNT(CASE WHEN status = 'RUNNING' THEN 1 END) as running_tasks
            FROM async_task_execution_logs
        """
        stats_result = await mysql_service.execute_query(stats_sql)
        
        return {
            "table_structure": structure_result.get('data', []) if structure_result else [],
            "table_statistics": stats_result.get('data', [{}])[0] if stats_result else {}
        }
        
    except Exception as e:
        logger.error(f"获取表信息失败: {e}")
        return None


async def main():
    """主函数"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    try:
        # 检查表是否已存在
        table_exists = await check_table_exists()
        
        if table_exists:
            logger.info("异步任务执行记录表已存在")
        else:
            # 创建表
            success = await create_table()
            if not success:
                logger.error("表创建失败，退出")
                return
            
            # 插入示例数据
            await insert_sample_data()
        
        # 获取表信息
        table_info = await get_table_info()
        if table_info:
            logger.info("表信息:")
            logger.info(f"字段数量: {len(table_info['table_structure'])}")
            logger.info(f"统计信息: {table_info['table_statistics']}")
        
        logger.info("异步任务执行记录表初始化完成")
        
    except Exception as e:
        logger.error(f"初始化失败: {e}")


if __name__ == "__main__":
    asyncio.run(main())

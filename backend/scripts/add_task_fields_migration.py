#!/usr/bin/env python3
"""
数据库迁移脚本：为tasks表添加测试用例相关字段
"""

import asyncio
import logging
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.mysql_service import MySQLService
from utils.config import Config

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 添加字段的SQL语句
ADD_FIELDS_SQL = [
    # 添加批量测试用例执行任务字段
    "ALTER TABLE tasks ADD COLUMN batch_name VARCHAR(255) COMMENT '批量任务名称'",
    "ALTER TABLE tasks ADD COLUMN database_type VARCHAR(50) COMMENT '数据库类型'",
    "ALTER TABLE tasks ADD COLUMN database_version VARCHAR(100) COMMENT '数据库版本'",
    "ALTER TABLE tasks ADD COLUMN total_test_cases INT COMMENT '总测试用例数'",
    "ALTER TABLE tasks ADD COLUMN capture_enabled BOOLEAN COMMENT '是否启用抓包'",
    "ALTER TABLE tasks ADD COLUMN timeout_per_case INT COMMENT '每个用例超时时间'",
    "ALTER TABLE tasks ADD COLUMN stop_on_failure BOOLEAN COMMENT '失败时是否停止'",
    "ALTER TABLE tasks ADD COLUMN description TEXT COMMENT '任务描述'",

    # 添加单个测试用例执行任务字段
    "ALTER TABLE tasks ADD COLUMN test_case_id VARCHAR(36) COMMENT '测试用例ID'",
    "ALTER TABLE tasks ADD COLUMN test_case_title VARCHAR(255) COMMENT '测试用例标题'",
    "ALTER TABLE tasks ADD COLUMN test_case_json LONGTEXT COMMENT '测试用例JSON内容'"
]

async def get_existing_columns(mysql_service):
    """获取tasks表现有的字段名"""
    try:
        sql = "DESCRIBE tasks"
        result = await mysql_service.execute_query(sql)
        return {row['Field'] for row in result}
    except Exception as e:
        logger.error(f"获取表结构失败: {e}")
        return set()

async def migrate_database():
    """执行数据库迁移"""
    mysql_service = None
    try:
        # 初始化MySQL服务
        mysql_config = Config.get_mysql_config()
        mysql_service = MySQLService(**mysql_config)
        await mysql_service.initialize()
        
        logger.info("开始执行数据库迁移...")
        
        # 先检查哪些字段已存在
        existing_columns = await get_existing_columns(mysql_service)

        # 执行每个SQL语句
        for i, sql in enumerate(ADD_FIELDS_SQL, 1):
            try:
                # 提取字段名
                column_name = sql.split("ADD COLUMN ")[1].split(" ")[0]

                if column_name in existing_columns:
                    logger.info(f"字段 {column_name} 已存在，跳过")
                    continue

                logger.info(f"执行迁移步骤 {i}/{len(ADD_FIELDS_SQL)}: 添加字段 {column_name}...")
                await mysql_service.execute_query(sql)
                logger.info(f"迁移步骤 {i} 完成")
            except Exception as e:
                logger.error(f"迁移步骤 {i} 失败: {e}")
                # 继续执行其他步骤
                continue
        
        logger.info("数据库迁移完成！")
        
    except Exception as e:
        logger.error(f"数据库迁移失败: {e}")
        raise
    finally:
        if mysql_service:
            await mysql_service.close()

async def check_fields():
    """检查字段是否已存在"""
    mysql_service = None
    try:
        # 初始化MySQL服务
        mysql_config = Config.get_mysql_config()
        mysql_service = MySQLService(**mysql_config)
        await mysql_service.initialize()
        
        # 查询表结构
        sql = "DESCRIBE tasks"
        result = await mysql_service.execute_query(sql)
        
        logger.info("当前tasks表结构:")
        for row in result:
            logger.info(f"  {row['Field']} - {row['Type']} - {row.get('Comment', '')}")
            
    except Exception as e:
        logger.error(f"检查表结构失败: {e}")
    finally:
        if mysql_service:
            await mysql_service.close()

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "check":
        # 检查表结构
        asyncio.run(check_fields())
    else:
        # 执行迁移
        asyncio.run(migrate_database())

#!/usr/bin/env python3
"""
数据库初始化脚本
创建所有必要的表结构
"""

import asyncio
import logging
import pymysql
import sys
import os
from dotenv import load_dotenv

# 加载环境变量
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
env_path = os.path.join(project_root, '.env')
load_dotenv(env_path)

# 强制设置正确的配置
os.environ['MYSQL_HOST'] = '************'
os.environ['MYSQL_PORT'] = '3306'
os.environ['MYSQL_USER'] = 'root'
os.environ['MYSQL_PASSWORD'] = '123456'
os.environ['MYSQL_DATABASE'] = 'ai_sql_pcap'

# 添加父目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.config import Config

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 数据库表创建SQL
CREATE_TABLES_SQL = {
    "database_configs": """
        CREATE TABLE IF NOT EXISTS database_configs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL COMMENT '配置名称',
            host VARCHAR(255) NOT NULL COMMENT '主机地址',
            port INT NOT NULL COMMENT '端口',
            user VARCHAR(100) NOT NULL COMMENT '用户名',
            password VARCHAR(255) NOT NULL COMMENT '密码',
            database_name VARCHAR(100) COMMENT '数据库名',
            database_type ENUM('mysql', 'postgresql', 'mongodb') DEFAULT 'mysql' COMMENT '数据库类型',
            description TEXT COMMENT '描述',
            is_default BOOLEAN DEFAULT FALSE COMMENT '是否默认配置',
            is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            UNIQUE KEY unique_name (name),
            INDEX idx_type (database_type),
            INDEX idx_active (is_active)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据库配置表';
    """,
    
    "server_configs": """
        CREATE TABLE IF NOT EXISTS server_configs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL COMMENT '服务器名称',
            host VARCHAR(255) NOT NULL COMMENT '服务器地址',
            port INT DEFAULT 22 COMMENT 'SSH端口',
            username VARCHAR(100) NOT NULL COMMENT '用户名',
            password VARCHAR(255) COMMENT '密码',
            private_key TEXT COMMENT '私钥',
            description TEXT COMMENT '描述',
            is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
            is_default BOOLEAN DEFAULT FALSE COMMENT '是否默认配置',
            last_connected_at TIMESTAMP NULL COMMENT '最后连接时间',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            UNIQUE KEY unique_name (name),
            INDEX idx_host (host),
            INDEX idx_active (is_active)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='服务器配置表';
    """,
    
    "capture_sessions": """
        CREATE TABLE IF NOT EXISTS capture_sessions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            session_id VARCHAR(100) NOT NULL COMMENT '会话ID',
            database_config_id INT COMMENT '数据库配置ID',
            server_config_id INT COMMENT '服务器配置ID',
            capture_type ENUM('mysql', 'postgresql', 'mongodb') NOT NULL COMMENT '抓包类型',
            sql_query TEXT COMMENT 'SQL查询语句',
            mongo_query TEXT COMMENT 'MongoDB查询语句',
            capture_file VARCHAR(500) COMMENT '抓包文件路径',
            status ENUM('pending', 'running', 'completed', 'failed', 'cancelled') DEFAULT 'pending' COMMENT '状态',
            progress INT DEFAULT 0 COMMENT '进度百分比',
            error_message TEXT COMMENT '错误信息',
            result_data JSON COMMENT '结果数据',
            capture_duration INT DEFAULT 30 COMMENT '抓包持续时间（秒）',
            started_at TIMESTAMP NULL COMMENT '开始时间',
            completed_at TIMESTAMP NULL COMMENT '完成时间',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            UNIQUE KEY unique_session (session_id),
            INDEX idx_type (capture_type),
            INDEX idx_status (status),
            INDEX idx_created (created_at),
            FOREIGN KEY (database_config_id) REFERENCES database_configs(id) ON DELETE SET NULL,
            FOREIGN KEY (server_config_id) REFERENCES server_configs(id) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='抓包会话表';
    """,
    


    "server_docker_images": """
        CREATE TABLE IF NOT EXISTS server_docker_images (
            id INT AUTO_INCREMENT PRIMARY KEY,
            server_config_id INT NOT NULL COMMENT '服务器配置ID',
            image_id VARCHAR(255) NOT NULL COMMENT 'Docker镜像ID',
            repository VARCHAR(255) NOT NULL COMMENT '镜像仓库名',
            tag VARCHAR(100) NOT NULL DEFAULT 'latest' COMMENT '镜像标签',
            size BIGINT DEFAULT 0 COMMENT '镜像大小(字节)',
            created_time TIMESTAMP NULL COMMENT '镜像创建时间',
            last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
            is_available BOOLEAN DEFAULT TRUE COMMENT '镜像是否可用',
            image_info JSON COMMENT '镜像详细信息',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
            FOREIGN KEY (server_config_id) REFERENCES server_configs(id) ON DELETE CASCADE,
            UNIQUE KEY uk_server_image (server_config_id, image_id),
            INDEX idx_server_config_id (server_config_id),
            INDEX idx_repository (repository),
            INDEX idx_tag (tag),
            INDEX idx_is_available (is_available),
            INDEX idx_created_time (created_time)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='服务器Docker镜像表';
    """,


}

# 初始数据
INITIAL_DATA = {
    "database_configs": [
        {
            "name": "本地MySQL",
            "host": "************",
            "port": 3306,
            "user": "root",
            "password": "123456",
            "database_name": "ai_sql_pcap",
            "database_type": "mysql",
            "description": "本地MySQL数据库配置",
            "is_default": 1
        },
        {
            "name": "测试PostgreSQL",
            "host": "**************",
            "port": 5432,
            "user": "postgres",
            "password": "postgres",
            "database_name": "postgres",
            "database_type": "postgresql",
            "description": "测试PostgreSQL数据库配置",
            "is_default": 0
        },
        {
            "name": "测试MongoDB",
            "host": "**************",
            "port": 27017,
            "user": "admin",
            "password": "admin",
            "database_name": "ai-mongo-pcap",
            "database_type": "mongodb",
            "description": "测试MongoDB数据库配置",
            "is_default": 0
        }
    ],
    
    "server_configs": [
        {
            "name": "主服务器",
            "host": "**************",
            "port": 22,
            "username": "root",
            "password": "QZ@1005#1005",
            "description": "主要的远程服务器"
        },
        {
            "name": "测试服务器",
            "host": "*************",
            "port": 22,
            "username": "root",
            "password": "QZ@1005#1005",
            "description": "测试环境服务器"
        }
    ]
}

async def create_database_if_not_exists():
    """创建数据库（如果不存在）"""
    try:
        # 连接到MySQL服务器（不指定数据库）
        connection = pymysql.connect(
            host=Config.MYSQL_HOST,
            port=Config.MYSQL_PORT,
            user=Config.MYSQL_USER,
            password=Config.MYSQL_PASSWORD,
            charset='utf8mb4'
        )
        
        with connection.cursor() as cursor:
            # 创建数据库
            logger.info(f"正在创建数据库: {Config.MYSQL_DATABASE}")
            cursor.execute(f"CREATE DATABASE IF NOT EXISTS {Config.MYSQL_DATABASE} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            logger.info(f"数据库 {Config.MYSQL_DATABASE} 创建成功或已存在")
        
        connection.close()
        
    except Exception as e:
        logger.error(f"创建数据库失败: {e}")
        raise

async def create_tables():
    """创建所有表"""
    try:
        # 连接到指定数据库
        logger.info(f"连接到数据库: {Config.MYSQL_HOST}:{Config.MYSQL_PORT}/{Config.MYSQL_DATABASE}")
        connection = pymysql.connect(
            host=Config.MYSQL_HOST,
            port=Config.MYSQL_PORT,
            user=Config.MYSQL_USER,
            password=Config.MYSQL_PASSWORD,
            database=Config.MYSQL_DATABASE,
            charset='utf8mb4'
        )
        
        with connection.cursor() as cursor:
            for table_name, sql in CREATE_TABLES_SQL.items():
                logger.info(f"创建表: {table_name}")
                cursor.execute(sql)
                logger.info(f"✅ 表 {table_name} 创建成功")
        
        connection.commit()
        connection.close()
        
    except Exception as e:
        logger.error(f"创建表失败: {e}")
        raise

async def insert_initial_data():
    """插入初始数据"""
    try:
        connection = pymysql.connect(
            host=Config.MYSQL_HOST,
            port=Config.MYSQL_PORT,
            user=Config.MYSQL_USER,
            password=Config.MYSQL_PASSWORD,
            database=Config.MYSQL_DATABASE,
            charset='utf8mb4'
        )
        
        with connection.cursor() as cursor:
            # 插入数据库配置
            for config in INITIAL_DATA["database_configs"]:
                sql = """
                    INSERT IGNORE INTO database_configs 
                    (name, host, port, user, password, database_name, database_type, description, is_default)
                    VALUES (%(name)s, %(host)s, %(port)s, %(user)s, %(password)s, %(database_name)s, %(database_type)s, %(description)s, %(is_default)s)
                """
                cursor.execute(sql, config)
                logger.info(f"✅ 插入数据库配置: {config['name']}")
            
            # 插入服务器配置
            for config in INITIAL_DATA["server_configs"]:
                sql = """
                    INSERT IGNORE INTO server_configs 
                    (name, host, port, username, password, description)
                    VALUES (%(name)s, %(host)s, %(port)s, %(username)s, %(password)s, %(description)s)
                """
                cursor.execute(sql, config)
                logger.info(f"✅ 插入服务器配置: {config['name']}")
        
        connection.commit()
        connection.close()
        
    except Exception as e:
        logger.error(f"插入初始数据失败: {e}")
        raise

async def main():
    """主函数"""
    try:
        logger.info("🚀 开始初始化数据库...")
        logger.info(f"配置信息: {Config.MYSQL_HOST}:{Config.MYSQL_PORT}/{Config.MYSQL_DATABASE}")
        
        # 1. 创建数据库
        await create_database_if_not_exists()
        
        # 2. 创建表
        await create_tables()
        
        # 3. 插入初始数据
        await insert_initial_data()
        
        logger.info("🎉 数据库初始化完成！")
        
    except Exception as e:
        logger.error(f"❌ 数据库初始化失败: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())

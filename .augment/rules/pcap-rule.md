---
type: "always_apply"
---

用中文回答
不要使用any网口抓包，要使用固定网口，可以用AI识别正确的网口后保存
当你需要启动后端，先检查8000端口是否被占用，如果占用，先kill掉，再运行后端
抓包顺序是：开始抓包→连接数据库→执行查询（包含多语句查询）→断开数据库连接→停止抓包
当你需要连接服务器，可以参考ssh -i ~/.ssh/id_rsa_192_168_70_220_root root@192.168.70.220连接，如果换服务器，你记得换秘钥，如果失败则进行互信
当你需要连接redis查看任务，用192.168.0.41:6379的redis，别使用本地的redis，后端mysql数据库用192.168.0.41:3306
默认服务已经启动，如果没有启动，启动后端使用source venv/bin/activate,然后启动python3 start_with_worker.py启动
所有的容器运行都不在本地，可进入数据库管理中查看对应数据库的服务器
如有的捕获异常except，不允许直接写pass，所有捕获或者异常操作，必须打印到日志文件，不要使用print语句打印日志
你在测试完成后，删除相关测试脚本
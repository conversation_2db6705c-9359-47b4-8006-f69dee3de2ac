#!/bin/bash

# 生产环境部署脚本 - 解决代码重置和依赖问题

# 配置信息
SERVER_IP="************"
SERVER_USER="root"
SERVER_PASSWORD="root@123"
SERVER_PORT="22"
REMOTE_DIR="/opt/ai_sql_pcap"
LOCAL_DIR="."

# 代理配置
REMOTE_SERVER_PROXY="http://**************:7890"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# 检测代理可用性
echo "检测远程服务器代理连接性..."
if sshpass -p "${SERVER_PASSWORD}" ssh -o StrictHostKeyChecking=no -p ${SERVER_PORT} ${SERVER_USER}@${SERVER_IP} "curl -s --connect-timeout 5 --proxy '$REMOTE_SERVER_PROXY' http://www.baidu.com > /dev/null 2>&1"; then
    echo "远程服务器代理可用: $REMOTE_SERVER_PROXY"
    USE_PROXY=true
else
    echo "远程服务器代理不可用，使用直连方式"
    USE_PROXY=false
fi

echo -e "${YELLOW}=== 生产环境部署开始 ===${NC}"

# 1. 停止现有容器
echo -e "${YELLOW}步骤1: 停止现有容器...${NC}"
sshpass -p "${SERVER_PASSWORD}" ssh -o StrictHostKeyChecking=no -p ${SERVER_PORT} ${SERVER_USER}@${SERVER_IP} "
    cd ${REMOTE_DIR}
    docker-compose down
    docker system prune -f
"

# 2. 同步代码（排除会影响容器的目录）
echo -e "${YELLOW}步骤2: 同步最新代码...${NC}"
sshpass -p "${SERVER_PASSWORD}" rsync -avz \
    --exclude 'node_modules' \
    --exclude 'venv' \
    --exclude '__pycache__' \
    --exclude '.git' \
    --exclude 'logs' \
    --exclude '*.pyc' \
    --delete \
    -e "ssh -o StrictHostKeyChecking=no -p ${SERVER_PORT}" \
    ${LOCAL_DIR}/ ${SERVER_USER}@${SERVER_IP}:${REMOTE_DIR}/

echo -e "${GREEN}✓ 代码同步完成${NC}"

# 3. 清理Docker缓存并重新构建
echo -e "${YELLOW}步骤3: 清理Docker缓存并重新构建...${NC}"
sshpass -p "${SERVER_PASSWORD}" ssh -o StrictHostKeyChecking=no -p ${SERVER_PORT} ${SERVER_USER}@${SERVER_IP} "
    cd ${REMOTE_DIR}
    # 清理Docker构建缓存
    docker builder prune -f
    docker image prune -f
    
    # 强制重新构建镜像（不使用缓存）
    if [ '$USE_PROXY' = 'true' ]; then
        echo '使用代理重新构建镜像...'
        export HTTP_PROXY=$REMOTE_SERVER_PROXY
        export HTTPS_PROXY=$REMOTE_SERVER_PROXY
        docker-compose build --no-cache --build-arg HTTP_PROXY=$REMOTE_SERVER_PROXY --build-arg HTTPS_PROXY=$REMOTE_SERVER_PROXY
    else
        echo '使用直连方式重新构建镜像...'
        unset HTTP_PROXY HTTPS_PROXY http_proxy https_proxy
        docker-compose build --no-cache --build-arg HTTP_PROXY= --build-arg HTTPS_PROXY=
    fi
"

# 4. 启动新容器
echo -e "${YELLOW}步骤4: 启动新容器...${NC}"
sshpass -p "${SERVER_PASSWORD}" ssh -o StrictHostKeyChecking=no -p ${SERVER_PORT} ${SERVER_USER}@${SERVER_IP} "
    cd ${REMOTE_DIR}
    docker-compose up -d
"

# 5. 等待服务启动
echo -e "${YELLOW}步骤5: 等待服务启动...${NC}"
sleep 20

# 6. 验证部署结果
echo -e "${YELLOW}步骤6: 验证部署结果...${NC}"
sshpass -p "${SERVER_PASSWORD}" ssh -o StrictHostKeyChecking=no -p ${SERVER_PORT} ${SERVER_USER}@${SERVER_IP} "
    cd ${REMOTE_DIR}
    
    echo '=== 容器状态检查 ==='
    docker-compose ps
    
    echo '=== 后端依赖验证 ==='
    docker-compose exec -T backend python3 -c '
import sys
try:
    import confluent_kafka
    print(\"✓ confluent-kafka:\", confluent_kafka.__version__)
except ImportError as e:
    print(\"✗ confluent-kafka 导入失败:\", e)

try:
    import kafka
    print(\"✓ kafka-python 可用\")
except ImportError as e:
    print(\"✗ kafka-python 导入失败:\", e)

try:
    import fastapi
    print(\"✓ FastAPI 可用\")
except ImportError as e:
    print(\"✗ FastAPI 导入失败:\", e)

print(\"Python路径:\", sys.path[:3])
'
    
    echo '=== 应用健康检查 ==='
    sleep 5
    curl -s http://localhost:8000/docs | head -5 || echo '后端API检查失败'
    curl -s http://localhost:8080 | head -5 || echo '前端检查失败'
    
    echo '=== 后端日志检查 ==='
    docker-compose logs backend | tail -20
"

echo -e "${GREEN}=== 生产环境部署完成 ===${NC}"
echo -e "${GREEN}前端地址: http://${SERVER_IP}:8080${NC}"
echo -e "${GREEN}后端API: http://${SERVER_IP}:8000${NC}"
echo -e "${GREEN}API文档: http://${SERVER_IP}:8000/docs${NC}"

echo -e "${YELLOW}关键改进：${NC}"
echo -e "${GREEN}✓ 移除了容器卷挂载，避免代码重置${NC}"
echo -e "${GREEN}✓ 强制重新构建镜像，确保依赖安装${NC}"
echo -e "${GREEN}✓ 增强了依赖验证机制${NC}"
echo -e "${GREEN}✓ 添加了详细的部署验证步骤${NC}"

# DeepSeek AI SQL PCAP Analyzer

一个基于DeepSeek AI的MySQL数据包捕获和分析系统，支持自然语言查询转换为SQL，并自动捕获和分析MySQL网络流量。

## 功能特性

- 🤖 **DeepSeek AI处理**: 使用DeepSeek大模型和LangChain将自然语言转换为SQL查询
- 📦 **自动数据包捕获**: 在MySQL操作前后自动启动/停止tcpdump抓包
- 🔍 **数据包分析**: 分析pcap文件，提取MySQL查询和连接信息
- 🎯 **智能工具调用**: AI通过tools调用MySQL和抓包功能
- 🌐 **现代化前端**: Vue3 + Element Plus 响应式界面
- ⚡ **高性能后端**: FastAPI异步处理
- 🔒 **不加密抓包**: 确保pcap包内容可读，便于分析

## 技术栈

### 后端
- **FastAPI**: 现代化Python Web框架
- **LangChain**: AI应用开发框架
- **PyMySQL**: MySQL数据库连接
- **Scapy**: 数据包分析
- **tcpdump**: 网络数据包捕获

### 前端
- **Vue 3**: 渐进式JavaScript框架
- **Element Plus**: Vue 3组件库
- **Axios**: HTTP客户端

## 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Vue Frontend  │───▶│  FastAPI Backend │───▶│  MySQL Database │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │  LangChain AI   │
                       │     Tools       │
                       └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │ Packet Capture  │
                       │   (tcpdump)     │
                       └─────────────────┘
```

## 安装和运行

### 环境要求

- Python 3.8+
- Node.js 16+
- MySQL 5.7+
- tcpdump (用于数据包捕获)

### 快速启动

1. **克隆项目**
```bash
git clone <repository-url>
cd ai_sql_pcap
```

2. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件，配置MySQL连接信息和OpenAI API Key
```

3. **一键启动**
```bash
./start.sh
```

4. **访问应用**
- 前端界面: http://localhost:3000
- 后端API: http://localhost:8000
- API文档: http://localhost:8000/docs

### 手动启动

#### 后端启动
```bash
# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 启动后端
python main.py
```

#### 前端启动
```bash
cd frontend
npm install
npm run serve
```

## 使用说明

### 1. 自然语言查询

在前端界面输入自然语言查询，例如：
- "查询所有用户信息"
- "删除ID为1的用户"
- "创建一个名为products的表"
- "统计每个部门的员工数量"

### 2. 自动抓包

系统会在执行MySQL查询前自动启动数据包捕获，查询完成后停止捕获，生成pcap文件用于分析。

### 3. 查询结果

系统会显示：
- 生成的SQL语句
- 查询执行结果
- 数据包捕获文件信息
- 错误信息（如果有）

## 配置说明

### 环境变量配置 (.env)

```bash
# OpenAI API配置 (可选)
OPENAI_API_KEY=your_openai_api_key_here

# MySQL配置
MYSQL_HOST=**************
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=123456
MYSQL_DATABASE=test

# 应用配置
APP_HOST=0.0.0.0
APP_PORT=8000
DEBUG=True

# 抓包配置
CAPTURE_DIR=captures
CAPTURE_INTERFACE=any
```

### MySQL权限要求

确保MySQL用户具有以下权限：
- SELECT, INSERT, UPDATE, DELETE (数据操作)
- CREATE, DROP, ALTER (结构操作)
- SHOW DATABASES (查看数据库列表)

### tcpdump权限

在Linux/macOS系统上，tcpdump需要root权限或特殊配置：

```bash
# 方法1: 使用sudo运行应用
sudo python main.py

# 方法2: 给tcpdump设置权限 (Linux)
sudo setcap cap_net_raw,cap_net_admin=eip /usr/sbin/tcpdump

# 方法3: 将用户添加到特定组 (Linux)
sudo usermod -a -G wireshark $USER
```

## API接口

### 主要接口

- `POST /query` - 处理自然语言查询
- `GET /health` - 健康检查
- `GET /capture/status` - 获取抓包状态
- `POST /capture/start` - 启动抓包
- `POST /capture/stop` - 停止抓包
- `GET /databases` - 获取数据库列表
- `GET /tables/{database}` - 获取表列表

详细API文档请访问: http://localhost:8000/docs

## 项目结构

```
ai_sql_pcap/
├── main.py                 # FastAPI主应用
├── requirements.txt        # Python依赖
├── start.sh               # 启动脚本
├── .env                   # 环境配置
├── services/              # 业务服务层
│   ├── ai_service.py      # AI服务
│   ├── mysql_service.py   # MySQL服务
│   └── packet_capture_service.py # 抓包服务
├── tools/                 # LangChain工具
│   ├── mysql_tools.py     # MySQL工具
│   └── packet_tools.py    # 抓包工具
├── utils/                 # 工具函数
│   └── config.py          # 配置管理
├── captures/              # 抓包文件存储
└── frontend/              # Vue前端
    ├── src/
    │   ├── components/    # 组件
    │   ├── views/         # 页面
    │   └── services/      # API服务
    └── package.json
```

## 开发说明

### 添加新的AI工具

1. 在 `tools/` 目录创建新的工具文件
2. 继承 `BaseTool` 类
3. 在 `services/ai_service.py` 中注册工具

### 扩展数据包分析

1. 修改 `services/packet_capture_service.py`
2. 添加新的分析方法
3. 在前端显示分析结果

## 故障排除

### 常见问题

1. **抓包权限问题**
   - 确保tcpdump有足够权限
   - 尝试使用sudo运行

2. **MySQL连接失败**
   - 检查MySQL服务是否运行
   - 验证连接参数和权限

3. **AI解析失败**
   - 检查OpenAI API Key配置
   - 查看日志了解具体错误

4. **前端无法访问后端**
   - 检查CORS配置
   - 确认端口没有被占用

### 日志查看

```bash
# 查看后端日志
tail -f logs/app.log

# 查看抓包日志
tail -f logs/capture.log
```

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 发起Pull Request

## 许可证

MIT License

## 联系方式

如有问题或建议，请提交Issue或联系开发团队。

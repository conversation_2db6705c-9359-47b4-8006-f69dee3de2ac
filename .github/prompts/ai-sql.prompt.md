---
mode: agent
---
用中文回答

## 命令执行规则
- 当需要在终端执行命令时，默认“直接继续”，不要询问“继续/取消”。
- 如果遇到必须确认的系统级安全弹窗，选择“继续”，并记录执行的具体命令与结果到回复中。
- 如命令可能造成破坏性影响（删除/覆盖/停机），先在回复中清晰提示风险与影响范围，再继续执行。

## 项目启动规则
- 当你需要启动后端，先检查8000端口是否被占用，如果占用，先kill掉，再运行后端
- 后端启动方式：在进入后端backend目录下执行 `source venv/bin/activate` 激活虚拟环境，然后使用nohup启动 `python3 start_with_worker.py` 
- 如果需要清理进程：可以使用 `python3 start_with_worker.py --cleanup-only` 强制清理相关进程
- 虚拟环境路径：backend/venv/，包含所有后端依赖：fastapi、pymysql、scapy、arq、paramiko等
- Redis地址：192.168.0.41:6379，后端MySQL数据库：192.168.0.41:3306
- 默认服务已经启动，支持MySQL、PostgreSQL、GaussDB、Oracle、MongoDB多种数据库

## 抓包规则
- 不要使用any网口抓包，要使用固定网口，可以用AI识别正确的网口后保存
- 抓包顺序严格按照：开始抓包→连接数据库→执行查询（包含多语句查询）→断开数据库连接→停止抓包
- 每个测试用例的每个步骤都要独立进行一次完整的抓包流程
- 抓包文件存储在captures/目录下，命名格式：{数据库类型}_capture_{步骤标识}_{时间戳}.pcap
- 支持的数据库类型：mysql、postgres、gaussdb、oracle、mongodb
- 抓包服务类：MySQLPacketCaptureService、PostgresLocalPacketCaptureService、GaussDBPacketCaptureService、OraclePacketCaptureService、MongoLocalPacketCaptureService

## 服务器连接规则
- 项目运行依赖的数据库docker容器都不在本地，有需要的需要访问数据库服务器，数据库docker镜像分别在192.168.70.220、192.168.70.47和192.168.70.50服务器
- 当你需要连接服务器，可以参考 `ssh -i ~/.ssh/id_rsa_192_168_70_220_root root@192.168.70.220` 连接
- 如果换服务器，你记得换秘钥，如果失败则进行互信
- 所有的容器运行都不在本地，可进入数据库管理中查看对应数据库的服务器

## 代码规范
- 如有的捕获异常except，不允许直接写pass，所有捕获或者异常操作，必须打印到日志文件，不要使用print语句打印日志
- 使用logging模块记录日志，日志文件存储在logs/目录下分类存储
- 测试完成后，删除相关测试脚本

## 测试用例执行流程
- 测试用例管理API：/api/test-case-management/
- 测试用例执行API：/api/test-case-execution/
- 支持同步和异步执行模式
- 每个步骤的抓包都通过start_capture()和stop_capture()方法实现
- 执行顺序：分析测试用例→开始抓包→创建数据库连接→执行SQL语句→关闭连接→停止抓包→保存结果ly: true
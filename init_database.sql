-- AI SQL PCAP 系统数据库初始化脚本
-- 服务器部署版本 (************:3306)

-- 创建ai_sql_pcap数据库
CREATE DATABASE IF NOT EXISTS `ai_sql_pcap` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `ai_sql_pcap`;

-- 1. 数据库配置表
CREATE TABLE IF NOT EXISTS database_configs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE COMMENT '配置名称',
    host VARCHAR(255) NOT NULL COMMENT '主机地址',
    port INT NOT NULL DEFAULT 3306 COMMENT '端口号',
    user VARCHAR(100) NOT NULL COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码',
    database_name VARCHAR(100) NOT NULL COMMENT '数据库名',
    description TEXT COMMENT '描述',
    is_default BOOLEAN DEFAULT FALSE COMMENT '是否为默认配置',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_name (name),
    INDEX idx_host_port (host, port),
    INDEX idx_is_default (is_default),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据库配置表';

-- 2. 数据库表信息缓存表
CREATE TABLE IF NOT EXISTS database_tables (
    id INT AUTO_INCREMENT PRIMARY KEY,
    config_id INT NOT NULL COMMENT '数据库配置ID',
    database_name VARCHAR(100) NOT NULL COMMENT '数据库名',
    table_name VARCHAR(100) NOT NULL COMMENT '表名',
    table_type VARCHAR(50) DEFAULT 'BASE TABLE' COMMENT '表类型',
    table_comment TEXT COMMENT '表注释',
    table_rows BIGINT DEFAULT 0 COMMENT '表行数',
    data_length BIGINT DEFAULT 0 COMMENT '数据大小',
    index_length BIGINT DEFAULT 0 COMMENT '索引大小',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (config_id) REFERENCES database_configs(id) ON DELETE CASCADE,
    UNIQUE KEY uk_config_db_table (config_id, database_name, table_name),
    INDEX idx_config_id (config_id),
    INDEX idx_database_name (database_name),
    INDEX idx_table_name (table_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据库表信息缓存表';

-- 3. 数据库连接测试记录表
CREATE TABLE IF NOT EXISTS connection_tests (
    id INT AUTO_INCREMENT PRIMARY KEY,
    config_id INT NOT NULL COMMENT '数据库配置ID',
    test_result BOOLEAN NOT NULL COMMENT '测试结果',
    response_time INT DEFAULT 0 COMMENT '响应时间(毫秒)',
    error_message TEXT COMMENT '错误信息',
    databases_count INT DEFAULT 0 COMMENT '数据库数量',
    tables_count INT DEFAULT 0 COMMENT '表数量',
    tested_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '测试时间',
    FOREIGN KEY (config_id) REFERENCES database_configs(id) ON DELETE CASCADE,
    INDEX idx_config_id (config_id),
    INDEX idx_test_result (test_result),
    INDEX idx_tested_at (tested_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据库连接测试记录表';

-- 4. 查询执行记录表
CREATE TABLE IF NOT EXISTS query_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    config_id INT COMMENT '数据库配置ID',
    query_text TEXT NOT NULL COMMENT '查询语句',
    query_type VARCHAR(50) COMMENT '查询类型(SQL/PROTOCOL)',
    natural_language TEXT COMMENT '自然语言输入',
    execution_time INT DEFAULT 0 COMMENT '执行时间(毫秒)',
    result_rows INT DEFAULT 0 COMMENT '结果行数',
    packet_file VARCHAR(255) COMMENT '抓包文件路径',
    success BOOLEAN DEFAULT TRUE COMMENT '执行是否成功',
    error_message TEXT COMMENT '错误信息',
    user_ip VARCHAR(45) COMMENT '用户IP',
    executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '执行时间',
    FOREIGN KEY (config_id) REFERENCES database_configs(id) ON DELETE SET NULL,
    INDEX idx_config_id (config_id),
    INDEX idx_query_type (query_type),
    INDEX idx_success (success),
    INDEX idx_executed_at (executed_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='查询执行记录表';

-- 5. 抓包文件记录表
CREATE TABLE IF NOT EXISTS packet_files (
    id INT AUTO_INCREMENT PRIMARY KEY,
    query_log_id INT COMMENT '查询记录ID',
    filename VARCHAR(255) NOT NULL COMMENT '文件名',
    filepath VARCHAR(500) NOT NULL COMMENT '文件路径',
    file_size BIGINT DEFAULT 0 COMMENT '文件大小',
    total_packets INT DEFAULT 0 COMMENT '总数据包数',
    mysql_packets INT DEFAULT 0 COMMENT 'MySQL数据包数',
    connections_count INT DEFAULT 0 COMMENT '连接数',
    queries_count INT DEFAULT 0 COMMENT '查询数',
    analysis_data JSON COMMENT '分析结果JSON',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (query_log_id) REFERENCES query_logs(id) ON DELETE SET NULL,
    INDEX idx_query_log_id (query_log_id),
    INDEX idx_filename (filename),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='抓包文件记录表';


-- 6. 批量执行记录表
CREATE TABLE IF NOT EXISTS batch_executions (
    id VARCHAR(36) PRIMARY KEY COMMENT '批量执行UUID',
    name VARCHAR(255) NOT NULL COMMENT '批量执行名称',
    test_case_ids JSON NOT NULL COMMENT '测试用例ID数组',
    server_config_id INT COMMENT '服务器配置ID',
    database_config_id INT COMMENT '数据库配置ID',
    execution_options JSON COMMENT '执行选项',
    status ENUM('pending', 'running', 'completed', 'failed', 'cancelled') NOT NULL DEFAULT 'pending' COMMENT '执行状态',
    total_cases INT DEFAULT 0 COMMENT '总用例数',
    completed_cases INT DEFAULT 0 COMMENT '已完成用例数',
    success_cases INT DEFAULT 0 COMMENT '成功用例数',
    failed_cases INT DEFAULT 0 COMMENT '失败用例数',
    start_time TIMESTAMP NULL COMMENT '开始时间',
    end_time TIMESTAMP NULL COMMENT '结束时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (database_config_id) REFERENCES database_configs(id) ON DELETE SET NULL,
    INDEX idx_status (status),
    INDEX idx_start_time (start_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='批量执行记录表';

-- 异步任务执行记录表
CREATE TABLE IF NOT EXISTS async_task_execution_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    task_id VARCHAR(36) NOT NULL COMMENT '任务ID（UUID）',
    task_type ENUM(
        'mysql_capture', 'postgres_capture', 'mongo_capture', 'oracle_capture', 'gaussdb_capture',
        'ai_mysql_capture', 'ai_postgres_capture', 'ai_mongo_capture', 'ai_oracle_capture', 'ai_gaussdb_capture',
        'docker_build', 'docker_image_build', 'ai_test_case_generation',
        'batch_test_case_execution', 'single_test_case_execution'
    ) NOT NULL COMMENT '任务类型',
    task_name VARCHAR(255) COMMENT '任务名称',
    task_description TEXT COMMENT '任务描述',

    -- 任务状态信息
    status ENUM('PENDING', 'RUNNING', 'COMPLETED', 'FAILED', 'CANCELLED', 'TIMEOUT') NOT NULL DEFAULT 'PENDING' COMMENT '任务状态',
    progress INT DEFAULT 0 COMMENT '执行进度(0-100)',
    current_step VARCHAR(255) COMMENT '当前执行步骤',

    -- 执行器信息
    executor_type ENUM('PYTHON', 'C', 'JAVA') NOT NULL DEFAULT 'PYTHON' COMMENT '执行器类型',
    executor_version VARCHAR(50) COMMENT '执行器版本',
    executor_path VARCHAR(500) COMMENT '执行器路径',
    executor_selection_reason TEXT COMMENT '执行器选择原因',
    fallback_from_executor ENUM('PYTHON', 'C', 'JAVA') COMMENT '从哪个执行器回退而来',

    -- 配置信息
    database_config_id INT COMMENT '数据库配置ID',
    server_config_id INT COMMENT '服务器配置ID',
    capture_enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用抓包',
    capture_duration INT COMMENT '抓包持续时间(秒)',

    -- 输入参数
    input_sql_query TEXT COMMENT '输入SQL查询',
    input_mongo_query TEXT COMMENT '输入MongoDB查询',
    input_natural_query TEXT COMMENT '输入自然语言查询',
    input_parameters JSON COMMENT '其他输入参数',

    -- 执行结果
    execution_result JSON COMMENT '执行结果详情',
    output_files JSON COMMENT '输出文件列表',
    capture_files JSON COMMENT '抓包文件列表',
    generated_sql TEXT COMMENT 'AI生成的SQL语句',

    -- 性能指标
    start_time TIMESTAMP NULL COMMENT '开始时间',
    end_time TIMESTAMP NULL COMMENT '结束时间',
    duration_seconds DECIMAL(10,3) COMMENT '执行时长(秒)',
    cpu_usage_percent DECIMAL(5,2) COMMENT 'CPU使用率',
    memory_usage_mb DECIMAL(10,2) COMMENT '内存使用量(MB)',

    -- 错误信息
    error_code VARCHAR(50) COMMENT '错误代码',
    error_message TEXT COMMENT '错误消息',
    error_details JSON COMMENT '详细错误信息',
    error_stack_trace TEXT COMMENT '错误堆栈跟踪',
    retry_count INT DEFAULT 0 COMMENT '重试次数',
    max_retries INT DEFAULT 0 COMMENT '最大重试次数',

    -- 环境信息
    worker_id VARCHAR(100) COMMENT '工作器ID',
    worker_host VARCHAR(255) COMMENT '工作器主机',
    python_version VARCHAR(50) COMMENT 'Python版本',
    system_info JSON COMMENT '系统信息',
    environment_variables JSON COMMENT '环境变量',

    -- 依赖任务
    parent_task_id VARCHAR(36) COMMENT '父任务ID',
    child_task_ids JSON COMMENT '子任务ID列表',
    dependency_task_ids JSON COMMENT '依赖任务ID列表',

    -- 批量执行相关
    batch_id VARCHAR(36) COMMENT '批量执行ID',
    batch_name VARCHAR(255) COMMENT '批量执行名称',
    batch_total_count INT COMMENT '批量总数',
    batch_current_index INT COMMENT '批量当前索引',

    -- 测试用例相关
    test_case_id VARCHAR(36) COMMENT '测试用例ID',
    test_case_name VARCHAR(255) COMMENT '测试用例名称',
    test_steps_executed JSON COMMENT '已执行的测试步骤',
    test_validation_results JSON COMMENT '测试验证结果',

    -- 抓包相关
    network_interface VARCHAR(100) COMMENT '网络接口',
    packet_count INT COMMENT '抓包数量',
    packet_size_bytes BIGINT COMMENT '抓包总大小(字节)',
    capture_filter VARCHAR(500) COMMENT '抓包过滤器',

    -- 审计信息
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(100) COMMENT '创建者',

    -- 外键约束
    FOREIGN KEY (database_config_id) REFERENCES database_configs(id) ON DELETE SET NULL,
    FOREIGN KEY (parent_task_id) REFERENCES async_task_execution_logs(task_id) ON DELETE SET NULL,

    -- 索引
    UNIQUE KEY uk_task_id (task_id),
    INDEX idx_task_type (task_type),
    INDEX idx_status (status),
    INDEX idx_executor_type (executor_type),
    INDEX idx_database_config_id (database_config_id),
    INDEX idx_server_config_id (server_config_id),
    INDEX idx_batch_id (batch_id),
    INDEX idx_test_case_id (test_case_id),
    INDEX idx_parent_task_id (parent_task_id),
    INDEX idx_start_time (start_time),
    INDEX idx_end_time (end_time),
    INDEX idx_duration (duration_seconds),
    INDEX idx_created_at (created_at),
    INDEX idx_status_type (status, task_type),
    INDEX idx_executor_status (executor_type, status),
    INDEX idx_batch_status (batch_id, status),
    INDEX idx_error_code (error_code),
    INDEX idx_retry_count (retry_count)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='异步任务执行记录表';

-- 插入默认配置数据
INSERT INTO database_configs (name, host, port, user, password, database_name, description, is_default)
VALUES
('local_mysql', '************', 3306, 'root', 'root', 'ai_sql_pcap', '本地MySQL数据库（Docker环境）', TRUE),
('production_mysql', '**************', 3308, 'root', 'QZ@1005#1005', 'mysql', '生产环境MySQL数据库', FALSE),
('test_mysql', '**************', 3306, 'root', '123456', 'test', '测试MySQL数据库', FALSE)
ON DUPLICATE KEY UPDATE
    host = VALUES(host),
    port = VALUES(port),
    user = VALUES(user),
    password = VALUES(password),
    database_name = VALUES(database_name),
    description = VALUES(description),
    updated_at = CURRENT_TIMESTAMP;

-- 创建测试数据库和表（用于抓包测试）
CREATE DATABASE IF NOT EXISTS test CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE test;

-- 删除旧表（如果存在），确保脚本可重入
DROP TABLE IF EXISTS order_items;
DROP TABLE IF EXISTS orders;
DROP TABLE IF EXISTS products;
DROP TABLE IF EXISTS users;


-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100),
    department VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE
);

-- 创建产品表
CREATE TABLE IF NOT EXISTS products (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    price DECIMAL(10, 2) NOT NULL,
    category VARCHAR(50),
    stock_quantity INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_available BOOLEAN DEFAULT TRUE
);

-- 创建订单表
CREATE TABLE IF NOT EXISTS orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    total_amount DECIMAL(10, 2) NOT NULL,
    status ENUM('pending', 'processing', 'shipped', 'delivered', 'cancelled') DEFAULT 'pending',
    order_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    shipping_address TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 创建订单详情表
CREATE TABLE IF NOT EXISTS order_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_id INT NOT NULL,
    product_id INT NOT NULL,
    quantity INT NOT NULL,
    unit_price DECIMAL(10, 2) NOT NULL,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
);

-- 插入测试数据
INSERT INTO users (username, email, password, full_name, department) VALUES
('admin', '<EMAIL>', 'hashed_password_1', '管理员', 'IT'),
('john_doe', '<EMAIL>', 'hashed_password_2', 'John Doe', 'Sales'),
('jane_smith', '<EMAIL>', 'hashed_password_3', 'Jane Smith', 'Marketing'),
('bob_wilson', '<EMAIL>', 'hashed_password_4', 'Bob Wilson', 'HR'),
('alice_brown', '<EMAIL>', 'hashed_password_5', 'Alice Brown', 'Finance')
ON DUPLICATE KEY UPDATE updated_at = CURRENT_TIMESTAMP;

INSERT INTO products (name, description, price, category, stock_quantity) VALUES
('笔记本电脑', '高性能办公笔记本电脑', 5999.00, '电子产品', 50),
('无线鼠标', '人体工学无线鼠标', 199.00, '电子产品', 200),
('机械键盘', '青轴机械键盘', 399.00, '电子产品', 100),
('显示器', '27寸4K显示器', 2999.00, '电子产品', 30),
('办公椅', '人体工学办公椅', 1299.00, '办公用品', 25)
ON DUPLICATE KEY UPDATE updated_at = CURRENT_TIMESTAMP;

INSERT INTO orders (user_id, total_amount, status, shipping_address, updated_at) VALUES
(2, 6198.00, 'delivered', '北京市朝阳区xxx街道xxx号', CURRENT_TIMESTAMP),
(3, 598.00, 'processing', '上海市浦东新区xxx路xxx号', CURRENT_TIMESTAMP),
(4, 2999.00, 'shipped', '广州市天河区xxx大道xxx号', CURRENT_TIMESTAMP),
(5, 1299.00, 'pending', '深圳市南山区xxx街xxx号', CURRENT_TIMESTAMP)
ON DUPLICATE KEY UPDATE updated_at = CURRENT_TIMESTAMP;

INSERT INTO order_items (order_id, product_id, quantity, unit_price) VALUES
(1, 1, 1, 5999.00),
(1, 2, 1, 199.00),
(2, 2, 1, 199.00),
(2, 3, 1, 399.00),
(3, 4, 1, 2999.00),
(4, 5, 1, 1299.00)
ON DUPLICATE KEY UPDATE order_id = VALUES(order_id);

-- 创建索引以提高查询性能
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_department ON users(department);
CREATE INDEX idx_products_category ON products(category);
CREATE INDEX idx_products_name ON products(name);
CREATE INDEX idx_orders_user_id ON orders(user_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_orders_date ON orders(order_date);
CREATE INDEX idx_order_items_order_id ON order_items(order_id);
CREATE INDEX idx_order_items_product_id ON order_items(product_id);

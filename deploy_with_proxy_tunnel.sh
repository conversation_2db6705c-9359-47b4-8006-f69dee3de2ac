#!/bin/bash

# 部署脚本 - 通过SSH隧道使用本地代理

# 配置信息
SERVER_IP="************"
SERVER_USER="root"
SERVER_PASSWORD="root@123"
SERVER_PORT="22"
REMOTE_DIR="/opt/ai_sql_pcap"
LOCAL_DIR="."

# 代理配置 - 确保使用正确的代理地址
LOCAL_PROXY_HOST="**************"
LOCAL_PROXY_PORT="7890"
REMOTE_PROXY_PORT="18890"  # 远程服务器上的代理端口

echo -e "${BLUE}代理配置信息:${NC}"
echo "本地代理: ${LOCAL_PROXY_HOST}:${LOCAL_PROXY_PORT}"
echo "远程隧道端口: ${REMOTE_PROXY_PORT}"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}=== 使用SSH隧道代理部署 ===${NC}"

# 检查本地代理是否可用
if curl -s --connect-timeout 3 --proxy "http://${LOCAL_PROXY_HOST}:${LOCAL_PROXY_PORT}" http://www.baidu.com > /dev/null 2>&1; then
    echo -e "${GREEN}✓ 本地代理可用: ${LOCAL_PROXY_HOST}:${LOCAL_PROXY_PORT}${NC}"
else
    echo -e "${RED}✗ 本地代理不可用，请检查代理设置${NC}"
    exit 1
fi

# 步骤1: 建立SSH隧道
echo -e "${YELLOW}步骤1: 建立SSH隧道...${NC}"
echo "创建SSH隧道: localhost:${REMOTE_PROXY_PORT} -> ${LOCAL_PROXY_HOST}:${LOCAL_PROXY_PORT}"

# 启动SSH隧道（后台运行）
sshpass -p "${SERVER_PASSWORD}" ssh -o StrictHostKeyChecking=no -f -N -L ${REMOTE_PROXY_PORT}:${LOCAL_PROXY_HOST}:${LOCAL_PROXY_PORT} ${SERVER_USER}@${SERVER_IP}

# 检查隧道是否建立成功
sleep 3
if pgrep -f "ssh.*${REMOTE_PROXY_PORT}:${LOCAL_PROXY_HOST}:${LOCAL_PROXY_PORT}" > /dev/null; then
    echo -e "${GREEN}✓ SSH隧道建立成功${NC}"
    TUNNEL_PID=$(pgrep -f "ssh.*${REMOTE_PROXY_PORT}:${LOCAL_PROXY_HOST}:${LOCAL_PROXY_PORT}")
    echo "隧道进程ID: $TUNNEL_PID"
else
    echo -e "${RED}✗ SSH隧道建立失败${NC}"
    exit 1
fi

# 清理函数
cleanup() {
    echo -e "${YELLOW}清理SSH隧道...${NC}"
    if [ -n "$TUNNEL_PID" ]; then
        kill $TUNNEL_PID 2>/dev/null && echo "SSH隧道已关闭"
    fi
}
trap cleanup EXIT

# 步骤2: 测试远程代理连接
echo -e "${YELLOW}步骤2: 测试远程代理连接...${NC}"
sshpass -p "${SERVER_PASSWORD}" ssh -o StrictHostKeyChecking=no ${SERVER_USER}@${SERVER_IP} "
curl -s --connect-timeout 5 --proxy http://localhost:${REMOTE_PROXY_PORT} http://www.baidu.com > /dev/null 2>&1
if [ \$? -eq 0 ]; then
    echo '✓ 远程代理连接测试成功'
else
    echo '✗ 远程代理连接测试失败'
    exit 1
fi
"

# 步骤3: 同步代码
echo -e "${YELLOW}步骤3: 同步项目文件...${NC}"
sshpass -p "${SERVER_PASSWORD}" ssh -o StrictHostKeyChecking=no ${SERVER_USER}@${SERVER_IP} "mkdir -p ${REMOTE_DIR}"

sshpass -p "${SERVER_PASSWORD}" rsync -avz \
    --exclude 'node_modules' \
    --exclude 'venv' \
    --exclude '__pycache__' \
    --exclude '.git' \
    --exclude 'captures' \
    --exclude 'logs' \
    --exclude '*.pyc' \
    -e "ssh -o StrictHostKeyChecking=no -p ${SERVER_PORT}" \
    ${LOCAL_DIR}/ ${SERVER_USER}@${SERVER_IP}:${REMOTE_DIR}/

echo -e "${GREEN}✓ 代码同步完成${NC}"

# 步骤4: 使用隧道代理构建Docker镜像
echo -e "${YELLOW}步骤4: 构建Docker镜像（使用SSH隧道代理）...${NC}"
sshpass -p "${SERVER_PASSWORD}" ssh -o StrictHostKeyChecking=no ${SERVER_USER}@${SERVER_IP} "
cd ${REMOTE_DIR}
echo '开始构建Docker镜像...'
export HTTP_PROXY=http://localhost:${REMOTE_PROXY_PORT}
export HTTPS_PROXY=http://localhost:${REMOTE_PROXY_PORT}
docker-compose build --build-arg HTTP_PROXY=http://localhost:${REMOTE_PROXY_PORT} --build-arg HTTPS_PROXY=http://localhost:${REMOTE_PROXY_PORT}
"

# 步骤5: 部署容器
echo -e "${YELLOW}步骤5: 部署容器...${NC}"
sshpass -p "${SERVER_PASSWORD}" ssh -o StrictHostKeyChecking=no ${SERVER_USER}@${SERVER_IP} "
cd ${REMOTE_DIR}
echo '停止现有容器...'
docker-compose down

echo '启动新容器...'
docker-compose up -d

echo '等待容器启动...'
sleep 15

echo '检查容器状态:'
docker-compose ps
"

# 步骤6: 健康检查
echo -e "${YELLOW}步骤6: 健康检查...${NC}"
sshpass -p "${SERVER_PASSWORD}" ssh -o StrictHostKeyChecking=no ${SERVER_USER}@${SERVER_IP} "
echo '=== 应用健康检查 ==='
echo '后端API:'
curl -s -I http://localhost:8000/api/health | head -2 || echo '后端API不可访问'
echo '前端:'
curl -s -I http://localhost:8080 | head -2 || echo '前端不可访问'
"

echo -e "\n${GREEN}=== 部署完成 ===${NC}"
echo -e "${GREEN}前端地址: http://${SERVER_IP}:8080${NC}"
echo -e "${GREEN}后端API: http://${SERVER_IP}:8000${NC}"
echo -e "${GREEN}API文档: http://${SERVER_IP}:8000/docs${NC}"

# 保持隧道运行
echo -e "${BLUE}SSH隧道将在脚本结束时自动关闭${NC}"
echo -e "${YELLOW}如需保持隧道运行，请按Ctrl+C取消自动清理${NC}"
sleep 5

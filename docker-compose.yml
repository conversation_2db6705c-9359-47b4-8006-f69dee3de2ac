version: '3.8'

services:
  # 后端服务
  backend:
    build:
      context: .
      dockerfile: docker/backend.Dockerfile
    container_name: ai_sql_pcap_backend
    ports:
      - "8000:8000"
    volumes:
      # 只挂载必要的数据目录，不覆盖整个应用目录
      - ./backend/captures:/app/captures
      - ./logs:/app/logs
      # 如果需要开发时代码热更新，可以单独挂载代码文件（但会影响依赖）
      # - ./backend:/app
    environment:
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
      - OPENAI_API_KEY=sk-e7f7a26acd294ba3a56f49a46ad1392e
      - OPENAI_API_BASE=https://api.deepseek.com/v1
      - MYSQL_HOST=************
      - MYSQL_PORT=3306
      - MYSQL_USER=root
      - MYSQL_PASSWORD=123456
      - MYSQL_DATABASE=ai_sql_pcap
      # 文件路径配置
      - CAPTURE_DIR=/app/captures
      # Oracle环境变量
      - LD_LIBRARY_PATH=/opt/oracle/instantclient_19_27:$LD_LIBRARY_PATH
      - ORACLE_HOME=/opt/oracle/instantclient_19_27
    networks:
      - ai_sql_network
    restart: unless-stopped
    extra_hosts:
      - "redis:************"
    privileged: true  # 需要特权模式来运行tcpdump
    cap_add:
      - NET_ADMIN
      - NET_RAW
      - SYS_ADMIN
    security_opt:
      - apparmor:unconfined

  # 前端服务
  frontend:
    build:
      context: .
      dockerfile: docker/frontend.Dockerfile
      args:
        HTTP_PROXY: ${HTTP_PROXY:-}
        HTTPS_PROXY: ${HTTPS_PROXY:-}
    container_name: ai_sql_pcap_frontend
    ports:
      - "8080:80"
    networks:
      - ai_sql_network
    restart: unless-stopped
    depends_on:
      - backend

  # SQLite数据库 (如果需要持久化)
  # 注意：SQLite通常作为文件存储，不需要单独的容器
  # 这里只是为了演示，实际使用时可以移除

networks:
  ai_sql_network:
    driver: bridge

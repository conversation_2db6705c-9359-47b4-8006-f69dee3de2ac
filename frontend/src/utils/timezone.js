/**
 * 时区工具模块
 * 统一处理前端的时区相关操作，确保所有时间都使用+8时区（Asia/Shanghai）
 */

// 定义+8时区偏移量（分钟）
const CHINA_TIMEZONE_OFFSET = 8 * 60; // +8小时 = 480分钟

/**
 * 获取当前时间（+8时区）
 * @returns {Date} 当前时间
 */
export function getCurrentTime() {
  return new Date();
}

/**
 * 格式化时间为本地字符串（+8时区）
 * @param {string|Date} timeStr - 时间字符串或Date对象
 * @param {Object} options - 格式化选项
 * @returns {string} 格式化后的时间字符串
 */
export function formatTime(timeStr, options = {}) {
  if (!timeStr) return '-';
  
  const date = typeof timeStr === 'string' ? new Date(timeStr) : timeStr;
  
  // 默认选项：使用中国时区
  const defaultOptions = {
    timeZone: 'Asia/Shanghai',
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  };
  
  const finalOptions = { ...defaultOptions, ...options };
  
  try {
    return date.toLocaleString('zh-CN', finalOptions);
  } catch (error) {
    console.warn('时间格式化失败，使用默认格式:', error);
    return date.toLocaleString();
  }
}

/**
 * 格式化时间为短格式（相对时间）
 * @param {string|Date} timeStr - 时间字符串或Date对象
 * @returns {string} 相对时间字符串
 */
export function formatTimeShort(timeStr) {
  if (!timeStr) return '-';
  
  const date = typeof timeStr === 'string' ? new Date(timeStr) : timeStr;
  const now = new Date();
  const diff = now - date;

  if (diff < 60000) return '刚刚';
  if (diff < 3600000) return Math.floor(diff / 60000) + '分钟前';
  if (diff < 86400000) return Math.floor(diff / 3600000) + '小时前';
  if (diff < 604800000) return Math.floor(diff / 86400000) + '天前';

  // 超过一周，显示具体日期（+8时区）
  return formatTime(date, {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
}

/**
 * 格式化持续时间
 * @param {number} seconds - 持续时间（秒）
 * @returns {string} 格式化后的持续时间
 */
export function formatDuration(seconds) {
  if (!seconds) return '-';
  
  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${mins}分${secs}秒`;
}

/**
 * 计算运行时长
 * @param {string|Date} startTime - 开始时间
 * @returns {string} 运行时长
 */
export function getRunningDuration(startTime) {
  if (!startTime) return '-';
  
  const start = typeof startTime === 'string' ? new Date(startTime) : startTime;
  const now = new Date();
  const duration = (now - start) / 1000;
  return formatDuration(duration);
}

/**
 * 将时间转换为+8时区
 * @param {string|Date} timeStr - 时间字符串或Date对象
 * @returns {Date} 转换后的Date对象
 */
export function toChinaTimezone(timeStr) {
  if (!timeStr) return null;
  
  const date = typeof timeStr === 'string' ? new Date(timeStr) : timeStr;
  
  // 获取UTC时间戳
  const utcTime = date.getTime() + (date.getTimezoneOffset() * 60000);
  
  // 转换为+8时区
  const chinaTime = new Date(utcTime + (CHINA_TIMEZONE_OFFSET * 60000));
  
  return chinaTime;
}

/**
 * 格式化日期（不包含时间）
 * @param {string|Date} dateStr - 日期字符串或Date对象
 * @returns {string} 格式化后的日期字符串
 */
export function formatDate(dateStr) {
  if (!dateStr) return '';
  
  return formatTime(dateStr, {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  });
}

/**
 * 格式化时间（不包含日期）
 * @param {string|Date} timeStr - 时间字符串或Date对象
 * @returns {string} 格式化后的时间字符串
 */
export function formatTimeOnly(timeStr) {
  if (!timeStr) return '';
  
  return formatTime(timeStr, {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
}

/**
 * 获取时间戳
 * @param {string|Date} timeStr - 时间字符串或Date对象，如果为空则使用当前时间
 * @returns {number} 时间戳
 */
export function getTimestamp(timeStr = null) {
  const date = timeStr ? (typeof timeStr === 'string' ? new Date(timeStr) : timeStr) : new Date();
  return date.getTime();
}

/**
 * 从时间戳创建Date对象
 * @param {number} timestamp - 时间戳
 * @returns {Date} Date对象
 */
export function fromTimestamp(timestamp) {
  return new Date(timestamp);
}

// 导出常用函数的别名
export const now = getCurrentTime;
export const format = formatTime;
export const formatShort = formatTimeShort;

// 默认导出
export default {
  getCurrentTime,
  formatTime,
  formatTimeShort,
  formatDuration,
  getRunningDuration,
  toChinaTimezone,
  formatDate,
  formatTimeOnly,
  getTimestamp,
  fromTimestamp,
  now,
  format,
  formatShort
};

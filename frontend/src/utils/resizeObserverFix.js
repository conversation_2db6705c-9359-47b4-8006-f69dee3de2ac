/**
 * ResizeObserver 错误修复工具
 * 解决 Element Plus 和其他组件库中常见的 ResizeObserver 错误
 */

// 错误消息匹配模式
const RESIZE_OBSERVER_ERROR_PATTERNS = [
  'ResizeObserver loop completed with undelivered notifications',
  'ResizeObserver loop limit exceeded',
  'Non-finite values are not allowed'
]

/**
 * 检查是否为 ResizeObserver 相关错误
 * @param {string|Error} error - 错误信息或错误对象
 * @returns {boolean}
 */
function isResizeObserverError(error) {
  const message = typeof error === 'string' ? error : error?.message || ''
  return RESIZE_OBSERVER_ERROR_PATTERNS.some(pattern => message.includes(pattern))
}

/**
 * 安装 ResizeObserver 错误修复
 */
export function installResizeObserverFix() {
  // 1. 全局错误处理
  const originalErrorHandler = window.onerror
  window.onerror = function(message, source, lineno, colno, error) {
    if (isResizeObserverError(message)) {
      return true // 阻止错误传播
    }
    if (originalErrorHandler) {
      return originalErrorHandler.call(this, message, source, lineno, colno, error)
    }
    return false
  }

  // 2. 错误事件监听
  window.addEventListener('error', (event) => {
    if (isResizeObserverError(event.error || event.message)) {
      event.stopImmediatePropagation()
      event.preventDefault()
    }
  }, true)

  // 3. 未处理的 Promise 错误
  window.addEventListener('unhandledrejection', (event) => {
    if (isResizeObserverError(event.reason)) {
      event.preventDefault()
    }
  })

  // 4. 重写 console.error
  const originalConsoleError = console.error
  console.error = function(...args) {
    if (args.length > 0 && isResizeObserverError(args[0])) {
      return // 静默忽略
    }
    originalConsoleError.apply(console, args)
  }

  // 5. 重写 ResizeObserver 构造函数
  if (typeof window !== 'undefined' && window.ResizeObserver) {
    const OriginalResizeObserver = window.ResizeObserver
    
    window.ResizeObserver = class ResizeObserver extends OriginalResizeObserver {
      constructor(callback) {
        const wrappedCallback = (entries, observer) => {
          try {
            // 使用 requestAnimationFrame 来避免循环问题
            requestAnimationFrame(() => {
              try {
                callback(entries, observer)
              } catch (error) {
                if (!isResizeObserverError(error)) {
                  throw error
                }
              }
            })
          } catch (error) {
            if (!isResizeObserverError(error)) {
              throw error
            }
          }
        }
        
        super(wrappedCallback)
      }
    }
  }

  console.log('✅ ResizeObserver 错误修复已安装')
}

/**
 * 创建安全的 ResizeObserver 实例
 * @param {Function} callback - 回调函数
 * @returns {ResizeObserver}
 */
export function createSafeResizeObserver(callback) {
  const safeCallback = (entries, observer) => {
    try {
      // 延迟执行以避免循环
      setTimeout(() => {
        try {
          callback(entries, observer)
        } catch (error) {
          if (!isResizeObserverError(error)) {
            console.error('ResizeObserver callback error:', error)
          }
        }
      }, 0)
    } catch (error) {
      if (!isResizeObserverError(error)) {
        console.error('ResizeObserver error:', error)
      }
    }
  }

  return new ResizeObserver(safeCallback)
}

/**
 * 防抖版本的 ResizeObserver
 * @param {Function} callback - 回调函数
 * @param {number} delay - 防抖延迟时间（毫秒）
 * @returns {ResizeObserver}
 */
export function createDebouncedResizeObserver(callback, delay = 100) {
  let timeoutId = null
  
  const debouncedCallback = (entries, observer) => {
    if (timeoutId) {
      clearTimeout(timeoutId)
    }
    
    timeoutId = setTimeout(() => {
      try {
        callback(entries, observer)
      } catch (error) {
        if (!isResizeObserverError(error)) {
          console.error('Debounced ResizeObserver callback error:', error)
        }
      }
    }, delay)
  }

  return createSafeResizeObserver(debouncedCallback)
}

// 自动安装修复（如果在浏览器环境中）
if (typeof window !== 'undefined') {
  // 在 DOM 加载完成后安装
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', installResizeObserverFix)
  } else {
    installResizeObserverFix()
  }
}

export default {
  installResizeObserverFix,
  createSafeResizeObserver,
  createDebouncedResizeObserver,
  isResizeObserverError
}

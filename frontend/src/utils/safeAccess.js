/**
 * 安全的对象属性访问工具
 * 防止访问undefined或null对象的属性时出现错误
 */

/**
 * 安全地获取嵌套对象的属性值
 * @param {Object} obj - 要访问的对象
 * @param {string} path - 属性路径，用点号分隔
 * @param {*} defaultValue - 默认值
 * @returns {*} 属性值或默认值
 */
export function safeGet(obj, path, defaultValue = undefined) {
  if (!obj || typeof obj !== 'object') {
    return defaultValue
  }
  
  const keys = path.split('.')
  let current = obj
  
  for (const key of keys) {
    if (current === null || current === undefined || typeof current !== 'object') {
      return defaultValue
    }
    current = current[key]
  }
  
  return current !== undefined ? current : defaultValue
}

/**
 * 安全地检查对象是否存在且具有特定属性
 * @param {Object} obj - 要检查的对象
 * @param {string} path - 属性路径，用点号分隔
 * @returns {boolean} 是否存在
 */
export function safeHas(obj, path) {
  return safeGet(obj, path) !== undefined
}

/**
 * 安全地访问数组
 * @param {Array} arr - 数组
 * @param {number} index - 索引
 * @param {*} defaultValue - 默认值
 * @returns {*} 数组元素或默认值
 */
export function safeArrayGet(arr, index, defaultValue = undefined) {
  if (!Array.isArray(arr) || index < 0 || index >= arr.length) {
    return defaultValue
  }
  return arr[index]
}

/**
 * 安全地获取数组长度
 * @param {Array} arr - 数组
 * @returns {number} 数组长度或0
 */
export function safeArrayLength(arr) {
  return Array.isArray(arr) ? arr.length : 0
}

/**
 * 安全地执行字符串方法
 * @param {string} str - 字符串
 * @param {string} method - 方法名
 * @param {...any} args - 方法参数
 * @returns {*} 方法结果或空字符串
 */
export function safeStringMethod(str, method, ...args) {
  if (typeof str !== 'string') {
    return ''
  }
  
  if (typeof str[method] === 'function') {
    return str[method](...args)
  }
  
  return ''
}

/**
 * 安全地执行对象方法
 * @param {Object} obj - 对象
 * @param {string} method - 方法名
 * @param {...any} args - 方法参数
 * @returns {*} 方法结果或undefined
 */
export function safeObjectMethod(obj, method, ...args) {
  if (!obj || typeof obj !== 'object') {
    return undefined
  }
  
  if (typeof obj[method] === 'function') {
    return obj[method](...args)
  }
  
  return undefined
}

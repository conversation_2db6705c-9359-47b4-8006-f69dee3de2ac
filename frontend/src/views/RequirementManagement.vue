<template>
  <div class="requirement-management">
    <div class="header">
      <h2>协议需求管理</h2>
      <div class="header-actions">
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          新建需求
        </el-button>
        <el-button type="success" @click="showParseDialog = true">
          <el-icon><Document /></el-icon>
          解析文档
        </el-button>
        <el-button type="warning" @click="showBatchOperationDialog = true" :disabled="selectedRequirements.length === 0">
          <el-icon><Operation /></el-icon>
          批量操作 ({{ selectedRequirements.length }})
        </el-button>
        <el-button @click="showStatistics = true">
          <el-icon><DataAnalysis /></el-icon>
          统计信息
        </el-button>
        <el-dropdown @command="handleExport" trigger="click">
          <el-button type="info">
            <el-icon><Download /></el-icon>
            导出需求
            <el-icon class="el-icon--right"><ArrowDown /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="excel">导出为Excel</el-dropdown-item>
              <el-dropdown-item command="csv">导出为CSV</el-dropdown-item>
              <el-dropdown-item command="json">导出为JSON</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="search-filters">
      <el-row :gutter="20">
        <el-col :span="5">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索标题、描述..."
            clearable
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="3">
          <el-select v-model="searchForm.requirement_type" placeholder="需求类型" clearable>
            <el-option
              v-for="type in requirementTypes"
              :key="type.value"
              :label="type.label"
              :value="type.value"
            />
          </el-select>
        </el-col>
        <el-col :span="3">
          <el-select v-model="searchForm.protocol_type" placeholder="协议类型" clearable>
            <el-option
              v-for="protocol in protocolTypes"
              :key="protocol.value"
              :label="protocol.label"
              :value="protocol.value"
            />
          </el-select>
        </el-col>
        <el-col :span="3">
          <el-select v-model="searchForm.priority" placeholder="优先级" clearable>
            <el-option
              v-for="priority in priorities"
              :key="priority.value"
              :label="priority.label"
              :value="priority.value"
            />
          </el-select>
        </el-col>
        <el-col :span="3">
          <el-select v-model="searchForm.status" placeholder="状态" clearable>
            <el-option
              v-for="status in statuses"
              :key="status.value"
              :label="status.label"
              :value="status.value"
            />
          </el-select>
        </el-col>
        <el-col :span="3">
          <el-select v-model="searchForm.complexity" placeholder="复杂度" clearable>
            <el-option
              v-for="complexity in complexities"
              :key="complexity.value"
              :label="complexity.label"
              :value="complexity.value"
            />
          </el-select>
        </el-col>
        <el-col :span="2">
          <el-button type="primary" @click="handleSearch" :loading="loading">搜索</el-button>
        </el-col>
      </el-row>
      
      <el-row :gutter="20" style="margin-top: 10px;">
        <el-col :span="3">
          <el-select v-model="searchForm.category" placeholder="分类" clearable>
            <el-option
              v-for="category in categories"
              :key="category"
              :label="category"
              :value="category"
            />
          </el-select>
        </el-col>
        <el-col :span="3">
          <el-select v-model="searchForm.module" placeholder="模块" clearable>
            <el-option
              v-for="module in modules"
              :key="module"
              :label="module"
              :value="module"
            />
          </el-select>
        </el-col>
        <el-col :span="3">
          <el-select v-model="searchForm.author" placeholder="创建者" clearable>
            <el-option
              v-for="author in authors"
              :key="author"
              :label="author"
              :value="author"
            />
          </el-select>
        </el-col>
        <el-col :span="3">
          <el-select v-model="searchForm.testability" placeholder="可测试性" clearable>
            <el-option
              v-for="testability in testabilities"
              :key="testability.value"
              :label="testability.label"
              :value="testability.value"
            />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-input
            v-model="searchForm.tags"
            placeholder="标签(逗号分隔)"
            clearable
          />
        </el-col>
        <el-col :span="2">
          <el-button @click="resetSearch">重置</el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 需求列表 -->
    <div class="requirements-table">
      <el-table
        :data="requirements"
        style="width: 100%"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="title" label="需求标题" min-width="200" show-overflow-tooltip>
          <template #default="{ row }">
            <div>
              <span style="font-weight: bold;">{{ row.title }}</span>
              <div style="font-size: 12px; color: #666; margin-top: 4px;">
                {{ row.protocol_type }} {{ row.protocol_version ? `v${row.protocol_version}` : '' }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="requirement_type" label="类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getRequirementTypeColor(row.requirement_type)" size="small">
              {{ getRequirementTypeLabel(row.requirement_type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="priority" label="优先级" width="100">
          <template #default="{ row }">
            <el-tag :type="getPriorityColor(row.priority)" size="small">
              {{ getPriorityLabel(row.priority) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusColor(row.status)" size="small">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="complexity" label="复杂度" width="100">
          <template #default="{ row }">
            <el-tag :type="getComplexityColor(row.complexity)" size="small">
              {{ getComplexityLabel(row.complexity) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="test_coverage" label="测试覆盖率" width="120">
          <template #default="{ row }">
            <div>
              <el-progress 
                :percentage="Math.round(row.test_coverage * 100)" 
                :stroke-width="8"
                :text-inside="true"
                :format="(percentage) => `${percentage}%`"
              />
              <div style="font-size: 11px; color: #666; text-align: center; margin-top: 2px;">
                {{ row.actual_test_cases }}/{{ row.expected_test_cases }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="author" label="创建者" width="100" />
        <el-table-column prop="created_at" label="创建时间" width="150">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button link type="primary" size="small" @click.stop="viewRequirement(row)">
              详情
            </el-button>
            <el-button link type="primary" size="small" @click.stop="editRequirement(row)">
              编辑
            </el-button>
            <el-button link type="success" size="small" @click.stop="generateTestCases(row)">
              生成用例
            </el-button>
            <el-button link type="danger" size="small" @click.stop="deleteRequirement(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          :current-page="pagination.page"
          :page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handlePageSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </div>

    <!-- 新建/编辑需求对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingRequirement ? '编辑需求' : '新建需求'"
      width="80%"
      :close-on-click-modal="false"
    >
      <requirement-form
        :requirement="editingRequirement"
        :requirement-types="requirementTypes"
        :protocol-types="protocolTypes"
        :priorities="priorities"
        :complexities="complexities"
        :testabilities="testabilities"
        :categories="categories"
        :modules="modules"
        :authors="authors"
        :reviewers="reviewers"
        :tags="allTags"
        @submit="handleRequirementSubmit"
        @cancel="showCreateDialog = false"
      />
    </el-dialog>

    <!-- 文档解析对话框 -->
    <el-dialog
      v-model="showParseDialog"
      title="解析协议文档"
      width="60%"
      :close-on-click-modal="false"
    >
      <document-parse-form
        :protocol-types="protocolTypes"
        :document-types="documentTypes"
        :authors="authors"
        :tags="allTags"
        @submit="handleDocumentParse"
        @cancel="showParseDialog = false"
      />
    </el-dialog>

    <!-- 需求详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="需求详情"
      width="70%"
    >
      <requirement-detail
        v-if="selectedRequirement"
        :requirement="selectedRequirement"
        @edit="editRequirement"
        @generate-test-cases="generateTestCases"
        @close="showDetailDialog = false"
      />
    </el-dialog>

    <!-- 批量操作对话框 -->
    <el-dialog
      v-model="showBatchOperationDialog"
      title="批量操作"
      width="50%"
    >
      <batch-operation-form
        :selected-requirements="selectedRequirements"
        :statuses="statuses"
        :priorities="priorities"
        :tags="allTags"
        @submit="handleBatchOperation"
        @cancel="showBatchOperationDialog = false"
      />
    </el-dialog>

    <!-- 统计信息对话框 -->
    <el-dialog
      v-model="showStatistics"
      title="需求统计信息"
      width="80%"
    >
      <requirement-statistics
        v-if="showStatistics"
        @close="showStatistics = false"
      />
    </el-dialog>

    <!-- 生成测试用例对话框 -->
    <el-dialog
      v-model="showGenerateTestCasesDialog"
      title="生成测试用例"
      width="60%"
    >
      <generate-test-cases-form
        v-if="selectedRequirement"
        :requirement="selectedRequirement"
        @submit="handleGenerateTestCases"
        @cancel="showGenerateTestCasesDialog = false"
      />
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Document, Operation, DataAnalysis, Download, ArrowDown, Search } from '@element-plus/icons-vue'
import requirementApi from '@/services/requirementApi'
import RequirementForm from '@/components/RequirementForm.vue'
import DocumentParseForm from '@/components/DocumentParseForm.vue'
import RequirementDetail from '@/components/RequirementDetail.vue'
import BatchOperationForm from '@/components/BatchOperationForm.vue'
import RequirementStatistics from '@/components/RequirementStatistics.vue'
import GenerateTestCasesForm from '@/components/GenerateTestCasesForm.vue'

export default {
  name: 'RequirementManagement',
  components: {
    RequirementForm,
    DocumentParseForm,
    RequirementDetail,
    BatchOperationForm,
    RequirementStatistics,
    GenerateTestCasesForm
  },
  setup() {
    // 响应式数据
    const loading = ref(false)
    const requirements = ref([])
    const selectedRequirements = ref([])
    const selectedRequirement = ref(null)
    const editingRequirement = ref(null)

    // 对话框显示状态
    const showCreateDialog = ref(false)
    const showParseDialog = ref(false)
    const showDetailDialog = ref(false)
    const showBatchOperationDialog = ref(false)
    const showStatistics = ref(false)
    const showGenerateTestCasesDialog = ref(false)

    // 搜索表单
    const searchForm = reactive({
      keyword: '',
      requirement_type: '',
      protocol_type: '',
      priority: '',
      status: '',
      complexity: '',
      testability: '',
      category: '',
      module: '',
      author: '',
      tags: ''
    })

    // 分页
    const pagination = reactive({
      page: 1,
      pageSize: 20,
      total: 0
    })

    // 枚举数据
    const requirementTypes = ref([])
    const protocolTypes = ref([])
    const priorities = ref([])
    const statuses = ref([])
    const complexities = ref([])
    const testabilities = ref([])
    const documentTypes = ref([])

    // 选项数据
    const categories = ref([])
    const modules = ref([])
    const authors = ref([])
    const reviewers = ref([])
    const allTags = ref([])

    // 格式化日期
    const formatDate = (dateString) => {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN', { 
        hour: '2-digit', 
        minute: '2-digit' 
      })
    }

    // 获取标签颜色和标签的辅助函数
    const getRequirementTypeColor = (type) => {
      const colorMap = {
        'functional': 'primary',
        'performance': 'warning',
        'security': 'danger',
        'compatibility': 'info',
        'reliability': 'success',
        'usability': '',
        'protocol_specific': 'primary'
      }
      return colorMap[type] || ''
    }

    const getRequirementTypeLabel = (type) => {
      const labelMap = {
        'functional': '功能性',
        'performance': '性能',
        'security': '安全',
        'compatibility': '兼容性',
        'reliability': '可靠性',
        'usability': '易用性',
        'protocol_specific': '协议特定'
      }
      return labelMap[type] || type
    }

    const getPriorityColor = (priority) => {
      const colorMap = {
        'low': 'info',
        'medium': '',
        'high': 'warning',
        'critical': 'danger'
      }
      return colorMap[priority] || ''
    }

    const getPriorityLabel = (priority) => {
      const labelMap = {
        'low': '低',
        'medium': '中',
        'high': '高',
        'critical': '紧急'
      }
      return labelMap[priority] || priority
    }

    const getStatusColor = (status) => {
      const colorMap = {
        'draft': 'info',
        'reviewing': 'warning',
        'approved': 'success',
        'rejected': 'danger',
        'implementing': 'primary',
        'testing': 'warning',
        'completed': 'success',
        'deprecated': 'info'
      }
      return colorMap[status] || ''
    }

    const getStatusLabel = (status) => {
      const labelMap = {
        'draft': '草稿',
        'reviewing': '评审中',
        'approved': '已批准',
        'rejected': '已拒绝',
        'implementing': '实现中',
        'testing': '测试中',
        'completed': '已完成',
        'deprecated': '已废弃'
      }
      return labelMap[status] || status
    }

    const getComplexityColor = (complexity) => {
      const colorMap = {
        'simple': 'success',
        'medium': '',
        'complex': 'warning',
        'very_complex': 'danger'
      }
      return colorMap[complexity] || ''
    }

    const getComplexityLabel = (complexity) => {
      const labelMap = {
        'simple': '简单',
        'medium': '中等',
        'complex': '复杂',
        'very_complex': '非常复杂'
      }
      return labelMap[complexity] || complexity
    }

    // 加载枚举数据
    const loadEnums = async () => {
      try {
        const [
          typesResponse,
          protocolsResponse,
          prioritiesResponse,
          statusesResponse,
          complexitiesResponse,
          testabilitiesResponse,
          documentTypesResponse
        ] = await Promise.all([
          requirementApi.getRequirementTypes(),
          requirementApi.getProtocolTypes(),
          requirementApi.getPriorities(),
          requirementApi.getStatuses(),
          requirementApi.getComplexities(),
          requirementApi.getTestabilities(),
          requirementApi.getDocumentTypes()
        ])

        requirementTypes.value = typesResponse || []
        protocolTypes.value = protocolsResponse || []
        priorities.value = prioritiesResponse || []
        statuses.value = statusesResponse || []
        complexities.value = complexitiesResponse || []
        testabilities.value = testabilitiesResponse || []
        documentTypes.value = documentTypesResponse || []
      } catch (error) {
        ElMessage.error('加载枚举数据失败: ' + error.message)
      }
    }

    // 加载选项数据
    const loadOptions = async () => {
      try {
        const [
          categoriesResponse,
          modulesResponse,
          authorsResponse,
          reviewersResponse,
          tagsResponse
        ] = await Promise.all([
          requirementApi.getCategories(),
          requirementApi.getModules(),
          requirementApi.getAuthors(),
          requirementApi.getReviewers(),
          requirementApi.getTags()
        ])

        categories.value = categoriesResponse || []
        modules.value = modulesResponse || []
        authors.value = authorsResponse || []
        reviewers.value = reviewersResponse || []
        allTags.value = tagsResponse || []
      } catch (error) {
        console.warn('加载选项数据失败:', error.message)
      }
    }

    // 加载需求列表
    const loadRequirements = async () => {
      loading.value = true
      try {
        const params = {
          page: pagination.page,
          page_size: pagination.pageSize,
          ...searchForm,
          tags: searchForm.tags ? searchForm.tags : undefined
        }

        // 移除空值
        Object.keys(params).forEach(key => {
          if (params[key] === '' || params[key] === null || params[key] === undefined) {
            delete params[key]
          }
        })

        const response = await requirementApi.getRequirements(params)
        requirements.value = response.items || []
        pagination.total = response.total || 0
      } catch (error) {
        ElMessage.error('加载需求列表失败: ' + error.message)
      } finally {
        loading.value = false
      }
    }

    // 搜索
    const handleSearch = () => {
      pagination.page = 1
      loadRequirements()
    }

    // 分页变化
    const handlePageChange = (page) => {
      pagination.page = page
      loadRequirements()
    }

    const handlePageSizeChange = (pageSize) => {
      pagination.pageSize = pageSize
      pagination.page = 1
      loadRequirements()
    }

    // 重置搜索
    const resetSearch = () => {
      Object.keys(searchForm).forEach(key => {
        searchForm[key] = ''
      })
      handleSearch()
    }

    // 选择变化
    const handleSelectionChange = (selection) => {
      selectedRequirements.value = selection
    }

    // 行点击
    const handleRowClick = (row) => {
      selectedRequirement.value = row
      showDetailDialog.value = true
    }

    // 查看需求详情
    const viewRequirement = (requirement) => {
      selectedRequirement.value = requirement
      showDetailDialog.value = true
    }

    // 编辑需求
    const editRequirement = (requirement) => {
      editingRequirement.value = { ...requirement }
      showCreateDialog.value = true
      showDetailDialog.value = false
    }

    // 删除需求
    const deleteRequirement = async (requirement) => {
      try {
        await ElMessageBox.confirm(
          `确定要删除需求 "${requirement.title}" 吗？此操作不可恢复。`,
          '确认删除',
          {
            confirmButtonText: '删除',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )

        await requirementApi.deleteRequirement(requirement.id)
        ElMessage.success('删除成功')
        loadRequirements()
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error('删除失败: ' + error.message)
        }
      }
    }

    // 生成测试用例
    const generateTestCases = (requirement) => {
      selectedRequirement.value = requirement
      showGenerateTestCasesDialog.value = true
      showDetailDialog.value = false
    }

    // 处理需求提交
    const handleRequirementSubmit = async (requirementData) => {
      try {
        if (editingRequirement.value) {
          await requirementApi.updateRequirement(editingRequirement.value.id, requirementData)
          ElMessage.success('更新成功')
        } else {
          await requirementApi.createRequirement(requirementData)
          ElMessage.success('创建成功')
        }
        
        showCreateDialog.value = false
        editingRequirement.value = null
        loadRequirements()
        loadOptions() // 重新加载选项数据
      } catch (error) {
        ElMessage.error('保存失败: ' + error.message)
      }
    }

    // 处理文档解析
    const handleDocumentParse = async (parseRequest) => {
      try {
        loading.value = true
        const response = await requirementApi.parseProtocolDocument(parseRequest)
        
        ElMessage.success(`文档解析成功，共提取 ${response.total_requirements} 个需求`)
        showParseDialog.value = false
        loadRequirements()
        loadOptions()
      } catch (error) {
        ElMessage.error('文档解析失败: ' + error.message)
      } finally {
        loading.value = false
      }
    }

    // 处理批量操作
    const handleBatchOperation = async (operation) => {
      try {
        const response = await requirementApi.batchOperationRequirements(operation)
        ElMessage.success(response.message)
        showBatchOperationDialog.value = false
        selectedRequirements.value = []
        loadRequirements()
      } catch (error) {
        ElMessage.error('批量操作失败: ' + error.message)
      }
    }

    // 处理生成测试用例
    const handleGenerateTestCases = async (params) => {
      try {
        const response = await requirementApi.generateTestCasesForRequirement(
          selectedRequirement.value.id,
          params.count,
          params.focusAreas.join(',')
        )
        
        ElMessage.success(response.message)
        showGenerateTestCasesDialog.value = false
        
        // 可以跳转到测试用例管理页面
        this.$router.push('/test-cases')
      } catch (error) {
        ElMessage.error('生成测试用例失败: ' + error.message)
      }
    }

    // 处理导出
    const handleExport = async (format) => {
      try {
        const requirementIds = selectedRequirements.value.length > 0 
          ? selectedRequirements.value.map(req => req.id)
          : null

        const response = await requirementApi.exportRequirements(format, requirementIds)
        ElMessage.success(response.message)
      } catch (error) {
        ElMessage.error('导出失败: ' + error.message)
      }
    }

    // 生命周期
    onMounted(() => {
      loadEnums()
      loadOptions()
      loadRequirements()
    })

    return {
      // 响应式数据
      loading,
      requirements,
      selectedRequirements,
      selectedRequirement,
      editingRequirement,
      
      // 对话框状态
      showCreateDialog,
      showParseDialog,
      showDetailDialog,
      showBatchOperationDialog,
      showStatistics,
      showGenerateTestCasesDialog,
      
      // 表单和分页
      searchForm,
      pagination,
      
      // 枚举和选项数据
      requirementTypes,
      protocolTypes,
      priorities,
      statuses,
      complexities,
      testabilities,
      documentTypes,
      categories,
      modules,
      authors,
      reviewers,
      allTags,
      
      // 方法
      formatDate,
      getRequirementTypeColor,
      getRequirementTypeLabel,
      getPriorityColor,
      getPriorityLabel,
      getStatusColor,
      getStatusLabel,
      getComplexityColor,
      getComplexityLabel,
      handleSearch,
      handlePageChange,
      handlePageSizeChange,
      resetSearch,
      handleSelectionChange,
      handleRowClick,
      viewRequirement,
      editRequirement,
      deleteRequirement,
      generateTestCases,
      handleRequirementSubmit,
      handleDocumentParse,
      handleBatchOperation,
      handleGenerateTestCases,
      handleExport,
      
      // 图标
      Plus,
      Document,
      Operation,
      DataAnalysis,
      Download,
      ArrowDown,
      Search
    }
  }
}
</script>

<style scoped>
.requirement-management {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h2 {
  margin: 0;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.search-filters {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.requirements-table {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.pagination {
  display: flex;
  justify-content: center;
  padding: 20px;
}

.el-table {
  border-radius: 8px;
}

.el-table .el-table__header-wrapper th {
  background-color: #fafafa;
  font-weight: 600;
}

.el-table .el-table__row:hover > td {
  background-color: #f5f7fa;
}
</style>

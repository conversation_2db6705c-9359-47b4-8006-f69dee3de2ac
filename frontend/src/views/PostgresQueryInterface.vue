<template>
  <div class="postgres-query-interface">
    <el-row :gutter="20">
      <!-- 左侧查询区域 -->
      <el-col :span="16">
        <el-card class="query-card">
          <template #header>
            <div class="card-header">
              <span>PostgreSQL抓包</span>
              <div class="header-controls">
                <el-select
                  v-model="selectedConfigId"
                  placeholder="选择PostgreSQL配置"
                  style="width: 200px; margin-right: 10px;"
                  @change="onDatabaseChange"
                >
                  <el-option
                    v-for="config in postgresConfigs"
                    :key="config.id"
                    :label="`${config.name} (${config.host}:${config.port})`"
                    :value="config.id"
                  >
                    <span>{{ config.name }}</span>
                    <el-tag v-if="config.is_default" type="success" size="small" style="margin-left: 10px;">默认</el-tag>
                  </el-option>
                </el-select>
                <el-switch
                  v-model="autoCapture"
                  active-text="自动抓包"
                  inactive-text="手动抓包"
                />
              </div>
            </div>
          </template>
          
          <div class="query-input">
            <el-input
              v-model="naturalQuery"
              type="textarea"
              :rows="4"
              placeholder="请输入自然语言查询，例如：查询所有用户信息、统计订单数量等..."
              @keydown.ctrl.enter="executeQuery"
            />
            <div class="query-actions">
              <el-button
                type="primary"
                @click="executeQuery"
                :loading="queryLoading"
                :disabled="!naturalQuery.trim() || !selectedConfigId"
              >
                <el-icon><Search /></el-icon>
                执行查询
              </el-button>
              <el-button
                type="info"
                @click="executeAsyncCapture"
                :loading="asyncCaptureLoading"
                :disabled="!naturalQuery.trim() || !selectedConfigId"
              >
                <el-icon><Monitor /></el-icon>
                异步抓包
              </el-button>
              <el-button @click="clearQuery">
                <el-icon><Delete /></el-icon>
                清空
              </el-button>
            </div>
          </div>
        </el-card>

        <!-- PostgreSQL查询预览和结果 -->
        <el-card class="result-card" v-if="queryResult">
          <template #header>
            <div class="card-header">
              <span>查询结果</span>
              <el-tag :type="queryResult.success ? 'success' : 'danger'">
                {{ queryResult.success ? '成功' : '失败' }}
              </el-tag>
            </div>
          </template>
          
          <!-- 生成的SQL -->
          <div v-if="queryResult.sql_query" class="sql-section">
            <h4>生成的SQL:</h4>
            <el-input
              v-model="queryResult.sql_query"
              type="textarea"
              :rows="3"
              readonly
              class="sql-display"
            />
          </div>
          
          <!-- 查询结果 -->
          <div v-if="queryResult && queryResult.result" class="result-section">
            <h4>执行结果:</h4>
            <div v-if="queryResult.result && queryResult.result.type === 'query'" class="table-result">
              <el-table
                :data="queryResult.result.data || []"
                border
                stripe
                max-height="400"
                style="width: 100%"
              >
                <el-table-column
                  v-for="column in getTableColumns(queryResult.result.data || [])"
                  :key="column"
                  :prop="column"
                  :label="column"
                  show-overflow-tooltip
                />
              </el-table>
              <div class="result-info">
                共 {{ queryResult.result.count || (queryResult.result.data ? queryResult.result.data.length : 0) }} 条记录
              </div>
            </div>
            <div v-else-if="queryResult.result && queryResult.result.message" class="modification-result">
              <el-alert
                :title="queryResult.result.message"
                type="success"
                :closable="false"
              />
            </div>
          </div>
          
          <!-- 抓包文件信息 -->
          <div v-if="queryResult.packet_file" class="packet-info">
            <h4>数据包文件:</h4>
            <el-tag type="info">{{ queryResult.packet_file }}</el-tag>
          </div>
          
          <!-- 错误信息 -->
          <div v-if="queryResult.error" class="error-section">
            <h4>错误信息:</h4>
            <el-alert
              :title="queryResult.error"
              type="error"
              :closable="false"
            />
          </div>
        </el-card>
      </el-col>
      
      <!-- 右侧信息区域 -->
      <el-col :span="8">
        <!-- 数据库信息 -->
        <el-card class="info-card" v-if="selectedConfig">
          <template #header>
            <span>当前数据库</span>
          </template>
          <div class="database-info">
            <p><strong>名称:</strong> {{ selectedConfig.name }}</p>
            <p><strong>主机:</strong> {{ selectedConfig.host }}:{{ selectedConfig.port }}</p>
            <p><strong>数据库:</strong> {{ selectedConfig.database_name }}</p>
            <p><strong>类型:</strong> PostgreSQL</p>
          </div>
        </el-card>
        
        <!-- PostgreSQL信息 -->
        <el-card class="info-card">
          <template #header>
            <div class="card-header">
              <span>PostgreSQL信息</span>
              <el-button
                icon="Refresh"
                size="small"
                @click="loadPostgresInfo"
                :loading="dbLoading"
              />
            </div>
          </template>
          <div v-if="databases.length > 0">
            <el-collapse v-model="activeCollapse">
              <el-collapse-item
                v-for="database in databases"
                :key="database"
                :title="database"
                :name="database"
              >
                <div v-if="tables[database]">
                  <div v-for="table in tables[database]" :key="table" class="table-item">
                    <el-icon><Document /></el-icon>
                    {{ table }}
                  </div>
                </div>
                <el-button
                  v-else
                  size="small"
                  @click="loadTables(database)"
                  :loading="tableLoading[database]"
                >
                  加载表信息
                </el-button>
              </el-collapse-item>
            </el-collapse>
          </div>
          <div v-else class="no-data">
            <el-empty description="暂无数据库信息" />
          </div>
        </el-card>

        <!-- 查询历史 -->
        <el-card class="history-card">
          <template #header>
            <span>查询历史</span>
          </template>
          <div class="history-list">
            <div
              v-for="(item, index) in queryHistory"
              :key="index"
              class="history-item"
              @click="loadHistoryQuery(item)"
            >
              <div class="history-query">{{ item.query }}</div>
              <div class="history-time">{{ item.timestamp }}</div>
              <el-tag 
                :type="item && item.result && item.result.success ? 'success' : 'danger'"
                size="small"
              >
                {{ item && item.result && item.result.success ? '成功' : '失败' }}
              </el-tag>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Delete, Document, Refresh, Monitor } from '@element-plus/icons-vue'
import apiService from '../services/api'

// 响应式数据
const naturalQuery = ref('')
const queryResult = ref(null)
const queryLoading = ref(false)
const asyncCaptureLoading = ref(false)
const autoCapture = ref(true)
const selectedConfigId = ref(null)
const postgresConfigs = ref([])
const queryHistory = ref([])
const databases = ref([])
const tables = reactive({})
const dbLoading = ref(false)
const tableLoading = reactive({})
const activeCollapse = ref([])

// 计算属性
const selectedConfig = computed(() => {
  return postgresConfigs.value.find(config => config.id === selectedConfigId.value)
})

// 方法
const loadPostgresConfigs = async () => {
  try {
    const response = await apiService.getDatabaseConfigs()
    // 只显示PostgreSQL配置
    postgresConfigs.value = (response.databases || []).filter(db => db.database_type === 'postgresql')
    
    // 自动选择默认配置
    const defaultConfig = postgresConfigs.value.find(config => config.is_default)
    if (defaultConfig) {
      selectedConfigId.value = defaultConfig.id
    } else if (postgresConfigs.value.length > 0) {
      selectedConfigId.value = postgresConfigs.value[0].id
    }
  } catch (error) {
    ElMessage.error('加载PostgreSQL配置失败: ' + error.message)
  }
}

const onDatabaseChange = (configId) => {
  selectedConfigId.value = configId
  // 重新加载PostgreSQL信息
  loadPostgresInfo()
}

const loadPostgresInfo = async () => {
  if (!selectedConfigId.value) return

  dbLoading.value = true
  try {
    // 测试连接并获取数据库信息
    const result = await apiService.testPostgresConnection(selectedConfigId.value)
    if (result.success) {
      databases.value = result.databases || []
      // 清空之前的表信息
      Object.keys(tables).forEach(key => delete tables[key])
    } else {
      databases.value = []
      ElMessage.error('连接PostgreSQL失败: ' + (result.error || '未知错误'))
    }
  } catch (error) {
    databases.value = []
    ElMessage.error('加载PostgreSQL信息失败: ' + error.message)
  } finally {
    dbLoading.value = false
  }
}

const loadTables = async (database) => {
  if (!selectedConfigId.value) return

  tableLoading[database] = true
  try {
    const result = await apiService.getPostgresTables('public', selectedConfigId.value)
    tables[database] = result.tables || []
  } catch (error) {
    ElMessage.error(`加载数据库 ${database} 的表信息失败: ` + error.message)
  } finally {
    tableLoading[database] = false
  }
}

const executeQuery = async () => {
  if (!naturalQuery.value.trim()) {
    ElMessage.warning('请输入查询内容')
    return
  }
  
  if (!selectedConfigId.value) {
    ElMessage.warning('请选择PostgreSQL配置')
    return
  }
  
  queryLoading.value = true
  try {
    const result = await apiService.processPostgresQuery(
      naturalQuery.value,
      autoCapture.value,
      selectedConfigId.value
    )
    
    queryResult.value = result
    
    // 添加到历史记录
    queryHistory.value.unshift({
      query: naturalQuery.value,
      timestamp: new Date().toLocaleString(),
      result: result
    })
    
    // 限制历史记录数量
    if (queryHistory.value.length > 10) {
      queryHistory.value = queryHistory.value.slice(0, 10)
    }
    
    if (result.success) {
      ElMessage.success('PostgreSQL查询执行成功')
    } else {
      ElMessage.error('PostgreSQL查询执行失败')
    }
    
  } catch (error) {
    ElMessage.error('查询失败: ' + error.message)
    queryResult.value = {
      success: false,
      error: error.message
    }
  } finally {
    queryLoading.value = false
  }
}

const clearQuery = () => {
  naturalQuery.value = ''
  queryResult.value = null
}

const executeAsyncCapture = async () => {
  if (!naturalQuery.value.trim()) {
    ElMessage.warning('请输入查询内容')
    return
  }

  if (!selectedConfigId.value) {
    ElMessage.warning('请选择PostgreSQL配置')
    return
  }

  asyncCaptureLoading.value = true
  try {
    // 直接创建AI+PostgreSQL异步抓包任务（包含大模型请求）
    const result = await apiService.createAIPostgreSQLTask(
      selectedConfigId.value,
      naturalQuery.value,
      30 // 默认30秒抓包时长
    )

    ElMessage.success(`AI异步抓包任务已创建，任务ID: ${result.task_id}`)

    // 添加到历史记录
    queryHistory.value.unshift({
      query: naturalQuery.value,
      timestamp: new Date().toLocaleString(),
      result: {
        success: true,
        task_id: result.task_id,
        type: 'ai_async_capture',
        message: '任务已提交，请在任务管理中查看进度'
      }
    })

    // 限制历史记录数量
    if (queryHistory.value.length > 10) {
      queryHistory.value = queryHistory.value.slice(0, 10)
    }

  } catch (error) {
    ElMessage.error('创建异步抓包任务失败: ' + error.message)
  } finally {
    asyncCaptureLoading.value = false
  }
}

const loadHistoryQuery = (historyItem) => {
  naturalQuery.value = historyItem.query
  queryResult.value = historyItem.result
}

const getTableColumns = (data) => {
  if (!data || data.length === 0 || !data[0]) return []
  return Object.keys(data[0])
}

// 生命周期
onMounted(() => {
  loadPostgresConfigs().then(() => {
    // 配置加载完成后，加载PostgreSQL信息
    if (selectedConfigId.value) {
      loadPostgresInfo()
    }
  })
})
</script>

<style scoped>
.postgres-query-interface {
  padding: 20px;
}

.query-card, .result-card, .info-card, .history-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.query-input {
  margin-bottom: 20px;
}

.query-actions {
  margin-top: 10px;
  display: flex;
  gap: 10px;
}

.sql-section, .result-section, .packet-info, .error-section {
  margin-bottom: 20px;
}

.sql-display {
  font-family: 'Courier New', monospace;
}

.result-info {
  margin-top: 10px;
  color: #666;
  font-size: 14px;
}

.database-info p {
  margin: 8px 0;
  color: #666;
}

.history-list {
  max-height: 300px;
  overflow-y: auto;
}

.history-item {
  padding: 10px;
  border-bottom: 1px solid #eee;
  cursor: pointer;
  transition: background-color 0.2s;
}

.history-item:hover {
  background-color: #f5f5f5;
}

.history-query {
  font-size: 14px;
  margin-bottom: 5px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.history-time {
  font-size: 12px;
  color: #999;
  margin-bottom: 5px;
}

.table-result {
  margin-top: 10px;
}

.modification-result {
  margin-top: 10px;
}

.table-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 0;
  color: #606266;
}

.no-data {
  text-align: center;
  padding: 20px;
}
</style>

<template>
  <div class="download-manager">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>PCAP包下载管理</span>
          <div class="header-buttons">
            <el-button @click="loadCaptureFiles" :loading="loading">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <el-button
              type="success"
              @click="downloadSelected"
              :disabled="selectedFiles.length === 0"
              :loading="batchDownloading"
            >
              <el-icon><Download /></el-icon>
              批量下载 ({{ selectedFiles.length }})
            </el-button>
            <el-button
              type="danger"
              @click="deleteSelected"
              :disabled="selectedFiles.length === 0"
              :loading="batchDeleting"
            >
              <el-icon><Delete /></el-icon>
              批量删除 ({{ selectedFiles.length }})
            </el-button>
            <el-button type="danger" @click="clearAllFiles" :loading="clearing">
              <el-icon><Delete /></el-icon>
              清空所有
            </el-button>
          </div>
        </div>
      </template>

      <!-- 统计信息 -->
      <div class="stats-section">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-statistic title="总文件数" :value="totalFiles" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="总大小" :value="totalSize" suffix="MB" :precision="2" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="MySQL包" :value="mysqlFiles" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="MongoDB包" :value="mongoFiles" />
          </el-col>
        </el-row>
        <el-row :gutter="20" style="margin-top: 20px;">
          <el-col :span="6">
            <el-statistic title="PostgreSQL包" :value="postgresFiles" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="GaussDB包" :value="gaussdbFiles" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="Oracle包" :value="oracleFiles" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="最新文件" :value="latestFileTime" />
          </el-col>
        </el-row>
      </div>

      <!-- 文件列表 -->
      <el-table
        :data="captureFiles"
        style="width: 100%; margin-top: 20px;"
        v-loading="loading"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="filename" label="文件名" min-width="250">
          <template #default="scope">
            <div class="file-name-cell">
              <el-tag
                :type="getFileTypeColor(scope.row.database_type)"
                size="small"
                class="file-type-tag"
              >
                {{ getFileTypeName(scope.row.database_type) }}
              </el-tag>
              <span class="file-name" :title="scope.row.filename">{{ scope.row.filename }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="file_size" label="文件大小" width="120" sortable>
          <template #default="scope">
            <span class="file-size">{{ formatFileSize(scope.row.file_size) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="created_time" label="创建时间" width="180" sortable>
          <template #default="scope">
            <el-tooltip :content="formatTime(scope.row.created_time)" placement="top">
              <span class="file-time">{{ formatTimeShort(scope.row.created_time) }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="220" fixed="right">
          <template #default="scope">
            <div class="action-buttons">
              <el-tooltip content="下载文件" placement="top">
                <el-button
                  type="primary"
                  size="small"
                  @click="downloadFile(scope.row)"
                  :loading="scope.row.downloading"
                  circle
                >
                  <el-icon><Download /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="查看详情" placement="top">
                <el-button
                  type="info"
                  size="small"
                  @click="viewFileInfo(scope.row)"
                  circle
                >
                  <el-icon><View /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="复制文件名" placement="top">
                <el-button
                  type="success"
                  size="small"
                  @click="copyFileName(scope.row.filename)"
                  circle
                >
                  <el-icon><CopyDocument /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="删除文件" placement="top">
                <el-button
                  type="danger"
                  size="small"
                  @click="deleteFile(scope.row)"
                  :loading="scope.row.deleting"
                  circle
                >
                  <el-icon><Delete /></el-icon>
                </el-button>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 文件详情对话框 -->
    <el-dialog title="PCAP文件详情" v-model="showFileInfo" width="800px">
      <div v-if="selectedFile">
        <el-descriptions title="文件信息" :column="2" border>
          <el-descriptions-item label="文件名">{{ selectedFile.filename }}</el-descriptions-item>
          <el-descriptions-item label="文件类型">{{ getFileTypeName(selectedFile.database_type) }}</el-descriptions-item>
          <el-descriptions-item label="文件大小">{{ formatFileSize(selectedFile.file_size) }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatTime(selectedFile.created_time) }}</el-descriptions-item>
          <el-descriptions-item label="数据包数">{{ selectedFile.packets || 0 }}</el-descriptions-item>
          <el-descriptions-item label="文件路径">{{ selectedFile.file_path }}</el-descriptions-item>
          <el-descriptions-item label="目标主机">{{ selectedFile.target_host }}:{{ selectedFile.target_port }}</el-descriptions-item>
          <el-descriptions-item label="描述">{{ selectedFile.description }}</el-descriptions-item>
        </el-descriptions>
        
        <div v-if="selectedFile.analysis" style="margin-top: 20px;">
          <h4>抓包分析</h4>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="总数据包">{{ selectedFile.analysis.total_packets }}</el-descriptions-item>
            <el-descriptions-item label="有效数据包">{{ selectedFile.analysis.valid_packets }}</el-descriptions-item>
            <el-descriptions-item label="连接数">{{ selectedFile.analysis.connections }}</el-descriptions-item>
            <el-descriptions-item label="检测到的操作">{{ selectedFile.analysis.operations }}</el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showFileInfo = false">关闭</el-button>
          <el-button type="primary" @click="downloadFile(selectedFile)">
            <el-icon><Download /></el-icon>
            下载文件
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh, Delete, Download, View, CopyDocument } from '@element-plus/icons-vue'
import apiService from '../services/api'

// 响应式数据
const captureFiles = ref([])
const loading = ref(false)
const clearing = ref(false)
const showFileInfo = ref(false)
const selectedFile = ref(null)
const selectedFiles = ref([])
const batchDownloading = ref(false)
const batchDeleting = ref(false)

// 计算属性
const totalFiles = computed(() => captureFiles.value.length)
const totalSize = computed(() => {
  return captureFiles.value.reduce((sum, file) => sum + (file.file_size || 0), 0) / (1024 * 1024)
})
const mysqlFiles = computed(() => captureFiles.value.filter(f => f.database_type === 'mysql').length)
const mongoFiles = computed(() => captureFiles.value.filter(f => f.database_type === 'mongodb').length)
const postgresFiles = computed(() => captureFiles.value.filter(f => f.database_type === 'postgresql').length)
const gaussdbFiles = computed(() => captureFiles.value.filter(f => f.database_type === 'gaussdb').length)
const oracleFiles = computed(() => captureFiles.value.filter(f => f.database_type === 'oracle').length)
const latestFileTime = computed(() => {
  if (captureFiles.value.length === 0) return '-'
  const latest = captureFiles.value.reduce((latest, file) => {
    return new Date(file.created_time) > new Date(latest.created_time) ? file : latest
  })
  return formatTime(latest.created_time)
})

// 方法
const loadCaptureFiles = async () => {
  loading.value = true
  try {
    const response = await apiService.getCaptureFiles()
    captureFiles.value = response.files || []
  } catch (error) {
    ElMessage.error('加载抓包文件失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

const downloadFile = async (file) => {
  try {
    const response = await fetch(`/api/captures/download/${file.filename}`)
    if (!response.ok) throw new Error('下载失败')

    const blob = await response.blob()
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = file.filename
    document.body.appendChild(a)
    a.click()
    window.URL.revokeObjectURL(url)
    document.body.removeChild(a)

    ElMessage.success('文件下载成功')
  } catch (error) {
    ElMessage.error('下载文件失败: ' + error.message)
  }
}

const viewFileInfo = (file) => {
  selectedFile.value = file
  showFileInfo.value = true
}

const deleteFile = async (file) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除文件 "${file.filename}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await apiService.deleteCaptureFile(file.filename)
    ElMessage.success('文件删除成功')
    await loadCaptureFiles()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除文件失败: ' + error.message)
    }
  }
}

const clearAllFiles = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空所有抓包文件吗？此操作不可恢复！',
      '确认清空',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    clearing.value = true
    await apiService.clearAllCaptureFiles()
    ElMessage.success('所有文件已清空')
    await loadCaptureFiles()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('清空文件失败: ' + error.message)
    }
  } finally {
    clearing.value = false
  }
}

const getFileTypeName = (type) => {
  const typeMap = {
    'mysql': 'MySQL',
    'mongodb': 'MongoDB',
    'postgresql': 'PostgreSQL',
    'gaussdb': 'GaussDB',
    'oracle': 'Oracle'
  }
  return typeMap[type] || type
}

const getFileTypeColor = (type) => {
  const colorMap = {
    'mysql': 'primary',
    'mongodb': 'success',
    'postgresql': 'warning',
    'gaussdb': 'danger',
    'oracle': 'info'
  }
  return colorMap[type] || 'info'
}

const formatFileSize = (bytes) => {
  if (!bytes) return '0 B'
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
}

const formatTime = (timeStr) => {
  if (!timeStr) return '-'
  // 强制使用+8时区
  const date = new Date(timeStr)
  return date.toLocaleString('zh-CN', {
    timeZone: 'Asia/Shanghai',
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  })
}

// 导入时区工具
import { formatTimeShort as formatTimeShortUtil } from '@/utils/timezone'

const formatTimeShort = (timeStr) => {
  return formatTimeShortUtil(timeStr)
}

const copyFileName = async (fileName) => {
  try {
    await navigator.clipboard.writeText(fileName)
    ElMessage.success('文件名已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

const handleSelectionChange = (selection) => {
  selectedFiles.value = selection
}

const downloadSelected = async () => {
  if (selectedFiles.value.length === 0) return

  batchDownloading.value = true
  let successCount = 0
  let failCount = 0

  for (const file of selectedFiles.value) {
    try {
      await downloadFile(file)
      successCount++
    } catch (error) {
      failCount++
    }
  }

  batchDownloading.value = false

  if (failCount === 0) {
    ElMessage.success(`成功下载 ${successCount} 个文件`)
  } else {
    ElMessage.warning(`下载完成：成功 ${successCount} 个，失败 ${failCount} 个`)
  }
}

const deleteSelected = async () => {
  if (selectedFiles.value.length === 0) return

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedFiles.value.length} 个文件吗？此操作不可恢复！`,
      '确认批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    batchDeleting.value = true
    let successCount = 0
    let failCount = 0

    for (const file of selectedFiles.value) {
      try {
        await apiService.deleteCaptureFile(file.filename)
        successCount++
      } catch (error) {
        failCount++
      }
    }

    batchDeleting.value = false

    if (failCount === 0) {
      ElMessage.success(`成功删除 ${successCount} 个文件`)
    } else {
      ElMessage.warning(`删除完成：成功 ${successCount} 个，失败 ${failCount} 个`)
    }

    await loadCaptureFiles()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败: ' + error.message)
    }
  } finally {
    batchDeleting.value = false
  }
}

// 生命周期
onMounted(() => {
  loadCaptureFiles()
})
</script>

<style scoped>
.download-manager {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-buttons {
  display: flex;
  gap: 10px;
}

.stats-section {
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 8px;
  margin-bottom: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.file-name-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-type-tag {
  flex-shrink: 0;
}

.file-name {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
}

.file-size {
  font-weight: 500;
  color: #606266;
}

.file-time {
  color: #909399;
  font-size: 12px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.action-buttons .el-button {
  margin: 0;
}
</style>

<template>
  <div class="gateway-server-management">
    <div class="header">
      <h2>网关服务器管理</h2>
      <div class="header-actions">
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          添加网关服务器
        </el-button>
        <el-button @click="showStatsDialog = true">
          <el-icon><DataAnalysis /></el-icon>
          统计信息
        </el-button>
        <el-button @click="showImportDialog = true">
          <el-icon><Upload /></el-icon>
          导入配置
        </el-button>
        <el-button @click="exportConfigs">
          <el-icon><Download /></el-icon>
          导出配置
        </el-button>
      </div>
    </div>

    <!-- 搜索栏 -->
    <div class="search-bar">
      <el-input
        v-model="searchIp"
        placeholder="根据IP地址搜索网关服务器"
        style="width: 300px; margin-right: 10px;"
        @keyup.enter="loadGateways"
        clearable
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
      </el-input>
      <el-button type="primary" @click="loadGateways">搜索</el-button>
      <el-button @click="clearSearch">清空</el-button>
    </div>

    <!-- 网关服务器列表 -->
    <el-table :data="gateways" v-loading="loading" style="width: 100%">
      <el-table-column prop="name" label="名称" width="150" />
      <el-table-column prop="host" label="主机地址" width="150" />
      <el-table-column prop="port" label="端口" width="80" />
      <el-table-column prop="gateway_type" label="类型" width="150">
        <template #default="scope">
          <el-tag :type="getGatewayTypeColor(scope.row.gateway_type)">
            {{ getGatewayTypeName(scope.row.gateway_type) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="kafka_enabled" label="Kafka" width="150">
        <template #default="scope">
          <el-tag :type="scope.row.kafka_enabled ? 'success' : 'info'">
            {{ scope.row.kafka_enabled ? '已启用' : '未启用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="is_active" label="状态" width="150">
        <template #default="scope">
          <el-tag :type="scope.row.is_active ? 'success' : 'danger'">
            {{ scope.row.is_active ? '激活' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="is_default" label="默认" width="150">
        <template #default="scope">
          <el-tag v-if="scope.row.is_default" type="primary">默认</el-tag>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right">
        <template #default="scope">
          <el-button size="small" @click="testConnection(scope.row)">测试</el-button>
          <el-button size="small" @click="editGateway(scope.row)">编辑</el-button>
          <!-- 移动设为默认按钮 -->
          <el-button 
              size="small"
              :disabled="scope.row.is_default"
              @click="setDefaultGateway(scope.row)"
          >
              设为默认
          </el-button>
          <!-- 移动删除按钮 -->
          <el-button 
              size="small"
              type="danger"
              @click="deleteGateway(scope.row)"
          >
              删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination">
      <el-pagination
        :current-page="currentPage"
        :page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 创建/编辑网关服务器对话框 -->
    <el-dialog 
      :title="editingGateway ? '编辑网关服务器' : '添加网关服务器'" 
      v-model="showCreateDialog"
      width="600px"
    >
      <el-form
        ref="gatewayFormRef"
        :model="gatewayForm"
        :rules="gatewayRules"
        label-width="120px"
        style="max-width: 600px"
      >
        <!-- 基本信息 -->
        <el-form-item label="名称" prop="name">
          <el-input v-model="gatewayForm.name" placeholder="请输入网关服务器名称" />
        </el-form-item>
        
        <el-form-item label="主机地址" prop="host">
          <el-input v-model="gatewayForm.host" placeholder="请输入服务器IP地址" />
        </el-form-item>
        
        <el-form-item label="端口" prop="port">
          <el-input-number v-model="gatewayForm.port" :min="1" :max="65535" />
        </el-form-item>
        
        <el-form-item label="用户名" prop="username">
          <el-input v-model="gatewayForm.username" placeholder="请输入用户名" />
        </el-form-item>
        
        <el-form-item label="密码" prop="password">
          <el-input 
            v-model="gatewayForm.password" 
            type="password" 
            placeholder="请输入密码" 
            show-password
          />
        </el-form-item>
        
        <el-form-item label="描述">
          <el-input 
            v-model="gatewayForm.description" 
            type="textarea" 
            placeholder="请输入描述信息"
          />
        </el-form-item>
        
        <el-form-item label="文件上传路径" prop="upload_path">
          <el-input v-model="gatewayForm.upload_path" placeholder="请输入文件上传路径" />
        </el-form-item>
        
        <el-form-item label="是否默认">
          <el-switch v-model="gatewayForm.is_default" />
        </el-form-item>
        
        <el-form-item label="是否激活">
          <el-switch v-model="gatewayForm.is_active" />
        </el-form-item>
        
        <!-- 高级配置 -->
        <el-divider>高级配置</el-divider>
        
        <el-form-item label="连接超时(秒)">
          <el-input-number v-model="gatewayForm.connection_timeout" :min="1" :max="300" />
        </el-form-item>
        
        <el-form-item label="最大连接数">
          <el-input-number v-model="gatewayForm.max_connections" :min="1" :max="100" />
        </el-form-item>
        
        <el-form-item label="网关类型">
          <el-select v-model="gatewayForm.gateway_type" placeholder="请选择网关类型">
            <el-option label="SSH" value="ssh" />
            <el-option label="SFTP" value="sftp" />
            <el-option label="FTP" value="ftp" />
          </el-select>
        </el-form-item>
        
        <!-- 代理配置 -->
        <el-divider>代理配置</el-divider>
        
        <el-form-item label="启用代理">
          <el-switch v-model="gatewayForm.proxy_enabled" />
        </el-form-item>
        
        <template v-if="gatewayForm.proxy_enabled">
          <el-form-item label="代理主机">
            <el-input v-model="gatewayForm.proxy_host" placeholder="请输入代理主机地址" />
          </el-form-item>
          
          <el-form-item label="代理端口">
            <el-input-number v-model="gatewayForm.proxy_port" :min="1" :max="65535" />
          </el-form-item>
        </template>
        
        <!-- Kafka配置 -->
        <el-divider>Kafka配置</el-divider>
        
        <el-form-item label="启用Kafka">
          <el-switch v-model="gatewayForm.kafka_enabled" />
        </el-form-item>
        
        <template v-if="gatewayForm.kafka_enabled">
          <el-form-item label="Kafka地址">
            <el-input v-model="gatewayForm.kafka_host" placeholder="请输入Kafka服务器IP地址" />
          </el-form-item>
          
          <el-form-item label="Kafka端口">
            <el-input-number v-model="gatewayForm.kafka_port" :min="1" :max="65535" />
          </el-form-item>
          
          <el-form-item label="Kafka主题">
            <el-input v-model="gatewayForm.kafka_topic" placeholder="请输入Kafka主题名称" />
          </el-form-item>
        </template>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="saveGateway" :loading="saving">保存</el-button>
      </template>
    </el-dialog>

    <!-- 连接测试结果对话框 -->
    <el-dialog title="连接测试结果" v-model="showTestDialog" width="600px">
      <div v-if="testResult">
        <el-alert
          :type="testResult.success ? 'success' : 'error'"
          :title="testResult.message"
          show-icon
          :closable="false"
        />

        <div v-if="testResult.success" class="test-details">
          <el-descriptions :column="2" border style="margin-top: 20px;">
            <el-descriptions-item label="响应时间">
              {{ testResult.response_time ? Math.round(testResult.response_time) + ' ms' : '未知' }}
            </el-descriptions-item>
            <el-descriptions-item label="上传路径测试">
              <el-tag :type="testResult.upload_test ? 'success' : 'danger'">
                {{ testResult.upload_test ? '可写' : '不可写' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="磁盘空间" v-if="testResult.disk_space">
              {{ testResult.disk_space }}
            </el-descriptions-item>
          </el-descriptions>

          <div v-if="testResult.server_info" class="server-info" style="margin-top: 20px;">
            <h4>服务器信息</h4>
            <p v-if="testResult.server_info.system"><strong>系统:</strong> {{ testResult.server_info.system }}</p>
            <p v-if="testResult.server_info.uptime"><strong>运行时间:</strong> {{ testResult.server_info.uptime }}</p>
          </div>
        </div>
      </div>
      <template #footer>
        <el-button @click="showTestDialog = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 统计信息对话框 -->
    <el-dialog title="网关服务器统计" v-model="showStatsDialog" width="500px">
      <div v-if="stats" class="stats-container">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-statistic title="总数量" :value="stats.total_gateways" />
          </el-col>
          <el-col :span="12">
            <el-statistic title="活跃数量" :value="stats.active_gateways" />
          </el-col>
        </el-row>
        
        <div v-if="stats.type_statistics" class="type-stats" style="margin-top: 20px;">
          <h4>按类型统计</h4>
          <el-table :data="formatTypeStats(stats.type_statistics)" size="small">
            <el-table-column prop="type" label="类型" />
            <el-table-column prop="count" label="数量" />
          </el-table>
        </div>
      </div>
      <template #footer>
        <el-button @click="loadStats">刷新</el-button>
        <el-button @click="showStatsDialog = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 导入配置对话框 -->
    <el-dialog title="导入网关配置" v-model="showImportDialog" width="500px">
      <div class="import-container">
        <p>请粘贴JSON格式的网关配置数据：</p>
        <el-input
          v-model="importData"
          type="textarea"
          :rows="8"
          placeholder="粘贴JSON配置数据..."
        />
        <div class="import-hint">
          <el-alert
            title="提示"
            type="info"
            :closable="false"
            show-icon
          >
            <template #default>
              <p>请确保JSON格式正确，配置中不应包含密码等敏感信息。</p>
              <p>导入后需要手动设置密码。</p>
            </template>
          </el-alert>
        </div>
      </div>
      <template #footer>
        <el-button @click="showImportDialog = false">取消</el-button>
        <el-button type="primary" @click="importConfigs" :loading="importing">导入</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import apiService from '@/services/api'

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const importing = ref(false)
const gateways = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)
const searchIp = ref('')

const showCreateDialog = ref(false)
const showTestDialog = ref(false)
const showStatsDialog = ref(false)
const showImportDialog = ref(false)
const editingGateway = ref(null)
const testResult = ref(null)
const stats = ref(null)
const importData = ref('')

// 网关服务器表单
const gatewayForm = reactive({
  name: '',
  host: '',
  port: 22,
  username: '',
  password: '',
  upload_path: '/tmp',
  description: '',
  is_default: false,
  connection_timeout: 30,
  max_connections: 10,
  gateway_type: 'ssh',
  proxy_enabled: false,
  proxy_host: '',
  proxy_port: null,
  // Kafka相关字段
  kafka_enabled: false,
  kafka_host: '',
  kafka_port: 9092,
  kafka_topic: ''
})

const gatewayRules = {
  name: [{ required: true, message: '请输入网关服务器名称', trigger: 'blur' }],
  host: [{ required: true, message: '请输入服务器IP地址', trigger: 'blur' }],
  username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
  upload_path: [{ required: true, message: '请输入文件上传路径', trigger: 'blur' }]
}

const gatewayFormRef = ref()

// 方法
const loadGateways = async () => {
  loading.value = true
  try {
    const response = await apiService.getGatewayServers(
      currentPage.value,
      pageSize.value,
      searchIp.value || null
    )
    gateways.value = response.gateways || []
    total.value = response.total || 0
  } catch (error) {
    ElMessage.error('加载网关服务器列表失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

const handleSizeChange = (newSize) => {
  pageSize.value = newSize
  currentPage.value = 1
  loadGateways()
}

const handleCurrentChange = (newPage) => {
  currentPage.value = newPage
  loadGateways()
}

const clearSearch = () => {
  searchIp.value = ''
  loadGateways()
}

const resetForm = () => {
  Object.assign(gatewayForm, {
    name: '',
    host: '',
    port: 22,
    username: '',
    password: '',
    upload_path: '/tmp',
    description: '',
    is_default: false,
    connection_timeout: 30,
    max_connections: 10,
    gateway_type: 'ssh',
    proxy_enabled: false,
    proxy_host: '',
    proxy_port: null
  })
  editingGateway.value = null
}

const editGateway = (gateway) => {
  editingGateway.value = gateway
  Object.assign(gatewayForm, {
    name: gateway.name,
    host: gateway.host,
    port: gateway.port,
    username: gateway.username,
    password: '', // 不显示密码
    upload_path: gateway.upload_path,
    description: gateway.description,
    is_default: gateway.is_default,
    connection_timeout: gateway.connection_timeout,
    max_connections: gateway.max_connections,
    gateway_type: gateway.gateway_type,
    proxy_enabled: gateway.proxy_enabled,
    proxy_host: gateway.proxy_host,
    proxy_port: gateway.proxy_port,
    // Kafka相关字段
    kafka_enabled: gateway.kafka_enabled,
    kafka_host: gateway.kafka_host,
    kafka_port: gateway.kafka_port,
    kafka_topic: gateway.kafka_topic
  })
  showCreateDialog.value = true
}

const saveGateway = async () => {
  if (!gatewayFormRef.value) return
  
  try {
    await gatewayFormRef.value.validate()
    saving.value = true
    
    if (editingGateway.value) {
      await apiService.updateGatewayServer(editingGateway.value.id, gatewayForm)
      ElMessage.success('网关服务器更新成功')
    } else {
      await apiService.createGatewayServer(gatewayForm)
      ElMessage.success('网关服务器创建成功')
    }
    
    showCreateDialog.value = false
    resetForm()
    await loadGateways()
  } catch (error) {
    if (error.message) {
      ElMessage.error('保存失败: ' + error.message)
    }
  } finally {
    saving.value = false
  }
}

const setDefaultGateway = (row) => {
    // 处理设为默认的逻辑
    ElMessageBox.confirm(
        `确定要将 ${row.name} 设为默认网关吗？`,
        '提示',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        }
    ).then(() => {
        apiService.setGatewayAsDefault(row.id)
            .then(response => {
                ElMessage.success('设置成功');
                loadGateways();
            })
            .catch(error => {
                ElMessage.error(`设置失败：${error.message}`);
            });
    }).catch(() => {
        ElMessage.info('已取消设置');
    });
};

const deleteGateway = (row) => {
    // 处理删除的逻辑
    ElMessageBox.confirm(
        `确定要删除 ${row.name} 吗？`,
        '提示',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        }
    ).then(() => {
        apiService.deleteGateway(row.id)
            .then(response => {
                ElMessage.success('删除成功');
                loadGateways();
            })
            .catch(error => {
                ElMessage.error(`删除失败：${error.message}`);
            });
    }).catch(() => {
        ElMessage.info('已取消删除');
    });
};

const testConnection = async (gateway) => {
  try {
    testResult.value = await apiService.testGatewayConnection(gateway.id)
    showTestDialog.value = true
  } catch (error) {
    ElMessage.error('连接测试失败: ' + error.message)
  }
}

const setAsDefault = async (gateway) => {
  try {
    await apiService.setDefaultGateway(gateway.id)
    ElMessage.success('默认网关设置成功')
    await loadGateways()
  } catch (error) {
    ElMessage.error('设置默认网关失败: ' + error.message)
  }
}

const loadStats = async () => {
  try {
    stats.value = await apiService.getGatewayStats()
  } catch (error) {
    ElMessage.error('加载统计信息失败: ' + error.message)
  }
}

const exportConfigs = async () => {
  try {
    const response = await apiService.exportGatewayConfigs()
    const dataStr = JSON.stringify(response.configs, null, 2)
    const blob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `gateway_configs_${new Date().toISOString().slice(0, 10)}.json`
    link.click()
    URL.revokeObjectURL(url)
    ElMessage.success('配置导出成功')
  } catch (error) {
    ElMessage.error('导出配置失败: ' + error.message)
  }
}

const importConfigs = async () => {
  if (!importData.value.trim()) {
    ElMessage.error('请输入配置数据')
    return
  }

  try {
    const configs = JSON.parse(importData.value)
    if (!Array.isArray(configs)) {
      throw new Error('配置数据应该是一个数组')
    }

    importing.value = true
    const result = await apiService.importGatewayConfigs(configs)

    if (result.imported > 0) {
      ElMessage.success(`成功导入 ${result.imported} 个配置`)
      await loadGateways()
    }

    if (result.failed > 0) {
      ElMessage.warning(`导入失败 ${result.failed} 个配置`)
      console.warn('Import errors:', result.errors)
    }

    showImportDialog.value = false
    importData.value = ''
  } catch (error) {
    ElMessage.error('导入失败: ' + error.message)
  } finally {
    importing.value = false
  }
}

// 辅助方法
const getGatewayTypeColor = (type) => {
  const colors = {
    ssh: 'primary',
    ftp: 'success',
    sftp: 'warning',
    scp: 'info'
  }
  return colors[type] || 'default'
}

const getGatewayTypeName = (type) => {
  const names = {
    ssh: 'SSH',
    ftp: 'FTP',
    sftp: 'SFTP',
    scp: 'SCP'
  }
  return names[type] || type.toUpperCase()
}

const formatTypeStats = (typeStats) => {
  return Object.entries(typeStats).map(([type, count]) => ({
    type: getGatewayTypeName(type),
    count
  }))
}

// 生命周期
onMounted(() => {
  loadGateways()
  loadStats()
})
</script>

<style scoped>
.gateway-server-management {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.search-bar {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.test-details {
  margin-top: 20px;
}

.server-info h4 {
  margin-bottom: 10px;
  color: #409eff;
}

.server-info p {
  margin: 5px 0;
  color: #606266;
}

.stats-container {
  padding: 10px;
}

.type-stats h4 {
  margin-bottom: 10px;
  color: #409eff;
}

.import-container {
  padding: 10px;
}

.import-hint {
  margin-top: 15px;
}

.import-hint p {
  margin: 5px 0;
}
</style>

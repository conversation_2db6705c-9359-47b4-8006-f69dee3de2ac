<template>
  <div class="c-executor-test">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>C语言执行器测试页面</span>
        </div>
      </template>
      
      <el-form :model="testForm" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="数据库配置">
              <el-select v-model="testForm.config_id" placeholder="选择数据库配置">
                <el-option
                  v-for="config in databaseConfigs"
                  :key="config.id"
                  :label="`${config.name} (${config.host}:${config.port})`"
                  :value="config.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="数据库类型">
              <el-select v-model="testForm.database_type" placeholder="选择数据库类型">
                <el-option label="GaussDB" value="gaussdb" />
                <el-option label="MySQL" value="mysql" />
                <el-option label="PostgreSQL" value="postgresql" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="执行选项">
              <div class="execution-options">
                <el-checkbox v-model="testForm.capture_enabled">启用抓包</el-checkbox>
                <el-checkbox v-model="testForm.async_execution">异步执行</el-checkbox>
                <el-checkbox
                  v-model="testForm.use_c_executor"
                  v-if="testForm.database_type === 'gaussdb' || testForm.database_type === 'postgresql'"
                  :title="'使用C语言执行器连接数据库，提供更好的性能'"
                >
                  使用C语言执行器
                  <el-tooltip
                    :content="getExecutorTooltip(testForm.database_type)"
                    placement="top"
                  >
                    <el-icon style="margin-left: 4px; color: #909399;"><QuestionFilled /></el-icon>
                  </el-tooltip>
                </el-checkbox>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="测试SQL">
              <el-input
                v-model="testForm.test_sql"
                type="textarea"
                :rows="4"
                placeholder="输入要测试的SQL语句"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row>
          <el-col :span="24">
            <el-form-item>
              <el-button type="primary" @click="executeTest" :loading="executing">
                执行测试
              </el-button>
              <el-button @click="resetForm">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      
      <!-- 执行结果 -->
      <el-divider>执行结果</el-divider>
      
      <div v-if="executionResult" class="result-section">
        <el-alert
          :title="executionResult.success ? '执行成功' : '执行失败'"
          :type="executionResult.success ? 'success' : 'error'"
          :description="executionResult.message"
          show-icon
          :closable="false"
        />
        
        <el-card v-if="executionResult.details" style="margin-top: 16px;">
          <template #header>
            <span>执行详情</span>
          </template>
          <pre>{{ JSON.stringify(executionResult.details, null, 2) }}</pre>
        </el-card>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { QuestionFilled } from '@element-plus/icons-vue'
import apiService from '@/services/api'

// 表单数据
const testForm = reactive({
  config_id: null,
  database_type: 'gaussdb',
  capture_enabled: true,
  async_execution: false,
  use_c_executor: false,
  test_sql: 'SELECT current_database(), current_user, version()'
})

// 数据库配置列表
const databaseConfigs = ref([])

// 执行状态
const executing = ref(false)
const executionResult = ref(null)

// 加载数据库配置
const loadDatabaseConfigs = async () => {
  try {
    const response = await apiService.getDatabaseConfigs()
    if (response.success) {
      databaseConfigs.value = response.data
    }
  } catch (error) {
    console.error('加载数据库配置失败:', error)
    ElMessage.error('加载数据库配置失败')
  }
}

// 执行测试
const executeTest = async () => {
  if (!testForm.config_id) {
    ElMessage.warning('请选择数据库配置')
    return
  }
  
  if (!testForm.test_sql.trim()) {
    ElMessage.warning('请输入测试SQL')
    return
  }
  
  executing.value = true
  executionResult.value = null
  
  try {
    // 构造测试用例
    const testCase = {
      id: 'c_executor_test',
      title: 'C语言执行器测试',
      database_type: testForm.database_type,
      test_steps: [
        {
          step_number: 1,
          action: 'execute_sql',
          sql: testForm.test_sql,
          expected_result: 'success'
        }
      ]
    }
    
    // 执行请求
    const requestData = {
      test_case_json: JSON.stringify(testCase),
      config_id: testForm.config_id,
      capture_enabled: testForm.capture_enabled,
      async_execution: testForm.async_execution,
      use_c_executor: testForm.use_c_executor
    }
    
    console.log('执行请求:', requestData)
    
    const response = await apiService.executeTestCase(requestData)
    
    if (response.success) {
      executionResult.value = {
        success: true,
        message: `测试执行成功！使用了${testForm.use_c_executor ? 'C语言' : 'Python'}执行器`,
        details: response
      }
      ElMessage.success('测试执行成功')
    } else {
      executionResult.value = {
        success: false,
        message: response.error || '执行失败',
        details: response
      }
      ElMessage.error('测试执行失败')
    }
    
  } catch (error) {
    console.error('执行测试失败:', error)
    executionResult.value = {
      success: false,
      message: error.message || '执行异常',
      details: error
    }
    ElMessage.error('执行测试失败')
  } finally {
    executing.value = false
  }
}

// 获取执行器工具提示
const getExecutorTooltip = (databaseType) => {
  if (databaseType === 'gaussdb') {
    return '使用C语言libpq库直接连接GaussDB，减少Python解释器开销，提供更好的性能'
  } else if (databaseType === 'postgresql') {
    return '使用C语言libpq库直接连接PostgreSQL，减少Python解释器开销，提供更好的性能'
  }
  return '使用C语言执行器提供更好的性能'
}

// 重置表单
const resetForm = () => {
  Object.assign(testForm, {
    config_id: null,
    database_type: 'gaussdb',
    capture_enabled: true,
    async_execution: false,
    use_c_executor: false,
    test_sql: 'SELECT current_database(), current_user, version()'
  })
  executionResult.value = null
}

// 组件挂载时加载数据
onMounted(() => {
  loadDatabaseConfigs()
})
</script>

<style scoped>
.c-executor-test {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.execution-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.execution-options .el-checkbox {
  margin-right: 0;
}

.result-section {
  margin-top: 20px;
}

pre {
  background-color: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
  line-height: 1.4;
}
</style>

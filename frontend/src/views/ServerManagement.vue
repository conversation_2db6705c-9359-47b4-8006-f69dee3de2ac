<template>
  <div class="server-management">
    <div class="header">
      <h2>服务器管理</h2>
      <el-button type="primary" @click="showCreateDialog = true">
        <el-icon><Plus /></el-icon>
        添加服务器
      </el-button>
    </div>

    <!-- 服务器列表 -->
    <el-table :data="servers" v-loading="loading" style="width: 100%">
      <el-table-column prop="name" label="名称" width="150" />
      <el-table-column prop="host" label="主机地址" width="150" />
      <el-table-column prop="port" label="端口" width="80" />
      <el-table-column prop="username" label="用户名" width="100" />
      <el-table-column prop="description" label="描述" />
      <el-table-column label="状态" width="100">
        <template #default="scope">
          <el-tag v-if="scope.row.is_default" type="success">默认</el-tag>
          <el-tag v-else type="info">普通</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="300">
        <template #default="scope">
          <el-button size="small" @click="testConnection(scope.row)">测试连接</el-button>
          <el-button size="small" @click="viewInterfaces(scope.row)">网络接口</el-button>
          <el-button size="small" @click="showDockerImages(scope.row)">Docker镜像</el-button>
          <el-button size="small" @click="buildDockerEnvironment(scope.row)">构建Docker环境</el-button>
          <el-button size="small" @click="editServer(scope.row)">编辑</el-button>
          <el-button size="small" type="danger" @click="deleteServer(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 创建/编辑服务器对话框 -->
    <el-dialog 
      :title="editingServer ? '编辑服务器' : '添加服务器'" 
      v-model="showCreateDialog"
      width="500px"
    >
      <el-form :model="serverForm" :rules="serverRules" ref="serverFormRef" label-width="100px">
        <el-form-item label="名称" prop="name">
          <el-input v-model="serverForm.name" placeholder="请输入服务器名称" />
        </el-form-item>
        <el-form-item label="主机地址" prop="host">
          <el-input v-model="serverForm.host" placeholder="请输入IP地址或域名" />
        </el-form-item>
        <el-form-item label="SSH端口" prop="port">
          <el-input-number v-model="serverForm.port" :min="1" :max="65535" />
        </el-form-item>
        <el-form-item label="用户名" prop="username">
          <el-input v-model="serverForm.username" placeholder="请输入SSH用户名" />
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input v-model="serverForm.password" type="password" placeholder="请输入SSH密码" show-password />
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="serverForm.description" type="textarea" placeholder="请输入服务器描述" />
        </el-form-item>
        <el-form-item>
          <el-checkbox v-model="serverForm.is_default">设为默认服务器</el-checkbox>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="saveServer" :loading="saving">保存</el-button>
      </template>
    </el-dialog>

    <!-- 网络接口对话框 -->
    <el-dialog title="网络接口信息" v-model="showInterfacesDialog" width="800px">
      <el-table :data="interfaces" v-loading="interfacesLoading">
        <el-table-column prop="name" label="接口名称" width="150" />
        <el-table-column prop="ip_address" label="IP地址" width="150" />
        <el-table-column prop="interface_type" label="类型" width="100">
          <template #default="scope">
            <el-tag :type="getInterfaceTypeColor(scope.row.interface_type)">
              {{ getInterfaceTypeName(scope.row.interface_type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="is_up" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.is_up ? 'success' : 'danger'">
              {{ scope.row.is_up ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <el-button @click="showInterfacesDialog = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 数据库部署检测对话框 -->
    <el-dialog title="数据库部署检测" v-model="showDeploymentDialog" width="600px">
      <el-form :model="deploymentForm" label-width="120px">
        <el-form-item label="数据库类型">
          <el-select v-model="deploymentForm.database_type" placeholder="请选择数据库类型">
            <el-option label="MySQL" value="mysql" />
            <el-option label="PostgreSQL" value="postgresql" />
            <el-option label="MongoDB" value="mongodb" />
          </el-select>
        </el-form-item>
        <el-form-item label="数据库主机">
          <el-input v-model="deploymentForm.database_host" placeholder="请输入数据库主机地址" />
        </el-form-item>
        <el-form-item label="数据库端口">
          <el-input-number v-model="deploymentForm.database_port" :min="1" :max="65535" />
        </el-form-item>
      </el-form>
      
      <div v-if="deploymentResult" class="deployment-result">
        <h4>检测结果:</h4>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="部署类型">
            <el-tag :type="deploymentResult.deployment_type === 'docker' ? 'success' : 'info'">
              {{ deploymentResult.deployment_type === 'docker' ? 'Docker容器' : '本地部署' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="主机地址">{{ deploymentResult.host }}</el-descriptions-item>
          <el-descriptions-item label="端口">{{ deploymentResult.port }}</el-descriptions-item>
          <el-descriptions-item label="容器名称" v-if="deploymentResult.container_name">
            {{ deploymentResult.container_name }}
          </el-descriptions-item>
          <el-descriptions-item label="容器IP" v-if="deploymentResult.container_ip">
            {{ deploymentResult.container_ip }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <div v-if="captureStrategy" class="capture-strategy">
        <h4>抓包策略:</h4>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="网络接口">{{ captureStrategy.interface }}</el-descriptions-item>
          <el-descriptions-item label="过滤表达式">{{ captureStrategy.filter_expression }}</el-descriptions-item>
          <el-descriptions-item label="可信度">
            <el-progress :percentage="captureStrategy.confidence * 100" :color="getConfidenceColor(captureStrategy.confidence)" />
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <template #footer>
        <el-button @click="detectDeployment" :loading="detecting">检测部署</el-button>
        <el-button @click="generateStrategy" :loading="generating">生成策略</el-button>
        <el-button @click="showDeploymentDialog = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- AI Docker环境构建对话框 -->
    <el-dialog title="AI Docker环境构建" v-model="showDockerDialog" width="900px">
      <!-- 服务器信息显示 -->
      <div v-if="currentServerInfo" class="server-info-card" style="margin-bottom: 20px;">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>目标服务器信息</span>
            </div>
          </template>
          <div class="server-details">
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">服务器名称:</span>
                  <span class="value">{{ currentServerInfo.name }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">服务器地址:</span>
                  <span class="value">{{ currentServerInfo.host }}:{{ currentServerInfo.port }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">用户名:</span>
                  <span class="value">{{ currentServerInfo.username }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row style="margin-top: 10px;">
              <el-col :span="24">
                <div class="info-item">
                  <span class="label">描述:</span>
                  <span class="value">{{ currentServerInfo.description || '无' }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </div>

      <el-tabs v-model="dockerActiveTab" type="card">
        <!-- 单个构建 -->
        <el-tab-pane label="单个构建" name="single">
          <el-form :model="dockerForm" label-width="120px">
            <el-form-item label="数据库类型">
              <el-select v-model="dockerForm.database_type" placeholder="请选择数据库类型">
                <el-option label="MySQL" value="mysql" />
                <el-option label="PostgreSQL" value="postgresql" />
                <el-option label="MongoDB" value="mongodb" />
                <el-option label="GaussDB" value="gaussdb" />
              </el-select>
            </el-form-item>
            <el-form-item label="AI提示词">
              <el-input
                v-model="dockerForm.ai_prompt"
                type="textarea"
                :rows="4"
                placeholder="请输入您的需求，例如：创建一个MySQL数据库，端口3306，密码123456，数据库名testdb"
              />
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <!-- 批量构建 -->
        <el-tab-pane label="批量构建" name="batch">
          <div class="batch-build-container">
            <div class="batch-header">
              <el-button @click="addBatchRequest" type="primary" size="small">
                <el-icon><Plus /></el-icon>
                添加构建请求
              </el-button>
              <el-button @click="clearBatchRequests" type="danger" size="small" v-if="batchRequests.length > 0">
                <el-icon><Delete /></el-icon>
                清空所有
              </el-button>
            </div>

            <div class="batch-requests" v-if="batchRequests.length > 0">
              <div
                v-for="(request, index) in batchRequests"
                :key="index"
                class="batch-request-item"
              >
                <div class="request-header">
                  <span class="request-index">请求 {{ index + 1 }}</span>
                  <el-button
                    @click="removeBatchRequest(index)"
                    type="danger"
                    size="small"
                    text
                  >
                    <el-icon><Close /></el-icon>
                  </el-button>
                </div>
                <el-form :model="request" label-width="100px" size="small">
                  <el-form-item label="数据库类型">
                    <el-select v-model="request.database_type" placeholder="请选择数据库类型">
                      <el-option label="MySQL" value="mysql" />
                      <el-option label="PostgreSQL" value="postgresql" />
                      <el-option label="MongoDB" value="mongodb" />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="AI提示词">
                    <el-input
                      v-model="request.ai_prompt"
                      type="textarea"
                      :rows="3"
                      placeholder="请输入构建需求"
                    />
                  </el-form-item>
                </el-form>
              </div>
            </div>

            <el-empty v-else description="暂无构建请求，点击上方按钮添加" />
          </div>
        </el-tab-pane>
      </el-tabs>

      <div v-if="dockerResult" class="docker-result">
        <h4>构建结果:</h4>
        <el-alert
          :type="dockerResult.success ? 'success' : 'error'"
          :title="dockerResult.message"
          show-icon
          :closable="false"
        />

        <div v-if="dockerResult.success" class="docker-info">
          <el-descriptions :column="2" border style="margin-top: 20px;">
            <el-descriptions-item label="容器名称">{{ dockerResult.container_name }}</el-descriptions-item>
            <el-descriptions-item label="容器ID">{{ dockerResult.container_id }}</el-descriptions-item>
            <el-descriptions-item label="端口">{{ dockerResult.port }}</el-descriptions-item>
            <el-descriptions-item label="用户名">{{ dockerResult.username }}</el-descriptions-item>
            <el-descriptions-item label="密码">{{ dockerResult.password }}</el-descriptions-item>
            <el-descriptions-item label="数据库名">{{ dockerResult.database_name }}</el-descriptions-item>
          </el-descriptions>

          <!-- 自动保存的数据库配置信息 -->
          <div v-if="dockerResult.database_config_id" class="auto-saved-config" style="margin-top: 20px;">
            <el-alert
              type="success"
              title="数据库配置已自动保存"
              show-icon
              :closable="false"
            >
              <template #default>
                <p>配置名称: <strong>{{ dockerResult.database_config_name }}</strong></p>
                <p>配置ID: <strong>{{ dockerResult.database_config_id }}</strong></p>
                <p>您可以在 <router-link to="/databases" style="color: #409eff;">数据库管理页面</router-link> 中查看和使用此配置</p>
              </template>
            </el-alert>
          </div>

          <div class="docker-command" style="margin-top: 20px;">
            <h5>Docker命令:</h5>
            <el-input
              v-model="dockerResult.docker_command"
              type="textarea"
              :rows="3"
              readonly
            />
            <el-button
              size="small"
              style="margin-top: 10px;"
              @click="copyToClipboard(dockerResult.docker_command)"
            >
              复制命令
            </el-button>
          </div>
        </div>
      </div>

      <template #footer>
        <el-button
          v-if="dockerActiveTab === 'single'"
          @click="buildDockerEnv"
          :loading="buildingDocker"
          type="primary"
        >
          构建环境
        </el-button>
        <el-button
          v-if="dockerActiveTab === 'batch'"
          @click="buildDockerEnvBatch"
          :loading="buildingDocker"
          type="primary"
          :disabled="batchRequests.length === 0"
        >
          批量构建 ({{ batchRequests.length }}个)
        </el-button>
        <el-button @click="showDockerDialog = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- Docker镜像管理对话框 -->
    <el-dialog
      v-model="showDockerImagesDialog"
      title="Docker镜像管理"
      width="50%"
      :close-on-click-modal="false"
    >
      <div class="docker-images-container">
        <div class="images-header">
          <div class="search-container">
            <el-input
              v-model="dockerImageSearch"
              placeholder="搜索镜像名称或标签"
              style="width: 300px; margin-right: 10px;"
              @keyup.enter="searchDockerImages"
            />
            <el-button @click="searchDockerImages" :loading="loadingDockerImages">搜索</el-button>
          </div>
          <el-button type="primary" @click="updateDockerImages" :loading="loadingDockerImages">
            更新镜像列表
          </el-button>
        </div>

        <el-table
          :data="dockerImages"
          v-loading="loadingDockerImages"
          style="width: 100%; margin-top: 20px;"
          max-height="400"
        >
          <el-table-column prop="repository" label="镜像仓库" width="200" />
          <el-table-column prop="tag" label="标签" width="120" />
          <el-table-column prop="image_id" label="镜像ID" width="150">
            <template #default="scope">
              <span>{{ scope.row.image_id.substring(0, 12) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="size" label="大小" width="100">
            <template #default="scope">
              <span>{{ formatImageSize(scope.row.size) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="last_updated" label="最后更新" width="180">
            <template #default="scope">
              <span>{{ scope.row.last_updated ? formatDateTime(scope.row.last_updated) : '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template #default="scope">
              <el-button size="small" type="primary" @click="selectImageForBuild(scope.row)">
                启动容器
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <div v-if="dockerImages.length === 0 && !loadingDockerImages" class="empty-state">
          <p>暂无Docker镜像数据</p>
          <p>点击"更新镜像列表"按钮获取服务器上的镜像信息</p>
        </div>
      </div>

      <template #footer>
        <el-button @click="showDockerImagesDialog = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 镜像选择构建对话框 -->
    <el-dialog
      v-model="showImageSelectionDialog"
      title="使用镜像构建容器"
      width="600px"
      :close-on-click-modal="false"
    >
      <div v-if="selectedDockerImage">
        <div class="selected-image-info">
          <h4>选择的镜像</h4>
          <p><strong>仓库:</strong> {{ selectedDockerImage.repository }}</p>
          <p><strong>标签:</strong> {{ selectedDockerImage.tag }}</p>
          <p><strong>大小:</strong> {{ formatImageSize(selectedDockerImage.size) }}</p>
        </div>

        <el-form :model="imageSelectionForm" label-width="120px" style="margin-top: 20px;">
          <el-form-item label="数据库类型">
            <el-select v-model="imageSelectionForm.database_type" style="width: 100%;">
              <el-option label="MySQL" value="mysql" />
              <el-option label="PostgreSQL" value="postgresql" />
              <el-option label="MongoDB" value="mongodb" />
              <el-option label="Oracle" value="oracle" />
              <el-option label="GaussDB" value="gaussdb" />
            </el-select>
          </el-form-item>
          <el-form-item label="AI提示词">
            <el-input
              v-model="imageSelectionForm.ai_prompt"
              type="textarea"
              :rows="4"
              placeholder="描述您的需求，AI将生成相应的Docker启动命令..."
            />
          </el-form-item>
          <el-form-item label="构建模式">
            <el-radio-group v-model="imageSelectionForm.build_mode">
              <el-radio value="sync">同步构建（等待完成）</el-radio>
              <el-radio value="async">异步构建（后台执行）</el-radio>
            </el-radio-group>
            <div class="build-mode-hint">
              <p v-if="imageSelectionForm.build_mode === 'sync'" class="hint-text">
                同步构建：等待构建完成后返回结果，适合快速测试
              </p>
              <p v-if="imageSelectionForm.build_mode === 'async'" class="hint-text">
                异步构建：任务在后台执行，可在任务监控页面查看进度
              </p>
            </div>
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <el-button @click="showImageSelectionDialog = false">取消</el-button>
        <el-button
          type="primary"
          @click="buildWithSelectedImage"
          :loading="buildingWithImage"
        >
          构建容器
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
// import { Plus, Delete, Close, Refresh } from '@element-plus/icons-vue'
import apiService from '@/services/api'

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const servers = ref([])
const showCreateDialog = ref(false)
const editingServer = ref(null)

// 服务器表单
const serverForm = reactive({
  name: '',
  host: '',
  port: 22,
  username: 'root',
  password: '',
  description: '',
  is_default: false
})

const serverRules = {
  name: [{ required: true, message: '请输入服务器名称', trigger: 'blur' }],
  host: [{ required: true, message: '请输入主机地址', trigger: 'blur' }],
  username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }]
}

const serverFormRef = ref()

// 网络接口相关
const showInterfacesDialog = ref(false)
const interfaces = ref([])
const interfacesLoading = ref(false)

// 部署检测相关
const showDeploymentDialog = ref(false)
const deploymentForm = reactive({
  database_type: 'postgresql',
  database_host: '**************',
  database_port: 5432
})
const deploymentResult = ref(null)
const captureStrategy = ref(null)
const detecting = ref(false)
const generating = ref(false)
const currentServerId = ref(null)

// Docker环境构建相关
const showDockerDialog = ref(false)
const dockerActiveTab = ref('single')
const currentServerInfo = ref(null)
const dockerForm = reactive({
  database_type: 'mysql',
  ai_prompt: ''
})
const dockerResult = ref(null)
const buildingDocker = ref(false)

// 批量构建相关
const batchRequests = ref([])

const addBatchRequest = () => {
  batchRequests.value.push({
    database_type: 'postgresql',
    ai_prompt: ''
  })
}

const removeBatchRequest = (index) => {
  batchRequests.value.splice(index, 1)
}

const clearBatchRequests = () => {
  batchRequests.value = []
}

// Docker镜像管理相关
const showDockerImagesDialog = ref(false)
const dockerImages = ref([])
const loadingDockerImages = ref(false)
const dockerImageSearch = ref('')
const selectedDockerImage = ref(null)
const showImageSelectionDialog = ref(false)
const imageSelectionForm = reactive({
  database_type: 'mysql',
  ai_prompt: '',
  build_mode: 'sync'  // 'sync' 或 'async'
})
const buildingWithImage = ref(false)

// 方法
const loadServers = async () => {
  loading.value = true
  try {
    const response = await apiService.getServerConfigs()
    // API直接返回数组，不是包装在data字段中
    servers.value = Array.isArray(response) ? response : []
  } catch (error) {
    ElMessage.error('加载服务器列表失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

const resetForm = () => {
  Object.assign(serverForm, {
    name: '',
    host: '',
    port: 22,
    username: 'root',
    password: '',
    description: '',
    is_default: false
  })
  editingServer.value = null
}

const editServer = (server) => {
  editingServer.value = server
  Object.assign(serverForm, {
    name: server.name,
    host: server.host,
    port: server.port,
    username: server.username,
    password: '', // 不显示密码
    description: server.description,
    is_default: server.is_default
  })
  showCreateDialog.value = true
}

const saveServer = async () => {
  if (!serverFormRef.value) return
  
  try {
    await serverFormRef.value.validate()
    saving.value = true
    
    if (editingServer.value) {
      await apiService.updateServerConfig(editingServer.value.id, serverForm)
      ElMessage.success('服务器更新成功')
    } else {
      await apiService.createServerConfig(serverForm)
      ElMessage.success('服务器创建成功')
    }
    
    showCreateDialog.value = false
    resetForm()
    await loadServers()
  } catch (error) {
    if (error.message) {
      ElMessage.error('保存失败: ' + error.message)
    }
  } finally {
    saving.value = false
  }
}

const deleteServer = async (server) => {
  try {
    await ElMessageBox.confirm('确定要删除这个服务器配置吗？', '确认删除', {
      type: 'warning'
    })
    
    await apiService.deleteServerConfig(server.id)
    ElMessage.success('服务器删除成功')
    await loadServers()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败: ' + error.message)
    }
  }
}

const testConnection = async (server) => {
  ElMessage.info('连接测试功能开发中...')
}

const viewInterfaces = async (server) => {
  currentServerId.value = server.id
  showInterfacesDialog.value = true
  interfacesLoading.value = true
  
  try {
    const response = await apiService.getNetworkInterfaces(server.id)
    interfaces.value = Array.isArray(response) ? response : []
  } catch (error) {
    ElMessage.error('获取网络接口失败: ' + error.message)
  } finally {
    interfacesLoading.value = false
  }
}

const detectDeployment = async () => {
  if (!currentServerId.value) return
  
  detecting.value = true
  try {
    const response = await apiService.detectDatabaseDeployment(
      currentServerId.value,
      deploymentForm.database_type,
      deploymentForm.database_host,
      deploymentForm.database_port
    )
    deploymentResult.value = response
    ElMessage.success('部署检测完成')
  } catch (error) {
    ElMessage.error('部署检测失败: ' + error.message)
  } finally {
    detecting.value = false
  }
}

const generateStrategy = async () => {
  if (!currentServerId.value) return
  
  generating.value = true
  try {
    const response = await apiService.generateCaptureStrategy(
      currentServerId.value,
      deploymentForm.database_type,
      deploymentForm.database_host,
      deploymentForm.database_port
    )
    captureStrategy.value = response
    ElMessage.success('抓包策略生成完成')
  } catch (error) {
    ElMessage.error('策略生成失败: ' + error.message)
  } finally {
    generating.value = false
  }
}

// 辅助方法
const getInterfaceTypeColor = (type) => {
  const colors = {
    physical: 'success',
    docker: 'primary',
    loopback: 'info',
    virtual: 'warning',
    unknown: 'danger'
  }
  return colors[type] || 'info'
}

const getInterfaceTypeName = (type) => {
  const names = {
    physical: '物理',
    docker: 'Docker',
    loopback: '回环',
    virtual: '虚拟',
    unknown: '未知'
  }
  return names[type] || type
}

const getConfidenceColor = (confidence) => {
  if (confidence >= 0.8) return '#67c23a'
  if (confidence >= 0.6) return '#e6a23c'
  return '#f56c6c'
}

const buildDockerEnvironment = (server) => {
  currentServerId.value = server.id
  currentServerInfo.value = server  // 保存完整的服务器信息
  dockerActiveTab.value = 'single'
  dockerForm.database_type = 'mysql'
  dockerForm.ai_prompt = ''
  dockerResult.value = null
  batchRequests.value = []
  showDockerDialog.value = true
}

const buildDockerEnv = async () => {
  if (!currentServerId.value) return

  buildingDocker.value = true
  try {
    const response = await apiService.buildDockerEnvironment(
      currentServerId.value,
      dockerForm.ai_prompt,
      dockerForm.database_type
    )
    dockerResult.value = response

    if (dockerResult.value.success) {
      let successMessage = 'Docker环境构建成功'
      if (dockerResult.value.database_config_id) {
        successMessage += `，数据库配置已自动保存为: ${dockerResult.value.database_config_name}`
      }
      ElMessage.success(successMessage)
    } else {
      ElMessage.error('Docker环境构建失败: ' + dockerResult.value.message)
    }
  } catch (error) {
    ElMessage.error('Docker环境构建失败: ' + error.message)
  } finally {
    buildingDocker.value = false
  }
}

const buildDockerEnvBatch = async () => {
  if (!currentServerId.value || batchRequests.value.length === 0) return

  // 验证所有请求都有必要的信息
  const invalidRequests = batchRequests.value.filter(req => !req.database_type || !req.ai_prompt.trim())
  if (invalidRequests.length > 0) {
    ElMessage.error('请确保所有构建请求都填写了数据库类型和AI提示词')
    return
  }

  buildingDocker.value = true
  try {
    const response = await apiService.buildDockerEnvironmentsAsync(
      currentServerId.value,
      batchRequests.value
    )

    ElMessage.success(`批量构建任务已提交！任务ID: ${response.task_id}`)
    ElMessage.info('请前往任务管理页面查看构建进度')

    // 关闭对话框
    showDockerDialog.value = false

  } catch (error) {
    ElMessage.error('提交批量构建任务失败: ' + error.message)
  } finally {
    buildingDocker.value = false
  }
}

// Docker镜像管理相关方法
const showDockerImages = async (server) => {
  currentServerId.value = server.id
  currentServerInfo.value = server
  showDockerImagesDialog.value = true
  await loadDockerImages()
}

const loadDockerImages = async () => {
  if (!currentServerId.value) return

  loadingDockerImages.value = true
  try {
    const response = await apiService.getDockerImages(currentServerId.value, dockerImageSearch.value)
    dockerImages.value = response || []
  } catch (error) {
    ElMessage.error('加载Docker镜像失败: ' + error.message)
    dockerImages.value = []
  } finally {
    loadingDockerImages.value = false
  }
}

const updateDockerImages = async () => {
  if (!currentServerId.value) return

  loadingDockerImages.value = true
  try {
    const response = await apiService.updateDockerImages(currentServerId.value)
    if (response.success) {
      ElMessage.success(response.message)
      await loadDockerImages()
    } else {
      ElMessage.error(response.message || '更新Docker镜像失败')
    }
  } catch (error) {
    ElMessage.error('更新Docker镜像失败: ' + error.message)
  } finally {
    loadingDockerImages.value = false
  }
}

const searchDockerImages = async () => {
  await loadDockerImages()
}

const selectImageForBuild = (image) => {
  selectedDockerImage.value = image
  showImageSelectionDialog.value = true
  imageSelectionForm.database_type = 'mysql'
  imageSelectionForm.ai_prompt = ''
  imageSelectionForm.build_mode = 'sync'
}

const buildWithSelectedImage = async () => {
  if (!selectedDockerImage.value || !currentServerId.value) return

  buildingWithImage.value = true
  try {
    if (imageSelectionForm.build_mode === 'async') {
      // 异步构建
      const response = await apiService.buildDockerEnvironmentWithImageAsync(
        currentServerId.value,
        selectedDockerImage.value.repository,
        selectedDockerImage.value.tag,
        imageSelectionForm.database_type,
        imageSelectionForm.ai_prompt
      )

      ElMessage.success(`异步构建任务已提交！任务ID: ${response.task_id}`)
      showImageSelectionDialog.value = false
      showDockerImagesDialog.value = false

      // 可以选择跳转到任务监控页面
      // router.push('/tasks')

    } else {
      // 同步构建
      const response = await apiService.buildDockerEnvironmentWithImage(
        currentServerId.value,
        selectedDockerImage.value.repository,
        selectedDockerImage.value.tag,
        imageSelectionForm.database_type,
        imageSelectionForm.ai_prompt
      )

      if (response.success) {
        let successMessage = 'Docker环境构建成功'
        if (response.database_config_id) {
          successMessage += `，数据库配置已自动保存为: ${response.database_config_name}`
        }
        ElMessage.success(successMessage)
        showImageSelectionDialog.value = false
        showDockerImagesDialog.value = false
      } else {
        ElMessage.error('Docker环境构建失败: ' + response.message)
      }
    }
  } catch (error) {
    ElMessage.error('Docker环境构建失败: ' + error.message)
  } finally {
    buildingWithImage.value = false
  }
}

// 统一的时区格式化函数，强制使用+8时区
const formatDateTime = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    timeZone: 'Asia/Shanghai',
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  })
}

const formatImageSize = (size) => {
  if (size >= 1024 * 1024 * 1024) {
    return (size / (1024 * 1024 * 1024)).toFixed(2) + ' GB'
  } else if (size >= 1024 * 1024) {
    return (size / (1024 * 1024)).toFixed(2) + ' MB'
  } else if (size >= 1024) {
    return (size / 1024).toFixed(2) + ' KB'
  } else {
    return size + ' B'
  }
}

const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success('命令已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

// 生命周期
onMounted(() => {
  loadServers()
})
</script>

<style scoped>
.server-management {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.deployment-result,
.capture-strategy {
  margin-top: 20px;
}

.deployment-result h4,
.capture-strategy h4 {
  margin-bottom: 10px;
  color: #409eff;
}

/* 批量构建样式 */
.batch-build-container {
  max-height: 400px;
  overflow-y: auto;
}

.batch-header {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

.batch-requests {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.batch-request-item {
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  padding: 15px;
  background-color: #fafafa;
}

.request-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.request-index {
  font-weight: bold;
  color: #409eff;
}

/* 服务器信息卡片样式 */
.server-info-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
  color: #409eff;
}

.server-details .info-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.server-details .label {
  font-weight: 500;
  color: #606266;
  margin-right: 8px;
  min-width: 80px;
}

.server-details .value {
  color: #303133;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

/* Docker镜像管理样式 */
.docker-images-container {
  min-height: 400px;
}

.images-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-container {
  display: flex;
  align-items: center;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #909399;
}

.empty-state p {
  margin: 10px 0;
}

.selected-image-info {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 6px;
  margin-bottom: 20px;
}

.selected-image-info h4 {
  margin: 0 0 10px 0;
  color: #409eff;
}

.selected-image-info p {
  margin: 5px 0;
  color: #606266;
}

.build-mode-hint {
  margin-top: 8px;
}

.hint-text {
  font-size: 12px;
  color: #909399;
  margin: 0;
  line-height: 1.4;
}
</style>

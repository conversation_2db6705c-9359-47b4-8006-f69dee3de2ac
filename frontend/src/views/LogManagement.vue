<template>
  <div class="log-management">
    <!-- 顶部操作栏 -->
    <div class="header">
      <h2>日志管理</h2>
      <div class="header-actions">
        <el-button @click="refreshLogs" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button type="primary" @click="exportLogs" :loading="exportLoading">
          <el-icon><Download /></el-icon>
          导出日志
        </el-button>
        <el-button type="warning" @click="clearLogs" :loading="clearLoading">
          <el-icon><Delete /></el-icon>
          清理日志
        </el-button>
      </div>
    </div>

    <!-- 统计仪表板 -->
    <div class="stats-dashboard">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-header">
                <span class="stat-title">总日志数</span>
                <el-icon class="stat-icon" color="#409EFF"><Document /></el-icon>
              </div>
              <div class="stat-number">{{ dashboardStats.total_logs }}</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-header">
                <span class="stat-title">执行中任务</span>
                <el-icon class="stat-icon" color="#67C23A"><Loading /></el-icon>
              </div>
              <div class="stat-number running">{{ dashboardStats.running_tasks }}</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-header">
                <span class="stat-title">失败任务</span>
                <el-icon class="stat-icon" color="#F56C6C"><WarningFilled /></el-icon>
              </div>
              <div class="stat-number failed">{{ dashboardStats.failed_tasks }}</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-header">
                <span class="stat-title">错误日志</span>
                <el-icon class="stat-icon" color="#E6A23C"><Warning /></el-icon>
              </div>
              <div class="stat-number warning">{{ dashboardStats.error_logs }}</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 过滤和搜索 -->
    <div class="filters-section">
      <el-card>
        <el-row :gutter="15">
          <el-col :span="4">
            <el-select v-model="filters.logType" placeholder="日志类型" clearable @change="loadLogs">
              <el-option label="全部日志" value="" />
              <el-option label="执行日志" value="execution_logs" />
              <el-option label="ARQ任务" value="arq_tasks" />
              <el-option label="系统日志" value="system" />
              <el-option label="错误日志" value="error" />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-switch
              v-model="filters.includeArqTasks"
              active-text="包含ARQ任务"
              inactive-text="仅执行日志"
              @change="loadLogs"
            />
          </el-col>
          <el-col :span="4">
            <el-select v-model="filters.logLevel" placeholder="日志级别" clearable @change="loadLogs">
              <el-option label="DEBUG" value="DEBUG" />
              <el-option label="INFO" value="INFO" />
              <el-option label="WARNING" value="WARNING" />
              <el-option label="ERROR" value="ERROR" />
              <el-option label="CRITICAL" value="CRITICAL" />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select v-model="filters.taskStatus" placeholder="任务状态" clearable @change="loadLogs">
              <el-option label="运行中" value="RUNNING" />
              <el-option label="已完成" value="COMPLETED" />
              <el-option label="失败" value="FAILED" />
              <el-option label="超时" value="TIMEOUT" />
              <el-option label="已取消" value="CANCELLED" />
            </el-select>
          </el-col>
          <el-col :span="8">
            <el-date-picker
              v-model="filters.dateRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              @change="loadLogs"
            />
          </el-col>
        </el-row>
        <el-row :gutter="15" style="margin-top: 15px;">
          <el-col :span="12">
            <el-input
              v-model="filters.searchText"
              placeholder="搜索日志内容、任务ID、错误信息..."
              clearable
              @change="loadLogs"
              @keyup.enter="loadLogs"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="6">
            <el-select v-model="filters.taskType" placeholder="任务类型" clearable @change="loadLogs">
              <el-option label="MySQL抓包" value="mysql_capture" />
              <el-option label="PostgreSQL抓包" value="postgres_capture" />
              <el-option label="MongoDB抓包" value="mongo_capture" />
              <el-option label="GaussDB抓包" value="gaussdb_capture" />
              <el-option label="批量测试执行" value="batch_test_case_execution" />
              <el-option label="单个测试执行" value="single_test_case_execution" />
              <el-option label="网关执行" value="gateway_execution" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-button type="primary" @click="loadLogs" :loading="loading">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="resetFilters" style="margin-left: 10px;">
              <el-icon><RefreshRight /></el-icon>
              重置
            </el-button>
          </el-col>
        </el-row>
      </el-card>
    </div>

    <!-- 日志列表 -->
    <el-card class="logs-table-card">
      <div class="table-header">
        <span class="table-title">日志记录</span>
        <div class="table-actions">
          <el-tooltip content="实时刷新">
            <el-switch v-model="autoRefresh" @change="toggleAutoRefresh" />
          </el-tooltip>
        </div>
      </div>
      
      <el-table :data="logs" v-loading="loading" stripe row-key="id">
        <el-table-column prop="id" label="ID" width="80" />
        
        <el-table-column label="时间" width="180">
          <template #default="{ row }">
            <div class="time-column">
              <div class="primary-time">{{ formatTime(row.created_at || row.timestamp) }}</div>
              <div class="relative-time" v-if="row.created_at">{{ getRelativeTime(row.created_at) }}</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="级别" width="80">
          <template #default="{ row }">
            <el-tag :type="getLogLevelColor(row.level)" size="small">
              {{ row.level }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="类型/任务" width="220">
          <template #default="{ row }">
            <div class="type-column">
              <!-- ARQ任务特殊显示 -->
              <div v-if="row.category === 'ARQ_TASK'" class="arq-task-info">
                <div class="task-id">
                  <el-icon style="margin-right: 4px;"><Timer /></el-icon>
                  {{ row.task_id.substring(0, 8) }}...
                </div>
                <div class="task-type">
                  <el-tag size="small" type="primary">
                    {{ getArqTaskTypeName(row.arq_task_type || row.function_name) }}
                  </el-tag>
                </div>
                <div v-if="row.arq_progress !== undefined" class="arq-progress">
                  <el-progress 
                    :percentage="row.arq_progress" 
                    :show-text="false" 
                    :stroke-width="4"
                    :color="getProgressColor(row.arq_status)"
                    style="margin-top: 2px;"
                  />
                  <span style="font-size: 11px; color: #666;">{{ row.arq_progress }}%</span>
                </div>
              </div>
              <!-- 普通任务显示 -->
              <div v-else-if="row.task_id" class="task-info">
                <div class="task-id">{{ row.task_id.substring(0, 8) }}...</div>
                <div class="task-type" v-if="row.task_type">
                  <el-tag size="small" :type="getTaskTypeColor(row.task_type)">
                    {{ getTaskTypeName(row.task_type) }}
                  </el-tag>
                </div>
              </div>
              <!-- 日志分类显示 -->
              <div v-else-if="row.category" class="log-category">
                <el-tag size="small" type="info">{{ row.category }}</el-tag>
              </div>
              <!-- 系统日志 -->
              <div v-else class="system-log">
                <el-tag size="small" type="warning">系统日志</el-tag>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <!-- ARQ任务状态 -->
            <el-tag v-if="row.arq_status" :type="getArqStatusColor(row.arq_status)" size="small">
              <el-icon v-if="row.arq_status === 'RUNNING'" style="margin-right: 2px;"><Loading /></el-icon>
              {{ getArqStatusName(row.arq_status) }}
            </el-tag>
            <!-- 普通任务状态 -->
            <el-tag v-else-if="row.status" :type="getStatusColor(row.status)" size="small">
              {{ getStatusName(row.status) }}
            </el-tag>
            <span v-else class="no-status">-</span>
          </template>
        </el-table-column>

        <el-table-column label="执行器" width="100">
          <template #default="{ row }">
            <el-tag v-if="row.executor_type" :type="getExecutorColor(row.executor_type)" size="small">
              {{ row.executor_type }}
            </el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <el-table-column label="消息内容" min-width="300">
          <template #default="{ row }">
            <div class="message-column">
              <div class="message-text">{{ row.message || row.error_message || '无消息' }}</div>
              <div v-if="row.current_step" class="current-step">
                当前步骤: {{ row.current_step }}
              </div>
              <div v-if="row.progress !== undefined && row.progress !== null" class="progress-info">
                <el-progress :percentage="row.progress" :stroke-width="4" />
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button size="small" type="primary" link @click="viewLogDetail(row)">
              详情
            </el-button>
            <el-button v-if="row.task_id" size="small" type="success" link @click="viewTaskDetail(row.task_id)">
              任务
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          :current-page="pagination.page"
          :page-size="pagination.pageSize"
          :page-sizes="[20, 50, 100, 200]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 日志详情对话框 -->
    <el-dialog v-model="logDetailDialog.visible" title="日志详情" width="70%">
      <div v-if="logDetailDialog.log" class="log-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="日志ID">{{ logDetailDialog.log.id }}</el-descriptions-item>
          <el-descriptions-item label="时间">{{ formatTime(logDetailDialog.log.created_at || logDetailDialog.log.timestamp) }}</el-descriptions-item>
          <el-descriptions-item label="级别">
            <el-tag :type="getLogLevelColor(logDetailDialog.log.level)">{{ logDetailDialog.log.level }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="类别">{{ logDetailDialog.log.category || '系统日志' }}</el-descriptions-item>
          <el-descriptions-item label="任务ID" v-if="logDetailDialog.log.task_id">
            {{ logDetailDialog.log.task_id }}
          </el-descriptions-item>
          <el-descriptions-item label="任务类型" v-if="logDetailDialog.log.task_type">
            <el-tag :type="getTaskTypeColor(logDetailDialog.log.task_type)">
              {{ getTaskTypeName(logDetailDialog.log.task_type) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="执行器" v-if="logDetailDialog.log.executor_type">
            <el-tag :type="getExecutorColor(logDetailDialog.log.executor_type)">{{ logDetailDialog.log.executor_type }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="状态" v-if="logDetailDialog.log.status">
            <el-tag :type="getStatusColor(logDetailDialog.log.status)">{{ getStatusName(logDetailDialog.log.status) }}</el-tag>
          </el-descriptions-item>
        </el-descriptions>

        <!-- 消息内容 -->
        <div class="detail-section" style="margin-top: 20px;">
          <h4>消息内容</h4>
          <el-input
            type="textarea"
            :value="logDetailDialog.log.message || logDetailDialog.log.error_message || '无消息内容'"
            readonly
            :rows="4"
          />
        </div>

        <!-- 错误详情 -->
        <div v-if="logDetailDialog.log.error_details" class="detail-section" style="margin-top: 20px;">
          <h4>错误详情</h4>
          <el-input
            type="textarea"
            :value="typeof logDetailDialog.log.error_details === 'string' ? logDetailDialog.log.error_details : JSON.stringify(logDetailDialog.log.error_details, null, 2)"
            readonly
            :rows="6"
          />
        </div>

        <!-- 堆栈跟踪 -->
        <div v-if="logDetailDialog.log.error_stack_trace" class="detail-section" style="margin-top: 20px;">
          <h4>堆栈跟踪</h4>
          <el-input
            type="textarea"
            :value="logDetailDialog.log.error_stack_trace"
            readonly
            :rows="8"
          />
        </div>

        <!-- SQL查询 -->
        <div v-if="logDetailDialog.log.input_sql_query" class="detail-section" style="margin-top: 20px;">
          <h4>SQL查询</h4>
          <el-input
            type="textarea"
            :value="logDetailDialog.log.input_sql_query"
            readonly
            :rows="4"
          />
        </div>

        <!-- 系统信息 -->
        <div v-if="logDetailDialog.log.cpu_percent || logDetailDialog.log.memory_mb" class="detail-section" style="margin-top: 20px;">
          <h4>系统资源</h4>
          <el-descriptions :column="3" border size="small">
            <el-descriptions-item label="CPU使用率" v-if="logDetailDialog.log.cpu_percent">
              {{ logDetailDialog.log.cpu_percent }}%
            </el-descriptions-item>
            <el-descriptions-item label="内存使用" v-if="logDetailDialog.log.memory_mb">
              {{ logDetailDialog.log.memory_mb }} MB
            </el-descriptions-item>
            <el-descriptions-item label="进度" v-if="logDetailDialog.log.progress !== undefined">
              {{ logDetailDialog.log.progress }}%
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </el-dialog>

    <!-- 任务详情对话框 -->
    <el-dialog v-model="taskDetailDialog.visible" title="任务详情" width="70%">
      <div v-if="taskDetailDialog.task" class="task-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="任务ID">{{ taskDetailDialog.task.task_id }}</el-descriptions-item>
          <el-descriptions-item label="任务类型">
            <el-tag :type="getTaskTypeColor(taskDetailDialog.task.task_type)">
              {{ getTaskTypeName(taskDetailDialog.task.task_type) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusColor(taskDetailDialog.task.status)">
              {{ getStatusName(taskDetailDialog.task.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="进度">{{ taskDetailDialog.task.progress || 0 }}%</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatTime(taskDetailDialog.task.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ formatTime(taskDetailDialog.task.updated_at) }}</el-descriptions-item>
        </el-descriptions>

        <!-- 任务执行详情 -->
        <div v-if="taskDetailDialog.task.input_sql_query" class="detail-section" style="margin-top: 20px;">
          <h4>SQL查询</h4>
          <el-input
            type="textarea"
            :value="taskDetailDialog.task.input_sql_query"
            readonly
            :rows="4"
          />
        </div>

        <div v-if="taskDetailDialog.task.execution_result" class="detail-section" style="margin-top: 20px;">
          <h4>执行结果</h4>
          <el-input
            type="textarea"
            :value="JSON.stringify(taskDetailDialog.task.execution_result, null, 2)"
            readonly
            :rows="8"
          />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Refresh, Download, Delete, Document, Loading, WarningFilled, Warning,
  Search, RefreshRight
} from '@element-plus/icons-vue'
import apiService from '@/services/api'
import { formatTime as formatTimeWithTimezone } from '@/utils/timezone'

// 响应式数据
const loading = ref(false)
const exportLoading = ref(false)
const clearLoading = ref(false)
const logs = ref([])
const autoRefresh = ref(false)
let refreshTimer = null

// 仪表板统计
const dashboardStats = reactive({
  total_logs: 0,
  running_tasks: 0,
  failed_tasks: 0,
  error_logs: 0
})

// 过滤条件
const filters = reactive({
  logType: '',
  logLevel: '',
  taskStatus: '',
  taskType: '',
  dateRange: null,
  searchText: '',
  includeArqTasks: true
})

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 50,
  total: 0
})

// 对话框
const logDetailDialog = reactive({
  visible: false,
  log: null
})

const taskDetailDialog = reactive({
  visible: false,
  task: null
})

// 方法
const loadLogs = async () => {
  loading.value = true
  try {
    // 根据过滤条件构建查询参数
    const params = {
      page: pagination.page,
      page_size: pagination.pageSize
    }

    // 添加过滤条件
    if (filters.logLevel) params.level = filters.logLevel
    if (filters.taskStatus) params.status = filters.taskStatus
    if (filters.taskType) params.task_type = filters.taskType
    if (filters.searchText) params.search = filters.searchText
    if (filters.dateRange && filters.dateRange.length === 2) {
      params.start_date = filters.dateRange[0]
      params.end_date = filters.dateRange[1]
    }

    let response
    if (filters.logType === 'arq_tasks') {
      // 仅获取ARQ任务日志
      response = await apiService.get('/execution-logs/arq-tasks', { params })
      logs.value = response.data.tasks || []
      pagination.total = response.data.total || 0
      // 转换ARQ任务为日志格式显示
      logs.value = logs.value.map(task => ({
        id: `arq_${task.task_id}`,
        task_id: task.task_id,
        level: task.status === 'FAILED' ? 'ERROR' : (task.status === 'PENDING' ? 'WARNING' : 'INFO'),
        category: 'ARQ_TASK',
        message: `[ARQ任务] ${task.task_type}: ${task.message || '无消息'}`,
        timestamp: task.created_at,
        arq_status: task.status,
        arq_progress: task.progress,
        arq_task_type: task.task_type,
        function_name: task.task_type,
        error_details: task.error
      }))
    } else if (filters.logType === 'task_execution') {
      // 获取任务执行日志
      response = await apiService.getTaskExecutionLogs(params)
      logs.value = response.data.execution_logs || []
      pagination.total = response.data.total || 0
    } else {
      // 获取执行日志（包含ARQ任务选项）
      params.include_arq_tasks = filters.includeArqTasks
      response = await apiService.get('/execution-logs/search', { params })
      logs.value = response.data.logs || []
      pagination.total = response.data.total || 0
      
      // 显示ARQ任务集成信息
      if (response.data.arq_tasks_included && response.data.arq_tasks_count > 0) {
        ElMessage.info(`已包含 ${response.data.arq_tasks_count} 个ARQ任务日志`)
      } else if (response.data.arq_error) {
        console.warn('ARQ任务日志获取失败:', response.data.arq_error)
      }
    }

  } catch (error) {
    console.error('加载日志失败:', error)
    ElMessage.error('加载日志失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

const loadDashboardStats = async () => {
  try {
    // 加载仪表板统计数据
    const dashboardResponse = await apiService.get('/task-execution-logs/dashboard/stats')
    
    if (dashboardResponse.success && dashboardResponse.data) {
      Object.assign(dashboardStats, dashboardResponse.data)
    }

  } catch (error) {
    console.error('加载统计数据失败:', error)
    // 设置默认值
    Object.assign(dashboardStats, {
      total_logs: 0,
      running_tasks: 0,
      failed_tasks: 0,
      error_logs: 0
    })
  }
}

const refreshLogs = async () => {
  await Promise.all([loadLogs(), loadDashboardStats()])
}

const exportLogs = async () => {
  exportLoading.value = true
  try {
    // 构建导出参数
    const params = { ...filters, page: 1, page_size: 10000 } // 最大导出10000条
    
    await ElMessageBox.confirm('确定要导出当前筛选条件下的日志吗？', '确认导出', {
      type: 'warning'
    })

    const response = await apiService.exportLogs(params)
    
    // 创建下载链接
    const blob = new Blob([response], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `logs_export_${new Date().getTime()}.csv`
    document.body.appendChild(a)
    a.click()
    window.URL.revokeObjectURL(url)
    document.body.removeChild(a)

    ElMessage.success('日志导出成功')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('导出日志失败:', error)
      ElMessage.error('导出日志失败: ' + error.message)
    }
  } finally {
    exportLoading.value = false
  }
}

const clearLogs = async () => {
  try {
    await ElMessageBox.confirm('确定要清理过期日志吗？此操作不可恢复。', '确认清理', {
      type: 'warning'
    })

    clearLoading.value = true
    await apiService.clearLogs({ days: 30 }) // 清理30天前的日志
    ElMessage.success('日志清理任务已启动')
    
    // 延迟刷新
    setTimeout(refreshLogs, 2000)
  } catch (error) {
    if (error !== 'cancel') {
      console.error('清理日志失败:', error)
      ElMessage.error('清理日志失败: ' + error.message)
    }
  } finally {
    clearLoading.value = false
  }
}

const resetFilters = () => {
  Object.keys(filters).forEach(key => {
    if (key === 'dateRange') {
      filters[key] = null
    } else {
      filters[key] = ''
    }
  })
  pagination.page = 1
  loadLogs()
}

const viewLogDetail = (log) => {
  logDetailDialog.log = log
  logDetailDialog.visible = true
}

const viewTaskDetail = async (taskId) => {
  try {
    const response = await apiService.getTaskExecutionLog(taskId)
    taskDetailDialog.task = response.data
    taskDetailDialog.visible = true
  } catch (error) {
    console.error('获取任务详情失败:', error)
    ElMessage.error('获取任务详情失败: ' + error.message)
  }
}

const toggleAutoRefresh = (value) => {
  if (value) {
    refreshTimer = setInterval(refreshLogs, 10000) // 每10秒刷新
    ElMessage.success('已启用实时刷新')
  } else {
    if (refreshTimer) {
      clearInterval(refreshTimer)
      refreshTimer = null
    }
    ElMessage.info('已关闭实时刷新')
  }
}

const handleSizeChange = (size) => {
  pagination.pageSize = size
  pagination.page = 1
  loadLogs()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  loadLogs()
}

// 辅助方法
const formatTime = (timeStr) => {
  return formatTimeWithTimezone(timeStr)
}

const getRelativeTime = (timeStr) => {
  const now = new Date()
  const time = new Date(timeStr)
  const diff = now - time
  
  const minutes = Math.floor(diff / 60000)
  const hours = Math.floor(diff / 3600000)
  const days = Math.floor(diff / 86400000)
  
  if (days > 0) return `${days}天前`
  if (hours > 0) return `${hours}小时前`
  if (minutes > 0) return `${minutes}分钟前`
  return '刚刚'
}

const getLogLevelColor = (level) => {
  const colors = {
    'DEBUG': 'info',
    'INFO': 'success',
    'WARNING': 'warning',
    'ERROR': 'danger',
    'CRITICAL': 'danger'
  }
  return colors[level] || 'info'
}

const getTaskTypeColor = (type) => {
  const colors = {
    mysql_capture: 'primary',
    postgres_capture: 'success',
    mongo_capture: 'warning',
    gaussdb_capture: 'info',
    batch_test_case_execution: 'primary',
    single_test_case_execution: 'info',
    gateway_execution: 'success'
  }
  return colors[type] || 'info'
}

const getTaskTypeName = (type) => {
  const names = {
    mysql_capture: 'MySQL抓包',
    postgres_capture: 'PostgreSQL抓包',
    mongo_capture: 'MongoDB抓包',
    gaussdb_capture: 'GaussDB抓包',
    batch_test_case_execution: '批量测试执行',
    single_test_case_execution: '单个测试执行',
    gateway_execution: '网关执行'
  }
  return names[type] || type
}

const getStatusColor = (status) => {
  const colors = {
    'PENDING': 'info',
    'RUNNING': 'primary',
    'COMPLETED': 'success',
    'FAILED': 'danger',
    'TIMEOUT': 'warning',
    'CANCELLED': 'warning'
  }
  return colors[status] || 'info'
}

const getStatusName = (status) => {
  const names = {
    'PENDING': '等待中',
    'RUNNING': '运行中',
    'COMPLETED': '已完成',
    'FAILED': '失败',
    'TIMEOUT': '超时',
    'CANCELLED': '已取消'
  }
  return names[status] || status
}

const getExecutorColor = (executor) => {
  const colors = {
    'PYTHON': 'success',
    'C': 'warning',
    'JAVA': 'info'
  }
  return colors[executor] || 'info'
}

// ARQ任务相关方法
const getArqStatusColor = (status) => {
  const colors = {
    'PENDING': 'info',
    'RUNNING': 'primary',
    'COMPLETED': 'success',
    'FAILED': 'danger',
    'TIMEOUT': 'warning',
    'CANCELLED': 'warning'
  }
  return colors[status] || 'info'
}

const getArqStatusName = (status) => {
  const names = {
    'PENDING': '等待中',
    'RUNNING': '运行中', 
    'COMPLETED': '已完成',
    'FAILED': '失败',
    'TIMEOUT': '超时',
    'CANCELLED': '已取消'
  }
  return names[status] || status
}

const getArqTaskTypeName = (taskType) => {
  const names = {
    'gateway_batch_execution_task': '网关批量执行',
    'gateway_single_execution_task': '网关单个执行',
    'batch_test_case_execution': '批量测试执行',
    'single_test_case_execution': '单个测试执行',
    'mysql_capture': 'MySQL抓包',
    'postgres_capture': 'PostgreSQL抓包',
    'mongo_capture': 'MongoDB抓包',
    'oracle_capture': 'Oracle抓包',
    'gaussdb_capture': 'GaussDB抓包'
  }
  return names[taskType] || taskType
}

const getProgressColor = (status) => {
  const colors = {
    'PENDING': '#909399',
    'RUNNING': '#409EFF',
    'COMPLETED': '#67C23A',
    'FAILED': '#F56C6C',
    'TIMEOUT': '#E6A23C',
    'CANCELLED': '#E6A23C'
  }
  return colors[status] || '#409EFF'
}

// 生命周期
onMounted(() => {
  refreshLogs()
})

onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
})
</script>

<style scoped>
.log-management {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h2 {
  margin: 0;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.stats-dashboard {
  margin-bottom: 20px;
}

.stat-card {
  border-radius: 8px;
  overflow: hidden;
}

.stat-content {
  padding: 20px;
}

.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.stat-title {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.stat-icon {
  font-size: 20px;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: #409EFF;
}

.stat-number.running {
  color: #67C23A;
}

.stat-number.failed {
  color: #F56C6C;
}

.stat-number.warning {
  color: #E6A23C;
}

.filters-section {
  margin-bottom: 20px;
}

.logs-table-card {
  border-radius: 8px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.table-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.time-column {
  line-height: 1.4;
}

.primary-time {
  font-size: 13px;
  color: #303133;
}

.relative-time {
  font-size: 11px;
  color: #909399;
  margin-top: 2px;
}

.type-column {
  line-height: 1.4;
}

.task-info .task-id {
  font-family: monospace;
  font-size: 12px;
  color: #606266;
  margin-bottom: 4px;
}

.message-column {
  line-height: 1.4;
}

.message-text {
  color: #303133;
  margin-bottom: 5px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 300px;
}

.current-step {
  font-size: 12px;
  color: #909399;
  margin-bottom: 5px;
}

.progress-info {
  margin-top: 5px;
}

.no-status {
  color: #C0C4CC;
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.log-detail,
.task-detail {
  max-height: 600px;
  overflow-y: auto;
}

.detail-section {
  margin-top: 20px;
}

.detail-section h4 {
  margin-bottom: 10px;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}
</style>

<template>
  <div class="gaussdb-query-interface">
    <el-row :gutter="20">
      <!-- 左侧查询区域 -->
      <el-col :span="16">
        <el-card class="query-card">
          <template #header>
            <div class="card-header">
              <span>GaussDB抓包</span>
              <div class="header-controls">
                <el-select
                  v-model="selectedConfigId"
                  placeholder="选择GaussDB配置"
                  style="width: 200px; margin-right: 10px;"
                  @change="onDatabaseChange"
                >
                  <el-option
                    v-for="config in gaussdbConfigs"
                    :key="config.id"
                    :label="`${config.name} (${config.host}:${config.port})`"
                    :value="config.id"
                  >
                    <span>{{ config.name }}</span>
                    <el-tag v-if="config.is_default" type="success" size="small" style="margin-left: 10px;">默认</el-tag>
                  </el-option>
                </el-select>
                <el-switch
                  v-model="autoCapture"
                  active-text="自动抓包"
                  inactive-text="手动抓包"
                />
              </div>
            </div>
          </template>
          
          <div class="query-section">
            <el-input
              v-model="naturalQuery"
              type="textarea"
              :rows="6"
              placeholder="请输入自然语言查询或SQL语句，支持多语句执行（用分号分隔）&#10;例如：&#10;- 查询所有用户信息&#10;- SELECT * FROM users; SELECT COUNT(*) FROM orders;"
              class="query-input"
            />
            
            <div class="query-actions">
              <el-button
                type="primary"
                icon="Search"
                @click="executeQuery"
                :loading="queryLoading"
                size="large"
              >
                执行查询
              </el-button>
              
              <el-button
                type="info"
                icon="Delete"
                @click="clearQuery"
                size="large"
              >
                清空
              </el-button>

              <el-button
                v-if="!autoCapture"
                type="warning"
                icon="Monitor"
                @click="startAsyncCapture"
                :loading="asyncCaptureLoading"
                size="large"
              >
                手动抓包
              </el-button>

              <el-button
                type="success"
                icon="Timer"
                @click="executeAsyncCapture"
                :loading="asyncCaptureLoading"
                :disabled="!naturalQuery.trim() || !selectedConfigId"
                size="large"
              >
                异步抓包
              </el-button>


            </div>
          </div>
        </el-card>

        <!-- 查询结果 -->
        <el-card v-if="queryResult" class="result-card">
          <template #header>
            <div class="card-header">
              <span>查询结果</span>
              <el-tag :type="queryResult.success ? 'success' : 'danger'">
                {{ queryResult.success ? '成功' : '失败' }}
              </el-tag>
            </div>
          </template>
          
          <div class="result-content">
            <!-- SQL语句显示 -->
            <div v-if="queryResult.sql_query" class="sql-display">
              <h4>生成的SQL语句:</h4>
              <el-input
                v-model="queryResult.sql_query"
                type="textarea"
                :rows="3"
                readonly
                class="sql-textarea"
              />
            </div>

            <!-- 多语句结果 -->
            <div v-if="queryResult && queryResult.result && queryResult.result.statements_count > 1" class="multi-statement-results">
              <h4>多语句执行结果 ({{ queryResult.result.statements_count }} 条语句):</h4>
              <div class="statement-results">
                <div
                  v-for="(result, index) in (queryResult.result.results || [])"
                  :key="index"
                  class="statement-result"
                >
                  <h5>语句 {{ index + 1 }}:</h5>
                  <div class="result-summary">
                    <el-tag type="info">影响行数: {{ result.rows_affected || 0 }}</el-tag>
                  </div>
                </div>
              </div>
            </div>

            <!-- 单语句结果 -->
            <div v-else-if="queryResult && queryResult.result" class="single-statement-result">
              <div class="result-summary">
                <el-tag type="info">影响行数: {{ queryResult.result.rows_affected || 0 }}</el-tag>
              </div>

              <!-- 数据表格 -->
              <div v-if="queryResult.result.data && queryResult.result.data.length > 0" class="data-table">
                <h4>查询结果:</h4>
                <el-table :data="queryResult.result.data" border stripe max-height="400">
                  <el-table-column
                    v-for="column in (queryResult.result.columns || [])"
                    :key="column"
                    :prop="column"
                    :label="column"
                    show-overflow-tooltip
                  />
                </el-table>
              </div>
            </div>

            <!-- 抓包文件信息 -->
            <div v-if="queryResult.packet_file" class="packet-info">
              <h4>抓包文件:</h4>
              <el-tag type="info">{{ queryResult.packet_file }}</el-tag>
            </div>

            <!-- 错误信息 -->
            <div v-if="queryResult.error" class="error-message">
              <h4>错误信息:</h4>
              <el-alert :title="queryResult.error" type="error" show-icon />
            </div>
          </div>
        </el-card>

        <!-- 查询历史 -->
        <el-card v-if="queryHistory.length > 0" class="history-card">
          <template #header>
            <span>查询历史</span>
          </template>
          
          <div class="history-list">
            <div
              v-for="(item, index) in queryHistory"
              :key="index"
              class="history-item"
              @click="loadHistoryQuery(item)"
            >
              <div class="history-query">{{ item.query }}</div>
              <div class="history-meta">
                <span>{{ item.timestamp }}</span>
                <el-tag 
                  :type="item && item.result && item.result.success ? 'success' : 'danger'" 
                  size="small"
                >
                  {{ item && item.result && item.result.success ? '成功' : '失败' }}
                </el-tag>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 右侧信息区域 -->
      <el-col :span="8">
        <!-- 抓包状态 -->
        <el-card class="status-card">
          <template #header>
            <div class="card-header">
              <span>抓包状态</span>
              <el-button
                icon="Refresh"
                size="small"
                @click="loadCaptureStatus"
              />
            </div>
          </template>
          <div class="status-content">
            <el-descriptions :column="1" border>
              <el-descriptions-item label="状态">
                <el-tag :type="captureStatus.is_capturing ? 'success' : 'info'">
                  {{ captureStatus.is_capturing ? '抓包中' : '未抓包' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item v-if="captureStatus.current_file" label="当前文件">
                {{ captureStatus.current_file }}
              </el-descriptions-item>
              <el-descriptions-item v-if="captureStatus.tcpdump_pid" label="进程ID">
                {{ captureStatus.tcpdump_pid }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </el-card>


        
        <!-- GaussDB信息 -->
        <el-card class="info-card">
          <template #header>
            <div class="card-header">
              <span>GaussDB信息</span>
              <el-button
                icon="Refresh"
                size="small"
                @click="loadGaussDBInfo"
                :loading="dbLoading"
              />
            </div>
          </template>
          <div v-if="databases.length > 0">
            <el-collapse v-model="activeCollapse">
              <el-collapse-item
                v-for="database in databases"
                :key="database"
                :title="database"
                :name="database"
              >
                <div v-if="tables[database]">
                  <div v-for="table in tables[database]" :key="table" class="table-item">
                    <el-icon><Document /></el-icon>
                    {{ table }}
                  </div>
                </div>
                <el-button
                  v-else
                  size="small"
                  @click="loadTables(database)"
                  :loading="tableLoading[database]"
                >
                  加载表信息
                </el-button>
              </el-collapse-item>
            </el-collapse>
          </div>
          <div v-else class="no-data">
            <el-empty description="暂无数据库信息" />
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Delete, Document, Refresh, Monitor } from '@element-plus/icons-vue'
import apiService from '../services/api'
import { formatTime } from '@/utils/timezone'

// 响应式数据
const naturalQuery = ref('')
const queryResult = ref(null)
const queryLoading = ref(false)
const asyncCaptureLoading = ref(false)
const autoCapture = ref(true)
const selectedConfigId = ref(null)
const gaussdbConfigs = ref([])
const queryHistory = ref([])
const databases = ref([])
const tables = reactive({})
const dbLoading = ref(false)
const tableLoading = reactive({})
const activeCollapse = ref([])
const activeStatements = ref([])
const captureStatus = ref({
  is_capturing: false,
  current_file: null,
  tcpdump_pid: null
})



// 计算属性
const hasValidConfig = computed(() => {
  return selectedConfigId.value && gaussdbConfigs.value.length > 0
})

// 方法
const loadGaussDBConfigs = async () => {
  try {
    const response = await apiService.getDatabaseConfigs(1, 100, 'gaussdb')
    gaussdbConfigs.value = response.databases || []

    // 自动选择默认配置
    const defaultConfig = gaussdbConfigs.value.find(config => config.is_default)
    if (defaultConfig) {
      selectedConfigId.value = defaultConfig.id
      await loadGaussDBInfo()
    }
  } catch (error) {
    ElMessage.error('加载GaussDB配置失败: ' + error.message)
  }
}

const onDatabaseChange = (configId) => {
  selectedConfigId.value = configId
  // 重新加载GaussDB信息
  loadGaussDBInfo()
}

const loadGaussDBInfo = async () => {
  if (!selectedConfigId.value) return

  dbLoading.value = true
  try {
    // 测试连接并获取数据库信息
    const result = await apiService.testGaussDBConnection(selectedConfigId.value)
    if (result.success) {
      databases.value = result.databases || []
      // 清空之前的表信息
      Object.keys(tables).forEach(key => delete tables[key])
    } else {
      databases.value = []
      ElMessage.error('连接GaussDB失败: ' + (result.error || '未知错误'))
    }
  } catch (error) {
    databases.value = []
    ElMessage.error('加载GaussDB信息失败: ' + error.message)
  } finally {
    dbLoading.value = false
  }
}

const loadTables = async (database) => {
  if (!selectedConfigId.value) return

  tableLoading[database] = true
  try {
    const result = await apiService.getGaussDBTables('public', selectedConfigId.value)
    if (result.success) {
      tables[database] = result.tables || []
    } else {
      ElMessage.error('加载表信息失败')
    }
  } catch (error) {
    ElMessage.error('加载表信息失败: ' + error.message)
  } finally {
    tableLoading[database] = false
  }
}

const executeQuery = async () => {
  if (!naturalQuery.value.trim()) {
    ElMessage.warning('请输入查询内容')
    return
  }

  if (!selectedConfigId.value) {
    ElMessage.warning('请选择GaussDB配置')
    return
  }

  queryLoading.value = true
  try {
    const result = await apiService.processGaussDBQuery(
      naturalQuery.value,
      autoCapture.value,
      selectedConfigId.value
    )

    queryResult.value = result

    // 添加到历史记录
    queryHistory.value.unshift({
      query: naturalQuery.value,
      timestamp: new Date().toLocaleString(),
      result: result
    })

    // 限制历史记录数量
    if (queryHistory.value.length > 10) {
      queryHistory.value = queryHistory.value.slice(0, 10)
    }

    if (result.success) {
      ElMessage.success('GaussDB查询执行成功')
    } else {
      ElMessage.error('GaussDB查询执行失败')
    }

    // 刷新抓包状态
    await loadCaptureStatus()

  } catch (error) {
    ElMessage.error('查询执行失败: ' + error.message)
  } finally {
    queryLoading.value = false
  }
}

const clearQuery = () => {
  naturalQuery.value = ''
  queryResult.value = null
}

const loadHistoryQuery = (historyItem) => {
  naturalQuery.value = historyItem.query
  queryResult.value = historyItem.result
}

const startAsyncCapture = async () => {
  asyncCaptureLoading.value = true
  try {
    const result = await apiService.startGaussDBCapture()
    if (result.success) {
      ElMessage.success('GaussDB抓包已启动: ' + result.packet_file)
      await loadCaptureStatus()
    } else {
      ElMessage.error('启动抓包失败')
    }
  } catch (error) {
    ElMessage.error('启动抓包失败: ' + error.message)
  } finally {
    asyncCaptureLoading.value = false
  }
}

const loadCaptureStatus = async () => {
  try {
    const status = await apiService.getGaussDBCaptureStatus()
    captureStatus.value = status
  } catch (error) {
    console.error('获取抓包状态失败:', error)
  }
}

// 异步抓包方法（与PostgreSQL保持一致）
const executeAsyncCapture = async () => {
  if (!naturalQuery.value.trim()) {
    ElMessage.warning('请输入查询内容')
    return
  }

  if (!selectedConfigId.value) {
    ElMessage.warning('请选择GaussDB配置')
    return
  }

  asyncCaptureLoading.value = true
  try {
    // 判断是否为自然语言查询
    const isNaturalLanguage = !naturalQuery.value.trim().toUpperCase().startsWith('SELECT') &&
                               !naturalQuery.value.trim().toUpperCase().startsWith('INSERT') &&
                               !naturalQuery.value.trim().toUpperCase().startsWith('UPDATE') &&
                               !naturalQuery.value.trim().toUpperCase().startsWith('DELETE') &&
                               !naturalQuery.value.trim().toUpperCase().startsWith('CREATE') &&
                               !naturalQuery.value.trim().toUpperCase().startsWith('DROP') &&
                               !naturalQuery.value.trim().toUpperCase().startsWith('ALTER') &&
                               !naturalQuery.value.trim().toUpperCase().startsWith('SHOW') &&
                               !naturalQuery.value.trim().toUpperCase().startsWith('DESCRIBE') &&
                               !naturalQuery.value.trim().toUpperCase().startsWith('EXPLAIN')

    let result
    if (isNaturalLanguage) {
      // 使用AI+GaussDB异步抓包
      result = await apiService.createAIGaussDBTask(
        selectedConfigId.value,
        naturalQuery.value,
        30 // 默认30秒抓包时长
      )
    } else {
      // 使用普通GaussDB异步抓包
      result = await apiService.createGaussDBCaptureTask(
        selectedConfigId.value,
        naturalQuery.value,
        30 // 默认30秒抓包时长
      )
    }

    ElMessage.success(`GaussDB异步抓包任务已创建，任务ID: ${result.task_id}`)

    // 添加到历史记录
    queryHistory.value.unshift({
      query: naturalQuery.value,
      timestamp: formatTime(new Date()),
      result: {
        success: true,
        task_id: result.task_id,
        type: isNaturalLanguage ? 'ai_async_capture' : 'async_capture',
        message: '任务已提交，请在任务管理中查看进度'
      }
    })

    // 限制历史记录数量
    if (queryHistory.value.length > 10) {
      queryHistory.value = queryHistory.value.slice(0, 10)
    }

  } catch (error) {
    ElMessage.error('创建异步抓包任务失败: ' + error.message)
  } finally {
    asyncCaptureLoading.value = false
  }
}



// 生命周期
onMounted(async () => {
  await loadGaussDBConfigs()
  await loadCaptureStatus()
})
</script>

<style scoped>
.gaussdb-query-interface {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.query-card {
  margin-bottom: 20px;
}

.query-section {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.query-input {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.query-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-start;
}

.result-card {
  margin-bottom: 20px;
}

.result-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.sql-display {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.sql-display h4 {
  margin: 0 0 10px 0;
  color: #495057;
  font-size: 14px;
}

.sql-textarea {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
}

.multi-statement-results {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.multi-statement-results h4 {
  margin: 0 0 15px 0;
  color: #495057;
}

.statement-result {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.statement-result h5 {
  margin: 0;
  color: #6c757d;
  font-size: 13px;
}

.single-statement-result {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.data-table {
  margin-top: 15px;
}

.data-table h4,
.data-table h5 {
  margin: 0 0 10px 0;
  color: #495057;
  font-size: 14px;
}

.packet-info {
  background-color: #e3f2fd;
  padding: 15px;
  border-radius: 6px;
  border: 1px solid #bbdefb;
}

.packet-info h4 {
  margin: 0 0 10px 0;
  color: #1976d2;
  font-size: 14px;
}

.error-message {
  background-color: #ffebee;
  padding: 15px;
  border-radius: 6px;
  border: 1px solid #ffcdd2;
}

.error-message h4,
.error-message h5 {
  margin: 0 0 10px 0;
  color: #d32f2f;
  font-size: 14px;
}

.history-card {
  margin-bottom: 20px;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.history-item {
  padding: 12px;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
}

.history-item:hover {
  background-color: #f8f9fa;
  border-color: #409eff;
}

.history-query {
  font-size: 14px;
  color: #495057;
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.history-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #6c757d;
}

.status-card,
.info-card,
.async-task-card {
  margin-bottom: 20px;
}

.status-content {
  padding: 10px 0;
}

.table-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
  font-size: 14px;
  color: #606266;
}

.table-item:last-child {
  border-bottom: none;
}

.no-data {
  text-align: center;
  padding: 20px;
  color: #909399;
}

.async-task-content {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.task-info p {
  margin: 5px 0;
  font-size: 14px;
  color: #606266;
}

.progress-section {
  margin: 10px 0;
}

.task-result {
  background-color: #f8f9fa;
  padding: 10px;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.task-result h4 {
  margin: 0 0 10px 0;
  color: #495057;
  font-size: 14px;
}

.task-actions {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding-top: 10px;
  border-top: 1px solid #f0f0f0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .gaussdb-query-interface {
    padding: 10px;
  }

  .query-actions {
    flex-direction: column;
  }

  .header-controls {
    flex-direction: column;
    gap: 5px;
  }
}
</style>

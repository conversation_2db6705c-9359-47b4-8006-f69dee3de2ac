<template>
  <div class="oracle-query-interface">
    <el-row :gutter="20">
      <!-- 左侧查询区域 -->
      <el-col :span="16">
        <el-card class="query-card">
          <template #header>
            <div class="card-header">
              <span>Oracle抓包</span>
              <div class="header-controls">
                <el-select
                  v-model="selectedConfigId"
                  placeholder="选择Oracle配置"
                  style="width: 200px; margin-right: 10px;"
                  @change="onDatabaseChange"
                >
                  <el-option
                    v-for="config in oracleConfigs"
                    :key="config.id"
                    :label="`${config.name} (${config.host}:${config.port})`"
                    :value="config.id"
                  >
                    <span>{{ config.name }}</span>
                    <el-tag v-if="config.is_default" type="success" size="small" style="margin-left: 10px;">默认</el-tag>
                  </el-option>
                </el-select>
                <el-switch
                  v-model="autoCapture"
                  active-text="自动抓包"
                  inactive-text="手动抓包"
                />
              </div>
            </div>
          </template>
          
          <div class="query-input">
            <el-input
              v-model="naturalQuery"
              type="textarea"
              :rows="4"
              placeholder="请输入Oracle SQL查询，例如：SELECT * FROM dual、SELECT * FROM user_tables等..."
              @keydown.ctrl.enter="executeQuery"
            />
            <div class="query-actions">
              <el-button
                type="primary"
                @click="executeQuery"
                :loading="queryLoading"
                :disabled="!naturalQuery.trim() || !selectedConfigId"
              >
                <el-icon><Search /></el-icon>
                执行查询
              </el-button>
              <el-button
                type="info"
                @click="executeAsyncCapture"
                :loading="asyncCaptureLoading"
                :disabled="!naturalQuery.trim() || !selectedConfigId"
              >
                <el-icon><MagicStick /></el-icon>
                异步抓包
              </el-button>

              <el-button @click="clearQuery">
                <el-icon><Delete /></el-icon>
                清空
              </el-button>
            </div>
          </div>
        </el-card>

        <!-- Oracle查询结果 -->
        <el-card class="result-card" v-if="queryResult">
          <template #header>
            <div class="card-header">
              <span>查询结果</span>
              <el-tag :type="queryResult.success ? 'success' : 'danger'">
                {{ queryResult.success ? '成功' : '失败' }}
              </el-tag>
            </div>
          </template>
          
          <!-- 查询结果 -->
          <div v-if="queryResult.success" class="result-section">
            <div v-if="queryResult.data && queryResult.data.length > 0" class="table-result">
              <el-table
                :data="queryResult.data"
                border
                stripe
                style="width: 100%"
                max-height="400"
              >
                <el-table-column
                  v-for="column in queryResult.columns"
                  :key="column"
                  :prop="column"
                  :label="column"
                  show-overflow-tooltip
                />
              </el-table>
              <div class="result-info">
                <el-tag type="info">共 {{ queryResult.row_count }} 行数据</el-tag>
                <el-tag type="info">执行时间: {{ queryResult.execution_time?.toFixed(3) }}s</el-tag>
                <el-tag v-if="queryResult.capture_file" type="success">
                  抓包文件: {{ queryResult.capture_file }}
                </el-tag>
                <el-button
                  v-if="queryResult.capture_file"
                  type="primary"
                  size="small"
                  @click="analyzePackets(queryResult.capture_file)"
                  :loading="analyzingPackets"
                >
                  <el-icon><MagicStick /></el-icon>
                  AI解析SQL
                </el-button>
              </div>
            </div>
            <div v-else class="no-data">
              <el-empty description="查询无结果" />
            </div>
          </div>
          
          <!-- 错误信息 -->
          <div v-if="!queryResult.success" class="error-section">
            <el-alert
              :title="queryResult.error || '查询执行失败'"
              type="error"
              show-icon
              :closable="false"
            />
          </div>
        </el-card>
      </el-col>

      <!-- 右侧信息区域 -->
      <el-col :span="8">
        <!-- Oracle容器状态 -->
        <el-card class="info-card">
          <template #header>
            <div class="card-header">
              <span>Oracle容器状态</span>
              <el-button
                size="small"
                @click="checkContainerStatus"
                :loading="statusLoading"
              >
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </template>
          <div class="container-status">
            <div v-if="containerStatus">
              <p><strong>状态:</strong> 
                <el-tag :type="containerStatus.running ? 'success' : 'danger'">
                  {{ containerStatus.running ? '运行中' : '已停止' }}
                </el-tag>
              </p>
              <p v-if="containerStatus.container_id"><strong>容器ID:</strong> {{ containerStatus.container_id.substring(0, 12) }}</p>
              <p v-if="containerStatus.image"><strong>镜像:</strong> {{ containerStatus.image }}</p>
              <p v-if="containerStatus.ports"><strong>端口:</strong> {{ containerStatus.ports }}</p>

            </div>
            <div v-else>
              <el-empty description="无容器信息" />
            </div>
          </div>
        </el-card>

        <!-- Oracle连接信息 -->
        <el-card class="info-card" v-if="connectionInfo">
          <template #header>
            <span>连接信息</span>
          </template>
          <div class="connection-info">
            <p><strong>主机:</strong> {{ connectionInfo.host }}</p>
            <p><strong>端口:</strong> {{ connectionInfo.port }}</p>
            <p><strong>服务名:</strong> {{ connectionInfo.service_name }}</p>
            <p><strong>用户名:</strong> {{ connectionInfo.username }}</p>
            <p><strong>密码:</strong> {{ connectionInfo.password }}</p>
          </div>
        </el-card>

        <!-- Oracle信息 -->
        <el-card class="info-card">
          <template #header>
            <div class="card-header">
              <span>Oracle信息</span>
              <el-button
                size="small"
                @click="loadOracleInfo"
                :loading="dbLoading"
              >
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </template>
          <div class="oracle-info">
            <div v-if="schemas.length > 0">
              <h4>Schemas ({{ schemas.length }})</h4>
              <el-collapse v-model="activeCollapse">
                <el-collapse-item
                  v-for="schema in schemas.slice(0, 10)"
                  :key="schema"
                  :title="schema"
                  :name="schema"
                >
                  <div v-if="!tables[schema]" style="text-align: center; padding: 10px;">
                    <el-button
                      size="small"
                      @click="loadTables(schema)"
                      :loading="tableLoading[schema]"
                    >
                      加载表信息
                    </el-button>
                  </div>
                  <div v-else>
                    <div v-for="table in tables[schema].slice(0, 20)" :key="table.TABLE_NAME" class="table-item">
                      <el-icon><Document /></el-icon>
                      <span>{{ table.TABLE_NAME }}</span>
                      <el-tag v-if="table.NUM_ROWS" size="small" type="info">{{ table.NUM_ROWS }} 行</el-tag>
                    </div>
                  </div>
                </el-collapse-item>
              </el-collapse>
            </div>
            <div v-else-if="!dbLoading">
              <el-empty description="暂无Schema信息" />
            </div>
          </div>
        </el-card>

        <!-- 查询历史 -->
        <el-card class="history-card">
          <template #header>
            <span>查询历史</span>
          </template>
          <div class="history-list">
            <div
              v-for="(item, index) in queryHistory"
              :key="index"
              class="history-item"
              @click="loadHistoryQuery(item)"
            >
              <div class="history-query">{{ item.query }}</div>
              <div class="history-time">{{ item.timestamp }}</div>
              <el-tag 
                :type="item && item.result && item.result.success ? 'success' : 'danger'"
                size="small"
              >
                {{ item && item.result && item.result.success ? '成功' : '失败' }}
              </el-tag>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Delete, Document, Refresh, MagicStick } from '@element-plus/icons-vue'
import apiService from '../services/api'

// 响应式数据
const naturalQuery = ref('')
const queryResult = ref(null)
const queryLoading = ref(false)
const asyncCaptureLoading = ref(false)
const containerLoading = ref(false)
const statusLoading = ref(false)
const autoCapture = ref(true)
const selectedConfigId = ref(null)
const oracleConfigs = ref([])
const queryHistory = ref([])
const schemas = ref([])
const tables = reactive({})
const dbLoading = ref(false)
const tableLoading = reactive({})
const activeCollapse = ref([])
const containerStatus = ref(null)
const connectionInfo = ref(null)
const analyzingPackets = ref(false)
const analysisResult = ref(null)

// 计算属性
const selectedConfig = computed(() => {
  return oracleConfigs.value.find(config => config.id === selectedConfigId.value)
})

// 方法
const loadOracleConfigs = async () => {
  try {
    // 使用专门的Oracle配置获取方法
    oracleConfigs.value = await apiService.getOracleConfigs()

    // 自动选择默认配置
    const defaultConfig = oracleConfigs.value.find(config => config.is_default)
    if (defaultConfig) {
      selectedConfigId.value = defaultConfig.id
    } else if (oracleConfigs.value.length > 0) {
      selectedConfigId.value = oracleConfigs.value[0].id
    } else {
      ElMessage.warning('未找到Oracle数据库配置，请先在数据库管理页面添加Oracle配置')
    }
  } catch (error) {
    ElMessage.error('加载Oracle配置失败: ' + error.message)
  }
}

const onDatabaseChange = async (configId) => {
  selectedConfigId.value = configId
  // 重新加载Oracle信息
  await loadOracleInfo()
  // 重新检查容器状态
  await checkContainerStatus()
  // 重新获取连接信息
  await getConnectionInfo()
}

const loadOracleInfo = async () => {
  if (!selectedConfigId.value) return

  dbLoading.value = true
  try {
    // 获取schemas
    const result = await apiService.getOracleSchemas(selectedConfigId.value)
    if (result.success) {
      schemas.value = result.schemas || []
      // 清空之前的表信息
      Object.keys(tables).forEach(key => delete tables[key])
    } else {
      schemas.value = []
      ElMessage.error('获取Oracle schemas失败')
    }
  } catch (error) {
    schemas.value = []
    ElMessage.error('加载Oracle信息失败: ' + error.message)
  } finally {
    dbLoading.value = false
  }
}

const loadTables = async (schema) => {
  tableLoading[schema] = true
  try {
    const result = await apiService.getOracleTables(schema, selectedConfigId.value)
    tables[schema] = result.tables || []
  } catch (error) {
    ElMessage.error(`加载schema ${schema} 的表信息失败: ` + error.message)
  } finally {
    tableLoading[schema] = false
  }
}

const executeQuery = async () => {
  if (!naturalQuery.value.trim()) {
    ElMessage.warning('请输入查询内容')
    return
  }

  if (!selectedConfigId.value) {
    ElMessage.warning('请选择Oracle配置')
    return
  }

  queryLoading.value = true
  queryResult.value = null // 清空之前的结果

  try {
    // 异步执行查询
    const result = await new Promise((resolve, reject) => {
      // 使用setTimeout模拟异步执行
      setTimeout(async () => {
        try {
          const queryResult = await apiService.executeOracleQuery({
            query: naturalQuery.value,
            capture_packets: autoCapture.value,
            config_id: selectedConfigId.value
          })
          resolve(queryResult)
        } catch (error) {
          reject(error)
        }
      }, 100)
    })

    queryResult.value = result

    // 添加到历史记录
    queryHistory.value.unshift({
      query: naturalQuery.value,
      timestamp: new Date().toLocaleString(),
      result: result
    })

    // 限制历史记录数量
    if (queryHistory.value.length > 10) {
      queryHistory.value = queryHistory.value.slice(0, 10)
    }

    if (result.success) {
      ElMessage.success('Oracle查询执行成功')
    } else {
      ElMessage.error('Oracle查询执行失败: ' + (result.error || '未知错误'))
    }
  } catch (error) {
    ElMessage.error('Oracle查询执行失败: ' + error.message)
  } finally {
    queryLoading.value = false
  }
}

const executeAsyncCapture = async () => {
  if (!naturalQuery.value.trim()) {
    ElMessage.warning('请输入查询内容')
    return
  }

  if (!selectedConfigId.value) {
    ElMessage.warning('请选择Oracle配置')
    return
  }

  asyncCaptureLoading.value = true
  try {
    // 直接创建AI+Oracle异步抓包任务（包含大模型请求）
    const result = await apiService.createAIOracleTask(
      selectedConfigId.value,
      naturalQuery.value,
      30 // 默认30秒抓包时长
    )

    ElMessage.success(`AI异步抓包任务已创建，任务ID: ${result.task_id}`)

    // 添加到历史记录
    queryHistory.value.unshift({
      query: naturalQuery.value,
      timestamp: new Date().toLocaleString(),
      result: {
        success: true,
        task_id: result.task_id,
        type: 'ai_async_capture',
        message: '任务已提交，请在任务管理中查看进度'
      }
    })

    // 限制历史记录数量
    if (queryHistory.value.length > 10) {
      queryHistory.value = queryHistory.value.slice(0, 10)
    }

  } catch (error) {
    ElMessage.error('创建异步抓包任务失败: ' + error.message)
  } finally {
    asyncCaptureLoading.value = false
  }
}

const clearQuery = () => {
  naturalQuery.value = ''
  queryResult.value = null
}

const loadHistoryQuery = (historyItem) => {
  naturalQuery.value = historyItem.query
  queryResult.value = historyItem.result
}

const checkContainerStatus = async () => {
  if (!selectedConfigId.value) return

  statusLoading.value = true
  try {
    const result = await apiService.getOracleContainerStatus(selectedConfigId.value)
    if (result.success) {
      containerStatus.value = result.container_status
    } else {
      ElMessage.error('获取容器状态失败')
    }
  } catch (error) {
    ElMessage.error('获取容器状态失败: ' + error.message)
  } finally {
    statusLoading.value = false
  }
}

const startOracleContainer = async () => {
  if (!selectedConfigId.value) return

  containerLoading.value = true
  try {
    const result = await apiService.startOracleContainer(selectedConfigId.value)
    if (result.success) {
      ElMessage.success(result.message)
      // 刷新容器状态
      await checkContainerStatus()
      // 获取连接信息
      await getConnectionInfo()
    } else {
      ElMessage.error(result.message || '启动容器失败')
    }
  } catch (error) {
    ElMessage.error('启动容器失败: ' + error.message)
  } finally {
    containerLoading.value = false
  }
}

const stopOracleContainer = async () => {
  if (!selectedConfigId.value) return

  containerLoading.value = true
  try {
    const result = await apiService.stopOracleContainer(selectedConfigId.value)
    if (result.success) {
      ElMessage.success(result.message)
      // 刷新容器状态
      await checkContainerStatus()
      connectionInfo.value = null
    } else {
      ElMessage.error(result.message || '停止容器失败')
    }
  } catch (error) {
    ElMessage.error('停止容器失败: ' + error.message)
  } finally {
    containerLoading.value = false
  }
}

const getConnectionInfo = async () => {
  if (!selectedConfigId.value) return

  try {
    const result = await apiService.getOracleConnectionInfo(selectedConfigId.value)
    if (result.success) {
      connectionInfo.value = result.connection_info
    }
  } catch (error) {
    console.error('获取连接信息失败:', error)
  }
}

// AI解析抓包文件
const analyzePackets = async (captureFile) => {
  if (!captureFile) {
    ElMessage.warning('没有抓包文件可供分析')
    return
  }

  analyzingPackets.value = true
  try {
    const result = await apiService.analyzeOraclePackets(captureFile)

    if (result.success) {
      analysisResult.value = result
      ElMessage.success('AI解析完成')

      // 显示解析结果
      if (result.sql_statements && result.sql_statements.length > 0) {
        const sqlList = result.sql_statements.map(sql => `• ${sql}`).join('\n')
        ElMessage({
          message: `解析到 ${result.sql_statements.length} 条SQL语句:\n${sqlList}`,
          type: 'success',
          duration: 10000,
          showClose: true
        })
      } else {
        ElMessage.info('未在抓包文件中发现SQL语句')
      }
    } else {
      ElMessage.error('AI解析失败: ' + (result.error || '未知错误'))
    }
  } catch (error) {
    ElMessage.error('AI解析失败: ' + error.message)
  } finally {
    analyzingPackets.value = false
  }
}

// 生命周期
onMounted(() => {
  loadOracleConfigs().then(() => {
    // 配置加载完成后，加载Oracle信息
    if (selectedConfigId.value) {
      loadOracleInfo()
    }
  })

  // 检查容器状态
  checkContainerStatus()

  // 获取连接信息
  getConnectionInfo()
})
</script>

<style scoped>
.oracle-query-interface {
  padding: 20px;
}

.query-card, .result-card, .info-card, .history-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.query-input {
  margin-bottom: 20px;
}

.query-actions {
  margin-top: 10px;
  display: flex;
  gap: 10px;
}

.result-section, .error-section {
  margin-bottom: 20px;
}

.result-info {
  margin-top: 10px;
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.container-status p, .connection-info p, .oracle-info p {
  margin: 8px 0;
  color: #666;
}

.container-actions {
  margin-top: 10px;
}

.history-list {
  max-height: 300px;
  overflow-y: auto;
}

.history-item {
  padding: 10px;
  border-bottom: 1px solid #eee;
  cursor: pointer;
  transition: background-color 0.2s;
}

.history-item:hover {
  background-color: #f5f5f5;
}

.history-query {
  font-size: 14px;
  margin-bottom: 5px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.history-time {
  font-size: 12px;
  color: #999;
  margin-bottom: 5px;
}

.table-result {
  margin-top: 10px;
}

.table-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 0;
  color: #606266;
}

.no-data {
  text-align: center;
  padding: 20px;
}
</style>

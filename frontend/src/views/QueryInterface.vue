<template>
  <div class="query-interface">
    <el-row :gutter="20">
      <!-- 左侧查询区域 -->
      <el-col :span="16">
        <el-card class="query-card">
          <template #header>
            <div class="card-header">
              <span>MySQL抓包</span>
              <div class="header-controls">
                <el-select
                  v-model="selectedConfigId"
                  placeholder="选择数据库配置"
                  style="width: 200px; margin-right: 10px;"
                  @change="onDatabaseChange"
                >
                  <el-option
                    v-for="config in databaseConfigs"
                    :key="config.id"
                    :label="`${config.name} (${config.host}:${config.port})`"
                    :value="config.id"
                  >
                    <span>{{ config.name }}</span>
                    <el-tag v-if="config.is_default" type="success" size="small" style="margin-left: 10px;">默认</el-tag>
                  </el-option>
                </el-select>
                <el-switch
                  v-model="autoCapture"
                  active-text="自动抓包"
                  inactive-text="手动抓包"
                />
              </div>
            </div>
          </template>
          
          <div class="query-input-section">
            <el-input
              v-model="naturalQuery"
              type="textarea"
              :rows="4"
              placeholder="请输入自然语言查询，例如：查询所有用户信息、删除ID为1的记录、创建一个新的用户表等..."
              class="query-textarea"
            />
            
            <div class="query-actions">
              <el-button
                type="primary"
                icon="Search"
                @click="executeQuery"
                :loading="queryLoading"
                :disabled="!naturalQuery.trim()"
                size="large"
              >
                执行查询
              </el-button>

              <el-button
                type="info"
                icon="Monitor"
                @click="executeAsyncCapture"
                :loading="asyncCaptureLoading"
                :disabled="!naturalQuery.trim() || !selectedConfigId"
                size="large"
              >
                异步抓包
              </el-button>

              <el-button
                icon="Delete"
                @click="clearQuery"
                size="large"
              >
                清空
              </el-button>
            </div>
          </div>
        </el-card>

        <!-- SQL预览和结果 -->
        <el-card class="result-card" v-if="queryResult">
          <template #header>
            <div class="card-header">
              <span>查询结果</span>
              <el-tag :type="queryResult.success ? 'success' : 'danger'">
                {{ queryResult.success ? '成功' : '失败' }}
              </el-tag>
            </div>
          </template>
          
          <!-- 生成的SQL -->
          <div v-if="queryResult.sql_query" class="sql-section">
            <h4>生成的SQL:</h4>
            <el-input
              v-model="queryResult.sql_query"
              type="textarea"
              :rows="3"
              readonly
              class="sql-display"
            />
          </div>
          
          <!-- 查询结果 -->
          <div v-if="queryResult && queryResult.result" class="result-section">
            <h4>执行结果:</h4>
            <div v-if="queryResult.result && queryResult.result.type === 'query'" class="table-result">
              <el-table
                :data="queryResult.result.data || []"
                border
                stripe
                max-height="400"
                style="width: 100%"
              >
                <el-table-column
                  v-for="column in (queryResult.result.columns || [])"
                  :key="column"
                  :prop="column"
                  :label="column"
                  show-overflow-tooltip
                />
              </el-table>
              <div class="result-info">
                共 {{ queryResult.result.row_count || 0 }} 条记录
              </div>
            </div>
            <div v-else-if="queryResult.result && queryResult.result.message" class="modification-result">
              <el-alert
                :title="queryResult.result.message"
                type="success"
                :closable="false"
              />
            </div>
          </div>
          
          <!-- 错误信息 -->
          <div v-if="queryResult.error" class="error-section">
            <el-alert
              :title="queryResult.error"
              type="error"
              :closable="false"
            />
          </div>
          
          <!-- 抓包文件信息 -->
          <div v-if="queryResult.packet_file" class="packet-section">
            <h4>数据包文件:</h4>
            <el-tag type="info">{{ queryResult.packet_file }}</el-tag>
          </div>
        </el-card>
      </el-col>
      
      <!-- 右侧信息面板 -->
      <el-col :span="8">
        <!-- 数据库信息 -->
        <el-card class="info-card">
          <template #header>
            <div class="card-header">
              <span>数据库信息</span>
              <el-button
                icon="Refresh"
                size="small"
                @click="loadDatabaseInfo"
                :loading="dbLoading"
              />
            </div>
          </template>
          
          <div class="database-info">
            <el-collapse v-model="activeCollapse">
              <el-collapse-item
                v-for="database in databases"
                :key="database"
                :title="database"
                :name="database"
              >
                <div v-if="tables[database] && tables[database].length > 0">
                  <div
                    v-for="table in tables[database]"
                    :key="table.name"
                    class="table-item"
                  >
                    <el-tag size="small">{{ table.name }}</el-tag>
                    <span class="table-info">
                      {{ table.rows }} 行
                    </span>
                  </div>
                </div>
                <div v-else-if="tables[database] && tables[database].length === 0" class="no-tables">
                  <span class="text-gray-500">该数据库中没有表</span>
                </div>
                <el-button
                  v-else
                  size="small"
                  @click="loadTables(database)"
                  :loading="tableLoading[database]"
                >
                  加载表信息
                </el-button>
              </el-collapse-item>
            </el-collapse>
          </div>
        </el-card>
        
        <!-- 查询历史 -->
        <el-card class="history-card">
          <template #header>
            <div class="card-header">
              <span>查询历史</span>
              <el-button
                icon="Delete"
                size="small"
                @click="clearHistory"
              />
            </div>
          </template>
          
          <div class="query-history">
            <div
              v-for="(item, index) in queryHistory"
              :key="index"
              class="history-item"
              @click="selectHistoryItem(item)"
            >
              <div class="history-query">{{ item.query }}</div>
              <div class="history-time">{{ item.timestamp }}</div>
            </div>
            <div v-if="queryHistory.length === 0" class="no-history">
              暂无查询历史
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Monitor } from '@element-plus/icons-vue'
import apiService from '../services/api'

export default {
  name: 'QueryInterface',
  components: {
    Monitor
  },
  setup() {
    const naturalQuery = ref('')
    const autoCapture = ref(true)
    const queryLoading = ref(false)
    const asyncCaptureLoading = ref(false)
    const dbLoading = ref(false)
    const queryResult = ref(null)

    const databases = ref([])
    const tables = reactive({})
    const tableLoading = reactive({})
    const activeCollapse = ref([])

    const queryHistory = ref([])

    // 数据库配置相关
    const databaseConfigs = ref([])
    const selectedConfigId = ref(null)
    
    const executeQuery = async () => {
      if (!naturalQuery.value.trim()) {
        ElMessage.warning('请输入查询内容')
        return
      }
      
      queryLoading.value = true
      try {
        const result = await apiService.processQuery(
          naturalQuery.value,
          autoCapture.value,
          selectedConfigId.value
        )
        
        queryResult.value = result
        
        // 添加到历史记录
        queryHistory.value.unshift({
          query: naturalQuery.value,
          timestamp: new Date().toLocaleString(),
          result: result
        })
        
        // 限制历史记录数量
        if (queryHistory.value.length > 10) {
          queryHistory.value = queryHistory.value.slice(0, 10)
        }
        
        if (result.success) {
          ElMessage.success('查询执行成功')
        } else {
          ElMessage.error('查询执行失败')
        }
        
      } catch (error) {
        ElMessage.error('查询失败: ' + error.message)
        queryResult.value = {
          success: false,
          error: error.message
        }
      } finally {
        queryLoading.value = false
      }
    }
    
    const clearQuery = () => {
      naturalQuery.value = ''
      queryResult.value = null
    }

    const executeAsyncCapture = async () => {
      if (!naturalQuery.value.trim()) {
        ElMessage.warning('请输入查询内容')
        return
      }

      if (!selectedConfigId.value) {
        ElMessage.warning('请选择数据库配置')
        return
      }

      asyncCaptureLoading.value = true
      try {
        // 直接创建AI+MySQL异步抓包任务（包含大模型请求）
        const result = await apiService.createAIMySQLTask(
          selectedConfigId.value,
          naturalQuery.value,
          30 // 默认30秒抓包时长
        )

        ElMessage.success(`AI异步抓包任务已创建，任务ID: ${result.task_id}`)

        // 添加到历史记录
        queryHistory.value.unshift({
          query: naturalQuery.value,
          timestamp: new Date().toLocaleString(),
          result: {
            success: true,
            task_id: result.task_id,
            type: 'ai_async_capture',
            message: '任务已提交，请在任务管理中查看进度'
          }
        })

        // 限制历史记录数量
        if (queryHistory.value.length > 10) {
          queryHistory.value = queryHistory.value.slice(0, 10)
        }

      } catch (error) {
        ElMessage.error('创建异步抓包任务失败: ' + error.message)
      } finally {
        asyncCaptureLoading.value = false
      }
    }
    
    const loadDatabaseInfo = async () => {
      if (!selectedConfigId.value) {
        databases.value = []
        return
      }

      dbLoading.value = true
      try {
        // 测试选中的数据库配置连接，获取数据库和表信息
        const result = await apiService.testDatabaseConnection(selectedConfigId.value)
        console.log('🔍 API响应:', result)
        if (result.success) {
          databases.value = result.databases || []
          console.log('📊 数据库列表:', databases.value)
          // 清空之前的表信息
          Object.keys(tables).forEach(key => delete tables[key])
          // 加载表信息
          if (result.tables) {
            const tablesByDb = {}
            result.tables.forEach(table => {
              if (!tablesByDb[table.database_name]) {
                tablesByDb[table.database_name] = []
              }
              // 转换表信息格式以匹配模板期望
              tablesByDb[table.database_name].push({
                name: table.table_name,
                rows: table.table_rows || 0,
                comment: table.table_comment || '',
                type: table.table_type || 'BASE TABLE'
              })
            })
            Object.assign(tables, tablesByDb)
            console.log('📋 转换后的表信息:', tables)
          }
        } else {
          databases.value = []
          ElMessage.error('连接数据库失败: ' + (result.error || '未知错误'))
        }
      } catch (error) {
        databases.value = []
        ElMessage.error('加载数据库信息失败: ' + error.message)
      } finally {
        dbLoading.value = false
      }
    }
    
    const loadTables = async (database) => {
      if (!selectedConfigId.value) {
        ElMessage.warning('请先选择数据库配置')
        return
      }

      tableLoading[database] = true
      try {
        // 如果表信息已经存在，直接返回
        if (tables[database] && tables[database].length > 0) {
          return
        }

        // 重新调用测试连接API来获取最新的表信息
        const result = await apiService.testDatabaseConnection(selectedConfigId.value)
        if (result.success && result.tables) {
          // 过滤出指定数据库的表
          const databaseTables = (result.tables || []).filter(table => table.database_name === database)
          if (databaseTables.length > 0) {
            tables[database] = databaseTables.map(table => ({
              name: table.table_name,
              rows: table.table_rows || 0,
              comment: table.table_comment || ''
            }))
          } else {
            tables[database] = []
            ElMessage.info(`数据库 ${database} 中没有表`)
          }
        } else {
          tables[database] = []
          ElMessage.error(`获取数据库 ${database} 的表信息失败: ${result.error || '未知错误'}`)
        }
      } catch (error) {
        ElMessage.error(`加载数据库 ${database} 的表信息失败: ` + error.message)
        tables[database] = []
      } finally {
        tableLoading[database] = false
      }
    }
    
    const selectHistoryItem = (item) => {
      naturalQuery.value = item.query
      queryResult.value = item.result
    }
    
    const clearHistory = () => {
      queryHistory.value = []
      ElMessage.success('历史记录已清空')
    }
    
    // 加载数据库配置
    const loadDatabaseConfigs = async () => {
      try {
        const response = await apiService.getDatabaseConfigs()
        // 只显示MySQL配置
        databaseConfigs.value = (response.databases || []).filter(db => db.database_type === 'mysql')

        // 设置默认选中的配置
        const defaultConfig = databaseConfigs.value.find(config => config.is_default)
        if (defaultConfig) {
          selectedConfigId.value = defaultConfig.id
        } else if (databaseConfigs.value.length > 0) {
          selectedConfigId.value = databaseConfigs.value[0].id
        }
      } catch (error) {
        ElMessage.error('加载数据库配置失败: ' + error.message)
      }
    }

    // 数据库配置变化处理
    const onDatabaseChange = (configId) => {
      selectedConfigId.value = configId
      // 重新加载数据库信息
      loadDatabaseInfo()
    }

    onMounted(() => {
      loadDatabaseConfigs()
      loadDatabaseInfo()
    })
    
    return {
      naturalQuery,
      autoCapture,
      queryLoading,
      asyncCaptureLoading,
      dbLoading,
      queryResult,
      databases,
      tables,
      tableLoading,
      activeCollapse,
      queryHistory,
      databaseConfigs,
      selectedConfigId,
      executeQuery,
      executeAsyncCapture,
      clearQuery,
      loadDatabaseInfo,
      loadTables,
      loadDatabaseConfigs,
      onDatabaseChange,
      selectHistoryItem,
      clearHistory
    }
  }
}
</script>

<style lang="scss" scoped>
.query-interface {
  .query-card {
    margin-bottom: 20px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .query-input-section {
      .query-textarea {
        margin-bottom: 15px;
      }
      
      .query-actions {
        display: flex;
        gap: 10px;
      }
    }
  }
  
  .result-card {
    .sql-section {
      margin-bottom: 20px;
      
      h4 {
        margin: 0 0 10px 0;
        color: #409eff;
      }
      
      .sql-display {
        font-family: 'Courier New', monospace;
      }
    }
    
    .result-section {
      margin-bottom: 20px;
      
      h4 {
        margin: 0 0 10px 0;
        color: #67c23a;
      }
      
      .result-info {
        margin-top: 10px;
        color: #909399;
        font-size: 14px;
      }
    }
    
    .error-section {
      margin-bottom: 20px;
    }
    
    .packet-section {
      h4 {
        margin: 0 0 10px 0;
        color: #e6a23c;
      }
    }
  }
  
  .info-card {
    margin-bottom: 20px;
    
    .database-info {
      .table-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 5px 0;
        border-bottom: 1px solid #f0f0f0;
        
        .table-info {
          font-size: 12px;
          color: #909399;
        }
      }
    }
  }
  
  .history-card {
    .query-history {
      max-height: 300px;
      overflow-y: auto;
      
      .history-item {
        padding: 10px;
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        margin-bottom: 8px;
        cursor: pointer;
        transition: all 0.3s;
        
        &:hover {
          background-color: #f5f7fa;
          border-color: #409eff;
        }
        
        .history-query {
          font-size: 14px;
          margin-bottom: 5px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        
        .history-time {
          font-size: 12px;
          color: #909399;
        }
      }
      
      .no-history {
        text-align: center;
        color: #909399;
        padding: 20px;
      }
    }
  }
}
</style>

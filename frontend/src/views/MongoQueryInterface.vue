<template>
  <div class="mongo-query-interface">
    <el-row :gutter="20">
      <!-- 左侧查询区域 -->
      <el-col :span="16">
        <el-card class="query-card">
          <template #header>
            <div class="card-header">
              <span>MongoDB抓包</span>
              <div class="header-controls">
                <el-select
                  v-model="selectedConfigId"
                  placeholder="选择MongoDB配置"
                  style="width: 200px; margin-right: 10px;"
                  @change="onDatabaseChange"
                >
                  <el-option
                    v-for="config in mongoConfigs"
                    :key="config.id"
                    :label="`${config.name} (${config.host}:${config.port})`"
                    :value="config.id"
                  >
                    <span>{{ config.name }}</span>
                    <el-tag v-if="config.is_default" type="success" size="small" style="margin-left: 10px;">默认</el-tag>
                  </el-option>
                </el-select>
                <el-switch
                  v-model="autoCapture"
                  active-text="自动抓包"
                  inactive-text="手动抓包"
                />
              </div>
            </div>
          </template>
          
          <div class="query-input-section">
            <el-input
              v-model="naturalQuery"
              type="textarea"
              :rows="4"
              placeholder="请输入MongoDB自然语言查询，例如：查询年龄大于18的用户、插入一个新用户、更新用户信息、删除指定用户等..."
              class="query-textarea"
            />
            
            <div class="query-actions">
              <el-button
                type="primary"
                icon="Search"
                @click="executeQuery"
                :loading="queryLoading"
                :disabled="!naturalQuery.trim()"
                size="large"
              >
                执行查询
              </el-button>

              <el-button
                type="info"
                icon="Monitor"
                @click="executeAsyncCapture"
                :loading="asyncCaptureLoading"
                :disabled="!naturalQuery.trim() || !selectedConfigId"
                size="large"
              >
                异步抓包
              </el-button>

              <el-button
                icon="Delete"
                @click="clearQuery"
                size="large"
              >
                清空
              </el-button>
            </div>
          </div>
        </el-card>

        <!-- MongoDB查询预览和结果 -->
        <el-card class="result-card" v-if="queryResult">
          <template #header>
            <div class="card-header">
              <span>查询结果</span>
              <el-tag :type="queryResult.success ? 'success' : 'danger'">
                {{ queryResult.success ? '成功' : '失败' }}
              </el-tag>
            </div>
          </template>
          
          <!-- 生成的MongoDB查询 -->
          <div v-if="queryResult.mongo_query" class="mongo-section">
            <h4>生成的MongoDB查询:</h4>
            <el-input
              v-model="queryResult.mongo_query"
              type="textarea"
              :rows="3"
              readonly
              class="mongo-display"
            />
          </div>
          
          <!-- 查询结果 -->
          <div v-if="queryResult && queryResult.result" class="result-section">
            <h4>执行结果:</h4>
            <div v-if="queryResult.result && queryResult.result.type === 'query'" class="table-result">
              <el-table
                :data="queryResult.result.data || []"
                border
                stripe
                max-height="400"
                style="width: 100%"
              >
                <el-table-column
                  v-for="column in getTableColumns(queryResult.result.data || [])"
                  :key="column"
                  :prop="column"
                  :label="column"
                  show-overflow-tooltip
                />
              </el-table>
              <div class="result-info">
                共 {{ queryResult.result.count || (queryResult.result.data ? queryResult.result.data.length : 0) }} 条记录
              </div>
            </div>
            <div v-else-if="queryResult.result && queryResult.result.message" class="modification-result">
              <el-alert
                :title="queryResult.result.message"
                type="success"
                :closable="false"
              />
            </div>
          </div>
          
          <!-- 错误信息 -->
          <div v-if="queryResult.error" class="error-section">
            <el-alert
              :title="queryResult.error"
              type="error"
              :closable="false"
            />
          </div>
          
          <!-- 抓包文件信息 -->
          <div v-if="queryResult.packet_file" class="packet-section">
            <h4>数据包文件:</h4>
            <el-tag type="info">{{ queryResult.packet_file }}</el-tag>
          </div>
        </el-card>
      </el-col>
      
      <!-- 右侧信息面板 -->
      <el-col :span="8">
        <!-- MongoDB信息 -->
        <el-card class="info-card">
          <template #header>
            <div class="card-header">
              <span>MongoDB信息</span>
              <el-button
                icon="Refresh"
                size="small"
                @click="loadMongoInfo"
                :loading="dbLoading"
              />
            </div>
          </template>
          
          <div class="mongo-info">
            <el-collapse v-model="activeCollapse">
              <el-collapse-item
                v-for="database in databases"
                :key="database"
                :title="database"
                :name="database"
              >
                <div v-if="collections[database]">
                  <div
                    v-for="collection in collections[database]"
                    :key="collection"
                    class="collection-item"
                  >
                    <el-tag size="small">{{ collection }}</el-tag>
                  </div>
                </div>
                <el-button
                  v-else
                  size="small"
                  @click="loadCollections(database)"
                  :loading="collectionLoading[database]"
                >
                  加载集合信息
                </el-button>
              </el-collapse-item>
            </el-collapse>
          </div>
        </el-card>
        
        <!-- 查询历史 -->
        <el-card class="history-card">
          <template #header>
            <div class="card-header">
              <span>查询历史</span>
              <el-button
                icon="Delete"
                size="small"
                @click="clearHistory"
              />
            </div>
          </template>
          
          <div class="query-history">
            <div
              v-for="(item, index) in queryHistory"
              :key="index"
              class="history-item"
              @click="selectHistoryItem(item)"
            >
              <div class="history-query">{{ item.query }}</div>
              <div class="history-time">{{ item.timestamp }}</div>
            </div>
            <div v-if="queryHistory.length === 0" class="no-history">
              暂无查询历史
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Monitor } from '@element-plus/icons-vue'
import apiService from '../services/api'

export default {
  name: 'MongoQueryInterface',
  components: {
    Monitor
  },
  setup() {
    const naturalQuery = ref('')
    const autoCapture = ref(true)
    const queryLoading = ref(false)
    const asyncCaptureLoading = ref(false)
    const dbLoading = ref(false)
    const queryResult = ref(null)
    const selectedConfigId = ref(null)
    const mongoConfigs = ref([])

    const databases = ref([])
    const collections = reactive({})
    const collectionLoading = reactive({})
    const activeCollapse = ref([])

    const queryHistory = ref([])
    
    const executeQuery = async () => {
      if (!naturalQuery.value.trim()) {
        ElMessage.warning('请输入查询内容')
        return
      }

      if (!selectedConfigId.value) {
        ElMessage.warning('请选择MongoDB配置')
        return
      }

      queryLoading.value = true
      try {
        const result = await apiService.processMongoQuery(
          naturalQuery.value,
          autoCapture.value,
          selectedConfigId.value
        )
        
        queryResult.value = result
        
        // 添加到历史记录
        queryHistory.value.unshift({
          query: naturalQuery.value,
          timestamp: new Date().toLocaleString(),
          result: result
        })
        
        // 限制历史记录数量
        if (queryHistory.value.length > 10) {
          queryHistory.value = queryHistory.value.slice(0, 10)
        }
        
        if (result.success) {
          ElMessage.success('MongoDB查询执行成功')
        } else {
          ElMessage.error('MongoDB查询执行失败')
        }
        
      } catch (error) {
        ElMessage.error('查询失败: ' + error.message)
        queryResult.value = {
          success: false,
          error: error.message
        }
      } finally {
        queryLoading.value = false
      }
    }
    
    const clearQuery = () => {
      naturalQuery.value = ''
      queryResult.value = null
    }

    const executeAsyncCapture = async () => {
      if (!naturalQuery.value.trim()) {
        ElMessage.warning('请输入查询内容')
        return
      }

      if (!selectedConfigId.value) {
        ElMessage.warning('请选择MongoDB配置')
        return
      }

      asyncCaptureLoading.value = true
      try {
        // 直接创建AI+MongoDB异步抓包任务（包含大模型请求）
        const result = await apiService.createAIMongoTask(
          selectedConfigId.value,
          naturalQuery.value,
          30 // 默认30秒抓包时长
        )

        ElMessage.success(`AI异步抓包任务已创建，任务ID: ${result.task_id}`)

        // 添加到历史记录
        queryHistory.value.unshift({
          query: naturalQuery.value,
          timestamp: new Date().toLocaleString(),
          result: {
            success: true,
            task_id: result.task_id,
            type: 'ai_async_capture',
            message: '任务已提交，请在任务管理中查看进度'
          }
        })

        // 限制历史记录数量
        if (queryHistory.value.length > 10) {
          queryHistory.value = queryHistory.value.slice(0, 10)
        }

      } catch (error) {
        ElMessage.error('创建异步抓包任务失败: ' + error.message)
      } finally {
        asyncCaptureLoading.value = false
      }
    }
    
    const loadMongoInfo = async () => {
      dbLoading.value = true
      try {
        const result = await apiService.testMongoConnection()
        if (result.success) {
          databases.value = result.databases || []
          // 清空之前的集合信息
          Object.keys(collections).forEach(key => delete collections[key])
        } else {
          databases.value = []
          ElMessage.error('连接MongoDB失败: ' + (result.error || '未知错误'))
        }
      } catch (error) {
        databases.value = []
        ElMessage.error('加载MongoDB信息失败: ' + error.message)
      } finally {
        dbLoading.value = false
      }
    }
    
    const loadCollections = async (database) => {
      collectionLoading[database] = true
      try {
        const result = await apiService.getMongoCollections(database)
        collections[database] = result.collections || []
      } catch (error) {
        ElMessage.error('加载集合信息失败: ' + error.message)
      } finally {
        collectionLoading[database] = false
      }
    }
    
    const selectHistoryItem = (item) => {
      naturalQuery.value = item.query
      queryResult.value = item.result
    }
    
    const clearHistory = () => {
      queryHistory.value = []
      ElMessage.success('历史记录已清空')
    }
    
    const getTableColumns = (data) => {
      if (!data || data.length === 0) return []

      // 获取所有可能的列名
      const columns = new Set()
      data.forEach(item => {
        if (item && typeof item === 'object') {
          Object.keys(item).forEach(key => columns.add(key))
        }
      })

      return Array.from(columns)
    }

    // 加载MongoDB配置
    const loadMongoConfigs = async () => {
      try {
        const response = await apiService.getDatabaseConfigs()
        // 只显示MongoDB配置
        mongoConfigs.value = (response.databases || []).filter(db => db.database_type === 'mongodb')

        // 自动选择默认配置
        const defaultConfig = mongoConfigs.value.find(config => config.is_default)
        if (defaultConfig) {
          selectedConfigId.value = defaultConfig.id
        } else if (mongoConfigs.value.length > 0) {
          selectedConfigId.value = mongoConfigs.value[0].id
        }
      } catch (error) {
        ElMessage.error('加载MongoDB配置失败: ' + error.message)
      }
    }

    const onDatabaseChange = (configId) => {
      selectedConfigId.value = configId
      // 重新加载MongoDB信息
      loadMongoInfo()
    }

    onMounted(() => {
      loadMongoConfigs()
      loadMongoInfo()
    })
    
    return {
      naturalQuery,
      autoCapture,
      queryLoading,
      asyncCaptureLoading,
      dbLoading,
      queryResult,
      selectedConfigId,
      mongoConfigs,
      databases,
      collections,
      collectionLoading,
      activeCollapse,
      queryHistory,
      executeQuery,
      executeAsyncCapture,
      clearQuery,
      loadMongoInfo,
      loadCollections,
      selectHistoryItem,
      clearHistory,
      getTableColumns,
      onDatabaseChange
    }
  }
}
</script>

<style lang="scss" scoped>
.mongo-query-interface {
  .query-card {
    margin-bottom: 20px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .query-input-section {
      .query-textarea {
        margin-bottom: 15px;
      }
      
      .query-actions {
        display: flex;
        gap: 10px;
      }
    }
  }
  
  .result-card {
    .mongo-section {
      margin-bottom: 20px;
      
      h4 {
        margin: 0 0 10px 0;
        color: #409eff;
      }
      
      .mongo-display {
        font-family: 'Courier New', monospace;
      }
    }
    
    .result-section {
      margin-bottom: 20px;
      
      h4 {
        margin: 0 0 10px 0;
        color: #67c23a;
      }
      
      .result-info {
        margin-top: 10px;
        color: #909399;
        font-size: 14px;
      }
    }
    
    .error-section {
      margin-bottom: 20px;
    }
    
    .packet-section {
      h4 {
        margin: 0 0 10px 0;
        color: #e6a23c;
      }
    }
  }
  
  .info-card {
    margin-bottom: 20px;
    
    .mongo-info {
      .collection-item {
        padding: 5px 0;
        border-bottom: 1px solid #f0f0f0;
      }
    }
  }
  
  .history-card {
    .query-history {
      max-height: 300px;
      overflow-y: auto;
      
      .history-item {
        padding: 10px;
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        margin-bottom: 8px;
        cursor: pointer;
        transition: all 0.3s;
        
        &:hover {
          background-color: #f5f7fa;
          border-color: #409eff;
        }
        
        .history-query {
          font-size: 14px;
          margin-bottom: 5px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        
        .history-time {
          font-size: 12px;
          color: #909399;
        }
      }
      
      .no-history {
        text-align: center;
        color: #909399;
        padding: 20px;
      }
    }
  }
}
</style>

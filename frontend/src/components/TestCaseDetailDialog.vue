<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    title="测试用例详情"
    width="70%"
  >
    <div v-if="testCase" class="test-case-detail">
      <!-- 基本信息 -->
      <div class="section">
        <h3>基本信息</h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="用例标题">{{ testCase.title }}</el-descriptions-item>
          <el-descriptions-item label="所属模块">{{ testCase.module }}</el-descriptions-item>
          <el-descriptions-item label="优先级">
            <el-tag :type="getPriorityType(testCase.priority)">
              {{ getPriorityText(testCase.priority) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(testCase.status)">
              {{ getStatusText(testCase.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建者">{{ testCase.author || '-' }}</el-descriptions-item>
          <el-descriptions-item label="评审者">{{ testCase.reviewer || '-' }}</el-descriptions-item>
          <el-descriptions-item label="自动化程度">
            {{ getAutomationText(testCase.automation_level) }}
          </el-descriptions-item>
          <el-descriptions-item label="预估时间">{{ testCase.estimated_time }}分钟</el-descriptions-item>
          <el-descriptions-item label="测试环境">{{ testCase.test_environment || '-' }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDate(testCase.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ formatDate(testCase.updated_at) }}</el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 执行统计 -->
      <div class="section">
        <h3>执行统计</h3>
        <el-descriptions :column="4" border>
          <el-descriptions-item label="执行次数">{{ testCase.execution_count }}</el-descriptions-item>
          <el-descriptions-item label="成功次数">{{ testCase.success_count }}</el-descriptions-item>
          <el-descriptions-item label="失败次数">{{ testCase.failure_count }}</el-descriptions-item>
          <el-descriptions-item label="通过率">{{ getPassRate(testCase) }}%</el-descriptions-item>
          <el-descriptions-item label="最后执行时间" :span="2">
            {{ testCase.last_execution_time ? formatDate(testCase.last_execution_time) : '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="最后执行结果" :span="2">
            <el-tag v-if="testCase.last_execution_result" :type="getExecutionResultType(testCase.last_execution_result)">
              {{ getExecutionResultText(testCase.last_execution_result) }}
            </el-tag>
            <span v-else>-</span>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 前置条件 -->
      <div class="section" v-if="testCase.preconditions">
        <h3>前置条件</h3>
        <div class="content-box">
          {{ testCase.preconditions }}
        </div>
      </div>

      <!-- 测试步骤 -->
      <div class="section">
        <h3>测试步骤</h3>
        <div class="test-steps">
          <div
            v-for="(step, index) in testCase.test_steps"
            :key="index"
            class="test-step"
          >
            <div class="step-header">
              <span class="step-number">步骤 {{ step.step_number }}</span>
            </div>
            <div class="step-content">
              <div class="step-item">
                <strong>操作：</strong>
                <div class="step-text">{{ step.action }}</div>
              </div>
              <div class="step-item">
                <strong>预期结果：</strong>
                <div class="step-text">{{ step.expected_result }}</div>
              </div>
              <div class="step-item" v-if="step.test_data">
                <strong>测试数据：</strong>
                <div class="step-text">{{ step.test_data }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 整体预期结果 -->
      <div class="section" v-if="testCase.expected_result">
        <h3>整体预期结果</h3>
        <div class="content-box">
          {{ testCase.expected_result }}
        </div>
      </div>

      <!-- 标签 -->
      <div class="section" v-if="testCase.tags && testCase.tags.length > 0">
        <h3>标签</h3>
        <div class="tags">
          <el-tag
            v-for="tag in testCase.tags"
            :key="tag"
            style="margin-right: 8px; margin-bottom: 8px;"
          >
            {{ tag }}
          </el-tag>
        </div>
      </div>

      <!-- 关联信息 -->
      <div class="section" v-if="testCase.related_requirements || testCase.related_defects">
        <h3>关联信息</h3>
        <el-descriptions :column="1" border>
          <el-descriptions-item label="关联需求" v-if="testCase.related_requirements">
            {{ testCase.related_requirements }}
          </el-descriptions-item>
          <el-descriptions-item label="关联缺陷" v-if="testCase.related_defects">
            {{ testCase.related_defects }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 备注 -->
      <div class="section" v-if="testCase.notes">
        <h3>备注</h3>
        <div class="content-box">
          {{ testCase.notes }}
        </div>
      </div>

      <!-- 评审信息 -->
      <div class="section" v-if="testCase.review_status !== 'pending' || testCase.review_comments">
        <h3>评审信息</h3>
        <el-descriptions :column="1" border>
          <el-descriptions-item label="评审状态">
            <el-tag :type="getReviewStatusType(testCase.review_status)">
              {{ getReviewStatusText(testCase.review_status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="评审意见" v-if="testCase.review_comments">
            {{ testCase.review_comments }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="$emit('update:visible', false)">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>


// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  testCase: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:visible'])

// 工具方法
const getPriorityType = (priority) => {
  const types = {
    low: '',
    medium: 'warning',
    high: 'danger',
    critical: 'danger'
  }
  return types[priority] || ''
}

const getPriorityText = (priority) => {
  const texts = {
    low: '低',
    medium: '中',
    high: '高',
    critical: '紧急'
  }
  return texts[priority] || priority
}

const getStatusType = (status) => {
  const types = {
    draft: 'info',
    active: 'success',
    completed: 'primary',
    failed: 'danger',
    deprecated: 'warning',
    archived: ''
  }
  return types[status] || ''
}

const getStatusText = (status) => {
  const texts = {
    draft: '草稿',
    active: '激活',
    completed: '已完成',
    failed: '失败',
    deprecated: '已废弃',
    archived: '已归档'
  }
  return texts[status] || status
}

const getAutomationText = (level) => {
  const texts = {
    manual: '手动',
    semi_auto: '半自动',
    auto: '自动'
  }
  return texts[level] || level
}

const getExecutionResultType = (result) => {
  const types = {
    pass: 'success',
    fail: 'danger',
    blocked: 'warning',
    skip: 'info'
  }
  return types[result] || ''
}

const getExecutionResultText = (result) => {
  const texts = {
    pass: '通过',
    fail: '失败',
    blocked: '阻塞',
    skip: '跳过'
  }
  return texts[result] || result
}

const getReviewStatusType = (status) => {
  const types = {
    pending: 'warning',
    approved: 'success',
    rejected: 'danger'
  }
  return types[status] || ''
}

const getReviewStatusText = (status) => {
  const texts = {
    pending: '待评审',
    approved: '已通过',
    rejected: '已拒绝'
  }
  return texts[status] || status
}

const getPassRate = (testCase) => {
  if (testCase.execution_count === 0) return 0
  return Math.round((testCase.success_count / testCase.execution_count) * 100)
}

// 导入时区工具
import { formatTime } from '@/utils/timezone'

const formatDate = (dateString) => {
  if (!dateString) return ''
  return formatTime(dateString)
}
</script>

<style scoped>
.test-case-detail {
  max-height: 70vh;
  overflow-y: auto;
}

.section {
  margin-bottom: 24px;
}

.section h3 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.content-box {
  padding: 12px;
  background: #f5f7fa;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
  white-space: pre-wrap;
  word-break: break-word;
}

.test-steps {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background: #fafafa;
  padding: 16px;
}

.test-step {
  margin-bottom: 16px;
  padding: 16px;
  background: white;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.test-step:last-child {
  margin-bottom: 0;
}

.step-header {
  margin-bottom: 12px;
}

.step-number {
  font-weight: bold;
  color: #409eff;
  font-size: 14px;
}

.step-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.step-item {
  display: flex;
  flex-direction: column;
}

.step-item strong {
  color: #606266;
  margin-bottom: 4px;
}

.step-text {
  padding: 12px;
  background: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
  white-space: pre-wrap;
  word-break: break-word;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
  max-height: 300px;
  overflow-y: auto;
}

.tags {
  display: flex;
  flex-wrap: wrap;
}

.dialog-footer {
  text-align: right;
}
</style>

<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    title="自动化执行测试用例"
    width="70%"
    :close-on-click-modal="false"
  >
    <div v-if="testCase" class="execution-container">
      <!-- 测试用例信息 -->
      <div class="test-case-info">
        <h4>{{ testCase.title }}</h4>
        <div class="info-row">
          <span class="label">模块:</span>
          <span>{{ testCase.module }}</span>
          <span class="label">数据库类型:</span>
          <el-tag :type="getDatabaseTypeColor(testCase.database_type)" size="small">
            {{ getDatabaseTypeName(testCase.database_type) }}
          </el-tag>
          <span class="label">优先级:</span>
          <el-tag :type="getPriorityType(testCase.priority)" size="small">
            {{ getPriorityText(testCase.priority) }}
          </el-tag>
        </div>
      </div>

      <!-- 执行配置 -->
      <div class="execution-config">
        <h5>执行配置</h5>
        <el-form :model="executionForm" label-width="120px" size="small">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="数据库配置" required>
                <el-select
                  v-model="executionForm.config_id"
                  placeholder="选择数据库配置"
                  style="width: 100%"
                  @change="handleConfigChange"
                >
                  <el-option
                    v-for="config in databaseConfigs"
                    :key="config.id"
                    :label="`${config.name} (${config.host}:${config.port})`"
                    :value="config.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="抓包设置">
                <el-switch
                  v-model="executionForm.capture_enabled"
                  active-text="启用抓包"
                  inactive-text="禁用抓包"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="执行模式">
                <el-radio-group v-model="executionForm.async_execution">
                  <el-radio :label="false">同步执行</el-radio>
                  <el-radio :label="true">异步执行</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="executionForm.capture_enabled">
              <el-form-item label="抓包时长">
                <el-input-number
                  v-model="executionForm.capture_duration"
                  :min="10"
                  :max="300"
                  style="width: 120px"
                />
                <span style="margin-left: 8px; color: #909399;">秒</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20" v-if="testCase?.database_type === 'gaussdb' || testCase?.database_type === 'postgresql' || testCase?.database_type === 'mongodb'">
            <el-col :span="24">
              <el-form-item label="执行器选择">
                <el-switch
                  v-model="executionForm.use_c_executor"
                  active-text="使用C语言执行器"
                  inactive-text="使用Python执行器"
                  :active-value="true"
                  :inactive-value="false"
                />
                <el-tooltip
                  :content="getExecutorTooltipText(testCase?.database_type)"
                  placement="top"
                >
                  <el-icon style="margin-left: 8px; color: #909399;"><QuestionFilled /></el-icon>
                </el-tooltip>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- 执行步骤预览 -->
      <div class="execution-preview" v-if="parsedSteps.length > 0">
        <h5>执行步骤预览</h5>
        <div class="steps-container">
          <div
            v-for="(step, index) in parsedSteps"
            :key="index"
            class="step-item"
            :class="{ 'capture-step': step.capture_strategy === 'capture' }"
          >
            <div class="step-header">
              <span class="step-number">{{ step.step_number }}</span>
              <span class="step-phase">{{ getPhaseText(step.phase) }}</span>
              <el-tag v-if="step.capture_strategy === 'capture'" type="warning" size="small">
                <el-icon><Camera /></el-icon>
                需要抓包
              </el-tag>
            </div>
            <div class="step-content">
              <p class="step-action">{{ step.action }}</p>
              <div v-if="step.sql_statements.length > 0" class="sql-statements">
                <div class="sql-header">SQL语句 ({{ step.sql_statements.length }}条):</div>
                <div
                  v-for="(sql, sqlIndex) in step.sql_statements"
                  :key="sqlIndex"
                  class="sql-item"
                >
                  <code>{{ sql }}</code>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 执行状态 -->
      <div class="execution-status" v-if="executionStatus">
        <h5>执行状态</h5>
        <div class="status-container">
          <div class="status-info">
            <el-tag :type="getStatusType(executionStatus.status)" size="large">
              {{ getStatusText(executionStatus.status) }}
            </el-tag>
            <span class="progress-text">{{ executionStatus.message }}</span>
          </div>
          <el-progress
            v-if="executionStatus.status === 'running'"
            :percentage="Math.round(executionStatus.progress * 100)"
            :stroke-width="8"
            status="active"
          />
        </div>
      </div>

      <!-- 执行结果 -->
      <div class="execution-results" v-if="executionResult">
        <h5>执行结果</h5>
        <div class="results-container">
          <div class="summary">
            <el-row :gutter="20">
              <el-col :span="6">
                <div class="summary-item">
                  <div class="summary-value">{{ executionResult.summary?.total_steps || 0 }}</div>
                  <div class="summary-label">总步骤</div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="summary-item success">
                  <div class="summary-value">{{ executionResult.summary?.successful_steps || 0 }}</div>
                  <div class="summary-label">成功步骤</div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="summary-item error">
                  <div class="summary-value">{{ executionResult.summary?.failed_steps || 0 }}</div>
                  <div class="summary-label">失败步骤</div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="summary-item">
                  <div class="summary-value">{{ Math.round((executionResult.summary?.success_rate || 0) * 100) }}%</div>
                  <div class="summary-label">成功率</div>
                </div>
              </el-col>
            </el-row>
          </div>

          <!-- 抓包文件 -->
          <div v-if="executionResult.summary?.capture_files?.length > 0" class="capture-files">
            <h6>抓包文件</h6>
            <div class="file-list">
              <div
                v-for="(file, index) in executionResult.summary.capture_files"
                :key="index"
                class="file-item"
              >
                <el-icon><Document /></el-icon>
                <span>{{ file }}</span>
                <el-button size="small" type="primary" link @click="downloadCaptureFile(file)">
                  下载
                </el-button>
              </div>
            </div>
          </div>

          <!-- PCAP验证结果 -->
          <div v-if="hasValidationResult" class="pcap-validation">
            <h6>
              <el-icon><Monitor /></el-icon>
              PCAP抓包质量验证
            </h6>

            <!-- 单个用例验证结果 -->
            <div v-if="executionResult.validation_result" class="validation-section">
              <div class="validation-summary">
                <el-row :gutter="16">
                  <el-col :span="6">
                    <div class="validation-item">
                      <div class="validation-value" :class="getValidationStatusClass(executionResult.validation_result.summary?.success_rate)">
                        {{ Math.round((executionResult.validation_result.summary?.success_rate || 0) * 100) }}%
                      </div>
                      <div class="validation-label">验证成功率</div>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="validation-item">
                      <div class="validation-value">{{ executionResult.validation_result.summary?.total_sql_found || 0 }}</div>
                      <div class="validation-label">SQL语句数</div>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="validation-item">
                      <div class="validation-value">{{ executionResult.validation_result.summary?.files_with_sql || 0 }}</div>
                      <div class="validation-label">包含SQL的文件</div>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="validation-item">
                      <div class="validation-value">{{ executionResult.validation_result.summary?.total_files || 0 }}</div>
                      <div class="validation-label">总验证文件</div>
                    </div>
                  </el-col>
                </el-row>
              </div>

              <!-- SQL操作匹配情况 -->
              <div v-if="executionResult.validation_result.operation_matches" class="operation-matches">
                <h7>SQL操作匹配情况</h7>
                <div class="operation-list">
                  <el-tag
                    v-for="(count, operation) in executionResult.validation_result.operation_matches"
                    :key="operation"
                    :type="count > 0 ? 'success' : 'danger'"
                    class="operation-tag"
                  >
                    {{ operation }}: {{ count }}
                  </el-tag>
                </div>
              </div>

              <!-- 验证建议 -->
              <div v-if="executionResult.validation_result.recommendations?.length > 0" class="validation-recommendations">
                <h7>验证建议</h7>
                <ul class="recommendation-list">
                  <li v-for="(rec, index) in executionResult.validation_result.recommendations" :key="index">
                    {{ rec }}
                  </li>
                </ul>
              </div>
            </div>

            <!-- 批量验证结果 -->
            <div v-if="executionResult.batch_validation_result" class="batch-validation-section">
              <div class="validation-summary">
                <el-row :gutter="16">
                  <el-col :span="6">
                    <div class="validation-item">
                      <div class="validation-value" :class="getValidationStatusClass(executionResult.batch_validation_result.summary?.success_rate)">
                        {{ Math.round((executionResult.batch_validation_result.summary?.success_rate || 0) * 100) }}%
                      </div>
                      <div class="validation-label">批量验证成功率</div>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="validation-item">
                      <div class="validation-value">{{ executionResult.batch_validation_result.summary?.total_sql_found || 0 }}</div>
                      <div class="validation-label">总SQL语句数</div>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="validation-item">
                      <div class="validation-value">{{ executionResult.batch_validation_result.summary?.files_with_sql || 0 }}</div>
                      <div class="validation-label">包含SQL的文件</div>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="validation-item">
                      <div class="validation-value">{{ executionResult.batch_validation_result.summary?.total_files || 0 }}</div>
                      <div class="validation-label">总验证文件</div>
                    </div>
                  </el-col>
                </el-row>
              </div>

              <!-- 批量操作匹配情况 -->
              <div v-if="executionResult.batch_validation_result.operation_matches" class="operation-matches">
                <h7>批量SQL操作匹配情况</h7>
                <div class="operation-list">
                  <el-tag
                    v-for="(count, operation) in executionResult.batch_validation_result.operation_matches"
                    :key="operation"
                    :type="count > 0 ? 'success' : 'danger'"
                    class="operation-tag"
                  >
                    {{ operation }}: {{ count }}
                  </el-tag>
                </div>
              </div>

              <!-- 批量验证建议 -->
              <div v-if="executionResult.batch_validation_result.recommendations?.length > 0" class="validation-recommendations">
                <h7>批量验证建议</h7>
                <ul class="recommendation-list">
                  <li v-for="(rec, index) in executionResult.batch_validation_result.recommendations" :key="index">
                    {{ rec }}
                  </li>
                </ul>
              </div>
            </div>
          </div>

          <!-- 步骤SQL校验结果 -->
          <div v-if="executionResult.step_validation" class="step-validation-results">
            <h6>
              <el-icon><DataAnalysis /></el-icon>
              步骤SQL校验结果
            </h6>
            <div class="step-validation-summary">
              <el-row :gutter="16">
                <el-col :span="4">
                  <div class="validation-item">
                    <div class="validation-value">{{ executionResult.step_validation.total_steps || 0 }}</div>
                    <div class="validation-label">总步骤</div>
                  </div>
                </el-col>
                <el-col :span="4">
                  <div class="validation-item success">
                    <div class="validation-value">{{ executionResult.step_validation.matched_steps || 0 }}</div>
                    <div class="validation-label">完全匹配</div>
                  </div>
                </el-col>
                <el-col :span="4">
                  <div class="validation-item warning">
                    <div class="validation-value">{{ executionResult.step_validation.similar_steps || 0 }}</div>
                    <div class="validation-label">相似匹配</div>
                  </div>
                </el-col>
                <el-col :span="4">
                  <div class="validation-item info">
                    <div class="validation-value">{{ executionResult.step_validation.partial_matched_steps || 0 }}</div>
                    <div class="validation-label">部分匹配</div>
                  </div>
                </el-col>
                <el-col :span="4">
                  <div class="validation-item error">
                    <div class="validation-value">{{ executionResult.step_validation.not_found_steps || 0 }}</div>
                    <div class="validation-label">未找到</div>
                  </div>
                </el-col>
                <el-col :span="4">
                  <div class="validation-item">
                    <div class="validation-value" :class="getStepValidationStatusClass(executionResult.step_validation.overall_success_rate)">
                      {{ Math.round((executionResult.step_validation.overall_success_rate || 0) * 100) }}%
                    </div>
                    <div class="validation-label">校验成功率</div>
                  </div>
                </el-col>
              </el-row>
            </div>
            <div v-if="executionResult.step_validation.pcap_file" class="pcap-file-info">
              <el-icon><Document /></el-icon>
              <span>使用抓包文件: {{ executionResult.step_validation.pcap_file }}</span>
            </div>
          </div>

          <!-- 详细执行结果 -->
          <div class="detailed-results">
            <h6>详细执行结果</h6>
            <el-collapse>
              <el-collapse-item
                v-for="(result, index) in executionResult.execution_results"
                :key="index"
                :name="index"
              >
                <template #title>
                  <div class="step-title">
                    <span>步骤 {{ result.step_number }}: {{ result.action }}</span>
                    <div class="step-validation-status" v-if="getStepValidationInfo(result.step_number)">
                      <el-tag
                        :type="getStepValidationTagType(getStepValidationInfo(result.step_number).validation_status)"
                        size="small"
                      >
                        {{ getStepValidationText(getStepValidationInfo(result.step_number).validation_status) }}
                      </el-tag>
                      <span class="similarity-score" v-if="getStepValidationInfo(result.step_number).similarity_score > 0">
                        相似度: {{ Math.round(getStepValidationInfo(result.step_number).similarity_score * 100) }}%
                      </span>
                    </div>
                  </div>
                </template>
                <div class="step-result">
                  <!-- 步骤SQL展示 -->
                  <div v-if="result.sql_results?.length > 0" class="step-sql-display">
                    <h7>执行的SQL语句:</h7>
                    <div
                      v-for="(sqlResult, sqlIndex) in result.sql_results"
                      :key="sqlIndex"
                      class="sql-statement-item"
                    >
                      <div class="sql-code">
                        <code>{{ sqlResult.sql }}</code>
                      </div>
                      <div v-if="getStepValidationInfo(result.step_number)" class="sql-validation-info">
                        <div class="validation-details">
                          <span class="validation-label">PCAP校验:</span>
                          <el-tag
                            :type="getStepValidationTagType(getStepValidationInfo(result.step_number).validation_status)"
                            size="small"
                          >
                            {{ getStepValidationText(getStepValidationInfo(result.step_number).validation_status) }}
                          </el-tag>
                          <span v-if="getStepValidationInfo(result.step_number).packet_numbers?.length > 0" class="packet-info">
                            数据包: {{ getStepValidationInfo(result.step_number).packet_numbers.join(', ') }}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="result-status">
                    <el-tag :type="result.success ? 'success' : 'danger'">
                      {{ result.success ? '成功' : '失败' }}
                    </el-tag>
                    <span v-if="result.capture_file" class="capture-info">
                      <el-icon><Camera /></el-icon>
                      已抓包
                    </span>
                  </div>
                  <div v-if="result.error" class="error-message">
                    <strong>错误信息:</strong> {{ result.error }}
                  </div>
                  <div v-if="result.sql_results?.length > 0" class="sql-results">
                    <strong>SQL执行结果:</strong>
                    <div
                      v-for="(sqlResult, sqlIndex) in result.sql_results"
                      :key="sqlIndex"
                      class="sql-result-item"
                    >
                      <div class="result-data">
                        <pre>{{ JSON.stringify(sqlResult.result, null, 2) }}</pre>
                      </div>
                    </div>
                  </div>
                </div>
              </el-collapse-item>
            </el-collapse>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button
          v-if="!executionStatus || executionStatus.status === 'completed' || executionStatus.status === 'failed'"
          type="primary"
          @click="handleExecute"
          :loading="executing"
          :disabled="!executionForm.config_id"
        >
          {{ executing ? '执行中...' : '开始执行' }}
        </el-button>
        <el-button
          v-if="executionStatus && executionStatus.status === 'running'"
          type="warning"
          @click="handleStop"
        >
          停止执行
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Camera, Document, QuestionFilled, Monitor, DataAnalysis } from '@element-plus/icons-vue'
import testCaseApi from '../services/testCaseApi'
import api from '../services/api'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  testCase: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// Router
const router = useRouter()

// 响应式数据
const executing = ref(false)
const databaseConfigs = ref([])
const parsedSteps = ref([])
const executionStatus = ref(null)
const executionResult = ref(null)
const executionId = ref(null)
const statusTimer = ref(null)

// 执行表单
const executionForm = reactive({
  config_id: null,
  capture_enabled: true,
  async_execution: true, // 默认选择异步执行
  capture_duration: 30,
  use_c_executor: false
})

// 监听对话框显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    resetDialog()
    loadDatabaseConfigs()
    if (props.testCase) {
      parseTestCase()
    }
  } else {
    clearStatusTimer()
  }
})

// 生命周期
onMounted(() => {
  loadDatabaseConfigs()
})

// 方法
const loadDatabaseConfigs = async () => {
  try {
    console.log('开始加载数据库配置...')
    const response = await api.getDatabaseConfigs(1, 100)
    console.log('数据库配置响应:', response)

    if (!response || !response.databases) {
      console.error('数据库配置响应格式错误:', response)
      ElMessage.error('数据库配置响应格式错误')
      return
    }

    // 过滤出与测试用例数据库类型匹配的配置，并且只选择活跃的配置
    if (props.testCase?.database_type) {
      databaseConfigs.value = response.databases.filter(
        config => config.database_type === props.testCase.database_type && config.is_active
      )
      console.log(`过滤${props.testCase.database_type}类型的配置:`, databaseConfigs.value)
    } else {
      databaseConfigs.value = response.databases.filter(config => config.is_active)
      console.log('过滤活跃的配置:', databaseConfigs.value)
    }

    if (databaseConfigs.value.length === 0) {
      const dbType = props.testCase?.database_type || '任意类型'
      ElMessage.warning(`没有找到${dbType}的活跃数据库配置，请先在数据库管理页面添加配置`)
      return
    }

    // 如果有默认配置，自动选择
    const defaultConfig = databaseConfigs.value.find(config => config.is_default)
    if (defaultConfig && !executionForm.config_id) {
      executionForm.config_id = defaultConfig.id
      console.log('自动选择默认配置:', defaultConfig.name)
    } else if (databaseConfigs.value.length === 1) {
      // 如果只有一个配置，自动选择
      executionForm.config_id = databaseConfigs.value[0].id
      console.log('自动选择唯一配置:', databaseConfigs.value[0].name)
    }
  } catch (error) {
    console.error('加载数据库配置失败:', error)
    console.error('错误详情:', error.response?.data)
    ElMessage.error('加载数据库配置失败: ' + (error.message || '网络错误'))
  }
}

const parseTestCase = async () => {
  try {
    const response = await api.post('/test-case-execution/parse-steps', {
      test_case_json: JSON.stringify(props.testCase)
    })
    
    if (response.success) {
      parsedSteps.value = response.parsed_steps.parsed_steps || []
    }
  } catch (error) {
    console.error('解析测试用例失败:', error)
    ElMessage.warning('无法解析测试用例步骤')
  }
}

const handleConfigChange = (configId) => {
  // 可以根据选择的配置调整其他设置
  console.log('选择的数据库配置:', configId)
}

// PCAP验证相关计算属性和方法
const hasValidationResult = computed(() => {
  return executionResult.value?.validation_result || executionResult.value?.batch_validation_result
})

const getValidationStatusClass = (successRate) => {
  if (successRate >= 0.8) return 'validation-excellent'
  if (successRate >= 0.6) return 'validation-good'
  if (successRate >= 0.4) return 'validation-warning'
  return 'validation-poor'
}

// 步骤校验相关方法
const getStepValidationStatusClass = (successRate) => {
  if (successRate >= 0.9) return 'validation-excellent'
  if (successRate >= 0.7) return 'validation-good'
  if (successRate >= 0.5) return 'validation-warning'
  return 'validation-poor'
}

const getStepValidationInfo = (stepNumber) => {
  if (!executionResult.value?.step_validation?.step_results) return null
  return executionResult.value.step_validation.step_results.find(
    result => result.step_number === stepNumber
  )
}

const getStepValidationTagType = (validationStatus) => {
  switch (validationStatus) {
    case 'matched': return 'success'
    case 'similar': return 'warning'
    case 'partial': return 'info'
    case 'not_found': return 'danger'
    case 'error': return 'danger'
    default: return 'info'
  }
}

const getStepValidationText = (validationStatus) => {
  switch (validationStatus) {
    case 'matched': return '完全匹配'
    case 'similar': return '相似匹配'
    case 'partial': return '部分匹配'
    case 'not_found': return '未找到'
    case 'error': return '校验错误'
    default: return '未知'
  }
}

const handleExecute = async () => {
  if (!executionForm.config_id) {
    ElMessage.error('请选择数据库配置')
    return
  }

  try {
    executing.value = true
    executionStatus.value = null
    executionResult.value = null

    const requestData = {
      test_case_json: JSON.stringify(props.testCase),
      config_id: executionForm.config_id,
      capture_enabled: executionForm.capture_enabled,
      async_execution: executionForm.async_execution,
      test_case_id: props.testCase?.id,
      use_c_executor: executionForm.use_c_executor
    }

    console.log('执行请求数据:', requestData)
    console.log('测试用例数据:', props.testCase)

    const response = await api.post('/test-case-execution/execute', requestData)
    console.log('执行响应:', response)

    if (response.success) {
      if (executionForm.async_execution) {
        // 异步执行
        executionId.value = response.execution_id
        ElMessage.success('测试用例已提交异步执行，正在跳转到任务管理')
        // 关闭对话框并跳转到任务管理
        emit('update:visible', false)
        router.push({ name: 'TaskManagement' })
      } else {
        // 同步执行
        executionResult.value = response.result
        executionStatus.value = {
          status: response && response.result && response.result.success ? 'completed' : 'failed',
          progress: 1,
          message: response && response.result && response.result.success ? '执行完成' : '执行失败'
        }
        ElMessage.success('测试用例执行完成')
      }
    } else {
      console.error('执行失败，响应:', response)
      ElMessage.error('执行失败: ' + (response.message || response.error || '未知错误'))
    }
  } catch (error) {
    console.error('执行异常:', error)
    console.error('错误详情:', error.response?.data)

    let errorMessage = '执行失败'
    if (error.response?.data?.detail) {
      errorMessage += ': ' + error.response.data.detail
    } else if (error.message) {
      errorMessage += ': ' + error.message
    }

    ElMessage.error(errorMessage)
    executionStatus.value = {
      status: 'failed',
      progress: 1,
      message: errorMessage
    }
  } finally {
    executing.value = false
  }
}

const startStatusPolling = () => {
  if (!executionId.value) return

  statusTimer.value = setInterval(async () => {
    try {
      const statusResponse = await api.get(`/test-case-execution/status/${executionId.value}`)
      executionStatus.value = statusResponse

      if (statusResponse.status === 'completed' || statusResponse.status === 'failed') {
        clearStatusTimer()
        
        // 获取执行结果
        try {
          const resultResponse = await api.get(`/test-case-execution/result/${executionId.value}`)
          if (resultResponse.success) {
            executionResult.value = resultResponse.result
          }
        } catch (error) {
          console.error('获取执行结果失败:', error)
        }
      }
    } catch (error) {
      console.error('获取执行状态失败:', error)
      clearStatusTimer()
    }
  }, 2000) // 每2秒轮询一次
}

const clearStatusTimer = () => {
  if (statusTimer.value) {
    clearInterval(statusTimer.value)
    statusTimer.value = null
  }
}

const handleStop = async () => {
  try {
    await ElMessageBox.confirm('确定要停止执行吗？', '确认停止', {
      type: 'warning'
    })
    
    clearStatusTimer()
    executionStatus.value = {
      status: 'stopped',
      progress: executionStatus.value?.progress || 0,
      message: '执行已停止'
    }
    
    ElMessage.info('执行已停止')
  } catch (error) {
    // 用户取消
  }
}

const downloadCaptureFile = (filename) => {
  // 下载抓包文件
  const downloadUrl = `/api/captures/download/${filename}`
  window.open(downloadUrl, '_blank')
}

const handleClose = () => {
  clearStatusTimer()
  emit('update:visible', false)
  
  // 如果执行成功，触发成功事件
  if (executionResult.value?.success) {
    emit('success')
  }
}

const resetDialog = () => {
  executing.value = false
  parsedSteps.value = []
  executionStatus.value = null
  executionResult.value = null
  executionId.value = null
  clearStatusTimer()
  
  // 重置表单
  Object.assign(executionForm, {
    config_id: null,
    capture_enabled: true,
    async_execution: true,
    capture_duration: 30,
    use_c_executor: false
  })
}

// 工具方法
const getExecutorTooltipText = (databaseType) => {
  if (databaseType === 'gaussdb') {
    return 'C语言执行器使用libpq库直接连接GaussDB，减少Python解释器开销，提供更好的性能'
  } else if (databaseType === 'postgresql') {
    return 'C语言执行器使用libpq库直接连接PostgreSQL，减少Python解释器开销，提供更好的性能'
  } else if (databaseType === 'mongodb') {
    return 'C语言执行器使用libmongoc库直接连接MongoDB，减少Python解释器开销，提供更好的性能'
  }
  return 'C语言执行器提供更好的性能'
}

const getDatabaseTypeName = (type) => {
  const nameMap = {
    'mysql': 'MySQL',
    'postgresql': 'PostgreSQL',
    'mongodb': 'MongoDB',
    'oracle': 'Oracle',
    'gaussdb': 'GaussDB'
  }
  return nameMap[type] || type
}

const getDatabaseTypeColor = (type) => {
  const colorMap = {
    'mysql': 'primary',
    'postgresql': 'success',
    'mongodb': 'warning',
    'oracle': 'danger',
    'gaussdb': 'info'
  }
  return colorMap[type] || 'info'
}

const getPriorityType = (priority) => {
  const types = {
    low: '',
    medium: 'warning',
    high: 'danger',
    critical: 'danger'
  }
  return types[priority] || ''
}

const getPriorityText = (priority) => {
  const texts = {
    low: '低',
    medium: '中',
    high: '高',
    critical: '紧急'
  }
  return texts[priority] || priority
}

const getPhaseText = (phase) => {
  const texts = {
    setup: '环境准备',
    execution: '执行查询',
    validation: '结果验证',
    cleanup: '环境清理'
  }
  return texts[phase] || phase
}

const getStatusType = (status) => {
  const types = {
    running: 'warning',
    completed: 'success',
    failed: 'danger',
    stopped: 'info'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    running: '执行中',
    completed: '执行完成',
    failed: '执行失败',
    stopped: '已停止'
  }
  return texts[status] || status
}
</script>

<style scoped>
.execution-container {
  max-height: 70vh;
  overflow-y: auto;
}

.test-case-info {
  margin-bottom: 20px;
  padding: 16px;
  background: #f5f7fa;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.test-case-info h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 16px;
}

.info-row {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.info-row .label {
  color: #606266;
  font-weight: 500;
}

.execution-config {
  margin-bottom: 20px;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.execution-config h5 {
  margin: 0 0 16px 0;
  color: #303133;
}

.execution-preview {
  margin-bottom: 20px;
}

.execution-preview h5 {
  margin: 0 0 16px 0;
  color: #303133;
}

.steps-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.step-item {
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.step-item:last-child {
  border-bottom: none;
}

.step-item.capture-step {
  background: #fef9e7;
}

.step-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.step-number {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: #409eff;
  color: white;
  border-radius: 50%;
  font-size: 12px;
  font-weight: bold;
}

.step-phase {
  font-size: 12px;
  color: #909399;
  background: #f0f0f0;
  padding: 2px 8px;
  border-radius: 12px;
}

.step-action {
  margin: 0 0 8px 0;
  color: #303133;
  font-weight: 500;
}

.sql-statements {
  background: #f8f9fa;
  padding: 8px;
  border-radius: 4px;
  border-left: 3px solid #409eff;
}

.sql-header {
  font-size: 12px;
  color: #606266;
  margin-bottom: 4px;
}

.sql-item {
  margin-bottom: 4px;
}

.sql-item code {
  font-size: 12px;
  background: white;
  padding: 4px 8px;
  border-radius: 2px;
  display: block;
  border: 1px solid #e4e7ed;
}

.execution-status {
  margin-bottom: 20px;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.execution-status h5 {
  margin: 0 0 16px 0;
  color: #303133;
}

.status-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.status-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.progress-text {
  color: #606266;
}

.execution-results {
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.execution-results h5,
.execution-results h6 {
  margin: 0 0 16px 0;
  color: #303133;
}

.summary {
  margin-bottom: 20px;
}

.summary-item {
  text-align: center;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.summary-item.success {
  border-color: #67c23a;
  background: #f0f9ff;
}

.summary-item.error {
  border-color: #f56c6c;
  background: #fef0f0;
}

.summary-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.summary-label {
  font-size: 12px;
  color: #909399;
}

.capture-files {
  margin-bottom: 20px;
}

.file-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.file-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 4px;
}

.detailed-results {
  margin-top: 20px;
}

.step-result {
  padding: 12px;
}

.result-status {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.capture-info {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #e6a23c;
  font-size: 12px;
}

.error-message {
  color: #f56c6c;
  margin-bottom: 8px;
}

.sql-results {
  margin-top: 8px;
}

.sql-result-item {
  margin-bottom: 12px;
}

.sql-result-item code {
  display: block;
  background: #f8f9fa;
  padding: 8px;
  border-radius: 4px;
  margin-bottom: 4px;
}

.result-data {
  background: #f0f0f0;
  padding: 8px;
  border-radius: 4px;
  max-height: 200px;
  overflow-y: auto;
}

/* PCAP验证样式 */
.pcap-validation {
  margin-bottom: 20px;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background: #fafbfc;
}

.pcap-validation h6 {
  margin: 0 0 16px 0;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.validation-section,
.batch-validation-section {
  margin-bottom: 16px;
}

.validation-summary {
  margin-bottom: 16px;
}

.validation-item {
  text-align: center;
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background: white;
}

.validation-value {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 4px;
}

.validation-value.validation-excellent {
  color: #67c23a;
}

.validation-value.validation-good {
  color: #409eff;
}

.validation-value.validation-warning {
  color: #e6a23c;
}

.validation-value.validation-poor {
  color: #f56c6c;
}

.validation-label {
  font-size: 12px;
  color: #909399;
}

.operation-matches {
  margin-bottom: 16px;
}

.operation-matches h7 {
  display: block;
  font-size: 14px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 8px;
}

.operation-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.operation-tag {
  margin: 0;
}

.validation-recommendations {
  margin-bottom: 16px;
}

.validation-recommendations h7 {
  display: block;
  font-size: 14px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 8px;
}

.recommendation-list {
  margin: 0;
  padding-left: 20px;
}

.recommendation-list li {
  margin-bottom: 4px;
  color: #606266;
  font-size: 13px;
}

.result-data pre {
  margin: 0;
  font-size: 12px;
  white-space: pre-wrap;
  word-break: break-all;
}

.dialog-footer {
  text-align: right;
}

/* 步骤校验结果样式 */
.step-validation-results {
  margin-bottom: 20px;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background: #fafbfc;
}

.step-validation-results h6 {
  margin: 0 0 16px 0;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.step-validation-summary {
  margin-bottom: 12px;
}

.pcap-file-info {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #606266;
  font-size: 14px;
}

/* 步骤标题样式 */
.step-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.step-validation-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.similarity-score {
  font-size: 12px;
  color: #909399;
}

/* SQL语句展示样式 */
.step-sql-display {
  margin-bottom: 16px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.step-sql-display h7 {
  margin: 0 0 12px 0;
  color: #495057;
  font-size: 14px;
  font-weight: 600;
}

.sql-statement-item {
  margin-bottom: 12px;
}

.sql-statement-item:last-child {
  margin-bottom: 0;
}

.sql-code {
  margin-bottom: 8px;
}

.sql-code code {
  display: block;
  padding: 8px 12px;
  background: #ffffff;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.4;
  color: #495057;
  white-space: pre-wrap;
  word-break: break-all;
}

.sql-validation-info {
  padding: 8px 12px;
  background: #ffffff;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  border-top: none;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.validation-details {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 13px;
}

.validation-label {
  color: #6c757d;
  font-weight: 500;
}

.packet-info {
  color: #6c757d;
  font-size: 12px;
}
</style>

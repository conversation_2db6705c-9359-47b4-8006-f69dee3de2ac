<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    title="AI生成测试用例"
    width="80%"
    :before-close="handleClose"
  >
    <div v-loading="loading" element-loading-text="AI正在生成测试用例...">
      <!-- 需求输入区域 -->
      <el-card class="mb-4" shadow="never">
        <template #header>
          <div class="card-header">
            <span>需求描述</span>
          </div>
        </template>

        <el-form :model="form" label-width="120px">
          <el-form-item label="需求描述" required>
            <el-input
              v-model="form.requirement"
              type="textarea"
              :rows="4"
              placeholder="请详细描述测试需求，例如：测试用户登录功能的SQL查询操作"
            />
          </el-form-item>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="数据库类型">
                <el-select v-model="form.databaseType" style="width: 100%" @change="onDatabaseTypeChange">
                  <el-option label="MySQL" value="mysql" />
                  <el-option label="PostgreSQL" value="postgresql" />
                  <el-option label="MongoDB" value="mongodb" />
                  <el-option label="Oracle" value="oracle" />
                  <el-option label="GaussDB" value="gaussdb" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="操作类型">
                <el-select v-model="form.operationType" style="width: 100%">
                  <el-option label="查询(SELECT)" value="查询" />
                  <el-option label="插入(INSERT)" value="插入" />
                  <el-option label="更新(UPDATE)" value="更新" />
                  <el-option label="删除(DELETE)" value="删除" />
                  <el-option label="创建表(CREATE)" value="创建" />
                  <el-option label="事务处理" value="事务" />
                  <el-option label="存储过程" value="存储过程" />
                  <el-option label="综合测试" value="综合" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="数据库版本">
                <el-select
                  v-model="form.databaseVersion"
                  placeholder="选择数据库版本"
                  style="width: 100%"
                  clearable
                >
                  <el-option
                    v-for="version in availableVersions"
                    :key="version"
                    :label="version"
                    :value="version"
                  />
                </el-select>
                <span class="form-tip">选择目标数据库版本，AI将生成对应版本特性的测试用例</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="数量限制" v-if="form.operationType !== '事务' && form.operationType !== '综合'">
                <el-input-number
                  v-model="form.batchSize"
                  :min="0"
                  :max="50"
                  :step="1"
                  placeholder="最大生成数量"
                />
                <span class="form-tip">0表示不限制，由AI智能决定；大于0表示最大生成数量限制</span>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item>
            <el-button type="primary" @click="generateTestCase" :loading="loading">
              <el-icon><MagicStick /></el-icon>
              智能生成测试用例{{ form.batchSize > 0 ? `（最多${form.batchSize}个）` : '（AI智能决定数量）' }}
            </el-button>
            <el-button type="success" @click="asyncGenerateTestCase" :loading="asyncLoading">
              <el-icon><Timer /></el-icon>
              异步生成（后台处理）
            </el-button>
            <!-- <el-button @click="useWorkflow" :loading="loading">
              <el-icon><Connection /></el-icon>
              完整工作流（生成+评审+改进）
            </el-button> -->
          </el-form-item>
        </el-form>
      </el-card>



      <!-- 生成结果区域 -->
      <div v-if="generatedTestCase || generatedTestCases.length > 0 || reviewResult || improvedTestCase">
        <el-tabs v-model="activeTab" type="card">
          <!-- 单个生成结果 -->
          <el-tab-pane label="生成结果" name="generated" v-if="generatedTestCase && !isBatchResult">
            <el-card shadow="never">
              <template #header>
                <div class="card-header">
                  <span>AI生成的测试用例</span>
                  <div>
                    <el-button size="small" @click="reviewTestCase" :loading="reviewLoading">
                      <el-icon><View /></el-icon>
                      评审用例
                    </el-button>
                    <el-button size="small" type="primary" @click="saveTestCase(generatedTestCase)">
                      <el-icon><Check /></el-icon>
                      保存用例
                    </el-button>
                  </div>
                </div>
              </template>

              <TestCasePreview :test-case="generatedTestCase" />
            </el-card>
          </el-tab-pane>

          <!-- 批量生成结果 -->
          <el-tab-pane :label="`批量生成结果 (${generatedTestCases.length})`" name="batch-generated" v-if="isBatchResult && generatedTestCases.length > 0">
            <div class="batch-results">
              <div class="batch-header">
                <el-alert
                  :title="`成功生成 ${generatedTestCases.length} 个测试用例`"
                  type="success"
                  :closable="false"
                  show-icon
                />
                <div class="batch-actions">
                  <el-button type="primary" @click="saveAllTestCases">
                    <el-icon><Check /></el-icon>
                    保存所有用例
                  </el-button>
                </div>
              </div>

              <div class="test-case-list">
                <el-collapse v-model="expandedCases">
                  <el-collapse-item
                    v-for="(testCase, index) in generatedTestCases"
                    :key="index"
                    :title="`${index + 1}. ${testCase.title}`"
                    :name="index"
                  >
                    <template #title>
                      <div class="collapse-title">
                        <span>{{ index + 1 }}. {{ testCase.title }}</span>
                        <el-button
                          size="small"
                          type="primary"
                          @click.stop="saveTestCase(testCase)"
                          style="margin-left: auto; margin-right: 10px;"
                        >
                          保存此用例
                        </el-button>
                      </div>
                    </template>
                    <TestCasePreview :test-case="testCase" />
                  </el-collapse-item>
                </el-collapse>
              </div>
            </div>
          </el-tab-pane>

          <!-- 评审结果 -->
          <el-tab-pane label="评审结果" name="review" v-if="reviewResult">
            <el-card shadow="never">
              <template #header>
                <div class="card-header">
                  <span>AI评审结果</span>
                  <div v-if="reviewResult.status !== '通过'">
                    <el-button size="small" type="warning" @click="regenerateTestCase" :loading="regenerateLoading">
                      <el-icon><Refresh /></el-icon>
                      根据建议重新生成
                    </el-button>
                  </div>
                </div>
              </template>

              <div class="review-result">
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-statistic title="评审评分" :value="reviewResult.score" suffix="/10">
                      <template #prefix>
                        <el-icon :color="getScoreColor(reviewResult.score)">
                          <Star />
                        </el-icon>
                      </template>
                    </el-statistic>
                  </el-col>
                  <el-col :span="8">
                    <div class="review-status">
                      <span class="label">评审状态:</span>
                      <el-tag :type="getStatusType(reviewResult.status)">
                        {{ reviewResult.status }}
                      </el-tag>
                    </div>
                  </el-col>
                </el-row>

                <el-divider />

                <div class="review-issues" v-if="reviewResult.issues && reviewResult.issues.length > 0">
                  <h4>问题和建议:</h4>
                  <ul>
                    <li v-for="(issue, index) in reviewResult.issues" :key="index">
                      {{ issue }}
                    </li>
                  </ul>
                </div>

                <div class="review-summary" v-if="reviewResult.summary">
                  <h4>总结:</h4>
                  <p>{{ reviewResult.summary }}</p>
                </div>
              </div>
            </el-card>
          </el-tab-pane>

          <!-- 改进结果 -->
          <el-tab-pane label="改进结果" name="improved" v-if="improvedTestCase">
            <el-card shadow="never">
              <template #header>
                <div class="card-header">
                  <span>AI改进后的测试用例</span>
                  <div>
                    <el-button size="small" type="primary" @click="saveTestCase(improvedTestCase)">
                      <el-icon><Check /></el-icon>
                      保存改进用例
                    </el-button>
                  </div>
                </div>
              </template>

              <TestCasePreview :test-case="improvedTestCase" />
            </el-card>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { MagicStick, Connection, Check, Refresh, Star, Timer } from '@element-plus/icons-vue'
import testCaseApi from '@/services/testCaseApi'
import TestCasePreview from './TestCasePreview.vue'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// 响应式数据
const loading = ref(false)
const asyncLoading = ref(false)
const reviewLoading = ref(false)
const regenerateLoading = ref(false)
const activeTab = ref('generated')
const expandedCases = ref([])

// 数据库版本数据
const databaseVersions = ref({})
const availableVersions = ref([])

// 表单数据
const form = reactive({
  requirement: '',
  databaseType: 'mysql',
  databaseVersion: '',
  operationType: '查询',
  batchSize: 0  // 默认不限制数量，由AI智能决定
})

// 生成结果
const generatedTestCase = ref(null)
const generatedTestCases = ref([]) // 批量生成结果
const reviewResult = ref(null)
const improvedTestCase = ref(null)
const isBatchResult = ref(false)

// 监听对话框显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    resetData()
  }
})

// 重置数据
const resetData = () => {
  form.requirement = ''
  form.databaseType = 'mysql'
  form.databaseVersion = ''
  form.operationType = '查询'
  form.batchSize = 0  // 默认不限制数量，由AI智能决定
  generatedTestCase.value = null
  generatedTestCases.value = []
  reviewResult.value = null
  improvedTestCase.value = null
  isBatchResult.value = false
  activeTab.value = 'generated'
}

// 获取数据库版本选项
const loadDatabaseVersions = async () => {
  try {
    const response = await testCaseApi.getDatabaseVersions()
    if (response.success) {
      databaseVersions.value = response.data
      // 初始化可用版本列表
      availableVersions.value = databaseVersions.value[form.databaseType] || []

      // 设置默认版本
      if (availableVersions.value.length > 0 && !form.databaseVersion) {
        form.databaseVersion = availableVersions.value[0]
      }
    }
  } catch (error) {
    console.error('获取数据库版本失败:', error)
  }
}

// 数据库类型变化处理
const onDatabaseTypeChange = (value) => {
  // 更新可用版本列表
  availableVersions.value = databaseVersions.value[value] || []

  // 设置默认版本
  if (availableVersions.value.length > 0) {
    form.databaseVersion = availableVersions.value[0]
  } else {
    form.databaseVersion = ''
  }
}

// 生成测试用例（总是批量生成）
const generateTestCase = async () => {
  if (!form.requirement.trim()) {
    ElMessage.warning('请输入需求描述')
    return
  }

  loading.value = true
  try {
    // 总是使用批量生成API，确保生成多个测试用例
    const response = await testCaseApi.aiBatchGenerateTestCases(
      form.requirement,
      form.databaseType,
      form.databaseVersion,
      form.operationType,
      form.batchSize
    )

    if (response.success) {
      // 批量生成结果
      generatedTestCases.value = response.data || []
      generatedTestCase.value = null
      isBatchResult.value = true

      const count = response.data ? response.data.length : 0
      if (count > 0) {
        ElMessage.success(`智能分析完成，共生成 ${count} 个测试用例`)
      } else {
        ElMessage.warning('生成完成，但没有生成测试用例，请尝试调整需求描述')
      }

      reviewResult.value = null
      improvedTestCase.value = null
      activeTab.value = 'batch-generated'
    } else {
      ElMessage.error(response.message || '生成失败')
    }
  } catch (error) {
    ElMessage.error('生成测试用例失败: ' + error.message)
  } finally {
    loading.value = false
  }
}





// 异步生成测试用例（后台处理，不显示进度）
const asyncGenerateTestCase = async () => {
  if (!form.requirement.trim()) {
    ElMessage.warning('请输入需求描述')
    return
  }

  asyncLoading.value = true
  try {
    // 提交异步任务
    const response = await testCaseApi.aiAsyncGenerateTestCases(
      form.requirement,
      form.databaseType,
      form.databaseVersion,
      form.operationType,
      form.batchSize
    )

    if (response.success) {
      ElMessage.success(`异步任务已提交成功！任务将在后台处理，完成后可在任务管理页面查看结果。`)

      // 关闭对话框，让用户去任务管理页面查看
      setTimeout(() => {
        handleClose()
      }, 2000)
    } else {
      ElMessage.error(response.message || '提交异步任务失败')
    }
  } catch (error) {
    ElMessage.error('提交异步任务失败: ' + error.message)
  } finally {
    asyncLoading.value = false
  }
}
// 使用完整工作流
const useWorkflow = async () => {
  if (!form.requirement.trim()) {
    ElMessage.warning('请输入需求描述')
    return
  }

  loading.value = true
  try {
    const response = await testCaseApi.aiTestCaseWorkflow(
      form.requirement,
      form.databaseType,
      form.databaseVersion,
      form.operationType
    )

    if (response.success) {
      generatedTestCase.value = response.data.original_test_case
      reviewResult.value = response.data.review_result
      improvedTestCase.value = response.data.improved_test_case

      // 根据结果决定显示哪个标签页
      if (improvedTestCase.value) {
        activeTab.value = 'improved'
      } else if (reviewResult.value) {
        activeTab.value = 'review'
      } else {
        activeTab.value = 'generated'
      }

      ElMessage.success('完整工作流执行成功')
    } else {
      ElMessage.error(response.message || '工作流执行失败')
    }
  } catch (error) {
    ElMessage.error('工作流执行失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

// 评审测试用例
const reviewTestCase = async () => {
  if (!generatedTestCase.value) {
    ElMessage.warning('请先生成测试用例')
    return
  }

  reviewLoading.value = true
  try {
    const response = await testCaseApi.aiReviewTestCase(generatedTestCase.value)

    if (response.success) {
      reviewResult.value = response.data
      activeTab.value = 'review'
      ElMessage.success('测试用例评审完成')
    } else {
      ElMessage.error(response.message || '评审失败')
    }
  } catch (error) {
    ElMessage.error('评审测试用例失败: ' + error.message)
  } finally {
    reviewLoading.value = false
  }
}

// 重新生成测试用例
const regenerateTestCase = async () => {
  if (!generatedTestCase.value || !reviewResult.value) {
    ElMessage.warning('请先生成并评审测试用例')
    return
  }

  regenerateLoading.value = true
  try {
    const response = await testCaseApi.aiRegenerateTestCase(
      generatedTestCase.value,
      reviewResult.value
    )

    if (response.success) {
      improvedTestCase.value = response.data
      activeTab.value = 'improved'
      ElMessage.success('测试用例重新生成成功')
    } else {
      ElMessage.error(response.message || '重新生成失败')
    }
  } catch (error) {
    ElMessage.error('重新生成测试用例失败: ' + error.message)
  } finally {
    regenerateLoading.value = false
  }
}

// 保存测试用例
const saveTestCase = async (testCase) => {
  try {
    await ElMessageBox.confirm('确认保存此测试用例？', '确认', {
      type: 'info'
    })

    // 添加用户选择的数据库版本信息
    const testCaseWithVersion = {
      ...testCase,
      database_type: form.databaseType,
      database_version: form.databaseVersion,
      operation_type: form.operationType
    }

    await testCaseApi.createTestCase(testCaseWithVersion)
    ElMessage.success('测试用例保存成功')
    emit('success')
    handleClose()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('保存测试用例失败: ' + error.message)
    }
  }
}

// 获取评分颜色
const getScoreColor = (score) => {
  if (score >= 8) return '#67c23a'
  if (score >= 6) return '#e6a23c'
  return '#f56c6c'
}

// 获取状态类型
const getStatusType = (status) => {
  switch (status) {
    case '通过': return 'success'
    case '需要修改': return 'warning'
    case '不通过': return 'danger'
    default: return 'info'
  }
}

// 保存所有测试用例
const saveAllTestCases = async () => {
  try {
    await ElMessageBox.confirm(`确认保存所有 ${generatedTestCases.value.length} 个测试用例？`, '批量保存', {
      type: 'info'
    })

    let successCount = 0
    let failCount = 0

    for (const testCase of generatedTestCases.value) {
      try {
        // 添加用户选择的数据库版本信息
        const testCaseWithVersion = {
          ...testCase,
          database_type: form.databaseType,
          database_version: form.databaseVersion,
          operation_type: form.operationType
        }

        await testCaseApi.createTestCase(testCaseWithVersion)
        successCount++
      } catch (error) {
        failCount++
        console.error('保存测试用例失败:', error)
      }
    }

    if (successCount > 0) {
      ElMessage.success(`成功保存 ${successCount} 个测试用例${failCount > 0 ? `，${failCount} 个失败` : ''}`)
      emit('success')
      if (failCount === 0) {
        handleClose()
      }
    } else {
      ElMessage.error('所有测试用例保存失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量保存失败: ' + error.message)
    }
  }
}



// 关闭对话框
const handleClose = () => {
  emit('update:visible', false)
}

// 组件挂载时加载数据库版本
onMounted(() => {
  loadDatabaseVersions()
})
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.review-result {
  padding: 16px 0;
}

.review-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.review-status .label {
  font-weight: 500;
  color: #606266;
}

.review-issues {
  margin-top: 16px;
}

.review-issues h4 {
  margin-bottom: 8px;
  color: #303133;
}

.review-issues ul {
  margin: 0;
  padding-left: 20px;
}

.review-issues li {
  margin-bottom: 8px;
  line-height: 1.5;
}

.review-summary {
  margin-top: 16px;
}

.review-summary h4 {
  margin-bottom: 8px;
  color: #303133;
}

.review-summary p {
  line-height: 1.6;
  color: #606266;
}

.mb-4 {
  margin-bottom: 16px;
}

.batch-results {
  padding: 16px 0;
}

.batch-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.batch-actions {
  margin-left: 16px;
}

.test-case-list {
  margin-top: 16px;
}

.collapse-title {
  display: flex;
  align-items: center;
  width: 100%;
  font-weight: 500;
}

:deep(.el-collapse-item__header) {
  padding-right: 0;
}

:deep(.el-collapse-item__content) {
  padding: 16px;
}

.form-tip {
  margin-left: 8px;
  font-size: 12px;
  color: #909399;
}

.task-progress {
  padding: 16px 0;
}

.task-info {
  margin-bottom: 16px;
}

.task-info p {
  margin: 8px 0;
  color: #606266;
}

.task-actions {
  margin-top: 16px;
  text-align: center;
}
</style>

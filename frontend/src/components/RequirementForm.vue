<template>
  <div class="requirement-form">
    <el-form :model="form" :rules="rules" ref="formRef" label-width="120px" :validate-on-rule-change="false">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="需求标题" prop="title">
            <el-input v-model="form.title" placeholder="请输入需求标题" maxlength="255" show-word-limit />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="需求类型" prop="requirement_type">
            <el-select v-model="form.requirement_type" placeholder="选择需求类型">
              <el-option
                v-for="type in requirementTypes"
                :key="type.value"
                :label="type.label"
                :value="type.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="协议类型" prop="protocol_type">
            <el-select v-model="form.protocol_type" placeholder="选择协议类型">
              <el-option
                v-for="protocol in protocolTypes"
                :key="protocol.value"
                :label="protocol.label"
                :value="protocol.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="协议版本">
            <el-input v-model="form.protocol_version" placeholder="如：v1.0, 8.0" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="优先级" prop="priority">
            <el-select v-model="form.priority" placeholder="选择优先级">
              <el-option
                v-for="priority in priorities"
                :key="priority.value"
                :label="priority.label"
                :value="priority.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="复杂度" prop="complexity">
            <el-select v-model="form.complexity" placeholder="选择复杂度">
              <el-option
                v-for="complexity in complexities"
                :key="complexity.value"
                :label="complexity.label"
                :value="complexity.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="可测试性" prop="testability">
            <el-select v-model="form.testability" placeholder="选择可测试性">
              <el-option
                v-for="testability in testabilities"
                :key="testability.value"
                :label="testability.label"
                :value="testability.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="分类">
            <el-select v-model="form.category" placeholder="选择分类" filterable allow-create>
              <el-option
                v-for="category in categories"
                :key="category"
                :label="category"
                :value="category"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="模块">
            <el-select v-model="form.module" placeholder="选择模块" filterable allow-create>
              <el-option
                v-for="module in modules"
                :key="module"
                :label="module"
                :value="module"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="创建者">
            <el-select v-model="form.author" placeholder="选择创建者" filterable allow-create>
              <el-option
                v-for="author in authors"
                :key="author"
                :label="author"
                :value="author"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="需求描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="4"
          placeholder="请详细描述需求内容"
          maxlength="2000"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="功能描述">
        <el-input
          v-model="form.functional_description"
          type="textarea"
          :rows="3"
          placeholder="详细的功能描述"
        />
      </el-form-item>

      <el-tabs v-model="activeTab">
        <el-tab-pane label="测试准则" name="test_criteria">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="验收标准">
                <el-input
                  v-model="acceptanceCriteriaText"
                  type="textarea"
                  :rows="6"
                  placeholder="每行一个验收标准"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="测试场景">
                <el-input
                  v-model="testScenariosText"
                  type="textarea"
                  :rows="6"
                  placeholder="每行一个测试场景"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="边界情况">
                <el-input
                  v-model="edgeCasesText"
                  type="textarea"
                  :rows="4"
                  placeholder="每行一个边界情况"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="负面测试">
                <el-input
                  v-model="negativeCasesText"
                  type="textarea"
                  :rows="4"
                  placeholder="每行一个负面测试用例"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="预期测试用例数">
            <el-input-number v-model="form.expected_test_cases" :min="0" :max="100" />
          </el-form-item>
        </el-tab-pane>

        <el-tab-pane label="业务规则" name="business_rules">
          <el-form-item label="业务规则">
            <el-input
              v-model="businessRulesText"
              type="textarea"
              :rows="6"
              placeholder="每行一个业务规则"
            />
          </el-form-item>
          <el-form-item label="约束条件">
            <el-input
              v-model="constraintsText"
              type="textarea"
              :rows="4"
              placeholder="每行一个约束条件"
            />
          </el-form-item>
          <el-form-item label="假设条件">
            <el-input
              v-model="assumptionsText"
              type="textarea"
              :rows="4"
              placeholder="每行一个假设条件"
            />
          </el-form-item>
        </el-tab-pane>

        <el-tab-pane label="追溯信息" name="traceability">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="源文档" prop="source_document">
                <el-input v-model="form.traceability.source_document" placeholder="源文档名称" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="章节">
                <el-input v-model="form.traceability.source_section" placeholder="如：第3章" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="页码">
                <el-input-number v-model="form.traceability.source_page" :min="1" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="提取方法">
            <el-radio-group v-model="form.traceability.extraction_method">
              <el-radio label="manual">手动提取</el-radio>
              <el-radio label="ai_parsed">AI解析</el-radio>
              <el-radio label="auto_imported">自动导入</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-tab-pane>

        <el-tab-pane label="其他信息" name="other">
          <el-form-item label="标签">
            <el-select
              v-model="form.tags"
              multiple
              filterable
              allow-create
              placeholder="选择或创建标签"
              style="width: 100%"
            >
              <el-option
                v-for="tag in tags"
                :key="tag"
                :label="tag"
                :value="tag"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="利益相关者">
            <el-select
              v-model="form.stakeholders"
              multiple
              filterable
              allow-create
              placeholder="选择或添加利益相关者"
              style="width: 100%"
            />
          </el-form-item>
          <el-form-item label="参考资料">
            <el-input
              v-model="referencesText"
              type="textarea"
              :rows="3"
              placeholder="每行一个参考资料链接或文档"
            />
          </el-form-item>
          <el-form-item label="相关文档">
            <el-input
              v-model="relatedDocumentsText"
              type="textarea"
              :rows="3"
              placeholder="每行一个相关文档"
            />
          </el-form-item>
          <el-form-item label="备注">
            <el-input
              v-model="form.notes"
              type="textarea"
              :rows="3"
              placeholder="其他备注信息"
            />
          </el-form-item>
        </el-tab-pane>
      </el-tabs>
    </el-form>

    <div class="form-actions">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="submitting">
        {{ requirement ? '更新' : '创建' }}
      </el-button>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

export default {
  name: 'RequirementForm',
  props: {
    requirement: {
      type: Object,
      default: null
    },
    requirementTypes: {
      type: Array,
      default: () => []
    },
    protocolTypes: {
      type: Array,
      default: () => []
    },
    priorities: {
      type: Array,
      default: () => []
    },
    complexities: {
      type: Array,
      default: () => []
    },
    testabilities: {
      type: Array,
      default: () => []
    },
    categories: {
      type: Array,
      default: () => []
    },
    modules: {
      type: Array,
      default: () => []
    },
    authors: {
      type: Array,
      default: () => []
    },
    reviewers: {
      type: Array,
      default: () => []
    },
    tags: {
      type: Array,
      default: () => []
    }
  },
  emits: ['submit', 'cancel'],
  setup(props, { emit }) {
    const formRef = ref(null)
    const submitting = ref(false)
    const activeTab = ref('test_criteria')

    // 表单数据
    const form = reactive({
      title: '',
      description: '',
      requirement_type: '',
      protocol_type: '',
      protocol_version: '',
      priority: 'medium',
      complexity: 'medium',
      testability: 'medium',
      category: '',
      module: '',
      author: '',
      functional_description: '',
      technical_specifications: {},
      business_rules: [],
      constraints: [],
      assumptions: [],
      test_criteria: {
        acceptance_criteria: [],
        test_scenarios: [],
        edge_cases: [],
        negative_cases: []
      },
      expected_test_cases: 5,
      traceability: {
        source_document: '',
        source_section: '',
        source_page: null,
        extraction_method: 'manual'
      },
      tags: [],
      stakeholders: [],
      references: [],
      related_documents: [],
      notes: ''
    })

    // 文本字段（用于输入）
    const acceptanceCriteriaText = ref('')
    const testScenariosText = ref('')
    const edgeCasesText = ref('')
    const negativeCasesText = ref('')
    const businessRulesText = ref('')
    const constraintsText = ref('')
    const assumptionsText = ref('')
    const referencesText = ref('')
    const relatedDocumentsText = ref('')

    // 表单验证规则
    const rules = {
      title: [
        { required: true, message: '请输入需求标题', trigger: 'blur' },
        { min: 1, max: 255, message: '标题长度在 1 到 255 个字符', trigger: 'blur' }
      ],
      description: [
        { required: true, message: '请输入需求描述', trigger: 'blur' }
      ],
      requirement_type: [
        { required: true, message: '请选择需求类型', trigger: 'change' }
      ],
      protocol_type: [
        { required: true, message: '请选择协议类型', trigger: 'change' }
      ],
      priority: [
        { required: true, message: '请选择优先级', trigger: 'change' }
      ],
      complexity: [
        { required: true, message: '请选择复杂度', trigger: 'change' }
      ],
      testability: [
        { required: true, message: '请选择可测试性', trigger: 'change' }
      ],
      source_document: [
        { required: true, message: '请输入源文档', trigger: 'blur' }
      ]
    }

    // 监听文本变化，转换为数组
    watch([acceptanceCriteriaText, testScenariosText, edgeCasesText, negativeCasesText], () => {
      form.test_criteria.acceptance_criteria = acceptanceCriteriaText.value
        .split('\n')
        .filter(line => line.trim())
      form.test_criteria.test_scenarios = testScenariosText.value
        .split('\n')
        .filter(line => line.trim())
      form.test_criteria.edge_cases = edgeCasesText.value
        .split('\n')
        .filter(line => line.trim())
      form.test_criteria.negative_cases = negativeCasesText.value
        .split('\n')
        .filter(line => line.trim())
    })

    watch([businessRulesText, constraintsText, assumptionsText], () => {
      form.business_rules = businessRulesText.value
        .split('\n')
        .filter(line => line.trim())
      form.constraints = constraintsText.value
        .split('\n')
        .filter(line => line.trim())
      form.assumptions = assumptionsText.value
        .split('\n')
        .filter(line => line.trim())
    })

    watch([referencesText, relatedDocumentsText], () => {
      form.references = referencesText.value
        .split('\n')
        .filter(line => line.trim())
      form.related_documents = relatedDocumentsText.value
        .split('\n')
        .filter(line => line.trim())
    })

    // 初始化表单数据
    const initializeForm = () => {
      if (props.requirement) {
        Object.keys(form).forEach(key => {
          if (props.requirement[key] !== undefined) {
            form[key] = props.requirement[key]
          }
        })

        // 初始化文本字段
        if (props.requirement.test_criteria) {
          acceptanceCriteriaText.value = (props.requirement.test_criteria.acceptance_criteria || []).join('\n')
          testScenariosText.value = (props.requirement.test_criteria.test_scenarios || []).join('\n')
          edgeCasesText.value = (props.requirement.test_criteria.edge_cases || []).join('\n')
          negativeCasesText.value = (props.requirement.test_criteria.negative_cases || []).join('\n')
        }

        businessRulesText.value = (props.requirement.business_rules || []).join('\n')
        constraintsText.value = (props.requirement.constraints || []).join('\n')
        assumptionsText.value = (props.requirement.assumptions || []).join('\n')
        referencesText.value = (props.requirement.references || []).join('\n')
        relatedDocumentsText.value = (props.requirement.related_documents || []).join('\n')
      }
    }

    // 提交表单
    const handleSubmit = async () => {
      try {
        await formRef.value.validate()
        submitting.value = true

        // 提交数据
        emit('submit', { ...form })
      } catch (error) {
        ElMessage.error('请检查表单填写是否正确')
      } finally {
        submitting.value = false
      }
    }

    // 取消
    const handleCancel = () => {
      emit('cancel')
    }

    // 生命周期
    onMounted(() => {
      initializeForm()
    })

    return {
      formRef,
      submitting,
      activeTab,
      form,
      rules,
      acceptanceCriteriaText,
      testScenariosText,
      edgeCasesText,
      negativeCasesText,
      businessRulesText,
      constraintsText,
      assumptionsText,
      referencesText,
      relatedDocumentsText,
      handleSubmit,
      handleCancel
    }
  }
}
</script>

<style scoped>
.requirement-form {
  max-height: 70vh;
  overflow-y: auto;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.el-tabs {
  margin-top: 20px;
}

.el-form-item {
  margin-bottom: 16px;
}
</style>

<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    title="测试用例统计信息"
    width="80%"
  >
    <div v-loading="loading" class="statistics-content">
      <div v-if="statistics" class="statistics-grid">
        <!-- 总体统计 -->
        <div class="stat-card">
          <div class="stat-header">
            <h3>总体统计</h3>
          </div>
          <div class="stat-content">
            <div class="stat-item">
              <div class="stat-number">{{ statistics.total_count }}</div>
              <div class="stat-label">总用例数</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">{{ statistics.execution_rate }}%</div>
              <div class="stat-label">执行率</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">{{ statistics.pass_rate }}%</div>
              <div class="stat-label">通过率</div>
            </div>
          </div>
        </div>

        <!-- 状态分布 -->
        <div class="stat-card">
          <div class="stat-header">
            <h3>状态分布</h3>
          </div>
          <div class="stat-content">
            <div class="chart-container">
              <div
                v-for="(count, status) in statistics.status_distribution"
                :key="status"
                class="bar-item"
              >
                <div class="bar-label">{{ getStatusText(status) }}</div>
                <div class="bar-container">
                  <div
                    class="bar"
                    :style="{
                      width: getPercentage(count, statistics.total_count) + '%',
                      backgroundColor: getStatusColor(status)
                    }"
                  ></div>
                  <span class="bar-value">{{ count }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 优先级分布 -->
        <div class="stat-card">
          <div class="stat-header">
            <h3>优先级分布</h3>
          </div>
          <div class="stat-content">
            <div class="chart-container">
              <div
                v-for="(count, priority) in statistics.priority_distribution"
                :key="priority"
                class="bar-item"
              >
                <div class="bar-label">{{ getPriorityText(priority) }}</div>
                <div class="bar-container">
                  <div
                    class="bar"
                    :style="{
                      width: getPercentage(count, statistics.total_count) + '%',
                      backgroundColor: getPriorityColor(priority)
                    }"
                  ></div>
                  <span class="bar-value">{{ count }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 自动化程度分布 -->
        <div class="stat-card">
          <div class="stat-header">
            <h3>自动化程度分布</h3>
          </div>
          <div class="stat-content">
            <div class="chart-container">
              <div
                v-for="(count, level) in statistics.automation_distribution"
                :key="level"
                class="bar-item"
              >
                <div class="bar-label">{{ getAutomationText(level) }}</div>
                <div class="bar-container">
                  <div
                    class="bar"
                    :style="{
                      width: getPercentage(count, statistics.total_count) + '%',
                      backgroundColor: getAutomationColor(level)
                    }"
                  ></div>
                  <span class="bar-value">{{ count }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 模块分布 -->
        <div class="stat-card full-width">
          <div class="stat-header">
            <h3>模块分布 (Top 10)</h3>
          </div>
          <div class="stat-content">
            <div class="chart-container">
              <div
                v-for="(count, module) in statistics.module_distribution"
                :key="module"
                class="bar-item"
              >
                <div class="bar-label">{{ module }}</div>
                <div class="bar-container">
                  <div
                    class="bar"
                    :style="{
                      width: getPercentage(count, statistics.total_count) + '%',
                      backgroundColor: '#409eff'
                    }"
                  ></div>
                  <span class="bar-value">{{ count }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="$emit('update:visible', false)">关闭</el-button>
        <el-button type="primary" @click="refreshStatistics">刷新数据</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import testCaseApi from '../services/testCaseApi'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:visible'])

// 响应式数据
const loading = ref(false)
const statistics = ref(null)

// 监听对话框显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    loadStatistics()
  }
})

// 方法
const loadStatistics = async () => {
  loading.value = true
  try {
    statistics.value = await testCaseApi.getStatistics()
  } catch (error) {
    ElMessage.error('加载统计信息失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

const refreshStatistics = () => {
  loadStatistics()
}

// 工具方法
const getPercentage = (value, total) => {
  if (total === 0) return 0
  return Math.round((value / total) * 100)
}

const getStatusText = (status) => {
  const texts = {
    draft: '草稿',
    active: '激活',
    completed: '已完成',
    failed: '失败',
    deprecated: '已废弃',
    archived: '已归档'
  }
  return texts[status] || status
}

const getStatusColor = (status) => {
  const colors = {
    draft: '#909399',
    active: '#67c23a',
    completed: '#409eff',
    failed: '#f56c6c',
    deprecated: '#e6a23c',
    archived: '#f56c6c'
  }
  return colors[status] || '#409eff'
}

const getPriorityText = (priority) => {
  const texts = {
    low: '低',
    medium: '中',
    high: '高',
    critical: '紧急'
  }
  return texts[priority] || priority
}

const getPriorityColor = (priority) => {
  const colors = {
    low: '#67c23a',
    medium: '#e6a23c',
    high: '#f56c6c',
    critical: '#f56c6c'
  }
  return colors[priority] || '#409eff'
}

const getAutomationText = (level) => {
  const texts = {
    manual: '手动',
    semi_auto: '半自动',
    auto: '自动'
  }
  return texts[level] || level
}

const getAutomationColor = (level) => {
  const colors = {
    manual: '#f56c6c',
    semi_auto: '#e6a23c',
    auto: '#67c23a'
  }
  return colors[level] || '#409eff'
}
</script>

<style scoped>
.statistics-content {
  min-height: 400px;
}

.statistics-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.stat-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: white;
  overflow: hidden;
}

.stat-card.full-width {
  grid-column: 1 / -1;
}

.stat-header {
  padding: 16px 20px;
  background: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
}

.stat-header h3 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.stat-content {
  padding: 20px;
}

.stat-item {
  text-align: center;
  margin-bottom: 16px;
}

.stat-item:last-child {
  margin-bottom: 0;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #409eff;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #606266;
  margin-top: 8px;
}

.chart-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.bar-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.bar-label {
  min-width: 80px;
  font-size: 14px;
  color: #606266;
  text-align: right;
}

.bar-container {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8px;
}

.bar {
  height: 20px;
  border-radius: 10px;
  min-width: 2px;
  transition: width 0.3s ease;
}

.bar-value {
  font-size: 12px;
  color: #606266;
  min-width: 30px;
}

.dialog-footer {
  text-align: right;
}

@media (max-width: 768px) {
  .statistics-grid {
    grid-template-columns: 1fr;
  }
}
</style>

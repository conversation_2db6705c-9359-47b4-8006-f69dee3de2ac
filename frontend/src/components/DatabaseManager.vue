<template>
  <div class="database-manager">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>数据库配置管理</span>
          <div class="header-buttons">
            <el-button @click="loadDatabases" :loading="loading">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <el-button type="success" @click="initializePresetConfigs" :loading="initializing">
              <el-icon><Setting /></el-icon>
              初始化预设配置
            </el-button>
            <el-button type="primary" @click="openAddDialog">
              <el-icon><Plus /></el-icon>
              添加数据库
            </el-button>
          </div>
        </div>
      </template>

      <!-- 搜索和过滤 -->
      <div class="filter-section">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-input
              v-model="searchText"
              placeholder="搜索配置名称、主机地址或描述"
              clearable
              @input="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="6">
            <el-select
              v-model="filterType"
              placeholder="筛选数据库类型"
              clearable
              @change="handleFilter"
            >
              <el-option label="MySQL" value="mysql" />
              <el-option label="MongoDB" value="mongodb" />
              <el-option label="PostgreSQL" value="postgresql" />
              <el-option label="GaussDB" value="gaussdb" />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-checkbox v-model="showDefaultOnly" @change="handleFilter">
              仅显示默认配置
            </el-checkbox>
          </el-col>
        </el-row>
      </div>

      <!-- 数据库配置列表 -->
      <el-table :data="filteredDatabases" style="width: 100%" v-loading="loading">
        <el-table-column prop="name" label="配置名称" min-width="180">
          <template #default="scope">
            <div class="config-name-cell">
              <el-tag v-if="scope.row.is_default" type="success" size="small" class="default-tag">默认</el-tag>
              <span class="config-name" :title="scope.row.name">{{ scope.row.name }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="database_type" label="数据库类型" width="120">
          <template #default="scope">
            <el-tag
              :type="getDatabaseTypeColor(scope.row.database_type)"
              size="small"
            >
              {{ getDatabaseTypeName(scope.row.database_type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="database_version" label="版本" width="120">
          <template #default="scope">
            <span v-if="scope.row.database_version">{{ scope.row.database_version }}</span>
            <span v-else class="text-gray-400">未指定</span>
          </template>
        </el-table-column>
        <el-table-column prop="host" label="主机地址" width="150" show-overflow-tooltip />
        <el-table-column prop="port" label="端口" width="80" />
        <el-table-column prop="user" label="用户名" width="100" show-overflow-tooltip />
        <el-table-column prop="database_name" label="数据库" width="120" show-overflow-tooltip />
        <el-table-column prop="description" label="描述" min-width="150" show-overflow-tooltip />
        <el-table-column label="操作" width="340" fixed="right">
          <template #default="scope">
            <div class="action-buttons">
              <el-tooltip content="测试连接" placement="top">
                <el-button
                  size="small"
                  type="primary"
                  @click="testConnection(scope.row)"
                  :loading="scope.row.testing"
                  circle
                >
                  <el-icon><Connection /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="异步抓包任务" placement="top">
                <el-button
                  size="small"
                  type="info"
                  @click="openCaptureDialog(scope.row)"
                  circle
                >
                  <el-icon><Monitor /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="编辑配置" placement="top">
                <el-button
                  size="small"
                  type="warning"
                  @click="editDatabase(scope.row)"
                  circle
                >
                  <el-icon><Edit /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="设为默认" placement="top">
                <el-button
                  size="small"
                  type="success"
                  @click="setDefault(scope.row)"
                  :disabled="scope.row.is_default"
                  circle
                >
                  <el-icon><Star /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="复制配置" placement="top">
                <el-button
                  size="small"
                  type="info"
                  @click="duplicateDatabase(scope.row)"
                  circle
                >
                  <el-icon><CopyDocument /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="删除配置" placement="top">
                <el-button
                  size="small"
                  type="danger"
                  @click="deleteDatabase(scope.row)"
                  :loading="scope.row.deleting"
                  circle
                >
                  <el-icon><Delete /></el-icon>
                </el-button>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="totalCount"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 添加/编辑数据库对话框 -->
    <el-dialog 
      :title="editingDatabase ? '编辑数据库配置' : '添加数据库配置'"
      v-model="showAddDialog"
      width="600px"
    >
      <el-form :model="databaseForm" :rules="rules" ref="databaseFormRef" label-width="120px">
        <el-form-item label="配置名称" prop="name">
          <el-input v-model="databaseForm.name" placeholder="请输入配置名称" />
        </el-form-item>
        <el-form-item label="数据库类型" prop="database_type">
          <el-select v-model="databaseForm.database_type" placeholder="请选择数据库类型" @change="onDatabaseTypeChange">
            <el-option label="MySQL" value="mysql" />
            <el-option label="MongoDB" value="mongodb" />
            <el-option label="PostgreSQL" value="postgresql" />
            <el-option label="Oracle" value="oracle" />
            <el-option label="GaussDB" value="gaussdb" />
          </el-select>
        </el-form-item>
        <el-form-item label="数据库版本">
          <el-select
            v-model="databaseForm.database_version"
            placeholder="选择数据库版本"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="version in availableVersions"
              :key="version"
              :label="version"
              :value="version"
            />
          </el-select>
          <div class="form-tip">选择目标数据库版本，用于版本特性识别</div>
        </el-form-item>
        <el-form-item label="主机地址" prop="host">
          <el-input v-model="databaseForm.host" placeholder="请输入主机地址" />
        </el-form-item>
        <el-form-item label="端口号" prop="port">
          <el-input-number v-model="databaseForm.port" :min="1" :max="65535" />
        </el-form-item>
        <el-form-item label="用户名" prop="user">
          <el-input v-model="databaseForm.user" placeholder="请输入用户名" />
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input v-model="databaseForm.password" type="password" placeholder="请输入密码" show-password />
        </el-form-item>
        <el-form-item label="数据库名" prop="database_name">
          <el-input v-model="databaseForm.database_name" placeholder="请输入数据库名" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="databaseForm.description" type="textarea" placeholder="请输入描述" />
        </el-form-item>
        <el-form-item>
          <el-checkbox v-model="databaseForm.is_default">设为默认配置</el-checkbox>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelEdit">取消</el-button>
          <el-button type="primary" @click="testFormConnection" :loading="testingConnection">
            <el-icon><Connection /></el-icon>
            测试连接
          </el-button>
          <el-button type="primary" @click="saveDatabase" :loading="saving">
            {{ editingDatabase ? '更新' : '保存' }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 连接测试结果对话框 -->
    <el-dialog title="连接测试结果" v-model="showTestResult" width="800px">
      <div v-if="testResult">
        <el-alert 
          :title="testResult.success ? '连接成功' : '连接失败'"
          :type="testResult.success ? 'success' : 'error'"
          :description="testResult.message"
          show-icon
        />
        
        <div v-if="testResult.success" style="margin-top: 20px;">
          <el-descriptions title="连接信息" :column="2" border>
            <el-descriptions-item label="响应时间">{{ testResult.response_time }}ms</el-descriptions-item>
            <el-descriptions-item label="数据库数量">{{ testResult.databases_count }}</el-descriptions-item>
            <el-descriptions-item label="表数量">{{ testResult.tables_count }}</el-descriptions-item>
          </el-descriptions>

          <!-- 数据库列表 -->
          <el-card style="margin-top: 20px;" v-if="testResult.databases && testResult.databases.length > 0">
            <template #header>
              <span>数据库列表 ({{ testResult.databases.length }})</span>
            </template>
            <el-tag v-for="db in testResult.databases" :key="db" style="margin: 2px;">
              {{ db }}
            </el-tag>
          </el-card>

          <!-- 表信息 -->
          <el-card style="margin-top: 20px;" v-if="testResult.tables && testResult.tables.length > 0">
            <template #header>
              <span>表信息 ({{ testResult.tables.length }})</span>
            </template>
            <el-table :data="testResult.tables" style="width: 100%" max-height="300">
              <el-table-column prop="database_name" label="数据库" width="120" />
              <el-table-column prop="table_name" label="表名" width="150" />
              <el-table-column prop="table_type" label="类型" width="100" />
              <el-table-column prop="table_rows" label="行数" width="100" />
              <el-table-column prop="table_comment" label="注释" />
            </el-table>
          </el-card>
        </div>
        
        <div v-else style="margin-top: 20px;">
          <el-alert 
            title="错误详情"
            :description="testResult.error"
            type="error"
            show-icon
          />
        </div>
      </div>
    </el-dialog>

    <!-- 异步抓包任务对话框 -->
    <el-dialog title="创建异步抓包任务" v-model="showCaptureDialog" width="600px">
      <el-form :model="captureForm" :rules="captureRules" ref="captureFormRef" label-width="120px">
        <el-form-item label="数据库配置">
          <el-input :value="selectedDatabase?.name" disabled />
        </el-form-item>
        <el-form-item label="数据库类型">
          <el-tag :type="getDatabaseTypeColor(selectedDatabase?.database_type)">
            {{ getDatabaseTypeName(selectedDatabase?.database_type) }}
          </el-tag>
        </el-form-item>
        <el-form-item
          :label="selectedDatabase?.database_type === 'mongodb' ? 'MongoDB查询' : 'SQL查询'"
          prop="query"
        >
          <el-input
            v-model="captureForm.query"
            type="textarea"
            :rows="4"
            :placeholder="getQueryPlaceholder(selectedDatabase?.database_type)"
          />
        </el-form-item>
        <el-form-item label="抓包时长" prop="captureDuration">
          <el-input-number
            v-model="captureForm.captureDuration"
            :min="10"
            :max="300"
            :step="10"
            style="width: 200px"
          />
          <span style="margin-left: 10px; color: #909399;">秒 (10-300秒)</span>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelCapture">取消</el-button>
          <el-button type="primary" @click="submitCaptureTask" :loading="submittingTask">
            <el-icon><Monitor /></el-icon>
            创建任务
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Edit, Delete, Connection, Star, Refresh, Setting, CopyDocument, Search, Monitor } from '@element-plus/icons-vue'
import apiService from '@/services/api'
import testCaseApi from '@/services/testCaseApi'

// 响应式数据
const databases = ref([])
const loading = ref(false)
const showAddDialog = ref(false)
const showTestResult = ref(false)
const testResult = ref(null)
const editingDatabase = ref(null)
const saving = ref(false)
const testingConnection = ref(false)
const initializing = ref(false)
const searchText = ref('')
const filterType = ref('')
const showDefaultOnly = ref(false)

// 分页相关数据
const currentPage = ref(1)
const pageSize = ref(10)
const totalCount = ref(0)

// 异步抓包相关数据
const showCaptureDialog = ref(false)
const selectedDatabase = ref(null)
const submittingTask = ref(false)

// 表单数据
const databaseForm = reactive({
  name: '',
  database_type: 'mysql',
  database_version: '',
  host: '',
  port: 3306,
  user: '',
  password: '',
  database_name: '',
  description: '',
  is_default: false
})

// 数据库版本数据
const databaseVersions = ref({})
const availableVersions = ref([])

// 抓包表单数据
const captureForm = reactive({
  query: '',
  captureDuration: 30
})

// 表单引用
const databaseFormRef = ref()
const captureFormRef = ref()

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入配置名称', trigger: 'blur' }
  ],
  database_type: [
    { required: true, message: '请选择数据库类型', trigger: 'change' }
  ],
  host: [
    { required: true, message: '请输入主机地址', trigger: 'blur' }
  ],
  port: [
    { required: true, message: '请输入端口号', trigger: 'blur' }
  ],
  user: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' }
  ],
  database_name: [
    { required: true, message: '请输入数据库名', trigger: 'blur' }
  ]
}

// 抓包表单验证规则
const captureRules = {
  query: [
    { required: true, message: '请输入查询语句', trigger: 'blur' }
  ],
  captureDuration: [
    { required: true, message: '请输入抓包时长', trigger: 'blur' },
    { type: 'number', min: 10, max: 300, message: '抓包时长必须在10-300秒之间', trigger: 'blur' }
  ]
}

// 方法
const loadDatabases = async () => {
  loading.value = true
  try {
    const response = await apiService.getDatabaseConfigs(
      currentPage.value,
      pageSize.value,
      filterType.value || null
    )
    databases.value = response.databases
    totalCount.value = response.total
  } catch (error) {
    ElMessage.error('加载数据库配置失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

const testConnection = async (database) => {
  try {
    const response = await apiService.testDatabaseConnection(database.id)
    testResult.value = response
    showTestResult.value = true
  } catch (error) {
    ElMessage.error('测试连接失败: ' + error.message)
  }
}

const testFormConnection = async () => {
  if (!databaseFormRef.value) return
  
  const valid = await databaseFormRef.value.validate().catch(() => false)
  if (!valid) return
  
  testingConnection.value = true
  try {
    if (editingDatabase.value) {
      // 如果是编辑模式，直接测试现有配置
      const response = await apiService.testDatabaseConnection(editingDatabase.value.id)
      testResult.value = response
      showTestResult.value = true
    } else {
      // 如果是新增模式，使用专门的测试API（不保存配置）
      const testConfig = {
        name: databaseForm.name,
        host: databaseForm.host,
        port: databaseForm.port,
        user: databaseForm.user,
        password: databaseForm.password,
        database_name: databaseForm.database_name,
        description: databaseForm.description,
        is_default: databaseForm.is_default
      }

      const response = await apiService.testDatabaseConfig(testConfig)
      testResult.value = response
      showTestResult.value = true
    }
  } catch (error) {
    ElMessage.error('测试连接失败: ' + error.message)
  } finally {
    testingConnection.value = false
  }
}

const saveDatabase = async () => {
  if (!databaseFormRef.value) return
  
  const valid = await databaseFormRef.value.validate().catch(() => false)
  if (!valid) return
  
  saving.value = true
  try {
    if (editingDatabase.value) {
      // 更新
      await apiService.updateDatabaseConfig(editingDatabase.value.id, databaseForm)
      ElMessage.success('数据库配置更新成功')
    } else {
      // 添加
      await apiService.addDatabaseConfig(databaseForm)
      ElMessage.success('数据库配置添加成功')
    }
    
    showAddDialog.value = false
    await loadDatabases()
  } catch (error) {
    ElMessage.error('保存失败: ' + error.message)
  } finally {
    saving.value = false
  }
}

// 重置表单方法
const resetForm = () => {
  Object.assign(databaseForm, {
    name: '',
    database_type: 'mysql',
    database_version: '',
    host: '',
    port: 3306,
    user: '',
    password: '',
    database_name: '',
    description: '',
    is_default: false
  })

  // 清除表单验证状态
  if (databaseFormRef.value) {
    databaseFormRef.value.clearValidate()
  }
}

const openAddDialog = async () => {
  editingDatabase.value = null
  resetForm()

  // 确保版本数据已加载
  if (Object.keys(databaseVersions.value).length === 0) {
    await loadDatabaseVersions()
  }

  // 设置默认版本选项
  availableVersions.value = databaseVersions.value['mysql'] || []
  if (availableVersions.value.length > 0) {
    databaseForm.database_version = availableVersions.value[0]
  }

  showAddDialog.value = true
}

const editDatabase = async (database) => {
  editingDatabase.value = database
  Object.assign(databaseForm, database)

  // 确保版本数据已加载
  if (Object.keys(databaseVersions.value).length === 0) {
    await loadDatabaseVersions()
  }

  // 更新版本选项
  availableVersions.value = databaseVersions.value[database.database_type] || []

  showAddDialog.value = true
}

const deleteDatabase = async (database) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除数据库配置 "${database.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await apiService.deleteDatabaseConfig(database.id)
    ElMessage.success('删除成功')
    await loadDatabases()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败: ' + error.message)
    }
  }
}

const setDefault = async (database) => {
  try {
    await apiService.setDefaultDatabase(database.id)
    ElMessage.success('默认配置设置成功')
    await loadDatabases()
  } catch (error) {
    ElMessage.error('设置默认配置失败: ' + error.message)
  }
}

const duplicateDatabase = async (database) => {
  editingDatabase.value = null
  Object.assign(databaseForm, {
    ...database,
    name: database.name + '_副本',
    is_default: false
  })

  // 确保版本数据已加载
  if (Object.keys(databaseVersions.value).length === 0) {
    await loadDatabaseVersions()
  }

  // 更新版本选项
  availableVersions.value = databaseVersions.value[database.database_type] || []

  showAddDialog.value = true
}

// 异步抓包相关方法
const openCaptureDialog = (database) => {
  selectedDatabase.value = database
  captureForm.query = getDefaultQuery(database.database_type)
  captureForm.captureDuration = 30
  showCaptureDialog.value = true
}

const cancelCapture = () => {
  showCaptureDialog.value = false
  selectedDatabase.value = null
  captureForm.query = ''
  captureForm.captureDuration = 30
}

const getDefaultQuery = (databaseType) => {
  switch (databaseType) {
    case 'mysql':
      return 'SELECT * FROM information_schema.tables LIMIT 10;'
    case 'postgresql':
      return 'SELECT * FROM information_schema.tables LIMIT 10;'
    case 'mongodb':
      return 'db.collection.find().limit(10)'
    default:
      return ''
  }
}

const getQueryPlaceholder = (databaseType) => {
  switch (databaseType) {
    case 'mysql':
      return '请输入SQL查询语句，例如：SELECT * FROM users WHERE id = 1;'
    case 'postgresql':
      return '请输入SQL查询语句，例如：SELECT * FROM users WHERE id = 1;'
    case 'mongodb':
      return '请输入MongoDB查询语句，例如：db.users.find({id: 1})'
    default:
      return '请输入查询语句'
  }
}

const submitCaptureTask = async () => {
  try {
    await captureFormRef.value.validate()

    submittingTask.value = true

    let response
    const { database_type } = selectedDatabase.value

    switch (database_type) {
      case 'mysql':
        response = await apiService.createMySQLTask(
          selectedDatabase.value.id,
          captureForm.query,
          captureForm.captureDuration
        )
        break
      case 'postgresql':
        response = await apiService.createPostgreSQLTask(
          selectedDatabase.value.id,
          captureForm.query,
          captureForm.captureDuration
        )
        break
      case 'mongodb':
        response = await apiService.createMongoDBTask(
          selectedDatabase.value.id,
          captureForm.query,
          captureForm.captureDuration
        )
        break
      default:
        throw new Error('不支持的数据库类型')
    }

    ElMessage.success(`异步抓包任务已创建，任务ID: ${response.task_id}`)
    cancelCapture()

  } catch (error) {
    ElMessage.error('创建任务失败: ' + error.message)
  } finally {
    submittingTask.value = false
  }
}

// 计算属性
const filteredDatabases = computed(() => {
  let filtered = databases.value

  // 搜索过滤（前端处理）
  if (searchText.value) {
    const search = searchText.value.toLowerCase()
    filtered = filtered.filter(db =>
      db.name.toLowerCase().includes(search) ||
      db.host.toLowerCase().includes(search) ||
      (db.description && db.description.toLowerCase().includes(search))
    )
  }

  // 默认配置过滤（前端处理）
  if (showDefaultOnly.value) {
    filtered = filtered.filter(db => db.is_default)
  }

  return filtered
})

// 搜索和过滤处理函数
const handleSearch = () => {
  // 搜索是响应式的，不需要额外处理
}

const handleFilter = () => {
  // 类型过滤需要重新加载数据
  currentPage.value = 1
  loadDatabases()
}

// 分页事件处理
const handleSizeChange = (newSize) => {
  pageSize.value = newSize
  currentPage.value = 1
  loadDatabases()
}

const handleCurrentChange = (newPage) => {
  currentPage.value = newPage
  loadDatabases()
}

// 数据库类型相关方法
const getDatabaseTypeName = (type) => {
  const typeMap = {
    'mysql': 'MySQL',
    'mongodb': 'MongoDB',
    'postgresql': 'PostgreSQL',
    'oracle': 'Oracle',
    'gaussdb': 'GaussDB'
  }
  return typeMap[type] || type
}

const getDatabaseTypeColor = (type) => {
  const colorMap = {
    'mysql': 'primary',
    'mongodb': 'success',
    'postgresql': 'warning',
    'oracle': 'danger',
    'gaussdb': 'info'
  }
  return colorMap[type] || 'info'
}

const onDatabaseTypeChange = (type) => {
  // 根据数据库类型设置默认端口
  const defaultPorts = {
    'mysql': 3306,
    'mongodb': 27017,
    'postgresql': 5432,
    'oracle': 1521,
    'gaussdb': 5432
  }
  databaseForm.port = defaultPorts[type] || 3306

  // 根据数据库类型设置默认数据库名
  const defaultDatabases = {
    'mysql': 'mysql',
    'mongodb': 'test',
    'postgresql': 'postgres',
    'oracle': 'helowin',
    'gaussdb': 'postgres'
  }
  databaseForm.database_name = defaultDatabases[type] || ''

  // 更新可用版本列表
  availableVersions.value = databaseVersions.value[type] || []

  // 设置默认版本
  if (availableVersions.value.length > 0) {
    databaseForm.database_version = availableVersions.value[0]
  } else {
    databaseForm.database_version = ''
  }
}

// 初始化预设配置
const initializePresetConfigs = async () => {
  try {
    await ElMessageBox.confirm(
      '这将添加MySQL、MongoDB、PostgreSQL和GaussDB的预设配置，如果配置已存在将跳过。是否继续？',
      '初始化预设配置',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )

    initializing.value = true

    // 预设配置列表
    const presetConfigs = [
      {
        name: 'MySQL默认配置',
        database_type: 'mysql',
        host: '**************',
        port: 3306,
        user: 'root',
        password: '123456',
        database_name: 'mysql',
        description: 'MySQL数据库默认配置，用于关系型数据存储',
        is_default: false
      },
      {
        name: 'MongoDB默认配置',
        database_type: 'mongodb',
        host: '**************',
        port: 27017,
        user: 'admin',
        password: 'admin',
        database_name: 'ai-mongo-pcap',
        description: 'MongoDB数据库默认配置，用于文档存储和查询',
        is_default: false
      },
      {
        name: 'PostgreSQL默认配置',
        database_type: 'postgresql',
        host: '**************',
        port: 5432,
        user: 'postgres',
        password: 'postgres',
        database_name: 'postgres',
        description: 'PostgreSQL数据库默认配置，用于关系型数据存储',
        is_default: false
      },
      {
        name: 'GaussDB默认配置',
        database_type: 'gaussdb',
        host: '**************',
        port: 5432,
        user: 'gaussdb',
        password: 'Gauss@123',
        database_name: 'postgres',
        description: 'GaussDB数据库默认配置，华为企业级数据库',
        is_default: false
      },
      {
        name: 'Oracle默认配置',
        database_type: 'oracle',
        host: '*************',
        port: 1521,
        user: 'system',
        password: 'helowin',
        database_name: 'helowin',
        description: 'Oracle数据库默认配置，用于企业级数据存储',
        is_default: false
      }
    ]

    let addedCount = 0
    let skippedCount = 0

    for (const config of presetConfigs) {
      try {
        // 检查是否已存在相同名称的配置
        const existingConfig = databases.value.find(db =>
          db.name === config.name ||
          (db.database_type === config.database_type && db.host === config.host && db.port === config.port)
        )

        if (existingConfig) {
          skippedCount++
          continue
        }

        // 添加配置
        await apiService.addDatabaseConfig(config)
        addedCount++
      } catch (error) {
        console.error(`Failed to add ${config.name}:`, error)
      }
    }

    // 刷新列表
    await loadDatabases()

    // 显示结果
    if (addedCount > 0) {
      ElMessage.success(`成功添加 ${addedCount} 个预设配置${skippedCount > 0 ? `，跳过 ${skippedCount} 个已存在的配置` : ''}`)
    } else if (skippedCount > 0) {
      ElMessage.info('所有预设配置都已存在，无需添加')
    } else {
      ElMessage.warning('未添加任何配置')
    }

  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('初始化预设配置失败: ' + error.message)
    }
  } finally {
    initializing.value = false
  }
}

const cancelEdit = () => {
  showAddDialog.value = false
  editingDatabase.value = null
  resetForm()

  // 重置版本选项为 mysql 默认值
  availableVersions.value = databaseVersions.value['mysql'] || []
  if (availableVersions.value.length > 0) {
    databaseForm.database_version = availableVersions.value[0]
  }
}

// 生命周期
onMounted(() => {
  loadDatabases()
  loadDatabaseVersions()
})

// 获取数据库版本选项
const loadDatabaseVersions = async () => {
  try {
    const response = await testCaseApi.getDatabaseVersions()
    if (response.success) {
      databaseVersions.value = response.data
      // 初始化可用版本列表
      availableVersions.value = databaseVersions.value[databaseForm.database_type] || []
    }
  } catch (error) {
    console.error('获取数据库版本失败:', error)
  }
}
</script>

<style scoped>
.database-manager {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-buttons {
  display: flex;
  gap: 10px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.action-buttons .el-button {
  margin: 0;
}

.config-name-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.default-tag {
  flex-shrink: 0;
}

.config-name {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
}

.filter-section {
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 8px;
  margin-bottom: 20px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 20px 0;
}
</style>

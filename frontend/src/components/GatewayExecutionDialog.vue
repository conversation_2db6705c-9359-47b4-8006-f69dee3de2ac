<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    title="网关执行"
    width="700px"
    :close-on-click-modal="false"
  >
    <div class="gateway-execution-dialog">
      <!-- 测试用例信息 -->
      <div class="section">
        <h4>测试用例信息</h4>
        <div class="info-grid">
          <div class="info-item">
            <label>用例标题：</label>
            <span>{{ testCase?.title || '未知' }}</span>
          </div>
          <div class="info-item">
            <label>数据库类型：</label>
            <span>{{ testCase?.database_type || '未知' }}</span>
          </div>
          <div class="info-item">
            <label>最新执行时间：</label>
            <span>{{ formatDate(testCase?.last_execution_time) || '未执行' }}</span>
          </div>
        </div>
      </div>

      <!-- 网关服务器选择 -->
      <div class="section">
        <h4>网关服务器选择</h4>
        <el-select 
          v-model="form.gateway_server_id" 
          placeholder="请选择网关服务器"
          style="width: 100%"
          @change="onGatewayServerChange"
        >
          <el-option
            v-for="server in gatewayServers"
            :key="server.id"
            :label="`${server.name} (${server.host}:${server.port})`"
            :value="server.id"
            :disabled="!server.kafka_enabled"
          >
            <span style="float: left">{{ server.name }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px">
              {{ server.host }}:{{ server.port }}
              <el-tag v-if="server.kafka_enabled" type="success" size="small">Kafka已启用</el-tag>
              <el-tag v-else type="danger" size="small">Kafka未启用</el-tag>
            </span>
          </el-option>
        </el-select>

        <!-- 选中的网关服务器详细信息 -->
        <div v-if="selectedGatewayServer" class="server-details">
          <h5>服务器配置信息：</h5>
          <div class="details-grid">
            <div class="detail-item">
              <label>Kafka地址：</label>
              <span>{{ selectedGatewayServer.kafka_host }}:{{ selectedGatewayServer.kafka_port }}</span>
            </div>
            <div class="detail-item">
              <label>Kafka主题：</label>
              <span>{{ selectedGatewayServer.kafka_topic }}</span>
            </div>
            <div class="detail-item">
              <label>上传目录：</label>
              <span>{{ selectedGatewayServer.upload_path || '/tmp/pcap_files' }}</span>
            </div>
          </div>
          
          <!-- Kafka连接测试 -->
          <div class="connection-test">
            <el-button 
              type="info" 
              size="small" 
              @click="testKafkaConnection"
              :loading="testingConnection"
            >
              <el-icon><Connection /></el-icon>
              测试Kafka连接
            </el-button>
            <span v-if="connectionTestResult" :class="connectionTestResult.success ? 'success-text' : 'error-text'">
              {{ connectionTestResult.message }}
            </span>
          </div>
        </div>
      </div>

      <!-- 执行参数配置 -->
      <div class="section">
        <h4>执行参数</h4>
        <el-form :model="form" label-width="120px">
          <el-form-item label="Kafka等待时间">
            <el-input-number 
              v-model="form.wait_time" 
              :min="1" 
              :max="10" 
              controls-position="right"
              style="width: 150px"
            />
            <span style="margin-left: 10px; color: #909399;">秒 (消费Kafka数据的等待时间)</span>
          </el-form-item>
          <el-form-item label="执行方式">
            <el-radio-group v-model="form.async_execution">
              <el-radio :label="false">同步执行</el-radio>
              <el-radio :label="true">异步执行</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>

      <!-- PCAP文件预览 -->
      <div class="section" v-if="props.testCase?.last_execution_capture_files">
        <h4>将要上传的PCAP文件</h4>
        <div class="pcap-files">
          <template v-if="Array.isArray(props.testCase.last_execution_capture_files)">
            <div 
              v-for="(file, index) in props.testCase.last_execution_capture_files" 
              :key="index"
              class="pcap-file-item"
              v-show="file"
            >
              <div class="file-info">
                <el-icon><Document /></el-icon>
                <span class="file-name">步骤{{ index + 1 }}: {{ getFileName(file) }}</span>
                <span class="file-size">{{ getFileSize(file) }}</span>
              </div>
            </div>
          </template>
          <template v-else-if="typeof props.testCase.last_execution_capture_files === 'object'">
            <div 
              v-for="(file, stepIndex) in props.testCase.last_execution_capture_files" 
              :key="stepIndex"
              class="pcap-file-item"
              v-show="file"
            >
              <div class="file-info">
                <el-icon><Document /></el-icon>
                <span class="file-name">步骤{{ stepIndex }}: {{ getFileName(file) }}</span>
                <span class="file-size">{{ getFileSize(file) }}</span>
              </div>
            </div>
          </template>
          <div v-if="!hasPcapFiles" class="no-files">
            <el-icon><WarningFilled /></el-icon>
            <span>没有找到PCAP文件，无法进行网关执行</span>
          </div>
        </div>
      </div>

      <!-- 执行状态显示 -->
      <div class="section" v-if="executionStatus">
        <h4>执行状态</h4>
        <div class="execution-status">
          <div class="status-item">
            <label>执行ID：</label>
            <span>{{ executionStatus.execution_id }}</span>
          </div>
          <div class="status-item">
            <label>当前状态：</label>
            <el-tag :type="getStatusType(executionStatus.status)">
              {{ getStatusText(executionStatus.status) }}
            </el-tag>
          </div>
          <div class="status-item">
            <label>执行进度：</label>
            <el-progress 
              :percentage="Math.round(executionStatus.progress * 100)"
              :status="executionStatus.status === 'failed' ? 'exception' : 'normal'"
            />
          </div>
          <div class="status-item" v-if="executionStatus.message">
            <label>状态消息：</label>
            <span>{{ executionStatus.message }}</span>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="$emit('update:visible', false)">取消</el-button>
        <el-button 
          type="primary" 
          @click="startExecution"
          :loading="executing"
          :disabled="!canExecute"
        >
          {{ executing ? '执行中...' : '开始执行' }}
        </el-button>
        <el-button 
          v-if="executionStatus && form.async_execution"
          type="info"
          @click="checkExecutionStatus"
        >
          刷新状态
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Connection, Document, WarningFilled } from '@element-plus/icons-vue'
import gatewayServerApi from '../services/gatewayServerApi'
import gatewayExecutionApi from '../services/gatewayExecutionApi'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  testCase: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:visible', 'execution-completed'])

// 响应式数据
const gatewayServers = ref([])
const executing = ref(false)
const testingConnection = ref(false)
const connectionTestResult = ref(null)
const executionStatus = ref(null)

const form = reactive({
  gateway_server_id: null,
  wait_time: 2,
  async_execution: false
})

// 计算属性
const selectedGatewayServer = computed(() => {
  return gatewayServers.value.find(server => server.id === form.gateway_server_id)
})

const canExecute = computed(() => {
  return form.gateway_server_id && hasPcapFiles.value && !executing.value
})

const hasPcapFiles = computed(() => {
  if (!props.testCase?.last_execution_capture_files) return false
  const captureFiles = props.testCase.last_execution_capture_files
  
  if (Array.isArray(captureFiles)) {
    return captureFiles.some(file => file && file.trim() !== '')
  } else if (typeof captureFiles === 'object') {
    return Object.values(captureFiles).some(file => file && file.trim() !== '')
  }
  
  return false
})

// 监听对话框显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    loadGatewayServers()
    resetForm()
  }
})

// 方法
const loadGatewayServers = async () => {
  try {
    const res = await gatewayServerApi.getGatewayServers({ page: 1, page_size: 100 })
    console.log('加载网关服务器响应:', res) // 调试日志
    // 后端返回形如 { gateways: [...], total: N }
    const items = res.gateways || res.items || res.data?.items || res.data?.gateways || []
    console.log('解析得到的服务器列表:', items) // 调试日志
    const kafkaEnabledServers = items.filter(server => server.kafka_enabled)
    console.log('Kafka启用的服务器:', kafkaEnabledServers) // 调试日志
    gatewayServers.value = kafkaEnabledServers
  } catch (error) {
    console.error('加载网关服务器失败:', error)
    ElMessage.error('加载网关服务器失败: ' + (error.message || '未知错误'))
  }
}

const resetForm = () => {
  form.gateway_server_id = null
  form.wait_time = 2
  form.async_execution = false
  connectionTestResult.value = null
  executionStatus.value = null
}

const onGatewayServerChange = () => {
  connectionTestResult.value = null
}

const testKafkaConnection = async () => {
  if (!form.gateway_server_id) {
    ElMessage.warning('请先选择网关服务器')
    return
  }

  testingConnection.value = true
  connectionTestResult.value = null

  try {
    const result = await gatewayExecutionApi.testKafkaConnection(form.gateway_server_id)
    connectionTestResult.value = result
    
    if (result.success) {
      ElMessage.success('Kafka连接测试成功')
    } else {
      ElMessage.error('Kafka连接测试失败: ' + result.error)
    }
  } catch (error) {
    console.error('Kafka连接测试失败:', error)
    connectionTestResult.value = {
      success: false,
      message: 'Kafka连接测试失败: ' + error.message
    }
    ElMessage.error('Kafka连接测试失败')
  } finally {
    testingConnection.value = false
  }
}

const startExecution = async () => {
  if (!form.gateway_server_id) {
    ElMessage.warning('请选择网关服务器')
    return
  }

  executing.value = true
  
  try {
    const executionRequest = {
      test_case_id: props.testCase.id,
      gateway_server_id: form.gateway_server_id,
      wait_time: form.wait_time,
      async_execution: form.async_execution
    }

    const result = await gatewayExecutionApi.executeGatewayTest(executionRequest)
    
    if (result.success) {
      if (form.async_execution) {
        ElMessage.success('网关执行已启动，正在后台运行')
        executionStatus.value = {
          execution_id: result.execution_id,
          status: 'running',
          progress: 0.1,
          message: '正在执行网关测试...'
        }
        // 开始轮询状态
        pollExecutionStatus(result.execution_id)
      } else {
        ElMessage.success('网关执行完成')
        showExecutionResult(result)
        emit('execution-completed', result)
        emit('update:visible', false)
      }
    } else {
      ElMessage.error('网关执行失败: ' + result.error)
    }
  } catch (error) {
    console.error('网关执行失败:', error)
    ElMessage.error('网关执行失败: ' + error.message)
  } finally {
    if (!form.async_execution) {
      executing.value = false
    }
  }
}

const checkExecutionStatus = async () => {
  if (!executionStatus.value?.execution_id) return
  
  try {
    const status = await gatewayExecutionApi.getExecutionStatus(executionStatus.value.execution_id)
    executionStatus.value = status
    
    if (status.status === 'completed' || status.status === 'failed') {
      executing.value = false
      if (status.status === 'completed') {
        ElMessage.success('网关执行完成')
        showExecutionResult(status.result)
        emit('execution-completed', status.result)
      } else {
        ElMessage.error('网关执行失败: ' + status.message)
      }
    }
  } catch (error) {
    console.error('获取执行状态失败:', error)
  }
}

const pollExecutionStatus = async (executionId) => {
  const interval = setInterval(async () => {
    try {
      const status = await gatewayExecutionApi.getExecutionStatus(executionId)
      executionStatus.value = status
      
      if (status.status === 'completed' || status.status === 'failed') {
        clearInterval(interval)
        executing.value = false
        
        if (status.status === 'completed') {
          ElMessage.success('网关执行完成')
          showExecutionResult(status.result)
          emit('execution-completed', status.result)
        } else {
          ElMessage.error('网关执行失败: ' + status.message)
        }
      }
    } catch (error) {
      console.error('轮询执行状态失败:', error)
      clearInterval(interval)
      executing.value = false
    }
  }, 2000) // 每2秒检查一次
}

const showExecutionResult = (result) => {
  // 这里可以显示详细的执行结果
  console.log('网关执行结果:', result)
}

// 辅助方法
const formatDate = (dateString) => {
  if (!dateString) return '未知'
  return new Date(dateString).toLocaleString()
}

const getFileName = (filePath) => {
  if (!filePath) return '未知文件'
  return filePath.split('/').pop()
}

const getFileSize = (filePath) => {
  // 这里可以实现获取文件大小的逻辑
  return '未知大小'
}

const getStatusType = (status) => {
  const statusMap = {
    'pending': 'info',
    'running': 'warning',
    'pcap_uploading': 'warning',
    'kafka_consuming': 'warning', 
    'sql_comparing': 'warning',
    'completed': 'success',
    'failed': 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    'pending': '等待中',
    'running': '运行中',
    'pcap_uploading': '上传PCAP文件',
    'kafka_consuming': '消费Kafka数据',
    'sql_comparing': 'SQL对比中',
    'completed': '已完成',
    'failed': '失败'
  }
  return statusMap[status] || '未知状态'
}
</script>

<style scoped>
.gateway-execution-dialog {
  max-height: 600px;
  overflow-y: auto;
}

.section {
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 4px;
}

.section h4 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 14px;
}

.section h5 {
  margin: 10px 0 5px 0;
  color: #606266;
  font-size: 13px;
}

.info-grid, .details-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}

.info-item, .detail-item {
  display: flex;
  align-items: center;
}

.info-item label, .detail-item label {
  font-weight: bold;
  color: #606266;
  margin-right: 8px;
  min-width: 80px;
}

.server-details {
  margin-top: 15px;
  padding: 10px;
  background: #fff;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.connection-test {
  margin-top: 10px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.success-text {
  color: #67c23a;
}

.error-text {
  color: #f56c6c;
}

.pcap-files {
  max-height: 150px;
  overflow-y: auto;
}

.pcap-file-item {
  display: flex;
  align-items: center;
  padding: 8px;
  margin-bottom: 5px;
  background: #fff;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.file-name {
  flex: 1;
  font-size: 13px;
}

.file-size {
  color: #909399;
  font-size: 12px;
}

.no-files {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 20px;
  color: #e6a23c;
}

.execution-status {
  padding: 10px;
  background: #fff;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.status-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.status-item:last-child {
  margin-bottom: 0;
}

.status-item label {
  font-weight: bold;
  color: #606266;
  margin-right: 8px;
  min-width: 80px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>

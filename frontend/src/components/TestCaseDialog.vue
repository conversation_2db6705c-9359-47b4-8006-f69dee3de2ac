<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    :title="isEdit ? '编辑测试用例' : '新建测试用例'"
    width="80%"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      v-loading="loading"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="用例标题" prop="title">
            <el-input v-model="form.title" placeholder="请输入测试用例标题" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所属模块" prop="module">
            <el-select
              v-model="form.module"
              placeholder="请选择或输入模块"
              filterable
              allow-create
              style="width: 100%"
            >
              <el-option
                v-for="module in modules"
                :key="module"
                :label="module"
                :value="module"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="优先级" prop="priority">
            <el-select v-model="form.priority" placeholder="请选择优先级">
              <el-option label="低" value="low" />
              <el-option label="中" value="medium" />
              <el-option label="高" value="high" />
              <el-option label="紧急" value="critical" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="状态" prop="status">
            <el-select v-model="form.status" placeholder="请选择状态">
              <el-option label="草稿" value="draft" />
              <el-option label="激活" value="active" />
              <el-option label="已完成" value="completed" />
              <el-option label="失败" value="failed" />
              <el-option label="已废弃" value="deprecated" />
              <el-option label="已归档" value="archived" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="自动化程度">
            <el-select v-model="form.automation_level" placeholder="请选择">
              <el-option label="手动" value="manual" />
              <el-option label="半自动" value="semi_auto" />
              <el-option label="自动" value="auto" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="预估时间(分钟)">
            <el-input-number
              v-model="form.estimated_time"
              :min="0"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="创建者">
            <el-select
              v-model="form.author"
              placeholder="请选择或输入创建者"
              filterable
              allow-create
            >
              <el-option
                v-for="author in authors"
                :key="author"
                :label="author"
                :value="author"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="评审者">
            <el-select
              v-model="form.reviewer"
              placeholder="请选择或输入评审者"
              filterable
              allow-create
            >
              <el-option
                v-for="reviewer in reviewers"
                :key="reviewer"
                :label="reviewer"
                :value="reviewer"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="测试环境">
            <el-input v-model="form.test_environment" placeholder="请输入测试环境" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="数据库类型">
            <el-select v-model="form.database_type" placeholder="请选择数据库类型" @change="onDatabaseTypeChange">
              <el-option label="MySQL" value="mysql" />
              <el-option label="PostgreSQL" value="postgresql" />
              <el-option label="MongoDB" value="mongodb" />
              <el-option label="Oracle" value="oracle" />
              <el-option label="GaussDB" value="gaussdb" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="数据库版本">
            <el-select
              v-model="form.database_version"
              placeholder="选择数据库版本"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="version in availableVersions"
                :key="version"
                :label="version"
                :value="version"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="前置条件">
        <el-input
          v-model="form.preconditions"
          type="textarea"
          :rows="3"
          placeholder="请输入前置条件"
        />
      </el-form-item>

      <el-form-item label="测试步骤" prop="test_steps">
        <div class="test-steps">
          <div
            v-for="(step, index) in form.test_steps"
            :key="index"
            class="test-step"
          >
            <div class="step-header">
              <span class="step-number">步骤 {{ index + 1 }}</span>
              <el-button
                size="small"
                type="danger"
                text
                @click="removeStep(index)"
                v-if="form.test_steps.length > 1"
              >
                删除
              </el-button>
            </div>
            <div class="step-content">
              <div class="step-item">
                <strong>操作：</strong>
                <el-input
                  v-model="step.action"
                  placeholder="请输入操作描述"
                  type="textarea"
                  :rows="4"
                  :autosize="{ minRows: 4, maxRows: 10 }"
                  resize="vertical"
                  class="step-textarea"
                />
              </div>
              <div class="step-item">
                <strong>预期结果：</strong>
                <el-input
                  v-model="step.expected_result"
                  placeholder="请输入预期结果"
                  type="textarea"
                  :rows="4"
                  :autosize="{ minRows: 4, maxRows: 10 }"
                  resize="vertical"
                  class="step-textarea"
                />
              </div>
              <div class="step-item">
                <strong>测试数据：</strong>
                <el-input
                  v-model="step.test_data"
                  placeholder="请输入测试数据，如SQL语句、测试参数等"
                  type="textarea"
                  :rows="8"
                  :autosize="{ minRows: 8, maxRows: 20 }"
                  resize="vertical"
                  class="step-textarea sql-textarea"
                />
              </div>
            </div>
          </div>
          <el-button @click="addStep" type="primary" text>
            <el-icon><Plus /></el-icon>
            添加步骤
          </el-button>
        </div>
      </el-form-item>

      <el-form-item label="整体预期结果">
        <el-input
          v-model="form.expected_result"
          type="textarea"
          :rows="3"
          placeholder="请输入整体预期结果"
        />
      </el-form-item>

      <el-form-item label="标签">
        <el-select
          v-model="form.tags"
          multiple
          filterable
          allow-create
          placeholder="请选择或输入标签"
          style="width: 100%"
        >
          <el-option
            v-for="tag in commonTags"
            :key="tag"
            :label="tag"
            :value="tag"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="关联需求">
        <el-input
          v-model="form.related_requirements"
          type="textarea"
          :rows="2"
          placeholder="请输入关联需求"
        />
      </el-form-item>

      <el-form-item label="备注">
        <el-input
          v-model="form.notes"
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import testCaseApi from '../services/testCaseApi'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  testCase: {
    type: Object,
    default: null
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// 响应式数据
const loading = ref(false)
const formRef = ref()
const modules = ref([])
const authors = ref([])
const reviewers = ref([])
const commonTags = ref(['功能测试', '性能测试', '接口测试', '安全测试', '兼容性测试'])

// 数据库版本数据
const databaseVersions = ref({})
const availableVersions = ref([])

// 表单数据
const form = reactive({
  title: '',
  module: '',
  priority: 'medium',
  status: 'draft',
  preconditions: '',
  test_steps: [
    {
      step_number: 1,
      action: '',
      expected_result: '',
      test_data: ''
    }
  ],
  expected_result: '',
  test_data: null,
  tags: [],
  author: '',
  reviewer: '',
  review_status: 'pending',
  review_comments: '',
  estimated_time: 0,
  automation_level: 'manual',
  test_environment: '',
  related_requirements: '',
  related_defects: '',
  notes: '',
  database_type: 'mysql',
  database_version: ''
})

// 表单验证规则
const rules = {
  title: [
    { required: true, message: '请输入测试用例标题', trigger: 'blur' },
    { min: 1, max: 255, message: '标题长度在 1 到 255 个字符', trigger: 'blur' }
  ],
  module: [
    { required: true, message: '请选择所属模块', trigger: 'change' }
  ],
  test_steps: [
    { required: true, message: '请至少添加一个测试步骤', trigger: 'change' }
  ]
}

// 监听对话框显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    loadFormData()
    loadSelectOptions()
  }
})

// 生命周期
onMounted(() => {
  loadSelectOptions()
})

// 方法
const loadFormData = () => {
  if (props.isEdit && props.testCase) {
    // 编辑模式，填充表单数据
    Object.keys(form).forEach(key => {
      if (props.testCase[key] !== undefined) {
        form[key] = props.testCase[key]
      }
    })
    
    // 确保测试步骤格式正确
    if (form.test_steps && form.test_steps.length > 0) {
      form.test_steps = form.test_steps.map((step, index) => ({
        step_number: index + 1,
        action: step.action || '',
        expected_result: step.expected_result || '',
        test_data: step.test_data || ''
      }))
    }
  } else {
    // 新建模式，重置表单
    resetForm()
  }
}

const loadSelectOptions = async () => {
  try {
    const [modulesRes, authorsRes, reviewersRes, versionsRes] = await Promise.all([
      testCaseApi.getModules(),
      testCaseApi.getAuthors(),
      testCaseApi.getReviewers(),
      testCaseApi.getDatabaseVersions()
    ])

    modules.value = modulesRes.modules || []
    authors.value = authorsRes.authors || []
    reviewers.value = reviewersRes.reviewers || []

    if (versionsRes.success) {
      databaseVersions.value = versionsRes.data
      // 初始化可用版本列表
      availableVersions.value = databaseVersions.value[form.database_type] || []

      // 设置默认版本
      if (availableVersions.value.length > 0 && !form.database_version) {
        form.database_version = availableVersions.value[0]
      }
    }
  } catch (error) {
    console.error('加载选项数据失败:', error)
  }
}

// 数据库类型变化处理
const onDatabaseTypeChange = (value) => {
  // 更新可用版本列表
  availableVersions.value = databaseVersions.value[value] || []

  // 设置默认版本
  if (availableVersions.value.length > 0) {
    form.database_version = availableVersions.value[0]
  } else {
    form.database_version = ''
  }
}

const addStep = () => {
  form.test_steps.push({
    step_number: form.test_steps.length + 1,
    action: '',
    expected_result: '',
    test_data: ''
  })
}

const removeStep = (index) => {
  form.test_steps.splice(index, 1)
  // 重新编号
  form.test_steps.forEach((step, idx) => {
    step.step_number = idx + 1
  })
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    loading.value = true
    
    // 验证测试步骤
    const validSteps = form.test_steps.filter(step => step.action.trim() && step.expected_result.trim())
    if (validSteps.length === 0) {
      ElMessage.error('请至少添加一个有效的测试步骤')
      return
    }
    
    const submitData = {
      ...form,
      test_steps: validSteps
    }
    
    if (props.isEdit) {
      await testCaseApi.updateTestCase(props.testCase.id, submitData)
      ElMessage.success('更新成功')
    } else {
      await testCaseApi.createTestCase(submitData)
      ElMessage.success('创建成功')
    }
    
    emit('success')
  } catch (error) {
    if (error.message) {
      ElMessage.error(error.message)
    }
  } finally {
    loading.value = false
  }
}

const handleClose = () => {
  emit('update:visible', false)
  resetForm()
}

const resetForm = () => {
  Object.assign(form, {
    title: '',
    module: '',
    priority: 'medium',
    status: 'draft',
    preconditions: '',
    test_steps: [
      {
        step_number: 1,
        action: '',
        expected_result: '',
        test_data: ''
      }
    ],
    expected_result: '',
    test_data: null,
    tags: [],
    author: '',
    reviewer: '',
    review_status: 'pending',
    review_comments: '',
    estimated_time: 0,
    automation_level: 'manual',
    test_environment: '',
    related_requirements: '',
    related_defects: '',
    notes: '',
    database_type: 'mysql',
    database_version: ''
  })

  // 重置版本选项
  availableVersions.value = databaseVersions.value['mysql'] || []
  if (availableVersions.value.length > 0) {
    form.database_version = availableVersions.value[0]
  }

  if (formRef.value) {
    formRef.value.resetFields()
  }
}
</script>

<style scoped>
/* 测试步骤与其他表单项保持一致的布局 */

.test-steps {
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  padding: 16px;
  background: #fafafa;
  max-height: 400px;
  overflow-y: auto;
  width: 100%;
}

.test-step {
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: box-shadow 0.2s ease;
}

.test-step:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.test-step:last-child {
  margin-bottom: 0;
}

.step-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #f0f0f0;
}

.step-number {
  font-weight: bold;
  color: #409eff;
  font-size: 16px;
}

.step-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.step-item {
  display: flex;
  flex-direction: column;
}

.step-item strong {
  color: #606266;
  margin-bottom: 8px;
  font-size: 14px;
}

.step-textarea .el-textarea__inner {
  padding: 12px !important;
  background: #f8f9fa !important;
  border-radius: 4px !important;
  border: 1px solid #e9ecef !important;
  white-space: pre-wrap !important;
  word-break: break-word !important;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace !important;
  font-size: 13px !important;
  line-height: 1.5 !important;
  min-height: 80px !important;
  resize: vertical !important;
}

.dialog-footer {
  text-align: right;
  padding-top: 20px;
}

/* 通用文本框样式已在step-textarea中定义 */

/* SQL文本框特殊样式 */
.sql-textarea .el-textarea__inner {
  min-height: 150px !important;
  max-height: 400px !important;
}

/* 确保文本框容器有足够的高度 */
.test-step .el-textarea {
  width: 100% !important;
}

.test-step .el-textarea .el-textarea__inner {
  width: 100% !important;
  box-sizing: border-box !important;
}

/* 强制覆盖Element Plus的默认样式 */
.el-dialog .test-steps .step-textarea .el-textarea__inner {
  padding: 12px !important;
  background: #f8f9fa !important;
  border: 1px solid #e9ecef !important;
  border-radius: 4px !important;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace !important;
  font-size: 13px !important;
  line-height: 1.5 !important;
  min-height: 80px !important;
}

/* 添加步骤按钮样式 */
.test-steps .el-button {
  margin-top: 10px;
}

/* 对话框内容区域滚动 */
.el-dialog__body {
  max-height: 75vh;
  overflow-y: auto;
  padding: 20px 24px;
}

/* 对话框标题样式 */
.el-dialog__header {
  padding: 20px 24px 10px;
  border-bottom: 1px solid #f0f0f0;
}

/* 表单项间距 */
.el-form-item {
  margin-bottom: 20px;
}

/* 步骤删除按钮样式 */
.step-header .el-button {
  margin: 0;
}
</style>

<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    title="记录执行结果"
    width="50%"
  >
    <div v-if="testCase" class="execution-form">
      <div class="test-case-info">
        <h4>{{ testCase.title }}</h4>
        <p class="module">模块: {{ testCase.module }}</p>
      </div>

      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        v-loading="loading"
      >
        <el-form-item label="执行结果" prop="execution_result">
          <el-radio-group v-model="form.execution_result">
            <el-radio label="pass">
              <el-tag type="success">通过</el-tag>
            </el-radio>
            <el-radio label="fail">
              <el-tag type="danger">失败</el-tag>
            </el-radio>
            <el-radio label="blocked">
              <el-tag type="warning">阻塞</el-tag>
            </el-radio>
            <el-radio label="skip">
              <el-tag type="info">跳过</el-tag>
            </el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="执行时间" prop="execution_time">
          <el-input-number
            v-model="form.execution_time"
            :min="0"
            :precision="0"
            style="width: 200px"
          />
          <span style="margin-left: 8px; color: #909399;">分钟</span>
        </el-form-item>

        <el-form-item label="执行者">
          <el-input
            v-model="form.executor"
            placeholder="请输入执行者姓名"
            style="width: 300px"
          />
        </el-form-item>

        <el-form-item label="执行环境">
          <el-input
            v-model="form.execution_environment"
            placeholder="请输入执行环境"
            style="width: 300px"
          />
        </el-form-item>

        <el-form-item label="发现的缺陷">
          <el-select
            v-model="form.defects_found"
            multiple
            filterable
            allow-create
            placeholder="请输入发现的缺陷ID或描述"
            style="width: 100%"
          >
          </el-select>
          <div class="form-tip">可以输入缺陷ID或缺陷描述，支持多个</div>
        </el-form-item>

        <el-form-item label="执行备注">
          <el-input
            v-model="form.execution_notes"
            type="textarea"
            :rows="4"
            placeholder="请输入执行过程中的备注信息，如遇到的问题、特殊情况等"
          />
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          保存执行记录
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import testCaseApi from '../services/testCaseApi'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  testCase: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// 响应式数据
const loading = ref(false)
const formRef = ref()

// 表单数据
const form = reactive({
  execution_result: 'pass',
  execution_time: 0,
  executor: '',
  execution_environment: '',
  defects_found: [],
  execution_notes: ''
})

// 表单验证规则
const rules = {
  execution_result: [
    { required: true, message: '请选择执行结果', trigger: 'change' }
  ],
  execution_time: [
    { required: true, message: '请输入执行时间', trigger: 'blur' },
    { type: 'number', min: 0, message: '执行时间不能小于0', trigger: 'blur' }
  ]
}

// 监听对话框显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    resetForm()
    // 如果测试用例有预估时间，设置为默认执行时间
    if (props.testCase && props.testCase.estimated_time) {
      form.execution_time = props.testCase.estimated_time
    }
    // 如果测试用例有测试环境，设置为默认执行环境
    if (props.testCase && props.testCase.test_environment) {
      form.execution_environment = props.testCase.test_environment
    }
  }
})

// 方法
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    loading.value = true
    
    const executionRecord = {
      test_case_id: props.testCase.id,
      execution_result: form.execution_result,
      execution_time: form.execution_time,
      executor: form.executor || null,
      execution_environment: form.execution_environment || null,
      defects_found: form.defects_found.length > 0 ? form.defects_found : null,
      execution_notes: form.execution_notes || null
    }
    
    await testCaseApi.recordExecution(props.testCase.id, executionRecord)
    ElMessage.success('执行记录保存成功')
    emit('success')
  } catch (error) {
    ElMessage.error('保存执行记录失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

const handleClose = () => {
  emit('update:visible', false)
  resetForm()
}

const resetForm = () => {
  Object.assign(form, {
    execution_result: 'pass',
    execution_time: 0,
    executor: '',
    execution_environment: '',
    defects_found: [],
    execution_notes: ''
  })
  
  if (formRef.value) {
    formRef.value.resetFields()
  }
}
</script>

<style scoped>
.execution-form {
  padding: 0 20px;
}

.test-case-info {
  margin-bottom: 24px;
  padding: 16px;
  background: #f5f7fa;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.test-case-info h4 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 16px;
}

.test-case-info .module {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.el-radio {
  margin-right: 20px;
  margin-bottom: 10px;
}

.dialog-footer {
  text-align: right;
}
</style>

<template>
  <div class="requirement-statistics">
    <el-row :gutter="20">
      <!-- 总体统计 -->
      <el-col :span="24">
        <el-card class="stats-card">
          <template #header>
            <div class="card-header">
              <span>总体统计</span>
              <el-button type="text" @click="refreshStats">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </template>
          <el-row :gutter="20">
            <el-col :span="6">
              <el-statistic title="总需求数" :value="statistics.total" suffix="个">
                <template #prefix>
                  <el-icon color="#409EFF"><Document /></el-icon>
                </template>
              </el-statistic>
            </el-col>
            <el-col :span="6">
              <el-statistic title="已完成" :value="statistics.completed" suffix="个">
                <template #prefix>
                  <el-icon color="#67C23A"><CircleCheck /></el-icon>
                </template>
              </el-statistic>
            </el-col>
            <el-col :span="6">
              <el-statistic title="进行中" :value="statistics.in_progress" suffix="个">
                <template #prefix>
                  <el-icon color="#E6A23C"><Clock /></el-icon>
                </template>
              </el-statistic>
            </el-col>
            <el-col :span="6">
              <el-statistic title="完成率" :value="completionRate" suffix="%">
                <template #prefix>
                  <el-icon color="#F56C6C"><TrendCharts /></el-icon>
                </template>
              </el-statistic>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px">
      <!-- 需求类型分布 -->
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <span>需求类型分布</span>
          </template>
          <div ref="typeChartRef" class="chart-container"></div>
        </el-card>
      </el-col>

      <!-- 优先级分布 -->
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <span>优先级分布</span>
          </template>
          <div ref="priorityChartRef" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px">
      <!-- 状态趋势 -->
      <el-col :span="16">
        <el-card class="chart-card">
          <template #header>
            <div class="chart-header">
              <span>状态趋势</span>
              <el-radio-group v-model="trendPeriod" size="small" @change="updateTrendChart">
                <el-radio-button label="7d">近7天</el-radio-button>
                <el-radio-button label="30d">近30天</el-radio-button>
                <el-radio-button label="90d">近90天</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <div ref="trendChartRef" class="chart-container trend-chart"></div>
        </el-card>
      </el-col>

      <!-- 协议分布 -->
      <el-col :span="8">
        <el-card class="chart-card">
          <template #header>
            <span>协议分布</span>
          </template>
          <div class="protocol-stats">
            <div
              v-for="(count, protocol) in statistics.protocol_distribution"
              :key="protocol"
              class="protocol-item"
            >
              <div class="protocol-name">{{ getProtocolLabel(protocol) }}</div>
              <el-progress
                :percentage="getProtocolPercentage(count)"
                :color="getProtocolColor(protocol)"
              />
              <div class="protocol-count">{{ count }} 个</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px">
      <!-- 测试覆盖率 -->
      <el-col :span="8">
        <el-card class="stats-card">
          <template #header>
            <span>测试覆盖率</span>
          </template>
          <div class="coverage-stats">
            <div class="coverage-item">
              <div class="coverage-label">有测试用例的需求</div>
              <el-progress
                type="circle"
                :percentage="statistics.test_coverage.with_test_cases"
                :width="100"
                :stroke-width="8"
                color="#67C23A"
              />
            </div>
            <div class="coverage-details">
              <el-descriptions :column="1" size="small">
                <el-descriptions-item label="已生成测试用例">
                  {{ statistics.test_coverage.generated_cases }} 个
                </el-descriptions-item>
                <el-descriptions-item label="手动创建测试用例">
                  {{ statistics.test_coverage.manual_cases }} 个
                </el-descriptions-item>
                <el-descriptions-item label="平均每需求测试用例">
                  {{ statistics.test_coverage.average_cases_per_requirement }} 个
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 质量指标 -->
      <el-col :span="8">
        <el-card class="stats-card">
          <template #header>
            <span>质量指标</span>
          </template>
          <div class="quality-stats">
            <div class="quality-item">
              <div class="quality-label">可测试性</div>
              <el-rate
                v-model="statistics.quality_metrics.testability_avg"
                disabled
                show-score
                text-color="#ff9900"
                score-template="{value} 分"
              />
            </div>
            <div class="quality-item">
              <div class="quality-label">完整性</div>
              <el-progress
                :percentage="statistics.quality_metrics.completeness"
                :color="getQualityColor(statistics.quality_metrics.completeness)"
              />
            </div>
            <div class="quality-item">
              <div class="quality-label">一致性</div>
              <el-progress
                :percentage="statistics.quality_metrics.consistency"
                :color="getQualityColor(statistics.quality_metrics.consistency)"
              />
            </div>
            <div class="quality-item">
              <div class="quality-label">可追溯性</div>
              <el-progress
                :percentage="statistics.quality_metrics.traceability"
                :color="getQualityColor(statistics.quality_metrics.traceability)"
              />
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 工作量统计 -->
      <el-col :span="8">
        <el-card class="stats-card">
          <template #header>
            <span>工作量统计</span>
          </template>
          <div class="workload-stats">
            <el-descriptions :column="1" size="small">
              <el-descriptions-item label="平均复杂度">
                <el-rate
                  v-model="statistics.workload.complexity_avg"
                  disabled
                  show-score
                  text-color="#ff9900"
                  score-template="{value} 分"
                />
              </el-descriptions-item>
              <el-descriptions-item label="高优先级需求">
                {{ statistics.workload.high_priority_count }} 个
              </el-descriptions-item>
              <el-descriptions-item label="待审核需求">
                {{ statistics.workload.pending_review }} 个
              </el-descriptions-item>
              <el-descriptions-item label="预计工作量">
                {{ statistics.workload.estimated_effort }} 人天
              </el-descriptions-item>
            </el-descriptions>
            <div class="effort-breakdown">
              <h5>工作量分解</h5>
              <div class="effort-item">
                <span>需求分析: {{ statistics.workload.analysis_effort }}%</span>
                <el-progress :percentage="statistics.workload.analysis_effort" :show-text="false" />
              </div>
              <div class="effort-item">
                <span>测试设计: {{ statistics.workload.design_effort }}%</span>
                <el-progress :percentage="statistics.workload.design_effort" :show-text="false" />
              </div>
              <div class="effort-item">
                <span>测试执行: {{ statistics.workload.execution_effort }}%</span>
                <el-progress :percentage="statistics.workload.execution_effort" :show-text="false" />
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 时间统计 -->
    <el-row style="margin-top: 20px">
      <el-col :span="24">
        <el-card class="stats-card">
          <template #header>
            <span>时间统计</span>
          </template>
          <el-row :gutter="20">
            <el-col :span="6">
              <el-statistic title="本周新增" :value="statistics.time_stats.this_week" suffix="个" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="本月新增" :value="statistics.time_stats.this_month" suffix="个" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="平均处理时间" :value="statistics.time_stats.avg_processing_time" suffix="天" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="最长处理时间" :value="statistics.time_stats.max_processing_time" suffix="天" />
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { Refresh, Document, CircleCheck, Clock, TrendCharts } from '@element-plus/icons-vue'
import * as echarts from 'echarts'

export default {
  name: 'RequirementStatistics',
  components: {
    Refresh,
    Document,
    CircleCheck,
    Clock,
    TrendCharts
  },
  props: {
    requirements: {
      type: Array,
      default: () => []
    }
  },
  setup(props) {
    const typeChartRef = ref(null)
    const priorityChartRef = ref(null)
    const trendChartRef = ref(null)
    const trendPeriod = ref('30d')

    let typeChart = null
    let priorityChart = null
    let trendChart = null

    // 统计数据
    const statistics = reactive({
      total: 156,
      completed: 89,
      in_progress: 45,
      draft: 22,
      protocol_distribution: {
        mysql: 45,
        postgresql: 32,
        mongodb: 28,
        redis: 21,
        http: 18,
        websocket: 12
      },
      test_coverage: {
        with_test_cases: 72,
        generated_cases: 234,
        manual_cases: 89,
        average_cases_per_requirement: 2.8
      },
      quality_metrics: {
        testability_avg: 3.8,
        completeness: 85,
        consistency: 78,
        traceability: 92
      },
      workload: {
        complexity_avg: 3.2,
        high_priority_count: 34,
        pending_review: 12,
        estimated_effort: 45.5,
        analysis_effort: 30,
        design_effort: 40,
        execution_effort: 30
      },
      time_stats: {
        this_week: 8,
        this_month: 23,
        avg_processing_time: 3.5,
        max_processing_time: 15
      }
    })

    // 计算完成率
    const completionRate = computed(() => {
      return statistics.total > 0 
        ? Math.round((statistics.completed / statistics.total) * 100)
        : 0
    })

    // 获取协议标签
    const getProtocolLabel = (protocol) => {
      const mapping = {
        mysql: 'MySQL',
        postgresql: 'PostgreSQL',
        mongodb: 'MongoDB',
        redis: 'Redis',
        http: 'HTTP',
        websocket: 'WebSocket'
      }
      return mapping[protocol] || protocol
    }

    // 获取协议百分比
    const getProtocolPercentage = (count) => {
      return Math.round((count / statistics.total) * 100)
    }

    // 获取协议颜色
    const getProtocolColor = (protocol) => {
      const colors = {
        mysql: '#409EFF',
        postgresql: '#67C23A',
        mongodb: '#E6A23C',
        redis: '#F56C6C',
        http: '#909399',
        websocket: '#8B5CF6'
      }
      return colors[protocol] || '#409EFF'
    }

    // 获取质量颜色
    const getQualityColor = (percentage) => {
      if (percentage >= 80) return '#67C23A'
      if (percentage >= 60) return '#E6A23C'
      return '#F56C6C'
    }

    // 初始化图表
    const initCharts = () => {
      nextTick(() => {
        initTypeChart()
        initPriorityChart()
        initTrendChart()
      })
    }

    // 需求类型分布图
    const initTypeChart = () => {
      if (!typeChartRef.value) return
      
      typeChart = echarts.init(typeChartRef.value)
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          bottom: '5%',
          left: 'center'
        },
        series: [
          {
            name: '需求类型',
            type: 'pie',
            radius: ['40%', '70%'],
            center: ['50%', '45%'],
            data: [
              { value: 89, name: '功能需求', itemStyle: { color: '#409EFF' } },
              { value: 45, name: '非功能需求', itemStyle: { color: '#67C23A' } },
              { value: 22, name: '测试需求', itemStyle: { color: '#E6A23C' } }
            ],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }
      typeChart.setOption(option)
    }

    // 优先级分布图
    const initPriorityChart = () => {
      if (!priorityChartRef.value) return
      
      priorityChart = echarts.init(priorityChartRef.value)
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: ['低', '中', '高']
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '需求数量',
            type: 'bar',
            data: [
              { value: 67, itemStyle: { color: '#67C23A' } },
              { value: 55, itemStyle: { color: '#E6A23C' } },
              { value: 34, itemStyle: { color: '#F56C6C' } }
            ],
            barWidth: '60%'
          }
        ]
      }
      priorityChart.setOption(option)
    }

    // 状态趋势图
    const initTrendChart = () => {
      if (!trendChartRef.value) return
      
      trendChart = echarts.init(trendChartRef.value)
      updateTrendChart()
    }

    // 更新趋势图
    const updateTrendChart = () => {
      if (!trendChart) return

      const data = getTrendData(trendPeriod.value)
      const option = {
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['新增', '完成', '审批']
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: data.dates
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '新增',
            type: 'line',
            stack: 'Total',
            data: data.created,
            itemStyle: { color: '#409EFF' }
          },
          {
            name: '完成',
            type: 'line',
            stack: 'Total',
            data: data.completed,
            itemStyle: { color: '#67C23A' }
          },
          {
            name: '审批',
            type: 'line',
            stack: 'Total',
            data: data.approved,
            itemStyle: { color: '#E6A23C' }
          }
        ]
      }
      trendChart.setOption(option)
    }

    // 获取趋势数据
    const getTrendData = (period) => {
      const days = parseInt(period)
      const dates = []
      const created = []
      const completed = []
      const approved = []

      for (let i = days - 1; i >= 0; i--) {
        const date = new Date()
        date.setDate(date.getDate() - i)
        dates.push(date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' }))
        
        // 模拟数据
        created.push(Math.floor(Math.random() * 8) + 1)
        completed.push(Math.floor(Math.random() * 6) + 1)
        approved.push(Math.floor(Math.random() * 4) + 1)
      }

      return { dates, created, completed, approved }
    }

    // 刷新统计数据
    const refreshStats = () => {
      // 模拟刷新数据
      console.log('刷新统计数据')
    }

    // 窗口大小变化时重新调整图表
    const handleResize = () => {
      typeChart?.resize()
      priorityChart?.resize()
      trendChart?.resize()
    }

    // 生命周期
    onMounted(() => {
      initCharts()
      window.addEventListener('resize', handleResize)
    })

    return {
      typeChartRef,
      priorityChartRef,
      trendChartRef,
      trendPeriod,
      statistics,
      completionRate,
      getProtocolLabel,
      getProtocolPercentage,
      getProtocolColor,
      getQualityColor,
      updateTrendChart,
      refreshStats
    }
  }
}
</script>

<style scoped>
.requirement-statistics {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.stats-card {
  height: 100%;
}

.chart-card {
  height: 300px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.chart-container {
  height: 220px;
}

.trend-chart {
  height: 240px;
}

.protocol-stats {
  padding: 10px 0;
}

.protocol-item {
  margin-bottom: 15px;
}

.protocol-name {
  font-size: 14px;
  margin-bottom: 5px;
  color: #606266;
}

.protocol-count {
  font-size: 12px;
  color: #909399;
  text-align: right;
  margin-top: 5px;
}

.coverage-stats {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.coverage-item {
  margin-bottom: 20px;
}

.coverage-label {
  text-align: center;
  margin-bottom: 10px;
  font-size: 14px;
  color: #606266;
}

.coverage-details {
  width: 100%;
}

.quality-stats, .workload-stats {
  padding: 10px 0;
}

.quality-item, .effort-item {
  margin-bottom: 15px;
}

.quality-label {
  font-size: 14px;
  margin-bottom: 8px;
  color: #606266;
}

.effort-breakdown {
  margin-top: 20px;
}

.effort-breakdown h5 {
  margin: 0 0 15px 0;
  font-size: 14px;
  color: #303133;
}

.effort-item span {
  font-size: 12px;
  color: #606266;
  margin-bottom: 5px;
  display: block;
}

.el-statistic {
  text-align: center;
}

.el-descriptions {
  margin-top: 10px;
}
</style>

<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    title="批量执行测试用例"
    width="70%"
    :before-close="handleClose"
  >
    <div v-loading="loading">
      <!-- 基本信息 -->
      <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="批量任务名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入批量执行任务名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="数据库类型" prop="database_type">
              <el-select 
                v-model="form.database_type" 
                placeholder="请选择数据库类型"
                @change="onDatabaseTypeChange"
                style="width: 100%"
              >
                <el-option label="MySQL" value="mysql" />
                <el-option label="PostgreSQL" value="postgresql" />
                <el-option label="MongoDB" value="mongodb" />
                <el-option label="Oracle" value="oracle" />
                <el-option label="GaussDB" value="gaussdb" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="数据库版本" prop="database_version">
              <el-select 
                v-model="form.database_version" 
                placeholder="请选择数据库版本"
                @change="onDatabaseVersionChange"
                style="width: 100%"
              >
                <el-option 
                  v-for="version in availableVersions" 
                  :key="version" 
                  :label="version" 
                  :value="version" 
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="数据库配置" prop="config_id">
              <el-select 
                v-model="form.config_id" 
                placeholder="请选择数据库配置"
                style="width: 100%"
                :loading="configsLoading"
              >
                <el-option 
                  v-for="config in availableConfigs" 
                  :key="config.id" 
                  :label="`${config.name} (${config.host}:${config.port})`" 
                  :value="config.id" 
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="单用例超时">
              <el-input-number 
                v-model="form.timeout_per_case" 
                :min="60" 
                :max="1800" 
                :step="30"
                style="width: 100%"
              />
              <span class="form-help">秒</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="执行选项">
              <div class="execution-options">
                <el-checkbox v-model="form.capture_enabled">启用抓包</el-checkbox>
                <el-checkbox v-model="form.stop_on_failure">遇到失败停止</el-checkbox>
                <el-checkbox
                  v-model="form.use_c_executor"
                  v-if="form.database_type === 'gaussdb' || form.database_type === 'postgresql' || form.database_type === 'mongodb'"
                  :title="'使用C语言执行器连接数据库，提供更好的性能'"
                >
                  使用C语言执行器
                  <el-tooltip
                    :content="getExecutorTooltip(form.database_type)"
                    placement="top"
                  >
                    <el-icon style="margin-left: 4px; color: #909399;"><QuestionFilled /></el-icon>
                  </el-tooltip>
                </el-checkbox>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="描述">
          <el-input 
            v-model="form.description" 
            type="textarea" 
            :rows="3" 
            placeholder="请输入批量执行描述（可选）" 
          />
        </el-form-item>
      </el-form>

      <!-- 选中的测试用例 -->
      <div class="selected-cases-section">
        <h4>选中的测试用例 ({{ filteredTestCases.length }}个)</h4>
        <div class="case-filter">
          <el-alert 
            v-if="incompatibleCases.length > 0"
            :title="`有 ${incompatibleCases.length} 个测试用例与选择的数据库类型/版本不兼容，将被过滤`"
            type="warning"
            :closable="false"
            style="margin-bottom: 10px;"
          />
        </div>
        
        <el-table :data="filteredTestCases" border size="small" max-height="300">
          <el-table-column prop="title" label="测试用例标题" min-width="200" show-overflow-tooltip />
          <el-table-column prop="module" label="模块" width="120" />
          <el-table-column prop="database_type" label="数据库类型" width="100" />
          <el-table-column prop="database_version" label="数据库版本" width="120" />
          <el-table-column prop="automation_level" label="自动化程度" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.automation_level === 'full' ? 'success' : 'warning'" size="small">
                {{ getAutomationLevelText(scope.row.automation_level) }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleSubmit" 
          :loading="loading"
          :disabled="filteredTestCases.length === 0"
        >
          开始批量执行 ({{ filteredTestCases.length }}个用例)
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { QuestionFilled } from '@element-plus/icons-vue'
import apiService from '@/services/api'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  selectedTestCases: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// 响应式数据
const loading = ref(false)
const configsLoading = ref(false)
const formRef = ref(null)

// 表单数据
const form = reactive({
  name: '',
  database_type: 'mysql',
  database_version: '',
  config_id: null,
  timeout_per_case: 300,
  capture_enabled: true,
  stop_on_failure: false,
  use_c_executor: false,
  description: ''
})

// 数据库版本选项
const databaseVersions = ref({
  mysql: ['MySQL 8.0', 'MySQL 5.7', 'MySQL 8.1', 'MySQL 8.2', 'MySQL 5.6'],
  postgresql: ['PostgreSQL 15', 'PostgreSQL 14', 'PostgreSQL 13', 'PostgreSQL 12', 'PostgreSQL 16'],
  mongodb: ['MongoDB 7.0', 'MongoDB 6.0', 'MongoDB 5.0', 'MongoDB 4.4', 'MongoDB 8.0'],
  oracle: ['Oracle 21c', 'Oracle 19c', 'Oracle 18c', 'Oracle 12c', 'Oracle 23c'],
  gaussdb: ['GaussDB 5.0', 'GaussDB 3.0', 'GaussDB 2.0', 'GaussDB 1.0', 'openGauss 5.0']
})

const availableVersions = ref([])
const availableConfigs = ref([])

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入批量任务名称', trigger: 'blur' },
    { min: 1, max: 255, message: '名称长度在 1 到 255 个字符', trigger: 'blur' }
  ],
  database_type: [
    { required: true, message: '请选择数据库类型', trigger: 'change' }
  ],
  database_version: [
    { required: true, message: '请选择数据库版本', trigger: 'change' }
  ],
  config_id: [
    { required: true, message: '请选择数据库配置', trigger: 'change' }
  ]
}

// 计算属性
const filteredTestCases = computed(() => {
  if (!form.database_type || !form.database_version) {
    return []
  }
  
  return props.selectedTestCases.filter(testCase => {
    return testCase.database_type === form.database_type && 
           testCase.database_version === form.database_version
  })
})

const incompatibleCases = computed(() => {
  if (!form.database_type || !form.database_version) {
    return []
  }
  
  return props.selectedTestCases.filter(testCase => {
    return testCase.database_type !== form.database_type || 
           testCase.database_version !== form.database_version
  })
})

// 监听器
watch(() => props.visible, (newVal) => {
  if (newVal) {
    resetForm()
    initializeForm()
  }
})

// 方法
const initializeForm = () => {
  // 根据选中的测试用例自动设置数据库类型和版本
  if (props.selectedTestCases.length > 0) {
    const firstCase = props.selectedTestCases[0]
    form.database_type = firstCase.database_type || 'mysql'
    onDatabaseTypeChange()
    
    if (firstCase.database_version) {
      form.database_version = firstCase.database_version
      onDatabaseVersionChange()
    }
    
    // 生成默认任务名称
    const timestamp = new Date().toLocaleString('zh-CN')
    form.name = `批量执行_${form.database_type}_${timestamp}`
  }
}

const onDatabaseTypeChange = () => {
  availableVersions.value = databaseVersions.value[form.database_type] || []
  form.database_version = ''
  form.config_id = null
  availableConfigs.value = []
}

const onDatabaseVersionChange = async () => {
  if (form.database_type && form.database_version) {
    await loadDatabaseConfigs()
  }
}

const loadDatabaseConfigs = async () => {
  try {
    configsLoading.value = true
    console.log('开始加载数据库配置...')
    const response = await apiService.getDatabaseConfigs(1, 100)
    console.log('数据库配置响应:', response)

    if (!response || !response.databases) {
      console.error('数据库配置响应格式错误:', response)
      ElMessage.error('数据库配置响应格式错误')
      return
    }

    // 过滤出与选择的数据库类型和版本匹配的配置，并且只选择活跃的配置
    if (form.database_type && form.database_version) {
      availableConfigs.value = response.databases.filter(
        config => config.database_type === form.database_type &&
                 config.database_version === form.database_version &&
                 config.is_active
      )
      console.log(`过滤${form.database_type} ${form.database_version}类型的配置:`, availableConfigs.value)
    } else if (form.database_type) {
      availableConfigs.value = response.databases.filter(
        config => config.database_type === form.database_type && config.is_active
      )
      console.log(`过滤${form.database_type}类型的配置:`, availableConfigs.value)
    } else {
      availableConfigs.value = response.databases.filter(config => config.is_active)
      console.log('过滤活跃的配置:', availableConfigs.value)
    }

    if (availableConfigs.value.length === 0) {
      const dbType = form.database_type || '任意类型'
      const dbVersion = form.database_version || '任意版本'
      ElMessage.warning(`没有找到${dbType} ${dbVersion}的活跃数据库配置，请先在数据库管理页面添加配置`)
      return
    }

    // 如果有默认配置，自动选择
    const defaultConfig = availableConfigs.value.find(config => config.is_default)
    if (defaultConfig && !form.config_id) {
      form.config_id = defaultConfig.id
      console.log('自动选择默认配置:', defaultConfig.name)
    } else if (availableConfigs.value.length === 1) {
      // 如果只有一个配置，自动选择
      form.config_id = availableConfigs.value[0].id
      console.log('自动选择唯一配置:', availableConfigs.value[0].name)
    }
  } catch (error) {
    console.error('加载数据库配置失败:', error)
    console.error('错误详情:', error.response?.data)
    ElMessage.error('加载数据库配置失败: ' + (error.message || '网络错误'))
  } finally {
    configsLoading.value = false
  }
}

const getAutomationLevelText = (level) => {
  const texts = {
    manual: '手动',
    semi: '半自动',
    full: '全自动'
  }
  return texts[level] || level
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    if (filteredTestCases.value.length === 0) {
      ElMessage.error('没有符合条件的测试用例可执行')
      return
    }
    
    loading.value = true
    
    // 准备批量执行请求数据
    const batchRequest = {
      name: form.name,
      database_type: form.database_type,
      database_version: form.database_version,
      config_id: form.config_id,
      test_case_items: filteredTestCases.value.map(testCase => ({
        test_case_id: testCase.id,
        test_case_title: testCase.title,
        test_case_json: JSON.stringify(testCase)
      })),
      capture_enabled: form.capture_enabled,
      timeout_per_case: form.timeout_per_case,
      stop_on_failure: form.stop_on_failure,
      use_c_executor: form.use_c_executor,
      description: form.description
    }
    
    // 提交批量执行请求
    const response = await apiService.batchExecuteTestCases(batchRequest)
    
    ElMessage.success(`批量执行任务已提交，任务ID: ${response.batch_id}`)
    emit('success')
    
  } catch (error) {
    console.error('提交批量执行失败:', error)
    ElMessage.error('提交批量执行失败: ' + (error.message || '未知错误'))
  } finally {
    loading.value = false
  }
}

const getExecutorTooltip = (databaseType) => {
  if (databaseType === 'gaussdb') {
    return '使用C语言libpq库直接连接GaussDB，减少Python解释器开销，提供更好的性能'
  } else if (databaseType === 'postgresql') {
    return '使用C语言libpq库直接连接PostgreSQL，减少Python解释器开销，提供更好的性能'
  } else if (databaseType === 'mongodb') {
    return '使用C语言MongoDB驱动直接连接MongoDB，减少Python解释器开销，提供更好的性能'
  }
  return '使用C语言执行器提供更好的性能'
}

const handleClose = () => {
  emit('update:visible', false)
  resetForm()
}

const resetForm = () => {
  Object.assign(form, {
    name: '',
    database_type: 'mysql',
    database_version: '',
    config_id: null,
    timeout_per_case: 600,
    capture_enabled: true,
    stop_on_failure: false,
    use_c_executor: false,
    description: ''
  })
  
  availableVersions.value = databaseVersions.value['mysql'] || []
  availableConfigs.value = []
  
  if (formRef.value) {
    formRef.value.resetFields()
  }
}
</script>

<style scoped>
.selected-cases-section {
  margin-top: 20px;
}

.selected-cases-section h4 {
  margin-bottom: 10px;
  color: #303133;
}

.case-filter {
  margin-bottom: 10px;
}

.form-help {
  margin-left: 8px;
  color: #909399;
  font-size: 12px;
}

.execution-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.execution-options .el-checkbox {
  margin-right: 0;
}

.dialog-footer {
  text-align: right;
}
</style>

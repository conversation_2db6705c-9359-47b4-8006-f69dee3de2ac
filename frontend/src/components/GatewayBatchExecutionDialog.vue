<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    title="网关批量执行"
    width="800px"
    :close-on-click-modal="false"
  >
    <div class="gateway-batch-execution-dialog">
      <!-- 测试用例信息 -->
      <div class="section">
        <h4>测试用例信息</h4>
        <div class="info-grid">
          <div class="info-item">
            <label>选中用例数量：</label>
            <span>{{ testCases?.length || 0 }} 个</span>
          </div>
          <div class="info-item">
            <label>数据库类型：</label>
            <span>{{ getDatabaseTypes() }}</span>
          </div>
          <div class="info-item">
            <label>最新执行时间：</label>
            <span>{{ formatDate(getLatestExecutionTime()) || '未执行' }}</span>
          </div>
        </div>
        
        <!-- 用例列表预览 -->
        <div class="test-case-preview">
          <h5>将要上传的PCAP文件：</h5>
          <div class="pcap-files-preview">
            <div v-if="!hasAnyPcapFiles" class="no-pcap-warning">
              <el-icon><WarningFilled /></el-icon>
              <span>所选测试用例中没有可用的PCAP文件，无法执行网关测试</span>
            </div>
            <div v-else>
              <div v-for="testCase in testCasesWithPcap" :key="testCase.id" class="pcap-item">
                <span class="case-title">{{ testCase.title }}</span>
                <span class="pcap-count">({{ getPcapFileCount(testCase) }} 个PCAP文件)</span>
              </div>
              <div v-if="testCasesWithoutPcap.length > 0" class="warning-section">
                <div class="warning-title">
                  <el-icon><WarningFilled /></el-icon>
                  <span>以下测试用例没有PCAP文件，将被跳过：</span>
                </div>
                <div v-for="testCase in testCasesWithoutPcap" :key="testCase.id" class="warning-item">
                  {{ testCase.title }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 网关服务器选择 -->
      <div class="section">
        <h4>网关服务器选择</h4>
        <el-select 
          v-model="form.gateway_server_id" 
          placeholder="请选择网关服务器"
          style="width: 100%"
          @change="onGatewayServerChange"
        >
          <el-option
            v-for="server in gatewayServers"
            :key="server.id"
            :label="`${server.name} (${server.host}:${server.port})`"
            :value="server.id"
            :disabled="!server.kafka_enabled"
          >
            <span style="float: left">{{ server.name }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px">
              {{ server.host }}:{{ server.port }}
              <el-tag v-if="server.kafka_enabled" type="success" size="small">Kafka已启用</el-tag>
              <el-tag v-else type="danger" size="small">Kafka未启用</el-tag>
            </span>
          </el-option>
        </el-select>

        <!-- 选中的网关服务器详细信息 -->
        <div v-if="selectedGatewayServer" class="server-details">
          <h5>服务器配置信息：</h5>
          <div class="details-grid">
            <div class="detail-item">
              <label>Kafka地址：</label>
              <span>{{ selectedGatewayServer.kafka_host }}:{{ selectedGatewayServer.kafka_port }}</span>
            </div>
            <div class="detail-item">
              <label>Kafka主题：</label>
              <span>{{ selectedGatewayServer.kafka_topic }}</span>
            </div>
            <div class="detail-item">
              <label>上传目录：</label>
              <span>{{ selectedGatewayServer.upload_path || '/tmp/pcap_files' }}</span>
            </div>
          </div>
          
          <!-- Kafka连接测试 -->
          <div class="connection-test">
            <el-button 
              type="info" 
              size="small" 
              @click="testKafkaConnection"
              :loading="testingConnection"
            >
              <el-icon><Connection /></el-icon>
              测试Kafka连接
            </el-button>
            
            <!-- 连接测试结果 -->
            <div v-if="connectionTestResult" class="test-result">
              <el-alert
                :type="connectionTestResult.success ? 'success' : 'error'"
                :title="connectionTestResult.success ? 'Kafka连接正常' : 'Kafka连接失败'"
                :description="connectionTestResult.message"
                show-icon
                :closable="false"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 执行参数 -->
      <div class="section">
        <h4>执行参数</h4>
        <div class="params-grid">
          <div class="param-item">
            <label>Kafka等待时间：</label>
            <el-input-number
              v-model="form.wait_time"
              :min="1"
              :max="10"
              :step="1"
              style="width: 120px"
            />
            <span class="unit">秒 (消费Kafka数据的等待时间)</span>
          </div>
          <div class="param-item">
            <label>执行方式：</label>
            <el-radio-group v-model="form.async_execution">
              <el-radio :label="false">同步执行</el-radio>
              <el-radio :label="true">异步执行</el-radio>
            </el-radio-group>
            <div class="execution-mode-tip">
              <span v-if="form.async_execution">异步执行：任务在后台运行，请在任务管理查看进度</span>
              <span v-else>同步执行：等待所有用例执行完成后返回结果</span>
            </div>
          </div>
        </div>
      </div>



      <!-- 执行结果 -->
      <div v-if="executionResult" class="section">
        <h4>执行结果</h4>
        <div class="execution-result">
          <div class="result-summary">
            <div class="summary-item">
              <label>总用例数：</label>
              <span>{{ executionResult.total_cases }}</span>
            </div>
            <div class="summary-item">
              <label>成功：</label>
              <span class="success-count">{{ executionResult.success_cases }}</span>
            </div>
            <div class="summary-item">
              <label>失败：</label>
              <span class="failed-count">{{ executionResult.failed_cases }}</span>
            </div>
          </div>
          
          <!-- 详细结果 -->
          <div v-if="executionResult.execution_summary" class="result-details">
            <h5>执行详情：</h5>
            <el-table :data="executionResult.execution_summary" size="small" max-height="200">
              <el-table-column prop="test_case_title" label="测试用例" />
              <el-table-column prop="status" label="状态" width="80">
                <template #default="{ row }">
                  <el-tag :type="row.status === 'success' ? 'success' : 'danger'">
                    {{ row.status === 'success' ? '成功' : '失败' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="message" label="结果信息" />
              <el-table-column prop="execution_time" label="执行时间(秒)" width="100" />
            </el-table>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="$emit('update:visible', false)">取消</el-button>
        <el-button 
          type="primary" 
          @click="startExecution"
          :loading="executing"
          :disabled="!canExecute"
        >
          {{ executing ? '执行中...' : '开始执行' }}
        </el-button>

      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Connection, WarningFilled } from '@element-plus/icons-vue'
import gatewayServerApi from '../services/gatewayServerApi'
import gatewayExecutionApi from '../services/gatewayExecutionApi'
import { formatDate } from '../utils/timezone'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  testCases: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:visible', 'execution-completed'])

// 响应式数据
const gatewayServers = ref([])
const executing = ref(false)
const testingConnection = ref(false)
const connectionTestResult = ref(null)
const executionStatus = ref(null)
const executionResult = ref(null)

const form = reactive({
  gateway_server_id: null,
  wait_time: 2,
  async_execution: true // 默认异步执行，适合批量操作
})

// 计算属性
const selectedGatewayServer = computed(() => {
  return gatewayServers.value.find(server => server.id === form.gateway_server_id)
})

const canExecute = computed(() => {
  return form.gateway_server_id && hasAnyPcapFiles.value && !executing.value
})

// 检查是否有任何PCAP文件
const hasAnyPcapFiles = computed(() => {
  return props.testCases.some(testCase => hasPcapFiles(testCase))
})

// 有PCAP文件的测试用例
const testCasesWithPcap = computed(() => {
  return props.testCases.filter(testCase => hasPcapFiles(testCase))
})

// 没有PCAP文件的测试用例
const testCasesWithoutPcap = computed(() => {
  return props.testCases.filter(testCase => !hasPcapFiles(testCase))
})

// 监听对话框显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    loadGatewayServers()
    resetForm()
  }
})

// 方法
const loadGatewayServers = async () => {
  try {
    const res = await gatewayServerApi.getGatewayServers({ page: 1, page_size: 100 })
    console.log('加载网关服务器响应:', res)
    
    let items = []
    if (res.gateways) {
      items = res.gateways
    } else if (res.items) {
      items = res.items
    } else if (res.data) {
      items = res.data.items || res.data.gateways || res.data || []
    } else if (Array.isArray(res)) {
      items = res
    }
    
    console.log('解析得到的服务器列表:', items)
    
    gatewayServers.value = items
    
    const kafkaEnabledServers = items.filter(server => server.kafka_enabled)
    console.log('Kafka启用的服务器:', kafkaEnabledServers)
    
    if (kafkaEnabledServers.length === 0) {
      ElMessage.warning('当前没有启用Kafka的网关服务器，请先配置网关服务器并启用Kafka功能')
    }
  } catch (error) {
    console.error('加载网关服务器失败:', error)
    ElMessage.error('加载网关服务器失败: ' + (error.message || '未知错误'))
    gatewayServers.value = []
  }
}

const resetForm = () => {
  form.gateway_server_id = null
  form.wait_time = 2
  form.async_execution = true
  connectionTestResult.value = null
  executionResult.value = null
}

const onGatewayServerChange = () => {
  connectionTestResult.value = null
}

// 检查测试用例是否有PCAP文件
const hasPcapFiles = (testCase) => {
  if (!testCase?.last_execution_capture_files) return false
  const captureFiles = testCase.last_execution_capture_files
  
  if (Array.isArray(captureFiles)) {
    return captureFiles.some(file => file && file.trim() !== '')
  } else if (typeof captureFiles === 'object') {
    return Object.values(captureFiles).some(file => file && file.trim() !== '')
  }
  
  return false
}

// 获取PCAP文件数量
const getPcapFileCount = (testCase) => {
  if (!testCase?.last_execution_capture_files) return 0
  const captureFiles = testCase.last_execution_capture_files
  
  if (Array.isArray(captureFiles)) {
    return captureFiles.filter(file => file && file.trim() !== '').length
  } else if (typeof captureFiles === 'object') {
    return Object.values(captureFiles).filter(file => file && file.trim() !== '').length
  }
  
  return 0
}

// 获取数据库类型列表
const getDatabaseTypes = () => {
  const types = [...new Set(props.testCases.map(tc => tc.database_type).filter(Boolean))]
  return types.join(', ') || '未知'
}

// 获取最新执行时间
const getLatestExecutionTime = () => {
  const times = props.testCases
    .map(tc => tc.last_execution_time)
    .filter(Boolean)
    .sort((a, b) => new Date(b) - new Date(a))
  return times[0] || null
}

const testKafkaConnection = async () => {
  if (!form.gateway_server_id) {
    ElMessage.warning('请先选择网关服务器')
    return
  }

  testingConnection.value = true
  connectionTestResult.value = null

  try {
    const result = await gatewayExecutionApi.testKafkaConnection(form.gateway_server_id)
    connectionTestResult.value = result
    
    if (result.success) {
      ElMessage.success('Kafka连接测试成功')
    } else {
      ElMessage.error('Kafka连接测试失败: ' + result.error)
    }
  } catch (error) {
    console.error('Kafka连接测试失败:', error)
    connectionTestResult.value = {
      success: false,
      message: 'Kafka连接测试失败: ' + error.message
    }
    ElMessage.error('Kafka连接测试失败')
  } finally {
    testingConnection.value = false
  }
}

const startExecution = async () => {
  if (!form.gateway_server_id) {
    ElMessage.warning('请选择网关服务器')
    return
  }

  if (!hasAnyPcapFiles.value) {
    ElMessage.warning('所选测试用例中没有可用的PCAP文件')
    return
  }

  executing.value = true
  executionResult.value = null

  try {
    // 只包含有PCAP文件的测试用例ID
    const validTestCaseIds = testCasesWithPcap.value.map(tc => String(tc.id))
    
    const executionRequest = {
      test_case_ids: validTestCaseIds,
      gateway_server_id: form.gateway_server_id,
      wait_time: form.wait_time
    }

    const result = await gatewayExecutionApi.executeGatewayBatch(executionRequest)
    
    if (result.success) {
      if (form.async_execution) {
        // 异步执行：直接关闭弹框并提示去任务管理查看
        ElMessage.success(`网关批量执行已启动，任务ID: ${result.task_id}，请前往任务管理查看执行进度`)
        emit('update:visible', false) // 关闭弹框
        emit('execution-completed', result)
      } else {
        // 同步执行：显示结果
        ElMessage.success('网关批量执行完成')
        showExecutionResult(result)
        emit('execution-completed', result)
      }
    } else {
      ElMessage.error('网关批量执行失败: ' + result.message)
    }
  } catch (error) {
    console.error('网关批量执行失败:', error)
    ElMessage.error('网关批量执行失败: ' + (error.message || '未知错误'))
  } finally {
    executing.value = false
  }
}

const showExecutionResult = (result) => {
  executionResult.value = result.result || result
}


</script>

<style scoped>
.gateway-batch-execution-dialog {
  padding: 10px 0;
}

.section {
  margin-bottom: 25px;
  padding-bottom: 20px;
  border-bottom: 1px solid #ebeef5;
}

.section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.section h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.section h5 {
  margin: 15px 0 10px 0;
  color: #606266;
  font-size: 14px;
  font-weight: 600;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 15px;
  margin-bottom: 15px;
}

.info-item {
  display: flex;
  align-items: center;
}

.info-item label {
  font-weight: 600;
  color: #606266;
  margin-right: 8px;
  min-width: 100px;
}

.test-case-preview {
  margin-top: 15px;
}

.pcap-files-preview {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 15px;
  max-height: 200px;
  overflow-y: auto;
}

.no-pcap-warning {
  display: flex;
  align-items: center;
  color: #e6a23c;
  font-size: 14px;
}

.no-pcap-warning .el-icon {
  margin-right: 8px;
}

.pcap-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #eee;
}

.pcap-item:last-child {
  border-bottom: none;
}

.case-title {
  font-weight: 500;
  color: #303133;
}

.pcap-count {
  color: #909399;
  font-size: 12px;
}

.warning-section {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #eee;
}

.warning-title {
  display: flex;
  align-items: center;
  color: #e6a23c;
  font-weight: 600;
  margin-bottom: 10px;
}

.warning-title .el-icon {
  margin-right: 8px;
}

.warning-item {
  padding: 5px 0;
  color: #909399;
  font-size: 14px;
}

.server-details {
  margin-top: 15px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
}

.details-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 15px;
}

.detail-item {
  display: flex;
  align-items: center;
}

.detail-item label {
  font-weight: 600;
  color: #606266;
  margin-right: 8px;
  min-width: 80px;
}

.connection-test {
  margin-top: 15px;
}

.test-result {
  margin-top: 10px;
}

.params-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 20px;
}

.param-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.param-item label {
  font-weight: 600;
  color: #606266;
}

.param-item > div {
  display: flex;
  align-items: center;
  gap: 10px;
}

.unit {
  color: #909399;
  font-size: 12px;
}

.execution-mode-tip {
  margin-top: 5px;
}

.execution-mode-tip span {
  color: #909399;
  font-size: 12px;
}



.execution-result {
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
}

.result-summary {
  display: flex;
  gap: 30px;
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.summary-item label {
  font-weight: 600;
  color: #606266;
}

.success-count {
  color: #67c23a;
  font-weight: 600;
}

.failed-count {
  color: #f56c6c;
  font-weight: 600;
}

.result-details {
  margin-top: 15px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>

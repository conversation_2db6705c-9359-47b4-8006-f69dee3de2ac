<template>
  <div class="generate-test-cases-form">
    <el-alert
      title="AI 测试用例生成"
      description="基于需求内容，使用人工智能自动生成测试用例"
      type="info"
      show-icon
      :closable="false"
      style="margin-bottom: 20px"
    />

    <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
      <!-- 需求信息 -->
      <el-card header="需求信息" shadow="never" style="margin-bottom: 20px">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="需求标题">{{ requirement?.title || '-' }}</el-descriptions-item>
          <el-descriptions-item label="需求类型">{{ getTypeLabel(requirement?.requirement_type) }}</el-descriptions-item>
          <el-descriptions-item label="协议类型">{{ getProtocolLabel(requirement?.protocol_type) }}</el-descriptions-item>
          <el-descriptions-item label="优先级">{{ getPriorityLabel(requirement?.priority) }}</el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 生成配置 -->
      <el-divider content-position="left">生成配置</el-divider>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="AI 模型" prop="ai_model">
            <el-select v-model="form.ai_model" placeholder="选择AI模型">
              <el-option label="GPT-4 (推荐)" value="gpt-4" />
              <el-option label="GPT-3.5 Turbo" value="gpt-3.5-turbo" />
              <el-option label="Claude-3" value="claude-3" />
              <el-option label="Gemini Pro" value="gemini-pro" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="生成数量" prop="test_count">
            <el-input-number
              v-model="form.test_count"
              :min="1"
              :max="20"
              placeholder="测试用例数量"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="复杂度级别" prop="complexity_level">
            <el-select v-model="form.complexity_level" placeholder="选择复杂度">
              <el-option label="简单" value="simple" />
              <el-option label="中等" value="medium" />
              <el-option label="复杂" value="complex" />
              <el-option label="混合" value="mixed" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 测试类型 -->
      <el-form-item label="测试类型" prop="test_types">
        <el-checkbox-group v-model="form.test_types">
          <el-checkbox label="functional">功能测试</el-checkbox>
          <el-checkbox label="boundary">边界测试</el-checkbox>
          <el-checkbox label="exception">异常测试</el-checkbox>
          <el-checkbox label="negative">负面测试</el-checkbox>
          <el-checkbox label="performance">性能测试</el-checkbox>
          <el-checkbox label="security">安全测试</el-checkbox>
          <el-checkbox label="compatibility">兼容性测试</el-checkbox>
          <el-checkbox label="integration">集成测试</el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <!-- 测试场景 -->
      <el-form-item label="测试场景">
        <el-checkbox-group v-model="form.test_scenarios">
          <el-checkbox label="normal_flow">正常流程</el-checkbox>
          <el-checkbox label="alternative_flow">备选流程</el-checkbox>
          <el-checkbox label="error_handling">错误处理</el-checkbox>
          <el-checkbox label="concurrent_access">并发访问</el-checkbox>
          <el-checkbox label="data_validation">数据验证</el-checkbox>
          <el-checkbox label="state_transition">状态转换</el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <!-- 协议特定配置 -->
      <el-divider content-position="left">协议特定配置</el-divider>

      <!-- MySQL/PostgreSQL 配置 -->
      <div v-if="isDatabase(requirement?.protocol_type)">
        <el-form-item label="SQL 操作类型">
          <el-checkbox-group v-model="form.sql_operations">
            <el-checkbox label="select">SELECT查询</el-checkbox>
            <el-checkbox label="insert">INSERT插入</el-checkbox>
            <el-checkbox label="update">UPDATE更新</el-checkbox>
            <el-checkbox label="delete">DELETE删除</el-checkbox>
            <el-checkbox label="ddl">DDL操作</el-checkbox>
            <el-checkbox label="transaction">事务处理</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="数据量级别">
          <el-radio-group v-model="form.data_scale">
            <el-radio label="small">小数据量 (&lt;1K)</el-radio>
            <el-radio label="medium">中等数据量 (1K-10K)</el-radio>
            <el-radio label="large">大数据量 (&gt;10K)</el-radio>
          </el-radio-group>
        </el-form-item>
      </div>

      <!-- MongoDB 配置 -->
      <div v-if="requirement?.protocol_type === 'mongodb'">
        <el-form-item label="MongoDB 操作">
          <el-checkbox-group v-model="form.mongo_operations">
            <el-checkbox label="find">查询操作</el-checkbox>
            <el-checkbox label="insert">插入操作</el-checkbox>
            <el-checkbox label="update">更新操作</el-checkbox>
            <el-checkbox label="delete">删除操作</el-checkbox>
            <el-checkbox label="aggregate">聚合操作</el-checkbox>
            <el-checkbox label="index">索引操作</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="文档结构">
          <el-radio-group v-model="form.document_structure">
            <el-radio label="simple">简单文档</el-radio>
            <el-radio label="nested">嵌套文档</el-radio>
            <el-radio label="array">数组文档</el-radio>
            <el-radio label="mixed">混合结构</el-radio>
          </el-radio-group>
        </el-form-item>
      </div>

      <!-- HTTP 配置 -->
      <div v-if="requirement?.protocol_type === 'http'">
        <el-form-item label="HTTP 方法">
          <el-checkbox-group v-model="form.http_methods">
            <el-checkbox label="GET">GET</el-checkbox>
            <el-checkbox label="POST">POST</el-checkbox>
            <el-checkbox label="PUT">PUT</el-checkbox>
            <el-checkbox label="DELETE">DELETE</el-checkbox>
            <el-checkbox label="PATCH">PATCH</el-checkbox>
            <el-checkbox label="HEAD">HEAD</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="响应格式">
          <el-checkbox-group v-model="form.response_formats">
            <el-checkbox label="json">JSON</el-checkbox>
            <el-checkbox label="xml">XML</el-checkbox>
            <el-checkbox label="html">HTML</el-checkbox>
            <el-checkbox label="plain">纯文本</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </div>

      <!-- 高级配置 -->
      <el-divider content-position="left">高级配置</el-divider>

      <el-form-item label="测试优先级">
        <el-slider
          v-model="form.priority_weight"
          :min="1"
          :max="5"
          :marks="{ 1: '低', 3: '中', 5: '高' }"
          show-stops
        />
      </el-form-item>

      <el-form-item label="覆盖率要求">
        <el-slider
          v-model="form.coverage_requirement"
          :min="50"
          :max="100"
          :step="10"
          :marks="{ 50: '50%', 70: '70%', 90: '90%', 100: '100%' }"
          show-stops
        />
      </el-form-item>

      <el-form-item label="自定义提示">
        <el-input
          v-model="form.custom_prompt"
          type="textarea"
          :rows="4"
          placeholder="额外的生成指导说明，例如：重点关注边界条件、考虑并发场景、包含错误处理等"
        />
      </el-form-item>

      <el-form-item label="测试数据">
        <el-radio-group v-model="form.test_data_mode">
          <el-radio label="auto">自动生成</el-radio>
          <el-radio label="template">使用模板</el-radio>
          <el-radio label="custom">自定义数据</el-radio>
        </el-radio-group>
      </el-form-item>

      <div v-if="form.test_data_mode === 'custom'">
        <el-form-item label="测试数据">
          <el-input
            v-model="form.test_data"
            type="textarea"
            :rows="6"
            placeholder="输入JSON格式的测试数据..."
          />
        </el-form-item>
      </div>

      <!-- 输出配置 -->
      <el-divider content-position="left">输出配置</el-divider>

      <el-form-item label="输出格式">
        <el-checkbox-group v-model="form.output_formats">
          <el-checkbox label="structured">结构化格式</el-checkbox>
          <el-checkbox label="executable">可执行脚本</el-checkbox>
          <el-checkbox label="documentation">文档格式</el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <el-form-item label="包含内容">
        <el-checkbox-group v-model="form.include_content">
          <el-checkbox label="test_steps">测试步骤</el-checkbox>
          <el-checkbox label="expected_results">预期结果</el-checkbox>
          <el-checkbox label="test_data_examples">测试数据示例</el-checkbox>
          <el-checkbox label="preconditions">前置条件</el-checkbox>
          <el-checkbox label="postconditions">后置条件</el-checkbox>
          <el-checkbox label="cleanup_steps">清理步骤</el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <!-- 生成进度 -->
      <div v-if="generating" class="generation-progress">
        <el-divider content-position="center">生成进度</el-divider>
        <el-progress
          :percentage="generationProgress"
          :status="generationStatus"
        />
        <el-steps :active="currentStep" finish-status="success" style="margin-top: 20px">
          <el-step title="分析需求" />
          <el-step title="设计用例" />
          <el-step title="生成内容" />
          <el-step title="质量检查" />
          <el-step title="格式化输出" />
        </el-steps>
        <div class="generation-log" v-if="generationLog.length > 0">
          <h4>生成日志：</h4>
          <el-scrollbar height="150px">
            <div v-for="(log, index) in generationLog" :key="index" class="log-item">
              <el-tag :type="log.type" size="small">{{ log.timestamp }}</el-tag>
              {{ log.message }}
            </div>
          </el-scrollbar>
        </div>
      </div>

      <!-- 生成结果预览 -->
      <div v-if="generationResult && !generating" class="generation-result">
        <el-divider content-position="center">生成结果预览</el-divider>
        <el-alert
          :title="`成功生成 ${generationResult.test_cases?.length || 0} 个测试用例`"
          type="success"
          show-icon
          :closable="false"
          style="margin-bottom: 15px"
        />
        
        <el-collapse>
          <el-collapse-item title="生成统计" name="stats">
            <el-row :gutter="20">
              <el-col :span="6">
                <el-statistic title="总用例数" :value="generationResult.statistics?.total_cases || 0" />
              </el-col>
              <el-col :span="6">
                <el-statistic title="功能测试" :value="generationResult.statistics?.functional_cases || 0" />
              </el-col>
              <el-col :span="6">
                <el-statistic title="边界测试" :value="generationResult.statistics?.boundary_cases || 0" />
              </el-col>
              <el-col :span="6">
                <el-statistic title="异常测试" :value="generationResult.statistics?.exception_cases || 0" />
              </el-col>
            </el-row>
          </el-collapse-item>
          
          <el-collapse-item title="测试用例预览" name="preview">
            <el-table :data="generationResult.test_cases?.slice(0, 3)" style="width: 100%">
              <el-table-column prop="name" label="用例名称" width="300" show-overflow-tooltip />
              <el-table-column prop="type" label="类型" width="120" />
              <el-table-column prop="priority" label="优先级" width="100" />
              <el-table-column prop="description" label="描述" show-overflow-tooltip />
            </el-table>
            <div v-if="generationResult.test_cases?.length > 3" style="text-align: center; margin-top: 10px">
              <el-text type="info">还有 {{ generationResult.test_cases.length - 3 }} 个测试用例...</el-text>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </el-form>

    <div class="form-actions">
      <el-button @click="handleCancel" :disabled="generating">取消</el-button>
      <el-button v-if="generationResult && !generating" @click="handleSave" type="success">
        保存测试用例 ({{ generationResult.test_cases?.length || 0 }})
      </el-button>
      <el-button v-else type="primary" @click="handleGenerate" :loading="generating">
        {{ generating ? '生成中...' : '开始生成' }}
      </el-button>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

export default {
  name: 'GenerateTestCasesForm',
  props: {
    requirement: {
      type: Object,
      default: null
    }
  },
  emits: ['generate', 'save', 'cancel'],
  setup(props, { emit }) {
    const formRef = ref(null)
    const generating = ref(false)
    const generationProgress = ref(0)
    const generationStatus = ref('')
    const currentStep = ref(0)
    const generationLog = ref([])
    const generationResult = ref(null)

    // 表单数据
    const form = reactive({
      ai_model: 'gpt-4',
      test_count: 5,
      complexity_level: 'medium',
      test_types: ['functional', 'boundary'],
      test_scenarios: ['normal_flow', 'error_handling'],
      sql_operations: ['select', 'insert'],
      data_scale: 'medium',
      mongo_operations: ['find', 'insert'],
      document_structure: 'simple',
      http_methods: ['GET', 'POST'],
      response_formats: ['json'],
      priority_weight: 3,
      coverage_requirement: 80,
      custom_prompt: '',
      test_data_mode: 'auto',
      test_data: '',
      output_formats: ['structured'],
      include_content: ['test_steps', 'expected_results', 'test_data_examples']
    })

    // 表单验证规则
    const rules = {
      ai_model: [
        { required: true, message: '请选择AI模型', trigger: 'change' }
      ],
      test_count: [
        { required: true, message: '请输入生成数量', trigger: 'blur' },
        { type: 'number', min: 1, max: 20, message: '数量范围1-20', trigger: 'blur' }
      ],
      complexity_level: [
        { required: true, message: '请选择复杂度级别', trigger: 'change' }
      ],
      test_types: [
        { type: 'array', min: 1, message: '请至少选择一种测试类型', trigger: 'change' }
      ]
    }

    // 工具函数
    const getTypeLabel = (type) => {
      const mapping = {
        functional: '功能需求',
        non_functional: '非功能需求',
        test_requirement: '测试需求',
        business_rule: '业务规则'
      }
      return mapping[type] || type
    }

    const getProtocolLabel = (protocol) => {
      const mapping = {
        mysql: 'MySQL',
        postgresql: 'PostgreSQL',
        mongodb: 'MongoDB',
        redis: 'Redis',
        http: 'HTTP',
        websocket: 'WebSocket'
      }
      return mapping[protocol] || protocol
    }

    const getPriorityLabel = (priority) => {
      const mapping = {
        low: '低',
        medium: '中',
        high: '高'
      }
      return mapping[priority] || priority
    }

    const isDatabase = (protocol) => {
      return ['mysql', 'postgresql'].includes(protocol)
    }

    // 模拟生成进度
    const simulateGeneration = () => {
      const steps = [
        { step: 0, progress: 20, message: '分析需求内容和结构...' },
        { step: 1, progress: 40, message: '设计测试用例框架...' },
        { step: 2, progress: 60, message: '生成测试内容...' },
        { step: 3, progress: 80, message: '进行质量检查...' },
        { step: 4, progress: 100, message: '格式化输出完成' }
      ]

      let index = 0
      const interval = setInterval(() => {
        if (index < steps.length) {
          const step = steps[index]
          currentStep.value = step.step
          generationProgress.value = step.progress
          
          generationLog.value.push({
            timestamp: new Date().toLocaleTimeString(),
            type: 'info',
            message: step.message
          })

          if (step.progress === 100) {
            generationStatus.value = 'success'
            // 模拟生成结果
            generationResult.value = {
              statistics: {
                total_cases: form.test_count,
                functional_cases: Math.ceil(form.test_count * 0.6),
                boundary_cases: Math.ceil(form.test_count * 0.3),
                exception_cases: Math.ceil(form.test_count * 0.1)
              },
              test_cases: Array.from({ length: form.test_count }, (_, i) => ({
                id: i + 1,
                name: `测试用例 ${i + 1}`,
                type: ['功能测试', '边界测试', '异常测试'][i % 3],
                priority: ['高', '中', '低'][i % 3],
                description: `基于需求"${props.requirement?.title}"生成的测试用例`
              }))
            }
            generating.value = false
            clearInterval(interval)
          }
          index++
        }
      }, 1500)
    }

    // 开始生成
    const handleGenerate = async () => {
      try {
        await formRef.value.validate()
        
        generating.value = true
        generationProgress.value = 0
        generationStatus.value = ''
        currentStep.value = 0
        generationLog.value = []
        generationResult.value = null

        // 开始模拟生成
        simulateGeneration()
        
        // 发送生成请求
        emit('generate', {
          requirement: props.requirement,
          config: { ...form }
        })
      } catch (error) {
        ElMessage.error('请检查表单填写是否正确')
      }
    }

    // 保存测试用例
    const handleSave = async () => {
      try {
        await ElMessageBox.confirm(
          `确定要保存 ${generationResult.value.test_cases?.length || 0} 个生成的测试用例吗？`,
          '确认保存',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'info'
          }
        )
        
        emit('save', {
          requirement: props.requirement,
          test_cases: generationResult.value.test_cases,
          config: { ...form }
        })
      } catch {
        // 用户取消
      }
    }

    // 取消
    const handleCancel = () => {
      emit('cancel')
    }

    return {
      formRef,
      generating,
      generationProgress,
      generationStatus,
      currentStep,
      generationLog,
      generationResult,
      form,
      rules,
      getTypeLabel,
      getProtocolLabel,
      getPriorityLabel,
      isDatabase,
      handleGenerate,
      handleSave,
      handleCancel
    }
  }
}
</script>

<style scoped>
.generate-test-cases-form {
  max-height: 80vh;
  overflow-y: auto;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.generation-progress, .generation-result {
  margin-top: 20px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.generation-log {
  margin-top: 15px;
}

.log-item {
  padding: 5px 0;
  font-size: 12px;
  color: #606266;
}

.log-item .el-tag {
  margin-right: 8px;
}

.el-statistic {
  text-align: center;
}

.el-form-item {
  margin-bottom: 16px;
}

.el-divider {
  margin: 20px 0 15px 0;
}

.el-card {
  margin-bottom: 20px;
}

.el-slider {
  margin: 0 20px;
}

.el-checkbox-group .el-checkbox {
  margin-right: 15px;
  margin-bottom: 8px;
}
</style>

<template>
  <div class="document-parse-form">
    <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
      <el-alert
        title="协议文档解析"
        description="上传协议规范文档，系统将使用AI自动提取和解析其中的测试需求"
        type="info"
        show-icon
        :closable="false"
        style="margin-bottom: 20px"
      />

      <el-form-item label="协议类型" prop="protocol_type">
        <el-select v-model="form.protocol_type" placeholder="选择协议类型" style="width: 100%">
          <el-option
            v-for="protocol in protocolTypes"
            :key="protocol.value"
            :label="protocol.label"
            :value="protocol.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="协议版本">
        <el-input v-model="form.protocol_version" placeholder="如：v1.0, 8.0, latest" />
      </el-form-item>

      <el-form-item label="解析方式" prop="parse_mode">
        <el-radio-group v-model="form.parse_mode">
          <el-radio label="file_upload">文件上传</el-radio>
          <el-radio label="url_fetch">URL获取</el-radio>
          <el-radio label="text_input">文本输入</el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 文件上传模式 -->
      <div v-if="form.parse_mode === 'file_upload'">
        <el-form-item label="文档文件" prop="document_file">
          <el-upload
            ref="uploadRef"
            :auto-upload="false"
            :limit="1"
            :on-change="handleFileChange"
            :on-remove="handleFileRemove"
            accept=".pdf,.doc,.docx,.txt,.md"
            drag
          >
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">
              拖拽文件到此处或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                支持 PDF, DOC, DOCX, TXT, MD 格式，文件大小不超过 10MB
              </div>
            </template>
          </el-upload>
        </el-form-item>

        <el-form-item label="解析选项">
          <el-checkbox-group v-model="form.parse_options">
            <el-checkbox label="extract_tables">提取表格</el-checkbox>
            <el-checkbox label="extract_images">提取图片描述</el-checkbox>
            <el-checkbox label="preserve_formatting">保留格式</el-checkbox>
            <el-checkbox label="ocr_enabled">启用OCR识别</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </div>

      <!-- URL获取模式 -->
      <div v-if="form.parse_mode === 'url_fetch'">
        <el-form-item label="文档URL" prop="document_url">
          <el-input
            v-model="form.document_url"
            placeholder="输入协议文档的在线地址"
            type="url"
          />
        </el-form-item>

        <el-form-item label="访问配置">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-input
                v-model="form.headers.authorization"
                placeholder="Authorization (可选)"
                prepend="Auth"
              />
            </el-col>
            <el-col :span="12">
              <el-input
                v-model="form.headers.user_agent"
                placeholder="User-Agent (可选)"
                prepend="UA"
              />
            </el-col>
          </el-row>
        </el-form-item>
      </div>

      <!-- 文本输入模式 -->
      <div v-if="form.parse_mode === 'text_input'">
        <el-form-item label="协议文档" prop="document_text">
          <el-input
            v-model="form.document_text"
            type="textarea"
            :rows="12"
            placeholder="请粘贴协议规范文档内容..."
            maxlength="50000"
            show-word-limit
          />
        </el-form-item>
      </div>

      <!-- 解析配置 -->
      <el-divider content-position="left">解析配置</el-divider>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="目标章节">
            <el-input
              v-model="form.target_sections"
              placeholder="如：第3章,第4章"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="关键词过滤">
            <el-input
              v-model="form.keywords"
              placeholder="用逗号分隔关键词"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="需求类型">
            <el-select v-model="form.requirement_types" multiple placeholder="选择需求类型">
              <el-option
                v-for="type in requirementTypes"
                :key="type.value"
                :label="type.label"
                :value="type.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="解析粒度">
            <el-select v-model="form.granularity" placeholder="选择粒度">
              <el-option label="粗粒度" value="coarse" />
              <el-option label="中等粒度" value="medium" />
              <el-option label="细粒度" value="fine" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="优先级阈值">
            <el-select v-model="form.priority_threshold">
              <el-option label="低" value="low" />
              <el-option label="中" value="medium" />
              <el-option label="高" value="high" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="自动分类">
            <el-switch v-model="form.auto_categorize" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="生成测试用例">
            <el-switch v-model="form.generate_test_cases" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="解析提示">
        <el-input
          v-model="form.custom_prompt"
          type="textarea"
          :rows="3"
          placeholder="自定义解析提示词，用于指导AI提取特定类型的需求..."
        />
      </el-form-item>

      <!-- 预览和配置 -->
      <el-divider content-position="left">高级配置</el-divider>

      <el-form-item label="输出格式">
        <el-checkbox-group v-model="form.output_formats">
          <el-checkbox label="json">JSON格式</el-checkbox>
          <el-checkbox label="markdown">Markdown格式</el-checkbox>
          <el-checkbox label="excel">Excel格式</el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <el-form-item label="后处理">
        <el-checkbox-group v-model="form.post_processing">
          <el-checkbox label="duplicate_removal">去重处理</el-checkbox>
          <el-checkbox label="similarity_merge">相似合并</el-checkbox>
          <el-checkbox label="hierarchy_organize">层次整理</el-checkbox>
          <el-checkbox label="cross_reference">交叉引用</el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <el-form-item label="质量检查">
        <el-checkbox-group v-model="form.quality_checks">
          <el-checkbox label="completeness">完整性检查</el-checkbox>
          <el-checkbox label="consistency">一致性检查</el-checkbox>
          <el-checkbox label="testability">可测试性评估</el-checkbox>
          <el-checkbox label="traceability">可追溯性验证</el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <!-- 解析进度 -->
      <div v-if="parsing" class="parse-progress">
        <el-divider content-position="center">解析进度</el-divider>
        <el-progress
          :percentage="parseProgress"
          :status="parseStatus"
        />
        <el-steps :active="currentStep" finish-status="success" style="margin-top: 20px">
          <el-step title="文档预处理" />
          <el-step title="内容提取" />
          <el-step title="需求识别" />
          <el-step title="结构化处理" />
          <el-step title="质量检查" />
        </el-steps>
        <div class="parse-log" v-if="parseLog.length > 0">
          <h4>解析日志：</h4>
          <el-scrollbar height="150px">
            <div v-for="(log, index) in parseLog" :key="index" class="log-item">
              <el-tag :type="log.type" size="small">{{ log.timestamp }}</el-tag>
              {{ log.message }}
            </div>
          </el-scrollbar>
        </div>
      </div>

      <!-- 解析结果预览 -->
      <div v-if="parseResult && !parsing" class="parse-result">
        <el-divider content-position="center">解析结果预览</el-divider>
        <el-alert
          :title="`成功解析 ${parseResult.requirements?.length || 0} 个需求`"
          type="success"
          show-icon
          :closable="false"
          style="margin-bottom: 15px"
        />
        
        <el-collapse>
          <el-collapse-item title="解析统计" name="stats">
            <el-row :gutter="20">
              <el-col :span="6">
                <el-statistic title="总需求数" :value="parseResult.statistics?.total_requirements || 0" />
              </el-col>
              <el-col :span="6">
                <el-statistic title="功能需求" :value="parseResult.statistics?.functional_requirements || 0" />
              </el-col>
              <el-col :span="6">
                <el-statistic title="非功能需求" :value="parseResult.statistics?.non_functional_requirements || 0" />
              </el-col>
              <el-col :span="6">
                <el-statistic title="测试需求" :value="parseResult.statistics?.test_requirements || 0" />
              </el-col>
            </el-row>
          </el-collapse-item>
          
          <el-collapse-item title="需求预览" name="preview">
            <el-table :data="parseResult.requirements?.slice(0, 5)" style="width: 100%">
              <el-table-column prop="title" label="标题" width="300" show-overflow-tooltip />
              <el-table-column prop="requirement_type" label="类型" width="120" />
              <el-table-column prop="priority" label="优先级" width="100" />
              <el-table-column prop="description" label="描述" show-overflow-tooltip />
            </el-table>
            <div v-if="parseResult.requirements?.length > 5" style="text-align: center; margin-top: 10px">
              <el-text type="info">还有 {{ parseResult.requirements.length - 5 }} 个需求...</el-text>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </el-form>

    <div class="form-actions">
      <el-button @click="handleCancel" :disabled="parsing">取消</el-button>
      <el-button v-if="parseResult && !parsing" @click="handleImport" type="success">
        导入需求 ({{ parseResult.requirements?.length || 0 }})
      </el-button>
      <el-button v-else type="primary" @click="handleParse" :loading="parsing">
        {{ parsing ? '解析中...' : '开始解析' }}
      </el-button>
    </div>
  </div>
</template>

<script>
import { ref, reactive, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'

export default {
  name: 'DocumentParseForm',
  components: {
    UploadFilled
  },
  props: {
    protocolTypes: {
      type: Array,
      default: () => []
    },
    requirementTypes: {
      type: Array,
      default: () => []
    }
  },
  emits: ['parse', 'import', 'cancel'],
  setup(props, { emit }) {
    const formRef = ref(null)
    const uploadRef = ref(null)
    const parsing = ref(false)
    const parseProgress = ref(0)
    const parseStatus = ref('')
    const currentStep = ref(0)
    const parseLog = ref([])
    const parseResult = ref(null)

    // 表单数据
    const form = reactive({
      protocol_type: '',
      protocol_version: '',
      parse_mode: 'file_upload',
      document_file: null,
      document_url: '',
      document_text: '',
      headers: {
        authorization: '',
        user_agent: ''
      },
      parse_options: ['extract_tables'],
      target_sections: '',
      keywords: '',
      requirement_types: [],
      granularity: 'medium',
      priority_threshold: 'medium',
      auto_categorize: true,
      generate_test_cases: false,
      custom_prompt: '',
      output_formats: ['json'],
      post_processing: ['duplicate_removal'],
      quality_checks: ['completeness', 'testability']
    })

    // 表单验证规则
    const rules = {
      protocol_type: [
        { required: true, message: '请选择协议类型', trigger: 'change' }
      ],
      parse_mode: [
        { required: true, message: '请选择解析方式', trigger: 'change' }
      ],
      document_file: [
        {
          validator: (rule, value, callback) => {
            if (form.parse_mode === 'file_upload' && !form.document_file) {
              callback(new Error('请上传文档文件'))
            } else {
              callback()
            }
          },
          trigger: 'change'
        }
      ],
      document_url: [
        {
          validator: (rule, value, callback) => {
            if (form.parse_mode === 'url_fetch' && !value) {
              callback(new Error('请输入文档URL'))
            } else if (form.parse_mode === 'url_fetch' && value && !/^https?:\/\/.+/.test(value)) {
              callback(new Error('请输入有效的URL地址'))
            } else {
              callback()
            }
          },
          trigger: 'blur'
        }
      ],
      document_text: [
        {
          validator: (rule, value, callback) => {
            if (form.parse_mode === 'text_input' && !value) {
              callback(new Error('请输入文档内容'))
            } else if (form.parse_mode === 'text_input' && value && value.length < 100) {
              callback(new Error('文档内容至少需要100个字符'))
            } else {
              callback()
            }
          },
          trigger: 'blur'
        }
      ]
    }

    // 文件上传处理
    const handleFileChange = (file, fileList) => {
      form.document_file = file.raw
    }

    const handleFileRemove = () => {
      form.document_file = null
    }

    // 模拟解析过程
    const simulateParseProgress = () => {
      const steps = [
        { step: 0, progress: 20, message: '开始文档预处理...' },
        { step: 1, progress: 40, message: '提取文档内容...' },
        { step: 2, progress: 60, message: '识别需求条目...' },
        { step: 3, progress: 80, message: '结构化处理...' },
        { step: 4, progress: 100, message: '质量检查完成' }
      ]

      let index = 0
      const interval = setInterval(() => {
        if (index < steps.length) {
          const step = steps[index]
          currentStep.value = step.step
          parseProgress.value = step.progress
          
          parseLog.value.push({
            timestamp: new Date().toLocaleTimeString(),
            type: 'info',
            message: step.message
          })

          if (step.progress === 100) {
            parseStatus.value = 'success'
            // 模拟解析结果
            parseResult.value = {
              statistics: {
                total_requirements: 15,
                functional_requirements: 8,
                non_functional_requirements: 4,
                test_requirements: 3
              },
              requirements: [
                {
                  title: '用户认证功能',
                  requirement_type: 'functional',
                  priority: 'high',
                  description: '系统应支持用户名密码认证...'
                },
                {
                  title: '响应时间要求',
                  requirement_type: 'non_functional',
                  priority: 'medium',
                  description: '系统响应时间应在2秒内...'
                }
                // ... 更多需求
              ]
            }
            parsing.value = false
            clearInterval(interval)
          }
          index++
        }
      }, 1500)
    }

    // 开始解析
    const handleParse = async () => {
      try {
        await formRef.value.validate()
        
        parsing.value = true
        parseProgress.value = 0
        parseStatus.value = ''
        currentStep.value = 0
        parseLog.value = []
        parseResult.value = null

        // 开始模拟解析
        simulateParseProgress()
        
        // 发送解析请求
        emit('parse', { ...form })
      } catch (error) {
        ElMessage.error('请检查表单填写是否正确')
      }
    }

    // 导入需求
    const handleImport = async () => {
      try {
        await ElMessageBox.confirm(
          `确定要导入 ${parseResult.value.requirements?.length || 0} 个解析出的需求吗？`,
          '确认导入',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'info'
          }
        )
        
        emit('import', parseResult.value)
      } catch {
        // 用户取消
      }
    }

    // 取消
    const handleCancel = () => {
      emit('cancel')
    }

    // 重置解析模式时清空相关数据
    watch(() => form.parse_mode, () => {
      form.document_file = null
      form.document_url = ''
      form.document_text = ''
      parseResult.value = null
      if (uploadRef.value) {
        uploadRef.value.clearFiles()
      }
    })

    return {
      formRef,
      uploadRef,
      parsing,
      parseProgress,
      parseStatus,
      currentStep,
      parseLog,
      parseResult,
      form,
      rules,
      handleFileChange,
      handleFileRemove,
      handleParse,
      handleImport,
      handleCancel
    }
  }
}
</script>

<style scoped>
.document-parse-form {
  max-height: 80vh;
  overflow-y: auto;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.parse-progress, .parse-result {
  margin-top: 20px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.parse-log {
  margin-top: 15px;
}

.log-item {
  padding: 5px 0;
  font-size: 12px;
  color: #606266;
}

.log-item .el-tag {
  margin-right: 8px;
}

.el-statistic {
  text-align: center;
}

.el-upload {
  width: 100%;
}

.el-upload__tip {
  margin-top: 7px;
  color: #606266;
}
</style>

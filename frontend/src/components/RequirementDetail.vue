<template>
  <div class="requirement-detail">
    <el-card class="detail-card" v-if="requirement">
      <!-- 头部信息 -->
      <template #header>
        <div class="card-header">
          <div class="title-section">
            <h3>{{ requirement.title }}</h3>
            <div class="badges">
              <el-tag :type="getTypeTagType(requirement.requirement_type)" size="large">
                {{ getTypeLabel(requirement.requirement_type) }}
              </el-tag>
              <el-tag :type="getPriorityTagType(requirement.priority)" size="large">
                {{ getPriorityLabel(requirement.priority) }}
              </el-tag>
              <el-tag :type="getStatusTagType(requirement.status)" size="large">
                {{ getStatusLabel(requirement.status) }}
              </el-tag>
            </div>
          </div>
          <div class="actions">
            <el-button type="primary" size="small" @click="$emit('edit', requirement)">
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
            <el-button type="success" size="small" @click="$emit('generate-test-cases', requirement)">
              <el-icon><DocumentAdd /></el-icon>
              生成测试用例
            </el-button>
          </div>
        </div>
      </template>

      <!-- 基本信息 -->
      <el-descriptions title="基本信息" :column="2" border>
        <el-descriptions-item label="需求ID">{{ requirement.id }}</el-descriptions-item>
        <el-descriptions-item label="协议类型">
          <el-tag>{{ getProtocolLabel(requirement.protocol_type) }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="协议版本">{{ requirement.protocol_version || '-' }}</el-descriptions-item>
        <el-descriptions-item label="复杂度">
          <el-rate
            v-model="complexityValue"
            disabled
            show-text
            :texts="['简单', '较简单', '中等', '较复杂', '复杂']"
          />
        </el-descriptions-item>
        <el-descriptions-item label="可测试性">
          <el-rate
            v-model="testabilityValue"
            disabled
            show-text
            :texts="['很难', '较难', '一般', '较易', '很易']"
          />
        </el-descriptions-item>
        <el-descriptions-item label="预期测试用例">{{ requirement.expected_test_cases || 0 }}</el-descriptions-item>
        <el-descriptions-item label="分类">{{ requirement.category || '-' }}</el-descriptions-item>
        <el-descriptions-item label="模块">{{ requirement.module || '-' }}</el-descriptions-item>
        <el-descriptions-item label="创建者">{{ requirement.author || '-' }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ formatDate(requirement.created_at) }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ formatDate(requirement.updated_at) }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="getStatusTagType(requirement.status)">
            {{ getStatusLabel(requirement.status) }}
          </el-tag>
        </el-descriptions-item>
      </el-descriptions>

      <!-- 描述信息 -->
      <el-divider content-position="left">详细描述</el-divider>
      <div class="description-section">
        <h4>需求描述</h4>
        <div class="content-box">{{ requirement.description }}</div>
        
        <h4 v-if="requirement.functional_description">功能描述</h4>
        <div v-if="requirement.functional_description" class="content-box">
          {{ requirement.functional_description }}
        </div>
      </div>

      <!-- 测试准则 -->
      <el-divider content-position="left">测试准则</el-divider>
      <el-row :gutter="20" v-if="requirement.test_criteria">
        <el-col :span="12">
          <el-card header="验收标准" shadow="never" class="criteria-card">
            <ul v-if="requirement.test_criteria.acceptance_criteria?.length">
              <li v-for="(criteria, index) in requirement.test_criteria.acceptance_criteria" :key="index">
                {{ criteria }}
              </li>
            </ul>
            <el-empty v-else description="暂无验收标准" :image-size="60" />
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card header="测试场景" shadow="never" class="criteria-card">
            <ul v-if="requirement.test_criteria.test_scenarios?.length">
              <li v-for="(scenario, index) in requirement.test_criteria.test_scenarios" :key="index">
                {{ scenario }}
              </li>
            </ul>
            <el-empty v-else description="暂无测试场景" :image-size="60" />
          </el-card>
        </el-col>
      </el-row>
      
      <el-row :gutter="20" v-if="requirement.test_criteria" style="margin-top: 20px">
        <el-col :span="12">
          <el-card header="边界情况" shadow="never" class="criteria-card">
            <ul v-if="requirement.test_criteria.edge_cases?.length">
              <li v-for="(edge, index) in requirement.test_criteria.edge_cases" :key="index">
                {{ edge }}
              </li>
            </ul>
            <el-empty v-else description="暂无边界情况" :image-size="60" />
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card header="负面测试" shadow="never" class="criteria-card">
            <ul v-if="requirement.test_criteria.negative_cases?.length">
              <li v-for="(negative, index) in requirement.test_criteria.negative_cases" :key="index">
                {{ negative }}
              </li>
            </ul>
            <el-empty v-else description="暂无负面测试" :image-size="60" />
          </el-card>
        </el-col>
      </el-row>

      <!-- 业务规则 -->
      <el-divider content-position="left">业务规则</el-divider>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-card header="业务规则" shadow="never" class="criteria-card">
            <ul v-if="requirement.business_rules?.length">
              <li v-for="(rule, index) in requirement.business_rules" :key="index">
                {{ rule }}
              </li>
            </ul>
            <el-empty v-else description="暂无业务规则" :image-size="60" />
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card header="约束条件" shadow="never" class="criteria-card">
            <ul v-if="requirement.constraints?.length">
              <li v-for="(constraint, index) in requirement.constraints" :key="index">
                {{ constraint }}
              </li>
            </ul>
            <el-empty v-else description="暂无约束条件" :image-size="60" />
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card header="假设条件" shadow="never" class="criteria-card">
            <ul v-if="requirement.assumptions?.length">
              <li v-for="(assumption, index) in requirement.assumptions" :key="index">
                {{ assumption }}
              </li>
            </ul>
            <el-empty v-else description="暂无假设条件" :image-size="60" />
          </el-card>
        </el-col>
      </el-row>

      <!-- 追溯信息 -->
      <el-divider content-position="left">追溯信息</el-divider>
      <el-descriptions :column="2" border v-if="requirement.traceability">
        <el-descriptions-item label="源文档">{{ requirement.traceability.source_document || '-' }}</el-descriptions-item>
        <el-descriptions-item label="章节">{{ requirement.traceability.source_section || '-' }}</el-descriptions-item>
        <el-descriptions-item label="页码">{{ requirement.traceability.source_page || '-' }}</el-descriptions-item>
        <el-descriptions-item label="提取方法">
          <el-tag :type="getExtractionMethodType(requirement.traceability.extraction_method)">
            {{ getExtractionMethodLabel(requirement.traceability.extraction_method) }}
          </el-tag>
        </el-descriptions-item>
      </el-descriptions>

      <!-- 标签和相关信息 -->
      <el-divider content-position="left">其他信息</el-divider>
      <el-row :gutter="20">
        <el-col :span="12">
          <h4>标签</h4>
          <div class="tags-section">
            <el-tag
              v-for="tag in requirement.tags"
              :key="tag"
              type="info"
              effect="plain"
              style="margin-right: 8px; margin-bottom: 8px"
            >
              {{ tag }}
            </el-tag>
            <span v-if="!requirement.tags?.length" class="empty-text">暂无标签</span>
          </div>
        </el-col>
        <el-col :span="12">
          <h4>利益相关者</h4>
          <div class="stakeholders-section">
            <el-tag
              v-for="stakeholder in requirement.stakeholders"
              :key="stakeholder"
              type="success"
              effect="plain"
              style="margin-right: 8px; margin-bottom: 8px"
            >
              {{ stakeholder }}
            </el-tag>
            <span v-if="!requirement.stakeholders?.length" class="empty-text">暂无利益相关者</span>
          </div>
        </el-col>
      </el-row>

      <!-- 参考资料 -->
      <div v-if="requirement.references?.length || requirement.related_documents?.length" style="margin-top: 20px">
        <el-row :gutter="20">
          <el-col :span="12" v-if="requirement.references?.length">
            <h4>参考资料</h4>
            <ul class="reference-list">
              <li v-for="(reference, index) in requirement.references" :key="index">
                <el-link :href="reference" target="_blank" v-if="isUrl(reference)">{{ reference }}</el-link>
                <span v-else>{{ reference }}</span>
              </li>
            </ul>
          </el-col>
          <el-col :span="12" v-if="requirement.related_documents?.length">
            <h4>相关文档</h4>
            <ul class="reference-list">
              <li v-for="(document, index) in requirement.related_documents" :key="index">
                {{ document }}
              </li>
            </ul>
          </el-col>
        </el-row>
      </div>

      <!-- 备注 -->
      <div v-if="requirement.notes" style="margin-top: 20px">
        <h4>备注</h4>
        <div class="content-box">{{ requirement.notes }}</div>
      </div>

      <!-- 关联的测试用例 -->
      <el-divider content-position="left">关联测试用例</el-divider>
      <div class="related-test-cases">
        <el-table :data="relatedTestCases" style="width: 100%" v-loading="loadingTestCases">
          <el-table-column prop="name" label="测试用例名称" width="300" show-overflow-tooltip />
          <el-table-column prop="type" label="类型" width="120">
            <template #default="scope">
              <el-tag size="small">{{ scope.row.type }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="getTestCaseStatusType(scope.row.status)" size="small">
                {{ scope.row.status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="created_at" label="创建时间" width="180">
            <template #default="scope">
              {{ formatDate(scope.row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120">
            <template #default="scope">
              <el-button type="text" size="small" @click="viewTestCase(scope.row)">查看</el-button>
              <el-button type="text" size="small" @click="editTestCase(scope.row)">编辑</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div v-if="!relatedTestCases.length && !loadingTestCases" class="empty-test-cases">
          <el-empty description="暂无关联的测试用例">
            <el-button type="primary" @click="$emit('generate-test-cases', requirement)">
              生成测试用例
            </el-button>
          </el-empty>
        </div>
      </div>
    </el-card>

    <el-empty v-else description="暂无需求详情" />
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { Edit, DocumentAdd } from '@element-plus/icons-vue'

export default {
  name: 'RequirementDetail',
  components: {
    Edit,
    DocumentAdd
  },
  props: {
    requirement: {
      type: Object,
      default: null
    }
  },
  emits: ['edit', 'generate-test-cases'],
  setup(props, { emit }) {
    const relatedTestCases = ref([])
    const loadingTestCases = ref(false)

    // 计算复杂度和可测试性的星级值
    const complexityValue = computed(() => {
      const mapping = { low: 1, medium: 3, high: 5 }
      return mapping[props.requirement?.complexity] || 3
    })

    const testabilityValue = computed(() => {
      const mapping = { low: 1, medium: 3, high: 5 }
      return mapping[props.requirement?.testability] || 3
    })

    // 获取标签类型
    const getTypeTagType = (type) => {
      const mapping = {
        functional: 'primary',
        non_functional: 'warning',
        test_requirement: 'success',
        business_rule: 'info'
      }
      return mapping[type] || ''
    }

    const getTypeLabel = (type) => {
      const mapping = {
        functional: '功能需求',
        non_functional: '非功能需求',
        test_requirement: '测试需求',
        business_rule: '业务规则'
      }
      return mapping[type] || type
    }

    const getPriorityTagType = (priority) => {
      const mapping = {
        low: 'info',
        medium: 'warning',
        high: 'danger'
      }
      return mapping[priority] || ''
    }

    const getPriorityLabel = (priority) => {
      const mapping = {
        low: '低',
        medium: '中',
        high: '高'
      }
      return mapping[priority] || priority
    }

    const getStatusTagType = (status) => {
      const mapping = {
        draft: 'info',
        review: 'warning',
        approved: 'success',
        rejected: 'danger',
        implemented: 'primary'
      }
      return mapping[status] || ''
    }

    const getStatusLabel = (status) => {
      const mapping = {
        draft: '草稿',
        review: '审查中',
        approved: '已批准',
        rejected: '已拒绝',
        implemented: '已实现'
      }
      return mapping[status] || status
    }

    const getProtocolLabel = (protocol) => {
      const mapping = {
        mysql: 'MySQL',
        postgresql: 'PostgreSQL',
        mongodb: 'MongoDB',
        redis: 'Redis',
        http: 'HTTP',
        websocket: 'WebSocket'
      }
      return mapping[protocol] || protocol
    }

    const getExtractionMethodType = (method) => {
      const mapping = {
        manual: 'info',
        ai_parsed: 'success',
        auto_imported: 'warning'
      }
      return mapping[method] || ''
    }

    const getExtractionMethodLabel = (method) => {
      const mapping = {
        manual: '手动提取',
        ai_parsed: 'AI解析',
        auto_imported: '自动导入'
      }
      return mapping[method] || method
    }

    const getTestCaseStatusType = (status) => {
      const mapping = {
        draft: 'info',
        ready: 'warning',
        passed: 'success',
        failed: 'danger',
        blocked: 'warning'
      }
      return mapping[status] || ''
    }

    // 工具函数
    const formatDate = (date) => {
      if (!date) return '-'
      return new Date(date).toLocaleString('zh-CN')
    }

    const isUrl = (str) => {
      try {
        new URL(str)
        return true
      } catch {
        return false
      }
    }

    // 模拟加载关联测试用例
    const loadRelatedTestCases = async () => {
      if (!props.requirement?.id) return
      
      loadingTestCases.value = true
      try {
        // 模拟API调用
        setTimeout(() => {
          relatedTestCases.value = [
            {
              id: 1,
              name: '用户认证功能测试',
              type: '功能测试',
              status: 'passed',
              created_at: '2024-01-15T10:30:00'
            },
            {
              id: 2,
              name: '性能压力测试',
              type: '性能测试',
              status: 'failed',
              created_at: '2024-01-16T14:20:00'
            }
          ]
          loadingTestCases.value = false
        }, 1000)
      } catch (error) {
        loadingTestCases.value = false
      }
    }

    const viewTestCase = (testCase) => {
      // 查看测试用例详情
      console.log('查看测试用例:', testCase)
    }

    const editTestCase = (testCase) => {
      // 编辑测试用例
      console.log('编辑测试用例:', testCase)
    }

    // 生命周期
    onMounted(() => {
      loadRelatedTestCases()
    })

    return {
      relatedTestCases,
      loadingTestCases,
      complexityValue,
      testabilityValue,
      getTypeTagType,
      getTypeLabel,
      getPriorityTagType,
      getPriorityLabel,
      getStatusTagType,
      getStatusLabel,
      getProtocolLabel,
      getExtractionMethodType,
      getExtractionMethodLabel,
      getTestCaseStatusType,
      formatDate,
      isUrl,
      viewTestCase,
      editTestCase
    }
  }
}
</script>

<style scoped>
.requirement-detail {
  height: 100%;
  overflow-y: auto;
}

.detail-card {
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.title-section h3 {
  margin: 0 0 10px 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.badges .el-tag {
  margin-right: 8px;
}

.actions {
  display: flex;
  gap: 8px;
}

.description-section {
  margin: 20px 0;
}

.description-section h4 {
  margin: 15px 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #606266;
}

.content-box {
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
  line-height: 1.6;
  color: #606266;
  white-space: pre-wrap;
}

.criteria-card {
  height: 200px;
  overflow-y: auto;
}

.criteria-card ul {
  margin: 0;
  padding-left: 20px;
}

.criteria-card li {
  margin-bottom: 8px;
  line-height: 1.5;
}

.tags-section, .stakeholders-section {
  min-height: 32px;
  line-height: 32px;
}

.empty-text {
  color: #c0c4cc;
  font-style: italic;
}

.reference-list {
  margin: 0;
  padding-left: 20px;
}

.reference-list li {
  margin-bottom: 8px;
  line-height: 1.5;
}

.related-test-cases {
  margin-top: 20px;
}

.empty-test-cases {
  text-align: center;
  padding: 40px;
}

.el-descriptions {
  margin: 20px 0;
}

.el-divider {
  margin: 30px 0 20px 0;
}
</style>

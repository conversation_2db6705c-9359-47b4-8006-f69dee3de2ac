<template>
  <el-dialog
    v-model="dialogVisible"
    title="最新执行记录"
    width="800px"
    :before-close="handleClose"
  >
    <div v-if="executionRecord && executionRecord.executionData" class="execution-record-content">
      <!-- 执行结果统计 -->
      <div class="execution-stats">
        <div class="stats-title">执行结果</div>
        <div class="stats-grid">
          <div class="stat-card total">
            <div class="stat-number">{{ getTotalSteps }}</div>
            <div class="stat-label">总步骤</div>
          </div>
          <div class="stat-card success">
            <div class="stat-number">{{ getSuccessSteps }}</div>
            <div class="stat-label">成功步骤</div>
          </div>
          <div class="stat-card failed">
            <div class="stat-number">{{ getFailedSteps }}</div>
            <div class="stat-label">失败步骤</div>
          </div>
          <div class="stat-card success-rate">
            <div class="stat-number">{{ getSuccessRate }}%</div>
            <div class="stat-label">成功率</div>
          </div>
        </div>
      </div>



      <!-- 详细执行信息 -->
      <div class="execution-details">
        <div class="section-title">详细执行信息</div>
        <div v-if="executionSteps.length > 0">
          <div v-for="(step, index) in executionSteps" :key="`step-${index}`">
            <div
              class="detail-item clickable"
              :class="{ 'success': step && step.success, 'failed': step && !step.success }"
              @click="toggleStepDetails(index)"
            >
              <div class="step-main-info">
                <div class="step-header">
                  <el-icon><VideoPlay /></el-icon>
                  <span class="detail-text">
                    步骤 {{ step && step.step_number || (index + 1) }}: {{ step && step.action || '执行SQL' }}
                  </span>
                  <span class="step-status" :class="{ 'success': step && step.success, 'failed': step && !step.success }">
                    {{ step && step.success ? '成功' : '失败' }}
                  </span>
                </div>

                <!-- 步骤概览信息 -->
                <div class="step-summary">
                  <div class="summary-item" v-if="step && step.sql_results && step.sql_results.length > 0">
                    <el-icon class="summary-icon"><Document /></el-icon>
                    <span>{{ step.sql_results.length }} 条SQL语句</span>
                  </div>
                  <div class="summary-item" v-if="step && step.capture_file">
                    <el-icon class="summary-icon"><Camera /></el-icon>
                    <span>{{ getFileName(step.capture_file) }}</span>
                  </div>
                  <div class="summary-item" v-if="getStepSqlPreview(step)">
                    <span class="sql-preview">{{ getStepSqlPreview(step) }}</span>
                  </div>
                </div>
              </div>

              <el-icon class="expand-icon" :class="{ 'expanded': expandedSteps[index] }"><ArrowRight /></el-icon>
            </div>

            <!-- 步骤详细信息 -->
            <div
              v-if="expandedSteps[index]"
              class="step-details"
            >
              <div class="step-detail-content">
                <div class="detail-section">
                  <h4>执行动作</h4>
                  <p>{{ step?.action || '执行SQL' }}</p>
                </div>

                <div v-if="step && step.sql_results && step.sql_results.length > 0" class="detail-section">
                  <h4>
                    <el-icon><Document /></el-icon>
                    SQL执行详情 ({{ step.sql_results.length }} 条语句)
                  </h4>
                  <div v-for="(sqlResult, sqlIndex) in step.sql_results" :key="sqlIndex" class="sql-result">
                    <div class="sql-header">
                      <span class="sql-index">SQL {{ sqlIndex + 1 }}</span>
                      <span class="sql-type" :class="getSqlTypeClass(sqlResult && sqlResult.sql)">
                        {{ getSqlType(sqlResult && sqlResult.sql) }}
                      </span>
                    </div>
                    <div class="sql-statement">
                      {{ sqlResult && sqlResult.sql }}
                    </div>
                    <div class="sql-result-data">
                      <div class="result-header">
                        <el-icon><Check /></el-icon>
                        <strong>执行结果:</strong>
                      </div>
                      <div v-if="sqlResult && sqlResult.result && sqlResult.result.type === 'query' && sqlResult.result.data">
                        <div class="result-summary">
                          <el-icon><DataBoard /></el-icon>
                          查询返回 {{ sqlResult.result.row_count || 0 }} 行数据
                        </div>
                        <div v-if="sqlResult.result.data && sqlResult.result.data.length > 0" class="data-preview">
                          <div class="data-table">
                            <div class="data-row header-row" v-if="sqlResult.result.data.length > 0 && sqlResult.result.data[0]">
                              <div class="data-cell" v-for="(key, keyIndex) in Object.keys(sqlResult.result.data[0] || {})" :key="keyIndex">
                                {{ key }}
                              </div>
                            </div>
                            <div class="data-row" v-for="(row, rowIndex) in (sqlResult.result.data || []).slice(0, 3)" :key="rowIndex">
                              <div class="data-cell" v-for="(value, key) in row" :key="key">
                                {{ value }}
                              </div>
                            </div>
                          </div>
                          <div v-if="sqlResult.result.data && sqlResult.result.data.length > 3" class="more-data">
                            <el-icon><MoreFilled /></el-icon>
                            还有 {{ sqlResult.result.data.length - 3 }} 行数据未显示
                          </div>
                        </div>
                      </div>
                      <div v-else-if="sqlResult && sqlResult.result" class="simple-result">
                        <el-icon><Check /></el-icon>
                        {{ sqlResult.result.message || '执行成功' }}
                      </div>
                    </div>
                  </div>
                </div>

                <div v-if="step && step.capture_file" class="detail-section">
                  <h4>
                    <el-icon><Camera /></el-icon>
                    网络抓包文件
                  </h4>
                  <div class="capture-file-detail">
                    <div class="capture-info">
                      <div class="capture-filename">
                        <el-icon><Document /></el-icon>
                        <span>{{ getFileName(step.capture_file) }}</span>
                      </div>
                      <div class="capture-description">
                        包含此步骤执行过程中的所有网络通信数据
                      </div>
                    </div>
                    <a
                      :href="`/api/captures/download/${encodeURIComponent(getFileName(step.capture_file))}`"
                      target="_blank"
                      class="download-button"
                    >
                      <el-icon><Download /></el-icon>
                      下载抓包文件
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-else class="detail-item">
          <el-icon><VideoPlay /></el-icon>
          <span class="detail-text">{{ executionRecord?.testCase?.title || '测试用例' }}</span>
          <el-icon class="expand-icon"><ArrowRight /></el-icon>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { Document, VideoPlay, ArrowRight, Camera, Check, DataBoard, MoreFilled, Download } from '@element-plus/icons-vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  executionRecord: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:visible'])

// 步骤展开状态
const expandedSteps = ref({})

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const handleClose = () => {
  dialogVisible.value = false
}

// 切换步骤详情展开/收起
const toggleStepDetails = (index) => {
  expandedSteps.value[index] = !expandedSteps.value[index]
}

const getFileName = (filePath) => {
  if (!filePath) return ''
  return filePath.split('/').pop()
}

// 计算总步骤数
const getTotalSteps = computed(() => {
  if (!props.executionRecord?.executionData) return 0

  // 优先使用execution_details中的数据
  if (props.executionRecord.executionData.execution_details) {
    const details = props.executionRecord.executionData.execution_details
    if (details.summary?.total_steps) {
      return details.summary.total_steps
    }
    if (details.execution_results) {
      return details.execution_results.length
    }
  }

  // 如果有summary数据，使用summary中的total_steps
  if (props.executionRecord.executionData.summary?.total_steps) {
    return props.executionRecord.executionData.summary.total_steps
  }

  // 如果有execution_results数组，使用数组长度
  if (props.executionRecord.executionData.execution_results) {
    return props.executionRecord.executionData.execution_results.length
  }

  // 对于简化的执行记录，返回1
  return 1
})

// 计算成功步骤数
const getSuccessSteps = computed(() => {
  if (!props.executionRecord?.executionData) return 0

  // 优先使用execution_details中的数据
  if (props.executionRecord.executionData.execution_details) {
    const details = props.executionRecord.executionData.execution_details
    if (details.summary?.successful_steps !== undefined) {
      return details.summary.successful_steps
    }
    if (details.execution_results) {
      return details.execution_results.filter(result => result && result.success).length
    }
  }

  // 如果有summary数据，使用summary中的successful_steps
  if (props.executionRecord.executionData.summary?.successful_steps !== undefined) {
    return props.executionRecord.executionData.summary.successful_steps
  }

  // 如果有execution_results数组，计算成功的步骤数
  if (props.executionRecord.executionData.execution_results) {
    return props.executionRecord.executionData.execution_results.filter(result => result && result.success).length
  }

  // 对于简化的执行记录，根据execution_result判断
  if (props.executionRecord.executionData.execution_result === 'pass') {
    return 1
  }

  return 0
})

// 计算失败步骤数
const getFailedSteps = computed(() => {
  if (!props.executionRecord?.executionData) return 0

  // 优先使用execution_details中的数据
  if (props.executionRecord.executionData.execution_details) {
    const details = props.executionRecord.executionData.execution_details
    if (details.summary?.failed_steps !== undefined) {
      return details.summary.failed_steps
    }
    if (details.execution_results) {
      return details.execution_results.filter(result => result && !result.success).length
    }
  }

  // 如果有summary数据，使用summary中的failed_steps
  if (props.executionRecord.executionData.summary?.failed_steps !== undefined) {
    return props.executionRecord.executionData.summary.failed_steps
  }

  // 如果有execution_results数组，计算失败的步骤数
  if (props.executionRecord.executionData.execution_results) {
    return props.executionRecord.executionData.execution_results.filter(result => result && !result.success).length
  }

  // 否则根据单个执行结果判断
  return props.executionRecord.executionData.execution_result === 'fail' ? 1 : 0
})

// 计算成功率
const getSuccessRate = computed(() => {
  if (!props.executionRecord?.executionData) return 0

  // 优先使用execution_details中的数据
  if (props.executionRecord.executionData.execution_details) {
    const details = props.executionRecord.executionData.execution_details
    if (details.summary?.success_rate !== undefined) {
      return Math.round(details.summary.success_rate * 100)
    }
    if (details.success_rate !== undefined) {
      return Math.round(details.success_rate)
    }
  }

  // 如果有summary数据，使用summary中的success_rate
  if (props.executionRecord.executionData.summary?.success_rate !== undefined) {
    return Math.round(props.executionRecord.executionData.summary.success_rate * 100)
  }

  // 如果有success_rate字段，直接使用
  if (props.executionRecord.executionData.success_rate !== undefined) {
    return Math.round(props.executionRecord.executionData.success_rate)
  }

  // 否则根据成功和总步骤数计算
  const total = getTotalSteps.value
  const success = getSuccessSteps.value
  return total > 0 ? Math.round((success / total) * 100) : 0
})

// 获取抓包文件列表
const getCaptureFiles = computed(() => {
  if (!props.executionRecord?.executionData) return []

  // 优先使用execution_details中的数据
  if (props.executionRecord.executionData.execution_details) {
    const details = props.executionRecord.executionData.execution_details
    if (details.summary?.capture_files) {
      return details.summary.capture_files
    }
    if (details.capture_files) {
      return details.capture_files
    }
    // 从execution_results中收集capture_file
    if (details.execution_results) {
      const files = []
      details.execution_results.forEach(result => {
        if (result && result.capture_file) {
          files.push(result.capture_file)
        }
      })
      if (files.length > 0) return files
    }
  }

  // 优先使用summary中的capture_files
  if (props.executionRecord.executionData.summary?.capture_files) {
    return props.executionRecord.executionData.summary.capture_files
  }

  // 其次使用根级别的capture_files
  if (props.executionRecord.executionData.capture_files) {
    return props.executionRecord.executionData.capture_files
  }

  // 最后从execution_results中收集capture_file
  if (props.executionRecord.executionData.execution_results) {
    const files = []
    props.executionRecord.executionData.execution_results.forEach(result => {
      if (result && result.capture_file) {
        files.push(result.capture_file)
      }
    })
    return files
  }

  return []
})

// 获取执行步骤列表
const executionSteps = computed(() => {
  if (!props.executionRecord?.executionData) return []

  // 优先使用execution_details中的数据
  if (props.executionRecord.executionData.execution_details) {
    const details = props.executionRecord.executionData.execution_details
    if (details.execution_results) {
      return details.execution_results
    }
  }

  // 如果有execution_results数组，返回执行结果
  if (props.executionRecord.executionData.execution_results) {
    return props.executionRecord.executionData.execution_results
  }

  return []
})

// 获取步骤SQL预览
const getStepSqlPreview = (step) => {
  if (!step || !step.sql_results || step.sql_results.length === 0) return ''

  // 获取第一条SQL语句作为预览
  const firstSql = step.sql_results[0]?.sql
  if (!firstSql) return ''

  // 截取前50个字符作为预览
  const preview = firstSql.length > 50 ? firstSql.substring(0, 50) + '...' : firstSql
  return preview
}

// 获取SQL类型
const getSqlType = (sql) => {
  if (!sql) return 'UNKNOWN'

  const upperSql = sql.trim().toUpperCase()

  // MongoDB查询识别
  if (upperSql.startsWith('DB.')) {
    if (upperSql.includes('.FIND(') || upperSql.includes('.FINDONE(')) return 'FIND'
    if (upperSql.includes('.INSERTONE(') || upperSql.includes('.INSERTMANY(')) return 'INSERT'
    if (upperSql.includes('.UPDATEONE(') || upperSql.includes('.UPDATEMANY(')) return 'UPDATE'
    if (upperSql.includes('.DELETEONE(') || upperSql.includes('.DELETEMANY(')) return 'DELETE'
    if (upperSql.includes('.DROP(')) return 'DROP'
    if (upperSql.includes('.AGGREGATE(')) return 'AGGREGATE'
    if (upperSql.includes('.COUNT(') || upperSql.includes('.COUNTDOCUMENTS(')) return 'COUNT'
    if (upperSql.includes('.CREATEINDEX(') || upperSql.includes('.DROPINDEX(')) return 'INDEX'
    return 'MONGO'
  }

  // 传统SQL识别
  if (upperSql.startsWith('SELECT')) return 'SELECT'
  if (upperSql.startsWith('INSERT')) return 'INSERT'
  if (upperSql.startsWith('UPDATE')) return 'UPDATE'
  if (upperSql.startsWith('DELETE')) return 'DELETE'
  if (upperSql.startsWith('CREATE')) return 'CREATE'
  if (upperSql.startsWith('DROP')) return 'DROP'
  if (upperSql.startsWith('ALTER')) return 'ALTER'
  if (upperSql.startsWith('TRUNCATE')) return 'TRUNCATE'

  return 'OTHER'
}

// 获取SQL类型样式类
const getSqlTypeClass = (sql) => {
  const type = getSqlType(sql)
  return `sql-type-${type.toLowerCase()}`
}
</script>

<style scoped>
.execution-record-content {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.execution-stats {
  margin-bottom: 24px;
}

.stats-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 16px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
}

.stat-card {
  padding: 20px;
  border-radius: 8px;
  text-align: center;
  border: 1px solid #e4e7ed;
}

.stat-card.total {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.stat-card.success {
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
}

.stat-card.failed {
  background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
}

.stat-card.success-rate {
  background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.capture-files, .execution-details {
  margin-bottom: 24px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 16px;
}

.file-list {
  background: #f5f7fa;
  border-radius: 8px;
  padding: 16px;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 12px;
  background: white;
  border-radius: 6px;
  margin-bottom: 8px;
  border-left: 3px solid #409EFF;
}

.file-item:last-child {
  margin-bottom: 0;
}

.file-icon {
  color: #409EFF;
  margin-right: 12px;
  font-size: 16px;
}

.file-name {
  flex: 1;
  color: #303133;
  font-weight: 500;
}

.download-link {
  color: #409EFF;
  text-decoration: none;
  font-size: 14px;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.download-link:hover {
  background-color: #ecf5ff;
}

.no-files {
  text-align: center;
  color: #909399;
  padding: 40px;
  background: #f5f7fa;
  border-radius: 8px;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  padding: 16px;
  background: #f5f7fa;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.3s;
  margin-bottom: 8px;
}

.step-main-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.step-header {
  display: flex;
  align-items: center;
  gap: 12px;
}

.step-summary {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-left: 24px;
  font-size: 13px;
  color: #606266;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 2px 8px;
  background: #ffffff;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.summary-icon {
  font-size: 12px;
  color: #909399;
}

.sql-preview {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #409eff;
  background: #f0f9ff;
  padding: 2px 6px;
  border-radius: 3px;
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-item:hover {
  background: #ecf5ff;
}

.detail-item.success {
  border-left: 4px solid #67c23a;
}

.detail-item.failed {
  border-left: 4px solid #f56c6c;
}

.detail-text {
  flex: 1;
  margin-left: 12px;
  color: #303133;
  font-weight: 500;
}

.step-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  margin-right: 8px;
}

.step-status.success {
  background: #f0f9ff;
  color: #67c23a;
}

.step-status.failed {
  background: #fef0f0;
  color: #f56c6c;
}

.expand-icon {
  color: #909399;
  transition: transform 0.3s ease;
  margin-left: 8px;
  margin-top: 4px;
  flex-shrink: 0;
}

.expand-icon.expanded {
  transform: rotate(90deg);
}

/* 步骤详情样式 */
.step-details {
  margin-left: 24px;
  margin-bottom: 16px;
  padding: 16px;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  animation: slideDown 0.3s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.step-detail-content {
  font-size: 14px;
}

.detail-section {
  margin-bottom: 16px;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.detail-section h4 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 12px 0;
  font-size: 15px;
  font-weight: 600;
  color: #303133;
  padding-bottom: 8px;
  border-bottom: 2px solid #e4e7ed;
}

.detail-section h4 .el-icon {
  color: #409eff;
  font-size: 16px;
}

.detail-section p {
  margin: 0;
  color: #606266;
  line-height: 1.5;
}

.sql-result {
  margin-bottom: 16px;
  padding: 16px;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.sql-result:last-child {
  margin-bottom: 0;
}

.sql-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.sql-index {
  font-size: 12px;
  font-weight: 600;
  color: #606266;
  background: #f5f7fa;
  padding: 2px 8px;
  border-radius: 4px;
}

.sql-type {
  font-size: 11px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 3px;
  text-transform: uppercase;
}

.sql-type-select {
  background: #e7f4ff;
  color: #409eff;
}

.sql-type-insert {
  background: #f0f9ff;
  color: #67c23a;
}

.sql-type-update {
  background: #fdf6ec;
  color: #e6a23c;
}

.sql-type-delete {
  background: #fef0f0;
  color: #f56c6c;
}

.sql-type-create {
  background: #f4f4f5;
  color: #909399;
}

.sql-type-drop {
  background: #fef0f0;
  color: #f56c6c;
}

.sql-type-find {
  background: #e7f4ff;
  color: #409eff;
}

.sql-type-mongo {
  background: #f0f9ff;
  color: #67c23a;
}

.sql-type-aggregate {
  background: #fdf6ec;
  color: #e6a23c;
}

.sql-type-count {
  background: #f4f4f5;
  color: #909399;
}

.sql-type-index {
  background: #f0f2f5;
  color: #606266;
}

.sql-type-other {
  background: #f4f4f5;
  color: #909399;
}

.sql-statement {
  margin-bottom: 12px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  color: #2c3e50;
  background: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
  border-left: 4px solid #409eff;
  line-height: 1.5;
  white-space: pre-wrap;
  word-break: break-all;
}

.sql-result-data {
  color: #606266;
}

.result-header {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 8px;
  font-weight: 500;
  color: #303133;
}

.result-header .el-icon {
  color: #67c23a;
  font-size: 14px;
}

.result-summary {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 500;
  color: #409eff;
  margin-bottom: 12px;
  padding: 8px 12px;
  background: #f0f9ff;
  border-radius: 6px;
  border: 1px solid #b3d8ff;
}

.result-summary .el-icon {
  font-size: 14px;
}

.data-preview {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
}

.data-table {
  width: 100%;
}

.data-row {
  display: flex;
  border-bottom: 1px solid #e4e7ed;
}

.data-row:last-child {
  border-bottom: none;
}

.data-row.header-row {
  background: #f5f7fa;
  font-weight: 600;
  color: #303133;
}

.data-cell {
  flex: 1;
  padding: 8px 12px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  border-right: 1px solid #e4e7ed;
  word-break: break-all;
  min-width: 0;
}

.data-cell:last-child {
  border-right: none;
}

.more-data {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  color: #909399;
  font-style: italic;
  text-align: center;
  padding: 12px;
  background: #f5f7fa;
  border-top: 1px solid #e4e7ed;
}

.simple-result {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: #f0f9ff;
  border-radius: 6px;
  color: #67c23a;
  font-weight: 500;
}

.capture-file-detail {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background: #f0f9ff;
  border-radius: 8px;
  border: 1px solid #b3d8ff;
}

.capture-info {
  flex: 1;
}

.capture-filename {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
}

.capture-filename .el-icon {
  color: #409eff;
  font-size: 16px;
}

.capture-description {
  font-size: 12px;
  color: #606266;
  margin-left: 24px;
}

.download-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: #409eff;
  color: white;
  text-decoration: none;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  transition: background-color 0.3s;
}

.download-button:hover {
  background: #337ecc;
  text-decoration: none;
}

.download-button .el-icon {
  font-size: 14px;
}

.dialog-footer {
  text-align: right;
}
</style>

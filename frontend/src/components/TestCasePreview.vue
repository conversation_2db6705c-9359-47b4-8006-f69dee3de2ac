<template>
  <div class="test-case-preview">
    <el-descriptions :column="2" border>
      <el-descriptions-item label="用例标题" :span="2">
        <el-tag type="primary">{{ testCase.title || '未设置' }}</el-tag>
      </el-descriptions-item>
      
      <el-descriptions-item label="所属模块">
        {{ testCase.module || '未设置' }}
      </el-descriptions-item>
      
      <el-descriptions-item label="优先级">
        <el-tag :type="getPriorityType(testCase.priority)">
          {{ getPriorityText(testCase.priority) }}
        </el-tag>
      </el-descriptions-item>
      
      <el-descriptions-item label="数据库类型" v-if="testCase.database_type">
        <el-tag type="info">{{ testCase.database_type.toUpperCase() }}</el-tag>
      </el-descriptions-item>
      
      <el-descriptions-item label="自动化程度">
        <el-tag :type="testCase.automation_level === 'auto' ? 'success' : 'warning'">
          {{ testCase.automation_level === 'auto' ? '自动化' : '手工' }}
        </el-tag>
      </el-descriptions-item>
      
      <el-descriptions-item label="前置条件" :span="2" v-if="testCase.preconditions">
        <div class="content-text">{{ testCase.preconditions }}</div>
      </el-descriptions-item>
    </el-descriptions>

    <!-- 测试步骤 -->
    <el-divider content-position="left">
      <el-icon><List /></el-icon>
      测试步骤
    </el-divider>
    
    <div class="test-steps">
      <el-timeline>
        <el-timeline-item
          v-for="(step, index) in testCase.test_steps || []"
          :key="index"
          :timestamp="`步骤 ${step.step_number || index + 1}`"
          placement="top"
        >
          <el-card shadow="never" class="step-card">
            <div class="step-content">
              <div class="step-action">
                <strong>操作:</strong>
                <div class="action-text">{{ step.action || step }}</div>
              </div>
              
              <div class="step-expected" v-if="step.expected_result">
                <strong>预期结果:</strong>
                <div class="expected-text">{{ step.expected_result }}</div>
              </div>
              
              <div class="step-data" v-if="step.test_data">
                <strong>测试数据:</strong>
                <div class="data-text">{{ step.test_data }}</div>
              </div>
            </div>
          </el-card>
        </el-timeline-item>
      </el-timeline>
    </div>

    <!-- 整体预期结果 -->
    <el-divider content-position="left" v-if="testCase.expected_result">
      <el-icon><Check /></el-icon>
      整体预期结果
    </el-divider>
    
    <div class="expected-result" v-if="testCase.expected_result">
      <el-alert
        :title="testCase.expected_result"
        type="success"
        :closable="false"
        show-icon
      />
    </div>

    <!-- 测试数据 -->
    <el-divider content-position="left" v-if="testCase.test_data && typeof testCase.test_data === 'object' && Object.keys(testCase.test_data).length > 0">
      <el-icon><DataBoard /></el-icon>
      测试数据
    </el-divider>

    <div class="test-data" v-if="testCase.test_data && typeof testCase.test_data === 'object' && Object.keys(testCase.test_data).length > 0">
      <el-card shadow="never">
        <pre class="data-json">{{ JSON.stringify(testCase.test_data, null, 2) }}</pre>
      </el-card>
    </div>

    <!-- 标签 -->
    <el-divider content-position="left" v-if="testCase.tags && testCase.tags.length > 0">
      <el-icon><PriceTag /></el-icon>
      标签
    </el-divider>
    
    <div class="test-tags" v-if="testCase.tags && testCase.tags.length > 0">
      <el-tag
        v-for="tag in testCase.tags"
        :key="tag"
        class="tag-item"
        type="info"
        size="small"
      >
        {{ tag }}
      </el-tag>
    </div>

    <!-- 其他信息 -->
    <el-divider content-position="left">
      <el-icon><InfoFilled /></el-icon>
      其他信息
    </el-divider>
    
    <el-descriptions :column="2" size="small">
      <el-descriptions-item label="创建者" v-if="testCase.author">
        {{ testCase.author }}
      </el-descriptions-item>
      
      <el-descriptions-item label="预计时间" v-if="testCase.estimated_time">
        {{ testCase.estimated_time }} 分钟
      </el-descriptions-item>
      
      <el-descriptions-item label="测试环境" v-if="testCase.test_environment">
        {{ testCase.test_environment }}
      </el-descriptions-item>
      
      <el-descriptions-item label="评审状态" v-if="testCase.review_status">
        <el-tag :type="getReviewStatusType(testCase.review_status)">
          {{ getReviewStatusText(testCase.review_status) }}
        </el-tag>
      </el-descriptions-item>
    </el-descriptions>

    <!-- 备注 -->
    <div class="notes" v-if="testCase.notes">
      <el-divider content-position="left">
        <el-icon><Document /></el-icon>
        备注
      </el-divider>
      <div class="content-text">{{ testCase.notes }}</div>
    </div>
  </div>
</template>

<script setup>
import { List, Check, DataBoard, PriceTag, InfoFilled, Document } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  testCase: {
    type: Object,
    required: true,
    default: () => ({})
  }
})

// 获取优先级类型
const getPriorityType = (priority) => {
  switch (priority) {
    case 'critical': return 'danger'
    case 'high': return 'warning'
    case 'medium': return 'primary'
    case 'low': return 'info'
    default: return 'info'
  }
}

// 获取优先级文本
const getPriorityText = (priority) => {
  switch (priority) {
    case 'critical': return '紧急'
    case 'high': return '高'
    case 'medium': return '中'
    case 'low': return '低'
    default: return '未设置'
  }
}

// 获取评审状态类型
const getReviewStatusType = (status) => {
  switch (status) {
    case 'approved': return 'success'
    case 'rejected': return 'danger'
    case 'pending': return 'warning'
    default: return 'info'
  }
}

// 获取评审状态文本
const getReviewStatusText = (status) => {
  switch (status) {
    case 'approved': return '已通过'
    case 'rejected': return '已拒绝'
    case 'pending': return '待评审'
    default: return '未知'
  }
}
</script>

<style scoped>
.test-case-preview {
  padding: 16px 0;
}

.content-text {
  line-height: 1.6;
  color: #606266;
  white-space: pre-wrap;
}

.test-steps {
  margin: 16px 0;
}

.step-card {
  margin-bottom: 8px;
}

.step-content {
  padding: 8px 0;
}

.step-action,
.step-expected,
.step-data {
  margin-bottom: 8px;
}

.step-action strong,
.step-expected strong,
.step-data strong {
  color: #303133;
  display: block;
  margin-bottom: 4px;
}

.action-text,
.expected-text,
.data-text {
  color: #606266;
  line-height: 1.5;
  padding-left: 12px;
  border-left: 3px solid #e4e7ed;
  background-color: #f8f9fa;
  padding: 8px 12px;
  border-radius: 4px;
}

.expected-result {
  margin: 16px 0;
}

.test-data {
  margin: 16px 0;
}

.data-json {
  background-color: #f8f9fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 12px;
  font-size: 12px;
  line-height: 1.4;
  color: #606266;
  overflow-x: auto;
}

.test-tags {
  margin: 16px 0;
}

.tag-item {
  margin-right: 8px;
  margin-bottom: 8px;
}

.notes {
  margin-top: 16px;
}

:deep(.el-timeline-item__timestamp) {
  font-weight: 500;
  color: #409eff;
}

:deep(.el-descriptions__label) {
  font-weight: 500;
}

:deep(.el-divider__text) {
  font-weight: 500;
  color: #303133;
}
</style>

<template>
  <div class="batch-operation-form">
    <el-alert
      :title="`已选择 ${selectedRequirements.length} 个需求`"
      type="info"
      show-icon
      :closable="false"
      style="margin-bottom: 20px"
    />

    <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
      <el-form-item label="操作类型" prop="operation_type">
        <el-select v-model="form.operation_type" placeholder="选择批量操作类型" style="width: 100%">
          <el-option-group label="状态操作">
            <el-option label="批量更新状态" value="update_status" />
            <el-option label="批量审批" value="batch_approve" />
            <el-option label="批量拒绝" value="batch_reject" />
          </el-option-group>
          <el-option-group label="属性操作">
            <el-option label="批量更新优先级" value="update_priority" />
            <el-option label="批量更新分类" value="update_category" />
            <el-option label="批量更新模块" value="update_module" />
            <el-option label="批量添加标签" value="add_tags" />
            <el-option label="批量移除标签" value="remove_tags" />
          </el-option-group>
          <el-option-group label="关联操作">
            <el-option label="批量分配审核者" value="assign_reviewers" />
            <el-option label="批量关联文档" value="link_documents" />
          </el-option-group>
          <el-option-group label="测试操作">
            <el-option label="批量生成测试用例" value="generate_test_cases" />
            <el-option label="批量更新测试准则" value="update_test_criteria" />
          </el-option-group>
          <el-option-group label="数据操作">
            <el-option label="批量导出" value="export_requirements" />
            <el-option label="批量删除" value="delete_requirements" />
          </el-option-group>
        </el-select>
      </el-form-item>

      <!-- 状态更新 -->
      <div v-if="form.operation_type === 'update_status'">
        <el-form-item label="新状态" prop="new_status">
          <el-select v-model="form.new_status" placeholder="选择新状态">
            <el-option
              v-for="status in statuses"
              :key="status.value"
              :label="status.label"
              :value="status.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态说明">
          <el-input v-model="form.status_comment" type="textarea" :rows="3" placeholder="状态变更说明" />
        </el-form-item>
      </div>

      <!-- 优先级更新 -->
      <div v-if="form.operation_type === 'update_priority'">
        <el-form-item label="新优先级" prop="new_priority">
          <el-select v-model="form.new_priority" placeholder="选择新优先级">
            <el-option
              v-for="priority in priorities"
              :key="priority.value"
              :label="priority.label"
              :value="priority.value"
            />
          </el-select>
        </el-form-item>
      </div>

      <!-- 分类更新 -->
      <div v-if="form.operation_type === 'update_category'">
        <el-form-item label="新分类" prop="new_category">
          <el-select v-model="form.new_category" filterable allow-create placeholder="选择或创建分类">
            <el-option
              v-for="category in categories"
              :key="category"
              :label="category"
              :value="category"
            />
          </el-select>
        </el-form-item>
      </div>

      <!-- 模块更新 -->
      <div v-if="form.operation_type === 'update_module'">
        <el-form-item label="新模块" prop="new_module">
          <el-select v-model="form.new_module" filterable allow-create placeholder="选择或创建模块">
            <el-option
              v-for="module in modules"
              :key="module"
              :label="module"
              :value="module"
            />
          </el-select>
        </el-form-item>
      </div>

      <!-- 标签操作 -->
      <div v-if="form.operation_type === 'add_tags' || form.operation_type === 'remove_tags'">
        <el-form-item :label="form.operation_type === 'add_tags' ? '添加标签' : '移除标签'" prop="tags">
          <el-select
            v-model="form.tags"
            multiple
            filterable
            allow-create
            placeholder="选择或创建标签"
            style="width: 100%"
          >
            <el-option
              v-for="tag in availableTags"
              :key="tag"
              :label="tag"
              :value="tag"
            />
          </el-select>
        </el-form-item>
      </div>

      <!-- 审核者分配 -->
      <div v-if="form.operation_type === 'assign_reviewers'">
        <el-form-item label="审核者" prop="reviewers">
          <el-select
            v-model="form.reviewers"
            multiple
            filterable
            placeholder="选择审核者"
            style="width: 100%"
          >
            <el-option
              v-for="reviewer in reviewers"
              :key="reviewer.value"
              :label="reviewer.label"
              :value="reviewer.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="审核截止时间">
          <el-date-picker
            v-model="form.review_deadline"
            type="datetime"
            placeholder="选择审核截止时间"
            style="width: 100%"
          />
        </el-form-item>
      </div>

      <!-- 文档关联 -->
      <div v-if="form.operation_type === 'link_documents'">
        <el-form-item label="关联文档" prop="documents">
          <el-input
            v-model="documentsText"
            type="textarea"
            :rows="4"
            placeholder="每行一个文档链接或文档名称"
          />
        </el-form-item>
      </div>

      <!-- 测试用例生成 -->
      <div v-if="form.operation_type === 'generate_test_cases'">
        <el-form-item label="生成配置">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="AI模型">
                <el-select v-model="form.test_generation_config.ai_model">
                  <el-option label="GPT-4" value="gpt-4" />
                  <el-option label="GPT-3.5" value="gpt-3.5-turbo" />
                  <el-option label="Claude-3" value="claude-3" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="用例类型">
                <el-select v-model="form.test_generation_config.test_types" multiple>
                  <el-option label="功能测试" value="functional" />
                  <el-option label="边界测试" value="boundary" />
                  <el-option label="异常测试" value="exception" />
                  <el-option label="性能测试" value="performance" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="每个需求生成">
                <el-input-number v-model="form.test_generation_config.cases_per_requirement" :min="1" :max="10" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="生成模板">
          <el-input
            v-model="form.test_generation_config.template"
            type="textarea"
            :rows="3"
            placeholder="自定义测试用例生成模板..."
          />
        </el-form-item>
      </div>

      <!-- 测试准则更新 -->
      <div v-if="form.operation_type === 'update_test_criteria'">
        <el-form-item label="更新类型">
          <el-checkbox-group v-model="form.criteria_updates">
            <el-checkbox label="acceptance_criteria">验收标准</el-checkbox>
            <el-checkbox label="test_scenarios">测试场景</el-checkbox>
            <el-checkbox label="edge_cases">边界情况</el-checkbox>
            <el-checkbox label="negative_cases">负面测试</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="批量验收标准" v-if="form.criteria_updates.includes('acceptance_criteria')">
          <el-input
            v-model="form.batch_acceptance_criteria"
            type="textarea"
            :rows="3"
            placeholder="每行一个验收标准，将添加到所选需求中"
          />
        </el-form-item>
        <el-form-item label="批量测试场景" v-if="form.criteria_updates.includes('test_scenarios')">
          <el-input
            v-model="form.batch_test_scenarios"
            type="textarea"
            :rows="3"
            placeholder="每行一个测试场景，将添加到所选需求中"
          />
        </el-form-item>
      </div>

      <!-- 导出配置 -->
      <div v-if="form.operation_type === 'export_requirements'">
        <el-form-item label="导出格式" prop="export_format">
          <el-radio-group v-model="form.export_format">
            <el-radio label="excel">Excel文件</el-radio>
            <el-radio label="csv">CSV文件</el-radio>
            <el-radio label="json">JSON文件</el-radio>
            <el-radio label="pdf">PDF报告</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="包含字段">
          <el-checkbox-group v-model="form.export_fields">
            <el-checkbox label="basic_info">基本信息</el-checkbox>
            <el-checkbox label="test_criteria">测试准则</el-checkbox>
            <el-checkbox label="business_rules">业务规则</el-checkbox>
            <el-checkbox label="traceability">追溯信息</el-checkbox>
            <el-checkbox label="related_test_cases">关联测试用例</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="文件名">
          <el-input v-model="form.export_filename" placeholder="导出文件名（可选）" />
        </el-form-item>
      </div>

      <!-- 删除确认 -->
      <div v-if="form.operation_type === 'delete_requirements'">
        <el-alert
          title="危险操作"
          description="删除操作不可逆，请确认后再继续"
          type="error"
          show-icon
          :closable="false"
        />
        <el-form-item label="删除确认" prop="delete_confirmation">
          <el-input
            v-model="form.delete_confirmation"
            placeholder="请输入 DELETE 确认删除"
          />
        </el-form-item>
        <el-form-item label="删除原因">
          <el-input
            v-model="form.delete_reason"
            type="textarea"
            :rows="3"
            placeholder="请说明删除原因"
          />
        </el-form-item>
      </div>

      <!-- 批量审批 -->
      <div v-if="form.operation_type === 'batch_approve' || form.operation_type === 'batch_reject'">
        <el-form-item :label="form.operation_type === 'batch_approve' ? '审批意见' : '拒绝原因'" prop="review_comment">
          <el-input
            v-model="form.review_comment"
            type="textarea"
            :rows="4"
            :placeholder="form.operation_type === 'batch_approve' ? '请输入审批意见' : '请输入拒绝原因'"
          />
        </el-form-item>
      </div>

      <!-- 操作预览 -->
      <div v-if="form.operation_type" class="operation-preview">
        <el-divider content-position="left">操作预览</el-divider>
        <el-table :data="previewData" style="width: 100%" max-height="300">
          <el-table-column prop="id" label="需求ID" width="80" />
          <el-table-column prop="title" label="需求标题" show-overflow-tooltip />
          <el-table-column prop="current_value" label="当前值" width="120" />
          <el-table-column prop="new_value" label="新值" width="120">
            <template #default="scope">
              <el-tag v-if="scope.row.new_value" size="small" type="success">
                {{ scope.row.new_value }}
              </el-tag>
              <span v-else>-</span>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 执行进度 -->
      <div v-if="executing" class="execution-progress">
        <el-divider content-position="center">执行进度</el-divider>
        <el-progress
          :percentage="executionProgress"
          :status="executionStatus"
        />
        <div class="progress-info">
          <span>{{ executionMessage }}</span>
          <span style="float: right">{{ executedCount }}/{{ selectedRequirements.length }}</span>
        </div>
      </div>
    </el-form>

    <div class="form-actions">
      <el-button @click="handleCancel" :disabled="executing">取消</el-button>
      <el-button type="primary" @click="handleExecute" :loading="executing" :disabled="!form.operation_type">
        {{ executing ? '执行中...' : '执行操作' }}
      </el-button>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

export default {
  name: 'BatchOperationForm',
  props: {
    selectedRequirements: {
      type: Array,
      default: () => []
    },
    statuses: {
      type: Array,
      default: () => []
    },
    priorities: {
      type: Array,
      default: () => []
    },
    categories: {
      type: Array,
      default: () => []
    },
    modules: {
      type: Array,
      default: () => []
    },
    availableTags: {
      type: Array,
      default: () => []
    },
    reviewers: {
      type: Array,
      default: () => []
    }
  },
  emits: ['execute', 'cancel'],
  setup(props, { emit }) {
    const formRef = ref(null)
    const executing = ref(false)
    const executionProgress = ref(0)
    const executionStatus = ref('')
    const executionMessage = ref('')
    const executedCount = ref(0)
    const documentsText = ref('')

    // 表单数据
    const form = reactive({
      operation_type: '',
      new_status: '',
      status_comment: '',
      new_priority: '',
      new_category: '',
      new_module: '',
      tags: [],
      reviewers: [],
      review_deadline: null,
      documents: [],
      test_generation_config: {
        ai_model: 'gpt-4',
        test_types: ['functional'],
        cases_per_requirement: 3,
        template: ''
      },
      criteria_updates: [],
      batch_acceptance_criteria: '',
      batch_test_scenarios: '',
      export_format: 'excel',
      export_fields: ['basic_info', 'test_criteria'],
      export_filename: '',
      delete_confirmation: '',
      delete_reason: '',
      review_comment: ''
    })

    // 表单验证规则
    const rules = {
      operation_type: [
        { required: true, message: '请选择操作类型', trigger: 'change' }
      ],
      new_status: [
        {
          validator: (rule, value, callback) => {
            if (form.operation_type === 'update_status' && !value) {
              callback(new Error('请选择新状态'))
            } else {
              callback()
            }
          },
          trigger: 'change'
        }
      ],
      new_priority: [
        {
          validator: (rule, value, callback) => {
            if (form.operation_type === 'update_priority' && !value) {
              callback(new Error('请选择新优先级'))
            } else {
              callback()
            }
          },
          trigger: 'change'
        }
      ],
      delete_confirmation: [
        {
          validator: (rule, value, callback) => {
            if (form.operation_type === 'delete_requirements' && value !== 'DELETE') {
              callback(new Error('请输入 DELETE 确认删除'))
            } else {
              callback()
            }
          },
          trigger: 'blur'
        }
      ],
      review_comment: [
        {
          validator: (rule, value, callback) => {
            if ((form.operation_type === 'batch_approve' || form.operation_type === 'batch_reject') && !value) {
              callback(new Error('请输入审批意见或拒绝原因'))
            } else {
              callback()
            }
          },
          trigger: 'blur'
        }
      ]
    }

    // 操作预览数据
    const previewData = computed(() => {
      return props.selectedRequirements.map(req => {
        let currentValue = ''
        let newValue = ''

        switch (form.operation_type) {
          case 'update_status':
            currentValue = req.status || 'draft'
            newValue = form.new_status
            break
          case 'update_priority':
            currentValue = req.priority || 'medium'
            newValue = form.new_priority
            break
          case 'update_category':
            currentValue = req.category || '无'
            newValue = form.new_category
            break
          case 'update_module':
            currentValue = req.module || '无'
            newValue = form.new_module
            break
          case 'add_tags':
            currentValue = (req.tags || []).join(', ') || '无'
            newValue = form.tags.join(', ')
            break
          case 'delete_requirements':
            currentValue = '存在'
            newValue = '删除'
            break
          default:
            currentValue = '-'
            newValue = '-'
        }

        return {
          id: req.id,
          title: req.title,
          current_value: currentValue,
          new_value: newValue
        }
      })
    })

    // 监听文档文本变化
    watch(documentsText, (newValue) => {
      form.documents = newValue.split('\n').filter(line => line.trim())
    })

    // 模拟执行进度
    const simulateExecution = () => {
      const total = props.selectedRequirements.length
      let current = 0
      
      const interval = setInterval(() => {
        current++
        executedCount.value = current
        executionProgress.value = Math.round((current / total) * 100)
        executionMessage.value = `正在处理第 ${current} 个需求...`

        if (current >= total) {
          executionStatus.value = 'success'
          executionMessage.value = '操作执行完成'
          executing.value = false
          clearInterval(interval)
          ElMessage.success('批量操作执行成功')
        }
      }, 800)
    }

    // 执行操作
    const handleExecute = async () => {
      try {
        await formRef.value.validate()

        // 危险操作二次确认
        if (form.operation_type === 'delete_requirements') {
          await ElMessageBox.confirm(
            `确定要删除 ${props.selectedRequirements.length} 个需求吗？此操作不可撤销！`,
            '危险操作确认',
            {
              confirmButtonText: '确定删除',
              cancelButtonText: '取消',
              type: 'error'
            }
          )
        }

        executing.value = true
        executionProgress.value = 0
        executionStatus.value = ''
        executionMessage.value = '准备执行操作...'
        executedCount.value = 0

        // 开始执行
        simulateExecution()
        
        // 发送执行请求
        emit('execute', {
          operation_type: form.operation_type,
          requirements: props.selectedRequirements,
          params: { ...form }
        })
      } catch (error) {
        if (error === 'cancel') {
          // 用户取消
          return
        }
        ElMessage.error('请检查表单填写是否正确')
      }
    }

    // 取消
    const handleCancel = () => {
      emit('cancel')
    }

    return {
      formRef,
      executing,
      executionProgress,
      executionStatus,
      executionMessage,
      executedCount,
      documentsText,
      form,
      rules,
      previewData,
      handleExecute,
      handleCancel
    }
  }
}
</script>

<style scoped>
.batch-operation-form {
  max-height: 80vh;
  overflow-y: auto;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.operation-preview {
  margin-top: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.execution-progress {
  margin-top: 20px;
  padding: 20px;
  background-color: #f0f9ff;
  border-radius: 6px;
}

.progress-info {
  margin-top: 10px;
  font-size: 14px;
  color: #606266;
}

.el-form-item {
  margin-bottom: 16px;
}

.el-divider {
  margin: 20px 0 15px 0;
}

.el-alert {
  margin-bottom: 20px;
}
</style>

<template>
  <div class="step-validation-dashboard">
    <el-card class="header-card">
      <template #header>
        <div class="card-header">
          <span>步骤校验仪表板</span>
          <el-button type="primary" @click="refreshData" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </template>
      
      <!-- 执行任务选择 -->
      <div class="execution-selector">
        <el-select 
          v-model="selectedExecutionId" 
          placeholder="选择执行任务" 
          @change="onExecutionChange"
          style="width: 300px"
        >
          <el-option
            v-for="execution in executions"
            :key="execution.execution_id"
            :label="`${execution.name} (${execution.database_type})`"
            :value="execution.execution_id"
          >
            <span>{{ execution.name }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px">
              {{ execution.success_cases }}/{{ execution.total_cases }}
            </span>
          </el-option>
        </el-select>
      </div>
    </el-card>

    <!-- 整体统计信息 -->
    <el-card v-if="executionDetail && executionDetail.overall_statistics" class="statistics-card">
      <template #header>
        <span>整体统计</span>
      </template>
      <el-row :gutter="20">
        <el-col :span="6">
          <el-statistic title="总用例数" :value="executionDetail.overall_statistics.total_cases" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="总步骤数" :value="executionDetail.overall_statistics.total_steps" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="匹配步骤数" :value="executionDetail.overall_statistics.matched_steps" />
        </el-col>
        <el-col :span="6">
          <el-statistic 
            title="整体成功率" 
            :value="(executionDetail.overall_statistics.overall_success_rate * 100).toFixed(1)" 
            suffix="%" 
          />
        </el-col>
      </el-row>
    </el-card>

    <!-- 测试用例列表 -->
    <div v-if="executionDetail && executionDetail.cases" class="cases-container">
      <el-card 
        v-for="(testCase, index) in executionDetail.cases" 
        :key="testCase.test_case_id" 
        class="case-card"
      >
        <template #header>
          <div class="case-header">
            <div>
              <h3>{{ testCase.test_case_title || `测试用例 ${testCase.test_case_id}` }}</h3>
              <div class="case-info">
                <el-tag size="small">ID: {{ testCase.test_case_id }}</el-tag>
                <el-tag 
                  size="small" 
                  :type="testCase.statistics.success_rate > 0.8 ? 'success' : testCase.statistics.success_rate > 0.5 ? 'warning' : 'danger'"
                >
                  成功率: {{ (testCase.statistics.success_rate * 100).toFixed(1) }}%
                </el-tag>
                <el-tag size="small" type="info">
                  {{ testCase.statistics.matched_steps }}/{{ testCase.statistics.total_steps }} 步骤匹配
                </el-tag>
              </div>
            </div>
            <div class="pcap-files">
              <span class="pcap-label">PCAP文件:</span>
              <el-tag 
                v-for="file in testCase.capture_files" 
                :key="file" 
                size="small" 
                type="info"
                class="pcap-tag"
              >
                {{ getFileName(file) }}
              </el-tag>
            </div>
          </div>
        </template>

        <!-- 步骤详情 -->
        <div class="steps-container">
          <el-collapse v-model="activeSteps[index]">
            <el-collapse-item 
              v-for="step in testCase.steps" 
              :key="step.step_number"
              :title="`步骤 ${step.step_number}`"
              :name="step.step_number"
            >
              <template #title>
                <div class="step-title">
                  <span>步骤 {{ step.step_number }}</span>
                  <div class="step-status">
                    <el-icon 
                      v-if="step.validation && step.validation.matched" 
                      color="#67c23a"
                      size="16"
                    >
                      <Check />
                    </el-icon>
                    <el-icon 
                      v-else-if="step.validation && step.validation.status === 'pcap_missing'" 
                      color="#f56c6c"
                      size="16"
                    >
                      <Warning />
                    </el-icon>
                    <el-icon 
                      v-else 
                      color="#e6a23c"
                      size="16"
                    >
                      <Close />
                    </el-icon>
                    <span class="status-text">
                      {{ getStatusText(step.validation) }}
                    </span>
                  </div>
                </div>
              </template>

              <div class="step-content">
                <!-- SQL语句展示 -->
                <div class="sql-section">
                  <h4>SQL语句 ({{ step.sqls.length }}条)</h4>
                  <div v-if="step.sqls.length === 0" class="no-sql">
                    <el-empty description="此步骤没有SQL语句" :image-size="60" />
                  </div>
                  <div v-else>
                    <div 
                      v-for="(sql, sqlIndex) in step.sqls" 
                      :key="sqlIndex" 
                      class="sql-item"
                    >
                      <div class="sql-header">
                        <span class="sql-index">SQL {{ sqlIndex + 1 }}</span>
                      </div>
                      <el-input
                        :value="sql"
                        type="textarea"
                        :rows="3"
                        readonly
                        class="sql-textarea"
                      />
                    </div>
                  </div>
                </div>

                <!-- PCAP匹配结果 -->
                <div class="validation-section">
                  <h4>PCAP包匹配结果</h4>
                  <div v-if="!step.validation" class="no-validation">
                    <el-alert title="未进行校验" type="info" show-icon :closable="false" />
                  </div>
                  <div v-else-if="step.validation.status === 'pcap_missing'" class="pcap-missing">
                    <el-alert 
                      :title="step.validation.reason || 'PCAP文件缺失'" 
                      type="error" 
                      show-icon 
                      :closable="false" 
                    />
                  </div>
                  <div v-else class="validation-result">
                    <el-row :gutter="20">
                      <el-col :span="12">
                        <div class="validation-info">
                          <p><strong>PCAP文件:</strong> {{ step.pcap_file || '未知' }}</p>
                          <p><strong>匹配状态:</strong> 
                            <el-tag 
                              :type="getStatusTagType(step.validation.status)"
                              size="small"
                            >
                              {{ getStatusText(step.validation) }}
                            </el-tag>
                          </p>
                          <p><strong>相似度:</strong> {{ (step.validation.similarity * 100).toFixed(1) }}%</p>
                          <p v-if="step.validation.confidence">
                            <strong>置信度:</strong> {{ (step.validation.confidence * 100).toFixed(1) }}%
                          </p>
                        </div>
                      </el-col>
                      <el-col :span="12">
                        <div class="match-summary">
                          <p><strong>匹配语句数:</strong> {{ step.validation.matched_statements_count || 0 }}</p>
                          <p v-if="step.validation.packet_numbers && step.validation.packet_numbers.length">
                            <strong>数据包编号:</strong> {{ step.validation.packet_numbers.join(', ') }}
                          </p>
                          <p v-if="step.validation.error_message" class="error-message">
                            <strong>错误信息:</strong> {{ step.validation.error_message }}
                          </p>
                        </div>
                      </el-col>
                    </el-row>

                    <!-- 匹配的SQL语句详情 -->
                    <div v-if="step.validation.matched_statements && step.validation.matched_statements.length" class="matched-statements">
                      <h5>PCAP中匹配的SQL语句</h5>
                      <el-table 
                        :data="step.validation.matched_statements" 
                        size="small"
                        max-height="300"
                      >
                        <el-table-column prop="packet_number" label="数据包" width="80" />
                        <el-table-column prop="sql_type" label="SQL类型" width="100" />
                        <el-table-column prop="database_type" label="数据库类型" width="100" />
                        <el-table-column prop="confidence" label="置信度" width="100">
                          <template #default="scope">
                            {{ (scope.row.confidence * 100).toFixed(1) }}%
                          </template>
                        </el-table-column>
                        <el-table-column prop="timestamp" label="时间戳" width="150" />
                        <el-table-column prop="sql" label="SQL语句" min-width="300">
                          <template #default="scope">
                            <el-input
                              :value="scope.row.sql"
                              type="textarea"
                              :rows="2"
                              readonly
                              size="small"
                            />
                          </template>
                        </el-table-column>
                      </el-table>
                    </div>
                  </div>
                </div>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
      </el-card>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-loading-directive />
    </div>

    <!-- 空状态 -->
    <el-empty 
      v-if="!loading && (!executionDetail || !executionDetail.cases || executionDetail.cases.length === 0)" 
      description="请选择执行任务查看步骤校验结果"
      :image-size="100"
    />
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh, Check, Close, Warning } from '@element-plus/icons-vue'
import axios from 'axios'

export default {
  name: 'StepValidationDashboard',
  components: {
    Refresh,
    Check,
    Close,
    Warning
  },
  setup() {
    const loading = ref(false)
    const executions = ref([])
    const selectedExecutionId = ref('')
    const executionDetail = ref(null)
    const activeSteps = ref([])

    // 获取执行任务列表
    const fetchExecutions = async () => {
      try {
        const response = await axios.get('/api/step-validation-dashboard/executions')
        if (response.data.success) {
          executions.value = response.data.executions
        }
      } catch (error) {
        console.error('获取执行任务列表失败:', error)
        ElMessage.error('获取执行任务列表失败')
      }
    }

    // 获取执行详情
    const fetchExecutionDetail = async (executionId) => {
      if (!executionId) return
      
      loading.value = true
      try {
        const response = await axios.get(`/api/step-validation-dashboard/execution/${executionId}`)
        if (response.data.success) {
          executionDetail.value = response.data
          // 初始化折叠面板状态
          activeSteps.value = response.data.cases.map(() => [])
        } else {
          ElMessage.error('获取执行详情失败')
        }
      } catch (error) {
        console.error('获取执行详情失败:', error)
        ElMessage.error('获取执行详情失败')
      } finally {
        loading.value = false
      }
    }

    // 执行任务变更
    const onExecutionChange = (executionId) => {
      fetchExecutionDetail(executionId)
    }

    // 刷新数据
    const refreshData = async () => {
      await fetchExecutions()
      if (selectedExecutionId.value) {
        await fetchExecutionDetail(selectedExecutionId.value)
      }
    }

    // 获取文件名
    const getFileName = (filePath) => {
      if (!filePath) return ''
      return filePath.split('/').pop() || filePath
    }

    // 获取状态文本
    const getStatusText = (validation) => {
      if (!validation) return '未校验'
      
      switch (validation.status) {
        case 'matched': return '完全匹配'
        case 'partial': return '部分匹配'
        case 'similar': return '相似匹配'
        case 'not_found': return '未找到'
        case 'pcap_missing': return 'PCAP缺失'
        case 'error': return '校验错误'
        default: return validation.status || '未知'
      }
    }

    // 获取状态标签类型
    const getStatusTagType = (status) => {
      switch (status) {
        case 'matched': return 'success'
        case 'partial': 
        case 'similar': return 'warning'
        case 'not_found': 
        case 'error': return 'danger'
        case 'pcap_missing': return 'info'
        default: return 'info'
      }
    }

    onMounted(() => {
      fetchExecutions()
    })

    return {
      loading,
      executions,
      selectedExecutionId,
      executionDetail,
      activeSteps,
      onExecutionChange,
      refreshData,
      getFileName,
      getStatusText,
      getStatusTagType
    }
  }
}
</script>

<style scoped>
.step-validation-dashboard {
  padding: 20px;
}

.header-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.execution-selector {
  margin-top: 10px;
}

.statistics-card {
  margin-bottom: 20px;
}

.cases-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.case-card {
  margin-bottom: 20px;
}

.case-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.case-header h3 {
  margin: 0 0 10px 0;
  color: #303133;
}

.case-info {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.pcap-files {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.pcap-label {
  font-weight: bold;
  color: #606266;
}

.pcap-tag {
  margin: 2px;
}

.step-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.step-status {
  display: flex;
  align-items: center;
  gap: 4px;
}

.status-text {
  font-size: 12px;
  color: #606266;
}

.step-content {
  padding: 20px 0;
}

.sql-section,
.validation-section {
  margin-bottom: 20px;
}

.sql-section h4,
.validation-section h4 {
  margin: 0 0 15px 0;
  color: #303133;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 8px;
}

.sql-item {
  margin-bottom: 15px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
}

.sql-header {
  background: #f5f7fa;
  padding: 8px 12px;
  border-bottom: 1px solid #ebeef5;
}

.sql-index {
  font-weight: bold;
  color: #606266;
}

.sql-textarea {
  border: none;
}

.sql-textarea :deep(.el-textarea__inner) {
  border: none;
  box-shadow: none;
  background: #fafafa;
}

.no-sql,
.no-validation {
  text-align: center;
  padding: 20px;
}

.validation-info p,
.match-summary p {
  margin: 8px 0;
  color: #606266;
}

.error-message {
  color: #f56c6c !important;
}

.matched-statements {
  margin-top: 20px;
}

.matched-statements h5 {
  margin: 0 0 10px 0;
  color: #303133;
}

.loading-container {
  text-align: center;
  padding: 50px;
}

@media (max-width: 768px) {
  .case-header {
    flex-direction: column;
    gap: 15px;
  }
  
  .pcap-files {
    width: 100%;
  }
  
  .step-title {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
</style>

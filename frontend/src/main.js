import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

// 导入并安装 ResizeObserver 错误修复
import { installResizeObserverFix } from './utils/resizeObserverFix'
installResizeObserverFix()

// 导入安全访问工具
import * as safeAccess from './utils/safeAccess'

const app = createApp(App)

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 全局注册安全访问工具
app.config.globalProperties.$safe = safeAccess

// 提供全局属性
app.provide('$safe', safeAccess)

app.use(ElementPlus)
app.use(router)
app.mount('#app')

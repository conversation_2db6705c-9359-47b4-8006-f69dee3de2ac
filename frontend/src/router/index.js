import { createRouter, createWebHistory } from 'vue-router'
import QueryInterface from '../views/QueryInterface.vue'
import MongoQueryInterface from '../views/MongoQueryInterface.vue'
import PostgresQueryInterface from '../views/PostgresQueryInterface.vue'
import GaussDBQueryInterface from '../views/GaussDBQueryInterface.vue'
import OracleQueryInterface from '../views/OracleQueryInterface.vue'
import DownloadManager from '../views/DownloadManager.vue'
import DatabaseManager from '../components/DatabaseManager.vue'
import ServerManagement from '../views/ServerManagement.vue'
import GatewayServerManagement from '../views/GatewayServerManagement.vue'
import TaskManagement from '../components/TaskManagement.vue'
import RequirementManagement from '../views/RequirementManagement.vue'
import TestCaseManagement from '../views/TestCaseManagement.vue'
import CExecutorTest from '../views/CExecutorTest.vue'
import StepValidationDashboard from '../views/StepValidationDashboard.vue'
import LogManagement from '../views/LogManagement.vue'

const routes = [
  {
    path: '/',
    name: 'QueryInterface',
    component: QueryInterface
  },
  {
    path: '/mongo',
    name: 'MongoQueryInterface',
    component: MongoQueryInterface
  },
  {
    path: '/postgres',
    name: 'PostgresQueryInterface',
    component: PostgresQueryInterface
  },
  {
    path: '/gaussdb',
    name: 'GaussDBQueryInterface',
    component: GaussDBQueryInterface
  },
  {
    path: '/oracle',
    name: 'OracleQueryInterface',
    component: OracleQueryInterface
  },
  {
    path: '/downloads',
    name: 'DownloadManager',
    component: DownloadManager
  },
  {
    path: '/databases',
    name: 'DatabaseManager',
    component: DatabaseManager
  },
  {
    path: '/servers',
    name: 'ServerManagement',
    component: ServerManagement
  },
  {
    path: '/gateways',
    name: 'GatewayServerManagement',
    component: GatewayServerManagement
  },
  {
    path: '/tasks',
    name: 'TaskManagement',
    component: TaskManagement
  },
  {
    path: '/requirements',
    name: 'RequirementManagement',
    component: RequirementManagement
  },
  {
    path: '/test-cases',
    name: 'TestCaseManagement',
    component: TestCaseManagement
  },
  {
    path: '/step-validation',
    name: 'StepValidationDashboard',
    component: StepValidationDashboard
  },
  {
    path: '/logs',
    name: 'LogManagement',
    component: LogManagement
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router

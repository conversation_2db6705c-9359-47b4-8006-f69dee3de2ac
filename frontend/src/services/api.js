import axios from 'axios'

// 根据环境确定API基础URL
const getBaseURL = () => {
  // 开发环境
  if (process.env.NODE_ENV === 'development') {
    return '/api'  // 使用代理
  }
  // 生产环境
  return '/api'  // 通过nginx代理
}

// 创建统一的axios实例，所有API都使用/api前缀
const api = axios.create({
  baseURL: getBaseURL(),
  timeout: 120000,  // 增加超时时间，因为AI处理可能需要更长时间
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    console.log('API Request:', config.method?.toUpperCase(), config.url, config.data)
    return config
  },
  error => {
    console.error('Request Error:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    console.log('API Response:', response.status, response.data)
    return response.data
  },
  error => {
    console.error('Response Error:', error.response?.data || error.message)
    const message = error.response?.data?.detail || error.message || '请求失败'
    return Promise.reject(new Error(message))
  }
)

const apiService = {
  // 直接暴露axios实例的方法
  get: api.get.bind(api),
  post: api.post.bind(api),
  put: api.put.bind(api),
  delete: api.delete.bind(api),
  patch: api.patch.bind(api),

  // 健康检查
  async getHealth() {
    return await api.get('/health')
  },

  // 处理自然语言查询
  async processQuery(query, capturePackets = true, configId = null) {
    const requestData = {
      query,
      capture_packets: capturePackets
    }

    if (configId) {
      requestData.config_id = configId
    }

    return await api.post('/query', requestData)
  },

  // 获取抓包状态
  async getCaptureStatus() {
    return await api.get('/capture/status')
  },

  // 启动抓包
  async startCapture() {
    return await api.post('/capture/start')
  },

  // 停止抓包
  async stopCapture() {
    return await api.post('/capture/stop')
  },

  // 获取数据库列表
  async getDatabases(params = {}) {
    return await api.get('/databases', { params })
  },

  // 根据数据库类型和版本获取数据库配置
  async getDatabasesByTypeAndVersion(dbType, dbVersion) {
    const params = {}
    if (dbType) params.db_type = dbType
    if (dbVersion) params.db_version = dbVersion
    return await api.get('/databases', { params })
  },

  // 获取表列表
  async getTables(database) {
    return await api.get(`/tables/${database}`)
  },

  // PostgreSQL相关API
  // 处理PostgreSQL自然语言查询
  async processPostgresQuery(query, capturePackets = true, configId = null) {
    const requestData = {
      query,
      capture_packets: capturePackets
    }

    if (configId) {
      requestData.config_id = configId
    }

    return await api.post('/postgres/query', requestData)
  },

  // PostgreSQL抓包相关
  async getPostgresCaptureStatus() {
    return await api.get('/postgres/capture/status')
  },

  async startPostgresCapture() {
    return await api.post('/postgres/capture/start')
  },

  async stopPostgresCapture() {
    return await api.post('/postgres/capture/stop')
  },

  // PostgreSQL相关API
  // 处理PostgreSQL自然语言查询
  async processPostgresQuery(query, capturePackets = true, configId = null) {
    const requestData = {
      query,
      capture_packets: capturePackets
    }

    if (configId) {
      requestData.config_id = configId
    }

    return await api.post('/postgres/query', requestData)
  },

  // PostgreSQL抓包相关
  async getPostgresCaptureStatus() {
    return await api.get('/postgres/capture/status')
  },

  async startPostgresCapture() {
    return await api.post('/postgres/capture/start')
  },

  async stopPostgresCapture() {
    return await api.post('/postgres/capture/stop')
  },

  // 获取PostgreSQL数据库列表
  async getPostgresDatabases() {
    return await api.get('/postgres/databases')
  },

  // 获取PostgreSQL表列表
  async getPostgresTables(schema = 'public', configId = null) {
    const params = { schema }
    if (configId) {
      params.config_id = configId
    }
    return await api.get('/postgres/tables', { params })
  },

  // 获取PostgreSQL表信息
  async getPostgresTableInfo(tableName, schema = 'public') {
    return await api.get(`/postgres/table/${tableName}?schema=${schema}`)
  },

  // 测试PostgreSQL连接
  async testPostgresConnection(configId = null) {
    const params = configId ? { config_id: configId } : {}
    return await api.post('/postgres/test-connection', params)
  },

  // 服务器配置管理
  async getServerConfigs() {
    return await api.get('/servers/')
  },

  async createServerConfig(config) {
    return await api.post('/servers/', config)
  },

  async getServerConfig(configId) {
    return await api.get(`/servers/${configId}`)
  },

  async updateServerConfig(configId, config) {
    return await api.put(`/servers/${configId}`, config)
  },

  async deleteServerConfig(configId) {
    return await api.delete(`/servers/${configId}`)
  },

  async getNetworkInterfaces(configId) {
    return await api.get(`/servers/${configId}/interfaces`)
  },

  async detectDatabaseDeployment(configId, databaseType, databaseHost, databasePort) {
    return await api.post(`/servers/${configId}/detect-deployment`, null, {
      params: {
        database_type: databaseType,
        database_host: databaseHost,
        database_port: databasePort
      }
    })
  },

  async generateCaptureStrategy(configId, databaseType, databaseHost, databasePort) {
    return await api.post(`/servers/${configId}/capture-strategy`, null, {
      params: {
        database_type: databaseType,
        database_host: databaseHost,
        database_port: databasePort
      }
    })
  },

  async buildDockerEnvironment(configId, aiPrompt, databaseType) {
    return await api.post(`/servers/${configId}/build-docker-environment`, {
      ai_prompt: aiPrompt,
      database_type: databaseType
    })
  },

  async buildDockerEnvironmentsAsync(configId, buildRequests) {
    return await api.post(`/servers/${configId}/build-docker-environments-async`, {
      build_requests: buildRequests
    })
  },

  // Docker镜像管理相关API
  async getDockerImages(configId, search = null) {
    const params = {}
    if (search) {
      params.search = search
    }
    return await api.get(`/servers/${configId}/docker-images`, { params })
  },

  async updateDockerImages(configId) {
    return await api.post(`/servers/${configId}/docker-images/update`)
  },

  async getDatabaseDockerImages(configId) {
    return await api.get(`/servers/${configId}/docker-images/database`)
  },

  async buildDockerEnvironmentWithImage(configId, imageRepository, imageTag, databaseType, aiPrompt = '') {
    return await api.post(`/servers/${configId}/build-docker-environment-with-image`, {
      image_repository: imageRepository,
      image_tag: imageTag,
      database_type: databaseType,
      ai_prompt: aiPrompt
    })
  },

  async buildDockerEnvironmentWithImageAsync(configId, imageRepository, imageTag, databaseType, aiPrompt = '') {
    return await api.post(`/servers/${configId}/build-docker-environment-with-image-async`, {
      image_repository: imageRepository,
      image_tag: imageTag,
      database_type: databaseType,
      ai_prompt: aiPrompt
    })
  },

  // MongoDB相关API
  // 处理MongoDB自然语言查询
  async processMongoQuery(query, capturePackets = true, configId = null, executorType = 'python') {
    const requestData = {
      query,
      capture_packets: capturePackets,
      executor_type: executorType
    }

    if (configId) {
      requestData.config_id = configId
    }

    return await api.post('/mongo/query', requestData)
  },

  // MongoDB健康检查
  async getMongoHealth() {
    return await api.get('/mongo/health')
  },

  // 测试MongoDB连接
  async testMongoConnection() {
    return await api.post('/mongo/test-connection')
  },

  // 测试MongoDB C语言执行器
  async testMongoCExecutor() {
    return await api.post('/mongo/test-c-executor')
  },

  // 获取MongoDB数据库列表
  async getMongoDatabases() {
    return await api.get('/mongo/databases')
  },

  // 获取MongoDB集合列表
  async getMongoCollections(database = null) {
    const params = database ? { database } : {}
    return await api.get('/mongo/collections', { params })
  },

  // MongoDB抓包相关
  async getMongoCaptureStatus() {
    return await api.get('/mongo/capture/status')
  },

  async startMongoCapture() {
    return await api.post('/mongo/capture/start')
  },

  async stopMongoCapture() {
    return await api.post('/mongo/capture/stop')
  },

  async analyzeMongoCapture(filePath) {
    return await api.post('/mongo/capture/analyze', { file_path: filePath })
  },

  // MongoDB查询分析
  async analyzeMongoQueryIntent(query) {
    return await api.post('/mongo/query/analyze', { query })
  },

  async explainMongoQueryResult(mongoQuery, result) {
    return await api.post('/mongo/query/explain', { mongo_query: mongoQuery, result })
  },

  async suggestMongoOptimizations(mongoQuery) {
    return await api.post('/mongo/query/optimize', { mongo_query: mongoQuery })
  },

  // 获取数据库配置列表
  async getDatabaseConfigs(page = 1, pageSize = 20, dbType = null) {
    const params = {
      page,
      page_size: pageSize
    }
    if (dbType) {
      params.db_type = dbType
    }
    return await api.get('/databases/', { params })
  },

  // 添加数据库配置
  async addDatabaseConfig(config) {
    return await api.post('/databases/', config)
  },

  // 更新数据库配置
  async updateDatabaseConfig(configId, config) {
    return await api.put(`/databases/${configId}`, config)
  },

  // 删除数据库配置
  async deleteDatabaseConfig(configId) {
    return await api.delete(`/databases/${configId}`)
  },

  // 测试数据库连接
  async testDatabaseConnection(configId) {
    return await api.post(`/databases/${configId}/test`)
  },

  // 设置默认数据库
  async setDefaultDatabase(configId) {
    return await api.post(`/databases/${configId}/set-default`)
  },

  // 测试数据库配置（不保存）
  async testDatabaseConfig(config) {
    return await api.post('/databases/test-config', config)
  },

  // 下载管理相关API
  async getCaptureFiles() {
    return await api.get('/captures/files')
  },

  async deleteCaptureFile(filename) {
    return await api.delete(`/captures/delete/${filename}`)
  },

  async clearAllCaptureFiles() {
    return await api.delete('/captures/clear')
  },

  // 任务管理相关API
  async getTasks(page = 1, pageSize = 20, taskType = null) {
    const params = { page, page_size: pageSize }
    if (taskType) {
      params.task_type = taskType
    }
    return await api.get('/tasks/', { params })
  },

  async getTask(taskId) {
    return await api.get(`/tasks/${taskId}`)
  },

  async cancelTask(taskId) {
    return await api.delete(`/tasks/${taskId}`)
  },

  async getTaskStats() {
    return await api.get('/tasks/stats/summary')
  },

  async cleanupTasks(taskType = null, status = null, hours = 24) {
    const params = { hours }
    if (taskType) {
      params.task_type = taskType
    }
    if (status) {
      params.status = status
    }
    return await api.post('/tasks/cleanup', null, { params })
  },

  async createMySQLTask(configId, sqlQuery, captureDuration = 30) {
    return await api.post('/tasks/mysql/capture', {
      config_id: configId,
      sql_query: sqlQuery,
      capture_duration: captureDuration
    })
  },

  async createPostgreSQLTask(configId, sqlQuery, captureDuration = 30) {
    return await api.post('/tasks/postgres/capture', {
      config_id: configId,
      sql_query: sqlQuery,
      capture_duration: captureDuration
    })
  },

  async createMongoDBTask(configId, mongoQuery, captureDuration = 30, executorType = 'python') {
    return await api.post('/tasks/mongo/capture', {
      config_id: configId,
      mongo_query: mongoQuery,
      capture_duration: captureDuration,
      executor_type: executorType
    })
  },

  // 创建AI+MySQL任务（包含大模型请求）
  async createAIMySQLTask(configId, naturalQuery, captureDuration = 30) {
    return await api.post('/tasks/mysql/ai-capture', {
      config_id: configId,
      natural_query: naturalQuery,
      capture_duration: captureDuration
    })
  },

  // 创建AI+PostgreSQL任务（包含大模型请求）
  async createAIPostgreSQLTask(configId, naturalQuery, captureDuration = 30) {
    return await api.post('/tasks/postgres/ai-capture', {
      config_id: configId,
      natural_query: naturalQuery,
      capture_duration: captureDuration
    })
  },

  // 创建AI+MongoDB任务（包含大模型请求）
  async createAIMongoTask(configId, naturalQuery, captureDuration = 30, executorType = 'python') {
    return await api.post('/tasks/mongo/ai-capture', {
      config_id: configId,
      natural_query: naturalQuery,
      capture_duration: captureDuration,
      executor_type: executorType
    })
  },

  // 创建Oracle任务
  async createOracleTask(configId, sqlQuery, captureDuration = 30) {
    return await api.post('/tasks/oracle/capture', {
      config_id: configId,
      sql_query: sqlQuery,
      capture_duration: captureDuration
    })
  },

  // 创建AI+Oracle任务（包含大模型请求）
  async createAIOracleTask(configId, naturalQuery, captureDuration = 30) {
    return await api.post('/tasks/oracle/ai-capture', {
      config_id: configId,
      natural_query: naturalQuery,
      capture_duration: captureDuration
    })
  },

  // ==================== Oracle API ====================

  // 执行Oracle查询
  async executeOracleQuery(queryData) {
    return await api.post('/oracle/query', queryData)
  },

  // 测试Oracle连接
  async testOracleConnection(connectionData) {
    return await api.post('/oracle/test-connection', connectionData)
  },

  // 获取Oracle schemas
  async getOracleSchemas(configId) {
    const params = configId ? { config_id: configId } : {}
    return await api.get('/oracle/schemas', { params })
  },

  // 获取Oracle表列表
  async getOracleTables(schema, configId) {
    const params = configId ? { config_id: configId } : {}
    return await api.get(`/oracle/tables/${schema}`, { params })
  },

  // 获取Oracle表结构
  async getOracleTableStructure(schema, table, configId) {
    const params = configId ? { config_id: configId } : {}
    return await api.get(`/oracle/table-structure/${schema}/${table}`, { params })
  },

  // Oracle容器管理
  async getOracleContainerStatus(configId) {
    const params = configId ? { config_id: configId } : {}
    return await api.get('/oracle/container/status', { params })
  },

  async startOracleContainer(configId) {
    const params = configId ? { config_id: configId } : {}
    return await api.post('/oracle/container/start', null, { params })
  },

  async stopOracleContainer(configId) {
    const params = configId ? { config_id: configId } : {}
    return await api.post('/oracle/container/stop', null, { params })
  },

  async getOracleConnectionInfo(configId) {
    const params = configId ? { config_id: configId } : {}
    return await api.get('/oracle/container/connection-info', { params })
  },

  // Oracle抓包管理
  async startOracleCapture(host = '*************', port = 1521, serverConfigId = null) {
    const params = { host, port }
    if (serverConfigId) {
      params.server_config_id = serverConfigId
    }
    return await api.post('/oracle/capture/start', null, { params })
  },

  async stopOracleCapture() {
    return await api.post('/oracle/capture/stop')
  },

  async getOracleCaptureStatus() {
    return await api.get('/oracle/capture/status')
  },

  // Oracle数据库配置相关
  async getOracleConfigs() {
    const response = await this.getDatabaseConfigs(1, 100, 'oracle')
    return response.databases || []
  },

  // Oracle AI解析
  async analyzeOraclePackets(captureFile) {
    return await api.post('/oracle/analyze-packets', { capture_file: captureFile })
  },

  // GaussDB相关API
  // 处理GaussDB自然语言查询
  async processGaussDBQuery(query, capturePackets = true, configId = null) {
    const requestData = {
      query,
      capture_packets: capturePackets
    }

    if (configId) {
      requestData.config_id = configId
    }

    return await api.post('/gaussdb/query', requestData)
  },

  // GaussDB抓包相关
  async getGaussDBCaptureStatus() {
    return await api.get('/gaussdb/capture/status')
  },

  async startGaussDBCapture() {
    return await api.post('/gaussdb/capture/start')
  },

  async stopGaussDBCapture() {
    return await api.post('/gaussdb/capture/stop')
  },

  // 获取GaussDB数据库列表
  async getGaussDBDatabases(configId = null) {
    const params = configId ? { config_id: configId } : {}
    return await api.get('/gaussdb/databases', { params })
  },

  // 获取GaussDB表列表
  async getGaussDBTables(schema = 'public', configId = null) {
    const params = { schema }
    if (configId) {
      params.config_id = configId
    }
    return await api.get('/gaussdb/tables', { params })
  },

  // 获取GaussDB表信息
  async getGaussDBTableInfo(tableName, schema = 'public', configId = null) {
    const params = { schema }
    if (configId) {
      params.config_id = configId
    }
    return await api.get(`/gaussdb/table/${tableName}`, { params })
  },

  // 测试GaussDB连接
  async testGaussDBConnection(configId = null) {
    const params = configId ? { config_id: configId } : {}
    return await api.post('/gaussdb/test-connection', params)
  },

  // GaussDB异步抓包任务
  async createGaussDBCaptureTask(configId, sqlQuery, captureDuration = 30) {
    return await api.post('/tasks/gaussdb/capture', {
      config_id: configId,
      sql_query: sqlQuery,
      capture_duration: captureDuration
    })
  },

  // 创建AI+GaussDB任务（包含大模型请求）
  async createAIGaussDBTask(configId, naturalQuery, captureDuration = 30) {
    return await api.post('/tasks/gaussdb/ai-capture', {
      config_id: configId,
      natural_query: naturalQuery,
      capture_duration: captureDuration
    })
  },

  // 批量执行测试用例
  async batchExecuteTestCases(batchRequest) {
    return await api.post('/test-case-execution/batch-execute', batchRequest)
  },

  // 获取批量执行状态
  async getBatchExecutionStatus(batchId) {
    return await api.get(`/test-case-execution/batch-status/${batchId}`)
  },

  // 获取批量执行列表
  async getBatchExecutionList(page = 1, pageSize = 20, databaseType = null, status = null) {
    const params = { page, page_size: pageSize }
    if (databaseType) params.database_type = databaseType
    if (status) params.status = status
    return await api.get('/test-case-execution/batch-list', { params })
  },

  // 取消批量执行
  async cancelBatchExecution(batchId) {
    return await api.delete(`/test-case-execution/batch/${batchId}`)
  },

  // 网关服务器管理
  async getGatewayServers(page = 1, pageSize = 20, searchIp = null) {
    const params = { page, page_size: pageSize }
    if (searchIp) {
      params.search_ip = searchIp
    }
    return await api.get('/gateways/', { params })
  },

  async createGatewayServer(config) {
    return await api.post('/gateways/', {
      name: config.name,
      host: config.host,
      port: config.port,
      username: config.username,
      password: config.password,
      upload_path: config.upload_path,
      description: config.description,
      is_active: config.is_active,
      is_default: config.is_default,
      connection_timeout: config.connection_timeout,
      max_connections: config.max_connections,
      gateway_type: config.gateway_type,
      proxy_enabled: config.proxy_enabled,
      proxy_host: config.proxy_host,
      proxy_port: config.proxy_port,
      kafka_enabled: config.kafka_enabled,
      kafka_host: config.kafka_host,
      kafka_port: config.kafka_port,
      kafka_topic: config.kafka_topic
    })
  },

  async getGatewayServer(gatewayId) {
    return await api.get(`/gateways/${gatewayId}`)
  },

  async updateGatewayServer(gatewayId, config) {
    return await api.put(`/gateways/${gatewayId}`, {
      name: config.name,
      host: config.host,
      port: config.port,
      username: config.username,
      password: config.password,
      upload_path: config.upload_path,
      description: config.description,
      is_active: config.is_active,
      is_default: config.is_default,
      connection_timeout: config.connection_timeout,
      max_connections: config.max_connections,
      gateway_type: config.gateway_type,
      proxy_enabled: config.proxy_enabled,
      proxy_host: config.proxy_host,
      proxy_port: config.proxy_port,
      kafka_enabled: config.kafka_enabled,
      kafka_host: config.kafka_host,
      kafka_port: config.kafka_port,
      kafka_topic: config.kafka_topic
    })
  },

  async deleteGatewayServer(gatewayId) {
    return await api.delete(`/gateways/${gatewayId}`)
  },

  async testGatewayConnection(gatewayId) {
    return await api.post(`/gateways/${gatewayId}/test`)
  },

  async testGatewayConfig(config) {
    return await api.post('/gateways/test', {
      name: config.name,
      host: config.host,
      port: config.port,
      username: config.username,
      password: config.password,
      upload_path: config.upload_path,
      description: config.description,
      is_active: config.is_active,
      is_default: config.is_default,
      connection_timeout: config.connection_timeout,
      max_connections: config.max_connections,
      gateway_type: config.gateway_type,
      proxy_enabled: config.proxy_enabled,
      proxy_host: config.proxy_host,
      proxy_port: config.proxy_port,
      kafka_enabled: config.kafka_enabled,
      kafka_host: config.kafka_host,
      kafka_port: config.kafka_port,
      kafka_topic: config.kafka_topic
    })
  },

  async setDefaultGateway(gatewayId) {
    return await api.post(`/gateways/${gatewayId}/set-default`)
  },

  async searchGatewaysByIp(ip) {
    return await api.get(`/gateways/search/${ip}`)
  },

  async getGatewayStats() {
    return await api.get('/gateways/stats')
  },

  async getDefaultGateway() {
    return await api.get('/gateways/default/get')
  },

  async exportGatewayConfigs() {
    return await api.get('/gateways/export/data')
  },

  async importGatewayConfigs(configs) {
    return await api.post('/gateways/import/data', configs)
  },

  // 日志管理相关API
  // 获取任务执行日志
  async getTaskExecutionLogs(params = {}) {
    return await api.get('/task-execution-logs/search', { params })
  },

  // 获取指定任务的执行日志
  async getTaskExecutionLog(taskId) {
    return await api.get(`/task-execution-logs/task/${taskId}`)
  },

  // 获取执行日志（通用日志）
  async getExecutionLogs(params = {}) {
    return await api.get('/execution-logs/search', { params })
  },

  // 导出日志
  async exportLogs(params = {}) {
    return await api.get('/execution-logs/export', { 
      params,
      responseType: 'blob'  // 用于文件下载
    })
  },

  // 清理日志
  async clearLogs(params = {}) {
    return await api.post('/execution-logs/cleanup', params)
  },

  // 获取失败任务列表
  async getFailedTasks(hours = 24) {
    return await api.get('/task-execution-logs/tasks/failed', { 
      params: { hours }
    })
  },

  // 根据任务类型获取日志
  async getTaskLogsByType(taskType, limit = 100) {
    return await api.get(`/task-execution-logs/tasks/by-type/${taskType}`, {
      params: { limit }
    })
  },

  // 获取执行器统计
  async getExecutorStatistics(days = 7) {
    return await api.get('/task-execution-logs/statistics/executor', {
      params: { days }
    })
  }
}

export default apiService

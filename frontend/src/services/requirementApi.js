import apiService from './api'

/**
 * 协议需求管理API服务
 */
class RequirementApi {
  // 获取需求列表
  async getRequirements(params = {}) {
    const response = await apiService.get('/requirements/', { params })
    return response
  }

  // 获取需求详情
  async getRequirement(requirementId) {
    const response = await apiService.get(`/requirements/${requirementId}`)
    return response
  }

  // 创建需求
  async createRequirement(requirementData) {
    const response = await apiService.post('/requirements/', requirementData)
    return response
  }

  // 更新需求
  async updateRequirement(requirementId, requirementData) {
    const response = await apiService.put(`/requirements/${requirementId}`, requirementData)
    return response
  }

  // 删除需求
  async deleteRequirement(requirementId) {
    const response = await apiService.delete(`/requirements/${requirementId}`)
    return response
  }

  // 获取需求统计信息
  async getRequirementsStatistics() {
    const response = await apiService.get('/requirements/statistics/overview')
    return response
  }

  // 解析协议文档
  async parseProtocolDocument(parseRequest) {
    const response = await apiService.post('/requirements/parse-document', parseRequest)
    return response
  }

  // 批量操作需求
  async batchOperationRequirements(operation) {
    const response = await apiService.post('/requirements/batch-operation', operation)
    return response
  }

  // 获取需求依赖关系
  async getRequirementDependencies(requirementId) {
    const response = await apiService.get(`/requirements/dependencies/${requirementId}`)
    return response
  }

  // 验证需求依赖关系
  async validateRequirementDependencies(requirementIds) {
    const response = await apiService.post('/requirements/validate-dependencies', requirementIds)
    return response
  }

  // 获取需求关联的测试用例
  async getRequirementTestCases(requirementId) {
    const response = await apiService.get(`/requirements/test-cases/${requirementId}`)
    return response
  }

  // 为需求生成测试用例
  async generateTestCasesForRequirement(requirementId, count = 5, focusAreas = '') {
    const response = await apiService.post(`/requirements/generate-test-cases/${requirementId}`, null, {
      params: {
        count,
        focus_areas: focusAreas
      }
    })
    return response
  }

  // 获取枚举选项
  async getRequirementTypes() {
    const response = await apiService.get('/requirements/enums/requirement-types')
    return response
  }

  async getProtocolTypes() {
    const response = await apiService.get('/requirements/enums/protocol-types')
    return response
  }

  async getPriorities() {
    const response = await apiService.get('/requirements/enums/priorities')
    return response
  }

  async getStatuses() {
    const response = await apiService.get('/requirements/enums/statuses')
    return response
  }

  async getComplexities() {
    const response = await apiService.get('/requirements/enums/complexities')
    return response
  }

  async getTestabilities() {
    const response = await apiService.get('/requirements/enums/testabilities')
    return response
  }

  async getDocumentTypes() {
    const response = await apiService.get('/requirements/enums/document-types')
    return response
  }

  // 获取选项数据
  async getCategories() {
    const response = await apiService.get('/requirements/options/categories')
    return response
  }

  async getModules() {
    const response = await apiService.get('/requirements/options/modules')
    return response
  }

  async getAuthors() {
    const response = await apiService.get('/requirements/options/authors')
    return response
  }

  async getReviewers() {
    const response = await apiService.get('/requirements/options/reviewers')
    return response
  }

  async getTags() {
    const response = await apiService.get('/requirements/options/tags')
    return response
  }

  // 导出需求
  async exportRequirements(format = 'excel', requirementIds = null) {
    const response = await apiService.post('/requirements/export', requirementIds, {
      params: { format }
    })
    return response
  }

  // 导入需求
  async importRequirements(filePath, format = 'excel', options = {}) {
    const response = await apiService.post('/requirements/import', {
      file_path: filePath,
      format,
      options
    })
    return response
  }
}

export default new RequirementApi()

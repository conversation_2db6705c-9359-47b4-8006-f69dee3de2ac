import apiService from './api'

export const taskApi = {
  // 获取任务列表
  async getTaskList(page = 1, pageSize = 20, taskType = null, status = null) {
    const params = {
      page,
      page_size: pageSize
    }
    
    if (taskType) {
      params.task_type = taskType
    }
    
    if (status) {
      params.status = status
    }
    
    const response = await apiService.get('/tasks', { params })
    return response
  },

  // 获取任务详情
  async getTaskDetail(taskId) {
    const response = await apiService.get(`/tasks/${taskId}`)
    return response
  },

  // 取消任务
  async cancelTask(taskId) {
    const response = await apiService.delete(`/tasks/${taskId}`)
    return response
  },

  // 提交AI测试用例生成任务
  async submitAITestCaseGenerationTask(requirement, databaseType = 'mysql', operationType = '查询', batchSize = 1) {
    const response = await apiService.post('/tasks/ai-test-case-generation', {
      requirement,
      database_type: databaseType,
      operation_type: operationType,
      batch_size: batchSize
    })
    return response
  },

  // 获取任务统计信息
  async getTaskStats() {
    const response = await apiService.get('/tasks/stats/summary')
    return response
  },

  // 清理过期任务
  async cleanupExpiredTasks(taskType = null, status = null, hours = 24) {
    const params = {}

    if (taskType) {
      params.task_type = taskType
    }

    if (status) {
      params.status = status
    }

    if (hours !== 24) {
      params.hours = hours
    }

    const response = await apiService.post('/tasks/cleanup', null, { params })
    return response
  },

  // 轮询任务状态
  async pollTaskStatus(taskId, onUpdate, interval = 2000, maxAttempts = 150) {
    let attempts = 0

    const poll = async () => {
      try {
        attempts++
        const response = await taskApi.getTaskDetail(taskId)
        const task = response.data

        if (onUpdate) {
          onUpdate(task)
        }

        // 如果任务完成或失败，停止轮询
        if (task.status === 'completed' || task.status === 'failed' || task.status === 'cancelled') {
          return task
        }

        // 如果达到最大尝试次数，停止轮询
        if (attempts >= maxAttempts) {
          console.warn(`任务 ${taskId} 轮询达到最大尝试次数`)
          return task
        }

        // 继续轮询
        setTimeout(poll, interval)
      } catch (error) {
        console.error('轮询任务状态失败:', error)
        if (onUpdate) {
          onUpdate({ error: error.message })
        }
      }
    }

    poll()
  }
}

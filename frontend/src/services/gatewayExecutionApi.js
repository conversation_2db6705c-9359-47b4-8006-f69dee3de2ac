/**
 * 网关执行API服务
 */
import apiService from './api'

const gatewayExecutionApi = {
  /**
   * 执行网关测试
   * @param {Object} request 执行请求参数
   * @returns {Promise} 执行结果
   */
  async executeGatewayTest(request) {
    try {
      const response = await apiService.post('/gateway-execution/execute', request)
      return response
    } catch (error) {
      console.error('执行网关测试失败:', error)
      throw error
    }
  },

  /**
   * 获取异步执行状态
   * @param {string} executionId 执行ID
   * @returns {Promise} 执行状态
   */
  async getExecutionStatus(executionId) {
    try {
      const response = await apiService.get(`/gateway-execution/status/${executionId}`)
      return response
    } catch (error) {
      console.error('获取执行状态失败:', error)
      throw error
    }
  },

  /**
   * 清理执行状态记录
   * @param {string} executionId 执行ID
   * @returns {Promise} 清理结果
   */
  async clearExecutionStatus(executionId) {
    try {
      const response = await apiService.delete(`/gateway-execution/status/${executionId}`)
      return response
    } catch (error) {
      console.error('清理执行状态失败:', error)
      throw error
    }
  },

  /**
   * 列出所有执行状态
   * @returns {Promise} 执行状态列表
   */
  async listExecutionStatus() {
    try {
      const response = await apiService.get('/gateway-execution/status')
      return response
    } catch (error) {
      console.error('列出执行状态失败:', error)
      throw error
    }
  },

  /**
   * 测试Kafka连接
   * @param {number} gatewayServerId 网关服务器ID
   * @returns {Promise} 连接测试结果
   */
  async testKafkaConnection(gatewayServerId) {
    try {
      const response = await apiService.post('/gateway-execution/test-kafka-connection', null, {
        params: { gateway_server_id: gatewayServerId }
      })
      return response
    } catch (error) {
      console.error('Kafka连接测试失败:', error)
      throw error
    }
  }
}

export default gatewayExecutionApi

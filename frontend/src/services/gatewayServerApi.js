/**
 * 网关服务器API服务
 */
import apiService from './api'

const gatewayServerApi = {
  /**
   * 获取网关服务器列表
   * @param {Object} params 查询参数
   * @returns {Promise} 网关服务器列表
   */
  async getGatewayServers(params = {}) {
    try {
  // 与后端 /gateways 路由对齐，基础 baseURL 已含 /api，无需再次加 /api
  const response = await apiService.get('/gateways/', { params })
  return response
    } catch (error) {
      console.error('获取网关服务器列表失败:', error)
      throw error
    }
  },

  /**
   * 获取单个网关服务器详情
   * @param {number} id 网关服务器ID
   * @returns {Promise} 网关服务器详情
   */
  async getGatewayServer(id) {
    try {
  const response = await apiService.get(`/gateways/${id}`)
  return response
    } catch (error) {
      console.error('获取网关服务器详情失败:', error)
      throw error
    }
  },

  /**
   * 创建网关服务器
   * @param {Object} data 网关服务器数据
   * @returns {Promise} 创建结果
   */
  async createGatewayServer(data) {
    try {
  const response = await apiService.post('/gateways/', data)
  return response
    } catch (error) {
      console.error('创建网关服务器失败:', error)
      throw error
    }
  },

  /**
   * 更新网关服务器
   * @param {number} id 网关服务器ID
   * @param {Object} data 更新数据
   * @returns {Promise} 更新结果
   */
  async updateGatewayServer(id, data) {
    try {
  const response = await apiService.put(`/gateways/${id}`, data)
  return response
    } catch (error) {
      console.error('更新网关服务器失败:', error)
      throw error
    }
  },

  /**
   * 删除网关服务器
   * @param {number} id 网关服务器ID
   * @returns {Promise} 删除结果
   */
  async deleteGatewayServer(id) {
    try {
  const response = await apiService.delete(`/gateways/${id}`)
  return response
    } catch (error) {
      console.error('删除网关服务器失败:', error)
      throw error
    }
  },

  /**
   * 测试网关服务器连接
   * @param {number} id 网关服务器ID
   * @returns {Promise} 连接测试结果
   */
  async testConnection(id) {
    try {
  // 后端提供 /gateways/{id}/test
  const response = await apiService.post(`/gateways/${id}/test`)
  return response
    } catch (error) {
      console.error('测试网关服务器连接失败:', error)
      throw error
    }
  }
}

export default gatewayServerApi

import axios from 'axios'

const API_BASE_URL = process.env.VUE_APP_API_BASE_URL || '/api'

// 创建axios实例
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
apiClient.interceptors.request.use(
  config => {
    // 可以在这里添加认证token等
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
apiClient.interceptors.response.use(
  response => {
    // 如果是blob响应，直接返回response对象
    if (response.config.responseType === 'blob') {
      return response
    }
    return response.data
  },
  error => {
    const message = error.response?.data?.detail || error.message || '请求失败'
    return Promise.reject(new Error(message))
  }
)

export const testCaseApi = {
  // 获取测试用例列表
  async getTestCases(params = {}) {
    const response = await apiClient.get('/test-cases', { params })
    return response
  },

  // 导出批量执行报告
  async exportBatchReport(batchId, format = 'excel') {
    const response = await apiClient.get(`/test-case-execution/batch-report/${batchId}`, {
      params: { format },
      responseType: 'blob'
    })
    return response
  },

  // 获取测试用例详情
  async getTestCase(id) {
    const response = await apiClient.get(`/test-cases/${id}`)
    return response
  },

  // 创建测试用例
  async createTestCase(testCase) {
    const response = await apiClient.post('/test-cases', testCase)
    return response
  },

  // 更新测试用例
  async updateTestCase(id, testCase) {
    const response = await apiClient.put(`/test-cases/${id}`, testCase)
    return response
  },

  // 删除测试用例
  async deleteTestCase(id) {
    const response = await apiClient.delete(`/test-cases/${id}`)
    return response
  },

  // 批量删除测试用例
  async batchDeleteTestCases(testCaseIds) {
    const response = await apiClient.delete('/test-cases/batch', {
      data: testCaseIds
    })
    return response
  },

  // 复制测试用例
  async duplicateTestCase(id, newTitle) {
    const response = await apiClient.post(`/test-cases/${id}/duplicate`, null, {
      params: { new_title: newTitle }
    })
    return response
  },

  // 获取最近执行记录
  async getLastExecution(id) {
    const response = await apiClient.get(`/test-cases/${id}/last-execution`)
    return response
  },

  // 获取统计信息
  async getStatistics() {
    const response = await apiClient.get('/test-cases/statistics')
    return response
  },

  // 获取模块列表
  async getModules() {
    const response = await apiClient.get('/test-cases/modules')
    return response
  },

  // 获取创建者列表
  async getAuthors() {
    const response = await apiClient.get('/test-cases/authors')
    return response
  },

  // 获取评审者列表
  async getReviewers() {
    const response = await apiClient.get('/test-cases/reviewers')
    return response
  },



  // 导出测试用例
  async exportTestCases(params = {}) {
    const response = await apiClient.get('/test-cases/export', { params })
    return response
  },

  // 导出测试用例
  async exportTestCases(params = {}, format = 'excel') {
    const queryParams = {
      ...params,
      format
    }
    const response = await apiClient.get('/test-cases/export', {
      params: queryParams,
      responseType: 'blob'  // 重要：设置响应类型为blob
    })
    return response
  },

  // 导入测试用例
  async importTestCases(file) {
    const formData = new FormData()
    formData.append('file', file)
    const response = await apiClient.post('/test-cases/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    return response
  },

  // AI生成测试用例
  async aiGenerateTestCase(requirement, databaseType = 'mysql', operationType = '查询') {
    const response = await apiClient.post('/test-cases/ai/generate', null, {
      params: {
        requirement,
        database_type: databaseType,
        operation_type: operationType
      }
    })
    return response
  },

  // AI评审测试用例
  async aiReviewTestCase(testCase) {
    const response = await apiClient.post('/test-cases/ai/review', testCase)
    return response
  },

  // AI根据评审反馈重新生成测试用例
  async aiRegenerateTestCase(originalTestCase, reviewFeedback) {
    const response = await apiClient.post('/test-cases/ai/regenerate', {
      original_test_case: originalTestCase,
      review_feedback: reviewFeedback
    })
    return response
  },

  // AI测试用例完整工作流
  async aiTestCaseWorkflow(requirement, databaseType = 'mysql', databaseVersion = '', operationType = '查询') {
    const response = await apiClient.post('/test-cases/ai/workflow', null, {
      params: {
        requirement,
        database_type: databaseType,
        database_version: databaseVersion,
        operation_type: operationType
      }
    })
    return response
  },

  // AI批量生成测试用例
  async aiBatchGenerateTestCases(requirement, databaseType = 'mysql', databaseVersion = '', operationType = '查询', batchSize = 0) {
    const response = await apiClient.post('/test-cases/ai/batch-generate', null, {
      params: {
        requirement,
        database_type: databaseType,
        database_version: databaseVersion,
        operation_type: operationType,
        batch_size: batchSize
      }
    })
    return response
  },

  // AI异步生成测试用例
  async aiAsyncGenerateTestCases(requirement, databaseType = 'mysql', databaseVersion = '', operationType = '查询', batchSize = 1) {
    const response = await apiClient.post('/test-cases/ai/async-generate', null, {
      params: {
        requirement,
        database_type: databaseType,
        database_version: databaseVersion,
        operation_type: operationType,
        batch_size: batchSize
      }
    })
    return response
  },

  // 获取数据库版本选项
  async getDatabaseVersions() {
    const response = await apiClient.get('/test-cases/database-versions')
    return response
  },

  // 标准化测试用例格式
  async standardizeFormat() {
    const response = await apiClient.post('/test-cases/standardize-format')
    return response
  }
}

export default testCaseApi

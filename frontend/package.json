{"name": "ai_sql_pcap-frontend", "version": "1.0.0", "description": "AI SQL PCAP Analyzer Frontend", "main": "index.js", "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "dev": "npm run serve"}, "dependencies": {"@codemirror/lang-sql": "^6.5.0", "@codemirror/theme-one-dark": "^6.1.0", "@element-plus/icons-vue": "^2.1.0", "axios": "^1.6.0", "codemirror": "^6.0.1", "echarts": "^6.0.0", "element-plus": "^2.4.0", "highlight.js": "^11.9.0", "vue": "^3.3.0", "vue-router": "^4.2.0"}, "devDependencies": {"@vue/cli-service": "^5.0.0", "@vue/compiler-sfc": "^3.3.0", "sass": "^1.69.0", "sass-loader": "^13.3.0", "string-replace-loader": "^3.2.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}
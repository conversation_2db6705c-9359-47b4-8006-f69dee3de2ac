const { defineConfig } = require('@vue/cli-service')

module.exports = defineConfig({
  transpileDependencies: true,

  // 生产环境配置
  publicPath: '/',
  outputDir: 'dist',
  assetsDir: 'static',

  // 开发服务器配置
  devServer: {
    port: 3001,
    host: '0.0.0.0',
    proxy: {
      '/api': {
        target: 'http://127.0.0.1:8000',
        changeOrigin: true,
        secure: false,
        logLevel: 'debug'
        // 保持/api前缀，不需要pathRewrite
      }
    }
  },

  // 生产环境优化
  productionSourceMap: false,

  // 配置webpack
  configureWebpack: {
    optimization: {
      splitChunks: {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            name: 'chunk-vendors',
            test: /[\\/]node_modules[\\/]/,
            priority: 10,
            chunks: 'initial'
          },
          common: {
            name: 'chunk-common',
            minChunks: 2,
            priority: 5,
            chunks: 'initial'
          }
        }
      }
    },
    // 忽略ResizeObserver错误
    ignoreWarnings: [
      {
        module: /node_modules/,
        message: /ResizeObserver loop completed with undelivered notifications/,
      },
    ]
  }
})

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# C/C++ compiled files
*.o
*.obj
*.exe
*.out
*.app
*.dll
*.so
*.dylib
*.a
*.lib
*.d
*.elf
*.bin
backend/c_executors/*_executor
backend/c_executors/*.o
backend/c_executors/*.so
backend/c_executors/*.dylib

# Virtual Environment
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Logs
logs/
*.log

# Environment variables
.env.local
.env.production

# Database
*.db
*.sqlite3

# Redis
dump.rdb
*.rdb

# Captures
backend/captures/
*.pcap
*.cap

# Node.js (Frontend)
frontend/node_modules/
frontend/dist/
frontend/.env.local
frontend/.env.production
frontend/npm-debug.log*
frontend/yarn-debug.log*
frontend/yarn-error.log*

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
*.bak
*.backup

# Docker
.dockerignore

# Coverage reports
htmlcov/
.coverage
.coverage.*
coverage.xml
*.cover
.hypothesis/
.pytest_cache/
oracle_device/*.zip
oracle_device/instantclient_*/

# Oracle Instant Client

此目录包含Oracle Instant Client文件，用于支持Oracle 11g数据库连接。

## 需要的文件

请从Oracle官网下载以下文件并放置在此目录：

### macOS版本（本地开发使用）
- `instantclient-basic-macos.x64-********.0dbru.zip`
- 下载地址：https://www.oracle.com/database/technologies/instant-client/macos-intel-x86-downloads.html

### Linux版本（Docker部署使用）
- `instantclient-basic-linux.x64-*********.0dbru.zip`
- 下载地址：https://www.oracle.com/database/technologies/instant-client/linux-x86-64-downloads.html

## 安装说明

### macOS本地安装
1. 下载macOS版本的zip文件
2. 解压到此目录
3. 运行 `backend/start_with_oracle.sh` 或直接运行 `python3 start_with_worker.py`（已自动配置环境变量）

### Docker部署
1. 下载Linux版本的zip文件
2. 确保文件名为 `instantclient-basic-linux.x64-*********.0dbru.zip`
3. 运行 `./build_docker.sh` 构建Docker镜像

## 注意事项

- 这些文件由于体积较大（~400MB）不包含在git仓库中
- 需要手动下载并放置在正确位置
- 确保文件名与配置中的完全一致

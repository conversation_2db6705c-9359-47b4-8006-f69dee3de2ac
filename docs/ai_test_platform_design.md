# AI测试平台设计方案

## 1. 系统架构
### 1.1 整体架构
```mermaid
graph TD
    A[前端 Vue3+Element Plus] --> B[FastAPI后端]
    B --> C[AI服务层 DeepSeek+LangChain]
    B --> D[协议测试引擎]
    D --> E[MySQL协议支持]
    D --> F[MongoDB协议支持]
    D --> G[PostgreSQL协议支持]
    B --> H[ARQ异步任务队列]
    B --> I[MCP服务集成]
    B --> J[智能抓包服务]
    J --> K[网络接口检测]
    J --> L[Docker环境管理]
    J --> M[tcpdump自动安装]
    B --> N[测试用例管理]
    N --> O[用例生成引擎]
    N --> P[用例执行引擎]
    N --> Q[结果验证引擎]
    B --> R[数据库配置管理]
    R --> S[服务器配置]
    R --> T[数据库连接池]
```

### 1.2 核心模块
- **前端**：Vue3 + Element Plus + 左侧边栏导航
- **后端**：FastAPI + Uvicorn + 异步处理
- **AI引擎**：DeepSeek模型 + LangChain框架 + MCP工具调用
- **任务队列**：ARQ + Redis + 异步任务处理
- **协议支持**：MySQL/MongoDB/PostgreSQL完整协议栈
- **智能抓包**：AI驱动的网络接口检测 + Docker环境管理
- **测试引擎**：用例生成 + 自动执行 + 结果验证

## 2. 协议测试流程
### 2.1 完整测试流程
```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant Backend
    participant AI_Engine
    participant MCP_Service
    participant Protocol_Test
    participant Packet_Capture
    participant Docker_Manager

    User->>Frontend: 上传协议文档/示例
    Frontend->>Backend: /api/generate-testcases
    Backend->>AI_Engine: 解析文档生成测试用例
    AI_Engine->>Backend: 返回结构化测试用例
    Backend->>Backend: 持久化测试用例

    User->>Frontend: 执行测试用例
    Frontend->>Backend: /api/execute-test
    Backend->>Docker_Manager: 检测/启动数据库环境
    Docker_Manager->>Backend: 环境就绪
    Backend->>Packet_Capture: 启动智能抓包
    Packet_Capture->>Backend: 抓包就绪
    Backend->>MCP_Service: 调用协议测试工具
    MCP_Service->>Protocol_Test: 执行具体协议操作
    Protocol_Test->>Packet_Capture: 生成网络流量
    Packet_Capture->>Backend: 实时抓包数据
    Backend->>AI_Engine: 分析抓包结果
    AI_Engine->>Backend: 验证结果报告
    Backend->>Frontend: 完整测试报告
```

### 2.2 AI测试用例生成流程
```mermaid
flowchart TD
    A[协议文档输入] --> B[LangChain文档解析]
    B --> C[DeepSeek协议理解]
    C --> D[测试场景识别]
    D --> E[用例模板匹配]
    E --> F[参数化测试数据]
    F --> G[测试用例验证]
    G --> H[JSON格式输出]
    H --> I[数据库持久化]
```

## 3. 核心功能
### 3.1 AI测试用例生成
- **输入支持**：
  - 协议RFC文档（PDF/HTML/Markdown）
  - 示例报文（HEX/Base64/PCAP文件）
  - 协议规范描述（自然语言）
  - 现有测试用例模板
- **输出格式**：结构化测试用例（JSON格式）
- **处理流程**：
  1. LangChain多格式文档加载器
  2. DeepSeek模型协议语义理解
  3. 测试场景自动识别（正常/异常/边界）
  4. 测试用例模板智能填充
  5. 参数化测试数据生成
  6. 测试用例验证和优化
  7. 数据库持久化存储

### 3.2 测试用例管理
- **用例存储**：生成的测试用例持久化存储到数据库
- **数据结构**：
  ```json
  {
    "id": "uuid",
    "protocol": "mysql|mongo|postgres",
    "name": "测试用例名称",
    "description": "测试用例描述",
    "category": "normal|error|boundary|performance",
    "test_case": {
      "setup": { "database_config": {...}, "initial_data": [...] },
      "steps": [
        {
          "action": "connect|query|insert|update|delete",
          "payload": {...},
          "expected_response": {...}
        }
      ],
      "cleanup": { "cleanup_sql": [...] },
      "validation": {
        "packet_validation": {...},
        "response_validation": {...}
      }
    },
    "tags": ["tag1", "tag2"],
    "source_document": "文档来源",
    "created_at": "datetime",
    "updated_at": "datetime",
    "execution_count": 0,
    "success_rate": 0.0
  }
  ```
- **复用机制**：
  - 历史测试用例查询和复用
  - 用例模板库管理
  - 标签分类和搜索
  - 用例执行统计和成功率跟踪

### 3.3 智能抓包测试系统
- **AI驱动的网络检测**：
  1. 自动检测数据库部署方式（本地/Docker/远程）
  2. AI智能选择最佳网络接口
  3. 动态生成协议特定的抓包过滤器
  4. 支持多网卡环境的智能切换
- **Docker环境管理**：
  1. 自动检测现有Docker容器
  2. AI构建测试环境（基于用户提示）
  3. 容器网络配置自动化
  4. 失败容器自动清理，保留成功容器
- **抓包工具自动化**：
  1. 自动检测tcpdump可用性
  2. 根据服务器类型自动安装tcpdump
  3. 支持不同Linux发行版的包管理器
  4. 抓包权限自动配置
- **协议解析增强**：
  - MySQL：支持扩展查询和简单查询协议
  - PostgreSQL：优先扩展查询，生成非空数据包
  - MongoDB：完整BSON协议解析
  - 第三方解析器集成（Wireshark、专用解析库）
- **执行流程优化**：
  1. 环境预检和自动修复
  2. 并发抓包和协议测试
  3. 实时数据包分析和过滤
  4. 智能结果比对（预期 vs 实际）
  5. 异常检测和自动重试
  6. 24字节空包自动删除

### 3.4 MCP服务集成增强
- **多协议MCP服务器**：
  - `dbhub-mysql`: MySQL通用数据库操作
  - `dbhub-postgres`: PostgreSQL数据库操作
  - `mongodb-lens`: MongoDB专用全功能服务
  - 支持动态MCP服务器注册和发现
- **MCP工具调用框架**：
```python
# 增强的MCP服务调用
from services.mcp_service import MCPService

class ProtocolTestEngine:
    def __init__(self):
        self.mcp_service = MCPService()

    async def run_protocol_test(self, test_case):
        """执行协议测试用例"""
        protocol = test_case['protocol']

        # 根据协议选择MCP服务器
        server_name = f"dbhub-{protocol}" if protocol != "mongo" else "mongodb-lens"

        # 执行测试步骤
        results = []
        for step in test_case['test_case']['steps']:
            result = await self.mcp_service.invoke_tool(
                server=server_name,
                tool=step['action'],
                args={
                    "query": step['payload'],
                    "expected": step['expected_response']
                }
            )
            results.append(result)

        return {
            "test_case_id": test_case['id'],
            "results": results,
            "success": all(r['success'] for r in results)
        }
```

### 3.5 测试结果验证系统
- **多层验证机制**：
  1. **协议层验证**：检查协议格式正确性
  2. **数据包验证**：验证抓包数据完整性
  3. **响应验证**：比对预期与实际响应
  4. **性能验证**：检查响应时间和资源使用
- **AI辅助验证**：
  - DeepSeek分析异常模式
  - 自动生成验证报告
  - 智能错误分类和建议

## 4. API接口设计
### 4.1 测试用例管理API
- **生成测试用例**：POST /api/testcases/generate
  ```json
  {
    "protocol": "mysql|mongo|postgres",
    "input_type": "document|packet|template",
    "document_url": "string",
    "document_content": "string",
    "sample_packets": ["base64_encoded_packet"],
    "template_id": "uuid",
    "generation_options": {
      "include_error_cases": true,
      "include_boundary_cases": true,
      "include_performance_cases": false
    }
  }
  ```

- **查询测试用例**：GET /api/testcases
  ```json
  {
    "protocol": "mysql|mongo|postgres",
    "category": "normal|error|boundary|performance",
    "tags": ["tag1", "tag2"],
    "page": 1,
    "page_size": 20
  }
  ```

- **更新测试用例**：PUT /api/testcases/{test_case_id}
- **删除测试用例**：DELETE /api/testcases/{test_case_id}

### 4.2 测试执行API
- **执行单个测试用例**：POST /api/test/execute
  ```json
  {
    "test_case_id": "uuid",
    "server_config_id": "int",
    "capture_enabled": true,
    "capture_options": {
      "interface": "auto|eth0|docker0",
      "timeout": 30,
      "max_packets": 1000
    }
  }
  ```

- **批量执行测试用例**：POST /api/test/execute-batch
  ```json
  {
    "test_case_ids": ["uuid1", "uuid2"],
    "server_config_id": "int",
    "execution_options": {
      "parallel": true,
      "stop_on_failure": false,
      "capture_enabled": true
    }
  }
  ```

- **查询执行状态**：GET /api/test/status/{execution_id}
- **停止测试执行**：POST /api/test/stop/{execution_id}

### 4.3 抓包管理API
- **启动智能抓包**：POST /api/capture/start
  ```json
  {
    "database_type": "mysql|mongo|postgres",
    "database_host": "string",
    "database_port": "int",
    "server_config_id": "int"
  }
  ```

- **停止抓包**：POST /api/capture/stop
- **抓包状态**：GET /api/capture/status
- **分析抓包文件**：POST /api/capture/analyze
  ```json
  {
    "file_path": "string",
    "protocol": "mysql|mongo|postgres"
  }
  ```

### 4.4 配置管理API
- **服务器配置**：
  - GET /api/config/servers - 获取服务器列表
  - POST /api/config/servers - 添加服务器配置
  - PUT /api/config/servers/{id} - 更新服务器配置
  - DELETE /api/config/servers/{id} - 删除服务器配置

- **数据库配置**：
  - GET /api/config/databases - 获取数据库配置列表
  - POST /api/config/databases - 添加数据库配置
  - PUT /api/config/databases/{id} - 更新数据库配置
  - DELETE /api/config/databases/{id} - 删除数据库配置

- **MCP服务配置**：
  - GET /api/config/mcp-servers - 获取MCP服务器列表
  - POST /api/config/mcp-servers - 注册新的MCP服务器
  - PUT /api/config/mcp-servers/{name} - 更新MCP服务器配置

## 5. 数据库协议支持方案
| 协议       | 客户端库                          | MCP服务器          | 抓包分析工具       | 第三方解析器       |
|------------|-----------------------------------|--------------------|--------------------|--------------------|
| MySQL      | PyMySQL + mysql-connector         | dbhub-mysql        | mysql-packet-analyzer | wireshark-mysql   |
| MongoDB    | pymongo + motor                   | mongodb-lens       | bson-stream-parser | mongo-protocol     |
| PostgreSQL | asyncpg + psycopg2               | dbhub-postgres     | pg_packet_parser   | pg-parser          |

### 5.1 协议特定优化
- **MySQL**：
  - 支持扩展查询和简单查询协议
  - 自动检测SSL/TLS加密状态
  - 支持预处理语句抓包
- **PostgreSQL**：
  - 优先使用扩展查询协议
  - 确保生成非空数据包
  - 支持COPY协议抓包
- **MongoDB**：
  - 完整OP_MSG协议支持
  - BSON数据解析
  - 聚合管道操作抓包

## 6. 部署方案
### 6.1 容器化部署
```dockerfile
# 后端Docker示例
FROM python:3.10-slim

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    tcpdump \
    net-tools \
    iputils-ping \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 安装Python依赖
COPY requirements.txt /app/
RUN pip install --no-cache-dir -r /app/requirements.txt

# 复制应用代码
COPY ./backend /app
WORKDIR /app

# 设置权限
RUN chmod +x /app/start_with_worker.py

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["python", "start_with_worker.py"]
```

```dockerfile
# 前端Docker示例
FROM node:18-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

### 6.2 Docker Compose部署
```yaml
version: '3.8'
services:
  backend:
    build: ./backend
    ports:
      - "8000:8000"
    environment:
      - REDIS_URL=redis://redis:6379
      - DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY}
    depends_on:
      - redis
    volumes:
      - ./captures:/app/captures
      - /var/run/docker.sock:/var/run/docker.sock
    privileged: true  # 需要抓包权限

  frontend:
    build: ./frontend
    ports:
      - "3000:80"
    depends_on:
      - backend

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  worker:
    build: ./backend
    command: ["arq", "tasks.worker.WorkerSettings"]
    environment:
      - REDIS_URL=redis://redis:6379
      - DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY}
    depends_on:
      - redis
    volumes:
      - ./captures:/app/captures
      - /var/run/docker.sock:/var/run/docker.sock
    privileged: true

volumes:
  redis_data:
```

### 6.3 服务依赖
- **必需服务**：
  - Redis（ARQ任务队列 + 缓存）
  - DeepSeek API访问密钥
  - Docker Engine（容器管理）
- **可选服务**：
  - PostgreSQL（元数据存储）
  - Nginx（反向代理）
  - Prometheus + Grafana（监控）

### 6.4 生产环境配置
- **安全配置**：
  - API密钥管理
  - 网络隔离
  - 抓包权限控制
- **性能优化**：
  - Redis集群
  - 负载均衡
  - 异步任务队列扩展
- **监控告警**：
  - 服务健康检查
  - 抓包状态监控
  - 测试执行统计

## 7. 技术实现细节
### 7.1 AI测试用例生成引擎
```python
from langchain.document_loaders import PDFLoader, WebBaseLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.embeddings import OpenAIEmbeddings
from langchain.vectorstores import FAISS
from langchain.chains import RetrievalQA

class TestCaseGenerator:
    def __init__(self):
        self.deepseek_client = OpenAI(
            api_key=Config.DEEPSEEK_API_KEY,
            base_url=Config.DEEPSEEK_BASE_URL
        )
        self.embeddings = OpenAIEmbeddings()

    async def generate_from_document(self, document_path: str, protocol: str):
        """从协议文档生成测试用例"""
        # 1. 文档加载和分割
        loader = PDFLoader(document_path)
        documents = loader.load()
        text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1000, chunk_overlap=200
        )
        chunks = text_splitter.split_documents(documents)

        # 2. 向量化存储
        vectorstore = FAISS.from_documents(chunks, self.embeddings)

        # 3. 协议特定的测试用例生成
        test_scenarios = await self._extract_test_scenarios(
            vectorstore, protocol
        )

        # 4. 生成结构化测试用例
        test_cases = []
        for scenario in test_scenarios:
            test_case = await self._generate_test_case(scenario, protocol)
            test_cases.append(test_case)

        return test_cases
```

### 7.2 智能抓包引擎
```python
class SmartPacketCapture:
    def __init__(self):
        self.network_detector = NetworkDetectionService()
        self.docker_manager = DockerManagerService()

    async def start_intelligent_capture(self, db_config):
        """启动智能抓包"""
        # 1. 环境检测
        deployment_info = await self._detect_database_deployment(db_config)

        # 2. 网络接口选择
        best_interface = await self.network_detector.detect_best_interface(
            deployment_info
        )

        # 3. 生成抓包策略
        capture_strategy = self._generate_capture_strategy(
            deployment_info, best_interface
        )

        # 4. 启动抓包
        return await self._start_capture(capture_strategy)
```

### 7.3 MCP服务集成框架
```python
class MCPServiceManager:
    def __init__(self):
        self.servers = {}
        self.load_mcp_config()

    async def invoke_tool(self, server_name: str, tool_name: str, args: dict):
        """调用MCP工具"""
        if server_name not in self.servers:
            await self._start_mcp_server(server_name)

        server = self.servers[server_name]
        result = await server.call_tool(tool_name, args)
        return result

    async def _start_mcp_server(self, server_name: str):
        """启动MCP服务器"""
        config = self.mcp_config[server_name]
        server = MCPServer(
            command=config['command'],
            args=config['args']
        )
        await server.start()
        self.servers[server_name] = server
```

## 8. 数据模型设计
### 8.1 数据库表结构
```sql
-- 测试用例表
CREATE TABLE test_cases (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    protocol VARCHAR(20) NOT NULL,
    category VARCHAR(50) NOT NULL,
    description TEXT,
    test_case JSONB NOT NULL,
    tags TEXT[],
    source_document VARCHAR(500),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    execution_count INTEGER DEFAULT 0,
    success_rate DECIMAL(5,2) DEFAULT 0.0
);

-- 测试执行记录表
CREATE TABLE test_executions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    test_case_id UUID REFERENCES test_cases(id),
    server_config_id INTEGER,
    status VARCHAR(20) NOT NULL, -- running, completed, failed
    start_time TIMESTAMP DEFAULT NOW(),
    end_time TIMESTAMP,
    result JSONB,
    packet_file VARCHAR(500),
    error_message TEXT,
    execution_log TEXT
);

-- 服务器配置表
CREATE TABLE server_configs (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    host VARCHAR(255) NOT NULL,
    port INTEGER DEFAULT 22,
    username VARCHAR(100) NOT NULL,
    password VARCHAR(255) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    is_default BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 数据库配置表
CREATE TABLE database_configs (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    database_type VARCHAR(20) NOT NULL,
    host VARCHAR(255) NOT NULL,
    port INTEGER NOT NULL,
    username VARCHAR(100),
    password VARCHAR(255),
    database_name VARCHAR(100),
    connection_params JSONB,
    server_config_id INTEGER REFERENCES server_configs(id),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

## 9. 前端界面设计
### 9.1 页面结构
- **主页面**：左侧边栏导航 + 右侧内容区域
- **测试用例管理**：
  - 用例列表（支持筛选、搜索、分页）
  - 用例详情（查看、编辑、复制）
  - 用例生成（文档上传、参数配置）
- **测试执行**：
  - 执行配置（服务器选择、抓包选项）
  - 实时执行状态
  - 结果展示（成功率、错误信息、抓包文件）
- **配置管理**：
  - 服务器配置
  - 数据库配置
  - MCP服务配置

### 9.2 关键组件
```vue
<!-- 测试用例生成组件 -->
<template>
  <el-card class="test-case-generator">
    <el-form :model="form" label-width="120px">
      <el-form-item label="协议类型">
        <el-select v-model="form.protocol">
          <el-option label="MySQL" value="mysql" />
          <el-option label="MongoDB" value="mongo" />
          <el-option label="PostgreSQL" value="postgres" />
        </el-select>
      </el-form-item>

      <el-form-item label="输入类型">
        <el-radio-group v-model="form.inputType">
          <el-radio label="document">协议文档</el-radio>
          <el-radio label="packet">示例报文</el-radio>
          <el-radio label="template">用例模板</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="文档上传" v-if="form.inputType === 'document'">
        <el-upload
          :action="uploadUrl"
          :on-success="handleUploadSuccess"
          accept=".pdf,.html,.md"
        >
          <el-button type="primary">上传文档</el-button>
        </el-upload>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="generateTestCases" :loading="generating">
          生成测试用例
        </el-button>
      </el-form-item>
    </el-form>
  </el-card>
</template>
```
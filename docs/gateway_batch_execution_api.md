# 网关批量执行API文档

## 概述

新增了两个OpenAPI接口来实现网关批量执行功能，允许外部系统根据网关服务器IP、数据库类型和数据库版本批量执行测试用例。

## API接口

### 1. 网关批量执行接口

**接口地址**: `POST /api/gateway-execution/batch-execute-by-version`

**功能描述**: 根据网关服务器IP、数据库类型和数据库版本，批量执行所有匹配的测试用例。

**请求参数**:

```json
{
  "gateway_server_ip": "*************",
  "database_type": "mysql",
  "database_version": "MySQL 8.0",
  "wait_time": 2
}
```

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| gateway_server_ip | string | 是 | 网关服务器IP地址 |
| database_type | string | 是 | 数据库类型 (mysql, postgresql, mongodb, oracle, gaussdb) |
| database_version | string | 是 | 数据库版本 (如: MySQL 8.0, PostgreSQL 15) |
| wait_time | integer | 否 | Kafka消费等待时间（1-10秒），默认2秒 |

**成功响应**:

```json
{
  "success": true,
  "task_id": "batch_gateway_execution_20250905_123456_abc123",
  "total_cases": 15,
  "message": "网关批量执行任务已提交，任务ID: batch_gateway_execution_20250905_123456_abc123，共 15 个测试用例"
}
```

**失败响应**:

```json
{
  "success": false,
  "error": "网关服务器不存在",
  "message": "未找到IP为 ************* 的网关服务器"
}
```

### 2. 查询任务状态接口

**接口地址**: `GET /api/gateway-execution/task-status/{task_id}`

**功能描述**: 根据任务ID查询网关执行任务的状态和结果详情。

**路径参数**:

| 参数名 | 类型 | 描述 |
|--------|------|------|
| task_id | string | 任务ID |

**成功响应**:

```json
{
  "success": true,
  "task_id": "batch_gateway_execution_20250905_123456_abc123",
  "task_type": "网关批量执行",
  "status": "running",
  "progress": 60,
  "message": "正在执行第9个测试用例...",
  "result": null,
  "error": null,
  "duration": null,
  "created_at": "2025-09-05T12:34:56+08:00",
  "updated_at": "2025-09-05T12:37:30+08:00",
  "completed_at": null,
  "failed_at": null,
  "total_test_cases": 15,
  "database_type": "mysql",
  "database_version": "MySQL 8.0"
}
```

**任务完成响应**:

```json
{
  "success": true,
  "task_id": "batch_gateway_execution_20250905_123456_abc123",
  "task_type": "网关批量执行",
  "status": "completed",
  "progress": 100,
  "message": "批量执行完成",
  "result": {
    "total_cases": 15,
    "success_cases": 12,
    "failed_cases": 3,
    "execution_summary": [
      {
        "test_case_id": "tc001",
        "status": "success",
        "message": "执行成功"
      },
      {
        "test_case_id": "tc002", 
        "status": "failed",
        "message": "PCAP上传失败"
      }
    ]
  },
  "error": null,
  "duration": 180.5,
  "created_at": "2025-09-05T12:34:56+08:00",
  "updated_at": "2025-09-05T12:38:00+08:00",
  "completed_at": "2025-09-05T12:38:00+08:00",
  "failed_at": null,
  "total_test_cases": 15,
  "database_type": "mysql",
  "database_version": "MySQL 8.0"
}
```

**任务不存在响应**:

```json
{
  "detail": "任务ID不存在"
}
```

## 业务流程

### 批量执行流程

1. **验证网关服务器**: 根据IP地址查找网关服务器配置
2. **查找测试用例**: 根据数据库类型和版本筛选匹配的测试用例
3. **提交异步任务**: 将所有匹配的测试用例加入批量执行队列
4. **返回任务ID**: 用于后续状态查询

### 执行状态

| 状态 | 描述 |
|------|------|
| pending | 任务排队中 |
| running | 任务执行中 |
| completed | 任务完成 |
| failed | 任务失败 |
| cancelled | 任务取消 |

## 错误码说明

| 错误信息 | 描述 | 解决方案 |
|----------|------|----------|
| 网关服务器不存在 | 指定的IP地址没有对应的网关服务器 | 检查IP地址是否正确，或先添加网关服务器配置 |
| 不支持的数据库类型 | 数据库类型参数值不在支持范围内 | 使用支持的数据库类型: mysql, postgresql, mongodb, oracle, gaussdb |
| 未找到匹配的测试用例 | 指定的数据库类型和版本没有对应的测试用例 | 检查数据库版本是否正确，或先创建相应的测试用例 |
| 任务ID不存在 | 查询的任务ID不存在或已过期 | 检查任务ID是否正确 |

## 使用示例

### Python示例

```python
import asyncio
import aiohttp

async def execute_gateway_batch():
    async with aiohttp.ClientSession() as session:
        # 1. 提交批量执行任务
        data = {
            "gateway_server_ip": "*************",
            "database_type": "mysql",
            "database_version": "MySQL 8.0"
        }
        
        async with session.post(
            "http://localhost:8000/api/gateway-execution/batch-execute-by-version",
            json=data
        ) as response:
            result = await response.json()
            if result['success']:
                task_id = result['task_id']
                print(f"任务提交成功，任务ID: {task_id}")
                
                # 2. 查询任务状态
                while True:
                    async with session.get(
                        f"http://localhost:8000/api/gateway-execution/task-status/{task_id}"
                    ) as status_response:
                        status = await status_response.json()
                        print(f"任务状态: {status['status']}, 进度: {status['progress']}%")
                        
                        if status['status'] in ['completed', 'failed']:
                            print(f"最终结果: {status['result']}")
                            break
                        
                        await asyncio.sleep(5)

asyncio.run(execute_gateway_batch())
```

### curl示例

```bash
# 提交批量执行任务
curl -X POST "http://localhost:8000/api/gateway-execution/batch-execute-by-version" \
     -H "Content-Type: application/json" \
     -d '{
       "gateway_server_ip": "*************",
       "database_type": "mysql", 
       "database_version": "MySQL 8.0"
     }'

# 查询任务状态
curl "http://localhost:8000/api/gateway-execution/task-status/batch_gateway_execution_20250905_123456_abc123"
```

## 注意事项

1. **网关服务器配置**: 确保网关服务器已正确配置并可访问
2. **测试用例数量**: 批量执行会查找所有匹配的测试用例，请确认数量合理
3. **异步执行**: 批量执行是异步的，需要通过任务状态接口查询进度
4. **任务超时**: 任务有超时机制，长时间运行的任务可能会被取消
5. **并发限制**: 系统有并发任务数量限制，避免同时提交过多任务

# 网关执行OpenAPI接口详细文档

## 概述

本文档详细描述了网关执行相关的三个OpenAPI接口，包括调用方式、参数说明和返回结果。这些接口用于实现网关批量执行测试用例的完整流程。

## 接口列表

1. **获取数据库类型和版本** - 查看系统中现有的数据库类型和版本信息
2. **网关批量执行** - 根据网关IP、数据库类型和版本批量执行测试用例
3. **查询任务状态** - 根据任务ID查询执行状态和结果

---

## 1. 获取数据库类型和版本接口

### 接口信息
- **URL**: `GET /api/gateway-execution/database-types-versions`
- **方法**: GET
- **认证**: 无需认证
- **功能**: 获取系统中所有测试用例的数据库类型和版本统计信息

### 请求参数
无需任何参数

### 请求示例

#### curl 请求
```bash
curl -X GET "http://localhost:8000/api/gateway-execution/database-types-versions"
```

#### Python 请求
```python
import requests

response = requests.get("http://localhost:8000/api/gateway-execution/database-types-versions")
data = response.json()
print(data)
```

#### JavaScript 请求
```javascript
fetch('http://localhost:8000/api/gateway-execution/database-types-versions')
  .then(response => response.json())
  .then(data => console.log(data));
```

### 响应结果

#### 成功响应
```json
{
  "success": true,
  "data": {
    "mysql": [
      "MySQL 8.0"
    ],
    "postgresql": [
      "PostgreSQL 16"
    ],
    "mongodb": [
      "MongoDB 6.0"
    ],
    "oracle": [
      "Oracle 19c"
    ],
    "gaussdb": [
      "openGauss 6.0"
    ]
  },
  "total_types": 5,
  "total_cases": 77,
  "message": "成功获取 5 种数据库类型的版本信息，共 77 个测试用例"
}
```

#### 响应字段说明

| 字段名 | 类型 | 描述 |
|--------|------|------|
| success | boolean | 请求是否成功 |
| data | object | 数据库类型-版本映射对象 |
| data.{数据库类型} | array | 该数据库类型的所有版本列表（包含用例数量） |
| total_types | integer | 数据库类型总数 |
| total_cases | integer | 测试用例总数 |
| message | string | 响应消息 |

#### 失败响应
```json
{
  "success": false,
  "data": {},
  "total_types": 0,
  "total_cases": 0,
  "message": "获取数据库类型和版本信息失败: 数据库连接失败"
}
```

---

## 2. 网关批量执行接口

### 接口信息
- **URL**: `POST /api/gateway-execution/batch-execute-by-version`
- **方法**: POST
- **认证**: 无需认证
- **功能**: 根据网关服务器IP、数据库类型和数据库版本批量执行所有匹配的测试用例

### 请求参数

#### 请求体 (JSON)
```json
{
  "gateway_server_ip": "*************",
  "database_type": "mysql",
  "database_version": "MySQL 8.0",
  "wait_time": 2
}
```

#### 参数说明

| 参数名 | 类型 | 必填 | 取值范围 | 默认值 | 描述 |
|--------|------|------|----------|--------|------|
| gateway_server_ip | string | 是 | 有效的IP地址 | - | 网关服务器IP地址 |
| database_type | string | 是 | mysql, postgresql, mongodb, oracle, gaussdb | - | 数据库类型 |
| database_version | string | 是 | 对应数据库的有效版本 | - | 数据库版本（如：MySQL 8.0） |
| wait_time | integer | 否 | 1-10 | 2 | Kafka消费等待时间（秒） |

### 请求示例

#### curl 请求
```bash
curl -X POST "http://localhost:8000/api/gateway-execution/batch-execute-by-version" \
     -H "Content-Type: application/json" \
     -d '{
       "gateway_server_ip": "*************",
       "database_type": "mysql",
       "database_version": "MySQL 8.0",
       "wait_time": 2
     }'
```

#### Python 请求
```python
import requests

url = "http://localhost:8000/api/gateway-execution/batch-execute-by-version"
data = {
    "gateway_server_ip": "*************",
    "database_type": "mysql",
    "database_version": "MySQL 8.0",
    "wait_time": 2
}

response = requests.post(url, json=data)
result = response.json()
print(f"任务ID: {result.get('task_id')}")
print(f"测试用例数量: {result.get('total_cases')}")
```

#### JavaScript 请求
```javascript
const data = {
  gateway_server_ip: "*************",
  database_type: "mysql",
  database_version: "MySQL 8.0",
  wait_time: 2
};

fetch('http://localhost:8000/api/gateway-execution/batch-execute-by-version', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(data)
})
.then(response => response.json())
.then(result => {
  console.log('任务ID:', result.task_id);
  console.log('测试用例数量:', result.total_cases);
});
```

### 响应结果

#### 成功响应
```json
{
  "success": true,
  "task_id": "07962b9f-3e36-40e3-804d-e060cff8d4ff",
  "total_cases": 8,
  "message": "网关批量执行任务已提交，任务ID: 07962b9f-3e36-40e3-804d-e060cff8d4ff，共 8 个测试用例",
  "error": null
}
```

#### 响应字段说明

| 字段名 | 类型 | 描述 |
|--------|------|------|
| success | boolean | 请求是否成功 |
| task_id | string | 任务唯一标识符，用于后续状态查询 |
| total_cases | integer | 匹配的测试用例总数 |
| message | string | 成功消息，包含任务信息 |
| error | string/null | 错误信息，成功时为null |

#### 失败响应示例

**网关服务器不存在**
```json
{
  "success": false,
  "task_id": null,
  "total_cases": null,
  "message": "未找到IP为 ************* 的网关服务器",
  "error": "网关服务器不存在"
}
```

**不支持的数据库类型**
```json
{
  "success": false,
  "task_id": null,
  "total_cases": null,
  "message": "不支持的数据库类型: invalid_db",
  "error": "不支持的数据库类型"
}
```

**没有匹配的测试用例**
```json
{
  "success": false,
  "task_id": null,
  "total_cases": null,
  "message": "未找到数据库类型为 mysql、版本为 MySQL 9.0 的测试用例",
  "error": "未找到匹配的测试用例"
}
```

---

## 3. 查询任务状态接口

### 接口信息
- **URL**: `GET /api/gateway-execution/task-status/{task_id}`
- **方法**: GET
- **认证**: 无需认证
- **功能**: 根据任务ID查询批量执行任务的状态、进度和结果

### 请求参数

#### 路径参数
- `task_id` (string): 任务唯一标识符

### 请求示例

#### curl 请求
```bash
curl -X GET "http://localhost:8000/api/gateway-execution/task-status/07962b9f-3e36-40e3-804d-e060cff8d4ff"
```

#### Python 请求
```python
import requests
import time

task_id = "07962b9f-3e36-40e3-804d-e060cff8d4ff"
url = f"http://localhost:8000/api/gateway-execution/task-status/{task_id}"

# 轮询查询任务状态
while True:
    response = requests.get(url)
    result = response.json()
    
    print(f"状态: {result['status']}")
    print(f"进度: {result['progress']}%")
    print(f"消息: {result['message']}")
    
    if result['status'] in ['completed', 'failed']:
        print(f"最终结果: {result['result']}")
        break
    
    time.sleep(5)  # 等待5秒后再次查询
```

#### JavaScript 请求
```javascript
const taskId = "07962b9f-3e36-40e3-804d-e060cff8d4ff";
const url = `http://localhost:8000/api/gateway-execution/task-status/${taskId}`;

// 定期查询任务状态
const checkStatus = async () => {
  try {
    const response = await fetch(url);
    const result = await response.json();
    
    console.log(`状态: ${result.status}`);
    console.log(`进度: ${result.progress}%`);
    console.log(`消息: ${result.message}`);
    
    if (result.status === 'completed' || result.status === 'failed') {
      console.log('最终结果:', result.result);
      return; // 任务结束
    }
    
    // 5秒后再次查询
    setTimeout(checkStatus, 5000);
  } catch (error) {
    console.error('查询失败:', error);
  }
};

checkStatus();
```

### 响应结果

#### 任务执行中响应
```json
{
  "success": true,
  "task_id": "07962b9f-3e36-40e3-804d-e060cff8d4ff",
  "task_type": "网关批量执行",
  "status": "running",
  "progress": 37,
  "message": "正在执行第 3/8 个测试用例...",
  "result": null,
  "error": null,
  "duration": null,
  "created_at": "2025-09-05T15:51:15.145456+08:00",
  "updated_at": "2025-09-05T15:53:28.892341+08:00",
  "completed_at": null,
  "failed_at": null,
  "total_test_cases": null,
  "database_type": null,
  "database_version": null
}
```

#### 任务完成响应
```json
{
  "success": true,
  "task_id": "07962b9f-3e36-40e3-804d-e060cff8d4ff",
  "task_type": "网关批量执行",
  "status": "completed",
  "progress": 100,
  "message": "批量执行完成",
  "result": {
    "total_cases": 8,
    "success_cases": 6,
    "failed_cases": 2,
    "execution_summary": [
      {
        "test_case_id": "tc001",
        "test_case_title": "MySQL基本查询测试",
        "status": "success",
        "message": "执行成功",
        "execution_time": 12.5
      },
      {
        "test_case_id": "tc002",
        "test_case_title": "MySQL连接测试",
        "status": "failed",
        "message": "PCAP上传失败",
        "execution_time": 5.2
      }
    ],
    "total_execution_time": 145.8
  },
  "error": null,
  "duration": 145.8,
  "created_at": "2025-09-05T15:51:15.145456+08:00",
  "updated_at": "2025-09-05T15:53:41.023789+08:00",
  "completed_at": "2025-09-05T15:53:41.023789+08:00",
  "failed_at": null,
  "total_test_cases": null,
  "database_type": null,
  "database_version": null
}
```

#### 任务失败响应
```json
{
  "success": true,
  "task_id": "07962b9f-3e36-40e3-804d-e060cff8d4ff",
  "task_type": "网关批量执行",
  "status": "failed",
  "progress": 25,
  "message": "任务执行失败",
  "result": null,
  "error": "网关服务器连接超时",
  "duration": 30.5,
  "created_at": "2025-09-05T15:51:15.145456+08:00",
  "updated_at": "2025-09-05T15:51:46.789123+08:00",
  "completed_at": null,
  "failed_at": "2025-09-05T15:51:46.789123+08:00",
  "total_test_cases": null,
  "database_type": null,
  "database_version": null
}
```

#### 任务不存在响应
```json
{
  "detail": "任务ID不存在"
}
```

#### 响应字段说明

| 字段名 | 类型 | 描述 |
|--------|------|------|
| success | boolean | 请求是否成功 |
| task_id | string | 任务唯一标识符 |
| task_type | string | 任务类型（网关批量执行） |
| status | string | 任务状态：pending（等待中）、running（执行中）、completed（已完成）、failed（失败）、cancelled（已取消） |
| progress | integer | 执行进度百分比（0-100） |
| message | string | 当前状态描述消息 |
| result | object/null | 任务执行结果（完成时包含详细信息） |
| error | string/null | 错误信息（失败时非空） |
| duration | float/null | 任务执行总时长（秒） |
| created_at | string | 任务创建时间（ISO 8601格式） |
| updated_at | string | 任务最后更新时间 |
| completed_at | string/null | 任务完成时间 |
| failed_at | string/null | 任务失败时间 |
| total_test_cases | integer/null | 总测试用例数 |
| database_type | string/null | 数据库类型 |
| database_version | string/null | 数据库版本 |

---

## 完整使用流程示例

### Python 完整示例
```python
import requests
import time
import json

BASE_URL = "http://localhost:8000/api/gateway-execution"

def get_database_types():
    """获取数据库类型和版本"""
    response = requests.get(f"{BASE_URL}/database-types-versions")
    return response.json()

def submit_batch_execution(gateway_ip, db_type, db_version):
    """提交批量执行任务"""
    data = {
        "gateway_server_ip": gateway_ip,
        "database_type": db_type,
        "database_version": db_version,
        "wait_time": 2
    }
    
    response = requests.post(f"{BASE_URL}/batch-execute-by-version", json=data)
    return response.json()

def monitor_task(task_id):
    """监控任务执行状态"""
    while True:
        response = requests.get(f"{BASE_URL}/task-status/{task_id}")
        result = response.json()
        
        print(f"状态: {result['status']}, 进度: {result['progress']}%")
        print(f"消息: {result['message']}")
        
        if result['status'] in ['completed', 'failed']:
            return result
        
        time.sleep(5)

# 使用示例
if __name__ == "__main__":
    # 1. 获取可用的数据库类型和版本
    db_info = get_database_types()
    print("可用的数据库类型和版本:")
    print(json.dumps(db_info['data'], indent=2, ensure_ascii=False))
    
    # 2. 提交批量执行任务
    result = submit_batch_execution(
        gateway_ip="*************",
        db_type="mysql",
        db_version="MySQL 8.0"
    )
    
    if result['success']:
        task_id = result['task_id']
        print(f"\n任务提交成功！任务ID: {task_id}")
        print(f"将执行 {result['total_cases']} 个测试用例")
        
        # 3. 监控任务执行
        print("\n开始监控任务执行...")
        final_result = monitor_task(task_id)
        
        print("\n=== 执行完成 ===")
        if final_result['status'] == 'completed':
            print("任务执行成功！")
            if final_result['result']:
                summary = final_result['result']
                print(f"总用例数: {summary.get('total_cases', 0)}")
                print(f"成功数: {summary.get('success_cases', 0)}")
                print(f"失败数: {summary.get('failed_cases', 0)}")
        else:
            print(f"任务执行失败: {final_result['error']}")
    else:
        print(f"任务提交失败: {result['message']}")
```

### 错误处理最佳实践

```python
import requests
from requests.exceptions import RequestException

def safe_api_call(url, method='GET', **kwargs):
    """安全的API调用，包含错误处理"""
    try:
        response = requests.request(method, url, timeout=30, **kwargs)
        response.raise_for_status()
        return response.json()
    except RequestException as e:
        print(f"网络请求失败: {e}")
        return None
    except ValueError as e:
        print(f"JSON解析失败: {e}")
        return None

# 使用示例
result = safe_api_call(
    f"{BASE_URL}/database-types-versions",
    method='GET'
)

if result and result.get('success'):
    print("获取数据库类型成功")
else:
    print("获取数据库类型失败")
```

---

## 注意事项

1. **网络超时**: 建议设置合理的网络超时时间（30秒以上）
2. **轮询频率**: 查询任务状态时建议间隔5-10秒，避免过于频繁的请求
3. **任务清理**: 已完成的任务会在系统中保存7天，7天后自动清理
4. **并发限制**: 系统同时处理的批量执行任务有数量限制，建议避免同时提交过多任务
5. **IP地址格式**: 网关服务器IP必须是有效的IPv4地址格式
6. **数据库版本匹配**: 数据库版本必须完全匹配，建议先通过第一个接口查询可用版本
7. **异常处理**: 建议在客户端代码中添加完善的异常处理机制

---

## 常见问题

### Q: 如何获取可用的网关服务器列表？
A: 使用 `GET /api/gateways/` 接口获取所有配置的网关服务器信息。

### Q: 任务执行失败后可以重新执行吗？
A: 可以使用相同的参数重新调用批量执行接口，系统会创建新的任务。

### Q: 如何取消正在执行的任务？
A: 目前暂不支持任务取消功能，任务会按照超时设置自动停止。

### Q: 批量执行的测试用例执行顺序是什么？
A: 测试用例按照创建时间顺序执行，最新创建的用例最后执行。

### Q: 如何确定数据库版本的准确名称？
A: 建议先调用数据库类型和版本查询接口，使用返回的准确版本名称。

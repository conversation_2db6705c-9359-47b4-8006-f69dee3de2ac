# DeepSeek API配置
OPENAI_API_KEY=***********************************
OPENAI_API_BASE=https://api.deepseek.com/v1

# 后台MySQL配置
MYSQL_HOST=************
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=123456
MYSQL_DATABASE=ai_sql_pcap

# 应用配置
APP_HOST=0.0.0.0
APP_PORT=8000
DEBUG=True

# 抓包配置
CAPTURE_DIR=captures
CAPTURE_INTERFACE=any

# Redis配置 - 使用远程Redis服务器
REDIS_HOST=************
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=
REDIS_MAX_CONNECTIONS=20

# 远程抓包服务器配置

REMOTE_HOST=**************
REMOTE_USER=root
REMOTE_PASSWORD=QZ@1005#1005
REMOTE_CAPTURE_DIR=/tmp/mysql_captures

REMOTE_HOST=*************
REMOTE_USER=root
REMOTE_PASSWORD=QZ@1005#1005
REMOTE_CAPTURE_DIR=/tmp/mysql_captures

# Gemini API配置
GEMINI_API_KEY=AIzaSyBAlT86w0PKG2O3UYWqYCgdfcfHijdUzpc
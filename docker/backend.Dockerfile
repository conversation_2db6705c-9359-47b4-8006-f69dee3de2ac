FROM python:3.13

WORKDIR /app

# 配置apt代理
RUN echo 'Acquire::http::Proxy "http://**************:7890";' > /etc/apt/apt.conf.d/proxy.conf && \
    echo 'Acquire::https::Proxy "http://**************:7890";' >> /etc/apt/apt.conf.d/proxy.conf

# 安装系统依赖（包括Oracle支持、抓包工具和C语言编译环境）
RUN apt-get update && apt-get install -y \
    tcpdump \
    net-tools \
    iputils-ping \
    openssh-client \
    libaio1 \
    wget \
    unzip \
    sudo \
    libcap2-bin \
    gcc \
    make \
    pkg-config \
    libpq-dev \
    libjson-c-dev \
    cmake \
    git \
    && rm -rf /var/lib/apt/lists/* \
    && rm -f /etc/apt/apt.conf.d/proxy.conf

# 安装MongoDB C驱动库 - 强制使用最新版本从源码编译
RUN echo 'Acquire::http::Proxy "http://**************:7890";' > /etc/apt/apt.conf.d/proxy.conf && \
    echo 'Acquire::https::Proxy "http://**************:7890";' >> /etc/apt/apt.conf.d/proxy.conf && \
    echo "Installing latest MongoDB C driver from source..." && \
    # 设置代理环境变量
    export http_proxy=http://**************:7890 && \
    export https_proxy=http://**************:7890 && \
    # 安装编译依赖
    apt-get update && apt-get install -y \
        libssl-dev \
        libsasl2-dev \
        libsnappy-dev \
        zlib1g-dev && \
    # 从源码编译最新版本的MongoDB C驱动
    cd /tmp && \
    echo "Cloning MongoDB C driver v1.28.1..." && \
    git clone --depth 1 --branch 1.28.1 https://github.com/mongodb/mongo-c-driver.git && \
    cd mongo-c-driver && \
    mkdir cmake-build && \
    cd cmake-build && \
    echo "Configuring MongoDB C driver build..." && \
    cmake \
        -DENABLE_AUTOMATIC_INIT_AND_CLEANUP=OFF \
        -DENABLE_TESTS=OFF \
        -DENABLE_EXAMPLES=OFF \
        -DENABLE_STATIC=OFF \
        -DCMAKE_BUILD_TYPE=Release \
        -DCMAKE_INSTALL_PREFIX=/usr/local \
        .. && \
    echo "Building MongoDB C driver..." && \
    make -j$(nproc) && \
    echo "Installing MongoDB C driver..." && \
    make install && \
    ldconfig && \
    echo "MongoDB C driver v1.28.1 installed successfully" && \
    # 验证安装
    pkg-config --modversion libmongoc-1.0 && \
    pkg-config --modversion libbson-1.0 && \
    # 清理源码
    cd /tmp && rm -rf mongo-c-driver && \
    # 清理代理配置
    rm -f /etc/apt/apt.conf.d/proxy.conf

# 编译C语言执行器
COPY backend/c_executors/ /app/c_executors/
RUN cd /app/c_executors && \
    echo "=== Starting C Executor Compilation ===" && \
    # 检查MongoDB库是否存在
    MONGO_AVAILABLE=false && \
    echo "Searching for MongoDB C driver libraries..." && \
    # 更新库缓存
    ldconfig && \
    # 使用pkg-config检查（优先检查/usr/local）
    export PKG_CONFIG_PATH="/usr/local/lib/pkgconfig:$PKG_CONFIG_PATH" && \
    if pkg-config --exists libmongoc-1.0; then \
        MONGO_VERSION=$(pkg-config --modversion libmongoc-1.0) && \
        echo "MongoDB C driver found via pkg-config: v$MONGO_VERSION"; \
        MONGO_AVAILABLE=true; \
    else \
        # 手动搜索库文件（优先搜索/usr/local）
        for lib_path in /usr/local/lib /usr/lib/x86_64-linux-gnu /usr/lib; do \
            if [ -f "$lib_path/libmongoc-1.0.so" ] || [ -f "$lib_path/libmongoc.so" ]; then \
                echo "MongoDB C driver found in $lib_path"; \
                MONGO_AVAILABLE=true; \
                break; \
            fi; \
        done; \
    fi && \
    # 检查PostgreSQL库
    POSTGRES_AVAILABLE=false && \
    if pkg-config --exists libpq; then \
        echo "PostgreSQL libpq found via pkg-config"; \
        POSTGRES_AVAILABLE=true; \
    else \
        echo "PostgreSQL libpq not found"; \
    fi && \
    # 更新库路径
    ldconfig && \
    # 清理之前的编译结果
    make clean 2>/dev/null || true && \
    # 编译GaussDB/PostgreSQL执行器
    if [ "$POSTGRES_AVAILABLE" = "true" ]; then \
        echo "Compiling GaussDB/PostgreSQL C executor..." && \
        make gaussdb && echo "GaussDB executor compiled successfully" || echo "GaussDB executor compilation failed"; \
    else \
        echo "Skipping GaussDB executor compilation due to missing libpq"; \
    fi && \
    # 编译MongoDB执行器
    if [ "$MONGO_AVAILABLE" = "true" ]; then \
        echo "Compiling MongoDB C executor..." && \
        make mongo && echo "MongoDB C executor compiled successfully" || echo "MongoDB C executor compilation failed"; \
    else \
        echo "Skipping MongoDB C executor compilation due to missing MongoDB C driver"; \
    fi && \
    # 设置执行权限
    chmod +x gaussdb_libpq_executor 2>/dev/null || true && \
    chmod +x mongo_libmongoc_executor 2>/dev/null || true && \
    # 验证编译结果
    echo "=== C Executor Compilation Results ===" && \
    if [ -f gaussdb_libpq_executor ]; then \
        echo "GaussDB executor: OK ($(file gaussdb_libpq_executor))"; \
    else \
        echo "GaussDB executor: MISSING"; \
    fi && \
    if [ -f mongo_libmongoc_executor ]; then \
        echo "MongoDB executor: OK ($(file mongo_libmongoc_executor))"; \
    else \
        echo "MongoDB executor: MISSING"; \
    fi && \
    echo "=== End of C Executor Compilation ==="

# 配置tcpdump权限 - 设置capabilities以允许非root用户使用tcpdump
RUN setcap cap_net_raw,cap_net_admin=eip /usr/bin/tcpdump

# 创建tcpdump用户组并添加权限
RUN groupadd -r tcpdump || true && \
    usermod -a -G tcpdump root

# 创建Oracle目录
RUN mkdir -p /opt/oracle

# 复制本地的Oracle Instant Client（Linux版本）
COPY oracle_device/instantclient-basic-linux.x64-*********.0dbru.zip /tmp/

# 安装Oracle Instant Client以支持Oracle 11g（Thick模式）
RUN cd /opt/oracle && \
    unzip -q /tmp/instantclient-basic-linux.x64-*********.0dbru.zip && \
    rm /tmp/instantclient-basic-linux.x64-*********.0dbru.zip && \
    # 创建符号链接（如果需要）
    cd instantclient_19_27 && \
    ln -sf libclntsh.so.19.1 libclntsh.so 2>/dev/null || true && \
    ln -sf libocci.so.19.1 libocci.so 2>/dev/null || true

# 设置Oracle环境变量
ENV LD_LIBRARY_PATH=/opt/oracle/instantclient_19_27:$LD_LIBRARY_PATH
ENV ORACLE_HOME=/opt/oracle/instantclient_19_27

# 配置动态链接库
RUN echo "/opt/oracle/instantclient_19_27" > /etc/ld.so.conf.d/oracle-instantclient.conf && \
    ldconfig

# 配置pip代理和国内源
RUN pip config set global.proxy http://**************:7890 && \
    pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple && \
    pip config set global.trusted-host pypi.tuna.tsinghua.edu.cn

# 复制requirements文件
COPY backend/requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码（排除c_executors目录，因为已经单独处理）
COPY backend/ .
# 确保C执行器在正确位置
RUN if [ ! -d "c_executors" ]; then mkdir -p c_executors; fi

# 创建必要的目录
RUN mkdir -p captures logs

# 设置权限
RUN chmod +x start_with_worker.py

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["python3", "start_with_worker.py"]
# 构建阶段
FROM node:23-alpine as build-stage

WORKDIR /app

# 配置npm代理和国内源
RUN npm config set proxy http://192.168.11.177:7890 && \
    npm config set https-proxy http://192.168.11.177:7890 && \
    npm config set registry https://registry.npmmirror.com

# 复制package文件
COPY package*.json ./

# 安装依赖
RUN npm install

# 复制源代码
COPY . .

# 构建生产版本
RUN npm run build

# 生产阶段
FROM nginx:alpine as production-stage

# 复制构建结果到nginx目录
COPY --from=build-stage /app/dist /usr/share/nginx/html

# 复制nginx配置
COPY nginx.conf /etc/nginx/nginx.conf

# 暴露端口
EXPOSE 80

# 启动nginx
CMD ["nginx", "-g", "daemon off;"]
#!/bin/bash

# 优化的热替换部署脚本 - 针对前端本地打包 + 后端直接替换
# 修复502错误，数据库已存在，重点处理代码更新与环境一致性

# 配置信息
SERVER_IP="************"
SERVER_USER="root"
SERVER_PASSWORD="root@123"
SERVER_PORT="22"
REMOTE_DIR="/opt/ai_sql_pcap"
LOCAL_DIR="."

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

set -e

echo -e "${BLUE}=== AI SQL PCAP 热替换部署脚本 (本地打包版) ===${NC}"
echo -e "${YELLOW}目标服务器: ${SERVER_USER}@${SERVER_IP}${NC}"
echo -e "${YELLOW}远程目录: ${REMOTE_DIR}${NC}"

# 显示当前代码状态
if [ -d ".git" ]; then
    echo -e "${YELLOW}当前代码状态:${NC}"
    echo "分支: $(git branch --show-current)"
    echo "最新提交: $(git log -1 --format='%h - %s (%an, %ar)')"
    if [ -n "$(git status --porcelain)" ]; then
        echo -e "${RED}工作区状态: 有未提交更改${NC}"
    else
        echo -e "${GREEN}工作区状态: 干净${NC}"
    fi
    echo ""
fi

# 检查依赖
if ! command -v sshpass &> /dev/null; then
    echo -e "${RED}错误: sshpass未安装${NC}"
    exit 1
fi

# Step 0: 拉取最新代码
echo -e "${YELLOW}步骤 0: 拉取最新代码...${NC}"
if [ -d ".git" ]; then
    echo "检测到Git仓库，正在拉取最新代码..."
    
    # 检查是否有未提交的更改
    if [ -n "$(git status --porcelain)" ]; then
        echo -e "${RED}警告：检测到未提交的更改${NC}"
        echo "当前工作区状态："
        git status --short
        echo ""
        read -p "是否要暂存这些更改并继续拉取？(y/n) " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            echo "暂存当前更改..."
            git stash push -m "Auto stash before hot deploy $(date)"
            STASHED=true
        else
            echo "请先处理未提交的更改，然后重新运行部署脚本"
            exit 1
        fi
    else
        STASHED=false
    fi
    
    # 获取当前分支名
    CURRENT_BRANCH=$(git branch --show-current)
    echo "当前分支: $CURRENT_BRANCH"
    
    # 拉取最新代码
    echo "正在拉取最新代码..."
    if git pull origin $CURRENT_BRANCH; then
        echo -e "${GREEN}✓ 代码拉取成功${NC}"
        
        # 如果之前暂存了更改，询问是否恢复
        if [ "$STASHED" = true ]; then
            echo ""
            read -p "是否要恢复之前暂存的更改？(y/n) " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                echo "恢复暂存的更改..."
                git stash pop
            else
                echo "暂存的更改保留在 stash 中，可以稍后使用 'git stash pop' 恢复"
            fi
        fi
    else
        echo -e "${RED}✗ 代码拉取失败${NC}"
        if [ "$STASHED" = true ]; then
            echo "恢复暂存的更改..."
            git stash pop
        fi
        exit 1
    fi
else
    echo -e "${YELLOW}未检测到Git仓库，跳过代码拉取${NC}"
fi

# Step 0: 预处理.env，确保数据库名正确
if [ -f ".env.production" ]; then
  DB_NAME_LINE=$(grep -E '^MYSQL_DATABASE=' .env.production || true)
  if [[ "$DB_NAME_LINE" == *"ai-sql-pcap"* ]]; then
    echo -e "${YELLOW}修正 .env.production 的 MYSQL_DATABASE 为 ai_sql_pcap${NC}"
    sed -i '' -e 's/MYSQL_DATABASE=.*/MYSQL_DATABASE=ai_sql_pcap/' .env.production 2>/dev/null || \
    gsed -i -e 's/MYSQL_DATABASE=.*/MYSQL_DATABASE=ai_sql_pcap/' .env.production 2>/dev/null || true
  fi
fi

# Step 1: 同步最新代码到远程服务器（不包含大文件）
echo -e "\n${YELLOW}步骤 1: 同步最新代码到远程服务器...${NC}"
sshpass -p "${SERVER_PASSWORD}" rsync -avz \
    --exclude 'node_modules' \
    --exclude 'venv' \
    --exclude '__pycache__' \
    --exclude '.git' \
    --exclude 'captures/*.pcap' \
    --exclude 'logs' \
    --exclude '*.pyc' \
    --exclude 'frontend/dist' \
    --exclude 'frontend/build' \
    -e "ssh -o StrictHostKeyChecking=no -p ${SERVER_PORT}" \
    ${LOCAL_DIR}/ ${SERVER_USER}@${SERVER_IP}:${REMOTE_DIR}/

echo -e "${GREEN}✓ 代码同步完成${NC}"

# Step 2: 后端热替换（直接替换Python文件）
echo -e "\n${YELLOW}步骤 2: 后端代码热替换...${NC}"
sshpass -p "${SERVER_PASSWORD}" ssh -o StrictHostKeyChecking=no -p ${SERVER_PORT} ${SERVER_USER}@${SERVER_IP} "
    cd ${REMOTE_DIR}/backend
    # 将源码刷新到容器挂载目录（/app 为绑定挂载）
    docker exec ai_sql_pcap_backend bash -lc 'mkdir -p /app'
    docker cp . ai_sql_pcap_backend:/app/
    echo '后端代码文件复制完成'
"

echo -e "${GREEN}✓ 后端代码热替换完成${NC}"

# Step 2.1: 注入环境变量文件，覆盖容器内环境（解决MySQL 1045）
echo -e "\n${YELLOW}步骤 2.1: 注入后端环境变量(.env)到容器...${NC}"
# 选择优先级：本地 .env > .env.production
ENV_FILE=".env"
if [ ! -f "$ENV_FILE" ] && [ -f ".env.production" ]; then
  ENV_FILE=".env.production"
fi
if [ -f "$ENV_FILE" ]; then
  echo -e "${BLUE}  使用本地 ${ENV_FILE} 注入容器...${NC}"
  sshpass -p "${SERVER_PASSWORD}" scp -P ${SERVER_PORT} -o StrictHostKeyChecking=no "$ENV_FILE" ${SERVER_USER}@${SERVER_IP}:/tmp/backend.env
  sshpass -p "${SERVER_PASSWORD}" ssh -o StrictHostKeyChecking=no -p ${SERVER_PORT} ${SERVER_USER}@${SERVER_IP} "
    docker cp /tmp/backend.env ai_sql_pcap_backend:/app/.env && rm -f /tmp/backend.env
    echo '已注入 /app/.env（优先覆盖容器环境变量）'
  "
else
  echo -e "${YELLOW}  未找到本地 .env 或 .env.production，跳过注入${NC}"
fi

# Step 3: 前端本地打包和部署
echo -e "\n${YELLOW}步骤 3: 前端本地打包和部署...${NC}"
cd ${LOCAL_DIR}/frontend

if ! command -v npm &> /dev/null; then
    echo -e "${RED}错误: 本地未安装Node.js/npm${NC}"
    exit 1
fi

echo -e "${BLUE}  安装前端依赖...${NC}"
npm install --silent

echo -e "${BLUE}  构建前端生产版本...${NC}"
npm run build

if [ ! -d "dist" ]; then
    echo -e "${RED}错误: 前端构建失败，找不到dist目录${NC}"
    exit 1
fi

echo -e "${GREEN}✓ 前端本地构建完成${NC}"

# 同步构建文件到远程服务器 - 修复路径问题
echo -e "${BLUE}  同步构建文件到远程服务器...${NC}"
# 确保在正确的目录下执行rsync
sshpass -p "${SERVER_PASSWORD}" rsync -avz \
    dist/ \
    -e "ssh -o StrictHostKeyChecking=no -p ${SERVER_PORT}" \
    ${SERVER_USER}@${SERVER_IP}:/tmp/frontend_dist/

# 部署前端到容器
echo -e "${BLUE}  部署前端到容器...${NC}"
sshpass -p "${SERVER_PASSWORD}" ssh -o StrictHostKeyChecking=no -p ${SERVER_PORT} ${SERVER_USER}@${SERVER_IP} "
    docker exec ai_sql_pcap_frontend rm -rf /usr/share/nginx/html/*
    docker cp /tmp/frontend_dist/. ai_sql_pcap_frontend:/usr/share/nginx/html/
    echo '前端文件部署完成'
"

echo -e "${GREEN}✓ 前端本地打包和部署完成${NC}"

# Step 4: 修复nginx配置（解决502错误）
echo -e "\n${YELLOW}步骤 4: 修复nginx配置...${NC}"
sshpass -p "${SERVER_PASSWORD}" ssh -o StrictHostKeyChecking=no -p ${SERVER_PORT} ${SERVER_USER}@${SERVER_IP} "
    docker cp ${REMOTE_DIR}/docker/nginx.conf ai_sql_pcap_frontend:/etc/nginx/nginx.conf
    docker exec ai_sql_pcap_frontend nginx -t || true
"

echo -e "${GREEN}✓ nginx配置更新完成${NC}"

# Step 5: 数据库检查和授权（避免root来源限制/口令不一致）
echo -e "\n${YELLOW}步骤 5: 数据库检查与授权...${NC}"
sshpass -p "${SERVER_PASSWORD}" ssh -o StrictHostKeyChecking=no -p ${SERVER_PORT} ${SERVER_USER}@${SERVER_IP} '
  MYSQL_HOST=127.0.0.1
  MYSQL_PORT=3306
  MYSQL_USER=root
  MYSQL_PASS=123456
  echo "  检测mysql客户端..."
  if command -v mysql >/dev/null 2>&1; then
    echo "  使用系统mysql客户端进行检查"
    mysql -h$MYSQL_HOST -P$MYSQL_PORT -u$MYSQL_USER -p$MYSQL_PASS -e "SELECT VERSION();" >/dev/null 2>&1 && echo "✓ 数据库连接正常" || echo "✗ 数据库连接失败（可忽略，如无客户端或口令不同）"
    mysql -h$MYSQL_HOST -P$MYSQL_PORT -u$MYSQL_USER -p$MYSQL_PASS -e "CREATE DATABASE IF NOT EXISTS ai_sql_pcap; GRANT ALL PRIVILEGES ON ai_sql_pcap.* TO '\''root'\''@'\''%'\'' IDENTIFIED BY '\''123456'\''; FLUSH PRIVILEGES;" >/dev/null 2>&1 || true
  else
    # 尝试在可能的MySQL容器中执行
    MYSQL_CONTAINER=$(docker ps --format "{{.Names}}" | grep -E "mysql|mariadb" | head -n1)
    if [ -n "$MYSQL_CONTAINER" ]; then
      echo "  在容器 $MYSQL_CONTAINER 内检查"
      docker exec "$MYSQL_CONTAINER" mysql -uroot -p123456 -e "SELECT VERSION();" >/dev/null 2>&1 && echo "✓ 数据库连接正常(容器)"
      docker exec "$MYSQL_CONTAINER" mysql -uroot -p123456 -e "CREATE DATABASE IF NOT EXISTS ai_sql_pcap; GRANT ALL PRIVILEGES ON ai_sql_pcap.* TO '\''root'\''@'\''%'\'' IDENTIFIED BY '\''123456'\''; FLUSH PRIVILEGES;" >/dev/null 2>&1 || true
    else
      echo "  未找到mysql客户端或容器，跳过授权"
    fi
  fi
'

echo -e "${GREEN}✓ 数据库检查完成${NC}"

# Step 6: 重启服务应用更改（不重建镜像）
echo -e "\n${YELLOW}步骤 6: 重启服务应用更改...${NC}"
sshpass -p "${SERVER_PASSWORD}" ssh -o StrictHostKeyChecking=no -p ${SERVER_PORT} ${SERVER_USER}@${SERVER_IP} "
  cd ${REMOTE_DIR}
  docker-compose restart backend || docker restart ai_sql_pcap_backend || true
  echo '后端容器已重启'
  sleep 6
  docker-compose restart frontend || docker restart ai_sql_pcap_frontend || true
  echo '前端容器已重启'
"

echo -e "${GREEN}✓ 服务重启完成${NC}"

# Step 7: 健康检查与502排查
echo -e "\n${YELLOW}步骤 7: 服务健康检查...${NC}"
sshpass -p "${SERVER_PASSWORD}" ssh -o StrictHostKeyChecking=no -p ${SERVER_PORT} ${SERVER_USER}@${SERVER_IP} "
  echo '=== 容器状态 ==='
  docker ps --format 'table {{.Names}}\t{{.Status}}\t{{.Ports}}'
  echo ''
  echo '=== 后端直接访问检查 ==='
  curl -s -o /dev/null -w '%{http_code}\n' http://localhost:8000/api/health || true
  echo '=== 前端首页 ==='
  curl -s -I http://localhost:8080 | head -2 || true
  echo '=== 前端代理到后端 /api/health ==='
  curl -s -o /dev/null -w '%{http_code}\n' http://localhost:8080/api/health || true
  echo '=== Nginx proxy_pass 片段 ==='
  docker exec ai_sql_pcap_frontend grep -n 'proxy_pass' /etc/nginx/nginx.conf 2>/dev/null || echo '未找到proxy_pass配置'
"

echo -e "\n${GREEN}=== 热替换部署完成 ===${NC}"
echo -e "${GREEN}前端地址: http://${SERVER_IP}:8080${NC}"
echo -e "${GREEN}后端API: http://${SERVER_IP}:8000${NC}"
echo -e "${GREEN}健康检查: http://${SERVER_IP}:8080/api/health${NC}"

# 提示调试命令
echo -e "\n${BLUE}=== 快速调试命令 ===${NC}"
echo -e "${YELLOW}后端日志:${NC} sshpass -p '${SERVER_PASSWORD}' ssh -p ${SERVER_PORT} ${SERVER_USER}@${SERVER_IP} 'docker logs ai_sql_pcap_backend --tail 200'"
echo -e "${YELLOW}前端日志:${NC} sshpass -p '${SERVER_PASSWORD}' ssh -p ${SERVER_PORT} ${SERVER_USER}@${SERVER_IP} 'docker logs ai_sql_pcap_frontend --tail 200'"
echo -e "${YELLOW}检查Nginx配置:${NC} sshpass -p '${SERVER_PASSWORD}' ssh -p ${SERVER_PORT} ${SERVER_USER}@${SERVER_IP} 'docker exec ai_sql_pcap_frontend nginx -T | sed -n \"/server {/,/}/p\" | sed -n \"1,120p\"'"

# 清理临时文件
sshpass -p "${SERVER_PASSWORD}" ssh -o StrictHostKeyChecking=no -p ${SERVER_PORT} ${SERVER_USER}@${SERVER_IP} "rm -rf /tmp/frontend_dist"
